# AI-DEEP-CPMS 智能深度精细化项目管理系统

## 项目简介

AI-DEEP-CPMS是一个类似于Microsoft Project的智能项目管理系统，专注于深度精细化的项目管理功能。系统提供了完整的项目计划、任务管理、甘特图可视化、成本控制等功能。

## 主要功能

### 1. 核心功能
- **用户认证系统**: 邮箱注册登录，支持Google/GitHub OAuth
- **项目管理**: 项目创建、编辑、导入导出
- **任务管理**: WBS任务分解、甘特图、依赖关系管理
- **工作日历**: 自定义工作日、节假日设置
- **文件格式支持**: 集成MPXJ库，支持多种项目文件格式

### 2. 特色功能
- **项目清单管理**: 支持Excel、PDF等格式导入
- **项目定额管理**: 定额数据管理和资源消耗明细
- **项目资源管理**: 资源价格管理和成本拆分
- **甘特图可视化**: 基于Highcharts的交互式甘特图
- **关键路径计算**: 自动计算项目关键路径

### 3. 智能分析
- **成本分析**: 精细化成本控制和预测
- **进度跟踪**: 实时项目进度监控
- **资源优化**: 资源配置优化建议

## 技术架构

### 后端技术栈
- **框架**: Django 5.2.3
- **API**: Django REST Framework
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **异步任务**: Celery + Redis
- **文件处理**: MPXJ, pandas, openpyxl

### 前端技术栈
- **框架**: Bootstrap 5
- **图表库**: Highcharts Gantt
- **JavaScript**: 原生JS + jQuery

## 安装和运行

### 1. 环境要求
- Python 3.8+
- Node.js (可选，用于前端开发)

### 2. 安装步骤

```bash
# 克隆项目
git clone <repository-url>
cd cost-Project-2025-06-16

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖 (使用中国镜像源)
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 运行数据库迁移
python manage.py migrate

# 创建示例数据
python create_sample_data.py

# 启动开发服务器
python manage.py runserver
```

### 3. 访问系统

- **前端首页**: http://127.0.0.1:8000/
- **甘特图页面**: http://127.0.0.1:8000/gantt/
- **管理后台**: http://127.0.0.1:8000/admin/
- **API文档**: http://127.0.0.1:8000/api/

### 4. 默认账户

- **管理员**: admin / admin123
- **测试用户**: testuser / test123

## 项目结构

```
cost-Project-2025-06-16/
├── ai_deep_cpms/           # Django项目配置
├── authentication/         # 用户认证模块
├── projects/              # 项目管理模块
├── tasks/                 # 任务管理模块
├── quotas/                # 定额管理模块
├── resources/             # 资源管理模块
├── templates/             # HTML模板
├── static/                # 静态文件
├── media/                 # 媒体文件
├── mpxj-master/           # MPXJ文件格式支持库
├── requirements.txt       # Python依赖
├── create_sample_data.py  # 示例数据创建脚本
└── README.md             # 项目说明
```

## API接口

### 项目管理
- `GET /api/projects/` - 获取项目列表
- `POST /api/projects/` - 创建新项目
- `GET /api/projects/{id}/` - 获取项目详情
- `GET /api/projects/{id}/gantt_data/` - 获取甘特图数据
- `POST /api/projects/{id}/import_list/` - 导入项目清单

### 任务管理
- `GET /api/tasks/` - 获取任务列表
- `POST /api/tasks/` - 创建新任务
- `PUT /api/tasks/{id}/` - 更新任务
- `DELETE /api/tasks/{id}/` - 删除任务
- `POST /api/tasks/{id}/add_list_item/` - 添加关联清单项

## 开发计划

### 已完成功能
- ✅ 基础项目架构搭建
- ✅ 用户认证系统
- ✅ 项目和任务数据模型
- ✅ 基础API接口
- ✅ 甘特图可视化
- ✅ 管理后台配置

### 开发中功能
- 🔄 前端React应用
- 🔄 文件导入导出功能
- 🔄 关键路径算法
- 🔄 成本分析报表

### 计划功能
- 📋 移动端适配
- 📋 多用户协作
- 📋 AI智能分析
- 📋 云端部署

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目链接: [项目仓库地址]

## 致谢

- [MPXJ](https://www.mpxj.org/) - 项目文件格式支持
- [Highcharts](https://www.highcharts.com/) - 甘特图可视化
- [Django](https://www.djangoproject.com/) - Web框架
- [Bootstrap](https://getbootstrap.com/) - UI框架
