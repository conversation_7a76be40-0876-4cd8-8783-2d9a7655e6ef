Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 3994
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3498
"GET /accounts/signup/ HTTP/1.1" 200 2883
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"POST /accounts/google/login/?process=login HTTP/1.1" 302 0
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/signup/ HTTP/1.1" 200 2883
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 23427
Internal Server Error: /accounts/signup/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'google_oauth2_login' not found. 'google_oauth2_login' is not a valid view function or pattern name.
"GET /accounts/signup/ HTTP/1.1" 500 167004
"GET /accounts/login/ HTTP/1.1" 200 18386
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET /accounts/confirm-email/ HTTP/1.1" 200 1534
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/signup/ HTTP/1.1" 200 19412
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 200 18602
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET / HTTP/1.1" 200 38449
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /gantt/ HTTP/1.1" 200 40908
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /api/projects/ HTTP/1.1" 200 2015
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
