Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 3994
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3498
"GET /accounts/signup/ HTTP/1.1" 200 2883
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"POST /accounts/google/login/?process=login HTTP/1.1" 302 0
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/signup/ HTTP/1.1" 200 2883
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 23427
Internal Server Error: /accounts/signup/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'google_oauth2_login' not found. 'google_oauth2_login' is not a valid view function or pattern name.
"GET /accounts/signup/ HTTP/1.1" 500 167004
"GET /accounts/login/ HTTP/1.1" 200 18386
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET /accounts/confirm-email/ HTTP/1.1" 200 1534
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/signup/ HTTP/1.1" 200 19412
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 200 18602
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET / HTTP/1.1" 200 38449
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /gantt/ HTTP/1.1" 200 40908
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /api/projects/ HTTP/1.1" 200 2015
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 33714
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /create-project/ HTTP/1.1" 200 51565
"POST /api/projects/ HTTP/1.1" 201 239
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=2 HTTP/1.1" 200 34890
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /api/list-items/?project_id=2 HTTP/1.1" 200 52
"GET /api/projects/2/ HTTP/1.1" 200 426
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /create-project/ HTTP/1.1" 200 51565
Internal Server Error: /api/projects/import_project/
"POST /api/projects/import_project/ HTTP/1.1" 500 61
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /project-tasks/ HTTP/1.1" 200 33719
"GET / HTTP/1.1" 200 23427
"GET /accounts/signup/ HTTP/1.1" 200 19412
"GET /accounts/login/ HTTP/1.1" 200 18375
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /create-project/ HTTP/1.1" 200 51565
"POST /api/projects/ HTTP/1.1" 201 208
"GET /create-project/ HTTP/1.1" 200 51565
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /gantt/ HTTP/1.1" 200 74294
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/2/ HTTP/1.1" 200 426
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 66, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107285
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 66, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107285
"GET /project/?id=2 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=2 HTTP/1.1" 200 52
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /api/projects/2/ HTTP/1.1" 200 426
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4205
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4187
"GET /admin/login/ HTTP/1.1" 200 4174
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4187
Forbidden: /api/
"GET /api/ HTTP/1.1" 403 43
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Watching for file changes with StatReloader
