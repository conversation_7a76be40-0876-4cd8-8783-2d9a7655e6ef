//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for EPSProjectWBSSpreadType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="EPSProjectWBSSpreadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="PeriodType"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Financial Period"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Period" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "EPSProjectWBSSpreadType", propOrder =
{
   "startDate",
   "endDate",
   "periodType",
   "period"
}) public class EPSProjectWBSSpreadType
{

   @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
   @XmlElement(name = "PeriodType", required = true) @XmlJavaTypeAdapter(Adapter1.class) protected String periodType;
   @XmlElement(name = "Period") protected List<EPSProjectWBSSpreadType.Period> period;

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the endDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEndDate()
   {
      return endDate;
   }

   /**
    * Sets the value of the endDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEndDate(LocalDateTime value)
   {
      this.endDate = value;
   }

   /**
    * Gets the value of the periodType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPeriodType()
   {
      return periodType;
   }

   /**
    * Sets the value of the periodType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPeriodType(String value)
   {
      this.periodType = value;
   }

   /**
    * Gets the value of the period property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the period property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getPeriod().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link EPSProjectWBSSpreadType.Period }
    *
    *
    */
   public List<EPSProjectWBSSpreadType.Period> getPeriod()
   {
      if (period == null)
      {
         period = new ArrayList<>();
      }
      return this.period;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "startDate",
      "endDate",
      "actualLaborUnits",
      "cumulativeActualLaborUnits",
      "actualNonlaborUnits",
      "cumulativeActualNonlaborUnits",
      "atCompletionLaborUnits",
      "cumulativeAtCompletionLaborUnits",
      "atCompletionNonlaborUnits",
      "cumulativeAtCompletionNonlaborUnits",
      "baselinePlannedLaborUnits",
      "cumulativeBaselinePlannedLaborUnits",
      "baselinePlannedNonlaborUnits",
      "cumulativeBaselinePlannedNonlaborUnits",
      "earnedValueLaborUnits",
      "cumulativeEarnedValueLaborUnits",
      "estimateAtCompletionLaborUnits",
      "cumulativeEstimateAtCompletionLaborUnits",
      "estimateToCompleteLaborUnits",
      "cumulativeEstimateToCompleteLaborUnits",
      "periodActualLaborUnits",
      "cumulativePeriodActualLaborUnits",
      "periodActualNonLaborUnits",
      "cumulativePeriodActualNonLaborUnits",
      "periodAtCompletionLaborUnits",
      "cumulativePeriodAtCompletionLaborUnits",
      "periodAtCompletionNonLaborUnits",
      "cumulativePeriodAtCompletionNonLaborUnits",
      "periodEarnedValueLaborUnits",
      "cumulativePeriodEarnedValueLaborUnits",
      "periodEstimateAtCompletionLaborUnits",
      "cumulativePeriodEstimateAtCompletionLaborUnits",
      "periodPlannedValueLaborUnits",
      "cumulativePeriodPlannedValueLaborUnits",
      "plannedLaborUnits",
      "cumulativePlannedLaborUnits",
      "plannedNonlaborUnits",
      "cumulativePlannedNonlaborUnits",
      "plannedValueLaborUnits",
      "cumulativePlannedValueLaborUnits",
      "remainingLaborUnits",
      "cumulativeRemainingLaborUnits",
      "remainingLateLaborUnits",
      "cumulativeRemainingLateLaborUnits",
      "remainingLateNonlaborUnits",
      "cumulativeRemainingLateNonlaborUnits",
      "remainingNonlaborUnits",
      "cumulativeRemainingNonlaborUnits",
      "actualCost",
      "cumulativeActualCost",
      "actualExpenseCost",
      "cumulativeActualExpenseCost",
      "actualLaborCost",
      "cumulativeActualLaborCost",
      "actualMaterialCost",
      "cumulativeActualMaterialCost",
      "actualNonlaborCost",
      "cumulativeActualNonlaborCost",
      "actualTotalCost",
      "cumulativeActualTotalCost",
      "atCompletionExpenseCost",
      "cumulativeAtCompletionExpenseCost",
      "atCompletionLaborCost",
      "cumulativeAtCompletionLaborCost",
      "atCompletionMaterialCost",
      "cumulativeAtCompletionMaterialCost",
      "atCompletionNonlaborCost",
      "cumulativeAtCompletionNonlaborCost",
      "atCompletionTotalCost",
      "cumulativeAtCompletionTotalCost",
      "baselinePlannedExpenseCost",
      "cumulativeBaselinePlannedExpenseCost",
      "baselinePlannedLaborCost",
      "cumulativeBaselinePlannedLaborCost",
      "baselinePlannedMaterialCost",
      "cumulativeBaselinePlannedMaterialCost",
      "baselinePlannedNonlaborCost",
      "cumulativeBaselinePlannedNonlaborCost",
      "baselinePlannedTotalCost",
      "cumulativeBaselinePlannedTotalCost",
      "earnedValueCost",
      "cumulativeEarnedValueCost",
      "estimateAtCompletionCost",
      "cumulativeEstimateAtCompletionCost",
      "estimateToCompleteCost",
      "cumulativeEstimateToCompleteCost",
      "periodActualCost",
      "cumulativePeriodActualCost",
      "periodActualExpenseCost",
      "cumulativePeriodActualExpenseCost",
      "periodActualLaborCost",
      "cumulativePeriodActualLaborCost",
      "periodActualMaterialCost",
      "cumulativePeriodActualMaterialCost",
      "periodActualNonLaborCost",
      "cumulativePeriodActualNonLaborCost",
      "periodAtCompletionExpenseCost",
      "cumulativePeriodAtCompletionExpenseCost",
      "periodAtCompletionLaborCost",
      "cumulativePeriodAtCompletionLaborCost",
      "periodAtCompletionMaterialCost",
      "cumulativePeriodAtCompletionMaterialCost",
      "periodAtCompletionNonLaborCost",
      "cumulativePeriodAtCompletionNonLaborCost",
      "periodAtCompletionTotalCost",
      "cumulativePeriodAtCompletionTotalCost",
      "periodEarnedValueCost",
      "cumulativePeriodEarnedValueCost",
      "periodEstimateAtCompletionCost",
      "cumulativePeriodEstimateAtCompletionCost",
      "periodPlannedValueCost",
      "cumulativePeriodPlannedValueCost",
      "plannedExpenseCost",
      "cumulativePlannedExpenseCost",
      "plannedLaborCost",
      "cumulativePlannedLaborCost",
      "plannedMaterialCost",
      "cumulativePlannedMaterialCost",
      "plannedNonlaborCost",
      "cumulativePlannedNonlaborCost",
      "plannedTotalCost",
      "cumulativePlannedTotalCost",
      "plannedValueCost",
      "cumulativePlannedValueCost",
      "remainingExpenseCost",
      "cumulativeRemainingExpenseCost",
      "remainingLaborCost",
      "cumulativeRemainingLaborCost",
      "remainingLateExpenseCost",
      "cumulativeRemainingLateExpenseCost",
      "remainingLateLaborCost",
      "cumulativeRemainingLateLaborCost",
      "remainingLateMaterialCost",
      "cumulativeRemainingLateMaterialCost",
      "remainingLateNonlaborCost",
      "cumulativeRemainingLateNonlaborCost",
      "remainingLateTotalCost",
      "cumulativeRemainingLateTotalCost",
      "remainingMaterialCost",
      "cumulativeRemainingMaterialCost",
      "remainingNonlaborCost",
      "cumulativeRemainingNonlaborCost",
      "remainingTotalCost",
      "cumulativeRemainingTotalCost"
   }) public static class Period
   {

      @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
      @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
      @XmlElement(name = "ActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborUnits;
      @XmlElement(name = "CumulativeActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualLaborUnits;
      @XmlElement(name = "ActualNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonlaborUnits;
      @XmlElement(name = "CumulativeActualNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualNonlaborUnits;
      @XmlElement(name = "AtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborUnits;
      @XmlElement(name = "CumulativeAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionLaborUnits;
      @XmlElement(name = "AtCompletionNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonlaborUnits;
      @XmlElement(name = "CumulativeAtCompletionNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionNonlaborUnits;
      @XmlElement(name = "BaselinePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborUnits;
      @XmlElement(name = "CumulativeBaselinePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedLaborUnits;
      @XmlElement(name = "BaselinePlannedNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonlaborUnits;
      @XmlElement(name = "CumulativeBaselinePlannedNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedNonlaborUnits;
      @XmlElement(name = "EarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueLaborUnits;
      @XmlElement(name = "CumulativeEarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEarnedValueLaborUnits;
      @XmlElement(name = "EstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionLaborUnits;
      @XmlElement(name = "CumulativeEstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateAtCompletionLaborUnits;
      @XmlElement(name = "EstimateToCompleteLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToCompleteLaborUnits;
      @XmlElement(name = "CumulativeEstimateToCompleteLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateToCompleteLaborUnits;
      @XmlElement(name = "PeriodActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualLaborUnits;
      @XmlElement(name = "CumulativePeriodActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualLaborUnits;
      @XmlElement(name = "PeriodActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualNonLaborUnits;
      @XmlElement(name = "CumulativePeriodActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualNonLaborUnits;
      @XmlElement(name = "PeriodAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionLaborUnits;
      @XmlElement(name = "CumulativePeriodAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionLaborUnits;
      @XmlElement(name = "PeriodAtCompletionNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionNonLaborUnits;
      @XmlElement(name = "CumulativePeriodAtCompletionNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionNonLaborUnits;
      @XmlElement(name = "PeriodEarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodEarnedValueLaborUnits;
      @XmlElement(name = "CumulativePeriodEarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodEarnedValueLaborUnits;
      @XmlElement(name = "PeriodEstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodEstimateAtCompletionLaborUnits;
      @XmlElement(name = "CumulativePeriodEstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodEstimateAtCompletionLaborUnits;
      @XmlElement(name = "PeriodPlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodPlannedValueLaborUnits;
      @XmlElement(name = "CumulativePeriodPlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodPlannedValueLaborUnits;
      @XmlElement(name = "PlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborUnits;
      @XmlElement(name = "CumulativePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedLaborUnits;
      @XmlElement(name = "PlannedNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonlaborUnits;
      @XmlElement(name = "CumulativePlannedNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedNonlaborUnits;
      @XmlElement(name = "PlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueLaborUnits;
      @XmlElement(name = "CumulativePlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedValueLaborUnits;
      @XmlElement(name = "RemainingLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborUnits;
      @XmlElement(name = "CumulativeRemainingLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLaborUnits;
      @XmlElement(name = "RemainingLateLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateLaborUnits;
      @XmlElement(name = "CumulativeRemainingLateLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateLaborUnits;
      @XmlElement(name = "RemainingLateNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateNonlaborUnits;
      @XmlElement(name = "CumulativeRemainingLateNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateNonlaborUnits;
      @XmlElement(name = "RemainingNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonlaborUnits;
      @XmlElement(name = "CumulativeRemainingNonlaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingNonlaborUnits;
      @XmlElement(name = "ActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualCost;
      @XmlElement(name = "CumulativeActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualCost;
      @XmlElement(name = "ActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualExpenseCost;
      @XmlElement(name = "CumulativeActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualExpenseCost;
      @XmlElement(name = "ActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborCost;
      @XmlElement(name = "CumulativeActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualLaborCost;
      @XmlElement(name = "ActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualMaterialCost;
      @XmlElement(name = "CumulativeActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualMaterialCost;
      @XmlElement(name = "ActualNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonlaborCost;
      @XmlElement(name = "CumulativeActualNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualNonlaborCost;
      @XmlElement(name = "ActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualTotalCost;
      @XmlElement(name = "CumulativeActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualTotalCost;
      @XmlElement(name = "AtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionExpenseCost;
      @XmlElement(name = "CumulativeAtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionExpenseCost;
      @XmlElement(name = "AtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborCost;
      @XmlElement(name = "CumulativeAtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionLaborCost;
      @XmlElement(name = "AtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionMaterialCost;
      @XmlElement(name = "CumulativeAtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionMaterialCost;
      @XmlElement(name = "AtCompletionNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonlaborCost;
      @XmlElement(name = "CumulativeAtCompletionNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionNonlaborCost;
      @XmlElement(name = "AtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionTotalCost;
      @XmlElement(name = "CumulativeAtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionTotalCost;
      @XmlElement(name = "BaselinePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedExpenseCost;
      @XmlElement(name = "CumulativeBaselinePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedExpenseCost;
      @XmlElement(name = "BaselinePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborCost;
      @XmlElement(name = "CumulativeBaselinePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedLaborCost;
      @XmlElement(name = "BaselinePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedMaterialCost;
      @XmlElement(name = "CumulativeBaselinePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedMaterialCost;
      @XmlElement(name = "BaselinePlannedNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonlaborCost;
      @XmlElement(name = "CumulativeBaselinePlannedNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedNonlaborCost;
      @XmlElement(name = "BaselinePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedTotalCost;
      @XmlElement(name = "CumulativeBaselinePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedTotalCost;
      @XmlElement(name = "EarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueCost;
      @XmlElement(name = "CumulativeEarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEarnedValueCost;
      @XmlElement(name = "EstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionCost;
      @XmlElement(name = "CumulativeEstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateAtCompletionCost;
      @XmlElement(name = "EstimateToCompleteCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToCompleteCost;
      @XmlElement(name = "CumulativeEstimateToCompleteCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateToCompleteCost;
      @XmlElement(name = "PeriodActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualCost;
      @XmlElement(name = "CumulativePeriodActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualCost;
      @XmlElement(name = "PeriodActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualExpenseCost;
      @XmlElement(name = "CumulativePeriodActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualExpenseCost;
      @XmlElement(name = "PeriodActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualLaborCost;
      @XmlElement(name = "CumulativePeriodActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualLaborCost;
      @XmlElement(name = "PeriodActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualMaterialCost;
      @XmlElement(name = "CumulativePeriodActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualMaterialCost;
      @XmlElement(name = "PeriodActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualNonLaborCost;
      @XmlElement(name = "CumulativePeriodActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualNonLaborCost;
      @XmlElement(name = "PeriodAtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionExpenseCost;
      @XmlElement(name = "CumulativePeriodAtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionExpenseCost;
      @XmlElement(name = "PeriodAtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionLaborCost;
      @XmlElement(name = "CumulativePeriodAtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionLaborCost;
      @XmlElement(name = "PeriodAtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionMaterialCost;
      @XmlElement(name = "CumulativePeriodAtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionMaterialCost;
      @XmlElement(name = "PeriodAtCompletionNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionNonLaborCost;
      @XmlElement(name = "CumulativePeriodAtCompletionNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionNonLaborCost;
      @XmlElement(name = "PeriodAtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionTotalCost;
      @XmlElement(name = "CumulativePeriodAtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionTotalCost;
      @XmlElement(name = "PeriodEarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodEarnedValueCost;
      @XmlElement(name = "CumulativePeriodEarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodEarnedValueCost;
      @XmlElement(name = "PeriodEstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodEstimateAtCompletionCost;
      @XmlElement(name = "CumulativePeriodEstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodEstimateAtCompletionCost;
      @XmlElement(name = "PeriodPlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodPlannedValueCost;
      @XmlElement(name = "CumulativePeriodPlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodPlannedValueCost;
      @XmlElement(name = "PlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedExpenseCost;
      @XmlElement(name = "CumulativePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedExpenseCost;
      @XmlElement(name = "PlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborCost;
      @XmlElement(name = "CumulativePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedLaborCost;
      @XmlElement(name = "PlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedMaterialCost;
      @XmlElement(name = "CumulativePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedMaterialCost;
      @XmlElement(name = "PlannedNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonlaborCost;
      @XmlElement(name = "CumulativePlannedNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedNonlaborCost;
      @XmlElement(name = "PlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedTotalCost;
      @XmlElement(name = "CumulativePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedTotalCost;
      @XmlElement(name = "PlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueCost;
      @XmlElement(name = "CumulativePlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedValueCost;
      @XmlElement(name = "RemainingExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingExpenseCost;
      @XmlElement(name = "CumulativeRemainingExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingExpenseCost;
      @XmlElement(name = "RemainingLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborCost;
      @XmlElement(name = "CumulativeRemainingLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLaborCost;
      @XmlElement(name = "RemainingLateExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateExpenseCost;
      @XmlElement(name = "CumulativeRemainingLateExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateExpenseCost;
      @XmlElement(name = "RemainingLateLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateLaborCost;
      @XmlElement(name = "CumulativeRemainingLateLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateLaborCost;
      @XmlElement(name = "RemainingLateMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateMaterialCost;
      @XmlElement(name = "CumulativeRemainingLateMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateMaterialCost;
      @XmlElement(name = "RemainingLateNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateNonlaborCost;
      @XmlElement(name = "CumulativeRemainingLateNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateNonlaborCost;
      @XmlElement(name = "RemainingLateTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateTotalCost;
      @XmlElement(name = "CumulativeRemainingLateTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateTotalCost;
      @XmlElement(name = "RemainingMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingMaterialCost;
      @XmlElement(name = "CumulativeRemainingMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingMaterialCost;
      @XmlElement(name = "RemainingNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonlaborCost;
      @XmlElement(name = "CumulativeRemainingNonlaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingNonlaborCost;
      @XmlElement(name = "RemainingTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingTotalCost;
      @XmlElement(name = "CumulativeRemainingTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingTotalCost;

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStartDate(LocalDateTime value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the endDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getEndDate()
      {
         return endDate;
      }

      /**
       * Sets the value of the endDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEndDate(LocalDateTime value)
      {
         this.endDate = value;
      }

      /**
       * Gets the value of the actualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualLaborUnits()
      {
         return actualLaborUnits;
      }

      /**
       * Sets the value of the actualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualLaborUnits(Double value)
      {
         this.actualLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualLaborUnits()
      {
         return cumulativeActualLaborUnits;
      }

      /**
       * Sets the value of the cumulativeActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualLaborUnits(Double value)
      {
         this.cumulativeActualLaborUnits = value;
      }

      /**
       * Gets the value of the actualNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualNonlaborUnits()
      {
         return actualNonlaborUnits;
      }

      /**
       * Sets the value of the actualNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualNonlaborUnits(Double value)
      {
         this.actualNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualNonlaborUnits()
      {
         return cumulativeActualNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativeActualNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualNonlaborUnits(Double value)
      {
         this.cumulativeActualNonlaborUnits = value;
      }

      /**
       * Gets the value of the atCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionLaborUnits()
      {
         return atCompletionLaborUnits;
      }

      /**
       * Sets the value of the atCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionLaborUnits(Double value)
      {
         this.atCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionLaborUnits()
      {
         return cumulativeAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionLaborUnits(Double value)
      {
         this.cumulativeAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the atCompletionNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionNonlaborUnits()
      {
         return atCompletionNonlaborUnits;
      }

      /**
       * Sets the value of the atCompletionNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionNonlaborUnits(Double value)
      {
         this.atCompletionNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionNonlaborUnits()
      {
         return cumulativeAtCompletionNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionNonlaborUnits(Double value)
      {
         this.cumulativeAtCompletionNonlaborUnits = value;
      }

      /**
       * Gets the value of the baselinePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedLaborUnits()
      {
         return baselinePlannedLaborUnits;
      }

      /**
       * Sets the value of the baselinePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedLaborUnits(Double value)
      {
         this.baselinePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedLaborUnits()
      {
         return cumulativeBaselinePlannedLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedLaborUnits(Double value)
      {
         this.cumulativeBaselinePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the baselinePlannedNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedNonlaborUnits()
      {
         return baselinePlannedNonlaborUnits;
      }

      /**
       * Sets the value of the baselinePlannedNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedNonlaborUnits(Double value)
      {
         this.baselinePlannedNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedNonlaborUnits()
      {
         return cumulativeBaselinePlannedNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedNonlaborUnits(Double value)
      {
         this.cumulativeBaselinePlannedNonlaborUnits = value;
      }

      /**
       * Gets the value of the earnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEarnedValueLaborUnits()
      {
         return earnedValueLaborUnits;
      }

      /**
       * Sets the value of the earnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEarnedValueLaborUnits(Double value)
      {
         this.earnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEarnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEarnedValueLaborUnits()
      {
         return cumulativeEarnedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEarnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEarnedValueLaborUnits(Double value)
      {
         this.cumulativeEarnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the estimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateAtCompletionLaborUnits()
      {
         return estimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the estimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateAtCompletionLaborUnits(Double value)
      {
         this.estimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateAtCompletionLaborUnits()
      {
         return cumulativeEstimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateAtCompletionLaborUnits(Double value)
      {
         this.cumulativeEstimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the estimateToCompleteLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateToCompleteLaborUnits()
      {
         return estimateToCompleteLaborUnits;
      }

      /**
       * Sets the value of the estimateToCompleteLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateToCompleteLaborUnits(Double value)
      {
         this.estimateToCompleteLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEstimateToCompleteLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateToCompleteLaborUnits()
      {
         return cumulativeEstimateToCompleteLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEstimateToCompleteLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateToCompleteLaborUnits(Double value)
      {
         this.cumulativeEstimateToCompleteLaborUnits = value;
      }

      /**
       * Gets the value of the periodActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualLaborUnits()
      {
         return periodActualLaborUnits;
      }

      /**
       * Sets the value of the periodActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualLaborUnits(Double value)
      {
         this.periodActualLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualLaborUnits()
      {
         return cumulativePeriodActualLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualLaborUnits(Double value)
      {
         this.cumulativePeriodActualLaborUnits = value;
      }

      /**
       * Gets the value of the periodActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualNonLaborUnits()
      {
         return periodActualNonLaborUnits;
      }

      /**
       * Sets the value of the periodActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualNonLaborUnits(Double value)
      {
         this.periodActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualNonLaborUnits()
      {
         return cumulativePeriodActualNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualNonLaborUnits(Double value)
      {
         this.cumulativePeriodActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the periodAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionLaborUnits()
      {
         return periodAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the periodAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionLaborUnits(Double value)
      {
         this.periodAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionLaborUnits()
      {
         return cumulativePeriodAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionLaborUnits(Double value)
      {
         this.cumulativePeriodAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the periodAtCompletionNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionNonLaborUnits()
      {
         return periodAtCompletionNonLaborUnits;
      }

      /**
       * Sets the value of the periodAtCompletionNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionNonLaborUnits(Double value)
      {
         this.periodAtCompletionNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionNonLaborUnits()
      {
         return cumulativePeriodAtCompletionNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionNonLaborUnits(Double value)
      {
         this.cumulativePeriodAtCompletionNonLaborUnits = value;
      }

      /**
       * Gets the value of the periodEarnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodEarnedValueLaborUnits()
      {
         return periodEarnedValueLaborUnits;
      }

      /**
       * Sets the value of the periodEarnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodEarnedValueLaborUnits(Double value)
      {
         this.periodEarnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodEarnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodEarnedValueLaborUnits()
      {
         return cumulativePeriodEarnedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodEarnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodEarnedValueLaborUnits(Double value)
      {
         this.cumulativePeriodEarnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the periodEstimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodEstimateAtCompletionLaborUnits()
      {
         return periodEstimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the periodEstimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodEstimateAtCompletionLaborUnits(Double value)
      {
         this.periodEstimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodEstimateAtCompletionLaborUnits()
      {
         return cumulativePeriodEstimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodEstimateAtCompletionLaborUnits(Double value)
      {
         this.cumulativePeriodEstimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the periodPlannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodPlannedValueLaborUnits()
      {
         return periodPlannedValueLaborUnits;
      }

      /**
       * Sets the value of the periodPlannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodPlannedValueLaborUnits(Double value)
      {
         this.periodPlannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodPlannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodPlannedValueLaborUnits()
      {
         return cumulativePeriodPlannedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativePeriodPlannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodPlannedValueLaborUnits(Double value)
      {
         this.cumulativePeriodPlannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the plannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedLaborUnits()
      {
         return plannedLaborUnits;
      }

      /**
       * Sets the value of the plannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedLaborUnits(Double value)
      {
         this.plannedLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedLaborUnits()
      {
         return cumulativePlannedLaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedLaborUnits(Double value)
      {
         this.cumulativePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the plannedNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedNonlaborUnits()
      {
         return plannedNonlaborUnits;
      }

      /**
       * Sets the value of the plannedNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedNonlaborUnits(Double value)
      {
         this.plannedNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedNonlaborUnits()
      {
         return cumulativePlannedNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedNonlaborUnits(Double value)
      {
         this.cumulativePlannedNonlaborUnits = value;
      }

      /**
       * Gets the value of the plannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedValueLaborUnits()
      {
         return plannedValueLaborUnits;
      }

      /**
       * Sets the value of the plannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedValueLaborUnits(Double value)
      {
         this.plannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedValueLaborUnits()
      {
         return cumulativePlannedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedValueLaborUnits(Double value)
      {
         this.cumulativePlannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLaborUnits()
      {
         return remainingLaborUnits;
      }

      /**
       * Sets the value of the remainingLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLaborUnits(Double value)
      {
         this.remainingLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLaborUnits()
      {
         return cumulativeRemainingLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLaborUnits(Double value)
      {
         this.cumulativeRemainingLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLateLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateLaborUnits()
      {
         return remainingLateLaborUnits;
      }

      /**
       * Sets the value of the remainingLateLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateLaborUnits(Double value)
      {
         this.remainingLateLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateLaborUnits()
      {
         return cumulativeRemainingLateLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateLaborUnits(Double value)
      {
         this.cumulativeRemainingLateLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLateNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateNonlaborUnits()
      {
         return remainingLateNonlaborUnits;
      }

      /**
       * Sets the value of the remainingLateNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateNonlaborUnits(Double value)
      {
         this.remainingLateNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateNonlaborUnits()
      {
         return cumulativeRemainingLateNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateNonlaborUnits(Double value)
      {
         this.cumulativeRemainingLateNonlaborUnits = value;
      }

      /**
       * Gets the value of the remainingNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingNonlaborUnits()
      {
         return remainingNonlaborUnits;
      }

      /**
       * Sets the value of the remainingNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingNonlaborUnits(Double value)
      {
         this.remainingNonlaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingNonlaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingNonlaborUnits()
      {
         return cumulativeRemainingNonlaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingNonlaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingNonlaborUnits(Double value)
      {
         this.cumulativeRemainingNonlaborUnits = value;
      }

      /**
       * Gets the value of the actualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualCost()
      {
         return actualCost;
      }

      /**
       * Sets the value of the actualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualCost(Double value)
      {
         this.actualCost = value;
      }

      /**
       * Gets the value of the cumulativeActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualCost()
      {
         return cumulativeActualCost;
      }

      /**
       * Sets the value of the cumulativeActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualCost(Double value)
      {
         this.cumulativeActualCost = value;
      }

      /**
       * Gets the value of the actualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualExpenseCost()
      {
         return actualExpenseCost;
      }

      /**
       * Sets the value of the actualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualExpenseCost(Double value)
      {
         this.actualExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualExpenseCost()
      {
         return cumulativeActualExpenseCost;
      }

      /**
       * Sets the value of the cumulativeActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualExpenseCost(Double value)
      {
         this.cumulativeActualExpenseCost = value;
      }

      /**
       * Gets the value of the actualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualLaborCost()
      {
         return actualLaborCost;
      }

      /**
       * Sets the value of the actualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualLaborCost(Double value)
      {
         this.actualLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualLaborCost()
      {
         return cumulativeActualLaborCost;
      }

      /**
       * Sets the value of the cumulativeActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualLaborCost(Double value)
      {
         this.cumulativeActualLaborCost = value;
      }

      /**
       * Gets the value of the actualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualMaterialCost()
      {
         return actualMaterialCost;
      }

      /**
       * Sets the value of the actualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualMaterialCost(Double value)
      {
         this.actualMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualMaterialCost()
      {
         return cumulativeActualMaterialCost;
      }

      /**
       * Sets the value of the cumulativeActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualMaterialCost(Double value)
      {
         this.cumulativeActualMaterialCost = value;
      }

      /**
       * Gets the value of the actualNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualNonlaborCost()
      {
         return actualNonlaborCost;
      }

      /**
       * Sets the value of the actualNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualNonlaborCost(Double value)
      {
         this.actualNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativeActualNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualNonlaborCost()
      {
         return cumulativeActualNonlaborCost;
      }

      /**
       * Sets the value of the cumulativeActualNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualNonlaborCost(Double value)
      {
         this.cumulativeActualNonlaborCost = value;
      }

      /**
       * Gets the value of the actualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualTotalCost()
      {
         return actualTotalCost;
      }

      /**
       * Sets the value of the actualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualTotalCost(Double value)
      {
         this.actualTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualTotalCost()
      {
         return cumulativeActualTotalCost;
      }

      /**
       * Sets the value of the cumulativeActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualTotalCost(Double value)
      {
         this.cumulativeActualTotalCost = value;
      }

      /**
       * Gets the value of the atCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionExpenseCost()
      {
         return atCompletionExpenseCost;
      }

      /**
       * Sets the value of the atCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionExpenseCost(Double value)
      {
         this.atCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionExpenseCost()
      {
         return cumulativeAtCompletionExpenseCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionExpenseCost(Double value)
      {
         this.cumulativeAtCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the atCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionLaborCost()
      {
         return atCompletionLaborCost;
      }

      /**
       * Sets the value of the atCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionLaborCost(Double value)
      {
         this.atCompletionLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionLaborCost()
      {
         return cumulativeAtCompletionLaborCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionLaborCost(Double value)
      {
         this.cumulativeAtCompletionLaborCost = value;
      }

      /**
       * Gets the value of the atCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionMaterialCost()
      {
         return atCompletionMaterialCost;
      }

      /**
       * Sets the value of the atCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionMaterialCost(Double value)
      {
         this.atCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionMaterialCost()
      {
         return cumulativeAtCompletionMaterialCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionMaterialCost(Double value)
      {
         this.cumulativeAtCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the atCompletionNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionNonlaborCost()
      {
         return atCompletionNonlaborCost;
      }

      /**
       * Sets the value of the atCompletionNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionNonlaborCost(Double value)
      {
         this.atCompletionNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionNonlaborCost()
      {
         return cumulativeAtCompletionNonlaborCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionNonlaborCost(Double value)
      {
         this.cumulativeAtCompletionNonlaborCost = value;
      }

      /**
       * Gets the value of the atCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionTotalCost()
      {
         return atCompletionTotalCost;
      }

      /**
       * Sets the value of the atCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionTotalCost(Double value)
      {
         this.atCompletionTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionTotalCost()
      {
         return cumulativeAtCompletionTotalCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionTotalCost(Double value)
      {
         this.cumulativeAtCompletionTotalCost = value;
      }

      /**
       * Gets the value of the baselinePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedExpenseCost()
      {
         return baselinePlannedExpenseCost;
      }

      /**
       * Sets the value of the baselinePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedExpenseCost(Double value)
      {
         this.baselinePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedExpenseCost()
      {
         return cumulativeBaselinePlannedExpenseCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedExpenseCost(Double value)
      {
         this.cumulativeBaselinePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the baselinePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedLaborCost()
      {
         return baselinePlannedLaborCost;
      }

      /**
       * Sets the value of the baselinePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedLaborCost(Double value)
      {
         this.baselinePlannedLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedLaborCost()
      {
         return cumulativeBaselinePlannedLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedLaborCost(Double value)
      {
         this.cumulativeBaselinePlannedLaborCost = value;
      }

      /**
       * Gets the value of the baselinePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedMaterialCost()
      {
         return baselinePlannedMaterialCost;
      }

      /**
       * Sets the value of the baselinePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedMaterialCost(Double value)
      {
         this.baselinePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedMaterialCost()
      {
         return cumulativeBaselinePlannedMaterialCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedMaterialCost(Double value)
      {
         this.cumulativeBaselinePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the baselinePlannedNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedNonlaborCost()
      {
         return baselinePlannedNonlaborCost;
      }

      /**
       * Sets the value of the baselinePlannedNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedNonlaborCost(Double value)
      {
         this.baselinePlannedNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedNonlaborCost()
      {
         return cumulativeBaselinePlannedNonlaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedNonlaborCost(Double value)
      {
         this.cumulativeBaselinePlannedNonlaborCost = value;
      }

      /**
       * Gets the value of the baselinePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedTotalCost()
      {
         return baselinePlannedTotalCost;
      }

      /**
       * Sets the value of the baselinePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedTotalCost(Double value)
      {
         this.baselinePlannedTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedTotalCost()
      {
         return cumulativeBaselinePlannedTotalCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedTotalCost(Double value)
      {
         this.cumulativeBaselinePlannedTotalCost = value;
      }

      /**
       * Gets the value of the earnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEarnedValueCost()
      {
         return earnedValueCost;
      }

      /**
       * Sets the value of the earnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEarnedValueCost(Double value)
      {
         this.earnedValueCost = value;
      }

      /**
       * Gets the value of the cumulativeEarnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEarnedValueCost()
      {
         return cumulativeEarnedValueCost;
      }

      /**
       * Sets the value of the cumulativeEarnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEarnedValueCost(Double value)
      {
         this.cumulativeEarnedValueCost = value;
      }

      /**
       * Gets the value of the estimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateAtCompletionCost()
      {
         return estimateAtCompletionCost;
      }

      /**
       * Sets the value of the estimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateAtCompletionCost(Double value)
      {
         this.estimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeEstimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateAtCompletionCost()
      {
         return cumulativeEstimateAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeEstimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateAtCompletionCost(Double value)
      {
         this.cumulativeEstimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the estimateToCompleteCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateToCompleteCost()
      {
         return estimateToCompleteCost;
      }

      /**
       * Sets the value of the estimateToCompleteCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateToCompleteCost(Double value)
      {
         this.estimateToCompleteCost = value;
      }

      /**
       * Gets the value of the cumulativeEstimateToCompleteCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateToCompleteCost()
      {
         return cumulativeEstimateToCompleteCost;
      }

      /**
       * Sets the value of the cumulativeEstimateToCompleteCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateToCompleteCost(Double value)
      {
         this.cumulativeEstimateToCompleteCost = value;
      }

      /**
       * Gets the value of the periodActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualCost()
      {
         return periodActualCost;
      }

      /**
       * Sets the value of the periodActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualCost(Double value)
      {
         this.periodActualCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualCost()
      {
         return cumulativePeriodActualCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualCost(Double value)
      {
         this.cumulativePeriodActualCost = value;
      }

      /**
       * Gets the value of the periodActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualExpenseCost()
      {
         return periodActualExpenseCost;
      }

      /**
       * Sets the value of the periodActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualExpenseCost(Double value)
      {
         this.periodActualExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualExpenseCost()
      {
         return cumulativePeriodActualExpenseCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualExpenseCost(Double value)
      {
         this.cumulativePeriodActualExpenseCost = value;
      }

      /**
       * Gets the value of the periodActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualLaborCost()
      {
         return periodActualLaborCost;
      }

      /**
       * Sets the value of the periodActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualLaborCost(Double value)
      {
         this.periodActualLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualLaborCost()
      {
         return cumulativePeriodActualLaborCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualLaborCost(Double value)
      {
         this.cumulativePeriodActualLaborCost = value;
      }

      /**
       * Gets the value of the periodActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualMaterialCost()
      {
         return periodActualMaterialCost;
      }

      /**
       * Sets the value of the periodActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualMaterialCost(Double value)
      {
         this.periodActualMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualMaterialCost()
      {
         return cumulativePeriodActualMaterialCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualMaterialCost(Double value)
      {
         this.cumulativePeriodActualMaterialCost = value;
      }

      /**
       * Gets the value of the periodActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualNonLaborCost()
      {
         return periodActualNonLaborCost;
      }

      /**
       * Sets the value of the periodActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualNonLaborCost(Double value)
      {
         this.periodActualNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualNonLaborCost()
      {
         return cumulativePeriodActualNonLaborCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualNonLaborCost(Double value)
      {
         this.cumulativePeriodActualNonLaborCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionExpenseCost()
      {
         return periodAtCompletionExpenseCost;
      }

      /**
       * Sets the value of the periodAtCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionExpenseCost(Double value)
      {
         this.periodAtCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionExpenseCost()
      {
         return cumulativePeriodAtCompletionExpenseCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionExpenseCost(Double value)
      {
         this.cumulativePeriodAtCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionLaborCost()
      {
         return periodAtCompletionLaborCost;
      }

      /**
       * Sets the value of the periodAtCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionLaborCost(Double value)
      {
         this.periodAtCompletionLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionLaborCost()
      {
         return cumulativePeriodAtCompletionLaborCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionLaborCost(Double value)
      {
         this.cumulativePeriodAtCompletionLaborCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionMaterialCost()
      {
         return periodAtCompletionMaterialCost;
      }

      /**
       * Sets the value of the periodAtCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionMaterialCost(Double value)
      {
         this.periodAtCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionMaterialCost()
      {
         return cumulativePeriodAtCompletionMaterialCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionMaterialCost(Double value)
      {
         this.cumulativePeriodAtCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionNonLaborCost()
      {
         return periodAtCompletionNonLaborCost;
      }

      /**
       * Sets the value of the periodAtCompletionNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionNonLaborCost(Double value)
      {
         this.periodAtCompletionNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionNonLaborCost()
      {
         return cumulativePeriodAtCompletionNonLaborCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionNonLaborCost(Double value)
      {
         this.cumulativePeriodAtCompletionNonLaborCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionTotalCost()
      {
         return periodAtCompletionTotalCost;
      }

      /**
       * Sets the value of the periodAtCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionTotalCost(Double value)
      {
         this.periodAtCompletionTotalCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionTotalCost()
      {
         return cumulativePeriodAtCompletionTotalCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionTotalCost(Double value)
      {
         this.cumulativePeriodAtCompletionTotalCost = value;
      }

      /**
       * Gets the value of the periodEarnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodEarnedValueCost()
      {
         return periodEarnedValueCost;
      }

      /**
       * Sets the value of the periodEarnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodEarnedValueCost(Double value)
      {
         this.periodEarnedValueCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodEarnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodEarnedValueCost()
      {
         return cumulativePeriodEarnedValueCost;
      }

      /**
       * Sets the value of the cumulativePeriodEarnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodEarnedValueCost(Double value)
      {
         this.cumulativePeriodEarnedValueCost = value;
      }

      /**
       * Gets the value of the periodEstimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodEstimateAtCompletionCost()
      {
         return periodEstimateAtCompletionCost;
      }

      /**
       * Sets the value of the periodEstimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodEstimateAtCompletionCost(Double value)
      {
         this.periodEstimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodEstimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodEstimateAtCompletionCost()
      {
         return cumulativePeriodEstimateAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativePeriodEstimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodEstimateAtCompletionCost(Double value)
      {
         this.cumulativePeriodEstimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the periodPlannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodPlannedValueCost()
      {
         return periodPlannedValueCost;
      }

      /**
       * Sets the value of the periodPlannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodPlannedValueCost(Double value)
      {
         this.periodPlannedValueCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodPlannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodPlannedValueCost()
      {
         return cumulativePeriodPlannedValueCost;
      }

      /**
       * Sets the value of the cumulativePeriodPlannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodPlannedValueCost(Double value)
      {
         this.cumulativePeriodPlannedValueCost = value;
      }

      /**
       * Gets the value of the plannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedExpenseCost()
      {
         return plannedExpenseCost;
      }

      /**
       * Sets the value of the plannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedExpenseCost(Double value)
      {
         this.plannedExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedExpenseCost()
      {
         return cumulativePlannedExpenseCost;
      }

      /**
       * Sets the value of the cumulativePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedExpenseCost(Double value)
      {
         this.cumulativePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the plannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedLaborCost()
      {
         return plannedLaborCost;
      }

      /**
       * Sets the value of the plannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedLaborCost(Double value)
      {
         this.plannedLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedLaborCost()
      {
         return cumulativePlannedLaborCost;
      }

      /**
       * Sets the value of the cumulativePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedLaborCost(Double value)
      {
         this.cumulativePlannedLaborCost = value;
      }

      /**
       * Gets the value of the plannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedMaterialCost()
      {
         return plannedMaterialCost;
      }

      /**
       * Sets the value of the plannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedMaterialCost(Double value)
      {
         this.plannedMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedMaterialCost()
      {
         return cumulativePlannedMaterialCost;
      }

      /**
       * Sets the value of the cumulativePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedMaterialCost(Double value)
      {
         this.cumulativePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the plannedNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedNonlaborCost()
      {
         return plannedNonlaborCost;
      }

      /**
       * Sets the value of the plannedNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedNonlaborCost(Double value)
      {
         this.plannedNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedNonlaborCost()
      {
         return cumulativePlannedNonlaborCost;
      }

      /**
       * Sets the value of the cumulativePlannedNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedNonlaborCost(Double value)
      {
         this.cumulativePlannedNonlaborCost = value;
      }

      /**
       * Gets the value of the plannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedTotalCost()
      {
         return plannedTotalCost;
      }

      /**
       * Sets the value of the plannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedTotalCost(Double value)
      {
         this.plannedTotalCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedTotalCost()
      {
         return cumulativePlannedTotalCost;
      }

      /**
       * Sets the value of the cumulativePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedTotalCost(Double value)
      {
         this.cumulativePlannedTotalCost = value;
      }

      /**
       * Gets the value of the plannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedValueCost()
      {
         return plannedValueCost;
      }

      /**
       * Sets the value of the plannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedValueCost(Double value)
      {
         this.plannedValueCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedValueCost()
      {
         return cumulativePlannedValueCost;
      }

      /**
       * Sets the value of the cumulativePlannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedValueCost(Double value)
      {
         this.cumulativePlannedValueCost = value;
      }

      /**
       * Gets the value of the remainingExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingExpenseCost()
      {
         return remainingExpenseCost;
      }

      /**
       * Sets the value of the remainingExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingExpenseCost(Double value)
      {
         this.remainingExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingExpenseCost()
      {
         return cumulativeRemainingExpenseCost;
      }

      /**
       * Sets the value of the cumulativeRemainingExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingExpenseCost(Double value)
      {
         this.cumulativeRemainingExpenseCost = value;
      }

      /**
       * Gets the value of the remainingLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLaborCost()
      {
         return remainingLaborCost;
      }

      /**
       * Sets the value of the remainingLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLaborCost(Double value)
      {
         this.remainingLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLaborCost()
      {
         return cumulativeRemainingLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLaborCost(Double value)
      {
         this.cumulativeRemainingLaborCost = value;
      }

      /**
       * Gets the value of the remainingLateExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateExpenseCost()
      {
         return remainingLateExpenseCost;
      }

      /**
       * Sets the value of the remainingLateExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateExpenseCost(Double value)
      {
         this.remainingLateExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateExpenseCost()
      {
         return cumulativeRemainingLateExpenseCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateExpenseCost(Double value)
      {
         this.cumulativeRemainingLateExpenseCost = value;
      }

      /**
       * Gets the value of the remainingLateLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateLaborCost()
      {
         return remainingLateLaborCost;
      }

      /**
       * Sets the value of the remainingLateLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateLaborCost(Double value)
      {
         this.remainingLateLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateLaborCost()
      {
         return cumulativeRemainingLateLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateLaborCost(Double value)
      {
         this.cumulativeRemainingLateLaborCost = value;
      }

      /**
       * Gets the value of the remainingLateMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateMaterialCost()
      {
         return remainingLateMaterialCost;
      }

      /**
       * Sets the value of the remainingLateMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateMaterialCost(Double value)
      {
         this.remainingLateMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateMaterialCost()
      {
         return cumulativeRemainingLateMaterialCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateMaterialCost(Double value)
      {
         this.cumulativeRemainingLateMaterialCost = value;
      }

      /**
       * Gets the value of the remainingLateNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateNonlaborCost()
      {
         return remainingLateNonlaborCost;
      }

      /**
       * Sets the value of the remainingLateNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateNonlaborCost(Double value)
      {
         this.remainingLateNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateNonlaborCost()
      {
         return cumulativeRemainingLateNonlaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateNonlaborCost(Double value)
      {
         this.cumulativeRemainingLateNonlaborCost = value;
      }

      /**
       * Gets the value of the remainingLateTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateTotalCost()
      {
         return remainingLateTotalCost;
      }

      /**
       * Sets the value of the remainingLateTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateTotalCost(Double value)
      {
         this.remainingLateTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateTotalCost()
      {
         return cumulativeRemainingLateTotalCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateTotalCost(Double value)
      {
         this.cumulativeRemainingLateTotalCost = value;
      }

      /**
       * Gets the value of the remainingMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingMaterialCost()
      {
         return remainingMaterialCost;
      }

      /**
       * Sets the value of the remainingMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingMaterialCost(Double value)
      {
         this.remainingMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingMaterialCost()
      {
         return cumulativeRemainingMaterialCost;
      }

      /**
       * Sets the value of the cumulativeRemainingMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingMaterialCost(Double value)
      {
         this.cumulativeRemainingMaterialCost = value;
      }

      /**
       * Gets the value of the remainingNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingNonlaborCost()
      {
         return remainingNonlaborCost;
      }

      /**
       * Sets the value of the remainingNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingNonlaborCost(Double value)
      {
         this.remainingNonlaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingNonlaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingNonlaborCost()
      {
         return cumulativeRemainingNonlaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingNonlaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingNonlaborCost(Double value)
      {
         this.cumulativeRemainingNonlaborCost = value;
      }

      /**
       * Gets the value of the remainingTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingTotalCost()
      {
         return remainingTotalCost;
      }

      /**
       * Sets the value of the remainingTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingTotalCost(Double value)
      {
         this.remainingTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingTotalCost()
      {
         return cumulativeRemainingTotalCost;
      }

      /**
       * Sets the value of the cumulativeRemainingTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingTotalCost(Double value)
      {
         this.cumulativeRemainingTotalCost = value;
      }

   }

}
