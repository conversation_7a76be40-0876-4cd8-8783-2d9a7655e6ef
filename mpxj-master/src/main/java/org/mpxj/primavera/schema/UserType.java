//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for UserType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="UserType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="AllResourceAccessFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AssignmentStaffingPreference" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="New"/&gt;
 *               &lt;enumeration value="Existing"/&gt;
 *               &lt;enumeration value="Ask Me"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="CurrencyId" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="6"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CurrencyName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="80"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CurrencyObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CurrencyShowDecimals" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="CurrencyShowSymbol" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DateFormatType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Month, Day, Year"/&gt;
 *               &lt;enumeration value="Day, Month, Year"/&gt;
 *               &lt;enumeration value="Year, Month, Day"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DateSeparator" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="/"/&gt;
 *               &lt;enumeration value="-"/&gt;
 *               &lt;enumeration value="."/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DateShowFourDigitYear" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DateShowMinutes" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DateTimeFormatType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="12 hour (1:30 PM)"/&gt;
 *               &lt;enumeration value="24 hour (13:30)"/&gt;
 *               &lt;enumeration value="Do not show time"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DateUseLeadingZero" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DateUseMonthName" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DoNotShowNewFeaturesAgain" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="DurationDecimalCount" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="0"/&gt;
 *               &lt;enumeration value="1"/&gt;
 *               &lt;enumeration value="2"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DurationUnitType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Days Hours"/&gt;
 *               &lt;enumeration value="Hours Minutes"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DurationUseFraction" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EditGlobalUserPreferences" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EmailAddress" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EmailProtocol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Internet Mail"/&gt;
 *               &lt;enumeration value="MAPI Mail"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EnableUserToModifyViewSettingsFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="FinancialPeriodEndObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="FinancialPeriodStartObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="GUID" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;pattern value="\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}|"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="GlobalProfileObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="MailServerLoginName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Name" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="NewProjectDurationType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Fixed Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Units"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="OfficePhone" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="32"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="OutgoingMailServer" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="120"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PersonalName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RateSourcePreference" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Resource"/&gt;
 *               &lt;enumeration value="Role"/&gt;
 *               &lt;enumeration value="Ask Me"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ReportingFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="RespectActivityDurationType" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="RoleLimitDisplayOption" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Custom Role Limit"/&gt;
 *               &lt;enumeration value="Calculated Primary Resources Limit"/&gt;
 *               &lt;enumeration value="Calculated Primary Active Resources Limit"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ShowDurationTimeUnit" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ShowTimeUnit" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="SmallScaleDecimalCount" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="0"/&gt;
 *               &lt;enumeration value="1"/&gt;
 *               &lt;enumeration value="2"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SmallScaleUnitType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Days Hours"/&gt;
 *               &lt;enumeration value="Hours Minutes"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SmallScaleUseFraction" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="TMSelectedActivityFilters" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TMSelectedActivityFiltersJoin" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TMSelectedTimesheetFilters" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TMSelectedTimesheetFiltersJoin" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberActivityFilters" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberActivityFiltersJoin" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberActivitySortField" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberActivitySortOrder" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberAllTimeframeForCompleted" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberApplicationTheme" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberDateFormat" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberDisplayQRQuickAccess" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberDisplayTimeFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberDisplayTimeFormat" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberLocale" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberProjectFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberResourceFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberTaskStatusFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberTimeframeFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberWBSFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberWorkUnitType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Days Hours"/&gt;
 *               &lt;enumeration value="Hours Minutes"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TimesheetProjectFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TimesheetWBSFilter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UnitsPerTimeShowAsPercentage" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="UserInterfaceViewObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ResourceRequests" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="ResourceRequest" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceRequestType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "UserType", propOrder =
{
   "allResourceAccessFlag",
   "assignmentStaffingPreference",
   "createDate",
   "createUser",
   "currencyId",
   "currencyName",
   "currencyObjectId",
   "currencyShowDecimals",
   "currencyShowSymbol",
   "dateFormatType",
   "dateSeparator",
   "dateShowFourDigitYear",
   "dateShowMinutes",
   "dateTimeFormatType",
   "dateUseLeadingZero",
   "dateUseMonthName",
   "doNotShowNewFeaturesAgain",
   "durationDecimalCount",
   "durationUnitType",
   "durationUseFraction",
   "editGlobalUserPreferences",
   "emailAddress",
   "emailProtocol",
   "enableUserToModifyViewSettingsFlag",
   "financialPeriodEndObjectId",
   "financialPeriodStartObjectId",
   "guid",
   "globalProfileObjectId",
   "lastUpdateDate",
   "lastUpdateUser",
   "mailServerLoginName",
   "name",
   "newProjectDurationType",
   "objectId",
   "officePhone",
   "outgoingMailServer",
   "personalName",
   "rateSourcePreference",
   "reportingFlag",
   "respectActivityDurationType",
   "roleLimitDisplayOption",
   "showDurationTimeUnit",
   "showTimeUnit",
   "smallScaleDecimalCount",
   "smallScaleUnitType",
   "smallScaleUseFraction",
   "tmSelectedActivityFilters",
   "tmSelectedActivityFiltersJoin",
   "tmSelectedTimesheetFilters",
   "tmSelectedTimesheetFiltersJoin",
   "teamMemberActivityFilters",
   "teamMemberActivityFiltersJoin",
   "teamMemberActivitySortField",
   "teamMemberActivitySortOrder",
   "teamMemberAllTimeframeForCompleted",
   "teamMemberApplicationTheme",
   "teamMemberDateFormat",
   "teamMemberDisplayQRQuickAccess",
   "teamMemberDisplayTimeFlag",
   "teamMemberDisplayTimeFormat",
   "teamMemberLocale",
   "teamMemberProjectFilter",
   "teamMemberResourceFilter",
   "teamMemberTaskStatusFilter",
   "teamMemberTimeframeFilter",
   "teamMemberWBSFilter",
   "teamMemberWorkUnitType",
   "timesheetProjectFilter",
   "timesheetWBSFilter",
   "unitsPerTimeShowAsPercentage",
   "userInterfaceViewObjectId",
   "resourceRequests"
}) public class UserType
{

   @XmlElement(name = "AllResourceAccessFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean allResourceAccessFlag;
   @XmlElement(name = "AssignmentStaffingPreference") @XmlJavaTypeAdapter(Adapter1.class) protected String assignmentStaffingPreference;
   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "CurrencyId") @XmlJavaTypeAdapter(Adapter1.class) protected String currencyId;
   @XmlElement(name = "CurrencyName") @XmlJavaTypeAdapter(Adapter1.class) protected String currencyName;
   @XmlElement(name = "CurrencyObjectId") protected Integer currencyObjectId;
   @XmlElement(name = "CurrencyShowDecimals", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean currencyShowDecimals;
   @XmlElement(name = "CurrencyShowSymbol", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean currencyShowSymbol;
   @XmlElement(name = "DateFormatType") @XmlJavaTypeAdapter(Adapter1.class) protected String dateFormatType;
   @XmlElement(name = "DateSeparator") @XmlJavaTypeAdapter(Adapter1.class) protected String dateSeparator;
   @XmlElement(name = "DateShowFourDigitYear", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean dateShowFourDigitYear;
   @XmlElement(name = "DateShowMinutes", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean dateShowMinutes;
   @XmlElement(name = "DateTimeFormatType") @XmlJavaTypeAdapter(Adapter1.class) protected String dateTimeFormatType;
   @XmlElement(name = "DateUseLeadingZero", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean dateUseLeadingZero;
   @XmlElement(name = "DateUseMonthName", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean dateUseMonthName;
   @XmlElement(name = "DoNotShowNewFeaturesAgain", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean doNotShowNewFeaturesAgain;
   @XmlElement(name = "DurationDecimalCount") @XmlJavaTypeAdapter(Adapter1.class) protected String durationDecimalCount;
   @XmlElement(name = "DurationUnitType") @XmlJavaTypeAdapter(Adapter1.class) protected String durationUnitType;
   @XmlElement(name = "DurationUseFraction", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean durationUseFraction;
   @XmlElement(name = "EditGlobalUserPreferences", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean editGlobalUserPreferences;
   @XmlElement(name = "EmailAddress") @XmlJavaTypeAdapter(Adapter1.class) protected String emailAddress;
   @XmlElement(name = "EmailProtocol") @XmlJavaTypeAdapter(Adapter1.class) protected String emailProtocol;
   @XmlElement(name = "EnableUserToModifyViewSettingsFlag", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enableUserToModifyViewSettingsFlag;
   @XmlElement(name = "FinancialPeriodEndObjectId", nillable = true) protected Integer financialPeriodEndObjectId;
   @XmlElement(name = "FinancialPeriodStartObjectId", nillable = true) protected Integer financialPeriodStartObjectId;
   @XmlElement(name = "GUID") @XmlJavaTypeAdapter(Adapter1.class) protected String guid;
   @XmlElement(name = "GlobalProfileObjectId") protected Integer globalProfileObjectId;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "MailServerLoginName") @XmlJavaTypeAdapter(Adapter1.class) protected String mailServerLoginName;
   @XmlElement(name = "Name") @XmlJavaTypeAdapter(Adapter1.class) protected String name;
   @XmlElement(name = "NewProjectDurationType") @XmlJavaTypeAdapter(Adapter1.class) protected String newProjectDurationType;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "OfficePhone") @XmlJavaTypeAdapter(Adapter1.class) protected String officePhone;
   @XmlElement(name = "OutgoingMailServer") @XmlJavaTypeAdapter(Adapter1.class) protected String outgoingMailServer;
   @XmlElement(name = "PersonalName") @XmlJavaTypeAdapter(Adapter1.class) protected String personalName;
   @XmlElement(name = "RateSourcePreference") @XmlJavaTypeAdapter(Adapter1.class) protected String rateSourcePreference;
   @XmlElement(name = "ReportingFlag", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean reportingFlag;
   @XmlElement(name = "RespectActivityDurationType", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean respectActivityDurationType;
   @XmlElement(name = "RoleLimitDisplayOption") @XmlJavaTypeAdapter(Adapter1.class) protected String roleLimitDisplayOption;
   @XmlElement(name = "ShowDurationTimeUnit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean showDurationTimeUnit;
   @XmlElement(name = "ShowTimeUnit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean showTimeUnit;
   @XmlElement(name = "SmallScaleDecimalCount") @XmlJavaTypeAdapter(Adapter1.class) protected String smallScaleDecimalCount;
   @XmlElement(name = "SmallScaleUnitType") @XmlJavaTypeAdapter(Adapter1.class) protected String smallScaleUnitType;
   @XmlElement(name = "SmallScaleUseFraction", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean smallScaleUseFraction;
   @XmlElement(name = "TMSelectedActivityFilters") @XmlJavaTypeAdapter(Adapter1.class) protected String tmSelectedActivityFilters;
   @XmlElement(name = "TMSelectedActivityFiltersJoin") @XmlJavaTypeAdapter(Adapter1.class) protected String tmSelectedActivityFiltersJoin;
   @XmlElement(name = "TMSelectedTimesheetFilters") @XmlJavaTypeAdapter(Adapter1.class) protected String tmSelectedTimesheetFilters;
   @XmlElement(name = "TMSelectedTimesheetFiltersJoin") @XmlJavaTypeAdapter(Adapter1.class) protected String tmSelectedTimesheetFiltersJoin;
   @XmlElement(name = "TeamMemberActivityFilters") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberActivityFilters;
   @XmlElement(name = "TeamMemberActivityFiltersJoin") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberActivityFiltersJoin;
   @XmlElement(name = "TeamMemberActivitySortField") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberActivitySortField;
   @XmlElement(name = "TeamMemberActivitySortOrder") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberActivitySortOrder;
   @XmlElement(name = "TeamMemberAllTimeframeForCompleted") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberAllTimeframeForCompleted;
   @XmlElement(name = "TeamMemberApplicationTheme") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberApplicationTheme;
   @XmlElement(name = "TeamMemberDateFormat") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberDateFormat;
   @XmlElement(name = "TeamMemberDisplayQRQuickAccess") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberDisplayQRQuickAccess;
   @XmlElement(name = "TeamMemberDisplayTimeFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean teamMemberDisplayTimeFlag;
   @XmlElement(name = "TeamMemberDisplayTimeFormat") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberDisplayTimeFormat;
   @XmlElement(name = "TeamMemberLocale") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberLocale;
   @XmlElement(name = "TeamMemberProjectFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberProjectFilter;
   @XmlElement(name = "TeamMemberResourceFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberResourceFilter;
   @XmlElement(name = "TeamMemberTaskStatusFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberTaskStatusFilter;
   @XmlElement(name = "TeamMemberTimeframeFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberTimeframeFilter;
   @XmlElement(name = "TeamMemberWBSFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberWBSFilter;
   @XmlElement(name = "TeamMemberWorkUnitType") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberWorkUnitType;
   @XmlElement(name = "TimesheetProjectFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String timesheetProjectFilter;
   @XmlElement(name = "TimesheetWBSFilter") @XmlJavaTypeAdapter(Adapter1.class) protected String timesheetWBSFilter;
   @XmlElement(name = "UnitsPerTimeShowAsPercentage", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean unitsPerTimeShowAsPercentage;
   @XmlElement(name = "UserInterfaceViewObjectId", nillable = true) protected Integer userInterfaceViewObjectId;
   @XmlElement(name = "ResourceRequests", nillable = true) protected UserType.ResourceRequests resourceRequests;

   /**
    * Gets the value of the allResourceAccessFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAllResourceAccessFlag()
   {
      return allResourceAccessFlag;
   }

   /**
    * Sets the value of the allResourceAccessFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAllResourceAccessFlag(Boolean value)
   {
      this.allResourceAccessFlag = value;
   }

   /**
    * Gets the value of the assignmentStaffingPreference property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getAssignmentStaffingPreference()
   {
      return assignmentStaffingPreference;
   }

   /**
    * Sets the value of the assignmentStaffingPreference property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAssignmentStaffingPreference(String value)
   {
      this.assignmentStaffingPreference = value;
   }

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the currencyId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCurrencyId()
   {
      return currencyId;
   }

   /**
    * Sets the value of the currencyId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrencyId(String value)
   {
      this.currencyId = value;
   }

   /**
    * Gets the value of the currencyName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCurrencyName()
   {
      return currencyName;
   }

   /**
    * Sets the value of the currencyName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrencyName(String value)
   {
      this.currencyName = value;
   }

   /**
    * Gets the value of the currencyObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCurrencyObjectId()
   {
      return currencyObjectId;
   }

   /**
    * Sets the value of the currencyObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCurrencyObjectId(Integer value)
   {
      this.currencyObjectId = value;
   }

   /**
    * Gets the value of the currencyShowDecimals property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isCurrencyShowDecimals()
   {
      return currencyShowDecimals;
   }

   /**
    * Sets the value of the currencyShowDecimals property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrencyShowDecimals(Boolean value)
   {
      this.currencyShowDecimals = value;
   }

   /**
    * Gets the value of the currencyShowSymbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isCurrencyShowSymbol()
   {
      return currencyShowSymbol;
   }

   /**
    * Sets the value of the currencyShowSymbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrencyShowSymbol(Boolean value)
   {
      this.currencyShowSymbol = value;
   }

   /**
    * Gets the value of the dateFormatType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDateFormatType()
   {
      return dateFormatType;
   }

   /**
    * Sets the value of the dateFormatType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateFormatType(String value)
   {
      this.dateFormatType = value;
   }

   /**
    * Gets the value of the dateSeparator property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDateSeparator()
   {
      return dateSeparator;
   }

   /**
    * Sets the value of the dateSeparator property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateSeparator(String value)
   {
      this.dateSeparator = value;
   }

   /**
    * Gets the value of the dateShowFourDigitYear property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDateShowFourDigitYear()
   {
      return dateShowFourDigitYear;
   }

   /**
    * Sets the value of the dateShowFourDigitYear property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateShowFourDigitYear(Boolean value)
   {
      this.dateShowFourDigitYear = value;
   }

   /**
    * Gets the value of the dateShowMinutes property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDateShowMinutes()
   {
      return dateShowMinutes;
   }

   /**
    * Sets the value of the dateShowMinutes property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateShowMinutes(Boolean value)
   {
      this.dateShowMinutes = value;
   }

   /**
    * Gets the value of the dateTimeFormatType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDateTimeFormatType()
   {
      return dateTimeFormatType;
   }

   /**
    * Sets the value of the dateTimeFormatType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateTimeFormatType(String value)
   {
      this.dateTimeFormatType = value;
   }

   /**
    * Gets the value of the dateUseLeadingZero property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDateUseLeadingZero()
   {
      return dateUseLeadingZero;
   }

   /**
    * Sets the value of the dateUseLeadingZero property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateUseLeadingZero(Boolean value)
   {
      this.dateUseLeadingZero = value;
   }

   /**
    * Gets the value of the dateUseMonthName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDateUseMonthName()
   {
      return dateUseMonthName;
   }

   /**
    * Sets the value of the dateUseMonthName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateUseMonthName(Boolean value)
   {
      this.dateUseMonthName = value;
   }

   /**
    * Gets the value of the doNotShowNewFeaturesAgain property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDoNotShowNewFeaturesAgain()
   {
      return doNotShowNewFeaturesAgain;
   }

   /**
    * Sets the value of the doNotShowNewFeaturesAgain property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDoNotShowNewFeaturesAgain(Boolean value)
   {
      this.doNotShowNewFeaturesAgain = value;
   }

   /**
    * Gets the value of the durationDecimalCount property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDurationDecimalCount()
   {
      return durationDecimalCount;
   }

   /**
    * Sets the value of the durationDecimalCount property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationDecimalCount(String value)
   {
      this.durationDecimalCount = value;
   }

   /**
    * Gets the value of the durationUnitType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDurationUnitType()
   {
      return durationUnitType;
   }

   /**
    * Sets the value of the durationUnitType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationUnitType(String value)
   {
      this.durationUnitType = value;
   }

   /**
    * Gets the value of the durationUseFraction property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isDurationUseFraction()
   {
      return durationUseFraction;
   }

   /**
    * Sets the value of the durationUseFraction property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationUseFraction(Boolean value)
   {
      this.durationUseFraction = value;
   }

   /**
    * Gets the value of the editGlobalUserPreferences property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEditGlobalUserPreferences()
   {
      return editGlobalUserPreferences;
   }

   /**
    * Sets the value of the editGlobalUserPreferences property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEditGlobalUserPreferences(Boolean value)
   {
      this.editGlobalUserPreferences = value;
   }

   /**
    * Gets the value of the emailAddress property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEmailAddress()
   {
      return emailAddress;
   }

   /**
    * Sets the value of the emailAddress property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEmailAddress(String value)
   {
      this.emailAddress = value;
   }

   /**
    * Gets the value of the emailProtocol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEmailProtocol()
   {
      return emailProtocol;
   }

   /**
    * Sets the value of the emailProtocol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEmailProtocol(String value)
   {
      this.emailProtocol = value;
   }

   /**
    * Gets the value of the enableUserToModifyViewSettingsFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnableUserToModifyViewSettingsFlag()
   {
      return enableUserToModifyViewSettingsFlag;
   }

   /**
    * Sets the value of the enableUserToModifyViewSettingsFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnableUserToModifyViewSettingsFlag(Boolean value)
   {
      this.enableUserToModifyViewSettingsFlag = value;
   }

   /**
    * Gets the value of the financialPeriodEndObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFinancialPeriodEndObjectId()
   {
      return financialPeriodEndObjectId;
   }

   /**
    * Sets the value of the financialPeriodEndObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFinancialPeriodEndObjectId(Integer value)
   {
      this.financialPeriodEndObjectId = value;
   }

   /**
    * Gets the value of the financialPeriodStartObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFinancialPeriodStartObjectId()
   {
      return financialPeriodStartObjectId;
   }

   /**
    * Sets the value of the financialPeriodStartObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFinancialPeriodStartObjectId(Integer value)
   {
      this.financialPeriodStartObjectId = value;
   }

   /**
    * Gets the value of the guid property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGUID()
   {
      return guid;
   }

   /**
    * Sets the value of the guid property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGUID(String value)
   {
      this.guid = value;
   }

   /**
    * Gets the value of the globalProfileObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getGlobalProfileObjectId()
   {
      return globalProfileObjectId;
   }

   /**
    * Sets the value of the globalProfileObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setGlobalProfileObjectId(Integer value)
   {
      this.globalProfileObjectId = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the mailServerLoginName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getMailServerLoginName()
   {
      return mailServerLoginName;
   }

   /**
    * Sets the value of the mailServerLoginName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMailServerLoginName(String value)
   {
      this.mailServerLoginName = value;
   }

   /**
    * Gets the value of the name property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getName()
   {
      return name;
   }

   /**
    * Sets the value of the name property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setName(String value)
   {
      this.name = value;
   }

   /**
    * Gets the value of the newProjectDurationType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getNewProjectDurationType()
   {
      return newProjectDurationType;
   }

   /**
    * Sets the value of the newProjectDurationType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNewProjectDurationType(String value)
   {
      this.newProjectDurationType = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the officePhone property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOfficePhone()
   {
      return officePhone;
   }

   /**
    * Sets the value of the officePhone property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOfficePhone(String value)
   {
      this.officePhone = value;
   }

   /**
    * Gets the value of the outgoingMailServer property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOutgoingMailServer()
   {
      return outgoingMailServer;
   }

   /**
    * Sets the value of the outgoingMailServer property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOutgoingMailServer(String value)
   {
      this.outgoingMailServer = value;
   }

   /**
    * Gets the value of the personalName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPersonalName()
   {
      return personalName;
   }

   /**
    * Sets the value of the personalName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPersonalName(String value)
   {
      this.personalName = value;
   }

   /**
    * Gets the value of the rateSourcePreference property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRateSourcePreference()
   {
      return rateSourcePreference;
   }

   /**
    * Sets the value of the rateSourcePreference property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRateSourcePreference(String value)
   {
      this.rateSourcePreference = value;
   }

   /**
    * Gets the value of the reportingFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isReportingFlag()
   {
      return reportingFlag;
   }

   /**
    * Sets the value of the reportingFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReportingFlag(Boolean value)
   {
      this.reportingFlag = value;
   }

   /**
    * Gets the value of the respectActivityDurationType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isRespectActivityDurationType()
   {
      return respectActivityDurationType;
   }

   /**
    * Sets the value of the respectActivityDurationType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRespectActivityDurationType(Boolean value)
   {
      this.respectActivityDurationType = value;
   }

   /**
    * Gets the value of the roleLimitDisplayOption property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRoleLimitDisplayOption()
   {
      return roleLimitDisplayOption;
   }

   /**
    * Sets the value of the roleLimitDisplayOption property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRoleLimitDisplayOption(String value)
   {
      this.roleLimitDisplayOption = value;
   }

   /**
    * Gets the value of the showDurationTimeUnit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isShowDurationTimeUnit()
   {
      return showDurationTimeUnit;
   }

   /**
    * Sets the value of the showDurationTimeUnit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setShowDurationTimeUnit(Boolean value)
   {
      this.showDurationTimeUnit = value;
   }

   /**
    * Gets the value of the showTimeUnit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isShowTimeUnit()
   {
      return showTimeUnit;
   }

   /**
    * Sets the value of the showTimeUnit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setShowTimeUnit(Boolean value)
   {
      this.showTimeUnit = value;
   }

   /**
    * Gets the value of the smallScaleDecimalCount property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSmallScaleDecimalCount()
   {
      return smallScaleDecimalCount;
   }

   /**
    * Sets the value of the smallScaleDecimalCount property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSmallScaleDecimalCount(String value)
   {
      this.smallScaleDecimalCount = value;
   }

   /**
    * Gets the value of the smallScaleUnitType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSmallScaleUnitType()
   {
      return smallScaleUnitType;
   }

   /**
    * Sets the value of the smallScaleUnitType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSmallScaleUnitType(String value)
   {
      this.smallScaleUnitType = value;
   }

   /**
    * Gets the value of the smallScaleUseFraction property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isSmallScaleUseFraction()
   {
      return smallScaleUseFraction;
   }

   /**
    * Sets the value of the smallScaleUseFraction property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSmallScaleUseFraction(Boolean value)
   {
      this.smallScaleUseFraction = value;
   }

   /**
    * Gets the value of the tmSelectedActivityFilters property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTMSelectedActivityFilters()
   {
      return tmSelectedActivityFilters;
   }

   /**
    * Sets the value of the tmSelectedActivityFilters property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTMSelectedActivityFilters(String value)
   {
      this.tmSelectedActivityFilters = value;
   }

   /**
    * Gets the value of the tmSelectedActivityFiltersJoin property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTMSelectedActivityFiltersJoin()
   {
      return tmSelectedActivityFiltersJoin;
   }

   /**
    * Sets the value of the tmSelectedActivityFiltersJoin property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTMSelectedActivityFiltersJoin(String value)
   {
      this.tmSelectedActivityFiltersJoin = value;
   }

   /**
    * Gets the value of the tmSelectedTimesheetFilters property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTMSelectedTimesheetFilters()
   {
      return tmSelectedTimesheetFilters;
   }

   /**
    * Sets the value of the tmSelectedTimesheetFilters property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTMSelectedTimesheetFilters(String value)
   {
      this.tmSelectedTimesheetFilters = value;
   }

   /**
    * Gets the value of the tmSelectedTimesheetFiltersJoin property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTMSelectedTimesheetFiltersJoin()
   {
      return tmSelectedTimesheetFiltersJoin;
   }

   /**
    * Sets the value of the tmSelectedTimesheetFiltersJoin property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTMSelectedTimesheetFiltersJoin(String value)
   {
      this.tmSelectedTimesheetFiltersJoin = value;
   }

   /**
    * Gets the value of the teamMemberActivityFilters property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberActivityFilters()
   {
      return teamMemberActivityFilters;
   }

   /**
    * Sets the value of the teamMemberActivityFilters property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberActivityFilters(String value)
   {
      this.teamMemberActivityFilters = value;
   }

   /**
    * Gets the value of the teamMemberActivityFiltersJoin property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberActivityFiltersJoin()
   {
      return teamMemberActivityFiltersJoin;
   }

   /**
    * Sets the value of the teamMemberActivityFiltersJoin property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberActivityFiltersJoin(String value)
   {
      this.teamMemberActivityFiltersJoin = value;
   }

   /**
    * Gets the value of the teamMemberActivitySortField property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberActivitySortField()
   {
      return teamMemberActivitySortField;
   }

   /**
    * Sets the value of the teamMemberActivitySortField property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberActivitySortField(String value)
   {
      this.teamMemberActivitySortField = value;
   }

   /**
    * Gets the value of the teamMemberActivitySortOrder property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberActivitySortOrder()
   {
      return teamMemberActivitySortOrder;
   }

   /**
    * Sets the value of the teamMemberActivitySortOrder property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberActivitySortOrder(String value)
   {
      this.teamMemberActivitySortOrder = value;
   }

   /**
    * Gets the value of the teamMemberAllTimeframeForCompleted property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberAllTimeframeForCompleted()
   {
      return teamMemberAllTimeframeForCompleted;
   }

   /**
    * Sets the value of the teamMemberAllTimeframeForCompleted property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberAllTimeframeForCompleted(String value)
   {
      this.teamMemberAllTimeframeForCompleted = value;
   }

   /**
    * Gets the value of the teamMemberApplicationTheme property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberApplicationTheme()
   {
      return teamMemberApplicationTheme;
   }

   /**
    * Sets the value of the teamMemberApplicationTheme property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberApplicationTheme(String value)
   {
      this.teamMemberApplicationTheme = value;
   }

   /**
    * Gets the value of the teamMemberDateFormat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberDateFormat()
   {
      return teamMemberDateFormat;
   }

   /**
    * Sets the value of the teamMemberDateFormat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberDateFormat(String value)
   {
      this.teamMemberDateFormat = value;
   }

   /**
    * Gets the value of the teamMemberDisplayQRQuickAccess property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberDisplayQRQuickAccess()
   {
      return teamMemberDisplayQRQuickAccess;
   }

   /**
    * Sets the value of the teamMemberDisplayQRQuickAccess property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberDisplayQRQuickAccess(String value)
   {
      this.teamMemberDisplayQRQuickAccess = value;
   }

   /**
    * Gets the value of the teamMemberDisplayTimeFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isTeamMemberDisplayTimeFlag()
   {
      return teamMemberDisplayTimeFlag;
   }

   /**
    * Sets the value of the teamMemberDisplayTimeFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberDisplayTimeFlag(Boolean value)
   {
      this.teamMemberDisplayTimeFlag = value;
   }

   /**
    * Gets the value of the teamMemberDisplayTimeFormat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberDisplayTimeFormat()
   {
      return teamMemberDisplayTimeFormat;
   }

   /**
    * Sets the value of the teamMemberDisplayTimeFormat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberDisplayTimeFormat(String value)
   {
      this.teamMemberDisplayTimeFormat = value;
   }

   /**
    * Gets the value of the teamMemberLocale property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberLocale()
   {
      return teamMemberLocale;
   }

   /**
    * Sets the value of the teamMemberLocale property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberLocale(String value)
   {
      this.teamMemberLocale = value;
   }

   /**
    * Gets the value of the teamMemberProjectFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberProjectFilter()
   {
      return teamMemberProjectFilter;
   }

   /**
    * Sets the value of the teamMemberProjectFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberProjectFilter(String value)
   {
      this.teamMemberProjectFilter = value;
   }

   /**
    * Gets the value of the teamMemberResourceFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberResourceFilter()
   {
      return teamMemberResourceFilter;
   }

   /**
    * Sets the value of the teamMemberResourceFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberResourceFilter(String value)
   {
      this.teamMemberResourceFilter = value;
   }

   /**
    * Gets the value of the teamMemberTaskStatusFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberTaskStatusFilter()
   {
      return teamMemberTaskStatusFilter;
   }

   /**
    * Sets the value of the teamMemberTaskStatusFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberTaskStatusFilter(String value)
   {
      this.teamMemberTaskStatusFilter = value;
   }

   /**
    * Gets the value of the teamMemberTimeframeFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberTimeframeFilter()
   {
      return teamMemberTimeframeFilter;
   }

   /**
    * Sets the value of the teamMemberTimeframeFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberTimeframeFilter(String value)
   {
      this.teamMemberTimeframeFilter = value;
   }

   /**
    * Gets the value of the teamMemberWBSFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberWBSFilter()
   {
      return teamMemberWBSFilter;
   }

   /**
    * Sets the value of the teamMemberWBSFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberWBSFilter(String value)
   {
      this.teamMemberWBSFilter = value;
   }

   /**
    * Gets the value of the teamMemberWorkUnitType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberWorkUnitType()
   {
      return teamMemberWorkUnitType;
   }

   /**
    * Sets the value of the teamMemberWorkUnitType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberWorkUnitType(String value)
   {
      this.teamMemberWorkUnitType = value;
   }

   /**
    * Gets the value of the timesheetProjectFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTimesheetProjectFilter()
   {
      return timesheetProjectFilter;
   }

   /**
    * Sets the value of the timesheetProjectFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimesheetProjectFilter(String value)
   {
      this.timesheetProjectFilter = value;
   }

   /**
    * Gets the value of the timesheetWBSFilter property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTimesheetWBSFilter()
   {
      return timesheetWBSFilter;
   }

   /**
    * Sets the value of the timesheetWBSFilter property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimesheetWBSFilter(String value)
   {
      this.timesheetWBSFilter = value;
   }

   /**
    * Gets the value of the unitsPerTimeShowAsPercentage property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isUnitsPerTimeShowAsPercentage()
   {
      return unitsPerTimeShowAsPercentage;
   }

   /**
    * Sets the value of the unitsPerTimeShowAsPercentage property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnitsPerTimeShowAsPercentage(Boolean value)
   {
      this.unitsPerTimeShowAsPercentage = value;
   }

   /**
    * Gets the value of the userInterfaceViewObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getUserInterfaceViewObjectId()
   {
      return userInterfaceViewObjectId;
   }

   /**
    * Sets the value of the userInterfaceViewObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setUserInterfaceViewObjectId(Integer value)
   {
      this.userInterfaceViewObjectId = value;
   }

   /**
    * Gets the value of the resourceRequests property.
    *
    * @return
    *     possible object is
    *     {@link UserType.ResourceRequests }
    *
    */
   public UserType.ResourceRequests getResourceRequests()
   {
      return resourceRequests;
   }

   /**
    * Sets the value of the resourceRequests property.
    *
    * @param value
    *     allowed object is
    *     {@link UserType.ResourceRequests }
    *
    */
   public void setResourceRequests(UserType.ResourceRequests value)
   {
      this.resourceRequests = value;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="ResourceRequest" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceRequestType" maxOccurs="unbounded" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "resourceRequest"
   }) public static class ResourceRequests
   {

      @XmlElement(name = "ResourceRequest") protected List<ResourceRequestType> resourceRequest;

      /**
       * Gets the value of the resourceRequest property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the resourceRequest property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getResourceRequest().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link ResourceRequestType }
       *
       *
       */
      public List<ResourceRequestType> getResourceRequest()
      {
         if (resourceRequest == null)
         {
            resourceRequest = new ArrayList<>();
         }
         return this.resourceRequest;
      }

   }

}
