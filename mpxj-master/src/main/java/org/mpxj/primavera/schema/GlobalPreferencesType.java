//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for GlobalPreferencesType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="GlobalPreferencesType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="AllowApprovedTSRejection" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AlwaysLaunchOnlineHelp" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="BaseCurrencyObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ContractManagementURL" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CustomLabel1" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CustomLabel2" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CustomLabel3" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DayAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DefaultDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DefaultTimesheetApprovalManager" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="EPPMConsentMessage" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="EPPMEnableConsent" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="EVEstimateToCompleteFactor" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EVEstimateToCompleteTechnique" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="ETC = Remaining Cost for Activity"/&gt;
 *               &lt;enumeration value="PF = 1"/&gt;
 *               &lt;enumeration value="PF = Custom Value"/&gt;
 *               &lt;enumeration value="PF = 1 / CPI"/&gt;
 *               &lt;enumeration value="PF = 1 / (CPI * SPI)"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EVPerformancePctCompleteCustomPct" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EVPerformancePctCompleteTechnique" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Activity Percent Complete"/&gt;
 *               &lt;enumeration value="0 / 100"/&gt;
 *               &lt;enumeration value="50 / 50"/&gt;
 *               &lt;enumeration value="Custom Percent Complete"/&gt;
 *               &lt;enumeration value="WBS Milestones Percent Complete"/&gt;
 *               &lt;enumeration value="Activity Percent Complete Using Resource Curves"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EarnedValueCalculation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="At Completion Values with Current Dates"/&gt;
 *               &lt;enumeration value="Planned Values with Planned Dates"/&gt;
 *               &lt;enumeration value="Planned Values with Current Dates"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EmailNotifyTSRejection" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EnablePasswordPolicy" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EnableTSAudit" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EnableWebServicesIPCheck" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EnableWhatsNewDialog" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ExceptionSiteList" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="FooterLabel1" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="FooterLabel2" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="FooterLabel3" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="GatewayApiUrl" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayExportERPSyncName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayExportUnifierSyncName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayImportERPSyncName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayImportUnifierSyncName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayP6DeploymentName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayPassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="GatewayUnifierEnabled" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="GatewayUsername" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="HeaderLabel1" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HeaderLabel2" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HeaderLabel3" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HourAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HoursPerDay" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0"/&gt;
 *               &lt;maxInclusive value="24.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HoursPerMonth" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0"/&gt;
 *               &lt;maxInclusive value="744.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HoursPerWeek" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0"/&gt;
 *               &lt;maxInclusive value="168.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HoursPerYear" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0"/&gt;
 *               &lt;maxInclusive value="8784.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="IPSiteList" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LogHoursAfterActualFinish" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LogHoursBeforeActualStart" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LogHoursCompletedActivities" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LogHoursInFuture" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LogHoursNotStartedActivities" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="MaxActivityCodeTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxActivityCodesPerProject" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="MaxActivityIdLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxAssignmentCodeTreeLevelCnt" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxBaselinesPerProject" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxCostAccountLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxCostAccountTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxFPCalendarCount" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="20"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxOBSTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxProjectCodeTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxProjectIdLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxResourceCodeTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxResourceIdLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxResourceTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxRoleCodeTreeLevelCnt" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxRoleIdLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxRoleTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="25"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxTimesheetResourceHours" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0"/&gt;
 *               &lt;maxInclusive value="24.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxWBSCodeLength" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxWBSTreeLevels" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="50"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaximumBaselinesCopiedWithProject" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="50"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MinuteAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MonthAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="NumberOfAccessibleFutureTimesheets" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="0"/&gt;
 *               &lt;maxInclusive value="200"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="NumberOfAccessiblePastTimesheets" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="0"/&gt;
 *               &lt;maxInclusive value="200"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrivateIPAllowList" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ReportEnableLazyLoad" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourcesCanAssignThemselvesToActivities" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="StartDayOfWeek" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="7"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SummarizeByCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="SummarizeByFinancialPeriods" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="SummaryResourceSpreadInterval" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SummaryWBSSpreadInterval" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TeamMemberConsentMessage" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberEnableConsent" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TimeWindowCompletedActivities" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="TimeWindowNotStartedActivities" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="TimesheetApprovalLevel" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="TimesheetDecimalDigits" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="TimesheetInterval" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="TimesheetPeriodEndsOnDay" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Sunday"/&gt;
 *               &lt;enumeration value="Monday"/&gt;
 *               &lt;enumeration value="Tuesday"/&gt;
 *               &lt;enumeration value="Wednesday"/&gt;
 *               &lt;enumeration value="Thursday"/&gt;
 *               &lt;enumeration value="Friday"/&gt;
 *               &lt;enumeration value="Saturday"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TimesheetPeriodType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Every Week"/&gt;
 *               &lt;enumeration value="Every Two Weeks"/&gt;
 *               &lt;enumeration value="Every Four Weeks"/&gt;
 *               &lt;enumeration value="Every Month"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="UnifierAuthCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UnifierCompanyShortName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UnifierIntegrationPassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UnifierIntegrationUserName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UnifierWebServiceURL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UseCalendarTimePeriodsFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="UseMaxTimesheetResourceHours" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="UseProjectManagerApproval" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="20"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="UseTimesheets" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="VersionForWhatsNew" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="WBSCategoryLabel" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WBSCodeSeparator" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="2"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WeekAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="YearAbbreviation" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "GlobalPreferencesType", propOrder =
{
   "allowApprovedTSRejection",
   "alwaysLaunchOnlineHelp",
   "baseCurrencyObjectId",
   "contractManagementURL",
   "createDate",
   "createUser",
   "customLabel1",
   "customLabel2",
   "customLabel3",
   "dayAbbreviation",
   "defaultDuration",
   "defaultTimesheetApprovalManager",
   "eppmConsentMessage",
   "eppmEnableConsent",
   "evEstimateToCompleteFactor",
   "evEstimateToCompleteTechnique",
   "evPerformancePctCompleteCustomPct",
   "evPerformancePctCompleteTechnique",
   "earnedValueCalculation",
   "emailNotifyTSRejection",
   "enablePasswordPolicy",
   "enableTSAudit",
   "enableWebServicesIPCheck",
   "enableWhatsNewDialog",
   "exceptionSiteList",
   "footerLabel1",
   "footerLabel2",
   "footerLabel3",
   "gatewayApiUrl",
   "gatewayExportERPSyncName",
   "gatewayExportUnifierSyncName",
   "gatewayImportERPSyncName",
   "gatewayImportUnifierSyncName",
   "gatewayP6DeploymentName",
   "gatewayPassword",
   "gatewayUnifierEnabled",
   "gatewayUsername",
   "headerLabel1",
   "headerLabel2",
   "headerLabel3",
   "hourAbbreviation",
   "hoursPerDay",
   "hoursPerMonth",
   "hoursPerWeek",
   "hoursPerYear",
   "ipSiteList",
   "lastUpdateDate",
   "lastUpdateUser",
   "logHoursAfterActualFinish",
   "logHoursBeforeActualStart",
   "logHoursCompletedActivities",
   "logHoursInFuture",
   "logHoursNotStartedActivities",
   "maxActivityCodeTreeLevels",
   "maxActivityCodesPerProject",
   "maxActivityIdLength",
   "maxAssignmentCodeTreeLevelCnt",
   "maxBaselinesPerProject",
   "maxCostAccountLength",
   "maxCostAccountTreeLevels",
   "maxFPCalendarCount",
   "maxOBSTreeLevels",
   "maxProjectCodeTreeLevels",
   "maxProjectIdLength",
   "maxResourceCodeTreeLevels",
   "maxResourceIdLength",
   "maxResourceTreeLevels",
   "maxRoleCodeTreeLevelCnt",
   "maxRoleIdLength",
   "maxRoleTreeLevels",
   "maxTimesheetResourceHours",
   "maxWBSCodeLength",
   "maxWBSTreeLevels",
   "maximumBaselinesCopiedWithProject",
   "minuteAbbreviation",
   "monthAbbreviation",
   "numberOfAccessibleFutureTimesheets",
   "numberOfAccessiblePastTimesheets",
   "privateIPAllowList",
   "reportEnableLazyLoad",
   "resourcesCanAssignThemselvesToActivities",
   "resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess",
   "startDayOfWeek",
   "summarizeByCalendar",
   "summarizeByFinancialPeriods",
   "summaryResourceSpreadInterval",
   "summaryWBSSpreadInterval",
   "teamMemberConsentMessage",
   "teamMemberEnableConsent",
   "timeWindowCompletedActivities",
   "timeWindowNotStartedActivities",
   "timesheetApprovalLevel",
   "timesheetDecimalDigits",
   "timesheetInterval",
   "timesheetPeriodEndsOnDay",
   "timesheetPeriodType",
   "unifierAuthCode",
   "unifierCompanyShortName",
   "unifierIntegrationPassword",
   "unifierIntegrationUserName",
   "unifierWebServiceURL",
   "useCalendarTimePeriodsFlag",
   "useMaxTimesheetResourceHours",
   "useProjectManagerApproval",
   "useTimesheets",
   "versionForWhatsNew",
   "wbsCategoryLabel",
   "wbsCodeSeparator",
   "weekAbbreviation",
   "yearAbbreviation"
}) public class GlobalPreferencesType
{

   @XmlElement(name = "AllowApprovedTSRejection", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean allowApprovedTSRejection;
   @XmlElement(name = "AlwaysLaunchOnlineHelp", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean alwaysLaunchOnlineHelp;
   @XmlElement(name = "BaseCurrencyObjectId") protected Integer baseCurrencyObjectId;
   @XmlElement(name = "ContractManagementURL") @XmlJavaTypeAdapter(Adapter1.class) protected String contractManagementURL;
   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "CustomLabel1") @XmlJavaTypeAdapter(Adapter1.class) protected String customLabel1;
   @XmlElement(name = "CustomLabel2") @XmlJavaTypeAdapter(Adapter1.class) protected String customLabel2;
   @XmlElement(name = "CustomLabel3") @XmlJavaTypeAdapter(Adapter1.class) protected String customLabel3;
   @XmlElement(name = "DayAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String dayAbbreviation;
   @XmlElement(name = "DefaultDuration", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double defaultDuration;
   @XmlElement(name = "DefaultTimesheetApprovalManager", nillable = true) protected Integer defaultTimesheetApprovalManager;
   @XmlElement(name = "EPPMConsentMessage") @XmlJavaTypeAdapter(Adapter1.class) protected String eppmConsentMessage;
   @XmlElement(name = "EPPMEnableConsent") @XmlJavaTypeAdapter(Adapter1.class) protected String eppmEnableConsent;
   @XmlElement(name = "EVEstimateToCompleteFactor", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double evEstimateToCompleteFactor;
   @XmlElement(name = "EVEstimateToCompleteTechnique") @XmlJavaTypeAdapter(Adapter1.class) protected String evEstimateToCompleteTechnique;
   @XmlElement(name = "EVPerformancePctCompleteCustomPct", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double evPerformancePctCompleteCustomPct;
   @XmlElement(name = "EVPerformancePctCompleteTechnique") @XmlJavaTypeAdapter(Adapter1.class) protected String evPerformancePctCompleteTechnique;
   @XmlElement(name = "EarnedValueCalculation") @XmlJavaTypeAdapter(Adapter1.class) protected String earnedValueCalculation;
   @XmlElement(name = "EmailNotifyTSRejection", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean emailNotifyTSRejection;
   @XmlElement(name = "EnablePasswordPolicy", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enablePasswordPolicy;
   @XmlElement(name = "EnableTSAudit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enableTSAudit;
   @XmlElement(name = "EnableWebServicesIPCheck", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enableWebServicesIPCheck;
   @XmlElement(name = "EnableWhatsNewDialog", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enableWhatsNewDialog;
   @XmlElement(name = "ExceptionSiteList") @XmlJavaTypeAdapter(Adapter1.class) protected String exceptionSiteList;
   @XmlElement(name = "FooterLabel1") @XmlJavaTypeAdapter(Adapter1.class) protected String footerLabel1;
   @XmlElement(name = "FooterLabel2") @XmlJavaTypeAdapter(Adapter1.class) protected String footerLabel2;
   @XmlElement(name = "FooterLabel3") @XmlJavaTypeAdapter(Adapter1.class) protected String footerLabel3;
   @XmlElement(name = "GatewayApiUrl") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayApiUrl;
   @XmlElement(name = "GatewayExportERPSyncName") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayExportERPSyncName;
   @XmlElement(name = "GatewayExportUnifierSyncName") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayExportUnifierSyncName;
   @XmlElement(name = "GatewayImportERPSyncName") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayImportERPSyncName;
   @XmlElement(name = "GatewayImportUnifierSyncName") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayImportUnifierSyncName;
   @XmlElement(name = "GatewayP6DeploymentName") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayP6DeploymentName;
   @XmlElement(name = "GatewayPassword") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayPassword;
   @XmlElement(name = "GatewayUnifierEnabled", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean gatewayUnifierEnabled;
   @XmlElement(name = "GatewayUsername") @XmlJavaTypeAdapter(Adapter1.class) protected String gatewayUsername;
   @XmlElement(name = "HeaderLabel1") @XmlJavaTypeAdapter(Adapter1.class) protected String headerLabel1;
   @XmlElement(name = "HeaderLabel2") @XmlJavaTypeAdapter(Adapter1.class) protected String headerLabel2;
   @XmlElement(name = "HeaderLabel3") @XmlJavaTypeAdapter(Adapter1.class) protected String headerLabel3;
   @XmlElement(name = "HourAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String hourAbbreviation;
   @XmlElement(name = "HoursPerDay", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double hoursPerDay;
   @XmlElement(name = "HoursPerMonth", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double hoursPerMonth;
   @XmlElement(name = "HoursPerWeek", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double hoursPerWeek;
   @XmlElement(name = "HoursPerYear", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double hoursPerYear;
   @XmlElement(name = "IPSiteList") @XmlJavaTypeAdapter(Adapter1.class) protected String ipSiteList;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "LogHoursAfterActualFinish", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean logHoursAfterActualFinish;
   @XmlElement(name = "LogHoursBeforeActualStart", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean logHoursBeforeActualStart;
   @XmlElement(name = "LogHoursCompletedActivities", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean logHoursCompletedActivities;
   @XmlElement(name = "LogHoursInFuture", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean logHoursInFuture;
   @XmlElement(name = "LogHoursNotStartedActivities", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean logHoursNotStartedActivities;
   @XmlElement(name = "MaxActivityCodeTreeLevels") protected Integer maxActivityCodeTreeLevels;
   @XmlElement(name = "MaxActivityCodesPerProject", nillable = true) protected Integer maxActivityCodesPerProject;
   @XmlElement(name = "MaxActivityIdLength") protected Integer maxActivityIdLength;
   @XmlElement(name = "MaxAssignmentCodeTreeLevelCnt") protected Integer maxAssignmentCodeTreeLevelCnt;
   @XmlElement(name = "MaxBaselinesPerProject") protected Integer maxBaselinesPerProject;
   @XmlElement(name = "MaxCostAccountLength") protected Integer maxCostAccountLength;
   @XmlElement(name = "MaxCostAccountTreeLevels") protected Integer maxCostAccountTreeLevels;
   @XmlElement(name = "MaxFPCalendarCount", nillable = true) protected Integer maxFPCalendarCount;
   @XmlElement(name = "MaxOBSTreeLevels") protected Integer maxOBSTreeLevels;
   @XmlElement(name = "MaxProjectCodeTreeLevels") protected Integer maxProjectCodeTreeLevels;
   @XmlElement(name = "MaxProjectIdLength") protected Integer maxProjectIdLength;
   @XmlElement(name = "MaxResourceCodeTreeLevels") protected Integer maxResourceCodeTreeLevels;
   @XmlElement(name = "MaxResourceIdLength") protected Integer maxResourceIdLength;
   @XmlElement(name = "MaxResourceTreeLevels") protected Integer maxResourceTreeLevels;
   @XmlElement(name = "MaxRoleCodeTreeLevelCnt") protected Integer maxRoleCodeTreeLevelCnt;
   @XmlElement(name = "MaxRoleIdLength") protected Integer maxRoleIdLength;
   @XmlElement(name = "MaxRoleTreeLevels") protected Integer maxRoleTreeLevels;
   @XmlElement(name = "MaxTimesheetResourceHours", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double maxTimesheetResourceHours;
   @XmlElement(name = "MaxWBSCodeLength") protected Integer maxWBSCodeLength;
   @XmlElement(name = "MaxWBSTreeLevels") protected Integer maxWBSTreeLevels;
   @XmlElement(name = "MaximumBaselinesCopiedWithProject") protected Integer maximumBaselinesCopiedWithProject;
   @XmlElement(name = "MinuteAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String minuteAbbreviation;
   @XmlElement(name = "MonthAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String monthAbbreviation;
   @XmlElement(name = "NumberOfAccessibleFutureTimesheets") protected Integer numberOfAccessibleFutureTimesheets;
   @XmlElement(name = "NumberOfAccessiblePastTimesheets") protected Integer numberOfAccessiblePastTimesheets;
   @XmlElement(name = "PrivateIPAllowList") @XmlJavaTypeAdapter(Adapter1.class) protected String privateIPAllowList;
   @XmlElement(name = "ReportEnableLazyLoad", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean reportEnableLazyLoad;
   @XmlElement(name = "ResourcesCanAssignThemselvesToActivities", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanAssignThemselvesToActivities;
   @XmlElement(name = "ResourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess;
   @XmlElement(name = "StartDayOfWeek") protected Integer startDayOfWeek;
   @XmlElement(name = "SummarizeByCalendar", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean summarizeByCalendar;
   @XmlElement(name = "SummarizeByFinancialPeriods", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean summarizeByFinancialPeriods;
   @XmlElement(name = "SummaryResourceSpreadInterval") @XmlJavaTypeAdapter(Adapter1.class) protected String summaryResourceSpreadInterval;
   @XmlElement(name = "SummaryWBSSpreadInterval") @XmlJavaTypeAdapter(Adapter1.class) protected String summaryWBSSpreadInterval;
   @XmlElement(name = "TeamMemberConsentMessage") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberConsentMessage;
   @XmlElement(name = "TeamMemberEnableConsent") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberEnableConsent;
   @XmlElement(name = "TimeWindowCompletedActivities") protected Integer timeWindowCompletedActivities;
   @XmlElement(name = "TimeWindowNotStartedActivities") protected Integer timeWindowNotStartedActivities;
   @XmlElement(name = "TimesheetApprovalLevel") protected Integer timesheetApprovalLevel;
   @XmlElement(name = "TimesheetDecimalDigits") protected Integer timesheetDecimalDigits;
   @XmlElement(name = "TimesheetInterval", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean timesheetInterval;
   @XmlElement(name = "TimesheetPeriodEndsOnDay") @XmlJavaTypeAdapter(Adapter1.class) protected String timesheetPeriodEndsOnDay;
   @XmlElement(name = "TimesheetPeriodType") @XmlJavaTypeAdapter(Adapter1.class) protected String timesheetPeriodType;
   @XmlElement(name = "UnifierAuthCode") @XmlJavaTypeAdapter(Adapter1.class) protected String unifierAuthCode;
   @XmlElement(name = "UnifierCompanyShortName") @XmlJavaTypeAdapter(Adapter1.class) protected String unifierCompanyShortName;
   @XmlElement(name = "UnifierIntegrationPassword") @XmlJavaTypeAdapter(Adapter1.class) protected String unifierIntegrationPassword;
   @XmlElement(name = "UnifierIntegrationUserName") @XmlJavaTypeAdapter(Adapter1.class) protected String unifierIntegrationUserName;
   @XmlElement(name = "UnifierWebServiceURL") @XmlJavaTypeAdapter(Adapter1.class) protected String unifierWebServiceURL;
   @XmlElement(name = "UseCalendarTimePeriodsFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean useCalendarTimePeriodsFlag;
   @XmlElement(name = "UseMaxTimesheetResourceHours", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean useMaxTimesheetResourceHours;
   @XmlElement(name = "UseProjectManagerApproval") @XmlJavaTypeAdapter(Adapter1.class) protected String useProjectManagerApproval;
   @XmlElement(name = "UseTimesheets", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean useTimesheets;
   @XmlElement(name = "VersionForWhatsNew") @XmlJavaTypeAdapter(Adapter1.class) protected String versionForWhatsNew;
   @XmlElement(name = "WBSCategoryLabel") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsCategoryLabel;
   @XmlElement(name = "WBSCodeSeparator") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsCodeSeparator;
   @XmlElement(name = "WeekAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String weekAbbreviation;
   @XmlElement(name = "YearAbbreviation") @XmlJavaTypeAdapter(Adapter1.class) protected String yearAbbreviation;

   /**
    * Gets the value of the allowApprovedTSRejection property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAllowApprovedTSRejection()
   {
      return allowApprovedTSRejection;
   }

   /**
    * Sets the value of the allowApprovedTSRejection property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAllowApprovedTSRejection(Boolean value)
   {
      this.allowApprovedTSRejection = value;
   }

   /**
    * Gets the value of the alwaysLaunchOnlineHelp property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAlwaysLaunchOnlineHelp()
   {
      return alwaysLaunchOnlineHelp;
   }

   /**
    * Sets the value of the alwaysLaunchOnlineHelp property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAlwaysLaunchOnlineHelp(Boolean value)
   {
      this.alwaysLaunchOnlineHelp = value;
   }

   /**
    * Gets the value of the baseCurrencyObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getBaseCurrencyObjectId()
   {
      return baseCurrencyObjectId;
   }

   /**
    * Sets the value of the baseCurrencyObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setBaseCurrencyObjectId(Integer value)
   {
      this.baseCurrencyObjectId = value;
   }

   /**
    * Gets the value of the contractManagementURL property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getContractManagementURL()
   {
      return contractManagementURL;
   }

   /**
    * Sets the value of the contractManagementURL property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setContractManagementURL(String value)
   {
      this.contractManagementURL = value;
   }

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the customLabel1 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCustomLabel1()
   {
      return customLabel1;
   }

   /**
    * Sets the value of the customLabel1 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCustomLabel1(String value)
   {
      this.customLabel1 = value;
   }

   /**
    * Gets the value of the customLabel2 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCustomLabel2()
   {
      return customLabel2;
   }

   /**
    * Sets the value of the customLabel2 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCustomLabel2(String value)
   {
      this.customLabel2 = value;
   }

   /**
    * Gets the value of the customLabel3 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCustomLabel3()
   {
      return customLabel3;
   }

   /**
    * Sets the value of the customLabel3 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCustomLabel3(String value)
   {
      this.customLabel3 = value;
   }

   /**
    * Gets the value of the dayAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDayAbbreviation()
   {
      return dayAbbreviation;
   }

   /**
    * Sets the value of the dayAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDayAbbreviation(String value)
   {
      this.dayAbbreviation = value;
   }

   /**
    * Gets the value of the defaultDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDefaultDuration()
   {
      return defaultDuration;
   }

   /**
    * Sets the value of the defaultDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDefaultDuration(Double value)
   {
      this.defaultDuration = value;
   }

   /**
    * Gets the value of the defaultTimesheetApprovalManager property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getDefaultTimesheetApprovalManager()
   {
      return defaultTimesheetApprovalManager;
   }

   /**
    * Sets the value of the defaultTimesheetApprovalManager property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setDefaultTimesheetApprovalManager(Integer value)
   {
      this.defaultTimesheetApprovalManager = value;
   }

   /**
    * Gets the value of the eppmConsentMessage property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEPPMConsentMessage()
   {
      return eppmConsentMessage;
   }

   /**
    * Sets the value of the eppmConsentMessage property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEPPMConsentMessage(String value)
   {
      this.eppmConsentMessage = value;
   }

   /**
    * Gets the value of the eppmEnableConsent property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEPPMEnableConsent()
   {
      return eppmEnableConsent;
   }

   /**
    * Sets the value of the eppmEnableConsent property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEPPMEnableConsent(String value)
   {
      this.eppmEnableConsent = value;
   }

   /**
    * Gets the value of the evEstimateToCompleteFactor property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEVEstimateToCompleteFactor()
   {
      return evEstimateToCompleteFactor;
   }

   /**
    * Sets the value of the evEstimateToCompleteFactor property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEVEstimateToCompleteFactor(Double value)
   {
      this.evEstimateToCompleteFactor = value;
   }

   /**
    * Gets the value of the evEstimateToCompleteTechnique property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEVEstimateToCompleteTechnique()
   {
      return evEstimateToCompleteTechnique;
   }

   /**
    * Sets the value of the evEstimateToCompleteTechnique property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEVEstimateToCompleteTechnique(String value)
   {
      this.evEstimateToCompleteTechnique = value;
   }

   /**
    * Gets the value of the evPerformancePctCompleteCustomPct property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEVPerformancePctCompleteCustomPct()
   {
      return evPerformancePctCompleteCustomPct;
   }

   /**
    * Sets the value of the evPerformancePctCompleteCustomPct property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEVPerformancePctCompleteCustomPct(Double value)
   {
      this.evPerformancePctCompleteCustomPct = value;
   }

   /**
    * Gets the value of the evPerformancePctCompleteTechnique property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEVPerformancePctCompleteTechnique()
   {
      return evPerformancePctCompleteTechnique;
   }

   /**
    * Sets the value of the evPerformancePctCompleteTechnique property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEVPerformancePctCompleteTechnique(String value)
   {
      this.evPerformancePctCompleteTechnique = value;
   }

   /**
    * Gets the value of the earnedValueCalculation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getEarnedValueCalculation()
   {
      return earnedValueCalculation;
   }

   /**
    * Sets the value of the earnedValueCalculation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEarnedValueCalculation(String value)
   {
      this.earnedValueCalculation = value;
   }

   /**
    * Gets the value of the emailNotifyTSRejection property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEmailNotifyTSRejection()
   {
      return emailNotifyTSRejection;
   }

   /**
    * Sets the value of the emailNotifyTSRejection property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEmailNotifyTSRejection(Boolean value)
   {
      this.emailNotifyTSRejection = value;
   }

   /**
    * Gets the value of the enablePasswordPolicy property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnablePasswordPolicy()
   {
      return enablePasswordPolicy;
   }

   /**
    * Sets the value of the enablePasswordPolicy property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnablePasswordPolicy(Boolean value)
   {
      this.enablePasswordPolicy = value;
   }

   /**
    * Gets the value of the enableTSAudit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnableTSAudit()
   {
      return enableTSAudit;
   }

   /**
    * Sets the value of the enableTSAudit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnableTSAudit(Boolean value)
   {
      this.enableTSAudit = value;
   }

   /**
    * Gets the value of the enableWebServicesIPCheck property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnableWebServicesIPCheck()
   {
      return enableWebServicesIPCheck;
   }

   /**
    * Sets the value of the enableWebServicesIPCheck property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnableWebServicesIPCheck(Boolean value)
   {
      this.enableWebServicesIPCheck = value;
   }

   /**
    * Gets the value of the enableWhatsNewDialog property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnableWhatsNewDialog()
   {
      return enableWhatsNewDialog;
   }

   /**
    * Sets the value of the enableWhatsNewDialog property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnableWhatsNewDialog(Boolean value)
   {
      this.enableWhatsNewDialog = value;
   }

   /**
    * Gets the value of the exceptionSiteList property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getExceptionSiteList()
   {
      return exceptionSiteList;
   }

   /**
    * Sets the value of the exceptionSiteList property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExceptionSiteList(String value)
   {
      this.exceptionSiteList = value;
   }

   /**
    * Gets the value of the footerLabel1 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getFooterLabel1()
   {
      return footerLabel1;
   }

   /**
    * Sets the value of the footerLabel1 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFooterLabel1(String value)
   {
      this.footerLabel1 = value;
   }

   /**
    * Gets the value of the footerLabel2 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getFooterLabel2()
   {
      return footerLabel2;
   }

   /**
    * Sets the value of the footerLabel2 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFooterLabel2(String value)
   {
      this.footerLabel2 = value;
   }

   /**
    * Gets the value of the footerLabel3 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getFooterLabel3()
   {
      return footerLabel3;
   }

   /**
    * Sets the value of the footerLabel3 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFooterLabel3(String value)
   {
      this.footerLabel3 = value;
   }

   /**
    * Gets the value of the gatewayApiUrl property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayApiUrl()
   {
      return gatewayApiUrl;
   }

   /**
    * Sets the value of the gatewayApiUrl property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayApiUrl(String value)
   {
      this.gatewayApiUrl = value;
   }

   /**
    * Gets the value of the gatewayExportERPSyncName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayExportERPSyncName()
   {
      return gatewayExportERPSyncName;
   }

   /**
    * Sets the value of the gatewayExportERPSyncName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayExportERPSyncName(String value)
   {
      this.gatewayExportERPSyncName = value;
   }

   /**
    * Gets the value of the gatewayExportUnifierSyncName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayExportUnifierSyncName()
   {
      return gatewayExportUnifierSyncName;
   }

   /**
    * Sets the value of the gatewayExportUnifierSyncName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayExportUnifierSyncName(String value)
   {
      this.gatewayExportUnifierSyncName = value;
   }

   /**
    * Gets the value of the gatewayImportERPSyncName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayImportERPSyncName()
   {
      return gatewayImportERPSyncName;
   }

   /**
    * Sets the value of the gatewayImportERPSyncName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayImportERPSyncName(String value)
   {
      this.gatewayImportERPSyncName = value;
   }

   /**
    * Gets the value of the gatewayImportUnifierSyncName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayImportUnifierSyncName()
   {
      return gatewayImportUnifierSyncName;
   }

   /**
    * Sets the value of the gatewayImportUnifierSyncName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayImportUnifierSyncName(String value)
   {
      this.gatewayImportUnifierSyncName = value;
   }

   /**
    * Gets the value of the gatewayP6DeploymentName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayP6DeploymentName()
   {
      return gatewayP6DeploymentName;
   }

   /**
    * Sets the value of the gatewayP6DeploymentName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayP6DeploymentName(String value)
   {
      this.gatewayP6DeploymentName = value;
   }

   /**
    * Gets the value of the gatewayPassword property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayPassword()
   {
      return gatewayPassword;
   }

   /**
    * Sets the value of the gatewayPassword property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayPassword(String value)
   {
      this.gatewayPassword = value;
   }

   /**
    * Gets the value of the gatewayUnifierEnabled property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isGatewayUnifierEnabled()
   {
      return gatewayUnifierEnabled;
   }

   /**
    * Sets the value of the gatewayUnifierEnabled property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayUnifierEnabled(Boolean value)
   {
      this.gatewayUnifierEnabled = value;
   }

   /**
    * Gets the value of the gatewayUsername property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGatewayUsername()
   {
      return gatewayUsername;
   }

   /**
    * Sets the value of the gatewayUsername property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGatewayUsername(String value)
   {
      this.gatewayUsername = value;
   }

   /**
    * Gets the value of the headerLabel1 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHeaderLabel1()
   {
      return headerLabel1;
   }

   /**
    * Sets the value of the headerLabel1 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHeaderLabel1(String value)
   {
      this.headerLabel1 = value;
   }

   /**
    * Gets the value of the headerLabel2 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHeaderLabel2()
   {
      return headerLabel2;
   }

   /**
    * Sets the value of the headerLabel2 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHeaderLabel2(String value)
   {
      this.headerLabel2 = value;
   }

   /**
    * Gets the value of the headerLabel3 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHeaderLabel3()
   {
      return headerLabel3;
   }

   /**
    * Sets the value of the headerLabel3 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHeaderLabel3(String value)
   {
      this.headerLabel3 = value;
   }

   /**
    * Gets the value of the hourAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHourAbbreviation()
   {
      return hourAbbreviation;
   }

   /**
    * Sets the value of the hourAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHourAbbreviation(String value)
   {
      this.hourAbbreviation = value;
   }

   /**
    * Gets the value of the hoursPerDay property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getHoursPerDay()
   {
      return hoursPerDay;
   }

   /**
    * Sets the value of the hoursPerDay property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHoursPerDay(Double value)
   {
      this.hoursPerDay = value;
   }

   /**
    * Gets the value of the hoursPerMonth property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getHoursPerMonth()
   {
      return hoursPerMonth;
   }

   /**
    * Sets the value of the hoursPerMonth property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHoursPerMonth(Double value)
   {
      this.hoursPerMonth = value;
   }

   /**
    * Gets the value of the hoursPerWeek property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getHoursPerWeek()
   {
      return hoursPerWeek;
   }

   /**
    * Sets the value of the hoursPerWeek property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHoursPerWeek(Double value)
   {
      this.hoursPerWeek = value;
   }

   /**
    * Gets the value of the hoursPerYear property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getHoursPerYear()
   {
      return hoursPerYear;
   }

   /**
    * Sets the value of the hoursPerYear property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHoursPerYear(Double value)
   {
      this.hoursPerYear = value;
   }

   /**
    * Gets the value of the ipSiteList property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getIPSiteList()
   {
      return ipSiteList;
   }

   /**
    * Sets the value of the ipSiteList property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIPSiteList(String value)
   {
      this.ipSiteList = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the logHoursAfterActualFinish property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLogHoursAfterActualFinish()
   {
      return logHoursAfterActualFinish;
   }

   /**
    * Sets the value of the logHoursAfterActualFinish property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLogHoursAfterActualFinish(Boolean value)
   {
      this.logHoursAfterActualFinish = value;
   }

   /**
    * Gets the value of the logHoursBeforeActualStart property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLogHoursBeforeActualStart()
   {
      return logHoursBeforeActualStart;
   }

   /**
    * Sets the value of the logHoursBeforeActualStart property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLogHoursBeforeActualStart(Boolean value)
   {
      this.logHoursBeforeActualStart = value;
   }

   /**
    * Gets the value of the logHoursCompletedActivities property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLogHoursCompletedActivities()
   {
      return logHoursCompletedActivities;
   }

   /**
    * Sets the value of the logHoursCompletedActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLogHoursCompletedActivities(Boolean value)
   {
      this.logHoursCompletedActivities = value;
   }

   /**
    * Gets the value of the logHoursInFuture property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLogHoursInFuture()
   {
      return logHoursInFuture;
   }

   /**
    * Sets the value of the logHoursInFuture property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLogHoursInFuture(Boolean value)
   {
      this.logHoursInFuture = value;
   }

   /**
    * Gets the value of the logHoursNotStartedActivities property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLogHoursNotStartedActivities()
   {
      return logHoursNotStartedActivities;
   }

   /**
    * Sets the value of the logHoursNotStartedActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLogHoursNotStartedActivities(Boolean value)
   {
      this.logHoursNotStartedActivities = value;
   }

   /**
    * Gets the value of the maxActivityCodeTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxActivityCodeTreeLevels()
   {
      return maxActivityCodeTreeLevels;
   }

   /**
    * Sets the value of the maxActivityCodeTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxActivityCodeTreeLevels(Integer value)
   {
      this.maxActivityCodeTreeLevels = value;
   }

   /**
    * Gets the value of the maxActivityCodesPerProject property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxActivityCodesPerProject()
   {
      return maxActivityCodesPerProject;
   }

   /**
    * Sets the value of the maxActivityCodesPerProject property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxActivityCodesPerProject(Integer value)
   {
      this.maxActivityCodesPerProject = value;
   }

   /**
    * Gets the value of the maxActivityIdLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxActivityIdLength()
   {
      return maxActivityIdLength;
   }

   /**
    * Sets the value of the maxActivityIdLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxActivityIdLength(Integer value)
   {
      this.maxActivityIdLength = value;
   }

   /**
    * Gets the value of the maxAssignmentCodeTreeLevelCnt property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxAssignmentCodeTreeLevelCnt()
   {
      return maxAssignmentCodeTreeLevelCnt;
   }

   /**
    * Sets the value of the maxAssignmentCodeTreeLevelCnt property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxAssignmentCodeTreeLevelCnt(Integer value)
   {
      this.maxAssignmentCodeTreeLevelCnt = value;
   }

   /**
    * Gets the value of the maxBaselinesPerProject property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxBaselinesPerProject()
   {
      return maxBaselinesPerProject;
   }

   /**
    * Sets the value of the maxBaselinesPerProject property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxBaselinesPerProject(Integer value)
   {
      this.maxBaselinesPerProject = value;
   }

   /**
    * Gets the value of the maxCostAccountLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxCostAccountLength()
   {
      return maxCostAccountLength;
   }

   /**
    * Sets the value of the maxCostAccountLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxCostAccountLength(Integer value)
   {
      this.maxCostAccountLength = value;
   }

   /**
    * Gets the value of the maxCostAccountTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxCostAccountTreeLevels()
   {
      return maxCostAccountTreeLevels;
   }

   /**
    * Sets the value of the maxCostAccountTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxCostAccountTreeLevels(Integer value)
   {
      this.maxCostAccountTreeLevels = value;
   }

   /**
    * Gets the value of the maxFPCalendarCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxFPCalendarCount()
   {
      return maxFPCalendarCount;
   }

   /**
    * Sets the value of the maxFPCalendarCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxFPCalendarCount(Integer value)
   {
      this.maxFPCalendarCount = value;
   }

   /**
    * Gets the value of the maxOBSTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxOBSTreeLevels()
   {
      return maxOBSTreeLevels;
   }

   /**
    * Sets the value of the maxOBSTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxOBSTreeLevels(Integer value)
   {
      this.maxOBSTreeLevels = value;
   }

   /**
    * Gets the value of the maxProjectCodeTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxProjectCodeTreeLevels()
   {
      return maxProjectCodeTreeLevels;
   }

   /**
    * Sets the value of the maxProjectCodeTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxProjectCodeTreeLevels(Integer value)
   {
      this.maxProjectCodeTreeLevels = value;
   }

   /**
    * Gets the value of the maxProjectIdLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxProjectIdLength()
   {
      return maxProjectIdLength;
   }

   /**
    * Sets the value of the maxProjectIdLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxProjectIdLength(Integer value)
   {
      this.maxProjectIdLength = value;
   }

   /**
    * Gets the value of the maxResourceCodeTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxResourceCodeTreeLevels()
   {
      return maxResourceCodeTreeLevels;
   }

   /**
    * Sets the value of the maxResourceCodeTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxResourceCodeTreeLevels(Integer value)
   {
      this.maxResourceCodeTreeLevels = value;
   }

   /**
    * Gets the value of the maxResourceIdLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxResourceIdLength()
   {
      return maxResourceIdLength;
   }

   /**
    * Sets the value of the maxResourceIdLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxResourceIdLength(Integer value)
   {
      this.maxResourceIdLength = value;
   }

   /**
    * Gets the value of the maxResourceTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxResourceTreeLevels()
   {
      return maxResourceTreeLevels;
   }

   /**
    * Sets the value of the maxResourceTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxResourceTreeLevels(Integer value)
   {
      this.maxResourceTreeLevels = value;
   }

   /**
    * Gets the value of the maxRoleCodeTreeLevelCnt property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxRoleCodeTreeLevelCnt()
   {
      return maxRoleCodeTreeLevelCnt;
   }

   /**
    * Sets the value of the maxRoleCodeTreeLevelCnt property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxRoleCodeTreeLevelCnt(Integer value)
   {
      this.maxRoleCodeTreeLevelCnt = value;
   }

   /**
    * Gets the value of the maxRoleIdLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxRoleIdLength()
   {
      return maxRoleIdLength;
   }

   /**
    * Sets the value of the maxRoleIdLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxRoleIdLength(Integer value)
   {
      this.maxRoleIdLength = value;
   }

   /**
    * Gets the value of the maxRoleTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxRoleTreeLevels()
   {
      return maxRoleTreeLevels;
   }

   /**
    * Sets the value of the maxRoleTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxRoleTreeLevels(Integer value)
   {
      this.maxRoleTreeLevels = value;
   }

   /**
    * Gets the value of the maxTimesheetResourceHours property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaxTimesheetResourceHours()
   {
      return maxTimesheetResourceHours;
   }

   /**
    * Sets the value of the maxTimesheetResourceHours property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaxTimesheetResourceHours(Double value)
   {
      this.maxTimesheetResourceHours = value;
   }

   /**
    * Gets the value of the maxWBSCodeLength property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxWBSCodeLength()
   {
      return maxWBSCodeLength;
   }

   /**
    * Sets the value of the maxWBSCodeLength property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxWBSCodeLength(Integer value)
   {
      this.maxWBSCodeLength = value;
   }

   /**
    * Gets the value of the maxWBSTreeLevels property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaxWBSTreeLevels()
   {
      return maxWBSTreeLevels;
   }

   /**
    * Sets the value of the maxWBSTreeLevels property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaxWBSTreeLevels(Integer value)
   {
      this.maxWBSTreeLevels = value;
   }

   /**
    * Gets the value of the maximumBaselinesCopiedWithProject property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getMaximumBaselinesCopiedWithProject()
   {
      return maximumBaselinesCopiedWithProject;
   }

   /**
    * Sets the value of the maximumBaselinesCopiedWithProject property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setMaximumBaselinesCopiedWithProject(Integer value)
   {
      this.maximumBaselinesCopiedWithProject = value;
   }

   /**
    * Gets the value of the minuteAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getMinuteAbbreviation()
   {
      return minuteAbbreviation;
   }

   /**
    * Sets the value of the minuteAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMinuteAbbreviation(String value)
   {
      this.minuteAbbreviation = value;
   }

   /**
    * Gets the value of the monthAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getMonthAbbreviation()
   {
      return monthAbbreviation;
   }

   /**
    * Sets the value of the monthAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMonthAbbreviation(String value)
   {
      this.monthAbbreviation = value;
   }

   /**
    * Gets the value of the numberOfAccessibleFutureTimesheets property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getNumberOfAccessibleFutureTimesheets()
   {
      return numberOfAccessibleFutureTimesheets;
   }

   /**
    * Sets the value of the numberOfAccessibleFutureTimesheets property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setNumberOfAccessibleFutureTimesheets(Integer value)
   {
      this.numberOfAccessibleFutureTimesheets = value;
   }

   /**
    * Gets the value of the numberOfAccessiblePastTimesheets property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getNumberOfAccessiblePastTimesheets()
   {
      return numberOfAccessiblePastTimesheets;
   }

   /**
    * Sets the value of the numberOfAccessiblePastTimesheets property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setNumberOfAccessiblePastTimesheets(Integer value)
   {
      this.numberOfAccessiblePastTimesheets = value;
   }

   /**
    * Gets the value of the privateIPAllowList property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPrivateIPAllowList()
   {
      return privateIPAllowList;
   }

   /**
    * Sets the value of the privateIPAllowList property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrivateIPAllowList(String value)
   {
      this.privateIPAllowList = value;
   }

   /**
    * Gets the value of the reportEnableLazyLoad property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isReportEnableLazyLoad()
   {
      return reportEnableLazyLoad;
   }

   /**
    * Sets the value of the reportEnableLazyLoad property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReportEnableLazyLoad(Boolean value)
   {
      this.reportEnableLazyLoad = value;
   }

   /**
    * Gets the value of the resourcesCanAssignThemselvesToActivities property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanAssignThemselvesToActivities()
   {
      return resourcesCanAssignThemselvesToActivities;
   }

   /**
    * Sets the value of the resourcesCanAssignThemselvesToActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanAssignThemselvesToActivities(Boolean value)
   {
      this.resourcesCanAssignThemselvesToActivities = value;
   }

   /**
    * Gets the value of the resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess()
   {
      return resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess;
   }

   /**
    * Sets the value of the resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess(Boolean value)
   {
      this.resourcesCanAssignThemselvesToActivitiesOutsideTheirOBSAccess = value;
   }

   /**
    * Gets the value of the startDayOfWeek property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getStartDayOfWeek()
   {
      return startDayOfWeek;
   }

   /**
    * Sets the value of the startDayOfWeek property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setStartDayOfWeek(Integer value)
   {
      this.startDayOfWeek = value;
   }

   /**
    * Gets the value of the summarizeByCalendar property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isSummarizeByCalendar()
   {
      return summarizeByCalendar;
   }

   /**
    * Sets the value of the summarizeByCalendar property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarizeByCalendar(Boolean value)
   {
      this.summarizeByCalendar = value;
   }

   /**
    * Gets the value of the summarizeByFinancialPeriods property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isSummarizeByFinancialPeriods()
   {
      return summarizeByFinancialPeriods;
   }

   /**
    * Sets the value of the summarizeByFinancialPeriods property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarizeByFinancialPeriods(Boolean value)
   {
      this.summarizeByFinancialPeriods = value;
   }

   /**
    * Gets the value of the summaryResourceSpreadInterval property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSummaryResourceSpreadInterval()
   {
      return summaryResourceSpreadInterval;
   }

   /**
    * Sets the value of the summaryResourceSpreadInterval property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryResourceSpreadInterval(String value)
   {
      this.summaryResourceSpreadInterval = value;
   }

   /**
    * Gets the value of the summaryWBSSpreadInterval property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSummaryWBSSpreadInterval()
   {
      return summaryWBSSpreadInterval;
   }

   /**
    * Sets the value of the summaryWBSSpreadInterval property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryWBSSpreadInterval(String value)
   {
      this.summaryWBSSpreadInterval = value;
   }

   /**
    * Gets the value of the teamMemberConsentMessage property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberConsentMessage()
   {
      return teamMemberConsentMessage;
   }

   /**
    * Sets the value of the teamMemberConsentMessage property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberConsentMessage(String value)
   {
      this.teamMemberConsentMessage = value;
   }

   /**
    * Gets the value of the teamMemberEnableConsent property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberEnableConsent()
   {
      return teamMemberEnableConsent;
   }

   /**
    * Sets the value of the teamMemberEnableConsent property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberEnableConsent(String value)
   {
      this.teamMemberEnableConsent = value;
   }

   /**
    * Gets the value of the timeWindowCompletedActivities property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getTimeWindowCompletedActivities()
   {
      return timeWindowCompletedActivities;
   }

   /**
    * Sets the value of the timeWindowCompletedActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setTimeWindowCompletedActivities(Integer value)
   {
      this.timeWindowCompletedActivities = value;
   }

   /**
    * Gets the value of the timeWindowNotStartedActivities property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getTimeWindowNotStartedActivities()
   {
      return timeWindowNotStartedActivities;
   }

   /**
    * Sets the value of the timeWindowNotStartedActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setTimeWindowNotStartedActivities(Integer value)
   {
      this.timeWindowNotStartedActivities = value;
   }

   /**
    * Gets the value of the timesheetApprovalLevel property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getTimesheetApprovalLevel()
   {
      return timesheetApprovalLevel;
   }

   /**
    * Sets the value of the timesheetApprovalLevel property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setTimesheetApprovalLevel(Integer value)
   {
      this.timesheetApprovalLevel = value;
   }

   /**
    * Gets the value of the timesheetDecimalDigits property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getTimesheetDecimalDigits()
   {
      return timesheetDecimalDigits;
   }

   /**
    * Sets the value of the timesheetDecimalDigits property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setTimesheetDecimalDigits(Integer value)
   {
      this.timesheetDecimalDigits = value;
   }

   /**
    * Gets the value of the timesheetInterval property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isTimesheetInterval()
   {
      return timesheetInterval;
   }

   /**
    * Sets the value of the timesheetInterval property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimesheetInterval(Boolean value)
   {
      this.timesheetInterval = value;
   }

   /**
    * Gets the value of the timesheetPeriodEndsOnDay property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTimesheetPeriodEndsOnDay()
   {
      return timesheetPeriodEndsOnDay;
   }

   /**
    * Sets the value of the timesheetPeriodEndsOnDay property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimesheetPeriodEndsOnDay(String value)
   {
      this.timesheetPeriodEndsOnDay = value;
   }

   /**
    * Gets the value of the timesheetPeriodType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTimesheetPeriodType()
   {
      return timesheetPeriodType;
   }

   /**
    * Sets the value of the timesheetPeriodType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimesheetPeriodType(String value)
   {
      this.timesheetPeriodType = value;
   }

   /**
    * Gets the value of the unifierAuthCode property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUnifierAuthCode()
   {
      return unifierAuthCode;
   }

   /**
    * Sets the value of the unifierAuthCode property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnifierAuthCode(String value)
   {
      this.unifierAuthCode = value;
   }

   /**
    * Gets the value of the unifierCompanyShortName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUnifierCompanyShortName()
   {
      return unifierCompanyShortName;
   }

   /**
    * Sets the value of the unifierCompanyShortName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnifierCompanyShortName(String value)
   {
      this.unifierCompanyShortName = value;
   }

   /**
    * Gets the value of the unifierIntegrationPassword property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUnifierIntegrationPassword()
   {
      return unifierIntegrationPassword;
   }

   /**
    * Sets the value of the unifierIntegrationPassword property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnifierIntegrationPassword(String value)
   {
      this.unifierIntegrationPassword = value;
   }

   /**
    * Gets the value of the unifierIntegrationUserName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUnifierIntegrationUserName()
   {
      return unifierIntegrationUserName;
   }

   /**
    * Sets the value of the unifierIntegrationUserName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnifierIntegrationUserName(String value)
   {
      this.unifierIntegrationUserName = value;
   }

   /**
    * Gets the value of the unifierWebServiceURL property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUnifierWebServiceURL()
   {
      return unifierWebServiceURL;
   }

   /**
    * Sets the value of the unifierWebServiceURL property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnifierWebServiceURL(String value)
   {
      this.unifierWebServiceURL = value;
   }

   /**
    * Gets the value of the useCalendarTimePeriodsFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isUseCalendarTimePeriodsFlag()
   {
      return useCalendarTimePeriodsFlag;
   }

   /**
    * Sets the value of the useCalendarTimePeriodsFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUseCalendarTimePeriodsFlag(Boolean value)
   {
      this.useCalendarTimePeriodsFlag = value;
   }

   /**
    * Gets the value of the useMaxTimesheetResourceHours property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isUseMaxTimesheetResourceHours()
   {
      return useMaxTimesheetResourceHours;
   }

   /**
    * Sets the value of the useMaxTimesheetResourceHours property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUseMaxTimesheetResourceHours(Boolean value)
   {
      this.useMaxTimesheetResourceHours = value;
   }

   /**
    * Gets the value of the useProjectManagerApproval property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getUseProjectManagerApproval()
   {
      return useProjectManagerApproval;
   }

   /**
    * Sets the value of the useProjectManagerApproval property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUseProjectManagerApproval(String value)
   {
      this.useProjectManagerApproval = value;
   }

   /**
    * Gets the value of the useTimesheets property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isUseTimesheets()
   {
      return useTimesheets;
   }

   /**
    * Sets the value of the useTimesheets property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUseTimesheets(Boolean value)
   {
      this.useTimesheets = value;
   }

   /**
    * Gets the value of the versionForWhatsNew property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getVersionForWhatsNew()
   {
      return versionForWhatsNew;
   }

   /**
    * Sets the value of the versionForWhatsNew property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setVersionForWhatsNew(String value)
   {
      this.versionForWhatsNew = value;
   }

   /**
    * Gets the value of the wbsCategoryLabel property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSCategoryLabel()
   {
      return wbsCategoryLabel;
   }

   /**
    * Sets the value of the wbsCategoryLabel property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSCategoryLabel(String value)
   {
      this.wbsCategoryLabel = value;
   }

   /**
    * Gets the value of the wbsCodeSeparator property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSCodeSeparator()
   {
      return wbsCodeSeparator;
   }

   /**
    * Sets the value of the wbsCodeSeparator property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSCodeSeparator(String value)
   {
      this.wbsCodeSeparator = value;
   }

   /**
    * Gets the value of the weekAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWeekAbbreviation()
   {
      return weekAbbreviation;
   }

   /**
    * Sets the value of the weekAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWeekAbbreviation(String value)
   {
      this.weekAbbreviation = value;
   }

   /**
    * Gets the value of the yearAbbreviation property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getYearAbbreviation()
   {
      return yearAbbreviation;
   }

   /**
    * Sets the value of the yearAbbreviation property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setYearAbbreviation(String value)
   {
      this.yearAbbreviation = value;
   }

}
