//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for CurrencyType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="CurrencyType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DecimalPlaces" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="0"/&gt;
 *               &lt;maxInclusive value="2"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DecimalSymbol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Period"/&gt;
 *               &lt;enumeration value="Comma"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DigitGroupingSymbol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Period"/&gt;
 *               &lt;enumeration value="Comma"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ExchangeRate" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="1.0E-6"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E8"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Id" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="6"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="IsBaseCurrency" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Name" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="NegativeSymbol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="(#1.1)"/&gt;
 *               &lt;enumeration value="-#1.1"/&gt;
 *               &lt;enumeration value="#-1.1"/&gt;
 *               &lt;enumeration value="#1.1-"/&gt;
 *               &lt;enumeration value="(1.1#)"/&gt;
 *               &lt;enumeration value="-1.1#"/&gt;
 *               &lt;enumeration value="1.1-#"/&gt;
 *               &lt;enumeration value="1.1#-"/&gt;
 *               &lt;enumeration value="-1.1 #"/&gt;
 *               &lt;enumeration value="-# 1.1"/&gt;
 *               &lt;enumeration value="1.1 #-"/&gt;
 *               &lt;enumeration value="# 1.1-"/&gt;
 *               &lt;enumeration value="# -1.1"/&gt;
 *               &lt;enumeration value="1.1- #"/&gt;
 *               &lt;enumeration value="(# 1.1)"/&gt;
 *               &lt;enumeration value="(1.1 #)"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="PositiveSymbol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="#1.1"/&gt;
 *               &lt;enumeration value="1.1#"/&gt;
 *               &lt;enumeration value="# 1.1"/&gt;
 *               &lt;enumeration value="1.1 #"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Symbol" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="3"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "CurrencyType", propOrder =
{
   "createDate",
   "createUser",
   "decimalPlaces",
   "decimalSymbol",
   "digitGroupingSymbol",
   "exchangeRate",
   "id",
   "isBaseCurrency",
   "lastUpdateDate",
   "lastUpdateUser",
   "name",
   "negativeSymbol",
   "objectId",
   "positiveSymbol",
   "symbol"
}) public class CurrencyType
{

   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "DecimalPlaces") protected Integer decimalPlaces;
   @XmlElement(name = "DecimalSymbol") @XmlJavaTypeAdapter(Adapter1.class) protected String decimalSymbol;
   @XmlElement(name = "DigitGroupingSymbol") @XmlJavaTypeAdapter(Adapter1.class) protected String digitGroupingSymbol;
   @XmlElement(name = "ExchangeRate", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double exchangeRate;
   @XmlElement(name = "Id") @XmlJavaTypeAdapter(Adapter1.class) protected String id;
   @XmlElement(name = "IsBaseCurrency", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isBaseCurrency;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "Name") @XmlJavaTypeAdapter(Adapter1.class) protected String name;
   @XmlElement(name = "NegativeSymbol") @XmlJavaTypeAdapter(Adapter1.class) protected String negativeSymbol;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "PositiveSymbol") @XmlJavaTypeAdapter(Adapter1.class) protected String positiveSymbol;
   @XmlElement(name = "Symbol") @XmlJavaTypeAdapter(Adapter1.class) protected String symbol;

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the decimalPlaces property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getDecimalPlaces()
   {
      return decimalPlaces;
   }

   /**
    * Sets the value of the decimalPlaces property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setDecimalPlaces(Integer value)
   {
      this.decimalPlaces = value;
   }

   /**
    * Gets the value of the decimalSymbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDecimalSymbol()
   {
      return decimalSymbol;
   }

   /**
    * Sets the value of the decimalSymbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDecimalSymbol(String value)
   {
      this.decimalSymbol = value;
   }

   /**
    * Gets the value of the digitGroupingSymbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDigitGroupingSymbol()
   {
      return digitGroupingSymbol;
   }

   /**
    * Sets the value of the digitGroupingSymbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDigitGroupingSymbol(String value)
   {
      this.digitGroupingSymbol = value;
   }

   /**
    * Gets the value of the exchangeRate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExchangeRate()
   {
      return exchangeRate;
   }

   /**
    * Sets the value of the exchangeRate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExchangeRate(Double value)
   {
      this.exchangeRate = value;
   }

   /**
    * Gets the value of the id property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getId()
   {
      return id;
   }

   /**
    * Sets the value of the id property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setId(String value)
   {
      this.id = value;
   }

   /**
    * Gets the value of the isBaseCurrency property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsBaseCurrency()
   {
      return isBaseCurrency;
   }

   /**
    * Sets the value of the isBaseCurrency property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsBaseCurrency(Boolean value)
   {
      this.isBaseCurrency = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the name property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getName()
   {
      return name;
   }

   /**
    * Sets the value of the name property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setName(String value)
   {
      this.name = value;
   }

   /**
    * Gets the value of the negativeSymbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getNegativeSymbol()
   {
      return negativeSymbol;
   }

   /**
    * Sets the value of the negativeSymbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNegativeSymbol(String value)
   {
      this.negativeSymbol = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the positiveSymbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPositiveSymbol()
   {
      return positiveSymbol;
   }

   /**
    * Sets the value of the positiveSymbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPositiveSymbol(String value)
   {
      this.positiveSymbol = value;
   }

   /**
    * Gets the value of the symbol property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSymbol()
   {
      return symbol;
   }

   /**
    * Sets the value of the symbol property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSymbol(String value)
   {
      this.symbol = value;
   }

}
