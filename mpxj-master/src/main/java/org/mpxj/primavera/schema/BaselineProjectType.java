//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for BaselineProjectType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="BaselineProjectType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ActivityDefaultActivityType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Task Dependent"/&gt;
 *               &lt;enumeration value="Resource Dependent"/&gt;
 *               &lt;enumeration value="Level of Effort"/&gt;
 *               &lt;enumeration value="Start Milestone"/&gt;
 *               &lt;enumeration value="Finish Milestone"/&gt;
 *               &lt;enumeration value="WBS Summary"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActivityDefaultCalendarObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ActivityDefaultCostAccountObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ActivityDefaultDurationType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Fixed Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Units"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActivityDefaultPercentCompleteType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Physical"/&gt;
 *               &lt;enumeration value="Duration"/&gt;
 *               &lt;enumeration value="Units"/&gt;
 *               &lt;enumeration value="Scope"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActivityDefaultPricePerUnit" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActivityDefaultReviewRequired" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ActivityIdBasedOnSelectedActivity" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ActivityIdIncrement" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ActivityIdPrefix" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="20"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActivityIdSuffix" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ActivityPercentCompleteBasedOnActivitySteps" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AddActualToRemaining" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AddedBy" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="32"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="AllowStatusReview" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AnnualDiscountRate" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="100.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="AnticipatedFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="AnticipatedStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="AssignmentDefaultDrivingFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="AssignmentDefaultRateType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Price / Unit"/&gt;
 *               &lt;enumeration value="Price / Unit 2"/&gt;
 *               &lt;enumeration value="Price / Unit 3"/&gt;
 *               &lt;enumeration value="Price / Unit 4"/&gt;
 *               &lt;enumeration value="Price / Unit 5"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="BaselineTypeName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="BaselineTypeObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CheckOutDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CheckOutStatus" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="CheckOutUserObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ContainsSummaryData" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="CostQuantityRecalculateFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CriticalActivityFloatLimit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CriticalActivityPathType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Critical Float"/&gt;
 *               &lt;enumeration value="Longest Path"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CurrentBudget" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CurrentVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="DataDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="DateAdded" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="DefaultPriceTimeUnits" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Days Hours"/&gt;
 *               &lt;enumeration value="Hours Minutes"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Description" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="500"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DiscountApplicationPeriod" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DistributedCurrentBudget" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EnablePublication" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="EnableSummarization" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="FinancialPeriodTmplId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="FiscalYearStartMonth" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ForecastFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ForecastStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="GUID" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;pattern value="\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}|"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HasFutureBucketData" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="HistoryInterval" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Financial Period"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HistoryLevel" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="None"/&gt;
 *               &lt;enumeration value="Project"/&gt;
 *               &lt;enumeration value="WBS"/&gt;
 *               &lt;enumeration value="Activity_Daily"/&gt;
 *               &lt;enumeration value="Activity_Weekly"/&gt;
 *               &lt;enumeration value="Activity_Monthly"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Id" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="IndependentETCLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="IndependentETCTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="IntegratedWBS" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LastBaselineUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastFinancialPeriodObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="LastLevelDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastPublishedOn" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastScheduleDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastSummarizedDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateBaselineOptions" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LevelingPriority" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LinkActualToActualThisPeriod" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LinkPercentCompleteWithActual" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LinkPlannedAndAtCompletionFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LocationName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LocationObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="MustFinishByDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Name" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="OBSName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="OBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="OriginalBudget" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="OriginalProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="OwnerResourceObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ParentEPSId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ParentEPSName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ParentEPSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="PlannedStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PrimaryResourcesCanMarkActivitiesAsCompleted" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ProjectForecastStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ProjectScheduleType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Duration"/&gt;
 *               &lt;enumeration value="Resource"/&gt;
 *               &lt;enumeration value="Cost"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ProposedBudget" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PublicationPriority" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ResetPlannedToRemainingFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourceCanBeAssignedToSameActivityMoreThanOnce" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourceName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="225"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ResourcesCanAssignThemselvesToActivities" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourcesCanAssignThemselvesToActivitiesOutsideOBSAccess" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourcesCanEditAssignmentPercentComplete" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ResourcesCanStaffRoleAssignment" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="RiskExposure" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RiskLevel" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Very High"/&gt;
 *               &lt;enumeration value="High"/&gt;
 *               &lt;enumeration value="Medium"/&gt;
 *               &lt;enumeration value="Low"/&gt;
 *               &lt;enumeration value="Very Low"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RiskMatrixObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="RiskScore" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ScheduledFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Status" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Planned"/&gt;
 *               &lt;enumeration value="Active"/&gt;
 *               &lt;enumeration value="Inactive"/&gt;
 *               &lt;enumeration value="What-If"/&gt;
 *               &lt;enumeration value="Requested"/&gt;
 *               &lt;enumeration value="Template"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="StatusReviewerName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="StatusReviewerObjectId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="StrategicPriority" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}int"&gt;
 *               &lt;minInclusive value="1"/&gt;
 *               &lt;maxInclusive value="10000"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SummarizeToWBSLevel" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummarizedDataDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAccountingVarianceByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAccountingVarianceByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualThisPeriodNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualValueByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryActualValueByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryAtCompletionTotalCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineCompletedActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineInProgressActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineNotStartedActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBaselineTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBudgetAtCompletionByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryBudgetAtCompletionByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCompletedActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostPercentOfPlanned" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostPerformanceIndexByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostPerformanceIndexByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostVarianceByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostVarianceByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostVarianceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostVarianceIndexByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryCostVarianceIndexByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryDurationPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryDurationPercentOfPlanned" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryDurationVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEarnedValueByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEarnedValueByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateAtCompletionByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateAtCompletionByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateAtCompletionHighPercentByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateAtCompletionLowPercentByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateToCompleteByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryEstimateToCompleteByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryExpenseCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryExpenseCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryFinishDateVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryInProgressActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryLaborCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryLaborCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryLaborUnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryLaborUnitsVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryMaterialCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryMaterialCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryNonLaborCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryNonLaborCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryNonLaborUnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryNonLaborUnitsVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryNotStartedActivityCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPerformancePercentCompleteByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedValueByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryPlannedValueByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryProgressFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SummaryRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummarySchedulePercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummarySchedulePercentCompleteByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummarySchedulePercentCompleteByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummarySchedulePerformanceIndexByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummarySchedulePerformanceIndexByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryScheduleVarianceByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryScheduleVarianceByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryScheduleVarianceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryScheduleVarianceIndexByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryScheduleVarianceIndexByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryStartDateVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryToCompletePerformanceIndexByCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryTotalCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryTotalFloat" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryUnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SummaryVarianceAtCompletionByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TeamMemberActivityFields" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="512"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TeamMemberAssignmentOption" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="32"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TeamMemberResourceAssignmentFields" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="512"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TeamMemberStepUDFViewableFields" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="512"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TeamMemberViewableFields" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="512"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TotalBenefitPlan" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalBenefitPlanTally" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalSpendingPlan" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalSpendingPlanTally" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="UnallocatedBudget" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="UndistributedCurrentVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="WBSCategoryObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="WBSCodeSeparator" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="2"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="WebSiteRootDirectory" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="120"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WebSiteURL" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="200"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Code" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}CodeAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="UDF" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}UDFAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="Spread" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}EPSProjectWBSSpreadType" minOccurs="0"/&gt;
 *         &lt;element name="ProjectResourceSpread" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectResourceSpreadType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="ProjectRoleSpread" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectRoleSpreadType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;sequence&gt;
 *           &lt;element name="Calendar" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}CalendarType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="WBS" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}WBSType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectResource" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectResourceType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectResourceQuantity" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectResourceQuantityType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectBudgetChangeLog" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectBudgetChangeLogType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="WBSMilestone" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}WBSMilestoneType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectNote" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectNoteType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectThreshold" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectThresholdType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectSpendingPlan" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectSpendingPlanType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectFunding" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectFundingType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityCodeType" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityCodeTypeType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityCode" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityCodeType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="Activity" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ResourceAssignment" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityExpense" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityExpenseType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityNote" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityNoteType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityStep" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityStepType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="Relationship" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RelationshipType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityPeriodActual" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityPeriodActualType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectIssue" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectIssueType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ResourceAssignmentPeriodActual" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceAssignmentPeriodActualType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="Document" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}DocumentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ProjectDocument" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ProjectDocumentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ScheduleOptions" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ScheduleOptionsType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="Risk" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RiskType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="ActivityRisk" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivityRiskType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="RiskImpact" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RiskImpactType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="RiskResponsePlan" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RiskResponsePlanType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="RiskResponseAction" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RiskResponseActionType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *           &lt;element name="RiskResponseActionImpact" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}RiskResponseActionImpactType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;/sequence&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "BaselineProjectType", propOrder =
{
   "activityDefaultActivityType",
   "activityDefaultCalendarObjectId",
   "activityDefaultCostAccountObjectId",
   "activityDefaultDurationType",
   "activityDefaultPercentCompleteType",
   "activityDefaultPricePerUnit",
   "activityDefaultReviewRequired",
   "activityIdBasedOnSelectedActivity",
   "activityIdIncrement",
   "activityIdPrefix",
   "activityIdSuffix",
   "activityPercentCompleteBasedOnActivitySteps",
   "addActualToRemaining",
   "addedBy",
   "allowStatusReview",
   "annualDiscountRate",
   "anticipatedFinishDate",
   "anticipatedStartDate",
   "assignmentDefaultDrivingFlag",
   "assignmentDefaultRateType",
   "baselineTypeName",
   "baselineTypeObjectId",
   "checkOutDate",
   "checkOutStatus",
   "checkOutUserObjectId",
   "containsSummaryData",
   "costQuantityRecalculateFlag",
   "createDate",
   "createUser",
   "criticalActivityFloatLimit",
   "criticalActivityPathType",
   "currentBudget",
   "currentVariance",
   "dataDate",
   "dateAdded",
   "defaultPriceTimeUnits",
   "description",
   "discountApplicationPeriod",
   "distributedCurrentBudget",
   "enablePublication",
   "enableSummarization",
   "financialPeriodTmplId",
   "finishDate",
   "fiscalYearStartMonth",
   "forecastFinishDate",
   "forecastStartDate",
   "guid",
   "hasFutureBucketData",
   "historyInterval",
   "historyLevel",
   "id",
   "independentETCLaborUnits",
   "independentETCTotalCost",
   "integratedWBS",
   "lastBaselineUpdateDate",
   "lastFinancialPeriodObjectId",
   "lastLevelDate",
   "lastPublishedOn",
   "lastScheduleDate",
   "lastSummarizedDate",
   "lastUpdateBaselineOptions",
   "lastUpdateDate",
   "lastUpdateUser",
   "levelingPriority",
   "linkActualToActualThisPeriod",
   "linkPercentCompleteWithActual",
   "linkPlannedAndAtCompletionFlag",
   "locationName",
   "locationObjectId",
   "mustFinishByDate",
   "name",
   "obsName",
   "obsObjectId",
   "objectId",
   "originalBudget",
   "originalProjectObjectId",
   "ownerResourceObjectId",
   "parentEPSId",
   "parentEPSName",
   "parentEPSObjectId",
   "plannedStartDate",
   "primaryResourcesCanMarkActivitiesAsCompleted",
   "projectForecastStartDate",
   "projectScheduleType",
   "proposedBudget",
   "publicationPriority",
   "resetPlannedToRemainingFlag",
   "resourceCanBeAssignedToSameActivityMoreThanOnce",
   "resourceName",
   "resourcesCanAssignThemselvesToActivities",
   "resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess",
   "resourcesCanEditAssignmentPercentComplete",
   "resourcesCanStaffRoleAssignment",
   "riskExposure",
   "riskLevel",
   "riskMatrixObjectId",
   "riskScore",
   "scheduledFinishDate",
   "startDate",
   "status",
   "statusReviewerName",
   "statusReviewerObjectId",
   "strategicPriority",
   "summarizeToWBSLevel",
   "summarizedDataDate",
   "summaryAccountingVarianceByCost",
   "summaryAccountingVarianceByLaborUnits",
   "summaryActivityCount",
   "summaryActualDuration",
   "summaryActualExpenseCost",
   "summaryActualFinishDate",
   "summaryActualLaborCost",
   "summaryActualLaborUnits",
   "summaryActualMaterialCost",
   "summaryActualNonLaborCost",
   "summaryActualNonLaborUnits",
   "summaryActualStartDate",
   "summaryActualThisPeriodCost",
   "summaryActualThisPeriodLaborCost",
   "summaryActualThisPeriodLaborUnits",
   "summaryActualThisPeriodMaterialCost",
   "summaryActualThisPeriodNonLaborCost",
   "summaryActualThisPeriodNonLaborUnits",
   "summaryActualTotalCost",
   "summaryActualValueByCost",
   "summaryActualValueByLaborUnits",
   "summaryAtCompletionDuration",
   "summaryAtCompletionExpenseCost",
   "summaryAtCompletionLaborCost",
   "summaryAtCompletionLaborUnits",
   "summaryAtCompletionMaterialCost",
   "summaryAtCompletionNonLaborCost",
   "summaryAtCompletionNonLaborUnits",
   "summaryAtCompletionTotalCost",
   "summaryAtCompletionTotalCostVariance",
   "summaryBaselineCompletedActivityCount",
   "summaryBaselineDuration",
   "summaryBaselineExpenseCost",
   "summaryBaselineFinishDate",
   "summaryBaselineInProgressActivityCount",
   "summaryBaselineLaborCost",
   "summaryBaselineLaborUnits",
   "summaryBaselineMaterialCost",
   "summaryBaselineNonLaborCost",
   "summaryBaselineNonLaborUnits",
   "summaryBaselineNotStartedActivityCount",
   "summaryBaselineStartDate",
   "summaryBaselineTotalCost",
   "summaryBudgetAtCompletionByCost",
   "summaryBudgetAtCompletionByLaborUnits",
   "summaryCompletedActivityCount",
   "summaryCostPercentComplete",
   "summaryCostPercentOfPlanned",
   "summaryCostPerformanceIndexByCost",
   "summaryCostPerformanceIndexByLaborUnits",
   "summaryCostVarianceByCost",
   "summaryCostVarianceByLaborUnits",
   "summaryCostVarianceIndex",
   "summaryCostVarianceIndexByCost",
   "summaryCostVarianceIndexByLaborUnits",
   "summaryDurationPercentComplete",
   "summaryDurationPercentOfPlanned",
   "summaryDurationVariance",
   "summaryEarnedValueByCost",
   "summaryEarnedValueByLaborUnits",
   "summaryEstimateAtCompletionByCost",
   "summaryEstimateAtCompletionByLaborUnits",
   "summaryEstimateAtCompletionHighPercentByLaborUnits",
   "summaryEstimateAtCompletionLowPercentByLaborUnits",
   "summaryEstimateToCompleteByCost",
   "summaryEstimateToCompleteByLaborUnits",
   "summaryExpenseCostPercentComplete",
   "summaryExpenseCostVariance",
   "summaryFinishDateVariance",
   "summaryInProgressActivityCount",
   "summaryLaborCostPercentComplete",
   "summaryLaborCostVariance",
   "summaryLaborUnitsPercentComplete",
   "summaryLaborUnitsVariance",
   "summaryMaterialCostPercentComplete",
   "summaryMaterialCostVariance",
   "summaryNonLaborCostPercentComplete",
   "summaryNonLaborCostVariance",
   "summaryNonLaborUnitsPercentComplete",
   "summaryNonLaborUnitsVariance",
   "summaryNotStartedActivityCount",
   "summaryPerformancePercentCompleteByLaborUnits",
   "summaryPlannedCost",
   "summaryPlannedDuration",
   "summaryPlannedExpenseCost",
   "summaryPlannedFinishDate",
   "summaryPlannedLaborCost",
   "summaryPlannedLaborUnits",
   "summaryPlannedMaterialCost",
   "summaryPlannedNonLaborCost",
   "summaryPlannedNonLaborUnits",
   "summaryPlannedStartDate",
   "summaryPlannedValueByCost",
   "summaryPlannedValueByLaborUnits",
   "summaryProgressFinishDate",
   "summaryRemainingDuration",
   "summaryRemainingExpenseCost",
   "summaryRemainingFinishDate",
   "summaryRemainingLaborCost",
   "summaryRemainingLaborUnits",
   "summaryRemainingMaterialCost",
   "summaryRemainingNonLaborCost",
   "summaryRemainingNonLaborUnits",
   "summaryRemainingStartDate",
   "summaryRemainingTotalCost",
   "summarySchedulePercentComplete",
   "summarySchedulePercentCompleteByCost",
   "summarySchedulePercentCompleteByLaborUnits",
   "summarySchedulePerformanceIndexByCost",
   "summarySchedulePerformanceIndexByLaborUnits",
   "summaryScheduleVarianceByCost",
   "summaryScheduleVarianceByLaborUnits",
   "summaryScheduleVarianceIndex",
   "summaryScheduleVarianceIndexByCost",
   "summaryScheduleVarianceIndexByLaborUnits",
   "summaryStartDateVariance",
   "summaryToCompletePerformanceIndexByCost",
   "summaryTotalCostVariance",
   "summaryTotalFloat",
   "summaryUnitsPercentComplete",
   "summaryVarianceAtCompletionByLaborUnits",
   "teamMemberActivityFields",
   "teamMemberAssignmentOption",
   "teamMemberResourceAssignmentFields",
   "teamMemberStepUDFViewableFields",
   "teamMemberViewableFields",
   "totalBenefitPlan",
   "totalBenefitPlanTally",
   "totalSpendingPlan",
   "totalSpendingPlanTally",
   "unallocatedBudget",
   "undistributedCurrentVariance",
   "wbsCategoryObjectId",
   "wbsCodeSeparator",
   "wbsObjectId",
   "webSiteRootDirectory",
   "webSiteURL",
   "code",
   "udf",
   "spread",
   "projectResourceSpread",
   "projectRoleSpread",
   "calendar",
   "wbs",
   "projectResource",
   "projectResourceQuantity",
   "projectBudgetChangeLog",
   "wbsMilestone",
   "projectNote",
   "projectThreshold",
   "projectSpendingPlan",
   "projectFunding",
   "activityCodeType",
   "activityCode",
   "activity",
   "resourceAssignment",
   "activityExpense",
   "activityNote",
   "activityStep",
   "relationship",
   "activityPeriodActual",
   "projectIssue",
   "resourceAssignmentPeriodActual",
   "document",
   "projectDocument",
   "scheduleOptions",
   "risk",
   "activityRisk",
   "riskImpact",
   "riskResponsePlan",
   "riskResponseAction",
   "riskResponseActionImpact"
}) public class BaselineProjectType
{

   @XmlElement(name = "ActivityDefaultActivityType") @XmlJavaTypeAdapter(Adapter1.class) protected String activityDefaultActivityType;
   @XmlElement(name = "ActivityDefaultCalendarObjectId", nillable = true) protected Integer activityDefaultCalendarObjectId;
   @XmlElement(name = "ActivityDefaultCostAccountObjectId", nillable = true) protected Integer activityDefaultCostAccountObjectId;
   @XmlElement(name = "ActivityDefaultDurationType") @XmlJavaTypeAdapter(Adapter1.class) protected String activityDefaultDurationType;
   @XmlElement(name = "ActivityDefaultPercentCompleteType") @XmlJavaTypeAdapter(Adapter1.class) protected String activityDefaultPercentCompleteType;
   @XmlElement(name = "ActivityDefaultPricePerUnit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double activityDefaultPricePerUnit;
   @XmlElement(name = "ActivityDefaultReviewRequired", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean activityDefaultReviewRequired;
   @XmlElement(name = "ActivityIdBasedOnSelectedActivity", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean activityIdBasedOnSelectedActivity;
   @XmlElement(name = "ActivityIdIncrement", nillable = true) protected Integer activityIdIncrement;
   @XmlElement(name = "ActivityIdPrefix") @XmlJavaTypeAdapter(Adapter1.class) protected String activityIdPrefix;
   @XmlElement(name = "ActivityIdSuffix", nillable = true) protected Integer activityIdSuffix;
   @XmlElement(name = "ActivityPercentCompleteBasedOnActivitySteps", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean activityPercentCompleteBasedOnActivitySteps;
   @XmlElement(name = "AddActualToRemaining", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean addActualToRemaining;
   @XmlElement(name = "AddedBy") @XmlJavaTypeAdapter(Adapter1.class) protected String addedBy;
   @XmlElement(name = "AllowStatusReview", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean allowStatusReview;
   @XmlElement(name = "AnnualDiscountRate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double annualDiscountRate;
   @XmlElement(name = "AnticipatedFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime anticipatedFinishDate;
   @XmlElement(name = "AnticipatedStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime anticipatedStartDate;
   @XmlElement(name = "AssignmentDefaultDrivingFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean assignmentDefaultDrivingFlag;
   @XmlElement(name = "AssignmentDefaultRateType") @XmlJavaTypeAdapter(Adapter1.class) protected String assignmentDefaultRateType;
   @XmlElement(name = "BaselineTypeName") @XmlJavaTypeAdapter(Adapter1.class) protected String baselineTypeName;
   @XmlElement(name = "BaselineTypeObjectId", nillable = true) protected Integer baselineTypeObjectId;
   @XmlElement(name = "CheckOutDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime checkOutDate;
   @XmlElement(name = "CheckOutStatus", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean checkOutStatus;
   @XmlElement(name = "CheckOutUserObjectId", nillable = true) protected Integer checkOutUserObjectId;
   @XmlElement(name = "ContainsSummaryData", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean containsSummaryData;
   @XmlElement(name = "CostQuantityRecalculateFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean costQuantityRecalculateFlag;
   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "CriticalActivityFloatLimit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double criticalActivityFloatLimit;
   @XmlElement(name = "CriticalActivityPathType") @XmlJavaTypeAdapter(Adapter1.class) protected String criticalActivityPathType;
   @XmlElement(name = "CurrentBudget", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double currentBudget;
   @XmlElement(name = "CurrentVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double currentVariance;
   @XmlElement(name = "DataDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dataDate;
   @XmlElement(name = "DateAdded", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateAdded;
   @XmlElement(name = "DefaultPriceTimeUnits") @XmlJavaTypeAdapter(Adapter1.class) protected String defaultPriceTimeUnits;
   @XmlElement(name = "Description") @XmlJavaTypeAdapter(Adapter1.class) protected String description;
   @XmlElement(name = "DiscountApplicationPeriod") @XmlJavaTypeAdapter(Adapter1.class) protected String discountApplicationPeriod;
   @XmlElement(name = "DistributedCurrentBudget", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double distributedCurrentBudget;
   @XmlElement(name = "EnablePublication", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enablePublication;
   @XmlElement(name = "EnableSummarization", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean enableSummarization;
   @XmlElement(name = "FinancialPeriodTmplId") protected Integer financialPeriodTmplId;
   @XmlElement(name = "FinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime finishDate;
   @XmlElement(name = "FiscalYearStartMonth") protected Integer fiscalYearStartMonth;
   @XmlElement(name = "ForecastFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime forecastFinishDate;
   @XmlElement(name = "ForecastStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime forecastStartDate;
   @XmlElement(name = "GUID") @XmlJavaTypeAdapter(Adapter1.class) protected String guid;
   @XmlElement(name = "HasFutureBucketData", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean hasFutureBucketData;
   @XmlElement(name = "HistoryInterval") @XmlJavaTypeAdapter(Adapter1.class) protected String historyInterval;
   @XmlElement(name = "HistoryLevel") @XmlJavaTypeAdapter(Adapter1.class) protected String historyLevel;
   @XmlElement(name = "Id") @XmlJavaTypeAdapter(Adapter1.class) protected String id;
   @XmlElement(name = "IndependentETCLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double independentETCLaborUnits;
   @XmlElement(name = "IndependentETCTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double independentETCTotalCost;
   @XmlElement(name = "IntegratedWBS", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean integratedWBS;
   @XmlElement(name = "LastBaselineUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastBaselineUpdateDate;
   @XmlElement(name = "LastFinancialPeriodObjectId", nillable = true) protected Integer lastFinancialPeriodObjectId;
   @XmlElement(name = "LastLevelDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastLevelDate;
   @XmlElement(name = "LastPublishedOn", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastPublishedOn;
   @XmlElement(name = "LastScheduleDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastScheduleDate;
   @XmlElement(name = "LastSummarizedDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastSummarizedDate;
   @XmlElement(name = "LastUpdateBaselineOptions") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateBaselineOptions;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "LevelingPriority") protected Integer levelingPriority;
   @XmlElement(name = "LinkActualToActualThisPeriod", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean linkActualToActualThisPeriod;
   @XmlElement(name = "LinkPercentCompleteWithActual", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean linkPercentCompleteWithActual;
   @XmlElement(name = "LinkPlannedAndAtCompletionFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean linkPlannedAndAtCompletionFlag;
   @XmlElement(name = "LocationName") @XmlJavaTypeAdapter(Adapter1.class) protected String locationName;
   @XmlElement(name = "LocationObjectId", nillable = true) protected Integer locationObjectId;
   @XmlElement(name = "MustFinishByDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime mustFinishByDate;
   @XmlElement(name = "Name") @XmlJavaTypeAdapter(Adapter1.class) protected String name;
   @XmlElement(name = "OBSName") @XmlJavaTypeAdapter(Adapter1.class) protected String obsName;
   @XmlElement(name = "OBSObjectId") protected Integer obsObjectId;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "OriginalBudget", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double originalBudget;
   @XmlElement(name = "OriginalProjectObjectId", nillable = true) protected Integer originalProjectObjectId;
   @XmlElement(name = "OwnerResourceObjectId", nillable = true) protected Integer ownerResourceObjectId;
   @XmlElement(name = "ParentEPSId") @XmlJavaTypeAdapter(Adapter1.class) protected String parentEPSId;
   @XmlElement(name = "ParentEPSName") @XmlJavaTypeAdapter(Adapter1.class) protected String parentEPSName;
   @XmlElement(name = "ParentEPSObjectId") protected Integer parentEPSObjectId;
   @XmlElement(name = "PlannedStartDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime plannedStartDate;
   @XmlElement(name = "PrimaryResourcesCanMarkActivitiesAsCompleted", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean primaryResourcesCanMarkActivitiesAsCompleted;
   @XmlElement(name = "ProjectForecastStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime projectForecastStartDate;
   @XmlElement(name = "ProjectScheduleType") @XmlJavaTypeAdapter(Adapter1.class) protected String projectScheduleType;
   @XmlElement(name = "ProposedBudget", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double proposedBudget;
   @XmlElement(name = "PublicationPriority") protected Integer publicationPriority;
   @XmlElement(name = "ResetPlannedToRemainingFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resetPlannedToRemainingFlag;
   @XmlElement(name = "ResourceCanBeAssignedToSameActivityMoreThanOnce", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourceCanBeAssignedToSameActivityMoreThanOnce;
   @XmlElement(name = "ResourceName") @XmlJavaTypeAdapter(Adapter1.class) protected String resourceName;
   @XmlElement(name = "ResourcesCanAssignThemselvesToActivities", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanAssignThemselvesToActivities;
   @XmlElement(name = "ResourcesCanAssignThemselvesToActivitiesOutsideOBSAccess", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess;
   @XmlElement(name = "ResourcesCanEditAssignmentPercentComplete", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanEditAssignmentPercentComplete;
   @XmlElement(name = "ResourcesCanStaffRoleAssignment", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean resourcesCanStaffRoleAssignment;
   @XmlElement(name = "RiskExposure", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double riskExposure;
   @XmlElement(name = "RiskLevel") @XmlJavaTypeAdapter(Adapter1.class) protected String riskLevel;
   @XmlElement(name = "RiskMatrixObjectId", nillable = true) protected Integer riskMatrixObjectId;
   @XmlElement(name = "RiskScore", nillable = true) protected Integer riskScore;
   @XmlElement(name = "ScheduledFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime scheduledFinishDate;
   @XmlElement(name = "StartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "Status") @XmlJavaTypeAdapter(Adapter1.class) protected String status;
   @XmlElement(name = "StatusReviewerName") @XmlJavaTypeAdapter(Adapter1.class) protected String statusReviewerName;
   @XmlElement(name = "StatusReviewerObjectId") @XmlJavaTypeAdapter(Adapter1.class) protected String statusReviewerObjectId;
   @XmlElement(name = "StrategicPriority", nillable = true) protected Integer strategicPriority;
   @XmlElement(name = "SummarizeToWBSLevel", nillable = true) protected Integer summarizeToWBSLevel;
   @XmlElement(name = "SummarizedDataDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summarizedDataDate;
   @XmlElement(name = "SummaryAccountingVarianceByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAccountingVarianceByCost;
   @XmlElement(name = "SummaryAccountingVarianceByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAccountingVarianceByLaborUnits;
   @XmlElement(name = "SummaryActivityCount", nillable = true) protected Integer summaryActivityCount;
   @XmlElement(name = "SummaryActualDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualDuration;
   @XmlElement(name = "SummaryActualExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualExpenseCost;
   @XmlElement(name = "SummaryActualFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryActualFinishDate;
   @XmlElement(name = "SummaryActualLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualLaborCost;
   @XmlElement(name = "SummaryActualLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualLaborUnits;
   @XmlElement(name = "SummaryActualMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualMaterialCost;
   @XmlElement(name = "SummaryActualNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualNonLaborCost;
   @XmlElement(name = "SummaryActualNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualNonLaborUnits;
   @XmlElement(name = "SummaryActualStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryActualStartDate;
   @XmlElement(name = "SummaryActualThisPeriodCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodCost;
   @XmlElement(name = "SummaryActualThisPeriodLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodLaborCost;
   @XmlElement(name = "SummaryActualThisPeriodLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodLaborUnits;
   @XmlElement(name = "SummaryActualThisPeriodMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodMaterialCost;
   @XmlElement(name = "SummaryActualThisPeriodNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodNonLaborCost;
   @XmlElement(name = "SummaryActualThisPeriodNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualThisPeriodNonLaborUnits;
   @XmlElement(name = "SummaryActualTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualTotalCost;
   @XmlElement(name = "SummaryActualValueByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualValueByCost;
   @XmlElement(name = "SummaryActualValueByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryActualValueByLaborUnits;
   @XmlElement(name = "SummaryAtCompletionDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionDuration;
   @XmlElement(name = "SummaryAtCompletionExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionExpenseCost;
   @XmlElement(name = "SummaryAtCompletionLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionLaborCost;
   @XmlElement(name = "SummaryAtCompletionLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionLaborUnits;
   @XmlElement(name = "SummaryAtCompletionMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionMaterialCost;
   @XmlElement(name = "SummaryAtCompletionNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionNonLaborCost;
   @XmlElement(name = "SummaryAtCompletionNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionNonLaborUnits;
   @XmlElement(name = "SummaryAtCompletionTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionTotalCost;
   @XmlElement(name = "SummaryAtCompletionTotalCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryAtCompletionTotalCostVariance;
   @XmlElement(name = "SummaryBaselineCompletedActivityCount", nillable = true) protected Integer summaryBaselineCompletedActivityCount;
   @XmlElement(name = "SummaryBaselineDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineDuration;
   @XmlElement(name = "SummaryBaselineExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineExpenseCost;
   @XmlElement(name = "SummaryBaselineFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryBaselineFinishDate;
   @XmlElement(name = "SummaryBaselineInProgressActivityCount", nillable = true) protected Integer summaryBaselineInProgressActivityCount;
   @XmlElement(name = "SummaryBaselineLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineLaborCost;
   @XmlElement(name = "SummaryBaselineLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineLaborUnits;
   @XmlElement(name = "SummaryBaselineMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineMaterialCost;
   @XmlElement(name = "SummaryBaselineNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineNonLaborCost;
   @XmlElement(name = "SummaryBaselineNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineNonLaborUnits;
   @XmlElement(name = "SummaryBaselineNotStartedActivityCount", nillable = true) protected Integer summaryBaselineNotStartedActivityCount;
   @XmlElement(name = "SummaryBaselineStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryBaselineStartDate;
   @XmlElement(name = "SummaryBaselineTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBaselineTotalCost;
   @XmlElement(name = "SummaryBudgetAtCompletionByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBudgetAtCompletionByCost;
   @XmlElement(name = "SummaryBudgetAtCompletionByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryBudgetAtCompletionByLaborUnits;
   @XmlElement(name = "SummaryCompletedActivityCount", nillable = true) protected Integer summaryCompletedActivityCount;
   @XmlElement(name = "SummaryCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostPercentComplete;
   @XmlElement(name = "SummaryCostPercentOfPlanned", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostPercentOfPlanned;
   @XmlElement(name = "SummaryCostPerformanceIndexByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostPerformanceIndexByCost;
   @XmlElement(name = "SummaryCostPerformanceIndexByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostPerformanceIndexByLaborUnits;
   @XmlElement(name = "SummaryCostVarianceByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostVarianceByCost;
   @XmlElement(name = "SummaryCostVarianceByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostVarianceByLaborUnits;
   @XmlElement(name = "SummaryCostVarianceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostVarianceIndex;
   @XmlElement(name = "SummaryCostVarianceIndexByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostVarianceIndexByCost;
   @XmlElement(name = "SummaryCostVarianceIndexByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryCostVarianceIndexByLaborUnits;
   @XmlElement(name = "SummaryDurationPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryDurationPercentComplete;
   @XmlElement(name = "SummaryDurationPercentOfPlanned", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryDurationPercentOfPlanned;
   @XmlElement(name = "SummaryDurationVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryDurationVariance;
   @XmlElement(name = "SummaryEarnedValueByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEarnedValueByCost;
   @XmlElement(name = "SummaryEarnedValueByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEarnedValueByLaborUnits;
   @XmlElement(name = "SummaryEstimateAtCompletionByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateAtCompletionByCost;
   @XmlElement(name = "SummaryEstimateAtCompletionByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateAtCompletionByLaborUnits;
   @XmlElement(name = "SummaryEstimateAtCompletionHighPercentByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateAtCompletionHighPercentByLaborUnits;
   @XmlElement(name = "SummaryEstimateAtCompletionLowPercentByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateAtCompletionLowPercentByLaborUnits;
   @XmlElement(name = "SummaryEstimateToCompleteByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateToCompleteByCost;
   @XmlElement(name = "SummaryEstimateToCompleteByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryEstimateToCompleteByLaborUnits;
   @XmlElement(name = "SummaryExpenseCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryExpenseCostPercentComplete;
   @XmlElement(name = "SummaryExpenseCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryExpenseCostVariance;
   @XmlElement(name = "SummaryFinishDateVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryFinishDateVariance;
   @XmlElement(name = "SummaryInProgressActivityCount", nillable = true) protected Integer summaryInProgressActivityCount;
   @XmlElement(name = "SummaryLaborCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryLaborCostPercentComplete;
   @XmlElement(name = "SummaryLaborCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryLaborCostVariance;
   @XmlElement(name = "SummaryLaborUnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryLaborUnitsPercentComplete;
   @XmlElement(name = "SummaryLaborUnitsVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryLaborUnitsVariance;
   @XmlElement(name = "SummaryMaterialCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryMaterialCostPercentComplete;
   @XmlElement(name = "SummaryMaterialCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryMaterialCostVariance;
   @XmlElement(name = "SummaryNonLaborCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryNonLaborCostPercentComplete;
   @XmlElement(name = "SummaryNonLaborCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryNonLaborCostVariance;
   @XmlElement(name = "SummaryNonLaborUnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryNonLaborUnitsPercentComplete;
   @XmlElement(name = "SummaryNonLaborUnitsVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryNonLaborUnitsVariance;
   @XmlElement(name = "SummaryNotStartedActivityCount", nillable = true) protected Integer summaryNotStartedActivityCount;
   @XmlElement(name = "SummaryPerformancePercentCompleteByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPerformancePercentCompleteByLaborUnits;
   @XmlElement(name = "SummaryPlannedCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedCost;
   @XmlElement(name = "SummaryPlannedDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedDuration;
   @XmlElement(name = "SummaryPlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedExpenseCost;
   @XmlElement(name = "SummaryPlannedFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryPlannedFinishDate;
   @XmlElement(name = "SummaryPlannedLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedLaborCost;
   @XmlElement(name = "SummaryPlannedLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedLaborUnits;
   @XmlElement(name = "SummaryPlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedMaterialCost;
   @XmlElement(name = "SummaryPlannedNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedNonLaborCost;
   @XmlElement(name = "SummaryPlannedNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedNonLaborUnits;
   @XmlElement(name = "SummaryPlannedStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryPlannedStartDate;
   @XmlElement(name = "SummaryPlannedValueByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedValueByCost;
   @XmlElement(name = "SummaryPlannedValueByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryPlannedValueByLaborUnits;
   @XmlElement(name = "SummaryProgressFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryProgressFinishDate;
   @XmlElement(name = "SummaryRemainingDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingDuration;
   @XmlElement(name = "SummaryRemainingExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingExpenseCost;
   @XmlElement(name = "SummaryRemainingFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryRemainingFinishDate;
   @XmlElement(name = "SummaryRemainingLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingLaborCost;
   @XmlElement(name = "SummaryRemainingLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingLaborUnits;
   @XmlElement(name = "SummaryRemainingMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingMaterialCost;
   @XmlElement(name = "SummaryRemainingNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingNonLaborCost;
   @XmlElement(name = "SummaryRemainingNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingNonLaborUnits;
   @XmlElement(name = "SummaryRemainingStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime summaryRemainingStartDate;
   @XmlElement(name = "SummaryRemainingTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryRemainingTotalCost;
   @XmlElement(name = "SummarySchedulePercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summarySchedulePercentComplete;
   @XmlElement(name = "SummarySchedulePercentCompleteByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summarySchedulePercentCompleteByCost;
   @XmlElement(name = "SummarySchedulePercentCompleteByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summarySchedulePercentCompleteByLaborUnits;
   @XmlElement(name = "SummarySchedulePerformanceIndexByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summarySchedulePerformanceIndexByCost;
   @XmlElement(name = "SummarySchedulePerformanceIndexByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summarySchedulePerformanceIndexByLaborUnits;
   @XmlElement(name = "SummaryScheduleVarianceByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryScheduleVarianceByCost;
   @XmlElement(name = "SummaryScheduleVarianceByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryScheduleVarianceByLaborUnits;
   @XmlElement(name = "SummaryScheduleVarianceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryScheduleVarianceIndex;
   @XmlElement(name = "SummaryScheduleVarianceIndexByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryScheduleVarianceIndexByCost;
   @XmlElement(name = "SummaryScheduleVarianceIndexByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryScheduleVarianceIndexByLaborUnits;
   @XmlElement(name = "SummaryStartDateVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryStartDateVariance;
   @XmlElement(name = "SummaryToCompletePerformanceIndexByCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryToCompletePerformanceIndexByCost;
   @XmlElement(name = "SummaryTotalCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryTotalCostVariance;
   @XmlElement(name = "SummaryTotalFloat", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryTotalFloat;
   @XmlElement(name = "SummaryUnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryUnitsPercentComplete;
   @XmlElement(name = "SummaryVarianceAtCompletionByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double summaryVarianceAtCompletionByLaborUnits;
   @XmlElement(name = "TeamMemberActivityFields") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberActivityFields;
   @XmlElement(name = "TeamMemberAssignmentOption") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberAssignmentOption;
   @XmlElement(name = "TeamMemberResourceAssignmentFields") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberResourceAssignmentFields;
   @XmlElement(name = "TeamMemberStepUDFViewableFields") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberStepUDFViewableFields;
   @XmlElement(name = "TeamMemberViewableFields") @XmlJavaTypeAdapter(Adapter1.class) protected String teamMemberViewableFields;
   @XmlElement(name = "TotalBenefitPlan", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalBenefitPlan;
   @XmlElement(name = "TotalBenefitPlanTally", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalBenefitPlanTally;
   @XmlElement(name = "TotalSpendingPlan", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalSpendingPlan;
   @XmlElement(name = "TotalSpendingPlanTally", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalSpendingPlanTally;
   @XmlElement(name = "UnallocatedBudget", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unallocatedBudget;
   @XmlElement(name = "UndistributedCurrentVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double undistributedCurrentVariance;
   @XmlElement(name = "WBSCategoryObjectId", nillable = true) protected Integer wbsCategoryObjectId;
   @XmlElement(name = "WBSCodeSeparator") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsCodeSeparator;
   @XmlElement(name = "WBSObjectId", nillable = true) protected Integer wbsObjectId;
   @XmlElement(name = "WebSiteRootDirectory") @XmlJavaTypeAdapter(Adapter1.class) protected String webSiteRootDirectory;
   @XmlElement(name = "WebSiteURL") @XmlJavaTypeAdapter(Adapter1.class) protected String webSiteURL;
   @XmlElement(name = "Code") protected List<CodeAssignmentType> code;
   @XmlElement(name = "UDF") protected List<UDFAssignmentType> udf;
   @XmlElement(name = "Spread") protected EPSProjectWBSSpreadType spread;
   @XmlElement(name = "ProjectResourceSpread") protected List<ProjectResourceSpreadType> projectResourceSpread;
   @XmlElement(name = "ProjectRoleSpread") protected List<ProjectRoleSpreadType> projectRoleSpread;
   @XmlElement(name = "Calendar") protected List<CalendarType> calendar;
   @XmlElement(name = "WBS") protected List<WBSType> wbs;
   @XmlElement(name = "ProjectResource") protected List<ProjectResourceType> projectResource;
   @XmlElement(name = "ProjectResourceQuantity") protected List<ProjectResourceQuantityType> projectResourceQuantity;
   @XmlElement(name = "ProjectBudgetChangeLog") protected List<ProjectBudgetChangeLogType> projectBudgetChangeLog;
   @XmlElement(name = "WBSMilestone") protected List<WBSMilestoneType> wbsMilestone;
   @XmlElement(name = "ProjectNote") protected List<ProjectNoteType> projectNote;
   @XmlElement(name = "ProjectThreshold") protected List<ProjectThresholdType> projectThreshold;
   @XmlElement(name = "ProjectSpendingPlan") protected List<ProjectSpendingPlanType> projectSpendingPlan;
   @XmlElement(name = "ProjectFunding") protected List<ProjectFundingType> projectFunding;
   @XmlElement(name = "ActivityCodeType") protected List<ActivityCodeTypeType> activityCodeType;
   @XmlElement(name = "ActivityCode") protected List<ActivityCodeType> activityCode;
   @XmlElement(name = "Activity") protected List<ActivityType> activity;
   @XmlElement(name = "ResourceAssignment") protected List<ResourceAssignmentType> resourceAssignment;
   @XmlElement(name = "ActivityExpense") protected List<ActivityExpenseType> activityExpense;
   @XmlElement(name = "ActivityNote") protected List<ActivityNoteType> activityNote;
   @XmlElement(name = "ActivityStep") protected List<ActivityStepType> activityStep;
   @XmlElement(name = "Relationship") protected List<RelationshipType> relationship;
   @XmlElement(name = "ActivityPeriodActual") protected List<ActivityPeriodActualType> activityPeriodActual;
   @XmlElement(name = "ProjectIssue") protected List<ProjectIssueType> projectIssue;
   @XmlElement(name = "ResourceAssignmentPeriodActual") protected List<ResourceAssignmentPeriodActualType> resourceAssignmentPeriodActual;
   @XmlElement(name = "Document") protected List<DocumentType> document;
   @XmlElement(name = "ProjectDocument") protected List<ProjectDocumentType> projectDocument;
   @XmlElement(name = "ScheduleOptions") protected List<ScheduleOptionsType> scheduleOptions;
   @XmlElement(name = "Risk") protected List<RiskType> risk;
   @XmlElement(name = "ActivityRisk") protected List<ActivityRiskType> activityRisk;
   @XmlElement(name = "RiskImpact") protected List<RiskImpactType> riskImpact;
   @XmlElement(name = "RiskResponsePlan") protected List<RiskResponsePlanType> riskResponsePlan;
   @XmlElement(name = "RiskResponseAction") protected List<RiskResponseActionType> riskResponseAction;
   @XmlElement(name = "RiskResponseActionImpact") protected List<RiskResponseActionImpactType> riskResponseActionImpact;

   /**
    * Gets the value of the activityDefaultActivityType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getActivityDefaultActivityType()
   {
      return activityDefaultActivityType;
   }

   /**
    * Sets the value of the activityDefaultActivityType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityDefaultActivityType(String value)
   {
      this.activityDefaultActivityType = value;
   }

   /**
    * Gets the value of the activityDefaultCalendarObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityDefaultCalendarObjectId()
   {
      return activityDefaultCalendarObjectId;
   }

   /**
    * Sets the value of the activityDefaultCalendarObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityDefaultCalendarObjectId(Integer value)
   {
      this.activityDefaultCalendarObjectId = value;
   }

   /**
    * Gets the value of the activityDefaultCostAccountObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityDefaultCostAccountObjectId()
   {
      return activityDefaultCostAccountObjectId;
   }

   /**
    * Sets the value of the activityDefaultCostAccountObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityDefaultCostAccountObjectId(Integer value)
   {
      this.activityDefaultCostAccountObjectId = value;
   }

   /**
    * Gets the value of the activityDefaultDurationType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getActivityDefaultDurationType()
   {
      return activityDefaultDurationType;
   }

   /**
    * Sets the value of the activityDefaultDurationType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityDefaultDurationType(String value)
   {
      this.activityDefaultDurationType = value;
   }

   /**
    * Gets the value of the activityDefaultPercentCompleteType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getActivityDefaultPercentCompleteType()
   {
      return activityDefaultPercentCompleteType;
   }

   /**
    * Sets the value of the activityDefaultPercentCompleteType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityDefaultPercentCompleteType(String value)
   {
      this.activityDefaultPercentCompleteType = value;
   }

   /**
    * Gets the value of the activityDefaultPricePerUnit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActivityDefaultPricePerUnit()
   {
      return activityDefaultPricePerUnit;
   }

   /**
    * Sets the value of the activityDefaultPricePerUnit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityDefaultPricePerUnit(Double value)
   {
      this.activityDefaultPricePerUnit = value;
   }

   /**
    * Gets the value of the activityDefaultReviewRequired property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isActivityDefaultReviewRequired()
   {
      return activityDefaultReviewRequired;
   }

   /**
    * Sets the value of the activityDefaultReviewRequired property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityDefaultReviewRequired(Boolean value)
   {
      this.activityDefaultReviewRequired = value;
   }

   /**
    * Gets the value of the activityIdBasedOnSelectedActivity property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isActivityIdBasedOnSelectedActivity()
   {
      return activityIdBasedOnSelectedActivity;
   }

   /**
    * Sets the value of the activityIdBasedOnSelectedActivity property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityIdBasedOnSelectedActivity(Boolean value)
   {
      this.activityIdBasedOnSelectedActivity = value;
   }

   /**
    * Gets the value of the activityIdIncrement property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityIdIncrement()
   {
      return activityIdIncrement;
   }

   /**
    * Sets the value of the activityIdIncrement property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityIdIncrement(Integer value)
   {
      this.activityIdIncrement = value;
   }

   /**
    * Gets the value of the activityIdPrefix property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getActivityIdPrefix()
   {
      return activityIdPrefix;
   }

   /**
    * Sets the value of the activityIdPrefix property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityIdPrefix(String value)
   {
      this.activityIdPrefix = value;
   }

   /**
    * Gets the value of the activityIdSuffix property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityIdSuffix()
   {
      return activityIdSuffix;
   }

   /**
    * Sets the value of the activityIdSuffix property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityIdSuffix(Integer value)
   {
      this.activityIdSuffix = value;
   }

   /**
    * Gets the value of the activityPercentCompleteBasedOnActivitySteps property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isActivityPercentCompleteBasedOnActivitySteps()
   {
      return activityPercentCompleteBasedOnActivitySteps;
   }

   /**
    * Sets the value of the activityPercentCompleteBasedOnActivitySteps property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActivityPercentCompleteBasedOnActivitySteps(Boolean value)
   {
      this.activityPercentCompleteBasedOnActivitySteps = value;
   }

   /**
    * Gets the value of the addActualToRemaining property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAddActualToRemaining()
   {
      return addActualToRemaining;
   }

   /**
    * Sets the value of the addActualToRemaining property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAddActualToRemaining(Boolean value)
   {
      this.addActualToRemaining = value;
   }

   /**
    * Gets the value of the addedBy property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getAddedBy()
   {
      return addedBy;
   }

   /**
    * Sets the value of the addedBy property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAddedBy(String value)
   {
      this.addedBy = value;
   }

   /**
    * Gets the value of the allowStatusReview property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAllowStatusReview()
   {
      return allowStatusReview;
   }

   /**
    * Sets the value of the allowStatusReview property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAllowStatusReview(Boolean value)
   {
      this.allowStatusReview = value;
   }

   /**
    * Gets the value of the annualDiscountRate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAnnualDiscountRate()
   {
      return annualDiscountRate;
   }

   /**
    * Sets the value of the annualDiscountRate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAnnualDiscountRate(Double value)
   {
      this.annualDiscountRate = value;
   }

   /**
    * Gets the value of the anticipatedFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getAnticipatedFinishDate()
   {
      return anticipatedFinishDate;
   }

   /**
    * Sets the value of the anticipatedFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAnticipatedFinishDate(LocalDateTime value)
   {
      this.anticipatedFinishDate = value;
   }

   /**
    * Gets the value of the anticipatedStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getAnticipatedStartDate()
   {
      return anticipatedStartDate;
   }

   /**
    * Sets the value of the anticipatedStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAnticipatedStartDate(LocalDateTime value)
   {
      this.anticipatedStartDate = value;
   }

   /**
    * Gets the value of the assignmentDefaultDrivingFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAssignmentDefaultDrivingFlag()
   {
      return assignmentDefaultDrivingFlag;
   }

   /**
    * Sets the value of the assignmentDefaultDrivingFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAssignmentDefaultDrivingFlag(Boolean value)
   {
      this.assignmentDefaultDrivingFlag = value;
   }

   /**
    * Gets the value of the assignmentDefaultRateType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getAssignmentDefaultRateType()
   {
      return assignmentDefaultRateType;
   }

   /**
    * Sets the value of the assignmentDefaultRateType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAssignmentDefaultRateType(String value)
   {
      this.assignmentDefaultRateType = value;
   }

   /**
    * Gets the value of the baselineTypeName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getBaselineTypeName()
   {
      return baselineTypeName;
   }

   /**
    * Sets the value of the baselineTypeName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselineTypeName(String value)
   {
      this.baselineTypeName = value;
   }

   /**
    * Gets the value of the baselineTypeObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getBaselineTypeObjectId()
   {
      return baselineTypeObjectId;
   }

   /**
    * Sets the value of the baselineTypeObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setBaselineTypeObjectId(Integer value)
   {
      this.baselineTypeObjectId = value;
   }

   /**
    * Gets the value of the checkOutDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCheckOutDate()
   {
      return checkOutDate;
   }

   /**
    * Sets the value of the checkOutDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCheckOutDate(LocalDateTime value)
   {
      this.checkOutDate = value;
   }

   /**
    * Gets the value of the checkOutStatus property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isCheckOutStatus()
   {
      return checkOutStatus;
   }

   /**
    * Sets the value of the checkOutStatus property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCheckOutStatus(Boolean value)
   {
      this.checkOutStatus = value;
   }

   /**
    * Gets the value of the checkOutUserObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCheckOutUserObjectId()
   {
      return checkOutUserObjectId;
   }

   /**
    * Sets the value of the checkOutUserObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCheckOutUserObjectId(Integer value)
   {
      this.checkOutUserObjectId = value;
   }

   /**
    * Gets the value of the containsSummaryData property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isContainsSummaryData()
   {
      return containsSummaryData;
   }

   /**
    * Sets the value of the containsSummaryData property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setContainsSummaryData(Boolean value)
   {
      this.containsSummaryData = value;
   }

   /**
    * Gets the value of the costQuantityRecalculateFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isCostQuantityRecalculateFlag()
   {
      return costQuantityRecalculateFlag;
   }

   /**
    * Sets the value of the costQuantityRecalculateFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostQuantityRecalculateFlag(Boolean value)
   {
      this.costQuantityRecalculateFlag = value;
   }

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the criticalActivityFloatLimit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCriticalActivityFloatLimit()
   {
      return criticalActivityFloatLimit;
   }

   /**
    * Sets the value of the criticalActivityFloatLimit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCriticalActivityFloatLimit(Double value)
   {
      this.criticalActivityFloatLimit = value;
   }

   /**
    * Gets the value of the criticalActivityPathType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCriticalActivityPathType()
   {
      return criticalActivityPathType;
   }

   /**
    * Sets the value of the criticalActivityPathType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCriticalActivityPathType(String value)
   {
      this.criticalActivityPathType = value;
   }

   /**
    * Gets the value of the currentBudget property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCurrentBudget()
   {
      return currentBudget;
   }

   /**
    * Sets the value of the currentBudget property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrentBudget(Double value)
   {
      this.currentBudget = value;
   }

   /**
    * Gets the value of the currentVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCurrentVariance()
   {
      return currentVariance;
   }

   /**
    * Sets the value of the currentVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCurrentVariance(Double value)
   {
      this.currentVariance = value;
   }

   /**
    * Gets the value of the dataDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getDataDate()
   {
      return dataDate;
   }

   /**
    * Sets the value of the dataDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDataDate(LocalDateTime value)
   {
      this.dataDate = value;
   }

   /**
    * Gets the value of the dateAdded property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getDateAdded()
   {
      return dateAdded;
   }

   /**
    * Sets the value of the dateAdded property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDateAdded(LocalDateTime value)
   {
      this.dateAdded = value;
   }

   /**
    * Gets the value of the defaultPriceTimeUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDefaultPriceTimeUnits()
   {
      return defaultPriceTimeUnits;
   }

   /**
    * Sets the value of the defaultPriceTimeUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDefaultPriceTimeUnits(String value)
   {
      this.defaultPriceTimeUnits = value;
   }

   /**
    * Gets the value of the description property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDescription()
   {
      return description;
   }

   /**
    * Sets the value of the description property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDescription(String value)
   {
      this.description = value;
   }

   /**
    * Gets the value of the discountApplicationPeriod property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDiscountApplicationPeriod()
   {
      return discountApplicationPeriod;
   }

   /**
    * Sets the value of the discountApplicationPeriod property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDiscountApplicationPeriod(String value)
   {
      this.discountApplicationPeriod = value;
   }

   /**
    * Gets the value of the distributedCurrentBudget property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDistributedCurrentBudget()
   {
      return distributedCurrentBudget;
   }

   /**
    * Sets the value of the distributedCurrentBudget property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDistributedCurrentBudget(Double value)
   {
      this.distributedCurrentBudget = value;
   }

   /**
    * Gets the value of the enablePublication property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnablePublication()
   {
      return enablePublication;
   }

   /**
    * Sets the value of the enablePublication property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnablePublication(Boolean value)
   {
      this.enablePublication = value;
   }

   /**
    * Gets the value of the enableSummarization property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isEnableSummarization()
   {
      return enableSummarization;
   }

   /**
    * Sets the value of the enableSummarization property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEnableSummarization(Boolean value)
   {
      this.enableSummarization = value;
   }

   /**
    * Gets the value of the financialPeriodTmplId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFinancialPeriodTmplId()
   {
      return financialPeriodTmplId;
   }

   /**
    * Sets the value of the financialPeriodTmplId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFinancialPeriodTmplId(Integer value)
   {
      this.financialPeriodTmplId = value;
   }

   /**
    * Gets the value of the finishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getFinishDate()
   {
      return finishDate;
   }

   /**
    * Sets the value of the finishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDate(LocalDateTime value)
   {
      this.finishDate = value;
   }

   /**
    * Gets the value of the fiscalYearStartMonth property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFiscalYearStartMonth()
   {
      return fiscalYearStartMonth;
   }

   /**
    * Sets the value of the fiscalYearStartMonth property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFiscalYearStartMonth(Integer value)
   {
      this.fiscalYearStartMonth = value;
   }

   /**
    * Gets the value of the forecastFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getForecastFinishDate()
   {
      return forecastFinishDate;
   }

   /**
    * Sets the value of the forecastFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setForecastFinishDate(LocalDateTime value)
   {
      this.forecastFinishDate = value;
   }

   /**
    * Gets the value of the forecastStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getForecastStartDate()
   {
      return forecastStartDate;
   }

   /**
    * Sets the value of the forecastStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setForecastStartDate(LocalDateTime value)
   {
      this.forecastStartDate = value;
   }

   /**
    * Gets the value of the guid property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGUID()
   {
      return guid;
   }

   /**
    * Sets the value of the guid property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGUID(String value)
   {
      this.guid = value;
   }

   /**
    * Gets the value of the hasFutureBucketData property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isHasFutureBucketData()
   {
      return hasFutureBucketData;
   }

   /**
    * Sets the value of the hasFutureBucketData property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHasFutureBucketData(Boolean value)
   {
      this.hasFutureBucketData = value;
   }

   /**
    * Gets the value of the historyInterval property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHistoryInterval()
   {
      return historyInterval;
   }

   /**
    * Sets the value of the historyInterval property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHistoryInterval(String value)
   {
      this.historyInterval = value;
   }

   /**
    * Gets the value of the historyLevel property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getHistoryLevel()
   {
      return historyLevel;
   }

   /**
    * Sets the value of the historyLevel property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHistoryLevel(String value)
   {
      this.historyLevel = value;
   }

   /**
    * Gets the value of the id property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getId()
   {
      return id;
   }

   /**
    * Sets the value of the id property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setId(String value)
   {
      this.id = value;
   }

   /**
    * Gets the value of the independentETCLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getIndependentETCLaborUnits()
   {
      return independentETCLaborUnits;
   }

   /**
    * Sets the value of the independentETCLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIndependentETCLaborUnits(Double value)
   {
      this.independentETCLaborUnits = value;
   }

   /**
    * Gets the value of the independentETCTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getIndependentETCTotalCost()
   {
      return independentETCTotalCost;
   }

   /**
    * Sets the value of the independentETCTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIndependentETCTotalCost(Double value)
   {
      this.independentETCTotalCost = value;
   }

   /**
    * Gets the value of the integratedWBS property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIntegratedWBS()
   {
      return integratedWBS;
   }

   /**
    * Sets the value of the integratedWBS property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIntegratedWBS(Boolean value)
   {
      this.integratedWBS = value;
   }

   /**
    * Gets the value of the lastBaselineUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastBaselineUpdateDate()
   {
      return lastBaselineUpdateDate;
   }

   /**
    * Sets the value of the lastBaselineUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastBaselineUpdateDate(LocalDateTime value)
   {
      this.lastBaselineUpdateDate = value;
   }

   /**
    * Gets the value of the lastFinancialPeriodObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getLastFinancialPeriodObjectId()
   {
      return lastFinancialPeriodObjectId;
   }

   /**
    * Sets the value of the lastFinancialPeriodObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setLastFinancialPeriodObjectId(Integer value)
   {
      this.lastFinancialPeriodObjectId = value;
   }

   /**
    * Gets the value of the lastLevelDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastLevelDate()
   {
      return lastLevelDate;
   }

   /**
    * Sets the value of the lastLevelDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastLevelDate(LocalDateTime value)
   {
      this.lastLevelDate = value;
   }

   /**
    * Gets the value of the lastPublishedOn property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastPublishedOn()
   {
      return lastPublishedOn;
   }

   /**
    * Sets the value of the lastPublishedOn property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastPublishedOn(LocalDateTime value)
   {
      this.lastPublishedOn = value;
   }

   /**
    * Gets the value of the lastScheduleDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastScheduleDate()
   {
      return lastScheduleDate;
   }

   /**
    * Sets the value of the lastScheduleDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastScheduleDate(LocalDateTime value)
   {
      this.lastScheduleDate = value;
   }

   /**
    * Gets the value of the lastSummarizedDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastSummarizedDate()
   {
      return lastSummarizedDate;
   }

   /**
    * Sets the value of the lastSummarizedDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastSummarizedDate(LocalDateTime value)
   {
      this.lastSummarizedDate = value;
   }

   /**
    * Gets the value of the lastUpdateBaselineOptions property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateBaselineOptions()
   {
      return lastUpdateBaselineOptions;
   }

   /**
    * Sets the value of the lastUpdateBaselineOptions property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateBaselineOptions(String value)
   {
      this.lastUpdateBaselineOptions = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the levelingPriority property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getLevelingPriority()
   {
      return levelingPriority;
   }

   /**
    * Sets the value of the levelingPriority property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setLevelingPriority(Integer value)
   {
      this.levelingPriority = value;
   }

   /**
    * Gets the value of the linkActualToActualThisPeriod property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLinkActualToActualThisPeriod()
   {
      return linkActualToActualThisPeriod;
   }

   /**
    * Sets the value of the linkActualToActualThisPeriod property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLinkActualToActualThisPeriod(Boolean value)
   {
      this.linkActualToActualThisPeriod = value;
   }

   /**
    * Gets the value of the linkPercentCompleteWithActual property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLinkPercentCompleteWithActual()
   {
      return linkPercentCompleteWithActual;
   }

   /**
    * Sets the value of the linkPercentCompleteWithActual property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLinkPercentCompleteWithActual(Boolean value)
   {
      this.linkPercentCompleteWithActual = value;
   }

   /**
    * Gets the value of the linkPlannedAndAtCompletionFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isLinkPlannedAndAtCompletionFlag()
   {
      return linkPlannedAndAtCompletionFlag;
   }

   /**
    * Sets the value of the linkPlannedAndAtCompletionFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLinkPlannedAndAtCompletionFlag(Boolean value)
   {
      this.linkPlannedAndAtCompletionFlag = value;
   }

   /**
    * Gets the value of the locationName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLocationName()
   {
      return locationName;
   }

   /**
    * Sets the value of the locationName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLocationName(String value)
   {
      this.locationName = value;
   }

   /**
    * Gets the value of the locationObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getLocationObjectId()
   {
      return locationObjectId;
   }

   /**
    * Sets the value of the locationObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setLocationObjectId(Integer value)
   {
      this.locationObjectId = value;
   }

   /**
    * Gets the value of the mustFinishByDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getMustFinishByDate()
   {
      return mustFinishByDate;
   }

   /**
    * Sets the value of the mustFinishByDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMustFinishByDate(LocalDateTime value)
   {
      this.mustFinishByDate = value;
   }

   /**
    * Gets the value of the name property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getName()
   {
      return name;
   }

   /**
    * Sets the value of the name property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setName(String value)
   {
      this.name = value;
   }

   /**
    * Gets the value of the obsName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOBSName()
   {
      return obsName;
   }

   /**
    * Sets the value of the obsName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOBSName(String value)
   {
      this.obsName = value;
   }

   /**
    * Gets the value of the obsObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getOBSObjectId()
   {
      return obsObjectId;
   }

   /**
    * Sets the value of the obsObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setOBSObjectId(Integer value)
   {
      this.obsObjectId = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the originalBudget property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getOriginalBudget()
   {
      return originalBudget;
   }

   /**
    * Sets the value of the originalBudget property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOriginalBudget(Double value)
   {
      this.originalBudget = value;
   }

   /**
    * Gets the value of the originalProjectObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getOriginalProjectObjectId()
   {
      return originalProjectObjectId;
   }

   /**
    * Sets the value of the originalProjectObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setOriginalProjectObjectId(Integer value)
   {
      this.originalProjectObjectId = value;
   }

   /**
    * Gets the value of the ownerResourceObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getOwnerResourceObjectId()
   {
      return ownerResourceObjectId;
   }

   /**
    * Sets the value of the ownerResourceObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setOwnerResourceObjectId(Integer value)
   {
      this.ownerResourceObjectId = value;
   }

   /**
    * Gets the value of the parentEPSId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getParentEPSId()
   {
      return parentEPSId;
   }

   /**
    * Sets the value of the parentEPSId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setParentEPSId(String value)
   {
      this.parentEPSId = value;
   }

   /**
    * Gets the value of the parentEPSName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getParentEPSName()
   {
      return parentEPSName;
   }

   /**
    * Sets the value of the parentEPSName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setParentEPSName(String value)
   {
      this.parentEPSName = value;
   }

   /**
    * Gets the value of the parentEPSObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getParentEPSObjectId()
   {
      return parentEPSObjectId;
   }

   /**
    * Sets the value of the parentEPSObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setParentEPSObjectId(Integer value)
   {
      this.parentEPSObjectId = value;
   }

   /**
    * Gets the value of the plannedStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPlannedStartDate()
   {
      return plannedStartDate;
   }

   /**
    * Sets the value of the plannedStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedStartDate(LocalDateTime value)
   {
      this.plannedStartDate = value;
   }

   /**
    * Gets the value of the primaryResourcesCanMarkActivitiesAsCompleted property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isPrimaryResourcesCanMarkActivitiesAsCompleted()
   {
      return primaryResourcesCanMarkActivitiesAsCompleted;
   }

   /**
    * Sets the value of the primaryResourcesCanMarkActivitiesAsCompleted property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrimaryResourcesCanMarkActivitiesAsCompleted(Boolean value)
   {
      this.primaryResourcesCanMarkActivitiesAsCompleted = value;
   }

   /**
    * Gets the value of the projectForecastStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getProjectForecastStartDate()
   {
      return projectForecastStartDate;
   }

   /**
    * Sets the value of the projectForecastStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectForecastStartDate(LocalDateTime value)
   {
      this.projectForecastStartDate = value;
   }

   /**
    * Gets the value of the projectScheduleType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectScheduleType()
   {
      return projectScheduleType;
   }

   /**
    * Sets the value of the projectScheduleType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectScheduleType(String value)
   {
      this.projectScheduleType = value;
   }

   /**
    * Gets the value of the proposedBudget property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getProposedBudget()
   {
      return proposedBudget;
   }

   /**
    * Sets the value of the proposedBudget property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProposedBudget(Double value)
   {
      this.proposedBudget = value;
   }

   /**
    * Gets the value of the publicationPriority property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getPublicationPriority()
   {
      return publicationPriority;
   }

   /**
    * Sets the value of the publicationPriority property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setPublicationPriority(Integer value)
   {
      this.publicationPriority = value;
   }

   /**
    * Gets the value of the resetPlannedToRemainingFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResetPlannedToRemainingFlag()
   {
      return resetPlannedToRemainingFlag;
   }

   /**
    * Sets the value of the resetPlannedToRemainingFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResetPlannedToRemainingFlag(Boolean value)
   {
      this.resetPlannedToRemainingFlag = value;
   }

   /**
    * Gets the value of the resourceCanBeAssignedToSameActivityMoreThanOnce property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourceCanBeAssignedToSameActivityMoreThanOnce()
   {
      return resourceCanBeAssignedToSameActivityMoreThanOnce;
   }

   /**
    * Sets the value of the resourceCanBeAssignedToSameActivityMoreThanOnce property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourceCanBeAssignedToSameActivityMoreThanOnce(Boolean value)
   {
      this.resourceCanBeAssignedToSameActivityMoreThanOnce = value;
   }

   /**
    * Gets the value of the resourceName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getResourceName()
   {
      return resourceName;
   }

   /**
    * Sets the value of the resourceName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourceName(String value)
   {
      this.resourceName = value;
   }

   /**
    * Gets the value of the resourcesCanAssignThemselvesToActivities property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanAssignThemselvesToActivities()
   {
      return resourcesCanAssignThemselvesToActivities;
   }

   /**
    * Sets the value of the resourcesCanAssignThemselvesToActivities property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanAssignThemselvesToActivities(Boolean value)
   {
      this.resourcesCanAssignThemselvesToActivities = value;
   }

   /**
    * Gets the value of the resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanAssignThemselvesToActivitiesOutsideOBSAccess()
   {
      return resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess;
   }

   /**
    * Sets the value of the resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanAssignThemselvesToActivitiesOutsideOBSAccess(Boolean value)
   {
      this.resourcesCanAssignThemselvesToActivitiesOutsideOBSAccess = value;
   }

   /**
    * Gets the value of the resourcesCanEditAssignmentPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanEditAssignmentPercentComplete()
   {
      return resourcesCanEditAssignmentPercentComplete;
   }

   /**
    * Sets the value of the resourcesCanEditAssignmentPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanEditAssignmentPercentComplete(Boolean value)
   {
      this.resourcesCanEditAssignmentPercentComplete = value;
   }

   /**
    * Gets the value of the resourcesCanStaffRoleAssignment property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isResourcesCanStaffRoleAssignment()
   {
      return resourcesCanStaffRoleAssignment;
   }

   /**
    * Sets the value of the resourcesCanStaffRoleAssignment property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResourcesCanStaffRoleAssignment(Boolean value)
   {
      this.resourcesCanStaffRoleAssignment = value;
   }

   /**
    * Gets the value of the riskExposure property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRiskExposure()
   {
      return riskExposure;
   }

   /**
    * Sets the value of the riskExposure property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRiskExposure(Double value)
   {
      this.riskExposure = value;
   }

   /**
    * Gets the value of the riskLevel property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRiskLevel()
   {
      return riskLevel;
   }

   /**
    * Sets the value of the riskLevel property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRiskLevel(String value)
   {
      this.riskLevel = value;
   }

   /**
    * Gets the value of the riskMatrixObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getRiskMatrixObjectId()
   {
      return riskMatrixObjectId;
   }

   /**
    * Sets the value of the riskMatrixObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setRiskMatrixObjectId(Integer value)
   {
      this.riskMatrixObjectId = value;
   }

   /**
    * Gets the value of the riskScore property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getRiskScore()
   {
      return riskScore;
   }

   /**
    * Sets the value of the riskScore property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setRiskScore(Integer value)
   {
      this.riskScore = value;
   }

   /**
    * Gets the value of the scheduledFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getScheduledFinishDate()
   {
      return scheduledFinishDate;
   }

   /**
    * Sets the value of the scheduledFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScheduledFinishDate(LocalDateTime value)
   {
      this.scheduledFinishDate = value;
   }

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the status property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getStatus()
   {
      return status;
   }

   /**
    * Sets the value of the status property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStatus(String value)
   {
      this.status = value;
   }

   /**
    * Gets the value of the statusReviewerName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getStatusReviewerName()
   {
      return statusReviewerName;
   }

   /**
    * Sets the value of the statusReviewerName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStatusReviewerName(String value)
   {
      this.statusReviewerName = value;
   }

   /**
    * Gets the value of the statusReviewerObjectId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getStatusReviewerObjectId()
   {
      return statusReviewerObjectId;
   }

   /**
    * Sets the value of the statusReviewerObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStatusReviewerObjectId(String value)
   {
      this.statusReviewerObjectId = value;
   }

   /**
    * Gets the value of the strategicPriority property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getStrategicPriority()
   {
      return strategicPriority;
   }

   /**
    * Sets the value of the strategicPriority property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setStrategicPriority(Integer value)
   {
      this.strategicPriority = value;
   }

   /**
    * Gets the value of the summarizeToWBSLevel property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummarizeToWBSLevel()
   {
      return summarizeToWBSLevel;
   }

   /**
    * Sets the value of the summarizeToWBSLevel property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummarizeToWBSLevel(Integer value)
   {
      this.summarizeToWBSLevel = value;
   }

   /**
    * Gets the value of the summarizedDataDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummarizedDataDate()
   {
      return summarizedDataDate;
   }

   /**
    * Sets the value of the summarizedDataDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarizedDataDate(LocalDateTime value)
   {
      this.summarizedDataDate = value;
   }

   /**
    * Gets the value of the summaryAccountingVarianceByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAccountingVarianceByCost()
   {
      return summaryAccountingVarianceByCost;
   }

   /**
    * Sets the value of the summaryAccountingVarianceByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAccountingVarianceByCost(Double value)
   {
      this.summaryAccountingVarianceByCost = value;
   }

   /**
    * Gets the value of the summaryAccountingVarianceByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAccountingVarianceByLaborUnits()
   {
      return summaryAccountingVarianceByLaborUnits;
   }

   /**
    * Sets the value of the summaryAccountingVarianceByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAccountingVarianceByLaborUnits(Double value)
   {
      this.summaryAccountingVarianceByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryActivityCount()
   {
      return summaryActivityCount;
   }

   /**
    * Sets the value of the summaryActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryActivityCount(Integer value)
   {
      this.summaryActivityCount = value;
   }

   /**
    * Gets the value of the summaryActualDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualDuration()
   {
      return summaryActualDuration;
   }

   /**
    * Sets the value of the summaryActualDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualDuration(Double value)
   {
      this.summaryActualDuration = value;
   }

   /**
    * Gets the value of the summaryActualExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualExpenseCost()
   {
      return summaryActualExpenseCost;
   }

   /**
    * Sets the value of the summaryActualExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualExpenseCost(Double value)
   {
      this.summaryActualExpenseCost = value;
   }

   /**
    * Gets the value of the summaryActualFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryActualFinishDate()
   {
      return summaryActualFinishDate;
   }

   /**
    * Sets the value of the summaryActualFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualFinishDate(LocalDateTime value)
   {
      this.summaryActualFinishDate = value;
   }

   /**
    * Gets the value of the summaryActualLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualLaborCost()
   {
      return summaryActualLaborCost;
   }

   /**
    * Sets the value of the summaryActualLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualLaborCost(Double value)
   {
      this.summaryActualLaborCost = value;
   }

   /**
    * Gets the value of the summaryActualLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualLaborUnits()
   {
      return summaryActualLaborUnits;
   }

   /**
    * Sets the value of the summaryActualLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualLaborUnits(Double value)
   {
      this.summaryActualLaborUnits = value;
   }

   /**
    * Gets the value of the summaryActualMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualMaterialCost()
   {
      return summaryActualMaterialCost;
   }

   /**
    * Sets the value of the summaryActualMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualMaterialCost(Double value)
   {
      this.summaryActualMaterialCost = value;
   }

   /**
    * Gets the value of the summaryActualNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualNonLaborCost()
   {
      return summaryActualNonLaborCost;
   }

   /**
    * Sets the value of the summaryActualNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualNonLaborCost(Double value)
   {
      this.summaryActualNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryActualNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualNonLaborUnits()
   {
      return summaryActualNonLaborUnits;
   }

   /**
    * Sets the value of the summaryActualNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualNonLaborUnits(Double value)
   {
      this.summaryActualNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryActualStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryActualStartDate()
   {
      return summaryActualStartDate;
   }

   /**
    * Sets the value of the summaryActualStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualStartDate(LocalDateTime value)
   {
      this.summaryActualStartDate = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodCost()
   {
      return summaryActualThisPeriodCost;
   }

   /**
    * Sets the value of the summaryActualThisPeriodCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodCost(Double value)
   {
      this.summaryActualThisPeriodCost = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodLaborCost()
   {
      return summaryActualThisPeriodLaborCost;
   }

   /**
    * Sets the value of the summaryActualThisPeriodLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodLaborCost(Double value)
   {
      this.summaryActualThisPeriodLaborCost = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodLaborUnits()
   {
      return summaryActualThisPeriodLaborUnits;
   }

   /**
    * Sets the value of the summaryActualThisPeriodLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodLaborUnits(Double value)
   {
      this.summaryActualThisPeriodLaborUnits = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodMaterialCost()
   {
      return summaryActualThisPeriodMaterialCost;
   }

   /**
    * Sets the value of the summaryActualThisPeriodMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodMaterialCost(Double value)
   {
      this.summaryActualThisPeriodMaterialCost = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodNonLaborCost()
   {
      return summaryActualThisPeriodNonLaborCost;
   }

   /**
    * Sets the value of the summaryActualThisPeriodNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodNonLaborCost(Double value)
   {
      this.summaryActualThisPeriodNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryActualThisPeriodNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualThisPeriodNonLaborUnits()
   {
      return summaryActualThisPeriodNonLaborUnits;
   }

   /**
    * Sets the value of the summaryActualThisPeriodNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualThisPeriodNonLaborUnits(Double value)
   {
      this.summaryActualThisPeriodNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryActualTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualTotalCost()
   {
      return summaryActualTotalCost;
   }

   /**
    * Sets the value of the summaryActualTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualTotalCost(Double value)
   {
      this.summaryActualTotalCost = value;
   }

   /**
    * Gets the value of the summaryActualValueByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualValueByCost()
   {
      return summaryActualValueByCost;
   }

   /**
    * Sets the value of the summaryActualValueByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualValueByCost(Double value)
   {
      this.summaryActualValueByCost = value;
   }

   /**
    * Gets the value of the summaryActualValueByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryActualValueByLaborUnits()
   {
      return summaryActualValueByLaborUnits;
   }

   /**
    * Sets the value of the summaryActualValueByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryActualValueByLaborUnits(Double value)
   {
      this.summaryActualValueByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryAtCompletionDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionDuration()
   {
      return summaryAtCompletionDuration;
   }

   /**
    * Sets the value of the summaryAtCompletionDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionDuration(Double value)
   {
      this.summaryAtCompletionDuration = value;
   }

   /**
    * Gets the value of the summaryAtCompletionExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionExpenseCost()
   {
      return summaryAtCompletionExpenseCost;
   }

   /**
    * Sets the value of the summaryAtCompletionExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionExpenseCost(Double value)
   {
      this.summaryAtCompletionExpenseCost = value;
   }

   /**
    * Gets the value of the summaryAtCompletionLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionLaborCost()
   {
      return summaryAtCompletionLaborCost;
   }

   /**
    * Sets the value of the summaryAtCompletionLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionLaborCost(Double value)
   {
      this.summaryAtCompletionLaborCost = value;
   }

   /**
    * Gets the value of the summaryAtCompletionLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionLaborUnits()
   {
      return summaryAtCompletionLaborUnits;
   }

   /**
    * Sets the value of the summaryAtCompletionLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionLaborUnits(Double value)
   {
      this.summaryAtCompletionLaborUnits = value;
   }

   /**
    * Gets the value of the summaryAtCompletionMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionMaterialCost()
   {
      return summaryAtCompletionMaterialCost;
   }

   /**
    * Sets the value of the summaryAtCompletionMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionMaterialCost(Double value)
   {
      this.summaryAtCompletionMaterialCost = value;
   }

   /**
    * Gets the value of the summaryAtCompletionNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionNonLaborCost()
   {
      return summaryAtCompletionNonLaborCost;
   }

   /**
    * Sets the value of the summaryAtCompletionNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionNonLaborCost(Double value)
   {
      this.summaryAtCompletionNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryAtCompletionNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionNonLaborUnits()
   {
      return summaryAtCompletionNonLaborUnits;
   }

   /**
    * Sets the value of the summaryAtCompletionNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionNonLaborUnits(Double value)
   {
      this.summaryAtCompletionNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryAtCompletionTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionTotalCost()
   {
      return summaryAtCompletionTotalCost;
   }

   /**
    * Sets the value of the summaryAtCompletionTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionTotalCost(Double value)
   {
      this.summaryAtCompletionTotalCost = value;
   }

   /**
    * Gets the value of the summaryAtCompletionTotalCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryAtCompletionTotalCostVariance()
   {
      return summaryAtCompletionTotalCostVariance;
   }

   /**
    * Sets the value of the summaryAtCompletionTotalCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryAtCompletionTotalCostVariance(Double value)
   {
      this.summaryAtCompletionTotalCostVariance = value;
   }

   /**
    * Gets the value of the summaryBaselineCompletedActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryBaselineCompletedActivityCount()
   {
      return summaryBaselineCompletedActivityCount;
   }

   /**
    * Sets the value of the summaryBaselineCompletedActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryBaselineCompletedActivityCount(Integer value)
   {
      this.summaryBaselineCompletedActivityCount = value;
   }

   /**
    * Gets the value of the summaryBaselineDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineDuration()
   {
      return summaryBaselineDuration;
   }

   /**
    * Sets the value of the summaryBaselineDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineDuration(Double value)
   {
      this.summaryBaselineDuration = value;
   }

   /**
    * Gets the value of the summaryBaselineExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineExpenseCost()
   {
      return summaryBaselineExpenseCost;
   }

   /**
    * Sets the value of the summaryBaselineExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineExpenseCost(Double value)
   {
      this.summaryBaselineExpenseCost = value;
   }

   /**
    * Gets the value of the summaryBaselineFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryBaselineFinishDate()
   {
      return summaryBaselineFinishDate;
   }

   /**
    * Sets the value of the summaryBaselineFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineFinishDate(LocalDateTime value)
   {
      this.summaryBaselineFinishDate = value;
   }

   /**
    * Gets the value of the summaryBaselineInProgressActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryBaselineInProgressActivityCount()
   {
      return summaryBaselineInProgressActivityCount;
   }

   /**
    * Sets the value of the summaryBaselineInProgressActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryBaselineInProgressActivityCount(Integer value)
   {
      this.summaryBaselineInProgressActivityCount = value;
   }

   /**
    * Gets the value of the summaryBaselineLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineLaborCost()
   {
      return summaryBaselineLaborCost;
   }

   /**
    * Sets the value of the summaryBaselineLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineLaborCost(Double value)
   {
      this.summaryBaselineLaborCost = value;
   }

   /**
    * Gets the value of the summaryBaselineLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineLaborUnits()
   {
      return summaryBaselineLaborUnits;
   }

   /**
    * Sets the value of the summaryBaselineLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineLaborUnits(Double value)
   {
      this.summaryBaselineLaborUnits = value;
   }

   /**
    * Gets the value of the summaryBaselineMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineMaterialCost()
   {
      return summaryBaselineMaterialCost;
   }

   /**
    * Sets the value of the summaryBaselineMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineMaterialCost(Double value)
   {
      this.summaryBaselineMaterialCost = value;
   }

   /**
    * Gets the value of the summaryBaselineNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineNonLaborCost()
   {
      return summaryBaselineNonLaborCost;
   }

   /**
    * Sets the value of the summaryBaselineNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineNonLaborCost(Double value)
   {
      this.summaryBaselineNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryBaselineNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineNonLaborUnits()
   {
      return summaryBaselineNonLaborUnits;
   }

   /**
    * Sets the value of the summaryBaselineNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineNonLaborUnits(Double value)
   {
      this.summaryBaselineNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryBaselineNotStartedActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryBaselineNotStartedActivityCount()
   {
      return summaryBaselineNotStartedActivityCount;
   }

   /**
    * Sets the value of the summaryBaselineNotStartedActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryBaselineNotStartedActivityCount(Integer value)
   {
      this.summaryBaselineNotStartedActivityCount = value;
   }

   /**
    * Gets the value of the summaryBaselineStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryBaselineStartDate()
   {
      return summaryBaselineStartDate;
   }

   /**
    * Sets the value of the summaryBaselineStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineStartDate(LocalDateTime value)
   {
      this.summaryBaselineStartDate = value;
   }

   /**
    * Gets the value of the summaryBaselineTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBaselineTotalCost()
   {
      return summaryBaselineTotalCost;
   }

   /**
    * Sets the value of the summaryBaselineTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBaselineTotalCost(Double value)
   {
      this.summaryBaselineTotalCost = value;
   }

   /**
    * Gets the value of the summaryBudgetAtCompletionByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBudgetAtCompletionByCost()
   {
      return summaryBudgetAtCompletionByCost;
   }

   /**
    * Sets the value of the summaryBudgetAtCompletionByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBudgetAtCompletionByCost(Double value)
   {
      this.summaryBudgetAtCompletionByCost = value;
   }

   /**
    * Gets the value of the summaryBudgetAtCompletionByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryBudgetAtCompletionByLaborUnits()
   {
      return summaryBudgetAtCompletionByLaborUnits;
   }

   /**
    * Sets the value of the summaryBudgetAtCompletionByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryBudgetAtCompletionByLaborUnits(Double value)
   {
      this.summaryBudgetAtCompletionByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryCompletedActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryCompletedActivityCount()
   {
      return summaryCompletedActivityCount;
   }

   /**
    * Sets the value of the summaryCompletedActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryCompletedActivityCount(Integer value)
   {
      this.summaryCompletedActivityCount = value;
   }

   /**
    * Gets the value of the summaryCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostPercentComplete()
   {
      return summaryCostPercentComplete;
   }

   /**
    * Sets the value of the summaryCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostPercentComplete(Double value)
   {
      this.summaryCostPercentComplete = value;
   }

   /**
    * Gets the value of the summaryCostPercentOfPlanned property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostPercentOfPlanned()
   {
      return summaryCostPercentOfPlanned;
   }

   /**
    * Sets the value of the summaryCostPercentOfPlanned property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostPercentOfPlanned(Double value)
   {
      this.summaryCostPercentOfPlanned = value;
   }

   /**
    * Gets the value of the summaryCostPerformanceIndexByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostPerformanceIndexByCost()
   {
      return summaryCostPerformanceIndexByCost;
   }

   /**
    * Sets the value of the summaryCostPerformanceIndexByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostPerformanceIndexByCost(Double value)
   {
      this.summaryCostPerformanceIndexByCost = value;
   }

   /**
    * Gets the value of the summaryCostPerformanceIndexByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostPerformanceIndexByLaborUnits()
   {
      return summaryCostPerformanceIndexByLaborUnits;
   }

   /**
    * Sets the value of the summaryCostPerformanceIndexByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostPerformanceIndexByLaborUnits(Double value)
   {
      this.summaryCostPerformanceIndexByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryCostVarianceByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostVarianceByCost()
   {
      return summaryCostVarianceByCost;
   }

   /**
    * Sets the value of the summaryCostVarianceByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostVarianceByCost(Double value)
   {
      this.summaryCostVarianceByCost = value;
   }

   /**
    * Gets the value of the summaryCostVarianceByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostVarianceByLaborUnits()
   {
      return summaryCostVarianceByLaborUnits;
   }

   /**
    * Sets the value of the summaryCostVarianceByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostVarianceByLaborUnits(Double value)
   {
      this.summaryCostVarianceByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryCostVarianceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostVarianceIndex()
   {
      return summaryCostVarianceIndex;
   }

   /**
    * Sets the value of the summaryCostVarianceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostVarianceIndex(Double value)
   {
      this.summaryCostVarianceIndex = value;
   }

   /**
    * Gets the value of the summaryCostVarianceIndexByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostVarianceIndexByCost()
   {
      return summaryCostVarianceIndexByCost;
   }

   /**
    * Sets the value of the summaryCostVarianceIndexByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostVarianceIndexByCost(Double value)
   {
      this.summaryCostVarianceIndexByCost = value;
   }

   /**
    * Gets the value of the summaryCostVarianceIndexByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryCostVarianceIndexByLaborUnits()
   {
      return summaryCostVarianceIndexByLaborUnits;
   }

   /**
    * Sets the value of the summaryCostVarianceIndexByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryCostVarianceIndexByLaborUnits(Double value)
   {
      this.summaryCostVarianceIndexByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryDurationPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryDurationPercentComplete()
   {
      return summaryDurationPercentComplete;
   }

   /**
    * Sets the value of the summaryDurationPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryDurationPercentComplete(Double value)
   {
      this.summaryDurationPercentComplete = value;
   }

   /**
    * Gets the value of the summaryDurationPercentOfPlanned property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryDurationPercentOfPlanned()
   {
      return summaryDurationPercentOfPlanned;
   }

   /**
    * Sets the value of the summaryDurationPercentOfPlanned property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryDurationPercentOfPlanned(Double value)
   {
      this.summaryDurationPercentOfPlanned = value;
   }

   /**
    * Gets the value of the summaryDurationVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryDurationVariance()
   {
      return summaryDurationVariance;
   }

   /**
    * Sets the value of the summaryDurationVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryDurationVariance(Double value)
   {
      this.summaryDurationVariance = value;
   }

   /**
    * Gets the value of the summaryEarnedValueByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEarnedValueByCost()
   {
      return summaryEarnedValueByCost;
   }

   /**
    * Sets the value of the summaryEarnedValueByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEarnedValueByCost(Double value)
   {
      this.summaryEarnedValueByCost = value;
   }

   /**
    * Gets the value of the summaryEarnedValueByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEarnedValueByLaborUnits()
   {
      return summaryEarnedValueByLaborUnits;
   }

   /**
    * Sets the value of the summaryEarnedValueByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEarnedValueByLaborUnits(Double value)
   {
      this.summaryEarnedValueByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryEstimateAtCompletionByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateAtCompletionByCost()
   {
      return summaryEstimateAtCompletionByCost;
   }

   /**
    * Sets the value of the summaryEstimateAtCompletionByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateAtCompletionByCost(Double value)
   {
      this.summaryEstimateAtCompletionByCost = value;
   }

   /**
    * Gets the value of the summaryEstimateAtCompletionByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateAtCompletionByLaborUnits()
   {
      return summaryEstimateAtCompletionByLaborUnits;
   }

   /**
    * Sets the value of the summaryEstimateAtCompletionByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateAtCompletionByLaborUnits(Double value)
   {
      this.summaryEstimateAtCompletionByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryEstimateAtCompletionHighPercentByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateAtCompletionHighPercentByLaborUnits()
   {
      return summaryEstimateAtCompletionHighPercentByLaborUnits;
   }

   /**
    * Sets the value of the summaryEstimateAtCompletionHighPercentByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateAtCompletionHighPercentByLaborUnits(Double value)
   {
      this.summaryEstimateAtCompletionHighPercentByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryEstimateAtCompletionLowPercentByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateAtCompletionLowPercentByLaborUnits()
   {
      return summaryEstimateAtCompletionLowPercentByLaborUnits;
   }

   /**
    * Sets the value of the summaryEstimateAtCompletionLowPercentByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateAtCompletionLowPercentByLaborUnits(Double value)
   {
      this.summaryEstimateAtCompletionLowPercentByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryEstimateToCompleteByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateToCompleteByCost()
   {
      return summaryEstimateToCompleteByCost;
   }

   /**
    * Sets the value of the summaryEstimateToCompleteByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateToCompleteByCost(Double value)
   {
      this.summaryEstimateToCompleteByCost = value;
   }

   /**
    * Gets the value of the summaryEstimateToCompleteByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryEstimateToCompleteByLaborUnits()
   {
      return summaryEstimateToCompleteByLaborUnits;
   }

   /**
    * Sets the value of the summaryEstimateToCompleteByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryEstimateToCompleteByLaborUnits(Double value)
   {
      this.summaryEstimateToCompleteByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryExpenseCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryExpenseCostPercentComplete()
   {
      return summaryExpenseCostPercentComplete;
   }

   /**
    * Sets the value of the summaryExpenseCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryExpenseCostPercentComplete(Double value)
   {
      this.summaryExpenseCostPercentComplete = value;
   }

   /**
    * Gets the value of the summaryExpenseCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryExpenseCostVariance()
   {
      return summaryExpenseCostVariance;
   }

   /**
    * Sets the value of the summaryExpenseCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryExpenseCostVariance(Double value)
   {
      this.summaryExpenseCostVariance = value;
   }

   /**
    * Gets the value of the summaryFinishDateVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryFinishDateVariance()
   {
      return summaryFinishDateVariance;
   }

   /**
    * Sets the value of the summaryFinishDateVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryFinishDateVariance(Double value)
   {
      this.summaryFinishDateVariance = value;
   }

   /**
    * Gets the value of the summaryInProgressActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryInProgressActivityCount()
   {
      return summaryInProgressActivityCount;
   }

   /**
    * Sets the value of the summaryInProgressActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryInProgressActivityCount(Integer value)
   {
      this.summaryInProgressActivityCount = value;
   }

   /**
    * Gets the value of the summaryLaborCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryLaborCostPercentComplete()
   {
      return summaryLaborCostPercentComplete;
   }

   /**
    * Sets the value of the summaryLaborCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryLaborCostPercentComplete(Double value)
   {
      this.summaryLaborCostPercentComplete = value;
   }

   /**
    * Gets the value of the summaryLaborCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryLaborCostVariance()
   {
      return summaryLaborCostVariance;
   }

   /**
    * Sets the value of the summaryLaborCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryLaborCostVariance(Double value)
   {
      this.summaryLaborCostVariance = value;
   }

   /**
    * Gets the value of the summaryLaborUnitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryLaborUnitsPercentComplete()
   {
      return summaryLaborUnitsPercentComplete;
   }

   /**
    * Sets the value of the summaryLaborUnitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryLaborUnitsPercentComplete(Double value)
   {
      this.summaryLaborUnitsPercentComplete = value;
   }

   /**
    * Gets the value of the summaryLaborUnitsVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryLaborUnitsVariance()
   {
      return summaryLaborUnitsVariance;
   }

   /**
    * Sets the value of the summaryLaborUnitsVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryLaborUnitsVariance(Double value)
   {
      this.summaryLaborUnitsVariance = value;
   }

   /**
    * Gets the value of the summaryMaterialCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryMaterialCostPercentComplete()
   {
      return summaryMaterialCostPercentComplete;
   }

   /**
    * Sets the value of the summaryMaterialCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryMaterialCostPercentComplete(Double value)
   {
      this.summaryMaterialCostPercentComplete = value;
   }

   /**
    * Gets the value of the summaryMaterialCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryMaterialCostVariance()
   {
      return summaryMaterialCostVariance;
   }

   /**
    * Sets the value of the summaryMaterialCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryMaterialCostVariance(Double value)
   {
      this.summaryMaterialCostVariance = value;
   }

   /**
    * Gets the value of the summaryNonLaborCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryNonLaborCostPercentComplete()
   {
      return summaryNonLaborCostPercentComplete;
   }

   /**
    * Sets the value of the summaryNonLaborCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryNonLaborCostPercentComplete(Double value)
   {
      this.summaryNonLaborCostPercentComplete = value;
   }

   /**
    * Gets the value of the summaryNonLaborCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryNonLaborCostVariance()
   {
      return summaryNonLaborCostVariance;
   }

   /**
    * Sets the value of the summaryNonLaborCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryNonLaborCostVariance(Double value)
   {
      this.summaryNonLaborCostVariance = value;
   }

   /**
    * Gets the value of the summaryNonLaborUnitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryNonLaborUnitsPercentComplete()
   {
      return summaryNonLaborUnitsPercentComplete;
   }

   /**
    * Sets the value of the summaryNonLaborUnitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryNonLaborUnitsPercentComplete(Double value)
   {
      this.summaryNonLaborUnitsPercentComplete = value;
   }

   /**
    * Gets the value of the summaryNonLaborUnitsVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryNonLaborUnitsVariance()
   {
      return summaryNonLaborUnitsVariance;
   }

   /**
    * Sets the value of the summaryNonLaborUnitsVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryNonLaborUnitsVariance(Double value)
   {
      this.summaryNonLaborUnitsVariance = value;
   }

   /**
    * Gets the value of the summaryNotStartedActivityCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getSummaryNotStartedActivityCount()
   {
      return summaryNotStartedActivityCount;
   }

   /**
    * Sets the value of the summaryNotStartedActivityCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setSummaryNotStartedActivityCount(Integer value)
   {
      this.summaryNotStartedActivityCount = value;
   }

   /**
    * Gets the value of the summaryPerformancePercentCompleteByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPerformancePercentCompleteByLaborUnits()
   {
      return summaryPerformancePercentCompleteByLaborUnits;
   }

   /**
    * Sets the value of the summaryPerformancePercentCompleteByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPerformancePercentCompleteByLaborUnits(Double value)
   {
      this.summaryPerformancePercentCompleteByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryPlannedCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedCost()
   {
      return summaryPlannedCost;
   }

   /**
    * Sets the value of the summaryPlannedCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedCost(Double value)
   {
      this.summaryPlannedCost = value;
   }

   /**
    * Gets the value of the summaryPlannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedDuration()
   {
      return summaryPlannedDuration;
   }

   /**
    * Sets the value of the summaryPlannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedDuration(Double value)
   {
      this.summaryPlannedDuration = value;
   }

   /**
    * Gets the value of the summaryPlannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedExpenseCost()
   {
      return summaryPlannedExpenseCost;
   }

   /**
    * Sets the value of the summaryPlannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedExpenseCost(Double value)
   {
      this.summaryPlannedExpenseCost = value;
   }

   /**
    * Gets the value of the summaryPlannedFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryPlannedFinishDate()
   {
      return summaryPlannedFinishDate;
   }

   /**
    * Sets the value of the summaryPlannedFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedFinishDate(LocalDateTime value)
   {
      this.summaryPlannedFinishDate = value;
   }

   /**
    * Gets the value of the summaryPlannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedLaborCost()
   {
      return summaryPlannedLaborCost;
   }

   /**
    * Sets the value of the summaryPlannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedLaborCost(Double value)
   {
      this.summaryPlannedLaborCost = value;
   }

   /**
    * Gets the value of the summaryPlannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedLaborUnits()
   {
      return summaryPlannedLaborUnits;
   }

   /**
    * Sets the value of the summaryPlannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedLaborUnits(Double value)
   {
      this.summaryPlannedLaborUnits = value;
   }

   /**
    * Gets the value of the summaryPlannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedMaterialCost()
   {
      return summaryPlannedMaterialCost;
   }

   /**
    * Sets the value of the summaryPlannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedMaterialCost(Double value)
   {
      this.summaryPlannedMaterialCost = value;
   }

   /**
    * Gets the value of the summaryPlannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedNonLaborCost()
   {
      return summaryPlannedNonLaborCost;
   }

   /**
    * Sets the value of the summaryPlannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedNonLaborCost(Double value)
   {
      this.summaryPlannedNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryPlannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedNonLaborUnits()
   {
      return summaryPlannedNonLaborUnits;
   }

   /**
    * Sets the value of the summaryPlannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedNonLaborUnits(Double value)
   {
      this.summaryPlannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryPlannedStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryPlannedStartDate()
   {
      return summaryPlannedStartDate;
   }

   /**
    * Sets the value of the summaryPlannedStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedStartDate(LocalDateTime value)
   {
      this.summaryPlannedStartDate = value;
   }

   /**
    * Gets the value of the summaryPlannedValueByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedValueByCost()
   {
      return summaryPlannedValueByCost;
   }

   /**
    * Sets the value of the summaryPlannedValueByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedValueByCost(Double value)
   {
      this.summaryPlannedValueByCost = value;
   }

   /**
    * Gets the value of the summaryPlannedValueByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryPlannedValueByLaborUnits()
   {
      return summaryPlannedValueByLaborUnits;
   }

   /**
    * Sets the value of the summaryPlannedValueByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryPlannedValueByLaborUnits(Double value)
   {
      this.summaryPlannedValueByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryProgressFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryProgressFinishDate()
   {
      return summaryProgressFinishDate;
   }

   /**
    * Sets the value of the summaryProgressFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryProgressFinishDate(LocalDateTime value)
   {
      this.summaryProgressFinishDate = value;
   }

   /**
    * Gets the value of the summaryRemainingDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingDuration()
   {
      return summaryRemainingDuration;
   }

   /**
    * Sets the value of the summaryRemainingDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingDuration(Double value)
   {
      this.summaryRemainingDuration = value;
   }

   /**
    * Gets the value of the summaryRemainingExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingExpenseCost()
   {
      return summaryRemainingExpenseCost;
   }

   /**
    * Sets the value of the summaryRemainingExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingExpenseCost(Double value)
   {
      this.summaryRemainingExpenseCost = value;
   }

   /**
    * Gets the value of the summaryRemainingFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryRemainingFinishDate()
   {
      return summaryRemainingFinishDate;
   }

   /**
    * Sets the value of the summaryRemainingFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingFinishDate(LocalDateTime value)
   {
      this.summaryRemainingFinishDate = value;
   }

   /**
    * Gets the value of the summaryRemainingLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingLaborCost()
   {
      return summaryRemainingLaborCost;
   }

   /**
    * Sets the value of the summaryRemainingLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingLaborCost(Double value)
   {
      this.summaryRemainingLaborCost = value;
   }

   /**
    * Gets the value of the summaryRemainingLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingLaborUnits()
   {
      return summaryRemainingLaborUnits;
   }

   /**
    * Sets the value of the summaryRemainingLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingLaborUnits(Double value)
   {
      this.summaryRemainingLaborUnits = value;
   }

   /**
    * Gets the value of the summaryRemainingMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingMaterialCost()
   {
      return summaryRemainingMaterialCost;
   }

   /**
    * Sets the value of the summaryRemainingMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingMaterialCost(Double value)
   {
      this.summaryRemainingMaterialCost = value;
   }

   /**
    * Gets the value of the summaryRemainingNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingNonLaborCost()
   {
      return summaryRemainingNonLaborCost;
   }

   /**
    * Sets the value of the summaryRemainingNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingNonLaborCost(Double value)
   {
      this.summaryRemainingNonLaborCost = value;
   }

   /**
    * Gets the value of the summaryRemainingNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingNonLaborUnits()
   {
      return summaryRemainingNonLaborUnits;
   }

   /**
    * Sets the value of the summaryRemainingNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingNonLaborUnits(Double value)
   {
      this.summaryRemainingNonLaborUnits = value;
   }

   /**
    * Gets the value of the summaryRemainingStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSummaryRemainingStartDate()
   {
      return summaryRemainingStartDate;
   }

   /**
    * Sets the value of the summaryRemainingStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingStartDate(LocalDateTime value)
   {
      this.summaryRemainingStartDate = value;
   }

   /**
    * Gets the value of the summaryRemainingTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryRemainingTotalCost()
   {
      return summaryRemainingTotalCost;
   }

   /**
    * Sets the value of the summaryRemainingTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryRemainingTotalCost(Double value)
   {
      this.summaryRemainingTotalCost = value;
   }

   /**
    * Gets the value of the summarySchedulePercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummarySchedulePercentComplete()
   {
      return summarySchedulePercentComplete;
   }

   /**
    * Sets the value of the summarySchedulePercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarySchedulePercentComplete(Double value)
   {
      this.summarySchedulePercentComplete = value;
   }

   /**
    * Gets the value of the summarySchedulePercentCompleteByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummarySchedulePercentCompleteByCost()
   {
      return summarySchedulePercentCompleteByCost;
   }

   /**
    * Sets the value of the summarySchedulePercentCompleteByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarySchedulePercentCompleteByCost(Double value)
   {
      this.summarySchedulePercentCompleteByCost = value;
   }

   /**
    * Gets the value of the summarySchedulePercentCompleteByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummarySchedulePercentCompleteByLaborUnits()
   {
      return summarySchedulePercentCompleteByLaborUnits;
   }

   /**
    * Sets the value of the summarySchedulePercentCompleteByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarySchedulePercentCompleteByLaborUnits(Double value)
   {
      this.summarySchedulePercentCompleteByLaborUnits = value;
   }

   /**
    * Gets the value of the summarySchedulePerformanceIndexByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummarySchedulePerformanceIndexByCost()
   {
      return summarySchedulePerformanceIndexByCost;
   }

   /**
    * Sets the value of the summarySchedulePerformanceIndexByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarySchedulePerformanceIndexByCost(Double value)
   {
      this.summarySchedulePerformanceIndexByCost = value;
   }

   /**
    * Gets the value of the summarySchedulePerformanceIndexByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummarySchedulePerformanceIndexByLaborUnits()
   {
      return summarySchedulePerformanceIndexByLaborUnits;
   }

   /**
    * Sets the value of the summarySchedulePerformanceIndexByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummarySchedulePerformanceIndexByLaborUnits(Double value)
   {
      this.summarySchedulePerformanceIndexByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryScheduleVarianceByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryScheduleVarianceByCost()
   {
      return summaryScheduleVarianceByCost;
   }

   /**
    * Sets the value of the summaryScheduleVarianceByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryScheduleVarianceByCost(Double value)
   {
      this.summaryScheduleVarianceByCost = value;
   }

   /**
    * Gets the value of the summaryScheduleVarianceByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryScheduleVarianceByLaborUnits()
   {
      return summaryScheduleVarianceByLaborUnits;
   }

   /**
    * Sets the value of the summaryScheduleVarianceByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryScheduleVarianceByLaborUnits(Double value)
   {
      this.summaryScheduleVarianceByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryScheduleVarianceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryScheduleVarianceIndex()
   {
      return summaryScheduleVarianceIndex;
   }

   /**
    * Sets the value of the summaryScheduleVarianceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryScheduleVarianceIndex(Double value)
   {
      this.summaryScheduleVarianceIndex = value;
   }

   /**
    * Gets the value of the summaryScheduleVarianceIndexByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryScheduleVarianceIndexByCost()
   {
      return summaryScheduleVarianceIndexByCost;
   }

   /**
    * Sets the value of the summaryScheduleVarianceIndexByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryScheduleVarianceIndexByCost(Double value)
   {
      this.summaryScheduleVarianceIndexByCost = value;
   }

   /**
    * Gets the value of the summaryScheduleVarianceIndexByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryScheduleVarianceIndexByLaborUnits()
   {
      return summaryScheduleVarianceIndexByLaborUnits;
   }

   /**
    * Sets the value of the summaryScheduleVarianceIndexByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryScheduleVarianceIndexByLaborUnits(Double value)
   {
      this.summaryScheduleVarianceIndexByLaborUnits = value;
   }

   /**
    * Gets the value of the summaryStartDateVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryStartDateVariance()
   {
      return summaryStartDateVariance;
   }

   /**
    * Sets the value of the summaryStartDateVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryStartDateVariance(Double value)
   {
      this.summaryStartDateVariance = value;
   }

   /**
    * Gets the value of the summaryToCompletePerformanceIndexByCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryToCompletePerformanceIndexByCost()
   {
      return summaryToCompletePerformanceIndexByCost;
   }

   /**
    * Sets the value of the summaryToCompletePerformanceIndexByCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryToCompletePerformanceIndexByCost(Double value)
   {
      this.summaryToCompletePerformanceIndexByCost = value;
   }

   /**
    * Gets the value of the summaryTotalCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryTotalCostVariance()
   {
      return summaryTotalCostVariance;
   }

   /**
    * Sets the value of the summaryTotalCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryTotalCostVariance(Double value)
   {
      this.summaryTotalCostVariance = value;
   }

   /**
    * Gets the value of the summaryTotalFloat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryTotalFloat()
   {
      return summaryTotalFloat;
   }

   /**
    * Sets the value of the summaryTotalFloat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryTotalFloat(Double value)
   {
      this.summaryTotalFloat = value;
   }

   /**
    * Gets the value of the summaryUnitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryUnitsPercentComplete()
   {
      return summaryUnitsPercentComplete;
   }

   /**
    * Sets the value of the summaryUnitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryUnitsPercentComplete(Double value)
   {
      this.summaryUnitsPercentComplete = value;
   }

   /**
    * Gets the value of the summaryVarianceAtCompletionByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSummaryVarianceAtCompletionByLaborUnits()
   {
      return summaryVarianceAtCompletionByLaborUnits;
   }

   /**
    * Sets the value of the summaryVarianceAtCompletionByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSummaryVarianceAtCompletionByLaborUnits(Double value)
   {
      this.summaryVarianceAtCompletionByLaborUnits = value;
   }

   /**
    * Gets the value of the teamMemberActivityFields property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberActivityFields()
   {
      return teamMemberActivityFields;
   }

   /**
    * Sets the value of the teamMemberActivityFields property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberActivityFields(String value)
   {
      this.teamMemberActivityFields = value;
   }

   /**
    * Gets the value of the teamMemberAssignmentOption property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberAssignmentOption()
   {
      return teamMemberAssignmentOption;
   }

   /**
    * Sets the value of the teamMemberAssignmentOption property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberAssignmentOption(String value)
   {
      this.teamMemberAssignmentOption = value;
   }

   /**
    * Gets the value of the teamMemberResourceAssignmentFields property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberResourceAssignmentFields()
   {
      return teamMemberResourceAssignmentFields;
   }

   /**
    * Sets the value of the teamMemberResourceAssignmentFields property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberResourceAssignmentFields(String value)
   {
      this.teamMemberResourceAssignmentFields = value;
   }

   /**
    * Gets the value of the teamMemberStepUDFViewableFields property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberStepUDFViewableFields()
   {
      return teamMemberStepUDFViewableFields;
   }

   /**
    * Sets the value of the teamMemberStepUDFViewableFields property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberStepUDFViewableFields(String value)
   {
      this.teamMemberStepUDFViewableFields = value;
   }

   /**
    * Gets the value of the teamMemberViewableFields property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTeamMemberViewableFields()
   {
      return teamMemberViewableFields;
   }

   /**
    * Sets the value of the teamMemberViewableFields property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTeamMemberViewableFields(String value)
   {
      this.teamMemberViewableFields = value;
   }

   /**
    * Gets the value of the totalBenefitPlan property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalBenefitPlan()
   {
      return totalBenefitPlan;
   }

   /**
    * Sets the value of the totalBenefitPlan property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalBenefitPlan(Double value)
   {
      this.totalBenefitPlan = value;
   }

   /**
    * Gets the value of the totalBenefitPlanTally property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalBenefitPlanTally()
   {
      return totalBenefitPlanTally;
   }

   /**
    * Sets the value of the totalBenefitPlanTally property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalBenefitPlanTally(Double value)
   {
      this.totalBenefitPlanTally = value;
   }

   /**
    * Gets the value of the totalSpendingPlan property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalSpendingPlan()
   {
      return totalSpendingPlan;
   }

   /**
    * Sets the value of the totalSpendingPlan property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalSpendingPlan(Double value)
   {
      this.totalSpendingPlan = value;
   }

   /**
    * Gets the value of the totalSpendingPlanTally property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalSpendingPlanTally()
   {
      return totalSpendingPlanTally;
   }

   /**
    * Sets the value of the totalSpendingPlanTally property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalSpendingPlanTally(Double value)
   {
      this.totalSpendingPlanTally = value;
   }

   /**
    * Gets the value of the unallocatedBudget property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getUnallocatedBudget()
   {
      return unallocatedBudget;
   }

   /**
    * Sets the value of the unallocatedBudget property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnallocatedBudget(Double value)
   {
      this.unallocatedBudget = value;
   }

   /**
    * Gets the value of the undistributedCurrentVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getUndistributedCurrentVariance()
   {
      return undistributedCurrentVariance;
   }

   /**
    * Sets the value of the undistributedCurrentVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUndistributedCurrentVariance(Double value)
   {
      this.undistributedCurrentVariance = value;
   }

   /**
    * Gets the value of the wbsCategoryObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getWBSCategoryObjectId()
   {
      return wbsCategoryObjectId;
   }

   /**
    * Sets the value of the wbsCategoryObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setWBSCategoryObjectId(Integer value)
   {
      this.wbsCategoryObjectId = value;
   }

   /**
    * Gets the value of the wbsCodeSeparator property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSCodeSeparator()
   {
      return wbsCodeSeparator;
   }

   /**
    * Sets the value of the wbsCodeSeparator property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSCodeSeparator(String value)
   {
      this.wbsCodeSeparator = value;
   }

   /**
    * Gets the value of the wbsObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getWBSObjectId()
   {
      return wbsObjectId;
   }

   /**
    * Sets the value of the wbsObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setWBSObjectId(Integer value)
   {
      this.wbsObjectId = value;
   }

   /**
    * Gets the value of the webSiteRootDirectory property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWebSiteRootDirectory()
   {
      return webSiteRootDirectory;
   }

   /**
    * Sets the value of the webSiteRootDirectory property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWebSiteRootDirectory(String value)
   {
      this.webSiteRootDirectory = value;
   }

   /**
    * Gets the value of the webSiteURL property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWebSiteURL()
   {
      return webSiteURL;
   }

   /**
    * Sets the value of the webSiteURL property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWebSiteURL(String value)
   {
      this.webSiteURL = value;
   }

   /**
    * Gets the value of the code property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the code property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getCode().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link CodeAssignmentType }
    *
    *
    */
   public List<CodeAssignmentType> getCode()
   {
      if (code == null)
      {
         code = new ArrayList<>();
      }
      return this.code;
   }

   /**
    * Gets the value of the udf property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the udf property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getUDF().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link UDFAssignmentType }
    *
    *
    */
   public List<UDFAssignmentType> getUDF()
   {
      if (udf == null)
      {
         udf = new ArrayList<>();
      }
      return this.udf;
   }

   /**
    * Gets the value of the spread property.
    *
    * @return
    *     possible object is
    *     {@link EPSProjectWBSSpreadType }
    *
    */
   public EPSProjectWBSSpreadType getSpread()
   {
      return spread;
   }

   /**
    * Sets the value of the spread property.
    *
    * @param value
    *     allowed object is
    *     {@link EPSProjectWBSSpreadType }
    *
    */
   public void setSpread(EPSProjectWBSSpreadType value)
   {
      this.spread = value;
   }

   /**
    * Gets the value of the projectResourceSpread property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectResourceSpread property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectResourceSpread().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectResourceSpreadType }
    *
    *
    */
   public List<ProjectResourceSpreadType> getProjectResourceSpread()
   {
      if (projectResourceSpread == null)
      {
         projectResourceSpread = new ArrayList<>();
      }
      return this.projectResourceSpread;
   }

   /**
    * Gets the value of the projectRoleSpread property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectRoleSpread property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectRoleSpread().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectRoleSpreadType }
    *
    *
    */
   public List<ProjectRoleSpreadType> getProjectRoleSpread()
   {
      if (projectRoleSpread == null)
      {
         projectRoleSpread = new ArrayList<>();
      }
      return this.projectRoleSpread;
   }

   /**
    * Gets the value of the calendar property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the calendar property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getCalendar().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link CalendarType }
    *
    *
    */
   public List<CalendarType> getCalendar()
   {
      if (calendar == null)
      {
         calendar = new ArrayList<>();
      }
      return this.calendar;
   }

   /**
    * Gets the value of the wbs property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the wbs property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getWBS().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link WBSType }
    *
    *
    */
   public List<WBSType> getWBS()
   {
      if (wbs == null)
      {
         wbs = new ArrayList<>();
      }
      return this.wbs;
   }

   /**
    * Gets the value of the projectResource property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectResource property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectResource().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectResourceType }
    *
    *
    */
   public List<ProjectResourceType> getProjectResource()
   {
      if (projectResource == null)
      {
         projectResource = new ArrayList<>();
      }
      return this.projectResource;
   }

   /**
    * Gets the value of the projectResourceQuantity property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectResourceQuantity property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectResourceQuantity().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectResourceQuantityType }
    *
    *
    */
   public List<ProjectResourceQuantityType> getProjectResourceQuantity()
   {
      if (projectResourceQuantity == null)
      {
         projectResourceQuantity = new ArrayList<>();
      }
      return this.projectResourceQuantity;
   }

   /**
    * Gets the value of the projectBudgetChangeLog property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectBudgetChangeLog property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectBudgetChangeLog().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectBudgetChangeLogType }
    *
    *
    */
   public List<ProjectBudgetChangeLogType> getProjectBudgetChangeLog()
   {
      if (projectBudgetChangeLog == null)
      {
         projectBudgetChangeLog = new ArrayList<>();
      }
      return this.projectBudgetChangeLog;
   }

   /**
    * Gets the value of the wbsMilestone property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the wbsMilestone property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getWBSMilestone().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link WBSMilestoneType }
    *
    *
    */
   public List<WBSMilestoneType> getWBSMilestone()
   {
      if (wbsMilestone == null)
      {
         wbsMilestone = new ArrayList<>();
      }
      return this.wbsMilestone;
   }

   /**
    * Gets the value of the projectNote property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectNote property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectNote().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectNoteType }
    *
    *
    */
   public List<ProjectNoteType> getProjectNote()
   {
      if (projectNote == null)
      {
         projectNote = new ArrayList<>();
      }
      return this.projectNote;
   }

   /**
    * Gets the value of the projectThreshold property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectThreshold property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectThreshold().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectThresholdType }
    *
    *
    */
   public List<ProjectThresholdType> getProjectThreshold()
   {
      if (projectThreshold == null)
      {
         projectThreshold = new ArrayList<>();
      }
      return this.projectThreshold;
   }

   /**
    * Gets the value of the projectSpendingPlan property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectSpendingPlan property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectSpendingPlan().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectSpendingPlanType }
    *
    *
    */
   public List<ProjectSpendingPlanType> getProjectSpendingPlan()
   {
      if (projectSpendingPlan == null)
      {
         projectSpendingPlan = new ArrayList<>();
      }
      return this.projectSpendingPlan;
   }

   /**
    * Gets the value of the projectFunding property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectFunding property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectFunding().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectFundingType }
    *
    *
    */
   public List<ProjectFundingType> getProjectFunding()
   {
      if (projectFunding == null)
      {
         projectFunding = new ArrayList<>();
      }
      return this.projectFunding;
   }

   /**
    * Gets the value of the activityCodeType property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityCodeType property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityCodeType().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityCodeTypeType }
    *
    *
    */
   public List<ActivityCodeTypeType> getActivityCodeType()
   {
      if (activityCodeType == null)
      {
         activityCodeType = new ArrayList<>();
      }
      return this.activityCodeType;
   }

   /**
    * Gets the value of the activityCode property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityCode property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityCode().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityCodeType }
    *
    *
    */
   public List<ActivityCodeType> getActivityCode()
   {
      if (activityCode == null)
      {
         activityCode = new ArrayList<>();
      }
      return this.activityCode;
   }

   /**
    * Gets the value of the activity property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activity property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivity().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityType }
    *
    *
    */
   public List<ActivityType> getActivity()
   {
      if (activity == null)
      {
         activity = new ArrayList<>();
      }
      return this.activity;
   }

   /**
    * Gets the value of the resourceAssignment property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the resourceAssignment property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getResourceAssignment().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ResourceAssignmentType }
    *
    *
    */
   public List<ResourceAssignmentType> getResourceAssignment()
   {
      if (resourceAssignment == null)
      {
         resourceAssignment = new ArrayList<>();
      }
      return this.resourceAssignment;
   }

   /**
    * Gets the value of the activityExpense property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityExpense property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityExpense().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityExpenseType }
    *
    *
    */
   public List<ActivityExpenseType> getActivityExpense()
   {
      if (activityExpense == null)
      {
         activityExpense = new ArrayList<>();
      }
      return this.activityExpense;
   }

   /**
    * Gets the value of the activityNote property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityNote property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityNote().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityNoteType }
    *
    *
    */
   public List<ActivityNoteType> getActivityNote()
   {
      if (activityNote == null)
      {
         activityNote = new ArrayList<>();
      }
      return this.activityNote;
   }

   /**
    * Gets the value of the activityStep property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityStep property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityStep().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityStepType }
    *
    *
    */
   public List<ActivityStepType> getActivityStep()
   {
      if (activityStep == null)
      {
         activityStep = new ArrayList<>();
      }
      return this.activityStep;
   }

   /**
    * Gets the value of the relationship property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the relationship property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRelationship().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RelationshipType }
    *
    *
    */
   public List<RelationshipType> getRelationship()
   {
      if (relationship == null)
      {
         relationship = new ArrayList<>();
      }
      return this.relationship;
   }

   /**
    * Gets the value of the activityPeriodActual property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityPeriodActual property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityPeriodActual().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityPeriodActualType }
    *
    *
    */
   public List<ActivityPeriodActualType> getActivityPeriodActual()
   {
      if (activityPeriodActual == null)
      {
         activityPeriodActual = new ArrayList<>();
      }
      return this.activityPeriodActual;
   }

   /**
    * Gets the value of the projectIssue property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectIssue property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectIssue().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectIssueType }
    *
    *
    */
   public List<ProjectIssueType> getProjectIssue()
   {
      if (projectIssue == null)
      {
         projectIssue = new ArrayList<>();
      }
      return this.projectIssue;
   }

   /**
    * Gets the value of the resourceAssignmentPeriodActual property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the resourceAssignmentPeriodActual property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getResourceAssignmentPeriodActual().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ResourceAssignmentPeriodActualType }
    *
    *
    */
   public List<ResourceAssignmentPeriodActualType> getResourceAssignmentPeriodActual()
   {
      if (resourceAssignmentPeriodActual == null)
      {
         resourceAssignmentPeriodActual = new ArrayList<>();
      }
      return this.resourceAssignmentPeriodActual;
   }

   /**
    * Gets the value of the document property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the document property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getDocument().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link DocumentType }
    *
    *
    */
   public List<DocumentType> getDocument()
   {
      if (document == null)
      {
         document = new ArrayList<>();
      }
      return this.document;
   }

   /**
    * Gets the value of the projectDocument property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the projectDocument property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getProjectDocument().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectDocumentType }
    *
    *
    */
   public List<ProjectDocumentType> getProjectDocument()
   {
      if (projectDocument == null)
      {
         projectDocument = new ArrayList<>();
      }
      return this.projectDocument;
   }

   /**
    * Gets the value of the scheduleOptions property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the scheduleOptions property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getScheduleOptions().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ScheduleOptionsType }
    *
    *
    */
   public List<ScheduleOptionsType> getScheduleOptions()
   {
      if (scheduleOptions == null)
      {
         scheduleOptions = new ArrayList<>();
      }
      return this.scheduleOptions;
   }

   /**
    * Gets the value of the risk property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the risk property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRisk().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RiskType }
    *
    *
    */
   public List<RiskType> getRisk()
   {
      if (risk == null)
      {
         risk = new ArrayList<>();
      }
      return this.risk;
   }

   /**
    * Gets the value of the activityRisk property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the activityRisk property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getActivityRisk().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivityRiskType }
    *
    *
    */
   public List<ActivityRiskType> getActivityRisk()
   {
      if (activityRisk == null)
      {
         activityRisk = new ArrayList<>();
      }
      return this.activityRisk;
   }

   /**
    * Gets the value of the riskImpact property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the riskImpact property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRiskImpact().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RiskImpactType }
    *
    *
    */
   public List<RiskImpactType> getRiskImpact()
   {
      if (riskImpact == null)
      {
         riskImpact = new ArrayList<>();
      }
      return this.riskImpact;
   }

   /**
    * Gets the value of the riskResponsePlan property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the riskResponsePlan property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRiskResponsePlan().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RiskResponsePlanType }
    *
    *
    */
   public List<RiskResponsePlanType> getRiskResponsePlan()
   {
      if (riskResponsePlan == null)
      {
         riskResponsePlan = new ArrayList<>();
      }
      return this.riskResponsePlan;
   }

   /**
    * Gets the value of the riskResponseAction property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the riskResponseAction property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRiskResponseAction().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RiskResponseActionType }
    *
    *
    */
   public List<RiskResponseActionType> getRiskResponseAction()
   {
      if (riskResponseAction == null)
      {
         riskResponseAction = new ArrayList<>();
      }
      return this.riskResponseAction;
   }

   /**
    * Gets the value of the riskResponseActionImpact property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the riskResponseActionImpact property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getRiskResponseActionImpact().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link RiskResponseActionImpactType }
    *
    *
    */
   public List<RiskResponseActionImpactType> getRiskResponseActionImpact()
   {
      if (riskResponseActionImpact == null)
      {
         riskResponseActionImpact = new ArrayList<>();
      }
      return this.riskResponseActionImpact;
   }

}
