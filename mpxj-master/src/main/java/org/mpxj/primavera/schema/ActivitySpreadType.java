//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for ActivitySpreadType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ActivitySpreadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="PeriodType"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Financial Period"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Period" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Baseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "ActivitySpreadType", propOrder =
{
   "startDate",
   "endDate",
   "periodType",
   "period"
}) public class ActivitySpreadType
{

   @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
   @XmlElement(name = "PeriodType", required = true) @XmlJavaTypeAdapter(Adapter1.class) protected String periodType;
   @XmlElement(name = "Period") protected List<ActivitySpreadType.Period> period;

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the endDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEndDate()
   {
      return endDate;
   }

   /**
    * Sets the value of the endDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEndDate(LocalDateTime value)
   {
      this.endDate = value;
   }

   /**
    * Gets the value of the periodType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPeriodType()
   {
      return periodType;
   }

   /**
    * Sets the value of the periodType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPeriodType(String value)
   {
      this.periodType = value;
   }

   /**
    * Gets the value of the period property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the period property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getPeriod().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ActivitySpreadType.Period }
    *
    *
    */
   public List<ActivitySpreadType.Period> getPeriod()
   {
      if (period == null)
      {
         period = new ArrayList<>();
      }
      return this.period;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Baseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "startDate",
      "endDate",
      "actualLaborUnits",
      "cumulativeActualLaborUnits",
      "actualNonLaborUnits",
      "cumulativeActualNonLaborUnits",
      "atCompletionLaborUnits",
      "cumulativeAtCompletionLaborUnits",
      "atCompletionNonLaborUnits",
      "cumulativeAtCompletionNonLaborUnits",
      "baseline1ActualLaborUnits",
      "cumulativeBaseline1ActualLaborUnits",
      "baseline1ActualNonLaborUnits",
      "cumulativeBaseline1ActualNonLaborUnits",
      "baseline1PlannedLaborUnits",
      "cumulativeBaseline1PlannedLaborUnits",
      "baseline1PlannedNonLaborUnits",
      "cumulativeBaseline1PlannedNonLaborUnits",
      "baselineActualLaborUnits",
      "cumulativeBaselineActualLaborUnits",
      "baselineActualNonLaborUnits",
      "cumulativeBaselineActualNonLaborUnits",
      "baselinePlannedLaborUnits",
      "cumulativeBaselinePlannedLaborUnits",
      "baselinePlannedNonLaborUnits",
      "cumulativeBaselinePlannedNonLaborUnits",
      "earnedValueLaborUnits",
      "cumulativeEarnedValueLaborUnits",
      "estimateAtCompletionLaborUnits",
      "cumulativeEstimateAtCompletionLaborUnits",
      "estimateToCompleteLaborUnits",
      "cumulativeEstimateToCompleteLaborUnits",
      "plannedLaborUnits",
      "cumulativePlannedLaborUnits",
      "plannedNonLaborUnits",
      "cumulativePlannedNonLaborUnits",
      "plannedValueLaborUnits",
      "cumulativePlannedValueLaborUnits",
      "remainingLaborUnits",
      "cumulativeRemainingLaborUnits",
      "remainingLateLaborUnits",
      "cumulativeRemainingLateLaborUnits",
      "remainingLateNonLaborUnits",
      "cumulativeRemainingLateNonLaborUnits",
      "remainingNonLaborUnits",
      "cumulativeRemainingNonLaborUnits",
      "actualCost",
      "cumulativeActualCost",
      "actualExpenseCost",
      "cumulativeActualExpenseCost",
      "actualLaborCost",
      "cumulativeActualLaborCost",
      "actualMaterialCost",
      "cumulativeActualMaterialCost",
      "actualNonLaborCost",
      "cumulativeActualNonLaborCost",
      "actualTotalCost",
      "cumulativeActualTotalCost",
      "atCompletionExpenseCost",
      "cumulativeAtCompletionExpenseCost",
      "atCompletionLaborCost",
      "cumulativeAtCompletionLaborCost",
      "atCompletionMaterialCost",
      "cumulativeAtCompletionMaterialCost",
      "atCompletionNonLaborCost",
      "cumulativeAtCompletionNonLaborCost",
      "atCompletionTotalCost",
      "cumulativeAtCompletionTotalCost",
      "baseline1ActualExpenseCost",
      "cumulativeBaseline1ActualExpenseCost",
      "baseline1ActualLaborCost",
      "cumulativeBaseline1ActualLaborCost",
      "baseline1ActualMaterialCost",
      "cumulativeBaseline1ActualMaterialCost",
      "baseline1ActualNonLaborCost",
      "cumulativeBaseline1ActualNonLaborCost",
      "baseline1ActualTotalCost",
      "cumulativeBaseline1ActualTotalCost",
      "baseline1PlannedExpenseCost",
      "cumulativeBaseline1PlannedExpenseCost",
      "baseline1PlannedLaborCost",
      "cumulativeBaseline1PlannedLaborCost",
      "baseline1PlannedMaterialCost",
      "cumulativeBaseline1PlannedMaterialCost",
      "baseline1PlannedNonLaborCost",
      "cumulativeBaseline1PlannedNonLaborCost",
      "baseline1PlannedTotalCost",
      "cumulativeBaseline1PlannedTotalCost",
      "baselineActualExpenseCost",
      "cumulativeBaselineActualExpenseCost",
      "baselineActualLaborCost",
      "cumulativeBaselineActualLaborCost",
      "baselineActualMaterialCost",
      "cumulativeBaselineActualMaterialCost",
      "baselineActualNonLaborCost",
      "cumulativeBaselineActualNonLaborCost",
      "baselineActualTotalCost",
      "cumulativeBaselineActualTotalCost",
      "baselinePlannedExpenseCost",
      "cumulativeBaselinePlannedExpenseCost",
      "baselinePlannedLaborCost",
      "cumulativeBaselinePlannedLaborCost",
      "baselinePlannedMaterialCost",
      "cumulativeBaselinePlannedMaterialCost",
      "baselinePlannedNonLaborCost",
      "cumulativeBaselinePlannedNonLaborCost",
      "baselinePlannedTotalCost",
      "cumulativeBaselinePlannedTotalCost",
      "earnedValueCost",
      "cumulativeEarnedValueCost",
      "estimateAtCompletionCost",
      "cumulativeEstimateAtCompletionCost",
      "estimateToCompleteCost",
      "cumulativeEstimateToCompleteCost",
      "plannedExpenseCost",
      "cumulativePlannedExpenseCost",
      "plannedLaborCost",
      "cumulativePlannedLaborCost",
      "plannedMaterialCost",
      "cumulativePlannedMaterialCost",
      "plannedNonLaborCost",
      "cumulativePlannedNonLaborCost",
      "plannedTotalCost",
      "cumulativePlannedTotalCost",
      "plannedValueCost",
      "cumulativePlannedValueCost",
      "remainingExpenseCost",
      "cumulativeRemainingExpenseCost",
      "remainingLaborCost",
      "cumulativeRemainingLaborCost",
      "remainingLateExpenseCost",
      "cumulativeRemainingLateExpenseCost",
      "remainingLateLaborCost",
      "cumulativeRemainingLateLaborCost",
      "remainingLateMaterialCost",
      "cumulativeRemainingLateMaterialCost",
      "remainingLateNonLaborCost",
      "cumulativeRemainingLateNonLaborCost",
      "remainingLateTotalCost",
      "cumulativeRemainingLateTotalCost",
      "remainingMaterialCost",
      "cumulativeRemainingMaterialCost",
      "remainingNonLaborCost",
      "cumulativeRemainingNonLaborCost",
      "remainingTotalCost",
      "cumulativeRemainingTotalCost"
   }) public static class Period
   {

      @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
      @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
      @XmlElement(name = "ActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborUnits;
      @XmlElement(name = "CumulativeActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualLaborUnits;
      @XmlElement(name = "ActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonLaborUnits;
      @XmlElement(name = "CumulativeActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualNonLaborUnits;
      @XmlElement(name = "AtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborUnits;
      @XmlElement(name = "CumulativeAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionLaborUnits;
      @XmlElement(name = "AtCompletionNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonLaborUnits;
      @XmlElement(name = "CumulativeAtCompletionNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionNonLaborUnits;
      @XmlElement(name = "Baseline1ActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualLaborUnits;
      @XmlElement(name = "CumulativeBaseline1ActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualLaborUnits;
      @XmlElement(name = "Baseline1ActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualNonLaborUnits;
      @XmlElement(name = "CumulativeBaseline1ActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualNonLaborUnits;
      @XmlElement(name = "Baseline1PlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedLaborUnits;
      @XmlElement(name = "CumulativeBaseline1PlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedLaborUnits;
      @XmlElement(name = "Baseline1PlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedNonLaborUnits;
      @XmlElement(name = "CumulativeBaseline1PlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedNonLaborUnits;
      @XmlElement(name = "BaselineActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualLaborUnits;
      @XmlElement(name = "CumulativeBaselineActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualLaborUnits;
      @XmlElement(name = "BaselineActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualNonLaborUnits;
      @XmlElement(name = "CumulativeBaselineActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualNonLaborUnits;
      @XmlElement(name = "BaselinePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborUnits;
      @XmlElement(name = "CumulativeBaselinePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedLaborUnits;
      @XmlElement(name = "BaselinePlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonLaborUnits;
      @XmlElement(name = "CumulativeBaselinePlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedNonLaborUnits;
      @XmlElement(name = "EarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueLaborUnits;
      @XmlElement(name = "CumulativeEarnedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEarnedValueLaborUnits;
      @XmlElement(name = "EstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionLaborUnits;
      @XmlElement(name = "CumulativeEstimateAtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateAtCompletionLaborUnits;
      @XmlElement(name = "EstimateToCompleteLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToCompleteLaborUnits;
      @XmlElement(name = "CumulativeEstimateToCompleteLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateToCompleteLaborUnits;
      @XmlElement(name = "PlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborUnits;
      @XmlElement(name = "CumulativePlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedLaborUnits;
      @XmlElement(name = "PlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonLaborUnits;
      @XmlElement(name = "CumulativePlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedNonLaborUnits;
      @XmlElement(name = "PlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueLaborUnits;
      @XmlElement(name = "CumulativePlannedValueLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedValueLaborUnits;
      @XmlElement(name = "RemainingLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborUnits;
      @XmlElement(name = "CumulativeRemainingLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLaborUnits;
      @XmlElement(name = "RemainingLateLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateLaborUnits;
      @XmlElement(name = "CumulativeRemainingLateLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateLaborUnits;
      @XmlElement(name = "RemainingLateNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateNonLaborUnits;
      @XmlElement(name = "CumulativeRemainingLateNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateNonLaborUnits;
      @XmlElement(name = "RemainingNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonLaborUnits;
      @XmlElement(name = "CumulativeRemainingNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingNonLaborUnits;
      @XmlElement(name = "ActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualCost;
      @XmlElement(name = "CumulativeActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualCost;
      @XmlElement(name = "ActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualExpenseCost;
      @XmlElement(name = "CumulativeActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualExpenseCost;
      @XmlElement(name = "ActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborCost;
      @XmlElement(name = "CumulativeActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualLaborCost;
      @XmlElement(name = "ActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualMaterialCost;
      @XmlElement(name = "CumulativeActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualMaterialCost;
      @XmlElement(name = "ActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonLaborCost;
      @XmlElement(name = "CumulativeActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualNonLaborCost;
      @XmlElement(name = "ActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualTotalCost;
      @XmlElement(name = "CumulativeActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualTotalCost;
      @XmlElement(name = "AtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionExpenseCost;
      @XmlElement(name = "CumulativeAtCompletionExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionExpenseCost;
      @XmlElement(name = "AtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborCost;
      @XmlElement(name = "CumulativeAtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionLaborCost;
      @XmlElement(name = "AtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionMaterialCost;
      @XmlElement(name = "CumulativeAtCompletionMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionMaterialCost;
      @XmlElement(name = "AtCompletionNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonLaborCost;
      @XmlElement(name = "CumulativeAtCompletionNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionNonLaborCost;
      @XmlElement(name = "AtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionTotalCost;
      @XmlElement(name = "CumulativeAtCompletionTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionTotalCost;
      @XmlElement(name = "Baseline1ActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualExpenseCost;
      @XmlElement(name = "CumulativeBaseline1ActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualExpenseCost;
      @XmlElement(name = "Baseline1ActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualLaborCost;
      @XmlElement(name = "CumulativeBaseline1ActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualLaborCost;
      @XmlElement(name = "Baseline1ActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualMaterialCost;
      @XmlElement(name = "CumulativeBaseline1ActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualMaterialCost;
      @XmlElement(name = "Baseline1ActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualNonLaborCost;
      @XmlElement(name = "CumulativeBaseline1ActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualNonLaborCost;
      @XmlElement(name = "Baseline1ActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1ActualTotalCost;
      @XmlElement(name = "CumulativeBaseline1ActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1ActualTotalCost;
      @XmlElement(name = "Baseline1PlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedExpenseCost;
      @XmlElement(name = "CumulativeBaseline1PlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedExpenseCost;
      @XmlElement(name = "Baseline1PlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedLaborCost;
      @XmlElement(name = "CumulativeBaseline1PlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedLaborCost;
      @XmlElement(name = "Baseline1PlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedMaterialCost;
      @XmlElement(name = "CumulativeBaseline1PlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedMaterialCost;
      @XmlElement(name = "Baseline1PlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedNonLaborCost;
      @XmlElement(name = "CumulativeBaseline1PlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedNonLaborCost;
      @XmlElement(name = "Baseline1PlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedTotalCost;
      @XmlElement(name = "CumulativeBaseline1PlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaseline1PlannedTotalCost;
      @XmlElement(name = "BaselineActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualExpenseCost;
      @XmlElement(name = "CumulativeBaselineActualExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualExpenseCost;
      @XmlElement(name = "BaselineActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualLaborCost;
      @XmlElement(name = "CumulativeBaselineActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualLaborCost;
      @XmlElement(name = "BaselineActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualMaterialCost;
      @XmlElement(name = "CumulativeBaselineActualMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualMaterialCost;
      @XmlElement(name = "BaselineActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualNonLaborCost;
      @XmlElement(name = "CumulativeBaselineActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualNonLaborCost;
      @XmlElement(name = "BaselineActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineActualTotalCost;
      @XmlElement(name = "CumulativeBaselineActualTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselineActualTotalCost;
      @XmlElement(name = "BaselinePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedExpenseCost;
      @XmlElement(name = "CumulativeBaselinePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedExpenseCost;
      @XmlElement(name = "BaselinePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborCost;
      @XmlElement(name = "CumulativeBaselinePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedLaborCost;
      @XmlElement(name = "BaselinePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedMaterialCost;
      @XmlElement(name = "CumulativeBaselinePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedMaterialCost;
      @XmlElement(name = "BaselinePlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonLaborCost;
      @XmlElement(name = "CumulativeBaselinePlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedNonLaborCost;
      @XmlElement(name = "BaselinePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedTotalCost;
      @XmlElement(name = "CumulativeBaselinePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeBaselinePlannedTotalCost;
      @XmlElement(name = "EarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueCost;
      @XmlElement(name = "CumulativeEarnedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEarnedValueCost;
      @XmlElement(name = "EstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionCost;
      @XmlElement(name = "CumulativeEstimateAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateAtCompletionCost;
      @XmlElement(name = "EstimateToCompleteCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToCompleteCost;
      @XmlElement(name = "CumulativeEstimateToCompleteCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeEstimateToCompleteCost;
      @XmlElement(name = "PlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedExpenseCost;
      @XmlElement(name = "CumulativePlannedExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedExpenseCost;
      @XmlElement(name = "PlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborCost;
      @XmlElement(name = "CumulativePlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedLaborCost;
      @XmlElement(name = "PlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedMaterialCost;
      @XmlElement(name = "CumulativePlannedMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedMaterialCost;
      @XmlElement(name = "PlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonLaborCost;
      @XmlElement(name = "CumulativePlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedNonLaborCost;
      @XmlElement(name = "PlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedTotalCost;
      @XmlElement(name = "CumulativePlannedTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedTotalCost;
      @XmlElement(name = "PlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueCost;
      @XmlElement(name = "CumulativePlannedValueCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedValueCost;
      @XmlElement(name = "RemainingExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingExpenseCost;
      @XmlElement(name = "CumulativeRemainingExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingExpenseCost;
      @XmlElement(name = "RemainingLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborCost;
      @XmlElement(name = "CumulativeRemainingLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLaborCost;
      @XmlElement(name = "RemainingLateExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateExpenseCost;
      @XmlElement(name = "CumulativeRemainingLateExpenseCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateExpenseCost;
      @XmlElement(name = "RemainingLateLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateLaborCost;
      @XmlElement(name = "CumulativeRemainingLateLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateLaborCost;
      @XmlElement(name = "RemainingLateMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateMaterialCost;
      @XmlElement(name = "CumulativeRemainingLateMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateMaterialCost;
      @XmlElement(name = "RemainingLateNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateNonLaborCost;
      @XmlElement(name = "CumulativeRemainingLateNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateNonLaborCost;
      @XmlElement(name = "RemainingLateTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateTotalCost;
      @XmlElement(name = "CumulativeRemainingLateTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateTotalCost;
      @XmlElement(name = "RemainingMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingMaterialCost;
      @XmlElement(name = "CumulativeRemainingMaterialCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingMaterialCost;
      @XmlElement(name = "RemainingNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonLaborCost;
      @XmlElement(name = "CumulativeRemainingNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingNonLaborCost;
      @XmlElement(name = "RemainingTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingTotalCost;
      @XmlElement(name = "CumulativeRemainingTotalCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingTotalCost;

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStartDate(LocalDateTime value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the endDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getEndDate()
      {
         return endDate;
      }

      /**
       * Sets the value of the endDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEndDate(LocalDateTime value)
      {
         this.endDate = value;
      }

      /**
       * Gets the value of the actualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualLaborUnits()
      {
         return actualLaborUnits;
      }

      /**
       * Sets the value of the actualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualLaborUnits(Double value)
      {
         this.actualLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualLaborUnits()
      {
         return cumulativeActualLaborUnits;
      }

      /**
       * Sets the value of the cumulativeActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualLaborUnits(Double value)
      {
         this.cumulativeActualLaborUnits = value;
      }

      /**
       * Gets the value of the actualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualNonLaborUnits()
      {
         return actualNonLaborUnits;
      }

      /**
       * Sets the value of the actualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualNonLaborUnits(Double value)
      {
         this.actualNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualNonLaborUnits()
      {
         return cumulativeActualNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualNonLaborUnits(Double value)
      {
         this.cumulativeActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the atCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionLaborUnits()
      {
         return atCompletionLaborUnits;
      }

      /**
       * Sets the value of the atCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionLaborUnits(Double value)
      {
         this.atCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionLaborUnits()
      {
         return cumulativeAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionLaborUnits(Double value)
      {
         this.cumulativeAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the atCompletionNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionNonLaborUnits()
      {
         return atCompletionNonLaborUnits;
      }

      /**
       * Sets the value of the atCompletionNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionNonLaborUnits(Double value)
      {
         this.atCompletionNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionNonLaborUnits()
      {
         return cumulativeAtCompletionNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionNonLaborUnits(Double value)
      {
         this.cumulativeAtCompletionNonLaborUnits = value;
      }

      /**
       * Gets the value of the baseline1ActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualLaborUnits()
      {
         return baseline1ActualLaborUnits;
      }

      /**
       * Sets the value of the baseline1ActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualLaborUnits(Double value)
      {
         this.baseline1ActualLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualLaborUnits()
      {
         return cumulativeBaseline1ActualLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualLaborUnits(Double value)
      {
         this.cumulativeBaseline1ActualLaborUnits = value;
      }

      /**
       * Gets the value of the baseline1ActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualNonLaborUnits()
      {
         return baseline1ActualNonLaborUnits;
      }

      /**
       * Sets the value of the baseline1ActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualNonLaborUnits(Double value)
      {
         this.baseline1ActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualNonLaborUnits()
      {
         return cumulativeBaseline1ActualNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualNonLaborUnits(Double value)
      {
         this.cumulativeBaseline1ActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the baseline1PlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedLaborUnits()
      {
         return baseline1PlannedLaborUnits;
      }

      /**
       * Sets the value of the baseline1PlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedLaborUnits(Double value)
      {
         this.baseline1PlannedLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedLaborUnits()
      {
         return cumulativeBaseline1PlannedLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedLaborUnits(Double value)
      {
         this.cumulativeBaseline1PlannedLaborUnits = value;
      }

      /**
       * Gets the value of the baseline1PlannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedNonLaborUnits()
      {
         return baseline1PlannedNonLaborUnits;
      }

      /**
       * Sets the value of the baseline1PlannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedNonLaborUnits(Double value)
      {
         this.baseline1PlannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedNonLaborUnits()
      {
         return cumulativeBaseline1PlannedNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedNonLaborUnits(Double value)
      {
         this.cumulativeBaseline1PlannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the baselineActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualLaborUnits()
      {
         return baselineActualLaborUnits;
      }

      /**
       * Sets the value of the baselineActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualLaborUnits(Double value)
      {
         this.baselineActualLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualLaborUnits()
      {
         return cumulativeBaselineActualLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselineActualLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualLaborUnits(Double value)
      {
         this.cumulativeBaselineActualLaborUnits = value;
      }

      /**
       * Gets the value of the baselineActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualNonLaborUnits()
      {
         return baselineActualNonLaborUnits;
      }

      /**
       * Sets the value of the baselineActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualNonLaborUnits(Double value)
      {
         this.baselineActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualNonLaborUnits()
      {
         return cumulativeBaselineActualNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselineActualNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualNonLaborUnits(Double value)
      {
         this.cumulativeBaselineActualNonLaborUnits = value;
      }

      /**
       * Gets the value of the baselinePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedLaborUnits()
      {
         return baselinePlannedLaborUnits;
      }

      /**
       * Sets the value of the baselinePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedLaborUnits(Double value)
      {
         this.baselinePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedLaborUnits()
      {
         return cumulativeBaselinePlannedLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedLaborUnits(Double value)
      {
         this.cumulativeBaselinePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the baselinePlannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedNonLaborUnits()
      {
         return baselinePlannedNonLaborUnits;
      }

      /**
       * Sets the value of the baselinePlannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedNonLaborUnits(Double value)
      {
         this.baselinePlannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedNonLaborUnits()
      {
         return cumulativeBaselinePlannedNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedNonLaborUnits(Double value)
      {
         this.cumulativeBaselinePlannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the earnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEarnedValueLaborUnits()
      {
         return earnedValueLaborUnits;
      }

      /**
       * Sets the value of the earnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEarnedValueLaborUnits(Double value)
      {
         this.earnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEarnedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEarnedValueLaborUnits()
      {
         return cumulativeEarnedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEarnedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEarnedValueLaborUnits(Double value)
      {
         this.cumulativeEarnedValueLaborUnits = value;
      }

      /**
       * Gets the value of the estimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateAtCompletionLaborUnits()
      {
         return estimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the estimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateAtCompletionLaborUnits(Double value)
      {
         this.estimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateAtCompletionLaborUnits()
      {
         return cumulativeEstimateAtCompletionLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateAtCompletionLaborUnits(Double value)
      {
         this.cumulativeEstimateAtCompletionLaborUnits = value;
      }

      /**
       * Gets the value of the estimateToCompleteLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateToCompleteLaborUnits()
      {
         return estimateToCompleteLaborUnits;
      }

      /**
       * Sets the value of the estimateToCompleteLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateToCompleteLaborUnits(Double value)
      {
         this.estimateToCompleteLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeEstimateToCompleteLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateToCompleteLaborUnits()
      {
         return cumulativeEstimateToCompleteLaborUnits;
      }

      /**
       * Sets the value of the cumulativeEstimateToCompleteLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateToCompleteLaborUnits(Double value)
      {
         this.cumulativeEstimateToCompleteLaborUnits = value;
      }

      /**
       * Gets the value of the plannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedLaborUnits()
      {
         return plannedLaborUnits;
      }

      /**
       * Sets the value of the plannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedLaborUnits(Double value)
      {
         this.plannedLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedLaborUnits()
      {
         return cumulativePlannedLaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedLaborUnits(Double value)
      {
         this.cumulativePlannedLaborUnits = value;
      }

      /**
       * Gets the value of the plannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedNonLaborUnits()
      {
         return plannedNonLaborUnits;
      }

      /**
       * Sets the value of the plannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedNonLaborUnits(Double value)
      {
         this.plannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedNonLaborUnits()
      {
         return cumulativePlannedNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedNonLaborUnits(Double value)
      {
         this.cumulativePlannedNonLaborUnits = value;
      }

      /**
       * Gets the value of the plannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedValueLaborUnits()
      {
         return plannedValueLaborUnits;
      }

      /**
       * Sets the value of the plannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedValueLaborUnits(Double value)
      {
         this.plannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedValueLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedValueLaborUnits()
      {
         return cumulativePlannedValueLaborUnits;
      }

      /**
       * Sets the value of the cumulativePlannedValueLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedValueLaborUnits(Double value)
      {
         this.cumulativePlannedValueLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLaborUnits()
      {
         return remainingLaborUnits;
      }

      /**
       * Sets the value of the remainingLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLaborUnits(Double value)
      {
         this.remainingLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLaborUnits()
      {
         return cumulativeRemainingLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLaborUnits(Double value)
      {
         this.cumulativeRemainingLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLateLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateLaborUnits()
      {
         return remainingLateLaborUnits;
      }

      /**
       * Sets the value of the remainingLateLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateLaborUnits(Double value)
      {
         this.remainingLateLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateLaborUnits()
      {
         return cumulativeRemainingLateLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateLaborUnits(Double value)
      {
         this.cumulativeRemainingLateLaborUnits = value;
      }

      /**
       * Gets the value of the remainingLateNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateNonLaborUnits()
      {
         return remainingLateNonLaborUnits;
      }

      /**
       * Sets the value of the remainingLateNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateNonLaborUnits(Double value)
      {
         this.remainingLateNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateNonLaborUnits()
      {
         return cumulativeRemainingLateNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateNonLaborUnits(Double value)
      {
         this.cumulativeRemainingLateNonLaborUnits = value;
      }

      /**
       * Gets the value of the remainingNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingNonLaborUnits()
      {
         return remainingNonLaborUnits;
      }

      /**
       * Sets the value of the remainingNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingNonLaborUnits(Double value)
      {
         this.remainingNonLaborUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingNonLaborUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingNonLaborUnits()
      {
         return cumulativeRemainingNonLaborUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingNonLaborUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingNonLaborUnits(Double value)
      {
         this.cumulativeRemainingNonLaborUnits = value;
      }

      /**
       * Gets the value of the actualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualCost()
      {
         return actualCost;
      }

      /**
       * Sets the value of the actualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualCost(Double value)
      {
         this.actualCost = value;
      }

      /**
       * Gets the value of the cumulativeActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualCost()
      {
         return cumulativeActualCost;
      }

      /**
       * Sets the value of the cumulativeActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualCost(Double value)
      {
         this.cumulativeActualCost = value;
      }

      /**
       * Gets the value of the actualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualExpenseCost()
      {
         return actualExpenseCost;
      }

      /**
       * Sets the value of the actualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualExpenseCost(Double value)
      {
         this.actualExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualExpenseCost()
      {
         return cumulativeActualExpenseCost;
      }

      /**
       * Sets the value of the cumulativeActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualExpenseCost(Double value)
      {
         this.cumulativeActualExpenseCost = value;
      }

      /**
       * Gets the value of the actualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualLaborCost()
      {
         return actualLaborCost;
      }

      /**
       * Sets the value of the actualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualLaborCost(Double value)
      {
         this.actualLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualLaborCost()
      {
         return cumulativeActualLaborCost;
      }

      /**
       * Sets the value of the cumulativeActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualLaborCost(Double value)
      {
         this.cumulativeActualLaborCost = value;
      }

      /**
       * Gets the value of the actualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualMaterialCost()
      {
         return actualMaterialCost;
      }

      /**
       * Sets the value of the actualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualMaterialCost(Double value)
      {
         this.actualMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualMaterialCost()
      {
         return cumulativeActualMaterialCost;
      }

      /**
       * Sets the value of the cumulativeActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualMaterialCost(Double value)
      {
         this.cumulativeActualMaterialCost = value;
      }

      /**
       * Gets the value of the actualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualNonLaborCost()
      {
         return actualNonLaborCost;
      }

      /**
       * Sets the value of the actualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualNonLaborCost(Double value)
      {
         this.actualNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualNonLaborCost()
      {
         return cumulativeActualNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualNonLaborCost(Double value)
      {
         this.cumulativeActualNonLaborCost = value;
      }

      /**
       * Gets the value of the actualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualTotalCost()
      {
         return actualTotalCost;
      }

      /**
       * Sets the value of the actualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualTotalCost(Double value)
      {
         this.actualTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualTotalCost()
      {
         return cumulativeActualTotalCost;
      }

      /**
       * Sets the value of the cumulativeActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualTotalCost(Double value)
      {
         this.cumulativeActualTotalCost = value;
      }

      /**
       * Gets the value of the atCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionExpenseCost()
      {
         return atCompletionExpenseCost;
      }

      /**
       * Sets the value of the atCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionExpenseCost(Double value)
      {
         this.atCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionExpenseCost()
      {
         return cumulativeAtCompletionExpenseCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionExpenseCost(Double value)
      {
         this.cumulativeAtCompletionExpenseCost = value;
      }

      /**
       * Gets the value of the atCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionLaborCost()
      {
         return atCompletionLaborCost;
      }

      /**
       * Sets the value of the atCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionLaborCost(Double value)
      {
         this.atCompletionLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionLaborCost()
      {
         return cumulativeAtCompletionLaborCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionLaborCost(Double value)
      {
         this.cumulativeAtCompletionLaborCost = value;
      }

      /**
       * Gets the value of the atCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionMaterialCost()
      {
         return atCompletionMaterialCost;
      }

      /**
       * Sets the value of the atCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionMaterialCost(Double value)
      {
         this.atCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionMaterialCost()
      {
         return cumulativeAtCompletionMaterialCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionMaterialCost(Double value)
      {
         this.cumulativeAtCompletionMaterialCost = value;
      }

      /**
       * Gets the value of the atCompletionNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionNonLaborCost()
      {
         return atCompletionNonLaborCost;
      }

      /**
       * Sets the value of the atCompletionNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionNonLaborCost(Double value)
      {
         this.atCompletionNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionNonLaborCost()
      {
         return cumulativeAtCompletionNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionNonLaborCost(Double value)
      {
         this.cumulativeAtCompletionNonLaborCost = value;
      }

      /**
       * Gets the value of the atCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionTotalCost()
      {
         return atCompletionTotalCost;
      }

      /**
       * Sets the value of the atCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionTotalCost(Double value)
      {
         this.atCompletionTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionTotalCost()
      {
         return cumulativeAtCompletionTotalCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionTotalCost(Double value)
      {
         this.cumulativeAtCompletionTotalCost = value;
      }

      /**
       * Gets the value of the baseline1ActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualExpenseCost()
      {
         return baseline1ActualExpenseCost;
      }

      /**
       * Sets the value of the baseline1ActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualExpenseCost(Double value)
      {
         this.baseline1ActualExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualExpenseCost()
      {
         return cumulativeBaseline1ActualExpenseCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualExpenseCost(Double value)
      {
         this.cumulativeBaseline1ActualExpenseCost = value;
      }

      /**
       * Gets the value of the baseline1ActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualLaborCost()
      {
         return baseline1ActualLaborCost;
      }

      /**
       * Sets the value of the baseline1ActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualLaborCost(Double value)
      {
         this.baseline1ActualLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualLaborCost()
      {
         return cumulativeBaseline1ActualLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualLaborCost(Double value)
      {
         this.cumulativeBaseline1ActualLaborCost = value;
      }

      /**
       * Gets the value of the baseline1ActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualMaterialCost()
      {
         return baseline1ActualMaterialCost;
      }

      /**
       * Sets the value of the baseline1ActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualMaterialCost(Double value)
      {
         this.baseline1ActualMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualMaterialCost()
      {
         return cumulativeBaseline1ActualMaterialCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualMaterialCost(Double value)
      {
         this.cumulativeBaseline1ActualMaterialCost = value;
      }

      /**
       * Gets the value of the baseline1ActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualNonLaborCost()
      {
         return baseline1ActualNonLaborCost;
      }

      /**
       * Sets the value of the baseline1ActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualNonLaborCost(Double value)
      {
         this.baseline1ActualNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualNonLaborCost()
      {
         return cumulativeBaseline1ActualNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualNonLaborCost(Double value)
      {
         this.cumulativeBaseline1ActualNonLaborCost = value;
      }

      /**
       * Gets the value of the baseline1ActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1ActualTotalCost()
      {
         return baseline1ActualTotalCost;
      }

      /**
       * Sets the value of the baseline1ActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1ActualTotalCost(Double value)
      {
         this.baseline1ActualTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1ActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1ActualTotalCost()
      {
         return cumulativeBaseline1ActualTotalCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1ActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1ActualTotalCost(Double value)
      {
         this.cumulativeBaseline1ActualTotalCost = value;
      }

      /**
       * Gets the value of the baseline1PlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedExpenseCost()
      {
         return baseline1PlannedExpenseCost;
      }

      /**
       * Sets the value of the baseline1PlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedExpenseCost(Double value)
      {
         this.baseline1PlannedExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedExpenseCost()
      {
         return cumulativeBaseline1PlannedExpenseCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedExpenseCost(Double value)
      {
         this.cumulativeBaseline1PlannedExpenseCost = value;
      }

      /**
       * Gets the value of the baseline1PlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedLaborCost()
      {
         return baseline1PlannedLaborCost;
      }

      /**
       * Sets the value of the baseline1PlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedLaborCost(Double value)
      {
         this.baseline1PlannedLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedLaborCost()
      {
         return cumulativeBaseline1PlannedLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedLaborCost(Double value)
      {
         this.cumulativeBaseline1PlannedLaborCost = value;
      }

      /**
       * Gets the value of the baseline1PlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedMaterialCost()
      {
         return baseline1PlannedMaterialCost;
      }

      /**
       * Sets the value of the baseline1PlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedMaterialCost(Double value)
      {
         this.baseline1PlannedMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedMaterialCost()
      {
         return cumulativeBaseline1PlannedMaterialCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedMaterialCost(Double value)
      {
         this.cumulativeBaseline1PlannedMaterialCost = value;
      }

      /**
       * Gets the value of the baseline1PlannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedNonLaborCost()
      {
         return baseline1PlannedNonLaborCost;
      }

      /**
       * Sets the value of the baseline1PlannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedNonLaborCost(Double value)
      {
         this.baseline1PlannedNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedNonLaborCost()
      {
         return cumulativeBaseline1PlannedNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedNonLaborCost(Double value)
      {
         this.cumulativeBaseline1PlannedNonLaborCost = value;
      }

      /**
       * Gets the value of the baseline1PlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaseline1PlannedTotalCost()
      {
         return baseline1PlannedTotalCost;
      }

      /**
       * Sets the value of the baseline1PlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaseline1PlannedTotalCost(Double value)
      {
         this.baseline1PlannedTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeBaseline1PlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaseline1PlannedTotalCost()
      {
         return cumulativeBaseline1PlannedTotalCost;
      }

      /**
       * Sets the value of the cumulativeBaseline1PlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaseline1PlannedTotalCost(Double value)
      {
         this.cumulativeBaseline1PlannedTotalCost = value;
      }

      /**
       * Gets the value of the baselineActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualExpenseCost()
      {
         return baselineActualExpenseCost;
      }

      /**
       * Sets the value of the baselineActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualExpenseCost(Double value)
      {
         this.baselineActualExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualExpenseCost()
      {
         return cumulativeBaselineActualExpenseCost;
      }

      /**
       * Sets the value of the cumulativeBaselineActualExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualExpenseCost(Double value)
      {
         this.cumulativeBaselineActualExpenseCost = value;
      }

      /**
       * Gets the value of the baselineActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualLaborCost()
      {
         return baselineActualLaborCost;
      }

      /**
       * Sets the value of the baselineActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualLaborCost(Double value)
      {
         this.baselineActualLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualLaborCost()
      {
         return cumulativeBaselineActualLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselineActualLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualLaborCost(Double value)
      {
         this.cumulativeBaselineActualLaborCost = value;
      }

      /**
       * Gets the value of the baselineActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualMaterialCost()
      {
         return baselineActualMaterialCost;
      }

      /**
       * Sets the value of the baselineActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualMaterialCost(Double value)
      {
         this.baselineActualMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualMaterialCost()
      {
         return cumulativeBaselineActualMaterialCost;
      }

      /**
       * Sets the value of the cumulativeBaselineActualMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualMaterialCost(Double value)
      {
         this.cumulativeBaselineActualMaterialCost = value;
      }

      /**
       * Gets the value of the baselineActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualNonLaborCost()
      {
         return baselineActualNonLaborCost;
      }

      /**
       * Sets the value of the baselineActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualNonLaborCost(Double value)
      {
         this.baselineActualNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualNonLaborCost()
      {
         return cumulativeBaselineActualNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselineActualNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualNonLaborCost(Double value)
      {
         this.cumulativeBaselineActualNonLaborCost = value;
      }

      /**
       * Gets the value of the baselineActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselineActualTotalCost()
      {
         return baselineActualTotalCost;
      }

      /**
       * Sets the value of the baselineActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselineActualTotalCost(Double value)
      {
         this.baselineActualTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselineActualTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselineActualTotalCost()
      {
         return cumulativeBaselineActualTotalCost;
      }

      /**
       * Sets the value of the cumulativeBaselineActualTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselineActualTotalCost(Double value)
      {
         this.cumulativeBaselineActualTotalCost = value;
      }

      /**
       * Gets the value of the baselinePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedExpenseCost()
      {
         return baselinePlannedExpenseCost;
      }

      /**
       * Sets the value of the baselinePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedExpenseCost(Double value)
      {
         this.baselinePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedExpenseCost()
      {
         return cumulativeBaselinePlannedExpenseCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedExpenseCost(Double value)
      {
         this.cumulativeBaselinePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the baselinePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedLaborCost()
      {
         return baselinePlannedLaborCost;
      }

      /**
       * Sets the value of the baselinePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedLaborCost(Double value)
      {
         this.baselinePlannedLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedLaborCost()
      {
         return cumulativeBaselinePlannedLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedLaborCost(Double value)
      {
         this.cumulativeBaselinePlannedLaborCost = value;
      }

      /**
       * Gets the value of the baselinePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedMaterialCost()
      {
         return baselinePlannedMaterialCost;
      }

      /**
       * Sets the value of the baselinePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedMaterialCost(Double value)
      {
         this.baselinePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedMaterialCost()
      {
         return cumulativeBaselinePlannedMaterialCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedMaterialCost(Double value)
      {
         this.cumulativeBaselinePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the baselinePlannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedNonLaborCost()
      {
         return baselinePlannedNonLaborCost;
      }

      /**
       * Sets the value of the baselinePlannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedNonLaborCost(Double value)
      {
         this.baselinePlannedNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedNonLaborCost()
      {
         return cumulativeBaselinePlannedNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedNonLaborCost(Double value)
      {
         this.cumulativeBaselinePlannedNonLaborCost = value;
      }

      /**
       * Gets the value of the baselinePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getBaselinePlannedTotalCost()
      {
         return baselinePlannedTotalCost;
      }

      /**
       * Sets the value of the baselinePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setBaselinePlannedTotalCost(Double value)
      {
         this.baselinePlannedTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeBaselinePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeBaselinePlannedTotalCost()
      {
         return cumulativeBaselinePlannedTotalCost;
      }

      /**
       * Sets the value of the cumulativeBaselinePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeBaselinePlannedTotalCost(Double value)
      {
         this.cumulativeBaselinePlannedTotalCost = value;
      }

      /**
       * Gets the value of the earnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEarnedValueCost()
      {
         return earnedValueCost;
      }

      /**
       * Sets the value of the earnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEarnedValueCost(Double value)
      {
         this.earnedValueCost = value;
      }

      /**
       * Gets the value of the cumulativeEarnedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEarnedValueCost()
      {
         return cumulativeEarnedValueCost;
      }

      /**
       * Sets the value of the cumulativeEarnedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEarnedValueCost(Double value)
      {
         this.cumulativeEarnedValueCost = value;
      }

      /**
       * Gets the value of the estimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateAtCompletionCost()
      {
         return estimateAtCompletionCost;
      }

      /**
       * Sets the value of the estimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateAtCompletionCost(Double value)
      {
         this.estimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeEstimateAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateAtCompletionCost()
      {
         return cumulativeEstimateAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeEstimateAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateAtCompletionCost(Double value)
      {
         this.cumulativeEstimateAtCompletionCost = value;
      }

      /**
       * Gets the value of the estimateToCompleteCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getEstimateToCompleteCost()
      {
         return estimateToCompleteCost;
      }

      /**
       * Sets the value of the estimateToCompleteCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEstimateToCompleteCost(Double value)
      {
         this.estimateToCompleteCost = value;
      }

      /**
       * Gets the value of the cumulativeEstimateToCompleteCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeEstimateToCompleteCost()
      {
         return cumulativeEstimateToCompleteCost;
      }

      /**
       * Sets the value of the cumulativeEstimateToCompleteCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeEstimateToCompleteCost(Double value)
      {
         this.cumulativeEstimateToCompleteCost = value;
      }

      /**
       * Gets the value of the plannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedExpenseCost()
      {
         return plannedExpenseCost;
      }

      /**
       * Sets the value of the plannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedExpenseCost(Double value)
      {
         this.plannedExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedExpenseCost()
      {
         return cumulativePlannedExpenseCost;
      }

      /**
       * Sets the value of the cumulativePlannedExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedExpenseCost(Double value)
      {
         this.cumulativePlannedExpenseCost = value;
      }

      /**
       * Gets the value of the plannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedLaborCost()
      {
         return plannedLaborCost;
      }

      /**
       * Sets the value of the plannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedLaborCost(Double value)
      {
         this.plannedLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedLaborCost()
      {
         return cumulativePlannedLaborCost;
      }

      /**
       * Sets the value of the cumulativePlannedLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedLaborCost(Double value)
      {
         this.cumulativePlannedLaborCost = value;
      }

      /**
       * Gets the value of the plannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedMaterialCost()
      {
         return plannedMaterialCost;
      }

      /**
       * Sets the value of the plannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedMaterialCost(Double value)
      {
         this.plannedMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedMaterialCost()
      {
         return cumulativePlannedMaterialCost;
      }

      /**
       * Sets the value of the cumulativePlannedMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedMaterialCost(Double value)
      {
         this.cumulativePlannedMaterialCost = value;
      }

      /**
       * Gets the value of the plannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedNonLaborCost()
      {
         return plannedNonLaborCost;
      }

      /**
       * Sets the value of the plannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedNonLaborCost(Double value)
      {
         this.plannedNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedNonLaborCost()
      {
         return cumulativePlannedNonLaborCost;
      }

      /**
       * Sets the value of the cumulativePlannedNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedNonLaborCost(Double value)
      {
         this.cumulativePlannedNonLaborCost = value;
      }

      /**
       * Gets the value of the plannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedTotalCost()
      {
         return plannedTotalCost;
      }

      /**
       * Sets the value of the plannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedTotalCost(Double value)
      {
         this.plannedTotalCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedTotalCost()
      {
         return cumulativePlannedTotalCost;
      }

      /**
       * Sets the value of the cumulativePlannedTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedTotalCost(Double value)
      {
         this.cumulativePlannedTotalCost = value;
      }

      /**
       * Gets the value of the plannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedValueCost()
      {
         return plannedValueCost;
      }

      /**
       * Sets the value of the plannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedValueCost(Double value)
      {
         this.plannedValueCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedValueCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedValueCost()
      {
         return cumulativePlannedValueCost;
      }

      /**
       * Sets the value of the cumulativePlannedValueCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedValueCost(Double value)
      {
         this.cumulativePlannedValueCost = value;
      }

      /**
       * Gets the value of the remainingExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingExpenseCost()
      {
         return remainingExpenseCost;
      }

      /**
       * Sets the value of the remainingExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingExpenseCost(Double value)
      {
         this.remainingExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingExpenseCost()
      {
         return cumulativeRemainingExpenseCost;
      }

      /**
       * Sets the value of the cumulativeRemainingExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingExpenseCost(Double value)
      {
         this.cumulativeRemainingExpenseCost = value;
      }

      /**
       * Gets the value of the remainingLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLaborCost()
      {
         return remainingLaborCost;
      }

      /**
       * Sets the value of the remainingLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLaborCost(Double value)
      {
         this.remainingLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLaborCost()
      {
         return cumulativeRemainingLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLaborCost(Double value)
      {
         this.cumulativeRemainingLaborCost = value;
      }

      /**
       * Gets the value of the remainingLateExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateExpenseCost()
      {
         return remainingLateExpenseCost;
      }

      /**
       * Sets the value of the remainingLateExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateExpenseCost(Double value)
      {
         this.remainingLateExpenseCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateExpenseCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateExpenseCost()
      {
         return cumulativeRemainingLateExpenseCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateExpenseCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateExpenseCost(Double value)
      {
         this.cumulativeRemainingLateExpenseCost = value;
      }

      /**
       * Gets the value of the remainingLateLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateLaborCost()
      {
         return remainingLateLaborCost;
      }

      /**
       * Sets the value of the remainingLateLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateLaborCost(Double value)
      {
         this.remainingLateLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateLaborCost()
      {
         return cumulativeRemainingLateLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateLaborCost(Double value)
      {
         this.cumulativeRemainingLateLaborCost = value;
      }

      /**
       * Gets the value of the remainingLateMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateMaterialCost()
      {
         return remainingLateMaterialCost;
      }

      /**
       * Sets the value of the remainingLateMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateMaterialCost(Double value)
      {
         this.remainingLateMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateMaterialCost()
      {
         return cumulativeRemainingLateMaterialCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateMaterialCost(Double value)
      {
         this.cumulativeRemainingLateMaterialCost = value;
      }

      /**
       * Gets the value of the remainingLateNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateNonLaborCost()
      {
         return remainingLateNonLaborCost;
      }

      /**
       * Sets the value of the remainingLateNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateNonLaborCost(Double value)
      {
         this.remainingLateNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateNonLaborCost()
      {
         return cumulativeRemainingLateNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateNonLaborCost(Double value)
      {
         this.cumulativeRemainingLateNonLaborCost = value;
      }

      /**
       * Gets the value of the remainingLateTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateTotalCost()
      {
         return remainingLateTotalCost;
      }

      /**
       * Sets the value of the remainingLateTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateTotalCost(Double value)
      {
         this.remainingLateTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateTotalCost()
      {
         return cumulativeRemainingLateTotalCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateTotalCost(Double value)
      {
         this.cumulativeRemainingLateTotalCost = value;
      }

      /**
       * Gets the value of the remainingMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingMaterialCost()
      {
         return remainingMaterialCost;
      }

      /**
       * Sets the value of the remainingMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingMaterialCost(Double value)
      {
         this.remainingMaterialCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingMaterialCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingMaterialCost()
      {
         return cumulativeRemainingMaterialCost;
      }

      /**
       * Sets the value of the cumulativeRemainingMaterialCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingMaterialCost(Double value)
      {
         this.cumulativeRemainingMaterialCost = value;
      }

      /**
       * Gets the value of the remainingNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingNonLaborCost()
      {
         return remainingNonLaborCost;
      }

      /**
       * Sets the value of the remainingNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingNonLaborCost(Double value)
      {
         this.remainingNonLaborCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingNonLaborCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingNonLaborCost()
      {
         return cumulativeRemainingNonLaborCost;
      }

      /**
       * Sets the value of the cumulativeRemainingNonLaborCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingNonLaborCost(Double value)
      {
         this.cumulativeRemainingNonLaborCost = value;
      }

      /**
       * Gets the value of the remainingTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingTotalCost()
      {
         return remainingTotalCost;
      }

      /**
       * Sets the value of the remainingTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingTotalCost(Double value)
      {
         this.remainingTotalCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingTotalCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingTotalCost()
      {
         return cumulativeRemainingTotalCost;
      }

      /**
       * Sets the value of the cumulativeRemainingTotalCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingTotalCost(Double value)
      {
         this.cumulativeRemainingTotalCost = value;
      }

   }

}
