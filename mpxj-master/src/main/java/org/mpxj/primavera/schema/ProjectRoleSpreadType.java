//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for ProjectRoleSpreadType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ProjectRoleSpreadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="WBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="FinancialPeriodObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="ProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="RoleObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="PeriodType"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Financial Period"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Period" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="Limit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeLimit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "ProjectRoleSpreadType", propOrder =
{
   "wbsObjectId",
   "financialPeriodObjectId",
   "projectObjectId",
   "roleObjectId",
   "startDate",
   "endDate",
   "periodType",
   "period"
}) public class ProjectRoleSpreadType
{

   @XmlElement(name = "WBSObjectId") protected int wbsObjectId;
   @XmlElement(name = "FinancialPeriodObjectId") protected int financialPeriodObjectId;
   @XmlElement(name = "ProjectObjectId") protected int projectObjectId;
   @XmlElement(name = "RoleObjectId") protected int roleObjectId;
   @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
   @XmlElement(name = "PeriodType", required = true) @XmlJavaTypeAdapter(Adapter1.class) protected String periodType;
   @XmlElement(name = "Period") protected List<ProjectRoleSpreadType.Period> period;

   /**
    * Gets the value of the wbsObjectId property.
    *
    */
   public int getWBSObjectId()
   {
      return wbsObjectId;
   }

   /**
    * Sets the value of the wbsObjectId property.
    *
    */
   public void setWBSObjectId(int value)
   {
      this.wbsObjectId = value;
   }

   /**
    * Gets the value of the financialPeriodObjectId property.
    *
    */
   public int getFinancialPeriodObjectId()
   {
      return financialPeriodObjectId;
   }

   /**
    * Sets the value of the financialPeriodObjectId property.
    *
    */
   public void setFinancialPeriodObjectId(int value)
   {
      this.financialPeriodObjectId = value;
   }

   /**
    * Gets the value of the projectObjectId property.
    *
    */
   public int getProjectObjectId()
   {
      return projectObjectId;
   }

   /**
    * Sets the value of the projectObjectId property.
    *
    */
   public void setProjectObjectId(int value)
   {
      this.projectObjectId = value;
   }

   /**
    * Gets the value of the roleObjectId property.
    *
    */
   public int getRoleObjectId()
   {
      return roleObjectId;
   }

   /**
    * Sets the value of the roleObjectId property.
    *
    */
   public void setRoleObjectId(int value)
   {
      this.roleObjectId = value;
   }

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the endDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEndDate()
   {
      return endDate;
   }

   /**
    * Sets the value of the endDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEndDate(LocalDateTime value)
   {
      this.endDate = value;
   }

   /**
    * Gets the value of the periodType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPeriodType()
   {
      return periodType;
   }

   /**
    * Sets the value of the periodType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPeriodType(String value)
   {
      this.periodType = value;
   }

   /**
    * Gets the value of the period property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the period property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getPeriod().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ProjectRoleSpreadType.Period }
    *
    *
    */
   public List<ProjectRoleSpreadType.Period> getPeriod()
   {
      if (period == null)
      {
         period = new ArrayList<>();
      }
      return this.period;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="Limit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeLimit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "startDate",
      "endDate",
      "actualOvertimeUnits",
      "cumulativeActualOvertimeUnits",
      "actualRegularUnits",
      "cumulativeActualRegularUnits",
      "actualUnits",
      "cumulativeActualUnits",
      "atCompletionUnits",
      "cumulativeAtCompletionUnits",
      "limit",
      "cumulativeLimit",
      "periodActualUnits",
      "cumulativePeriodActualUnits",
      "periodAtCompletionUnits",
      "cumulativePeriodAtCompletionUnits",
      "plannedUnits",
      "cumulativePlannedUnits",
      "remainingLateUnits",
      "cumulativeRemainingLateUnits",
      "remainingUnits",
      "cumulativeRemainingUnits",
      "staffedActualOvertimeUnits",
      "cumulativeStaffedActualOvertimeUnits",
      "staffedActualRegularUnits",
      "cumulativeStaffedActualRegularUnits",
      "staffedActualUnits",
      "cumulativeStaffedActualUnits",
      "staffedAtCompletionUnits",
      "cumulativeStaffedAtCompletionUnits",
      "staffedPlannedUnits",
      "cumulativeStaffedPlannedUnits",
      "staffedRemainingLateUnits",
      "cumulativeStaffedRemainingLateUnits",
      "staffedRemainingUnits",
      "cumulativeStaffedRemainingUnits",
      "unstaffedActualOvertimeUnits",
      "cumulativeUnstaffedActualOvertimeUnits",
      "unstaffedActualRegularUnits",
      "cumulativeUnstaffedActualRegularUnits",
      "unstaffedActualUnits",
      "cumulativeUnstaffedActualUnits",
      "unstaffedAtCompletionUnits",
      "cumulativeUnstaffedAtCompletionUnits",
      "unstaffedPlannedUnits",
      "cumulativeUnstaffedPlannedUnits",
      "unstaffedRemainingLateUnits",
      "cumulativeUnstaffedRemainingLateUnits",
      "unstaffedRemainingUnits",
      "cumulativeUnstaffedRemainingUnits",
      "actualCost",
      "cumulativeActualCost",
      "actualOvertimeCost",
      "cumulativeActualOvertimeCost",
      "actualRegularCost",
      "cumulativeActualRegularCost",
      "atCompletionCost",
      "cumulativeAtCompletionCost",
      "periodActualCost",
      "cumulativePeriodActualCost",
      "periodAtCompletionCost",
      "cumulativePeriodAtCompletionCost",
      "plannedCost",
      "cumulativePlannedCost",
      "remainingCost",
      "cumulativeRemainingCost",
      "remainingLateCost",
      "cumulativeRemainingLateCost",
      "staffedActualCost",
      "cumulativeStaffedActualCost",
      "staffedActualOvertimeCost",
      "cumulativeStaffedActualOvertimeCost",
      "staffedActualRegularCost",
      "cumulativeStaffedActualRegularCost",
      "staffedAtCompletionCost",
      "cumulativeStaffedAtCompletionCost",
      "staffedPlannedCost",
      "cumulativeStaffedPlannedCost",
      "staffedRemainingCost",
      "cumulativeStaffedRemainingCost",
      "staffedRemainingLateCost",
      "cumulativeStaffedRemainingLateCost",
      "unstaffedActualCost",
      "cumulativeUnstaffedActualCost",
      "unstaffedActualOvertimeCost",
      "cumulativeUnstaffedActualOvertimeCost",
      "unstaffedActualRegularCost",
      "cumulativeUnstaffedActualRegularCost",
      "unstaffedAtCompletionCost",
      "cumulativeUnstaffedAtCompletionCost",
      "unstaffedPlannedCost",
      "cumulativeUnstaffedPlannedCost",
      "unstaffedRemainingCost",
      "cumulativeUnstaffedRemainingCost",
      "unstaffedRemainingLateCost",
      "cumulativeUnstaffedRemainingLateCost"
   }) public static class Period
   {

      @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
      @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
      @XmlElement(name = "ActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualOvertimeUnits;
      @XmlElement(name = "CumulativeActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualOvertimeUnits;
      @XmlElement(name = "ActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualRegularUnits;
      @XmlElement(name = "CumulativeActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualRegularUnits;
      @XmlElement(name = "ActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualUnits;
      @XmlElement(name = "CumulativeActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualUnits;
      @XmlElement(name = "AtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionUnits;
      @XmlElement(name = "CumulativeAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionUnits;
      @XmlElement(name = "Limit", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double limit;
      @XmlElement(name = "CumulativeLimit", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeLimit;
      @XmlElement(name = "PeriodActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualUnits;
      @XmlElement(name = "CumulativePeriodActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualUnits;
      @XmlElement(name = "PeriodAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionUnits;
      @XmlElement(name = "CumulativePeriodAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionUnits;
      @XmlElement(name = "PlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedUnits;
      @XmlElement(name = "CumulativePlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedUnits;
      @XmlElement(name = "RemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateUnits;
      @XmlElement(name = "CumulativeRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateUnits;
      @XmlElement(name = "RemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingUnits;
      @XmlElement(name = "CumulativeRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingUnits;
      @XmlElement(name = "StaffedActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualOvertimeUnits;
      @XmlElement(name = "CumulativeStaffedActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualOvertimeUnits;
      @XmlElement(name = "StaffedActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualRegularUnits;
      @XmlElement(name = "CumulativeStaffedActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualRegularUnits;
      @XmlElement(name = "StaffedActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualUnits;
      @XmlElement(name = "CumulativeStaffedActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualUnits;
      @XmlElement(name = "StaffedAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedAtCompletionUnits;
      @XmlElement(name = "CumulativeStaffedAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedAtCompletionUnits;
      @XmlElement(name = "StaffedPlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedPlannedUnits;
      @XmlElement(name = "CumulativeStaffedPlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedPlannedUnits;
      @XmlElement(name = "StaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingLateUnits;
      @XmlElement(name = "CumulativeStaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingLateUnits;
      @XmlElement(name = "StaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingUnits;
      @XmlElement(name = "CumulativeStaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingUnits;
      @XmlElement(name = "UnstaffedActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualOvertimeUnits;
      @XmlElement(name = "CumulativeUnstaffedActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualOvertimeUnits;
      @XmlElement(name = "UnstaffedActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualRegularUnits;
      @XmlElement(name = "CumulativeUnstaffedActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualRegularUnits;
      @XmlElement(name = "UnstaffedActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualUnits;
      @XmlElement(name = "CumulativeUnstaffedActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualUnits;
      @XmlElement(name = "UnstaffedAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedAtCompletionUnits;
      @XmlElement(name = "CumulativeUnstaffedAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedAtCompletionUnits;
      @XmlElement(name = "UnstaffedPlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedPlannedUnits;
      @XmlElement(name = "CumulativeUnstaffedPlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedPlannedUnits;
      @XmlElement(name = "UnstaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingLateUnits;
      @XmlElement(name = "CumulativeUnstaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingLateUnits;
      @XmlElement(name = "UnstaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingUnits;
      @XmlElement(name = "CumulativeUnstaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingUnits;
      @XmlElement(name = "ActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualCost;
      @XmlElement(name = "CumulativeActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualCost;
      @XmlElement(name = "ActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualOvertimeCost;
      @XmlElement(name = "CumulativeActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualOvertimeCost;
      @XmlElement(name = "ActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualRegularCost;
      @XmlElement(name = "CumulativeActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualRegularCost;
      @XmlElement(name = "AtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionCost;
      @XmlElement(name = "CumulativeAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionCost;
      @XmlElement(name = "PeriodActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodActualCost;
      @XmlElement(name = "CumulativePeriodActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodActualCost;
      @XmlElement(name = "PeriodAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double periodAtCompletionCost;
      @XmlElement(name = "CumulativePeriodAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePeriodAtCompletionCost;
      @XmlElement(name = "PlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedCost;
      @XmlElement(name = "CumulativePlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedCost;
      @XmlElement(name = "RemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingCost;
      @XmlElement(name = "CumulativeRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingCost;
      @XmlElement(name = "RemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateCost;
      @XmlElement(name = "CumulativeRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateCost;
      @XmlElement(name = "StaffedActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualCost;
      @XmlElement(name = "CumulativeStaffedActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualCost;
      @XmlElement(name = "StaffedActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualOvertimeCost;
      @XmlElement(name = "CumulativeStaffedActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualOvertimeCost;
      @XmlElement(name = "StaffedActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedActualRegularCost;
      @XmlElement(name = "CumulativeStaffedActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedActualRegularCost;
      @XmlElement(name = "StaffedAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedAtCompletionCost;
      @XmlElement(name = "CumulativeStaffedAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedAtCompletionCost;
      @XmlElement(name = "StaffedPlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedPlannedCost;
      @XmlElement(name = "CumulativeStaffedPlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedPlannedCost;
      @XmlElement(name = "StaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingCost;
      @XmlElement(name = "CumulativeStaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingCost;
      @XmlElement(name = "StaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingLateCost;
      @XmlElement(name = "CumulativeStaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingLateCost;
      @XmlElement(name = "UnstaffedActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualCost;
      @XmlElement(name = "CumulativeUnstaffedActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualCost;
      @XmlElement(name = "UnstaffedActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualOvertimeCost;
      @XmlElement(name = "CumulativeUnstaffedActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualOvertimeCost;
      @XmlElement(name = "UnstaffedActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedActualRegularCost;
      @XmlElement(name = "CumulativeUnstaffedActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedActualRegularCost;
      @XmlElement(name = "UnstaffedAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedAtCompletionCost;
      @XmlElement(name = "CumulativeUnstaffedAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedAtCompletionCost;
      @XmlElement(name = "UnstaffedPlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedPlannedCost;
      @XmlElement(name = "CumulativeUnstaffedPlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedPlannedCost;
      @XmlElement(name = "UnstaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingCost;
      @XmlElement(name = "CumulativeUnstaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingCost;
      @XmlElement(name = "UnstaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingLateCost;
      @XmlElement(name = "CumulativeUnstaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingLateCost;

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStartDate(LocalDateTime value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the endDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getEndDate()
      {
         return endDate;
      }

      /**
       * Sets the value of the endDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEndDate(LocalDateTime value)
      {
         this.endDate = value;
      }

      /**
       * Gets the value of the actualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualOvertimeUnits()
      {
         return actualOvertimeUnits;
      }

      /**
       * Sets the value of the actualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualOvertimeUnits(Double value)
      {
         this.actualOvertimeUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualOvertimeUnits()
      {
         return cumulativeActualOvertimeUnits;
      }

      /**
       * Sets the value of the cumulativeActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualOvertimeUnits(Double value)
      {
         this.cumulativeActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the actualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualRegularUnits()
      {
         return actualRegularUnits;
      }

      /**
       * Sets the value of the actualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualRegularUnits(Double value)
      {
         this.actualRegularUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualRegularUnits()
      {
         return cumulativeActualRegularUnits;
      }

      /**
       * Sets the value of the cumulativeActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualRegularUnits(Double value)
      {
         this.cumulativeActualRegularUnits = value;
      }

      /**
       * Gets the value of the actualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualUnits()
      {
         return actualUnits;
      }

      /**
       * Sets the value of the actualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualUnits(Double value)
      {
         this.actualUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualUnits()
      {
         return cumulativeActualUnits;
      }

      /**
       * Sets the value of the cumulativeActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualUnits(Double value)
      {
         this.cumulativeActualUnits = value;
      }

      /**
       * Gets the value of the atCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionUnits()
      {
         return atCompletionUnits;
      }

      /**
       * Sets the value of the atCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionUnits(Double value)
      {
         this.atCompletionUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionUnits()
      {
         return cumulativeAtCompletionUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionUnits(Double value)
      {
         this.cumulativeAtCompletionUnits = value;
      }

      /**
       * Gets the value of the limit property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getLimit()
      {
         return limit;
      }

      /**
       * Sets the value of the limit property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLimit(Double value)
      {
         this.limit = value;
      }

      /**
       * Gets the value of the cumulativeLimit property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeLimit()
      {
         return cumulativeLimit;
      }

      /**
       * Sets the value of the cumulativeLimit property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeLimit(Double value)
      {
         this.cumulativeLimit = value;
      }

      /**
       * Gets the value of the periodActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualUnits()
      {
         return periodActualUnits;
      }

      /**
       * Sets the value of the periodActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualUnits(Double value)
      {
         this.periodActualUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualUnits()
      {
         return cumulativePeriodActualUnits;
      }

      /**
       * Sets the value of the cumulativePeriodActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualUnits(Double value)
      {
         this.cumulativePeriodActualUnits = value;
      }

      /**
       * Gets the value of the periodAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionUnits()
      {
         return periodAtCompletionUnits;
      }

      /**
       * Sets the value of the periodAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionUnits(Double value)
      {
         this.periodAtCompletionUnits = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionUnits()
      {
         return cumulativePeriodAtCompletionUnits;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionUnits(Double value)
      {
         this.cumulativePeriodAtCompletionUnits = value;
      }

      /**
       * Gets the value of the plannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedUnits()
      {
         return plannedUnits;
      }

      /**
       * Sets the value of the plannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedUnits(Double value)
      {
         this.plannedUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedUnits()
      {
         return cumulativePlannedUnits;
      }

      /**
       * Sets the value of the cumulativePlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedUnits(Double value)
      {
         this.cumulativePlannedUnits = value;
      }

      /**
       * Gets the value of the remainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateUnits()
      {
         return remainingLateUnits;
      }

      /**
       * Sets the value of the remainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateUnits(Double value)
      {
         this.remainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateUnits()
      {
         return cumulativeRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateUnits(Double value)
      {
         this.cumulativeRemainingLateUnits = value;
      }

      /**
       * Gets the value of the remainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingUnits()
      {
         return remainingUnits;
      }

      /**
       * Sets the value of the remainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingUnits(Double value)
      {
         this.remainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingUnits()
      {
         return cumulativeRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingUnits(Double value)
      {
         this.cumulativeRemainingUnits = value;
      }

      /**
       * Gets the value of the staffedActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualOvertimeUnits()
      {
         return staffedActualOvertimeUnits;
      }

      /**
       * Sets the value of the staffedActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualOvertimeUnits(Double value)
      {
         this.staffedActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualOvertimeUnits()
      {
         return cumulativeStaffedActualOvertimeUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualOvertimeUnits(Double value)
      {
         this.cumulativeStaffedActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the staffedActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualRegularUnits()
      {
         return staffedActualRegularUnits;
      }

      /**
       * Sets the value of the staffedActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualRegularUnits(Double value)
      {
         this.staffedActualRegularUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualRegularUnits()
      {
         return cumulativeStaffedActualRegularUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualRegularUnits(Double value)
      {
         this.cumulativeStaffedActualRegularUnits = value;
      }

      /**
       * Gets the value of the staffedActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualUnits()
      {
         return staffedActualUnits;
      }

      /**
       * Sets the value of the staffedActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualUnits(Double value)
      {
         this.staffedActualUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualUnits()
      {
         return cumulativeStaffedActualUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualUnits(Double value)
      {
         this.cumulativeStaffedActualUnits = value;
      }

      /**
       * Gets the value of the staffedAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedAtCompletionUnits()
      {
         return staffedAtCompletionUnits;
      }

      /**
       * Sets the value of the staffedAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedAtCompletionUnits(Double value)
      {
         this.staffedAtCompletionUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedAtCompletionUnits()
      {
         return cumulativeStaffedAtCompletionUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedAtCompletionUnits(Double value)
      {
         this.cumulativeStaffedAtCompletionUnits = value;
      }

      /**
       * Gets the value of the staffedPlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedPlannedUnits()
      {
         return staffedPlannedUnits;
      }

      /**
       * Sets the value of the staffedPlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedPlannedUnits(Double value)
      {
         this.staffedPlannedUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedPlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedPlannedUnits()
      {
         return cumulativeStaffedPlannedUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedPlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedPlannedUnits(Double value)
      {
         this.cumulativeStaffedPlannedUnits = value;
      }

      /**
       * Gets the value of the staffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingLateUnits()
      {
         return staffedRemainingLateUnits;
      }

      /**
       * Sets the value of the staffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingLateUnits(Double value)
      {
         this.staffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingLateUnits()
      {
         return cumulativeStaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingLateUnits(Double value)
      {
         this.cumulativeStaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the staffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingUnits()
      {
         return staffedRemainingUnits;
      }

      /**
       * Sets the value of the staffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingUnits(Double value)
      {
         this.staffedRemainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingUnits()
      {
         return cumulativeStaffedRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingUnits(Double value)
      {
         this.cumulativeStaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the unstaffedActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualOvertimeUnits()
      {
         return unstaffedActualOvertimeUnits;
      }

      /**
       * Sets the value of the unstaffedActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualOvertimeUnits(Double value)
      {
         this.unstaffedActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualOvertimeUnits()
      {
         return cumulativeUnstaffedActualOvertimeUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualOvertimeUnits(Double value)
      {
         this.cumulativeUnstaffedActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the unstaffedActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualRegularUnits()
      {
         return unstaffedActualRegularUnits;
      }

      /**
       * Sets the value of the unstaffedActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualRegularUnits(Double value)
      {
         this.unstaffedActualRegularUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualRegularUnits()
      {
         return cumulativeUnstaffedActualRegularUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualRegularUnits(Double value)
      {
         this.cumulativeUnstaffedActualRegularUnits = value;
      }

      /**
       * Gets the value of the unstaffedActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualUnits()
      {
         return unstaffedActualUnits;
      }

      /**
       * Sets the value of the unstaffedActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualUnits(Double value)
      {
         this.unstaffedActualUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualUnits()
      {
         return cumulativeUnstaffedActualUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualUnits(Double value)
      {
         this.cumulativeUnstaffedActualUnits = value;
      }

      /**
       * Gets the value of the unstaffedAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedAtCompletionUnits()
      {
         return unstaffedAtCompletionUnits;
      }

      /**
       * Sets the value of the unstaffedAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedAtCompletionUnits(Double value)
      {
         this.unstaffedAtCompletionUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedAtCompletionUnits()
      {
         return cumulativeUnstaffedAtCompletionUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedAtCompletionUnits(Double value)
      {
         this.cumulativeUnstaffedAtCompletionUnits = value;
      }

      /**
       * Gets the value of the unstaffedPlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedPlannedUnits()
      {
         return unstaffedPlannedUnits;
      }

      /**
       * Sets the value of the unstaffedPlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedPlannedUnits(Double value)
      {
         this.unstaffedPlannedUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedPlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedPlannedUnits()
      {
         return cumulativeUnstaffedPlannedUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedPlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedPlannedUnits(Double value)
      {
         this.cumulativeUnstaffedPlannedUnits = value;
      }

      /**
       * Gets the value of the unstaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingLateUnits()
      {
         return unstaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the unstaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingLateUnits(Double value)
      {
         this.unstaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingLateUnits()
      {
         return cumulativeUnstaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingLateUnits(Double value)
      {
         this.cumulativeUnstaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the unstaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingUnits()
      {
         return unstaffedRemainingUnits;
      }

      /**
       * Sets the value of the unstaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingUnits(Double value)
      {
         this.unstaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingUnits()
      {
         return cumulativeUnstaffedRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingUnits(Double value)
      {
         this.cumulativeUnstaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the actualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualCost()
      {
         return actualCost;
      }

      /**
       * Sets the value of the actualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualCost(Double value)
      {
         this.actualCost = value;
      }

      /**
       * Gets the value of the cumulativeActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualCost()
      {
         return cumulativeActualCost;
      }

      /**
       * Sets the value of the cumulativeActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualCost(Double value)
      {
         this.cumulativeActualCost = value;
      }

      /**
       * Gets the value of the actualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualOvertimeCost()
      {
         return actualOvertimeCost;
      }

      /**
       * Sets the value of the actualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualOvertimeCost(Double value)
      {
         this.actualOvertimeCost = value;
      }

      /**
       * Gets the value of the cumulativeActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualOvertimeCost()
      {
         return cumulativeActualOvertimeCost;
      }

      /**
       * Sets the value of the cumulativeActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualOvertimeCost(Double value)
      {
         this.cumulativeActualOvertimeCost = value;
      }

      /**
       * Gets the value of the actualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualRegularCost()
      {
         return actualRegularCost;
      }

      /**
       * Sets the value of the actualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualRegularCost(Double value)
      {
         this.actualRegularCost = value;
      }

      /**
       * Gets the value of the cumulativeActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualRegularCost()
      {
         return cumulativeActualRegularCost;
      }

      /**
       * Sets the value of the cumulativeActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualRegularCost(Double value)
      {
         this.cumulativeActualRegularCost = value;
      }

      /**
       * Gets the value of the atCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionCost()
      {
         return atCompletionCost;
      }

      /**
       * Sets the value of the atCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionCost(Double value)
      {
         this.atCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionCost()
      {
         return cumulativeAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionCost(Double value)
      {
         this.cumulativeAtCompletionCost = value;
      }

      /**
       * Gets the value of the periodActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodActualCost()
      {
         return periodActualCost;
      }

      /**
       * Sets the value of the periodActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodActualCost(Double value)
      {
         this.periodActualCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodActualCost()
      {
         return cumulativePeriodActualCost;
      }

      /**
       * Sets the value of the cumulativePeriodActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodActualCost(Double value)
      {
         this.cumulativePeriodActualCost = value;
      }

      /**
       * Gets the value of the periodAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPeriodAtCompletionCost()
      {
         return periodAtCompletionCost;
      }

      /**
       * Sets the value of the periodAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPeriodAtCompletionCost(Double value)
      {
         this.periodAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativePeriodAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePeriodAtCompletionCost()
      {
         return cumulativePeriodAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativePeriodAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePeriodAtCompletionCost(Double value)
      {
         this.cumulativePeriodAtCompletionCost = value;
      }

      /**
       * Gets the value of the plannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedCost()
      {
         return plannedCost;
      }

      /**
       * Sets the value of the plannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedCost(Double value)
      {
         this.plannedCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedCost()
      {
         return cumulativePlannedCost;
      }

      /**
       * Sets the value of the cumulativePlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedCost(Double value)
      {
         this.cumulativePlannedCost = value;
      }

      /**
       * Gets the value of the remainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingCost()
      {
         return remainingCost;
      }

      /**
       * Sets the value of the remainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingCost(Double value)
      {
         this.remainingCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingCost()
      {
         return cumulativeRemainingCost;
      }

      /**
       * Sets the value of the cumulativeRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingCost(Double value)
      {
         this.cumulativeRemainingCost = value;
      }

      /**
       * Gets the value of the remainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateCost()
      {
         return remainingLateCost;
      }

      /**
       * Sets the value of the remainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateCost(Double value)
      {
         this.remainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateCost()
      {
         return cumulativeRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateCost(Double value)
      {
         this.cumulativeRemainingLateCost = value;
      }

      /**
       * Gets the value of the staffedActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualCost()
      {
         return staffedActualCost;
      }

      /**
       * Sets the value of the staffedActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualCost(Double value)
      {
         this.staffedActualCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualCost()
      {
         return cumulativeStaffedActualCost;
      }

      /**
       * Sets the value of the cumulativeStaffedActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualCost(Double value)
      {
         this.cumulativeStaffedActualCost = value;
      }

      /**
       * Gets the value of the staffedActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualOvertimeCost()
      {
         return staffedActualOvertimeCost;
      }

      /**
       * Sets the value of the staffedActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualOvertimeCost(Double value)
      {
         this.staffedActualOvertimeCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualOvertimeCost()
      {
         return cumulativeStaffedActualOvertimeCost;
      }

      /**
       * Sets the value of the cumulativeStaffedActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualOvertimeCost(Double value)
      {
         this.cumulativeStaffedActualOvertimeCost = value;
      }

      /**
       * Gets the value of the staffedActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedActualRegularCost()
      {
         return staffedActualRegularCost;
      }

      /**
       * Sets the value of the staffedActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedActualRegularCost(Double value)
      {
         this.staffedActualRegularCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedActualRegularCost()
      {
         return cumulativeStaffedActualRegularCost;
      }

      /**
       * Sets the value of the cumulativeStaffedActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedActualRegularCost(Double value)
      {
         this.cumulativeStaffedActualRegularCost = value;
      }

      /**
       * Gets the value of the staffedAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedAtCompletionCost()
      {
         return staffedAtCompletionCost;
      }

      /**
       * Sets the value of the staffedAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedAtCompletionCost(Double value)
      {
         this.staffedAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedAtCompletionCost()
      {
         return cumulativeStaffedAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeStaffedAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedAtCompletionCost(Double value)
      {
         this.cumulativeStaffedAtCompletionCost = value;
      }

      /**
       * Gets the value of the staffedPlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedPlannedCost()
      {
         return staffedPlannedCost;
      }

      /**
       * Sets the value of the staffedPlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedPlannedCost(Double value)
      {
         this.staffedPlannedCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedPlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedPlannedCost()
      {
         return cumulativeStaffedPlannedCost;
      }

      /**
       * Sets the value of the cumulativeStaffedPlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedPlannedCost(Double value)
      {
         this.cumulativeStaffedPlannedCost = value;
      }

      /**
       * Gets the value of the staffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingCost()
      {
         return staffedRemainingCost;
      }

      /**
       * Sets the value of the staffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingCost(Double value)
      {
         this.staffedRemainingCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingCost()
      {
         return cumulativeStaffedRemainingCost;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingCost(Double value)
      {
         this.cumulativeStaffedRemainingCost = value;
      }

      /**
       * Gets the value of the staffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingLateCost()
      {
         return staffedRemainingLateCost;
      }

      /**
       * Sets the value of the staffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingLateCost(Double value)
      {
         this.staffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingLateCost()
      {
         return cumulativeStaffedRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingLateCost(Double value)
      {
         this.cumulativeStaffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the unstaffedActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualCost()
      {
         return unstaffedActualCost;
      }

      /**
       * Sets the value of the unstaffedActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualCost(Double value)
      {
         this.unstaffedActualCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualCost()
      {
         return cumulativeUnstaffedActualCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualCost(Double value)
      {
         this.cumulativeUnstaffedActualCost = value;
      }

      /**
       * Gets the value of the unstaffedActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualOvertimeCost()
      {
         return unstaffedActualOvertimeCost;
      }

      /**
       * Sets the value of the unstaffedActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualOvertimeCost(Double value)
      {
         this.unstaffedActualOvertimeCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualOvertimeCost()
      {
         return cumulativeUnstaffedActualOvertimeCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualOvertimeCost(Double value)
      {
         this.cumulativeUnstaffedActualOvertimeCost = value;
      }

      /**
       * Gets the value of the unstaffedActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedActualRegularCost()
      {
         return unstaffedActualRegularCost;
      }

      /**
       * Sets the value of the unstaffedActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedActualRegularCost(Double value)
      {
         this.unstaffedActualRegularCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedActualRegularCost()
      {
         return cumulativeUnstaffedActualRegularCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedActualRegularCost(Double value)
      {
         this.cumulativeUnstaffedActualRegularCost = value;
      }

      /**
       * Gets the value of the unstaffedAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedAtCompletionCost()
      {
         return unstaffedAtCompletionCost;
      }

      /**
       * Sets the value of the unstaffedAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedAtCompletionCost(Double value)
      {
         this.unstaffedAtCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedAtCompletionCost()
      {
         return cumulativeUnstaffedAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedAtCompletionCost(Double value)
      {
         this.cumulativeUnstaffedAtCompletionCost = value;
      }

      /**
       * Gets the value of the unstaffedPlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedPlannedCost()
      {
         return unstaffedPlannedCost;
      }

      /**
       * Sets the value of the unstaffedPlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedPlannedCost(Double value)
      {
         this.unstaffedPlannedCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedPlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedPlannedCost()
      {
         return cumulativeUnstaffedPlannedCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedPlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedPlannedCost(Double value)
      {
         this.cumulativeUnstaffedPlannedCost = value;
      }

      /**
       * Gets the value of the unstaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingCost()
      {
         return unstaffedRemainingCost;
      }

      /**
       * Sets the value of the unstaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingCost(Double value)
      {
         this.unstaffedRemainingCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingCost()
      {
         return cumulativeUnstaffedRemainingCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingCost(Double value)
      {
         this.cumulativeUnstaffedRemainingCost = value;
      }

      /**
       * Gets the value of the unstaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingLateCost()
      {
         return unstaffedRemainingLateCost;
      }

      /**
       * Sets the value of the unstaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingLateCost(Double value)
      {
         this.unstaffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingLateCost()
      {
         return cumulativeUnstaffedRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingLateCost(Double value)
      {
         this.cumulativeUnstaffedRemainingLateCost = value;
      }

   }

}
