//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for ActivityType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ActivityType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="AccountingVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AccountingVarianceLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActivityOwnerUserId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ActualDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ActualThisPeriodLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualThisPeriodLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualThisPeriodMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualThisPeriodNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualThisPeriodNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ActualTotalUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionLaborUnitsVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionTotalUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AtCompletionVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="AutoComputeActuals" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1Duration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline1StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2Duration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline2StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3Duration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Baseline3StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="BaselineDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselineFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="BaselineStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="BudgetAtCompletion" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CBSCode" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="1024"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CBSId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CalendarName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CalendarObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostPercentOfPlanned" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostPerformanceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostPerformanceIndexLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostVarianceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostVarianceIndexLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CostVarianceLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DataDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Duration1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Duration2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Duration3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="DurationPercentComplete" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DurationPercentOfPlanned" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="DurationType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Fixed Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units/Time"/&gt;
 *               &lt;enumeration value="Fixed Units"/&gt;
 *               &lt;enumeration value="Fixed Duration and Units"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DurationVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EarlyFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="EarlyStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EstimateToComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="EstimatedWeight" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExpectedFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ExpenseCost1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExpenseCost2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExpenseCost3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExpenseCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExpenseCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ExternalEarlyStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ExternalLateFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="Feedback" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="FinancialPeriodTmplId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="FinishDate1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="FinishDate2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="FinishDate3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="FinishDateVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="FloatPath" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="FloatPathOrder" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="FreeFloat" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="GUID" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;pattern value="\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}|"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HasFutureBucketData" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="Id" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="IsBaseline" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsCritical" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsLongestPath" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsNewFeedback" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsStarred" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsTemplate" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="IsWorkPackage" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LaborCost1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborCost2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborCost3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborUnits1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborUnits2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborUnits3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborUnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LaborUnitsVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LateFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LateStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LevelingPriority" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Top"/&gt;
 *               &lt;enumeration value="High"/&gt;
 *               &lt;enumeration value="Normal"/&gt;
 *               &lt;enumeration value="Low"/&gt;
 *               &lt;enumeration value="Lowest"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LocationName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LocationObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="MaterialCost1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="MaterialCost2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="MaterialCost3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="MaterialCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="MaterialCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="MaximumDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MinimumDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MostLikelyDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Name" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="120"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="NonLaborCost1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborCost2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborCost3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborCostPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborUnits1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborUnits2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborUnits3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborUnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NonLaborUnitsVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="NotesToResources" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="OwnerIDArray" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="OwnerNamesArray" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="PercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PercentCompleteType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Physical"/&gt;
 *               &lt;enumeration value="Duration"/&gt;
 *               &lt;enumeration value="Units"/&gt;
 *               &lt;enumeration value="Scope"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PerformancePercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PerformancePercentCompleteByLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PhysicalPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedTotalUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PostRespCriticalityIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PostResponsePessimisticFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PostResponsePessimisticStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PreRespCriticalityIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="PreResponsePessimisticFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PreResponsePessimisticStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PrimaryConstraintDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="PrimaryConstraintType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Start On"/&gt;
 *               &lt;enumeration value="Start On or Before"/&gt;
 *               &lt;enumeration value="Start On or After"/&gt;
 *               &lt;enumeration value="Finish On"/&gt;
 *               &lt;enumeration value="Finish On or Before"/&gt;
 *               &lt;enumeration value="Finish On or After"/&gt;
 *               &lt;enumeration value="As Late As Possible"/&gt;
 *               &lt;enumeration value="Mandatory Start"/&gt;
 *               &lt;enumeration value="Mandatory Finish"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrimaryResourceId" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrimaryResourceName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrimaryResourceObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ProjectFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProjectId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProjectName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProjectNameSepChar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ProjectProjectFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="RemainingDuration" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RemainingEarlyFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="RemainingEarlyStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingFloat" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingLateFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="RemainingLateStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="RemainingTotalUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ResumeDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ReviewFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="ReviewRequired" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ReviewStatus" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="OK"/&gt;
 *               &lt;enumeration value="For Review"/&gt;
 *               &lt;enumeration value="Rejected"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SchedulePercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SchedulePerformanceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SchedulePerformanceIndexLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ScheduleVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ScheduleVarianceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ScheduleVarianceIndexLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ScheduleVarianceLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="ScopePercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="SecondaryConstraintDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="SecondaryConstraintType" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Start On"/&gt;
 *               &lt;enumeration value="Start On or Before"/&gt;
 *               &lt;enumeration value="Start On or After"/&gt;
 *               &lt;enumeration value="Finish On"/&gt;
 *               &lt;enumeration value="Finish On or Before"/&gt;
 *               &lt;enumeration value="Finish On or After"/&gt;
 *               &lt;enumeration value="As Late As Possible"/&gt;
 *               &lt;enumeration value="Mandatory Start"/&gt;
 *               &lt;enumeration value="Mandatory Finish"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="StartDate1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="StartDate2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="StartDate3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="StartDateVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Status" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Not Started"/&gt;
 *               &lt;enumeration value="In Progress"/&gt;
 *               &lt;enumeration value="Completed"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="StatusCode" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="Planned"/&gt;
 *               &lt;enumeration value="Active"/&gt;
 *               &lt;enumeration value="Inactive"/&gt;
 *               &lt;enumeration value="What-If"/&gt;
 *               &lt;enumeration value="Requested"/&gt;
 *               &lt;enumeration value="Template"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="SuspendDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="TaskStatusCompletion" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="BOTH_NOT_COMPLETE"/&gt;
 *               &lt;enumeration value="TASKS_COMPLETE_ACTIVITY_NOT"/&gt;
 *               &lt;enumeration value="ACTIVITY_COMPLETE_TASKS_NOT"/&gt;
 *               &lt;enumeration value="BOTH_COMPLETE"/&gt;
 *               &lt;enumeration value="NO_TASKS"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TaskStatusDates" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value=""/&gt;
 *               &lt;enumeration value="AT_LEAST_ONE_OUTSIDE"/&gt;
 *               &lt;enumeration value="ALL_WITHIN"/&gt;
 *               &lt;enumeration value="NO_TASK_DATES"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TaskStatusIndicator" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="ToCompletePerformanceIndex" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalCost1Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalCost2Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalCost3Variance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalCostVariance" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalFloat" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodEarnedValueCostBCWP" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TotalPastPeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="Type" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Task Dependent"/&gt;
 *               &lt;enumeration value="Resource Dependent"/&gt;
 *               &lt;enumeration value="Level of Effort"/&gt;
 *               &lt;enumeration value="Start Milestone"/&gt;
 *               &lt;enumeration value="Finish Milestone"/&gt;
 *               &lt;enumeration value="WBS Summary"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="UnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="UnreadCommentCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="WBSCode" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WBSName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WBSNamePath" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="WBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="WBSPath" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="WorkPackageId" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WorkPackageName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Code" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}CodeAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="UDF" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}UDFAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="Spread" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ActivitySpreadType" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "ActivityType", propOrder =
{
   "accountingVariance",
   "accountingVarianceLaborUnits",
   "activityOwnerUserId",
   "actualDuration",
   "actualExpenseCost",
   "actualFinishDate",
   "actualLaborCost",
   "actualLaborUnits",
   "actualMaterialCost",
   "actualNonLaborCost",
   "actualNonLaborUnits",
   "actualStartDate",
   "actualThisPeriodLaborCost",
   "actualThisPeriodLaborUnits",
   "actualThisPeriodMaterialCost",
   "actualThisPeriodNonLaborCost",
   "actualThisPeriodNonLaborUnits",
   "actualTotalCost",
   "actualTotalUnits",
   "atCompletionDuration",
   "atCompletionExpenseCost",
   "atCompletionLaborCost",
   "atCompletionLaborUnits",
   "atCompletionLaborUnitsVariance",
   "atCompletionMaterialCost",
   "atCompletionNonLaborCost",
   "atCompletionNonLaborUnits",
   "atCompletionTotalCost",
   "atCompletionTotalUnits",
   "atCompletionVariance",
   "autoComputeActuals",
   "baseline1Duration",
   "baseline1FinishDate",
   "baseline1PlannedDuration",
   "baseline1PlannedExpenseCost",
   "baseline1PlannedLaborCost",
   "baseline1PlannedLaborUnits",
   "baseline1PlannedMaterialCost",
   "baseline1PlannedNonLaborCost",
   "baseline1PlannedNonLaborUnits",
   "baseline1PlannedTotalCost",
   "baseline1StartDate",
   "baseline2Duration",
   "baseline2FinishDate",
   "baseline2PlannedDuration",
   "baseline2PlannedExpenseCost",
   "baseline2PlannedLaborCost",
   "baseline2PlannedLaborUnits",
   "baseline2PlannedMaterialCost",
   "baseline2PlannedNonLaborCost",
   "baseline2PlannedNonLaborUnits",
   "baseline2PlannedTotalCost",
   "baseline2StartDate",
   "baseline3Duration",
   "baseline3FinishDate",
   "baseline3PlannedDuration",
   "baseline3PlannedExpenseCost",
   "baseline3PlannedLaborCost",
   "baseline3PlannedLaborUnits",
   "baseline3PlannedMaterialCost",
   "baseline3PlannedNonLaborCost",
   "baseline3PlannedNonLaborUnits",
   "baseline3PlannedTotalCost",
   "baseline3StartDate",
   "baselineDuration",
   "baselineFinishDate",
   "baselinePlannedDuration",
   "baselinePlannedExpenseCost",
   "baselinePlannedLaborCost",
   "baselinePlannedLaborUnits",
   "baselinePlannedMaterialCost",
   "baselinePlannedNonLaborCost",
   "baselinePlannedNonLaborUnits",
   "baselinePlannedTotalCost",
   "baselineStartDate",
   "budgetAtCompletion",
   "cbsCode",
   "cbsId",
   "cbsObjectId",
   "calendarName",
   "calendarObjectId",
   "costPercentComplete",
   "costPercentOfPlanned",
   "costPerformanceIndex",
   "costPerformanceIndexLaborUnits",
   "costVariance",
   "costVarianceIndex",
   "costVarianceIndexLaborUnits",
   "costVarianceLaborUnits",
   "createDate",
   "createUser",
   "dataDate",
   "duration1Variance",
   "duration2Variance",
   "duration3Variance",
   "durationPercentComplete",
   "durationPercentOfPlanned",
   "durationType",
   "durationVariance",
   "earlyFinishDate",
   "earlyStartDate",
   "earnedValueCost",
   "earnedValueLaborUnits",
   "estimateAtCompletionCost",
   "estimateAtCompletionLaborUnits",
   "estimateToComplete",
   "estimateToCompleteLaborUnits",
   "estimatedWeight",
   "expectedFinishDate",
   "expenseCost1Variance",
   "expenseCost2Variance",
   "expenseCost3Variance",
   "expenseCostPercentComplete",
   "expenseCostVariance",
   "externalEarlyStartDate",
   "externalLateFinishDate",
   "feedback",
   "financialPeriodTmplId",
   "finishDate",
   "finishDate1Variance",
   "finishDate2Variance",
   "finishDate3Variance",
   "finishDateVariance",
   "floatPath",
   "floatPathOrder",
   "freeFloat",
   "guid",
   "hasFutureBucketData",
   "id",
   "isBaseline",
   "isCritical",
   "isLongestPath",
   "isNewFeedback",
   "isStarred",
   "isTemplate",
   "isWorkPackage",
   "laborCost1Variance",
   "laborCost2Variance",
   "laborCost3Variance",
   "laborCostPercentComplete",
   "laborCostVariance",
   "laborUnits1Variance",
   "laborUnits2Variance",
   "laborUnits3Variance",
   "laborUnitsPercentComplete",
   "laborUnitsVariance",
   "lastUpdateDate",
   "lastUpdateUser",
   "lateFinishDate",
   "lateStartDate",
   "levelingPriority",
   "locationName",
   "locationObjectId",
   "materialCost1Variance",
   "materialCost2Variance",
   "materialCost3Variance",
   "materialCostPercentComplete",
   "materialCostVariance",
   "maximumDuration",
   "minimumDuration",
   "mostLikelyDuration",
   "name",
   "nonLaborCost1Variance",
   "nonLaborCost2Variance",
   "nonLaborCost3Variance",
   "nonLaborCostPercentComplete",
   "nonLaborCostVariance",
   "nonLaborUnits1Variance",
   "nonLaborUnits2Variance",
   "nonLaborUnits3Variance",
   "nonLaborUnitsPercentComplete",
   "nonLaborUnitsVariance",
   "notesToResources",
   "objectId",
   "ownerIDArray",
   "ownerNamesArray",
   "percentComplete",
   "percentCompleteType",
   "performancePercentComplete",
   "performancePercentCompleteByLaborUnits",
   "physicalPercentComplete",
   "plannedDuration",
   "plannedExpenseCost",
   "plannedFinishDate",
   "plannedLaborCost",
   "plannedLaborUnits",
   "plannedMaterialCost",
   "plannedNonLaborCost",
   "plannedNonLaborUnits",
   "plannedStartDate",
   "plannedTotalCost",
   "plannedTotalUnits",
   "plannedValueCost",
   "plannedValueLaborUnits",
   "postRespCriticalityIndex",
   "postResponsePessimisticFinish",
   "postResponsePessimisticStart",
   "preRespCriticalityIndex",
   "preResponsePessimisticFinish",
   "preResponsePessimisticStart",
   "primaryConstraintDate",
   "primaryConstraintType",
   "primaryResourceId",
   "primaryResourceName",
   "primaryResourceObjectId",
   "projectFlag",
   "projectId",
   "projectName",
   "projectNameSepChar",
   "projectObjectId",
   "projectProjectFlag",
   "remainingDuration",
   "remainingEarlyFinishDate",
   "remainingEarlyStartDate",
   "remainingExpenseCost",
   "remainingFloat",
   "remainingLaborCost",
   "remainingLaborUnits",
   "remainingLateFinishDate",
   "remainingLateStartDate",
   "remainingMaterialCost",
   "remainingNonLaborCost",
   "remainingNonLaborUnits",
   "remainingTotalCost",
   "remainingTotalUnits",
   "resumeDate",
   "reviewFinishDate",
   "reviewRequired",
   "reviewStatus",
   "schedulePercentComplete",
   "schedulePerformanceIndex",
   "schedulePerformanceIndexLaborUnits",
   "scheduleVariance",
   "scheduleVarianceIndex",
   "scheduleVarianceIndexLaborUnits",
   "scheduleVarianceLaborUnits",
   "scopePercentComplete",
   "secondaryConstraintDate",
   "secondaryConstraintType",
   "startDate",
   "startDate1Variance",
   "startDate2Variance",
   "startDate3Variance",
   "startDateVariance",
   "status",
   "statusCode",
   "suspendDate",
   "taskStatusCompletion",
   "taskStatusDates",
   "taskStatusIndicator",
   "toCompletePerformanceIndex",
   "totalCost1Variance",
   "totalCost2Variance",
   "totalCost3Variance",
   "totalCostVariance",
   "totalFloat",
   "totalPastPeriodEarnedValueCostBCWP",
   "totalPastPeriodEarnedValueLaborUnits",
   "totalPastPeriodExpenseCost",
   "totalPastPeriodLaborCost",
   "totalPastPeriodLaborUnits",
   "totalPastPeriodMaterialCost",
   "totalPastPeriodNonLaborCost",
   "totalPastPeriodNonLaborUnits",
   "totalPastPeriodPlannedValueCost",
   "totalPastPeriodPlannedValueLaborUnits",
   "type",
   "unitsPercentComplete",
   "unreadCommentCount",
   "wbsCode",
   "wbsName",
   "wbsNamePath",
   "wbsObjectId",
   "wbsPath",
   "workPackageId",
   "workPackageName",
   "code",
   "udf",
   "spread"
}) public class ActivityType
{

   @XmlElement(name = "AccountingVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double accountingVariance;
   @XmlElement(name = "AccountingVarianceLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double accountingVarianceLaborUnits;
   @XmlElement(name = "ActivityOwnerUserId") protected Integer activityOwnerUserId;
   @XmlElement(name = "ActualDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double actualDuration;
   @XmlElement(name = "ActualExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualExpenseCost;
   @XmlElement(name = "ActualFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime actualFinishDate;
   @XmlElement(name = "ActualLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborCost;
   @XmlElement(name = "ActualLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualLaborUnits;
   @XmlElement(name = "ActualMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualMaterialCost;
   @XmlElement(name = "ActualNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonLaborCost;
   @XmlElement(name = "ActualNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualNonLaborUnits;
   @XmlElement(name = "ActualStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime actualStartDate;
   @XmlElement(name = "ActualThisPeriodLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualThisPeriodLaborCost;
   @XmlElement(name = "ActualThisPeriodLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualThisPeriodLaborUnits;
   @XmlElement(name = "ActualThisPeriodMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualThisPeriodMaterialCost;
   @XmlElement(name = "ActualThisPeriodNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualThisPeriodNonLaborCost;
   @XmlElement(name = "ActualThisPeriodNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualThisPeriodNonLaborUnits;
   @XmlElement(name = "ActualTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualTotalCost;
   @XmlElement(name = "ActualTotalUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualTotalUnits;
   @XmlElement(name = "AtCompletionDuration", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double atCompletionDuration;
   @XmlElement(name = "AtCompletionExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionExpenseCost;
   @XmlElement(name = "AtCompletionLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborCost;
   @XmlElement(name = "AtCompletionLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborUnits;
   @XmlElement(name = "AtCompletionLaborUnitsVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionLaborUnitsVariance;
   @XmlElement(name = "AtCompletionMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionMaterialCost;
   @XmlElement(name = "AtCompletionNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonLaborCost;
   @XmlElement(name = "AtCompletionNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionNonLaborUnits;
   @XmlElement(name = "AtCompletionTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionTotalCost;
   @XmlElement(name = "AtCompletionTotalUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionTotalUnits;
   @XmlElement(name = "AtCompletionVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionVariance;
   @XmlElement(name = "AutoComputeActuals", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean autoComputeActuals;
   @XmlElement(name = "Baseline1Duration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1Duration;
   @XmlElement(name = "Baseline1FinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline1FinishDate;
   @XmlElement(name = "Baseline1PlannedDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedDuration;
   @XmlElement(name = "Baseline1PlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedExpenseCost;
   @XmlElement(name = "Baseline1PlannedLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedLaborCost;
   @XmlElement(name = "Baseline1PlannedLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedLaborUnits;
   @XmlElement(name = "Baseline1PlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedMaterialCost;
   @XmlElement(name = "Baseline1PlannedNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedNonLaborCost;
   @XmlElement(name = "Baseline1PlannedNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedNonLaborUnits;
   @XmlElement(name = "Baseline1PlannedTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline1PlannedTotalCost;
   @XmlElement(name = "Baseline1StartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline1StartDate;
   @XmlElement(name = "Baseline2Duration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2Duration;
   @XmlElement(name = "Baseline2FinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline2FinishDate;
   @XmlElement(name = "Baseline2PlannedDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedDuration;
   @XmlElement(name = "Baseline2PlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedExpenseCost;
   @XmlElement(name = "Baseline2PlannedLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedLaborCost;
   @XmlElement(name = "Baseline2PlannedLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedLaborUnits;
   @XmlElement(name = "Baseline2PlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedMaterialCost;
   @XmlElement(name = "Baseline2PlannedNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedNonLaborCost;
   @XmlElement(name = "Baseline2PlannedNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedNonLaborUnits;
   @XmlElement(name = "Baseline2PlannedTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline2PlannedTotalCost;
   @XmlElement(name = "Baseline2StartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline2StartDate;
   @XmlElement(name = "Baseline3Duration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3Duration;
   @XmlElement(name = "Baseline3FinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline3FinishDate;
   @XmlElement(name = "Baseline3PlannedDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedDuration;
   @XmlElement(name = "Baseline3PlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedExpenseCost;
   @XmlElement(name = "Baseline3PlannedLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedLaborCost;
   @XmlElement(name = "Baseline3PlannedLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedLaborUnits;
   @XmlElement(name = "Baseline3PlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedMaterialCost;
   @XmlElement(name = "Baseline3PlannedNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedNonLaborCost;
   @XmlElement(name = "Baseline3PlannedNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedNonLaborUnits;
   @XmlElement(name = "Baseline3PlannedTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baseline3PlannedTotalCost;
   @XmlElement(name = "Baseline3StartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseline3StartDate;
   @XmlElement(name = "BaselineDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselineDuration;
   @XmlElement(name = "BaselineFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineFinishDate;
   @XmlElement(name = "BaselinePlannedDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedDuration;
   @XmlElement(name = "BaselinePlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedExpenseCost;
   @XmlElement(name = "BaselinePlannedLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborCost;
   @XmlElement(name = "BaselinePlannedLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedLaborUnits;
   @XmlElement(name = "BaselinePlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedMaterialCost;
   @XmlElement(name = "BaselinePlannedNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonLaborCost;
   @XmlElement(name = "BaselinePlannedNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedNonLaborUnits;
   @XmlElement(name = "BaselinePlannedTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double baselinePlannedTotalCost;
   @XmlElement(name = "BaselineStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineStartDate;
   @XmlElement(name = "BudgetAtCompletion", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double budgetAtCompletion;
   @XmlElement(name = "CBSCode") @XmlJavaTypeAdapter(Adapter1.class) protected String cbsCode;
   @XmlElement(name = "CBSId", nillable = true) protected Integer cbsId;
   @XmlElement(name = "CBSObjectId", nillable = true) protected Integer cbsObjectId;
   @XmlElement(name = "CalendarName") @XmlJavaTypeAdapter(Adapter1.class) protected String calendarName;
   @XmlElement(name = "CalendarObjectId") protected Integer calendarObjectId;
   @XmlElement(name = "CostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costPercentComplete;
   @XmlElement(name = "CostPercentOfPlanned", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costPercentOfPlanned;
   @XmlElement(name = "CostPerformanceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costPerformanceIndex;
   @XmlElement(name = "CostPerformanceIndexLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costPerformanceIndexLaborUnits;
   @XmlElement(name = "CostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costVariance;
   @XmlElement(name = "CostVarianceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costVarianceIndex;
   @XmlElement(name = "CostVarianceIndexLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costVarianceIndexLaborUnits;
   @XmlElement(name = "CostVarianceLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costVarianceLaborUnits;
   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "DataDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dataDate;
   @XmlElement(name = "Duration1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double duration1Variance;
   @XmlElement(name = "Duration2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double duration2Variance;
   @XmlElement(name = "Duration3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double duration3Variance;
   @XmlElement(name = "DurationPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double durationPercentComplete;
   @XmlElement(name = "DurationPercentOfPlanned", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double durationPercentOfPlanned;
   @XmlElement(name = "DurationType") @XmlJavaTypeAdapter(Adapter1.class) protected String durationType;
   @XmlElement(name = "DurationVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double durationVariance;
   @XmlElement(name = "EarlyFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime earlyFinishDate;
   @XmlElement(name = "EarlyStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime earlyStartDate;
   @XmlElement(name = "EarnedValueCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueCost;
   @XmlElement(name = "EarnedValueLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double earnedValueLaborUnits;
   @XmlElement(name = "EstimateAtCompletionCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionCost;
   @XmlElement(name = "EstimateAtCompletionLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateAtCompletionLaborUnits;
   @XmlElement(name = "EstimateToComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToComplete;
   @XmlElement(name = "EstimateToCompleteLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimateToCompleteLaborUnits;
   @XmlElement(name = "EstimatedWeight", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double estimatedWeight;
   @XmlElement(name = "ExpectedFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime expectedFinishDate;
   @XmlElement(name = "ExpenseCost1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double expenseCost1Variance;
   @XmlElement(name = "ExpenseCost2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double expenseCost2Variance;
   @XmlElement(name = "ExpenseCost3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double expenseCost3Variance;
   @XmlElement(name = "ExpenseCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double expenseCostPercentComplete;
   @XmlElement(name = "ExpenseCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double expenseCostVariance;
   @XmlElement(name = "ExternalEarlyStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime externalEarlyStartDate;
   @XmlElement(name = "ExternalLateFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime externalLateFinishDate;
   @XmlElement(name = "Feedback") @XmlJavaTypeAdapter(Adapter1.class) protected String feedback;
   @XmlElement(name = "FinancialPeriodTmplId") protected Integer financialPeriodTmplId;
   @XmlElement(name = "FinishDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime finishDate;
   @XmlElement(name = "FinishDate1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double finishDate1Variance;
   @XmlElement(name = "FinishDate2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double finishDate2Variance;
   @XmlElement(name = "FinishDate3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double finishDate3Variance;
   @XmlElement(name = "FinishDateVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double finishDateVariance;
   @XmlElement(name = "FloatPath", nillable = true) protected Integer floatPath;
   @XmlElement(name = "FloatPathOrder", nillable = true) protected Integer floatPathOrder;
   @XmlElement(name = "FreeFloat", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double freeFloat;
   @XmlElement(name = "GUID") @XmlJavaTypeAdapter(Adapter1.class) protected String guid;
   @XmlElement(name = "HasFutureBucketData", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean hasFutureBucketData;
   @XmlElement(name = "Id") @XmlJavaTypeAdapter(Adapter1.class) protected String id;
   @XmlElement(name = "IsBaseline", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isBaseline;
   @XmlElement(name = "IsCritical", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isCritical;
   @XmlElement(name = "IsLongestPath", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isLongestPath;
   @XmlElement(name = "IsNewFeedback", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isNewFeedback;
   @XmlElement(name = "IsStarred", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isStarred;
   @XmlElement(name = "IsTemplate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isTemplate;
   @XmlElement(name = "IsWorkPackage", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isWorkPackage;
   @XmlElement(name = "LaborCost1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborCost1Variance;
   @XmlElement(name = "LaborCost2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborCost2Variance;
   @XmlElement(name = "LaborCost3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborCost3Variance;
   @XmlElement(name = "LaborCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborCostPercentComplete;
   @XmlElement(name = "LaborCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborCostVariance;
   @XmlElement(name = "LaborUnits1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborUnits1Variance;
   @XmlElement(name = "LaborUnits2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborUnits2Variance;
   @XmlElement(name = "LaborUnits3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborUnits3Variance;
   @XmlElement(name = "LaborUnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborUnitsPercentComplete;
   @XmlElement(name = "LaborUnitsVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double laborUnitsVariance;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "LateFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lateFinishDate;
   @XmlElement(name = "LateStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lateStartDate;
   @XmlElement(name = "LevelingPriority") @XmlJavaTypeAdapter(Adapter1.class) protected String levelingPriority;
   @XmlElement(name = "LocationName") @XmlJavaTypeAdapter(Adapter1.class) protected String locationName;
   @XmlElement(name = "LocationObjectId", nillable = true) protected Integer locationObjectId;
   @XmlElement(name = "MaterialCost1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double materialCost1Variance;
   @XmlElement(name = "MaterialCost2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double materialCost2Variance;
   @XmlElement(name = "MaterialCost3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double materialCost3Variance;
   @XmlElement(name = "MaterialCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double materialCostPercentComplete;
   @XmlElement(name = "MaterialCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double materialCostVariance;
   @XmlElement(name = "MaximumDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double maximumDuration;
   @XmlElement(name = "MinimumDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double minimumDuration;
   @XmlElement(name = "MostLikelyDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double mostLikelyDuration;
   @XmlElement(name = "Name") @XmlJavaTypeAdapter(Adapter1.class) protected String name;
   @XmlElement(name = "NonLaborCost1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborCost1Variance;
   @XmlElement(name = "NonLaborCost2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborCost2Variance;
   @XmlElement(name = "NonLaborCost3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborCost3Variance;
   @XmlElement(name = "NonLaborCostPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborCostPercentComplete;
   @XmlElement(name = "NonLaborCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborCostVariance;
   @XmlElement(name = "NonLaborUnits1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborUnits1Variance;
   @XmlElement(name = "NonLaborUnits2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborUnits2Variance;
   @XmlElement(name = "NonLaborUnits3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborUnits3Variance;
   @XmlElement(name = "NonLaborUnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborUnitsPercentComplete;
   @XmlElement(name = "NonLaborUnitsVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double nonLaborUnitsVariance;
   @XmlElement(name = "NotesToResources") @XmlJavaTypeAdapter(Adapter1.class) protected String notesToResources;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "OwnerIDArray", nillable = true) @XmlJavaTypeAdapter(Adapter1.class) protected String ownerIDArray;
   @XmlElement(name = "OwnerNamesArray", nillable = true) @XmlJavaTypeAdapter(Adapter1.class) protected String ownerNamesArray;
   @XmlElement(name = "PercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double percentComplete;
   @XmlElement(name = "PercentCompleteType") @XmlJavaTypeAdapter(Adapter1.class) protected String percentCompleteType;
   @XmlElement(name = "PerformancePercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double performancePercentComplete;
   @XmlElement(name = "PerformancePercentCompleteByLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double performancePercentCompleteByLaborUnits;
   @XmlElement(name = "PhysicalPercentComplete", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double physicalPercentComplete;
   @XmlElement(name = "PlannedDuration", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) protected Double plannedDuration;
   @XmlElement(name = "PlannedExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedExpenseCost;
   @XmlElement(name = "PlannedFinishDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime plannedFinishDate;
   @XmlElement(name = "PlannedLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborCost;
   @XmlElement(name = "PlannedLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedLaborUnits;
   @XmlElement(name = "PlannedMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedMaterialCost;
   @XmlElement(name = "PlannedNonLaborCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonLaborCost;
   @XmlElement(name = "PlannedNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedNonLaborUnits;
   @XmlElement(name = "PlannedStartDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime plannedStartDate;
   @XmlElement(name = "PlannedTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedTotalCost;
   @XmlElement(name = "PlannedTotalUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedTotalUnits;
   @XmlElement(name = "PlannedValueCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueCost;
   @XmlElement(name = "PlannedValueLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedValueLaborUnits;
   @XmlElement(name = "PostRespCriticalityIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double postRespCriticalityIndex;
   @XmlElement(name = "PostResponsePessimisticFinish", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime postResponsePessimisticFinish;
   @XmlElement(name = "PostResponsePessimisticStart", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime postResponsePessimisticStart;
   @XmlElement(name = "PreRespCriticalityIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double preRespCriticalityIndex;
   @XmlElement(name = "PreResponsePessimisticFinish", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime preResponsePessimisticFinish;
   @XmlElement(name = "PreResponsePessimisticStart", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime preResponsePessimisticStart;
   @XmlElement(name = "PrimaryConstraintDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime primaryConstraintDate;
   @XmlElement(name = "PrimaryConstraintType") @XmlJavaTypeAdapter(Adapter1.class) protected String primaryConstraintType;
   @XmlElement(name = "PrimaryResourceId") @XmlJavaTypeAdapter(Adapter1.class) protected String primaryResourceId;
   @XmlElement(name = "PrimaryResourceName") @XmlJavaTypeAdapter(Adapter1.class) protected String primaryResourceName;
   @XmlElement(name = "PrimaryResourceObjectId", nillable = true) protected Integer primaryResourceObjectId;
   @XmlElement(name = "ProjectFlag") @XmlJavaTypeAdapter(Adapter1.class) protected String projectFlag;
   @XmlElement(name = "ProjectId") @XmlJavaTypeAdapter(Adapter1.class) protected String projectId;
   @XmlElement(name = "ProjectName") @XmlJavaTypeAdapter(Adapter1.class) protected String projectName;
   @XmlElement(name = "ProjectNameSepChar") @XmlJavaTypeAdapter(Adapter1.class) protected String projectNameSepChar;
   @XmlElement(name = "ProjectObjectId") protected Integer projectObjectId;
   @XmlElement(name = "ProjectProjectFlag") @XmlJavaTypeAdapter(Adapter1.class) protected String projectProjectFlag;
   @XmlElement(name = "RemainingDuration", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double remainingDuration;
   @XmlElement(name = "RemainingEarlyFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime remainingEarlyFinishDate;
   @XmlElement(name = "RemainingEarlyStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime remainingEarlyStartDate;
   @XmlElement(name = "RemainingExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingExpenseCost;
   @XmlElement(name = "RemainingFloat", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingFloat;
   @XmlElement(name = "RemainingLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborCost;
   @XmlElement(name = "RemainingLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLaborUnits;
   @XmlElement(name = "RemainingLateFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime remainingLateFinishDate;
   @XmlElement(name = "RemainingLateStartDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime remainingLateStartDate;
   @XmlElement(name = "RemainingMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingMaterialCost;
   @XmlElement(name = "RemainingNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonLaborCost;
   @XmlElement(name = "RemainingNonLaborUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingNonLaborUnits;
   @XmlElement(name = "RemainingTotalCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingTotalCost;
   @XmlElement(name = "RemainingTotalUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingTotalUnits;
   @XmlElement(name = "ResumeDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime resumeDate;
   @XmlElement(name = "ReviewFinishDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime reviewFinishDate;
   @XmlElement(name = "ReviewRequired", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean reviewRequired;
   @XmlElement(name = "ReviewStatus") @XmlJavaTypeAdapter(Adapter1.class) protected String reviewStatus;
   @XmlElement(name = "SchedulePercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double schedulePercentComplete;
   @XmlElement(name = "SchedulePerformanceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double schedulePerformanceIndex;
   @XmlElement(name = "SchedulePerformanceIndexLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double schedulePerformanceIndexLaborUnits;
   @XmlElement(name = "ScheduleVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double scheduleVariance;
   @XmlElement(name = "ScheduleVarianceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double scheduleVarianceIndex;
   @XmlElement(name = "ScheduleVarianceIndexLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double scheduleVarianceIndexLaborUnits;
   @XmlElement(name = "ScheduleVarianceLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double scheduleVarianceLaborUnits;
   @XmlElement(name = "ScopePercentComplete", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double scopePercentComplete;
   @XmlElement(name = "SecondaryConstraintDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime secondaryConstraintDate;
   @XmlElement(name = "SecondaryConstraintType") @XmlJavaTypeAdapter(Adapter1.class) protected String secondaryConstraintType;
   @XmlElement(name = "StartDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "StartDate1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double startDate1Variance;
   @XmlElement(name = "StartDate2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double startDate2Variance;
   @XmlElement(name = "StartDate3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double startDate3Variance;
   @XmlElement(name = "StartDateVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double startDateVariance;
   @XmlElement(name = "Status") @XmlJavaTypeAdapter(Adapter1.class) protected String status;
   @XmlElement(name = "StatusCode") @XmlJavaTypeAdapter(Adapter1.class) protected String statusCode;
   @XmlElement(name = "SuspendDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime suspendDate;
   @XmlElement(name = "TaskStatusCompletion") @XmlJavaTypeAdapter(Adapter1.class) protected String taskStatusCompletion;
   @XmlElement(name = "TaskStatusDates") @XmlJavaTypeAdapter(Adapter1.class) protected String taskStatusDates;
   @XmlElement(name = "TaskStatusIndicator", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean taskStatusIndicator;
   @XmlElement(name = "ToCompletePerformanceIndex", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double toCompletePerformanceIndex;
   @XmlElement(name = "TotalCost1Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalCost1Variance;
   @XmlElement(name = "TotalCost2Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalCost2Variance;
   @XmlElement(name = "TotalCost3Variance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalCost3Variance;
   @XmlElement(name = "TotalCostVariance", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalCostVariance;
   @XmlElement(name = "TotalFloat", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalFloat;
   @XmlElement(name = "TotalPastPeriodEarnedValueCostBCWP", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodEarnedValueCostBCWP;
   @XmlElement(name = "TotalPastPeriodEarnedValueLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodEarnedValueLaborUnits;
   @XmlElement(name = "TotalPastPeriodExpenseCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodExpenseCost;
   @XmlElement(name = "TotalPastPeriodLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodLaborCost;
   @XmlElement(name = "TotalPastPeriodLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodLaborUnits;
   @XmlElement(name = "TotalPastPeriodMaterialCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodMaterialCost;
   @XmlElement(name = "TotalPastPeriodNonLaborCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodNonLaborCost;
   @XmlElement(name = "TotalPastPeriodNonLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodNonLaborUnits;
   @XmlElement(name = "TotalPastPeriodPlannedValueCost", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodPlannedValueCost;
   @XmlElement(name = "TotalPastPeriodPlannedValueLaborUnits", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double totalPastPeriodPlannedValueLaborUnits;
   @XmlElement(name = "Type") @XmlJavaTypeAdapter(Adapter1.class) protected String type;
   @XmlElement(name = "UnitsPercentComplete", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unitsPercentComplete;
   @XmlElement(name = "UnreadCommentCount", nillable = true) protected Integer unreadCommentCount;
   @XmlElement(name = "WBSCode") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsCode;
   @XmlElement(name = "WBSName") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsName;
   @XmlElement(name = "WBSNamePath") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsNamePath;
   @XmlElement(name = "WBSObjectId", nillable = true) protected Integer wbsObjectId;
   @XmlElement(name = "WBSPath") @XmlJavaTypeAdapter(Adapter1.class) protected String wbsPath;
   @XmlElement(name = "WorkPackageId") @XmlJavaTypeAdapter(Adapter1.class) protected String workPackageId;
   @XmlElement(name = "WorkPackageName") @XmlJavaTypeAdapter(Adapter1.class) protected String workPackageName;
   @XmlElement(name = "Code") protected List<CodeAssignmentType> code;
   @XmlElement(name = "UDF") protected List<UDFAssignmentType> udf;
   @XmlElement(name = "Spread") protected ActivitySpreadType spread;

   /**
    * Gets the value of the accountingVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAccountingVariance()
   {
      return accountingVariance;
   }

   /**
    * Sets the value of the accountingVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAccountingVariance(Double value)
   {
      this.accountingVariance = value;
   }

   /**
    * Gets the value of the accountingVarianceLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAccountingVarianceLaborUnits()
   {
      return accountingVarianceLaborUnits;
   }

   /**
    * Sets the value of the accountingVarianceLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAccountingVarianceLaborUnits(Double value)
   {
      this.accountingVarianceLaborUnits = value;
   }

   /**
    * Gets the value of the activityOwnerUserId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityOwnerUserId()
   {
      return activityOwnerUserId;
   }

   /**
    * Sets the value of the activityOwnerUserId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityOwnerUserId(Integer value)
   {
      this.activityOwnerUserId = value;
   }

   /**
    * Gets the value of the actualDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualDuration()
   {
      return actualDuration;
   }

   /**
    * Sets the value of the actualDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualDuration(Double value)
   {
      this.actualDuration = value;
   }

   /**
    * Gets the value of the actualExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualExpenseCost()
   {
      return actualExpenseCost;
   }

   /**
    * Sets the value of the actualExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualExpenseCost(Double value)
   {
      this.actualExpenseCost = value;
   }

   /**
    * Gets the value of the actualFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getActualFinishDate()
   {
      return actualFinishDate;
   }

   /**
    * Sets the value of the actualFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualFinishDate(LocalDateTime value)
   {
      this.actualFinishDate = value;
   }

   /**
    * Gets the value of the actualLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualLaborCost()
   {
      return actualLaborCost;
   }

   /**
    * Sets the value of the actualLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualLaborCost(Double value)
   {
      this.actualLaborCost = value;
   }

   /**
    * Gets the value of the actualLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualLaborUnits()
   {
      return actualLaborUnits;
   }

   /**
    * Sets the value of the actualLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualLaborUnits(Double value)
   {
      this.actualLaborUnits = value;
   }

   /**
    * Gets the value of the actualMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualMaterialCost()
   {
      return actualMaterialCost;
   }

   /**
    * Sets the value of the actualMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualMaterialCost(Double value)
   {
      this.actualMaterialCost = value;
   }

   /**
    * Gets the value of the actualNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualNonLaborCost()
   {
      return actualNonLaborCost;
   }

   /**
    * Sets the value of the actualNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualNonLaborCost(Double value)
   {
      this.actualNonLaborCost = value;
   }

   /**
    * Gets the value of the actualNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualNonLaborUnits()
   {
      return actualNonLaborUnits;
   }

   /**
    * Sets the value of the actualNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualNonLaborUnits(Double value)
   {
      this.actualNonLaborUnits = value;
   }

   /**
    * Gets the value of the actualStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getActualStartDate()
   {
      return actualStartDate;
   }

   /**
    * Sets the value of the actualStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualStartDate(LocalDateTime value)
   {
      this.actualStartDate = value;
   }

   /**
    * Gets the value of the actualThisPeriodLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualThisPeriodLaborCost()
   {
      return actualThisPeriodLaborCost;
   }

   /**
    * Sets the value of the actualThisPeriodLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualThisPeriodLaborCost(Double value)
   {
      this.actualThisPeriodLaborCost = value;
   }

   /**
    * Gets the value of the actualThisPeriodLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualThisPeriodLaborUnits()
   {
      return actualThisPeriodLaborUnits;
   }

   /**
    * Sets the value of the actualThisPeriodLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualThisPeriodLaborUnits(Double value)
   {
      this.actualThisPeriodLaborUnits = value;
   }

   /**
    * Gets the value of the actualThisPeriodMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualThisPeriodMaterialCost()
   {
      return actualThisPeriodMaterialCost;
   }

   /**
    * Sets the value of the actualThisPeriodMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualThisPeriodMaterialCost(Double value)
   {
      this.actualThisPeriodMaterialCost = value;
   }

   /**
    * Gets the value of the actualThisPeriodNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualThisPeriodNonLaborCost()
   {
      return actualThisPeriodNonLaborCost;
   }

   /**
    * Sets the value of the actualThisPeriodNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualThisPeriodNonLaborCost(Double value)
   {
      this.actualThisPeriodNonLaborCost = value;
   }

   /**
    * Gets the value of the actualThisPeriodNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualThisPeriodNonLaborUnits()
   {
      return actualThisPeriodNonLaborUnits;
   }

   /**
    * Sets the value of the actualThisPeriodNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualThisPeriodNonLaborUnits(Double value)
   {
      this.actualThisPeriodNonLaborUnits = value;
   }

   /**
    * Gets the value of the actualTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualTotalCost()
   {
      return actualTotalCost;
   }

   /**
    * Sets the value of the actualTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualTotalCost(Double value)
   {
      this.actualTotalCost = value;
   }

   /**
    * Gets the value of the actualTotalUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getActualTotalUnits()
   {
      return actualTotalUnits;
   }

   /**
    * Sets the value of the actualTotalUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setActualTotalUnits(Double value)
   {
      this.actualTotalUnits = value;
   }

   /**
    * Gets the value of the atCompletionDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionDuration()
   {
      return atCompletionDuration;
   }

   /**
    * Sets the value of the atCompletionDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionDuration(Double value)
   {
      this.atCompletionDuration = value;
   }

   /**
    * Gets the value of the atCompletionExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionExpenseCost()
   {
      return atCompletionExpenseCost;
   }

   /**
    * Sets the value of the atCompletionExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionExpenseCost(Double value)
   {
      this.atCompletionExpenseCost = value;
   }

   /**
    * Gets the value of the atCompletionLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionLaborCost()
   {
      return atCompletionLaborCost;
   }

   /**
    * Sets the value of the atCompletionLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionLaborCost(Double value)
   {
      this.atCompletionLaborCost = value;
   }

   /**
    * Gets the value of the atCompletionLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionLaborUnits()
   {
      return atCompletionLaborUnits;
   }

   /**
    * Sets the value of the atCompletionLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionLaborUnits(Double value)
   {
      this.atCompletionLaborUnits = value;
   }

   /**
    * Gets the value of the atCompletionLaborUnitsVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionLaborUnitsVariance()
   {
      return atCompletionLaborUnitsVariance;
   }

   /**
    * Sets the value of the atCompletionLaborUnitsVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionLaborUnitsVariance(Double value)
   {
      this.atCompletionLaborUnitsVariance = value;
   }

   /**
    * Gets the value of the atCompletionMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionMaterialCost()
   {
      return atCompletionMaterialCost;
   }

   /**
    * Sets the value of the atCompletionMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionMaterialCost(Double value)
   {
      this.atCompletionMaterialCost = value;
   }

   /**
    * Gets the value of the atCompletionNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionNonLaborCost()
   {
      return atCompletionNonLaborCost;
   }

   /**
    * Sets the value of the atCompletionNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionNonLaborCost(Double value)
   {
      this.atCompletionNonLaborCost = value;
   }

   /**
    * Gets the value of the atCompletionNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionNonLaborUnits()
   {
      return atCompletionNonLaborUnits;
   }

   /**
    * Sets the value of the atCompletionNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionNonLaborUnits(Double value)
   {
      this.atCompletionNonLaborUnits = value;
   }

   /**
    * Gets the value of the atCompletionTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionTotalCost()
   {
      return atCompletionTotalCost;
   }

   /**
    * Sets the value of the atCompletionTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionTotalCost(Double value)
   {
      this.atCompletionTotalCost = value;
   }

   /**
    * Gets the value of the atCompletionTotalUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionTotalUnits()
   {
      return atCompletionTotalUnits;
   }

   /**
    * Sets the value of the atCompletionTotalUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionTotalUnits(Double value)
   {
      this.atCompletionTotalUnits = value;
   }

   /**
    * Gets the value of the atCompletionVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getAtCompletionVariance()
   {
      return atCompletionVariance;
   }

   /**
    * Sets the value of the atCompletionVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAtCompletionVariance(Double value)
   {
      this.atCompletionVariance = value;
   }

   /**
    * Gets the value of the autoComputeActuals property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isAutoComputeActuals()
   {
      return autoComputeActuals;
   }

   /**
    * Sets the value of the autoComputeActuals property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setAutoComputeActuals(Boolean value)
   {
      this.autoComputeActuals = value;
   }

   /**
    * Gets the value of the baseline1Duration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1Duration()
   {
      return baseline1Duration;
   }

   /**
    * Sets the value of the baseline1Duration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1Duration(Double value)
   {
      this.baseline1Duration = value;
   }

   /**
    * Gets the value of the baseline1FinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline1FinishDate()
   {
      return baseline1FinishDate;
   }

   /**
    * Sets the value of the baseline1FinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1FinishDate(LocalDateTime value)
   {
      this.baseline1FinishDate = value;
   }

   /**
    * Gets the value of the baseline1PlannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedDuration()
   {
      return baseline1PlannedDuration;
   }

   /**
    * Sets the value of the baseline1PlannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedDuration(Double value)
   {
      this.baseline1PlannedDuration = value;
   }

   /**
    * Gets the value of the baseline1PlannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedExpenseCost()
   {
      return baseline1PlannedExpenseCost;
   }

   /**
    * Sets the value of the baseline1PlannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedExpenseCost(Double value)
   {
      this.baseline1PlannedExpenseCost = value;
   }

   /**
    * Gets the value of the baseline1PlannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedLaborCost()
   {
      return baseline1PlannedLaborCost;
   }

   /**
    * Sets the value of the baseline1PlannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedLaborCost(Double value)
   {
      this.baseline1PlannedLaborCost = value;
   }

   /**
    * Gets the value of the baseline1PlannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedLaborUnits()
   {
      return baseline1PlannedLaborUnits;
   }

   /**
    * Sets the value of the baseline1PlannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedLaborUnits(Double value)
   {
      this.baseline1PlannedLaborUnits = value;
   }

   /**
    * Gets the value of the baseline1PlannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedMaterialCost()
   {
      return baseline1PlannedMaterialCost;
   }

   /**
    * Sets the value of the baseline1PlannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedMaterialCost(Double value)
   {
      this.baseline1PlannedMaterialCost = value;
   }

   /**
    * Gets the value of the baseline1PlannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedNonLaborCost()
   {
      return baseline1PlannedNonLaborCost;
   }

   /**
    * Sets the value of the baseline1PlannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedNonLaborCost(Double value)
   {
      this.baseline1PlannedNonLaborCost = value;
   }

   /**
    * Gets the value of the baseline1PlannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedNonLaborUnits()
   {
      return baseline1PlannedNonLaborUnits;
   }

   /**
    * Sets the value of the baseline1PlannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedNonLaborUnits(Double value)
   {
      this.baseline1PlannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the baseline1PlannedTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline1PlannedTotalCost()
   {
      return baseline1PlannedTotalCost;
   }

   /**
    * Sets the value of the baseline1PlannedTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1PlannedTotalCost(Double value)
   {
      this.baseline1PlannedTotalCost = value;
   }

   /**
    * Gets the value of the baseline1StartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline1StartDate()
   {
      return baseline1StartDate;
   }

   /**
    * Sets the value of the baseline1StartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline1StartDate(LocalDateTime value)
   {
      this.baseline1StartDate = value;
   }

   /**
    * Gets the value of the baseline2Duration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2Duration()
   {
      return baseline2Duration;
   }

   /**
    * Sets the value of the baseline2Duration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2Duration(Double value)
   {
      this.baseline2Duration = value;
   }

   /**
    * Gets the value of the baseline2FinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline2FinishDate()
   {
      return baseline2FinishDate;
   }

   /**
    * Sets the value of the baseline2FinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2FinishDate(LocalDateTime value)
   {
      this.baseline2FinishDate = value;
   }

   /**
    * Gets the value of the baseline2PlannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedDuration()
   {
      return baseline2PlannedDuration;
   }

   /**
    * Sets the value of the baseline2PlannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedDuration(Double value)
   {
      this.baseline2PlannedDuration = value;
   }

   /**
    * Gets the value of the baseline2PlannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedExpenseCost()
   {
      return baseline2PlannedExpenseCost;
   }

   /**
    * Sets the value of the baseline2PlannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedExpenseCost(Double value)
   {
      this.baseline2PlannedExpenseCost = value;
   }

   /**
    * Gets the value of the baseline2PlannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedLaborCost()
   {
      return baseline2PlannedLaborCost;
   }

   /**
    * Sets the value of the baseline2PlannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedLaborCost(Double value)
   {
      this.baseline2PlannedLaborCost = value;
   }

   /**
    * Gets the value of the baseline2PlannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedLaborUnits()
   {
      return baseline2PlannedLaborUnits;
   }

   /**
    * Sets the value of the baseline2PlannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedLaborUnits(Double value)
   {
      this.baseline2PlannedLaborUnits = value;
   }

   /**
    * Gets the value of the baseline2PlannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedMaterialCost()
   {
      return baseline2PlannedMaterialCost;
   }

   /**
    * Sets the value of the baseline2PlannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedMaterialCost(Double value)
   {
      this.baseline2PlannedMaterialCost = value;
   }

   /**
    * Gets the value of the baseline2PlannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedNonLaborCost()
   {
      return baseline2PlannedNonLaborCost;
   }

   /**
    * Sets the value of the baseline2PlannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedNonLaborCost(Double value)
   {
      this.baseline2PlannedNonLaborCost = value;
   }

   /**
    * Gets the value of the baseline2PlannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedNonLaborUnits()
   {
      return baseline2PlannedNonLaborUnits;
   }

   /**
    * Sets the value of the baseline2PlannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedNonLaborUnits(Double value)
   {
      this.baseline2PlannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the baseline2PlannedTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline2PlannedTotalCost()
   {
      return baseline2PlannedTotalCost;
   }

   /**
    * Sets the value of the baseline2PlannedTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2PlannedTotalCost(Double value)
   {
      this.baseline2PlannedTotalCost = value;
   }

   /**
    * Gets the value of the baseline2StartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline2StartDate()
   {
      return baseline2StartDate;
   }

   /**
    * Sets the value of the baseline2StartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline2StartDate(LocalDateTime value)
   {
      this.baseline2StartDate = value;
   }

   /**
    * Gets the value of the baseline3Duration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3Duration()
   {
      return baseline3Duration;
   }

   /**
    * Sets the value of the baseline3Duration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3Duration(Double value)
   {
      this.baseline3Duration = value;
   }

   /**
    * Gets the value of the baseline3FinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline3FinishDate()
   {
      return baseline3FinishDate;
   }

   /**
    * Sets the value of the baseline3FinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3FinishDate(LocalDateTime value)
   {
      this.baseline3FinishDate = value;
   }

   /**
    * Gets the value of the baseline3PlannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedDuration()
   {
      return baseline3PlannedDuration;
   }

   /**
    * Sets the value of the baseline3PlannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedDuration(Double value)
   {
      this.baseline3PlannedDuration = value;
   }

   /**
    * Gets the value of the baseline3PlannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedExpenseCost()
   {
      return baseline3PlannedExpenseCost;
   }

   /**
    * Sets the value of the baseline3PlannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedExpenseCost(Double value)
   {
      this.baseline3PlannedExpenseCost = value;
   }

   /**
    * Gets the value of the baseline3PlannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedLaborCost()
   {
      return baseline3PlannedLaborCost;
   }

   /**
    * Sets the value of the baseline3PlannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedLaborCost(Double value)
   {
      this.baseline3PlannedLaborCost = value;
   }

   /**
    * Gets the value of the baseline3PlannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedLaborUnits()
   {
      return baseline3PlannedLaborUnits;
   }

   /**
    * Sets the value of the baseline3PlannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedLaborUnits(Double value)
   {
      this.baseline3PlannedLaborUnits = value;
   }

   /**
    * Gets the value of the baseline3PlannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedMaterialCost()
   {
      return baseline3PlannedMaterialCost;
   }

   /**
    * Sets the value of the baseline3PlannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedMaterialCost(Double value)
   {
      this.baseline3PlannedMaterialCost = value;
   }

   /**
    * Gets the value of the baseline3PlannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedNonLaborCost()
   {
      return baseline3PlannedNonLaborCost;
   }

   /**
    * Sets the value of the baseline3PlannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedNonLaborCost(Double value)
   {
      this.baseline3PlannedNonLaborCost = value;
   }

   /**
    * Gets the value of the baseline3PlannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedNonLaborUnits()
   {
      return baseline3PlannedNonLaborUnits;
   }

   /**
    * Sets the value of the baseline3PlannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedNonLaborUnits(Double value)
   {
      this.baseline3PlannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the baseline3PlannedTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaseline3PlannedTotalCost()
   {
      return baseline3PlannedTotalCost;
   }

   /**
    * Sets the value of the baseline3PlannedTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3PlannedTotalCost(Double value)
   {
      this.baseline3PlannedTotalCost = value;
   }

   /**
    * Gets the value of the baseline3StartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaseline3StartDate()
   {
      return baseline3StartDate;
   }

   /**
    * Sets the value of the baseline3StartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseline3StartDate(LocalDateTime value)
   {
      this.baseline3StartDate = value;
   }

   /**
    * Gets the value of the baselineDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselineDuration()
   {
      return baselineDuration;
   }

   /**
    * Sets the value of the baselineDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselineDuration(Double value)
   {
      this.baselineDuration = value;
   }

   /**
    * Gets the value of the baselineFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaselineFinishDate()
   {
      return baselineFinishDate;
   }

   /**
    * Sets the value of the baselineFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselineFinishDate(LocalDateTime value)
   {
      this.baselineFinishDate = value;
   }

   /**
    * Gets the value of the baselinePlannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedDuration()
   {
      return baselinePlannedDuration;
   }

   /**
    * Sets the value of the baselinePlannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedDuration(Double value)
   {
      this.baselinePlannedDuration = value;
   }

   /**
    * Gets the value of the baselinePlannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedExpenseCost()
   {
      return baselinePlannedExpenseCost;
   }

   /**
    * Sets the value of the baselinePlannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedExpenseCost(Double value)
   {
      this.baselinePlannedExpenseCost = value;
   }

   /**
    * Gets the value of the baselinePlannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedLaborCost()
   {
      return baselinePlannedLaborCost;
   }

   /**
    * Sets the value of the baselinePlannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedLaborCost(Double value)
   {
      this.baselinePlannedLaborCost = value;
   }

   /**
    * Gets the value of the baselinePlannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedLaborUnits()
   {
      return baselinePlannedLaborUnits;
   }

   /**
    * Sets the value of the baselinePlannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedLaborUnits(Double value)
   {
      this.baselinePlannedLaborUnits = value;
   }

   /**
    * Gets the value of the baselinePlannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedMaterialCost()
   {
      return baselinePlannedMaterialCost;
   }

   /**
    * Sets the value of the baselinePlannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedMaterialCost(Double value)
   {
      this.baselinePlannedMaterialCost = value;
   }

   /**
    * Gets the value of the baselinePlannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedNonLaborCost()
   {
      return baselinePlannedNonLaborCost;
   }

   /**
    * Sets the value of the baselinePlannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedNonLaborCost(Double value)
   {
      this.baselinePlannedNonLaborCost = value;
   }

   /**
    * Gets the value of the baselinePlannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedNonLaborUnits()
   {
      return baselinePlannedNonLaborUnits;
   }

   /**
    * Sets the value of the baselinePlannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedNonLaborUnits(Double value)
   {
      this.baselinePlannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the baselinePlannedTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBaselinePlannedTotalCost()
   {
      return baselinePlannedTotalCost;
   }

   /**
    * Sets the value of the baselinePlannedTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselinePlannedTotalCost(Double value)
   {
      this.baselinePlannedTotalCost = value;
   }

   /**
    * Gets the value of the baselineStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getBaselineStartDate()
   {
      return baselineStartDate;
   }

   /**
    * Sets the value of the baselineStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaselineStartDate(LocalDateTime value)
   {
      this.baselineStartDate = value;
   }

   /**
    * Gets the value of the budgetAtCompletion property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getBudgetAtCompletion()
   {
      return budgetAtCompletion;
   }

   /**
    * Sets the value of the budgetAtCompletion property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBudgetAtCompletion(Double value)
   {
      this.budgetAtCompletion = value;
   }

   /**
    * Gets the value of the cbsCode property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCBSCode()
   {
      return cbsCode;
   }

   /**
    * Sets the value of the cbsCode property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCBSCode(String value)
   {
      this.cbsCode = value;
   }

   /**
    * Gets the value of the cbsId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCBSId()
   {
      return cbsId;
   }

   /**
    * Sets the value of the cbsId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCBSId(Integer value)
   {
      this.cbsId = value;
   }

   /**
    * Gets the value of the cbsObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCBSObjectId()
   {
      return cbsObjectId;
   }

   /**
    * Sets the value of the cbsObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCBSObjectId(Integer value)
   {
      this.cbsObjectId = value;
   }

   /**
    * Gets the value of the calendarName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCalendarName()
   {
      return calendarName;
   }

   /**
    * Sets the value of the calendarName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCalendarName(String value)
   {
      this.calendarName = value;
   }

   /**
    * Gets the value of the calendarObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCalendarObjectId()
   {
      return calendarObjectId;
   }

   /**
    * Sets the value of the calendarObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCalendarObjectId(Integer value)
   {
      this.calendarObjectId = value;
   }

   /**
    * Gets the value of the costPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostPercentComplete()
   {
      return costPercentComplete;
   }

   /**
    * Sets the value of the costPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostPercentComplete(Double value)
   {
      this.costPercentComplete = value;
   }

   /**
    * Gets the value of the costPercentOfPlanned property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostPercentOfPlanned()
   {
      return costPercentOfPlanned;
   }

   /**
    * Sets the value of the costPercentOfPlanned property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostPercentOfPlanned(Double value)
   {
      this.costPercentOfPlanned = value;
   }

   /**
    * Gets the value of the costPerformanceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostPerformanceIndex()
   {
      return costPerformanceIndex;
   }

   /**
    * Sets the value of the costPerformanceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostPerformanceIndex(Double value)
   {
      this.costPerformanceIndex = value;
   }

   /**
    * Gets the value of the costPerformanceIndexLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostPerformanceIndexLaborUnits()
   {
      return costPerformanceIndexLaborUnits;
   }

   /**
    * Sets the value of the costPerformanceIndexLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostPerformanceIndexLaborUnits(Double value)
   {
      this.costPerformanceIndexLaborUnits = value;
   }

   /**
    * Gets the value of the costVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostVariance()
   {
      return costVariance;
   }

   /**
    * Sets the value of the costVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostVariance(Double value)
   {
      this.costVariance = value;
   }

   /**
    * Gets the value of the costVarianceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostVarianceIndex()
   {
      return costVarianceIndex;
   }

   /**
    * Sets the value of the costVarianceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostVarianceIndex(Double value)
   {
      this.costVarianceIndex = value;
   }

   /**
    * Gets the value of the costVarianceIndexLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostVarianceIndexLaborUnits()
   {
      return costVarianceIndexLaborUnits;
   }

   /**
    * Sets the value of the costVarianceIndexLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostVarianceIndexLaborUnits(Double value)
   {
      this.costVarianceIndexLaborUnits = value;
   }

   /**
    * Gets the value of the costVarianceLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostVarianceLaborUnits()
   {
      return costVarianceLaborUnits;
   }

   /**
    * Sets the value of the costVarianceLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostVarianceLaborUnits(Double value)
   {
      this.costVarianceLaborUnits = value;
   }

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the dataDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getDataDate()
   {
      return dataDate;
   }

   /**
    * Sets the value of the dataDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDataDate(LocalDateTime value)
   {
      this.dataDate = value;
   }

   /**
    * Gets the value of the duration1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDuration1Variance()
   {
      return duration1Variance;
   }

   /**
    * Sets the value of the duration1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDuration1Variance(Double value)
   {
      this.duration1Variance = value;
   }

   /**
    * Gets the value of the duration2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDuration2Variance()
   {
      return duration2Variance;
   }

   /**
    * Sets the value of the duration2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDuration2Variance(Double value)
   {
      this.duration2Variance = value;
   }

   /**
    * Gets the value of the duration3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDuration3Variance()
   {
      return duration3Variance;
   }

   /**
    * Sets the value of the duration3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDuration3Variance(Double value)
   {
      this.duration3Variance = value;
   }

   /**
    * Gets the value of the durationPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDurationPercentComplete()
   {
      return durationPercentComplete;
   }

   /**
    * Sets the value of the durationPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationPercentComplete(Double value)
   {
      this.durationPercentComplete = value;
   }

   /**
    * Gets the value of the durationPercentOfPlanned property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDurationPercentOfPlanned()
   {
      return durationPercentOfPlanned;
   }

   /**
    * Sets the value of the durationPercentOfPlanned property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationPercentOfPlanned(Double value)
   {
      this.durationPercentOfPlanned = value;
   }

   /**
    * Gets the value of the durationType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDurationType()
   {
      return durationType;
   }

   /**
    * Sets the value of the durationType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationType(String value)
   {
      this.durationType = value;
   }

   /**
    * Gets the value of the durationVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDurationVariance()
   {
      return durationVariance;
   }

   /**
    * Sets the value of the durationVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDurationVariance(Double value)
   {
      this.durationVariance = value;
   }

   /**
    * Gets the value of the earlyFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEarlyFinishDate()
   {
      return earlyFinishDate;
   }

   /**
    * Sets the value of the earlyFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEarlyFinishDate(LocalDateTime value)
   {
      this.earlyFinishDate = value;
   }

   /**
    * Gets the value of the earlyStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEarlyStartDate()
   {
      return earlyStartDate;
   }

   /**
    * Sets the value of the earlyStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEarlyStartDate(LocalDateTime value)
   {
      this.earlyStartDate = value;
   }

   /**
    * Gets the value of the earnedValueCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEarnedValueCost()
   {
      return earnedValueCost;
   }

   /**
    * Sets the value of the earnedValueCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEarnedValueCost(Double value)
   {
      this.earnedValueCost = value;
   }

   /**
    * Gets the value of the earnedValueLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEarnedValueLaborUnits()
   {
      return earnedValueLaborUnits;
   }

   /**
    * Sets the value of the earnedValueLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEarnedValueLaborUnits(Double value)
   {
      this.earnedValueLaborUnits = value;
   }

   /**
    * Gets the value of the estimateAtCompletionCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEstimateAtCompletionCost()
   {
      return estimateAtCompletionCost;
   }

   /**
    * Sets the value of the estimateAtCompletionCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEstimateAtCompletionCost(Double value)
   {
      this.estimateAtCompletionCost = value;
   }

   /**
    * Gets the value of the estimateAtCompletionLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEstimateAtCompletionLaborUnits()
   {
      return estimateAtCompletionLaborUnits;
   }

   /**
    * Sets the value of the estimateAtCompletionLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEstimateAtCompletionLaborUnits(Double value)
   {
      this.estimateAtCompletionLaborUnits = value;
   }

   /**
    * Gets the value of the estimateToComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEstimateToComplete()
   {
      return estimateToComplete;
   }

   /**
    * Sets the value of the estimateToComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEstimateToComplete(Double value)
   {
      this.estimateToComplete = value;
   }

   /**
    * Gets the value of the estimateToCompleteLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEstimateToCompleteLaborUnits()
   {
      return estimateToCompleteLaborUnits;
   }

   /**
    * Sets the value of the estimateToCompleteLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEstimateToCompleteLaborUnits(Double value)
   {
      this.estimateToCompleteLaborUnits = value;
   }

   /**
    * Gets the value of the estimatedWeight property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getEstimatedWeight()
   {
      return estimatedWeight;
   }

   /**
    * Sets the value of the estimatedWeight property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEstimatedWeight(Double value)
   {
      this.estimatedWeight = value;
   }

   /**
    * Gets the value of the expectedFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getExpectedFinishDate()
   {
      return expectedFinishDate;
   }

   /**
    * Sets the value of the expectedFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpectedFinishDate(LocalDateTime value)
   {
      this.expectedFinishDate = value;
   }

   /**
    * Gets the value of the expenseCost1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExpenseCost1Variance()
   {
      return expenseCost1Variance;
   }

   /**
    * Sets the value of the expenseCost1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpenseCost1Variance(Double value)
   {
      this.expenseCost1Variance = value;
   }

   /**
    * Gets the value of the expenseCost2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExpenseCost2Variance()
   {
      return expenseCost2Variance;
   }

   /**
    * Sets the value of the expenseCost2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpenseCost2Variance(Double value)
   {
      this.expenseCost2Variance = value;
   }

   /**
    * Gets the value of the expenseCost3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExpenseCost3Variance()
   {
      return expenseCost3Variance;
   }

   /**
    * Sets the value of the expenseCost3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpenseCost3Variance(Double value)
   {
      this.expenseCost3Variance = value;
   }

   /**
    * Gets the value of the expenseCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExpenseCostPercentComplete()
   {
      return expenseCostPercentComplete;
   }

   /**
    * Sets the value of the expenseCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpenseCostPercentComplete(Double value)
   {
      this.expenseCostPercentComplete = value;
   }

   /**
    * Gets the value of the expenseCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getExpenseCostVariance()
   {
      return expenseCostVariance;
   }

   /**
    * Sets the value of the expenseCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExpenseCostVariance(Double value)
   {
      this.expenseCostVariance = value;
   }

   /**
    * Gets the value of the externalEarlyStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getExternalEarlyStartDate()
   {
      return externalEarlyStartDate;
   }

   /**
    * Sets the value of the externalEarlyStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExternalEarlyStartDate(LocalDateTime value)
   {
      this.externalEarlyStartDate = value;
   }

   /**
    * Gets the value of the externalLateFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getExternalLateFinishDate()
   {
      return externalLateFinishDate;
   }

   /**
    * Sets the value of the externalLateFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setExternalLateFinishDate(LocalDateTime value)
   {
      this.externalLateFinishDate = value;
   }

   /**
    * Gets the value of the feedback property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getFeedback()
   {
      return feedback;
   }

   /**
    * Sets the value of the feedback property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFeedback(String value)
   {
      this.feedback = value;
   }

   /**
    * Gets the value of the financialPeriodTmplId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFinancialPeriodTmplId()
   {
      return financialPeriodTmplId;
   }

   /**
    * Sets the value of the financialPeriodTmplId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFinancialPeriodTmplId(Integer value)
   {
      this.financialPeriodTmplId = value;
   }

   /**
    * Gets the value of the finishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getFinishDate()
   {
      return finishDate;
   }

   /**
    * Sets the value of the finishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDate(LocalDateTime value)
   {
      this.finishDate = value;
   }

   /**
    * Gets the value of the finishDate1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getFinishDate1Variance()
   {
      return finishDate1Variance;
   }

   /**
    * Sets the value of the finishDate1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDate1Variance(Double value)
   {
      this.finishDate1Variance = value;
   }

   /**
    * Gets the value of the finishDate2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getFinishDate2Variance()
   {
      return finishDate2Variance;
   }

   /**
    * Sets the value of the finishDate2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDate2Variance(Double value)
   {
      this.finishDate2Variance = value;
   }

   /**
    * Gets the value of the finishDate3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getFinishDate3Variance()
   {
      return finishDate3Variance;
   }

   /**
    * Sets the value of the finishDate3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDate3Variance(Double value)
   {
      this.finishDate3Variance = value;
   }

   /**
    * Gets the value of the finishDateVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getFinishDateVariance()
   {
      return finishDateVariance;
   }

   /**
    * Sets the value of the finishDateVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDateVariance(Double value)
   {
      this.finishDateVariance = value;
   }

   /**
    * Gets the value of the floatPath property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFloatPath()
   {
      return floatPath;
   }

   /**
    * Sets the value of the floatPath property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFloatPath(Integer value)
   {
      this.floatPath = value;
   }

   /**
    * Gets the value of the floatPathOrder property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getFloatPathOrder()
   {
      return floatPathOrder;
   }

   /**
    * Sets the value of the floatPathOrder property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setFloatPathOrder(Integer value)
   {
      this.floatPathOrder = value;
   }

   /**
    * Gets the value of the freeFloat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getFreeFloat()
   {
      return freeFloat;
   }

   /**
    * Sets the value of the freeFloat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFreeFloat(Double value)
   {
      this.freeFloat = value;
   }

   /**
    * Gets the value of the guid property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getGUID()
   {
      return guid;
   }

   /**
    * Sets the value of the guid property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setGUID(String value)
   {
      this.guid = value;
   }

   /**
    * Gets the value of the hasFutureBucketData property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isHasFutureBucketData()
   {
      return hasFutureBucketData;
   }

   /**
    * Sets the value of the hasFutureBucketData property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setHasFutureBucketData(Boolean value)
   {
      this.hasFutureBucketData = value;
   }

   /**
    * Gets the value of the id property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getId()
   {
      return id;
   }

   /**
    * Sets the value of the id property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setId(String value)
   {
      this.id = value;
   }

   /**
    * Gets the value of the isBaseline property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsBaseline()
   {
      return isBaseline;
   }

   /**
    * Sets the value of the isBaseline property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsBaseline(Boolean value)
   {
      this.isBaseline = value;
   }

   /**
    * Gets the value of the isCritical property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsCritical()
   {
      return isCritical;
   }

   /**
    * Sets the value of the isCritical property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsCritical(Boolean value)
   {
      this.isCritical = value;
   }

   /**
    * Gets the value of the isLongestPath property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsLongestPath()
   {
      return isLongestPath;
   }

   /**
    * Sets the value of the isLongestPath property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsLongestPath(Boolean value)
   {
      this.isLongestPath = value;
   }

   /**
    * Gets the value of the isNewFeedback property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsNewFeedback()
   {
      return isNewFeedback;
   }

   /**
    * Sets the value of the isNewFeedback property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsNewFeedback(Boolean value)
   {
      this.isNewFeedback = value;
   }

   /**
    * Gets the value of the isStarred property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsStarred()
   {
      return isStarred;
   }

   /**
    * Sets the value of the isStarred property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsStarred(Boolean value)
   {
      this.isStarred = value;
   }

   /**
    * Gets the value of the isTemplate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsTemplate()
   {
      return isTemplate;
   }

   /**
    * Sets the value of the isTemplate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsTemplate(Boolean value)
   {
      this.isTemplate = value;
   }

   /**
    * Gets the value of the isWorkPackage property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsWorkPackage()
   {
      return isWorkPackage;
   }

   /**
    * Sets the value of the isWorkPackage property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsWorkPackage(Boolean value)
   {
      this.isWorkPackage = value;
   }

   /**
    * Gets the value of the laborCost1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborCost1Variance()
   {
      return laborCost1Variance;
   }

   /**
    * Sets the value of the laborCost1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborCost1Variance(Double value)
   {
      this.laborCost1Variance = value;
   }

   /**
    * Gets the value of the laborCost2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborCost2Variance()
   {
      return laborCost2Variance;
   }

   /**
    * Sets the value of the laborCost2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborCost2Variance(Double value)
   {
      this.laborCost2Variance = value;
   }

   /**
    * Gets the value of the laborCost3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborCost3Variance()
   {
      return laborCost3Variance;
   }

   /**
    * Sets the value of the laborCost3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborCost3Variance(Double value)
   {
      this.laborCost3Variance = value;
   }

   /**
    * Gets the value of the laborCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborCostPercentComplete()
   {
      return laborCostPercentComplete;
   }

   /**
    * Sets the value of the laborCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborCostPercentComplete(Double value)
   {
      this.laborCostPercentComplete = value;
   }

   /**
    * Gets the value of the laborCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborCostVariance()
   {
      return laborCostVariance;
   }

   /**
    * Sets the value of the laborCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborCostVariance(Double value)
   {
      this.laborCostVariance = value;
   }

   /**
    * Gets the value of the laborUnits1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborUnits1Variance()
   {
      return laborUnits1Variance;
   }

   /**
    * Sets the value of the laborUnits1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborUnits1Variance(Double value)
   {
      this.laborUnits1Variance = value;
   }

   /**
    * Gets the value of the laborUnits2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborUnits2Variance()
   {
      return laborUnits2Variance;
   }

   /**
    * Sets the value of the laborUnits2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborUnits2Variance(Double value)
   {
      this.laborUnits2Variance = value;
   }

   /**
    * Gets the value of the laborUnits3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborUnits3Variance()
   {
      return laborUnits3Variance;
   }

   /**
    * Sets the value of the laborUnits3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborUnits3Variance(Double value)
   {
      this.laborUnits3Variance = value;
   }

   /**
    * Gets the value of the laborUnitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborUnitsPercentComplete()
   {
      return laborUnitsPercentComplete;
   }

   /**
    * Sets the value of the laborUnitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborUnitsPercentComplete(Double value)
   {
      this.laborUnitsPercentComplete = value;
   }

   /**
    * Gets the value of the laborUnitsVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getLaborUnitsVariance()
   {
      return laborUnitsVariance;
   }

   /**
    * Sets the value of the laborUnitsVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLaborUnitsVariance(Double value)
   {
      this.laborUnitsVariance = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the lateFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLateFinishDate()
   {
      return lateFinishDate;
   }

   /**
    * Sets the value of the lateFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLateFinishDate(LocalDateTime value)
   {
      this.lateFinishDate = value;
   }

   /**
    * Gets the value of the lateStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLateStartDate()
   {
      return lateStartDate;
   }

   /**
    * Sets the value of the lateStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLateStartDate(LocalDateTime value)
   {
      this.lateStartDate = value;
   }

   /**
    * Gets the value of the levelingPriority property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLevelingPriority()
   {
      return levelingPriority;
   }

   /**
    * Sets the value of the levelingPriority property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLevelingPriority(String value)
   {
      this.levelingPriority = value;
   }

   /**
    * Gets the value of the locationName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLocationName()
   {
      return locationName;
   }

   /**
    * Sets the value of the locationName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLocationName(String value)
   {
      this.locationName = value;
   }

   /**
    * Gets the value of the locationObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getLocationObjectId()
   {
      return locationObjectId;
   }

   /**
    * Sets the value of the locationObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setLocationObjectId(Integer value)
   {
      this.locationObjectId = value;
   }

   /**
    * Gets the value of the materialCost1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaterialCost1Variance()
   {
      return materialCost1Variance;
   }

   /**
    * Sets the value of the materialCost1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaterialCost1Variance(Double value)
   {
      this.materialCost1Variance = value;
   }

   /**
    * Gets the value of the materialCost2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaterialCost2Variance()
   {
      return materialCost2Variance;
   }

   /**
    * Sets the value of the materialCost2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaterialCost2Variance(Double value)
   {
      this.materialCost2Variance = value;
   }

   /**
    * Gets the value of the materialCost3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaterialCost3Variance()
   {
      return materialCost3Variance;
   }

   /**
    * Sets the value of the materialCost3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaterialCost3Variance(Double value)
   {
      this.materialCost3Variance = value;
   }

   /**
    * Gets the value of the materialCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaterialCostPercentComplete()
   {
      return materialCostPercentComplete;
   }

   /**
    * Sets the value of the materialCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaterialCostPercentComplete(Double value)
   {
      this.materialCostPercentComplete = value;
   }

   /**
    * Gets the value of the materialCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaterialCostVariance()
   {
      return materialCostVariance;
   }

   /**
    * Sets the value of the materialCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaterialCostVariance(Double value)
   {
      this.materialCostVariance = value;
   }

   /**
    * Gets the value of the maximumDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaximumDuration()
   {
      return maximumDuration;
   }

   /**
    * Sets the value of the maximumDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaximumDuration(Double value)
   {
      this.maximumDuration = value;
   }

   /**
    * Gets the value of the minimumDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMinimumDuration()
   {
      return minimumDuration;
   }

   /**
    * Sets the value of the minimumDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMinimumDuration(Double value)
   {
      this.minimumDuration = value;
   }

   /**
    * Gets the value of the mostLikelyDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMostLikelyDuration()
   {
      return mostLikelyDuration;
   }

   /**
    * Sets the value of the mostLikelyDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMostLikelyDuration(Double value)
   {
      this.mostLikelyDuration = value;
   }

   /**
    * Gets the value of the name property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getName()
   {
      return name;
   }

   /**
    * Sets the value of the name property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setName(String value)
   {
      this.name = value;
   }

   /**
    * Gets the value of the nonLaborCost1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborCost1Variance()
   {
      return nonLaborCost1Variance;
   }

   /**
    * Sets the value of the nonLaborCost1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborCost1Variance(Double value)
   {
      this.nonLaborCost1Variance = value;
   }

   /**
    * Gets the value of the nonLaborCost2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborCost2Variance()
   {
      return nonLaborCost2Variance;
   }

   /**
    * Sets the value of the nonLaborCost2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborCost2Variance(Double value)
   {
      this.nonLaborCost2Variance = value;
   }

   /**
    * Gets the value of the nonLaborCost3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborCost3Variance()
   {
      return nonLaborCost3Variance;
   }

   /**
    * Sets the value of the nonLaborCost3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborCost3Variance(Double value)
   {
      this.nonLaborCost3Variance = value;
   }

   /**
    * Gets the value of the nonLaborCostPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborCostPercentComplete()
   {
      return nonLaborCostPercentComplete;
   }

   /**
    * Sets the value of the nonLaborCostPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborCostPercentComplete(Double value)
   {
      this.nonLaborCostPercentComplete = value;
   }

   /**
    * Gets the value of the nonLaborCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborCostVariance()
   {
      return nonLaborCostVariance;
   }

   /**
    * Sets the value of the nonLaborCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborCostVariance(Double value)
   {
      this.nonLaborCostVariance = value;
   }

   /**
    * Gets the value of the nonLaborUnits1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborUnits1Variance()
   {
      return nonLaborUnits1Variance;
   }

   /**
    * Sets the value of the nonLaborUnits1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborUnits1Variance(Double value)
   {
      this.nonLaborUnits1Variance = value;
   }

   /**
    * Gets the value of the nonLaborUnits2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborUnits2Variance()
   {
      return nonLaborUnits2Variance;
   }

   /**
    * Sets the value of the nonLaborUnits2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborUnits2Variance(Double value)
   {
      this.nonLaborUnits2Variance = value;
   }

   /**
    * Gets the value of the nonLaborUnits3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborUnits3Variance()
   {
      return nonLaborUnits3Variance;
   }

   /**
    * Sets the value of the nonLaborUnits3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborUnits3Variance(Double value)
   {
      this.nonLaborUnits3Variance = value;
   }

   /**
    * Gets the value of the nonLaborUnitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborUnitsPercentComplete()
   {
      return nonLaborUnitsPercentComplete;
   }

   /**
    * Sets the value of the nonLaborUnitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborUnitsPercentComplete(Double value)
   {
      this.nonLaborUnitsPercentComplete = value;
   }

   /**
    * Gets the value of the nonLaborUnitsVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getNonLaborUnitsVariance()
   {
      return nonLaborUnitsVariance;
   }

   /**
    * Sets the value of the nonLaborUnitsVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNonLaborUnitsVariance(Double value)
   {
      this.nonLaborUnitsVariance = value;
   }

   /**
    * Gets the value of the notesToResources property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getNotesToResources()
   {
      return notesToResources;
   }

   /**
    * Sets the value of the notesToResources property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setNotesToResources(String value)
   {
      this.notesToResources = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the ownerIDArray property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOwnerIDArray()
   {
      return ownerIDArray;
   }

   /**
    * Sets the value of the ownerIDArray property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOwnerIDArray(String value)
   {
      this.ownerIDArray = value;
   }

   /**
    * Gets the value of the ownerNamesArray property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOwnerNamesArray()
   {
      return ownerNamesArray;
   }

   /**
    * Sets the value of the ownerNamesArray property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOwnerNamesArray(String value)
   {
      this.ownerNamesArray = value;
   }

   /**
    * Gets the value of the percentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPercentComplete()
   {
      return percentComplete;
   }

   /**
    * Sets the value of the percentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPercentComplete(Double value)
   {
      this.percentComplete = value;
   }

   /**
    * Gets the value of the percentCompleteType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPercentCompleteType()
   {
      return percentCompleteType;
   }

   /**
    * Sets the value of the percentCompleteType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPercentCompleteType(String value)
   {
      this.percentCompleteType = value;
   }

   /**
    * Gets the value of the performancePercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPerformancePercentComplete()
   {
      return performancePercentComplete;
   }

   /**
    * Sets the value of the performancePercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPerformancePercentComplete(Double value)
   {
      this.performancePercentComplete = value;
   }

   /**
    * Gets the value of the performancePercentCompleteByLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPerformancePercentCompleteByLaborUnits()
   {
      return performancePercentCompleteByLaborUnits;
   }

   /**
    * Sets the value of the performancePercentCompleteByLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPerformancePercentCompleteByLaborUnits(Double value)
   {
      this.performancePercentCompleteByLaborUnits = value;
   }

   /**
    * Gets the value of the physicalPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPhysicalPercentComplete()
   {
      return physicalPercentComplete;
   }

   /**
    * Sets the value of the physicalPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPhysicalPercentComplete(Double value)
   {
      this.physicalPercentComplete = value;
   }

   /**
    * Gets the value of the plannedDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedDuration()
   {
      return plannedDuration;
   }

   /**
    * Sets the value of the plannedDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedDuration(Double value)
   {
      this.plannedDuration = value;
   }

   /**
    * Gets the value of the plannedExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedExpenseCost()
   {
      return plannedExpenseCost;
   }

   /**
    * Sets the value of the plannedExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedExpenseCost(Double value)
   {
      this.plannedExpenseCost = value;
   }

   /**
    * Gets the value of the plannedFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPlannedFinishDate()
   {
      return plannedFinishDate;
   }

   /**
    * Sets the value of the plannedFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedFinishDate(LocalDateTime value)
   {
      this.plannedFinishDate = value;
   }

   /**
    * Gets the value of the plannedLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedLaborCost()
   {
      return plannedLaborCost;
   }

   /**
    * Sets the value of the plannedLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedLaborCost(Double value)
   {
      this.plannedLaborCost = value;
   }

   /**
    * Gets the value of the plannedLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedLaborUnits()
   {
      return plannedLaborUnits;
   }

   /**
    * Sets the value of the plannedLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedLaborUnits(Double value)
   {
      this.plannedLaborUnits = value;
   }

   /**
    * Gets the value of the plannedMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedMaterialCost()
   {
      return plannedMaterialCost;
   }

   /**
    * Sets the value of the plannedMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedMaterialCost(Double value)
   {
      this.plannedMaterialCost = value;
   }

   /**
    * Gets the value of the plannedNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedNonLaborCost()
   {
      return plannedNonLaborCost;
   }

   /**
    * Sets the value of the plannedNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedNonLaborCost(Double value)
   {
      this.plannedNonLaborCost = value;
   }

   /**
    * Gets the value of the plannedNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedNonLaborUnits()
   {
      return plannedNonLaborUnits;
   }

   /**
    * Sets the value of the plannedNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedNonLaborUnits(Double value)
   {
      this.plannedNonLaborUnits = value;
   }

   /**
    * Gets the value of the plannedStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPlannedStartDate()
   {
      return plannedStartDate;
   }

   /**
    * Sets the value of the plannedStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedStartDate(LocalDateTime value)
   {
      this.plannedStartDate = value;
   }

   /**
    * Gets the value of the plannedTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedTotalCost()
   {
      return plannedTotalCost;
   }

   /**
    * Sets the value of the plannedTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedTotalCost(Double value)
   {
      this.plannedTotalCost = value;
   }

   /**
    * Gets the value of the plannedTotalUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedTotalUnits()
   {
      return plannedTotalUnits;
   }

   /**
    * Sets the value of the plannedTotalUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedTotalUnits(Double value)
   {
      this.plannedTotalUnits = value;
   }

   /**
    * Gets the value of the plannedValueCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedValueCost()
   {
      return plannedValueCost;
   }

   /**
    * Sets the value of the plannedValueCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedValueCost(Double value)
   {
      this.plannedValueCost = value;
   }

   /**
    * Gets the value of the plannedValueLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPlannedValueLaborUnits()
   {
      return plannedValueLaborUnits;
   }

   /**
    * Sets the value of the plannedValueLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPlannedValueLaborUnits(Double value)
   {
      this.plannedValueLaborUnits = value;
   }

   /**
    * Gets the value of the postRespCriticalityIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPostRespCriticalityIndex()
   {
      return postRespCriticalityIndex;
   }

   /**
    * Sets the value of the postRespCriticalityIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPostRespCriticalityIndex(Double value)
   {
      this.postRespCriticalityIndex = value;
   }

   /**
    * Gets the value of the postResponsePessimisticFinish property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPostResponsePessimisticFinish()
   {
      return postResponsePessimisticFinish;
   }

   /**
    * Sets the value of the postResponsePessimisticFinish property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPostResponsePessimisticFinish(LocalDateTime value)
   {
      this.postResponsePessimisticFinish = value;
   }

   /**
    * Gets the value of the postResponsePessimisticStart property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPostResponsePessimisticStart()
   {
      return postResponsePessimisticStart;
   }

   /**
    * Sets the value of the postResponsePessimisticStart property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPostResponsePessimisticStart(LocalDateTime value)
   {
      this.postResponsePessimisticStart = value;
   }

   /**
    * Gets the value of the preRespCriticalityIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPreRespCriticalityIndex()
   {
      return preRespCriticalityIndex;
   }

   /**
    * Sets the value of the preRespCriticalityIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPreRespCriticalityIndex(Double value)
   {
      this.preRespCriticalityIndex = value;
   }

   /**
    * Gets the value of the preResponsePessimisticFinish property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPreResponsePessimisticFinish()
   {
      return preResponsePessimisticFinish;
   }

   /**
    * Sets the value of the preResponsePessimisticFinish property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPreResponsePessimisticFinish(LocalDateTime value)
   {
      this.preResponsePessimisticFinish = value;
   }

   /**
    * Gets the value of the preResponsePessimisticStart property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPreResponsePessimisticStart()
   {
      return preResponsePessimisticStart;
   }

   /**
    * Sets the value of the preResponsePessimisticStart property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPreResponsePessimisticStart(LocalDateTime value)
   {
      this.preResponsePessimisticStart = value;
   }

   /**
    * Gets the value of the primaryConstraintDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getPrimaryConstraintDate()
   {
      return primaryConstraintDate;
   }

   /**
    * Sets the value of the primaryConstraintDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrimaryConstraintDate(LocalDateTime value)
   {
      this.primaryConstraintDate = value;
   }

   /**
    * Gets the value of the primaryConstraintType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPrimaryConstraintType()
   {
      return primaryConstraintType;
   }

   /**
    * Sets the value of the primaryConstraintType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrimaryConstraintType(String value)
   {
      this.primaryConstraintType = value;
   }

   /**
    * Gets the value of the primaryResourceId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPrimaryResourceId()
   {
      return primaryResourceId;
   }

   /**
    * Sets the value of the primaryResourceId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrimaryResourceId(String value)
   {
      this.primaryResourceId = value;
   }

   /**
    * Gets the value of the primaryResourceName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPrimaryResourceName()
   {
      return primaryResourceName;
   }

   /**
    * Sets the value of the primaryResourceName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPrimaryResourceName(String value)
   {
      this.primaryResourceName = value;
   }

   /**
    * Gets the value of the primaryResourceObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getPrimaryResourceObjectId()
   {
      return primaryResourceObjectId;
   }

   /**
    * Sets the value of the primaryResourceObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setPrimaryResourceObjectId(Integer value)
   {
      this.primaryResourceObjectId = value;
   }

   /**
    * Gets the value of the projectFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectFlag()
   {
      return projectFlag;
   }

   /**
    * Sets the value of the projectFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectFlag(String value)
   {
      this.projectFlag = value;
   }

   /**
    * Gets the value of the projectId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectId()
   {
      return projectId;
   }

   /**
    * Sets the value of the projectId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectId(String value)
   {
      this.projectId = value;
   }

   /**
    * Gets the value of the projectName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectName()
   {
      return projectName;
   }

   /**
    * Sets the value of the projectName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectName(String value)
   {
      this.projectName = value;
   }

   /**
    * Gets the value of the projectNameSepChar property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectNameSepChar()
   {
      return projectNameSepChar;
   }

   /**
    * Sets the value of the projectNameSepChar property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectNameSepChar(String value)
   {
      this.projectNameSepChar = value;
   }

   /**
    * Gets the value of the projectObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getProjectObjectId()
   {
      return projectObjectId;
   }

   /**
    * Sets the value of the projectObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setProjectObjectId(Integer value)
   {
      this.projectObjectId = value;
   }

   /**
    * Gets the value of the projectProjectFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getProjectProjectFlag()
   {
      return projectProjectFlag;
   }

   /**
    * Sets the value of the projectProjectFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setProjectProjectFlag(String value)
   {
      this.projectProjectFlag = value;
   }

   /**
    * Gets the value of the remainingDuration property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingDuration()
   {
      return remainingDuration;
   }

   /**
    * Sets the value of the remainingDuration property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingDuration(Double value)
   {
      this.remainingDuration = value;
   }

   /**
    * Gets the value of the remainingEarlyFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getRemainingEarlyFinishDate()
   {
      return remainingEarlyFinishDate;
   }

   /**
    * Sets the value of the remainingEarlyFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingEarlyFinishDate(LocalDateTime value)
   {
      this.remainingEarlyFinishDate = value;
   }

   /**
    * Gets the value of the remainingEarlyStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getRemainingEarlyStartDate()
   {
      return remainingEarlyStartDate;
   }

   /**
    * Sets the value of the remainingEarlyStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingEarlyStartDate(LocalDateTime value)
   {
      this.remainingEarlyStartDate = value;
   }

   /**
    * Gets the value of the remainingExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingExpenseCost()
   {
      return remainingExpenseCost;
   }

   /**
    * Sets the value of the remainingExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingExpenseCost(Double value)
   {
      this.remainingExpenseCost = value;
   }

   /**
    * Gets the value of the remainingFloat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingFloat()
   {
      return remainingFloat;
   }

   /**
    * Sets the value of the remainingFloat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingFloat(Double value)
   {
      this.remainingFloat = value;
   }

   /**
    * Gets the value of the remainingLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingLaborCost()
   {
      return remainingLaborCost;
   }

   /**
    * Sets the value of the remainingLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingLaborCost(Double value)
   {
      this.remainingLaborCost = value;
   }

   /**
    * Gets the value of the remainingLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingLaborUnits()
   {
      return remainingLaborUnits;
   }

   /**
    * Sets the value of the remainingLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingLaborUnits(Double value)
   {
      this.remainingLaborUnits = value;
   }

   /**
    * Gets the value of the remainingLateFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getRemainingLateFinishDate()
   {
      return remainingLateFinishDate;
   }

   /**
    * Sets the value of the remainingLateFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingLateFinishDate(LocalDateTime value)
   {
      this.remainingLateFinishDate = value;
   }

   /**
    * Gets the value of the remainingLateStartDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getRemainingLateStartDate()
   {
      return remainingLateStartDate;
   }

   /**
    * Sets the value of the remainingLateStartDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingLateStartDate(LocalDateTime value)
   {
      this.remainingLateStartDate = value;
   }

   /**
    * Gets the value of the remainingMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingMaterialCost()
   {
      return remainingMaterialCost;
   }

   /**
    * Sets the value of the remainingMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingMaterialCost(Double value)
   {
      this.remainingMaterialCost = value;
   }

   /**
    * Gets the value of the remainingNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingNonLaborCost()
   {
      return remainingNonLaborCost;
   }

   /**
    * Sets the value of the remainingNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingNonLaborCost(Double value)
   {
      this.remainingNonLaborCost = value;
   }

   /**
    * Gets the value of the remainingNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingNonLaborUnits()
   {
      return remainingNonLaborUnits;
   }

   /**
    * Sets the value of the remainingNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingNonLaborUnits(Double value)
   {
      this.remainingNonLaborUnits = value;
   }

   /**
    * Gets the value of the remainingTotalCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingTotalCost()
   {
      return remainingTotalCost;
   }

   /**
    * Sets the value of the remainingTotalCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingTotalCost(Double value)
   {
      this.remainingTotalCost = value;
   }

   /**
    * Gets the value of the remainingTotalUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getRemainingTotalUnits()
   {
      return remainingTotalUnits;
   }

   /**
    * Sets the value of the remainingTotalUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRemainingTotalUnits(Double value)
   {
      this.remainingTotalUnits = value;
   }

   /**
    * Gets the value of the resumeDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getResumeDate()
   {
      return resumeDate;
   }

   /**
    * Sets the value of the resumeDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setResumeDate(LocalDateTime value)
   {
      this.resumeDate = value;
   }

   /**
    * Gets the value of the reviewFinishDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getReviewFinishDate()
   {
      return reviewFinishDate;
   }

   /**
    * Sets the value of the reviewFinishDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReviewFinishDate(LocalDateTime value)
   {
      this.reviewFinishDate = value;
   }

   /**
    * Gets the value of the reviewRequired property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isReviewRequired()
   {
      return reviewRequired;
   }

   /**
    * Sets the value of the reviewRequired property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReviewRequired(Boolean value)
   {
      this.reviewRequired = value;
   }

   /**
    * Gets the value of the reviewStatus property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getReviewStatus()
   {
      return reviewStatus;
   }

   /**
    * Sets the value of the reviewStatus property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReviewStatus(String value)
   {
      this.reviewStatus = value;
   }

   /**
    * Gets the value of the schedulePercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSchedulePercentComplete()
   {
      return schedulePercentComplete;
   }

   /**
    * Sets the value of the schedulePercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSchedulePercentComplete(Double value)
   {
      this.schedulePercentComplete = value;
   }

   /**
    * Gets the value of the schedulePerformanceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSchedulePerformanceIndex()
   {
      return schedulePerformanceIndex;
   }

   /**
    * Sets the value of the schedulePerformanceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSchedulePerformanceIndex(Double value)
   {
      this.schedulePerformanceIndex = value;
   }

   /**
    * Gets the value of the schedulePerformanceIndexLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getSchedulePerformanceIndexLaborUnits()
   {
      return schedulePerformanceIndexLaborUnits;
   }

   /**
    * Sets the value of the schedulePerformanceIndexLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSchedulePerformanceIndexLaborUnits(Double value)
   {
      this.schedulePerformanceIndexLaborUnits = value;
   }

   /**
    * Gets the value of the scheduleVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getScheduleVariance()
   {
      return scheduleVariance;
   }

   /**
    * Sets the value of the scheduleVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScheduleVariance(Double value)
   {
      this.scheduleVariance = value;
   }

   /**
    * Gets the value of the scheduleVarianceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getScheduleVarianceIndex()
   {
      return scheduleVarianceIndex;
   }

   /**
    * Sets the value of the scheduleVarianceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScheduleVarianceIndex(Double value)
   {
      this.scheduleVarianceIndex = value;
   }

   /**
    * Gets the value of the scheduleVarianceIndexLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getScheduleVarianceIndexLaborUnits()
   {
      return scheduleVarianceIndexLaborUnits;
   }

   /**
    * Sets the value of the scheduleVarianceIndexLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScheduleVarianceIndexLaborUnits(Double value)
   {
      this.scheduleVarianceIndexLaborUnits = value;
   }

   /**
    * Gets the value of the scheduleVarianceLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getScheduleVarianceLaborUnits()
   {
      return scheduleVarianceLaborUnits;
   }

   /**
    * Sets the value of the scheduleVarianceLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScheduleVarianceLaborUnits(Double value)
   {
      this.scheduleVarianceLaborUnits = value;
   }

   /**
    * Gets the value of the scopePercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getScopePercentComplete()
   {
      return scopePercentComplete;
   }

   /**
    * Sets the value of the scopePercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setScopePercentComplete(Double value)
   {
      this.scopePercentComplete = value;
   }

   /**
    * Gets the value of the secondaryConstraintDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSecondaryConstraintDate()
   {
      return secondaryConstraintDate;
   }

   /**
    * Sets the value of the secondaryConstraintDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSecondaryConstraintDate(LocalDateTime value)
   {
      this.secondaryConstraintDate = value;
   }

   /**
    * Gets the value of the secondaryConstraintType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSecondaryConstraintType()
   {
      return secondaryConstraintType;
   }

   /**
    * Sets the value of the secondaryConstraintType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSecondaryConstraintType(String value)
   {
      this.secondaryConstraintType = value;
   }

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the startDate1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getStartDate1Variance()
   {
      return startDate1Variance;
   }

   /**
    * Sets the value of the startDate1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate1Variance(Double value)
   {
      this.startDate1Variance = value;
   }

   /**
    * Gets the value of the startDate2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getStartDate2Variance()
   {
      return startDate2Variance;
   }

   /**
    * Sets the value of the startDate2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate2Variance(Double value)
   {
      this.startDate2Variance = value;
   }

   /**
    * Gets the value of the startDate3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getStartDate3Variance()
   {
      return startDate3Variance;
   }

   /**
    * Sets the value of the startDate3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate3Variance(Double value)
   {
      this.startDate3Variance = value;
   }

   /**
    * Gets the value of the startDateVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getStartDateVariance()
   {
      return startDateVariance;
   }

   /**
    * Sets the value of the startDateVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDateVariance(Double value)
   {
      this.startDateVariance = value;
   }

   /**
    * Gets the value of the status property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getStatus()
   {
      return status;
   }

   /**
    * Sets the value of the status property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStatus(String value)
   {
      this.status = value;
   }

   /**
    * Gets the value of the statusCode property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getStatusCode()
   {
      return statusCode;
   }

   /**
    * Sets the value of the statusCode property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStatusCode(String value)
   {
      this.statusCode = value;
   }

   /**
    * Gets the value of the suspendDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getSuspendDate()
   {
      return suspendDate;
   }

   /**
    * Sets the value of the suspendDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSuspendDate(LocalDateTime value)
   {
      this.suspendDate = value;
   }

   /**
    * Gets the value of the taskStatusCompletion property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTaskStatusCompletion()
   {
      return taskStatusCompletion;
   }

   /**
    * Sets the value of the taskStatusCompletion property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTaskStatusCompletion(String value)
   {
      this.taskStatusCompletion = value;
   }

   /**
    * Gets the value of the taskStatusDates property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTaskStatusDates()
   {
      return taskStatusDates;
   }

   /**
    * Sets the value of the taskStatusDates property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTaskStatusDates(String value)
   {
      this.taskStatusDates = value;
   }

   /**
    * Gets the value of the taskStatusIndicator property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isTaskStatusIndicator()
   {
      return taskStatusIndicator;
   }

   /**
    * Sets the value of the taskStatusIndicator property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTaskStatusIndicator(Boolean value)
   {
      this.taskStatusIndicator = value;
   }

   /**
    * Gets the value of the toCompletePerformanceIndex property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getToCompletePerformanceIndex()
   {
      return toCompletePerformanceIndex;
   }

   /**
    * Sets the value of the toCompletePerformanceIndex property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setToCompletePerformanceIndex(Double value)
   {
      this.toCompletePerformanceIndex = value;
   }

   /**
    * Gets the value of the totalCost1Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalCost1Variance()
   {
      return totalCost1Variance;
   }

   /**
    * Sets the value of the totalCost1Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalCost1Variance(Double value)
   {
      this.totalCost1Variance = value;
   }

   /**
    * Gets the value of the totalCost2Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalCost2Variance()
   {
      return totalCost2Variance;
   }

   /**
    * Sets the value of the totalCost2Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalCost2Variance(Double value)
   {
      this.totalCost2Variance = value;
   }

   /**
    * Gets the value of the totalCost3Variance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalCost3Variance()
   {
      return totalCost3Variance;
   }

   /**
    * Sets the value of the totalCost3Variance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalCost3Variance(Double value)
   {
      this.totalCost3Variance = value;
   }

   /**
    * Gets the value of the totalCostVariance property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalCostVariance()
   {
      return totalCostVariance;
   }

   /**
    * Sets the value of the totalCostVariance property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalCostVariance(Double value)
   {
      this.totalCostVariance = value;
   }

   /**
    * Gets the value of the totalFloat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalFloat()
   {
      return totalFloat;
   }

   /**
    * Sets the value of the totalFloat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalFloat(Double value)
   {
      this.totalFloat = value;
   }

   /**
    * Gets the value of the totalPastPeriodEarnedValueCostBCWP property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodEarnedValueCostBCWP()
   {
      return totalPastPeriodEarnedValueCostBCWP;
   }

   /**
    * Sets the value of the totalPastPeriodEarnedValueCostBCWP property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodEarnedValueCostBCWP(Double value)
   {
      this.totalPastPeriodEarnedValueCostBCWP = value;
   }

   /**
    * Gets the value of the totalPastPeriodEarnedValueLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodEarnedValueLaborUnits()
   {
      return totalPastPeriodEarnedValueLaborUnits;
   }

   /**
    * Sets the value of the totalPastPeriodEarnedValueLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodEarnedValueLaborUnits(Double value)
   {
      this.totalPastPeriodEarnedValueLaborUnits = value;
   }

   /**
    * Gets the value of the totalPastPeriodExpenseCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodExpenseCost()
   {
      return totalPastPeriodExpenseCost;
   }

   /**
    * Sets the value of the totalPastPeriodExpenseCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodExpenseCost(Double value)
   {
      this.totalPastPeriodExpenseCost = value;
   }

   /**
    * Gets the value of the totalPastPeriodLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodLaborCost()
   {
      return totalPastPeriodLaborCost;
   }

   /**
    * Sets the value of the totalPastPeriodLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodLaborCost(Double value)
   {
      this.totalPastPeriodLaborCost = value;
   }

   /**
    * Gets the value of the totalPastPeriodLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodLaborUnits()
   {
      return totalPastPeriodLaborUnits;
   }

   /**
    * Sets the value of the totalPastPeriodLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodLaborUnits(Double value)
   {
      this.totalPastPeriodLaborUnits = value;
   }

   /**
    * Gets the value of the totalPastPeriodMaterialCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodMaterialCost()
   {
      return totalPastPeriodMaterialCost;
   }

   /**
    * Sets the value of the totalPastPeriodMaterialCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodMaterialCost(Double value)
   {
      this.totalPastPeriodMaterialCost = value;
   }

   /**
    * Gets the value of the totalPastPeriodNonLaborCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodNonLaborCost()
   {
      return totalPastPeriodNonLaborCost;
   }

   /**
    * Sets the value of the totalPastPeriodNonLaborCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodNonLaborCost(Double value)
   {
      this.totalPastPeriodNonLaborCost = value;
   }

   /**
    * Gets the value of the totalPastPeriodNonLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodNonLaborUnits()
   {
      return totalPastPeriodNonLaborUnits;
   }

   /**
    * Sets the value of the totalPastPeriodNonLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodNonLaborUnits(Double value)
   {
      this.totalPastPeriodNonLaborUnits = value;
   }

   /**
    * Gets the value of the totalPastPeriodPlannedValueCost property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodPlannedValueCost()
   {
      return totalPastPeriodPlannedValueCost;
   }

   /**
    * Sets the value of the totalPastPeriodPlannedValueCost property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodPlannedValueCost(Double value)
   {
      this.totalPastPeriodPlannedValueCost = value;
   }

   /**
    * Gets the value of the totalPastPeriodPlannedValueLaborUnits property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getTotalPastPeriodPlannedValueLaborUnits()
   {
      return totalPastPeriodPlannedValueLaborUnits;
   }

   /**
    * Sets the value of the totalPastPeriodPlannedValueLaborUnits property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTotalPastPeriodPlannedValueLaborUnits(Double value)
   {
      this.totalPastPeriodPlannedValueLaborUnits = value;
   }

   /**
    * Gets the value of the type property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getType()
   {
      return type;
   }

   /**
    * Sets the value of the type property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setType(String value)
   {
      this.type = value;
   }

   /**
    * Gets the value of the unitsPercentComplete property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getUnitsPercentComplete()
   {
      return unitsPercentComplete;
   }

   /**
    * Sets the value of the unitsPercentComplete property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setUnitsPercentComplete(Double value)
   {
      this.unitsPercentComplete = value;
   }

   /**
    * Gets the value of the unreadCommentCount property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getUnreadCommentCount()
   {
      return unreadCommentCount;
   }

   /**
    * Sets the value of the unreadCommentCount property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setUnreadCommentCount(Integer value)
   {
      this.unreadCommentCount = value;
   }

   /**
    * Gets the value of the wbsCode property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSCode()
   {
      return wbsCode;
   }

   /**
    * Sets the value of the wbsCode property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSCode(String value)
   {
      this.wbsCode = value;
   }

   /**
    * Gets the value of the wbsName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSName()
   {
      return wbsName;
   }

   /**
    * Sets the value of the wbsName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSName(String value)
   {
      this.wbsName = value;
   }

   /**
    * Gets the value of the wbsNamePath property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSNamePath()
   {
      return wbsNamePath;
   }

   /**
    * Sets the value of the wbsNamePath property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSNamePath(String value)
   {
      this.wbsNamePath = value;
   }

   /**
    * Gets the value of the wbsObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getWBSObjectId()
   {
      return wbsObjectId;
   }

   /**
    * Sets the value of the wbsObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setWBSObjectId(Integer value)
   {
      this.wbsObjectId = value;
   }

   /**
    * Gets the value of the wbsPath property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWBSPath()
   {
      return wbsPath;
   }

   /**
    * Sets the value of the wbsPath property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWBSPath(String value)
   {
      this.wbsPath = value;
   }

   /**
    * Gets the value of the workPackageId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWorkPackageId()
   {
      return workPackageId;
   }

   /**
    * Sets the value of the workPackageId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWorkPackageId(String value)
   {
      this.workPackageId = value;
   }

   /**
    * Gets the value of the workPackageName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWorkPackageName()
   {
      return workPackageName;
   }

   /**
    * Sets the value of the workPackageName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWorkPackageName(String value)
   {
      this.workPackageName = value;
   }

   /**
    * Gets the value of the code property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the code property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getCode().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link CodeAssignmentType }
    *
    *
    */
   public List<CodeAssignmentType> getCode()
   {
      if (code == null)
      {
         code = new ArrayList<>();
      }
      return this.code;
   }

   /**
    * Gets the value of the udf property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the udf property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getUDF().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link UDFAssignmentType }
    *
    *
    */
   public List<UDFAssignmentType> getUDF()
   {
      if (udf == null)
      {
         udf = new ArrayList<>();
      }
      return this.udf;
   }

   /**
    * Gets the value of the spread property.
    *
    * @return
    *     possible object is
    *     {@link ActivitySpreadType }
    *
    */
   public ActivitySpreadType getSpread()
   {
      return spread;
   }

   /**
    * Sets the value of the spread property.
    *
    * @param value
    *     allowed object is
    *     {@link ActivitySpreadType }
    *
    */
   public void setSpread(ActivitySpreadType value)
   {
      this.spread = value;
   }

}
