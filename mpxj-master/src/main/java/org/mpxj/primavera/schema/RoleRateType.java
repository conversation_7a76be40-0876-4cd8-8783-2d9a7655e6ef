//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for RoleRateType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="RoleRateType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="EffectiveDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MaxUnitsPerTime" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="PricePerUnit" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E12"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PricePerUnit2" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E12"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PricePerUnit3" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E12"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PricePerUnit4" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E12"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PricePerUnit5" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
 *               &lt;minInclusive value="0.0"/&gt;
 *               &lt;maxInclusive value="9.99999999999999E12"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RoleId" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="40"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RoleName" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="100"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RoleObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "RoleRateType", propOrder =
{
   "createDate",
   "createUser",
   "effectiveDate",
   "lastUpdateDate",
   "lastUpdateUser",
   "maxUnitsPerTime",
   "objectId",
   "pricePerUnit",
   "pricePerUnit2",
   "pricePerUnit3",
   "pricePerUnit4",
   "pricePerUnit5",
   "roleId",
   "roleName",
   "roleObjectId"
}) public class RoleRateType
{

   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "EffectiveDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime effectiveDate;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "MaxUnitsPerTime", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double maxUnitsPerTime;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "PricePerUnit", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double pricePerUnit;
   @XmlElement(name = "PricePerUnit2", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double pricePerUnit2;
   @XmlElement(name = "PricePerUnit3", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double pricePerUnit3;
   @XmlElement(name = "PricePerUnit4", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double pricePerUnit4;
   @XmlElement(name = "PricePerUnit5", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter3.class) protected Double pricePerUnit5;
   @XmlElement(name = "RoleId") @XmlJavaTypeAdapter(Adapter1.class) protected String roleId;
   @XmlElement(name = "RoleName") @XmlJavaTypeAdapter(Adapter1.class) protected String roleName;
   @XmlElement(name = "RoleObjectId") protected Integer roleObjectId;

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the effectiveDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEffectiveDate()
   {
      return effectiveDate;
   }

   /**
    * Sets the value of the effectiveDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEffectiveDate(LocalDateTime value)
   {
      this.effectiveDate = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the maxUnitsPerTime property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getMaxUnitsPerTime()
   {
      return maxUnitsPerTime;
   }

   /**
    * Sets the value of the maxUnitsPerTime property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMaxUnitsPerTime(Double value)
   {
      this.maxUnitsPerTime = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the pricePerUnit property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPricePerUnit()
   {
      return pricePerUnit;
   }

   /**
    * Sets the value of the pricePerUnit property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPricePerUnit(Double value)
   {
      this.pricePerUnit = value;
   }

   /**
    * Gets the value of the pricePerUnit2 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPricePerUnit2()
   {
      return pricePerUnit2;
   }

   /**
    * Sets the value of the pricePerUnit2 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPricePerUnit2(Double value)
   {
      this.pricePerUnit2 = value;
   }

   /**
    * Gets the value of the pricePerUnit3 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPricePerUnit3()
   {
      return pricePerUnit3;
   }

   /**
    * Sets the value of the pricePerUnit3 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPricePerUnit3(Double value)
   {
      this.pricePerUnit3 = value;
   }

   /**
    * Gets the value of the pricePerUnit4 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPricePerUnit4()
   {
      return pricePerUnit4;
   }

   /**
    * Sets the value of the pricePerUnit4 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPricePerUnit4(Double value)
   {
      this.pricePerUnit4 = value;
   }

   /**
    * Gets the value of the pricePerUnit5 property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getPricePerUnit5()
   {
      return pricePerUnit5;
   }

   /**
    * Sets the value of the pricePerUnit5 property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPricePerUnit5(Double value)
   {
      this.pricePerUnit5 = value;
   }

   /**
    * Gets the value of the roleId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRoleId()
   {
      return roleId;
   }

   /**
    * Sets the value of the roleId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRoleId(String value)
   {
      this.roleId = value;
   }

   /**
    * Gets the value of the roleName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRoleName()
   {
      return roleName;
   }

   /**
    * Sets the value of the roleName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRoleName(String value)
   {
      this.roleName = value;
   }

   /**
    * Gets the value of the roleObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getRoleObjectId()
   {
      return roleObjectId;
   }

   /**
    * Sets the value of the roleObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setRoleObjectId(Integer value)
   {
      this.roleObjectId = value;
   }

}
