/*
 * file:       Task.java
 * author:     <PERSON>
 *             <PERSON>
 * copyright:  (c) Packwood Software 2002-2003
 * date:       15/08/2002
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;

import org.mpxj.common.BooleanHelper;
import org.mpxj.common.LocalDateTimeHelper;
import org.mpxj.common.NumberHelper;
import org.mpxj.common.TaskFieldLists;

/**
 * This class represents a task record from a project file.
 */
public final class Task extends AbstractFieldContainer<Task> implements Comparable<Task>, ProjectEntityWithID, ChildTaskContainer
{
   /**
    * Default constructor.
    *
    * @param file Parent file to which this record belongs.
    * @param parent Parent task
    */
   Task(ProjectFile file, Task parent)
   {
      super(file);

      m_parent = parent;
      ProjectConfig config = file.getProjectConfig();

      if (config.getAutoTaskUniqueID())
      {
         setUniqueID(file.getUniqueIdObjectSequence(Task.class).getNext());
      }

      if (config.getAutoTaskID())
      {
         setID(file.getTasks().getNextID());
      }

      if (config.getAutoWBS())
      {
         generateWBS(parent);
      }

      if (config.getAutoOutlineNumber())
      {
         generateOutlineNumber(parent);
      }

      if (config.getAutoOutlineLevel())
      {
         if (parent == null)
         {
            setOutlineLevel(Integer.valueOf(1));
         }
         else
         {
            setOutlineLevel(Integer.valueOf(NumberHelper.getInt(parent.getOutlineLevel()) + 1));
         }
      }
   }

   /**
    * This method is used to automatically generate a value
    * for the WBS field of this task.
    *
    * @param parent Parent Task
    */
   public void generateWBS(Task parent)
   {
      String wbs;

      if (parent == null)
      {
         if (NumberHelper.getInt(getUniqueID()) == 0)
         {
            wbs = "0";
         }
         else
         {
            wbs = Integer.toString(getParentFile().getChildTasks().size() + 1);
         }
      }
      else
      {
         wbs = parent.getWBS();

         //
         // Apparently I added the next lines to support MPX files generated by Artemis, back in 2005
         // Unfortunately I have no test data which exercises this code, and it now breaks
         // otherwise valid WBS values read (in this case) from XER files. So it's commented out
         // until someone complains about their 2005-era Artemis MPX files not working!
         //
         //         int index = wbs.lastIndexOf(".0");
         //         if (index != -1)
         //         {
         //            wbs = wbs.substring(0, index);
         //         }

         int childTaskCount = parent.getChildTasks().size() + 1;
         if (wbs.equals("0"))
         {
            wbs = Integer.toString(childTaskCount);
         }
         else
         {
            wbs += ("." + childTaskCount);
         }
      }

      setWBS(wbs);
   }

   /**
    * This method is used to automatically generate a value
    * for the Outline Number field of this task.
    *
    * @param parent Parent Task
    */
   public void generateOutlineNumber(Task parent)
   {
      String outline;

      if (parent == null)
      {
         if (NumberHelper.getInt(getUniqueID()) == 0)
         {
            outline = "0";
         }
         else
         {
            outline = Integer.toString(getParentFile().getChildTasks().size() + 1);
         }
      }
      else
      {
         outline = parent.getOutlineNumber();

         int index = outline.lastIndexOf(".0");

         if (index != -1)
         {
            outline = outline.substring(0, index);
         }

         int childTaskCount = parent.getChildTasks().size() + 1;
         if (outline.equals("0"))
         {
            outline = Integer.toString(childTaskCount);
         }
         else
         {
            outline += ("." + childTaskCount);
         }
      }

      setOutlineNumber(outline);
   }

   /**
    * This method is used to add notes to the current task.
    *
    * @param notes notes to be added
    */
   public void setNotes(String notes)
   {
      set(TaskField.NOTES, notes == null ? null : new Notes(notes));
   }

   /**
    * Set the Notes instance representing the task notes.
    *
    * @param notes Notes instance
    */
   public void setNotesObject(Notes notes)
   {
      set(TaskField.NOTES, notes);
   }

   /**
    * This method allows nested tasks to be added, with the WBS being
    * completed automatically.
    *
    * @return new task
    */
   @Override public Task addTask()
   {
      ProjectFile parent = getParentFile();

      Task task = new Task(parent, this);

      m_children.add(task);

      parent.getTasks().add(task);

      setSummary(true);

      return (task);
   }

   /**
    * This method is used to associate a child task with the current
    * task instance. It has package access, and has been designed to
    * allow the hierarchical outline structure of tasks in an MPX
    * file to be constructed as the file is read in.
    *
    * @param child Child task.
    * @param childOutlineLevel Outline level of the child task.
    */
   public void addChildTask(Task child, int childOutlineLevel)
   {
      int outlineLevel = NumberHelper.getInt(getOutlineLevel());

      if ((outlineLevel + 1) == childOutlineLevel)
      {
         m_children.add(child);
         setSummary(true);
      }
      else
      {
         if (!m_children.isEmpty())
         {
            (m_children.get(m_children.size() - 1)).addChildTask(child, childOutlineLevel);
         }
      }
   }

   /**
    * This method is used to associate a child task with the current
    * task instance. It has been designed to
    * allow the hierarchical outline structure of tasks in an MPX
    * file to be updated once all of the task data has been read.
    *
    * @param child child task
    */
   public void addChildTask(Task child)
   {
      child.m_parent = this;
      m_children.add(child);
      setSummary(true);

      if (getParentFile().getProjectConfig().getAutoOutlineLevel())
      {
         child.setOutlineLevel(Integer.valueOf(NumberHelper.getInt(getOutlineLevel()) + 1));
      }
   }

   /**
    * Inserts a child task prior to a given sibling task.
    *
    * @param child new child task
    * @param previousSibling sibling task
    */
   public void addChildTaskBefore(Task child, Task previousSibling)
   {
      int index = m_children.indexOf(previousSibling);
      if (index == -1)
      {
         m_children.add(child);
      }
      else
      {
         m_children.add(index, child);
      }

      child.m_parent = this;
      setSummary(true);

      if (getParentFile().getProjectConfig().getAutoOutlineLevel())
      {
         child.setOutlineLevel(Integer.valueOf(NumberHelper.getInt(getOutlineLevel()) + 1));
      }
   }

   /**
    * Removes a child task.
    *
    * @param child child task instance
    */
   public void removeChildTask(Task child)
   {
      if (m_children.remove(child))
      {
         child.m_parent = null;
      }
      setSummary(!m_children.isEmpty());
   }

   /**
    * This method allows the list of child tasks to be cleared in preparation
    * for the hierarchical task structure to be built.
    */
   public void clearChildTasks()
   {
      // Only take action if the child list is populated. This ensures that
      // the summary flag value is preserved. This is important when identifying
      // WBS entries which have no child activities.
      if (!m_children.isEmpty())
      {
         m_children.clear();
         setSummary(false);
      }
   }

   /**
    * This method allows recurring task details to be added to the
    * current task.
    *
    * @return RecurringTask object
    */
   public RecurringTask addRecurringTask()
   {
      if (m_recurringTask == null)
      {
         m_recurringTask = new RecurringTask();
      }

      return (m_recurringTask);
   }

   /**
    * This method retrieves the recurring task record. If the current
    * task is not a recurring task, then this method will return null.
    *
    * @return Recurring task record.
    */
   public RecurringTask getRecurringTask()
   {
      return (m_recurringTask);
   }

   /**
    * Retrieve the activity code values associated with this task.
    *
    * @return map of activity code values
    */
   @SuppressWarnings("unchecked") public Map<ActivityCode, ActivityCodeValue> getActivityCodeValues()
   {
      return (Map<ActivityCode, ActivityCodeValue>) get(TaskField.ACTIVITY_CODE_VALUES);
   }

   /**
    * Assign an activity code value to this task.
    *
    * @param value activity code value
    */
   @SuppressWarnings("unchecked") public void addActivityCodeValue(ActivityCodeValue value)
   {
      ((Map<ActivityCode, ActivityCodeValue>) get(TaskField.ACTIVITY_CODE_VALUES)).put(value.getParentCode(), value);
   }

   /**
    * This method allows a resource assignment to be added to the
    * current task.
    *
    * @param resource the resource to assign
    * @return ResourceAssignment object
    */
   public ResourceAssignment addResourceAssignment(Resource resource)
   {
      ResourceAssignment assignment = new ResourceAssignment(getParentFile(), this);
      assignment.setTaskUniqueID(getUniqueID());
      assignment.setResourceUniqueID(resource == null ? null : resource.getUniqueID());
      assignment.setWork(getDuration());
      assignment.setUnits(ResourceAssignment.DEFAULT_UNITS);
      addResourceAssignment(assignment);

      return assignment;
   }

   /**
    * Add a resource assignment which has been populated elsewhere.
    *
    * @param assignment resource assignment
    */
   public void addResourceAssignment(ResourceAssignment assignment)
   {
      m_assignments.add(assignment);
      getParentFile().getResourceAssignments().add(assignment);

      Resource resource = assignment.getResource();
      if (resource != null)
      {
         resource.addResourceAssignment(assignment);
      }
   }

   /**
    * Retrieves an existing resource assignment if one is present,
    * to prevent duplicate resource assignments being added.
    *
    * @param resource resource to test for
    * @return existing resource assignment
    */
   public ResourceAssignment getExistingResourceAssignment(Resource resource)
   {
      Predicate<ResourceAssignment> filter = (a) -> (resource == null && a.getResource() == null) || (resource != null && NumberHelper.equals(resource.getUniqueID(), a.getResourceUniqueID()));
      return m_assignments.stream().filter(filter).findFirst().orElse(null);
   }

   /**
    * This method allows the list of resource assignments for this
    * task to be retrieved.
    *
    * @return list of resource assignments
    */
   public List<ResourceAssignment> getResourceAssignments()
   {
      return (m_assignments);
   }

   /**
    * Internal method used as part of the process of removing a
    * resource assignment.
    *
    * @param assignment resource assignment to be removed
    */
   void removeResourceAssignment(ResourceAssignment assignment)
   {
      m_assignments.remove(assignment);
   }

   /**
    * This method allows a predecessor relationship to be added to this
    * task instance.
    *
    * @param builder Relation.Builder instance
    * @return Relation instance
    */
   public Relation addPredecessor(Relation.Builder builder)
   {
      return getParentFile().getRelations().addPredecessor(builder.successorTask(this));
   }

   /**
    * This method allows a predecessor relationship to be removed from this
    * task instance.  It will only delete relationships that exactly match the
    * given targetTask, type and lag time.
    *
    * @param targetTask the predecessor task
    * @param type relation type
    * @param lag relation lag
    * @return returns true if the relation is found and removed
    */
   public boolean removePredecessor(Task targetTask, RelationType type, Duration lag)
   {
      return getParentFile().getRelations().removePredecessor(this, targetTask, type, lag);
   }

   /**
    * The % Complete field contains the current status of a task, expressed
    * as the percentage of the
    * task's duration that has been completed. You can enter percent complete,
    * or you can have
    * Microsoft Project calculate it for you based on actual duration.
    *
    * @param val value to be set
    */
   public void setPercentageComplete(Number val)
   {
      set(TaskField.PERCENT_COMPLETE, val);
   }

   /**
    * The % Work Complete field contains the current status of a task,
    * expressed as the
    * percentage of the task's work that has been completed. You can enter
    * percent work
    * complete, or you can have Microsoft Project calculate it for you
    * based on actual
    * work on the task.
    *
    * @param val value to be set
    */
   public void setPercentageWorkComplete(Number val)
   {
      set(TaskField.PERCENT_WORK_COMPLETE, val);
   }

   /**
    * The Actual Cost field shows costs incurred for work already performed
    * by all resources
    * on a task, along with any other recorded costs associated with the task.
    * You can enter
    * all the actual costs or have Microsoft Project calculate them for you.
    *
    * @param val value to be set
    */
   public void setActualCost(Number val)
   {
      set(TaskField.ACTUAL_COST, val);
   }

   /**
    * The Actual Duration field shows the span of actual working time for a
    * task so far,
    * based on the scheduled duration and current remaining work or
    * completion percentage.
    *
    * @param val value to be set
    */
   public void setActualDuration(Duration val)
   {
      set(TaskField.ACTUAL_DURATION, val);
   }

   /**
    * The Actual Finish field shows the date and time that a task actually
    * finished.
    * Microsoft Project sets the Actual Finish field to the scheduled finish
    * date if
    * the completion percentage is 100. This field contains "NA" until you
    * enter actual
    * information or set the completion percentage to 100.
    *
    * @param val value to be set
    */
   public void setActualFinish(LocalDateTime val)
   {
      set(TaskField.ACTUAL_FINISH, val);
   }

   /**
    * The Actual Start field shows the date and time that a task actually began.
    * When a task is first created, the Actual Start field contains "NA." Once you
    * enter the first actual work or a completion percentage for a task, Microsoft
    * Project sets the actual start date to the scheduled start date.
    * @param val value to be set
    */
   public void setActualStart(LocalDateTime val)
   {
      set(TaskField.ACTUAL_START, val);
   }

   /**
    * The Actual Work field shows the amount of work that has already been
    * done by the
    * resources assigned to a task.
    * @param val value to be set
    */
   public void setActualWork(Duration val)
   {
      set(TaskField.ACTUAL_WORK, val);
   }

   /**
    * The Baseline Cost field shows the total planned cost for a task.
    * Baseline cost is also referred to as budget at completion (BAC).
    *
    * @param val the amount to be set
    */
   public void setBaselineCost(Number val)
   {
      set(TaskField.BASELINE_COST, val);
   }

   /**
    * The Baseline Duration field shows the original span of time planned to
    * complete a task.
    *
    * @param val duration
    */
   public void setBaselineDuration(Duration val)
   {
      set(TaskField.BASELINE_DURATION, val);
   }

   /**
    * The Baseline Finish field shows the planned completion date for a
    * task at the time
    * you saved a baseline. Information in this field becomes available
    * when you set a
    * baseline for a task.
    *
    * @param val Date to be set
    */
   public void setBaselineFinish(LocalDateTime val)
   {
      set(TaskField.BASELINE_FINISH, val);
   }

   /**
    * The Baseline Start field shows the planned beginning date for a task at
    * the time
    * you saved a baseline. Information in this field becomes available when you
    * set a baseline.
    *
    * @param val Date to be set
    */
   public void setBaselineStart(LocalDateTime val)
   {
      set(TaskField.BASELINE_START, val);
   }

   /**
    * The Baseline Work field shows the originally planned amount of work to
    * be performed
    * by all resources assigned to a task. This field shows the planned
    * person-hours
    * scheduled for a task. Information in the Baseline Work field
    * becomes available
    * when you set a baseline for the project.
    *
    * @param val the duration to be set.
    */
   public void setBaselineWork(Duration val)
   {
      set(TaskField.BASELINE_WORK, val);
   }

   /**
    * The BCWP (budgeted cost of work performed) field contains the
    * cumulative value
    * of the assignment's timephased percent complete multiplied by
    * the assignments
    * timephased baseline cost. BCWP is calculated up to the status
    * date or today's
    * date. This information is also known as earned value.
    *
    * @param val the amount to be set
    */
   public void setBCWP(Number val)
   {
      set(TaskField.BCWP, val);
   }

   /**
    * The BCWS (budgeted cost of work scheduled) field contains the cumulative
    * timephased baseline costs up to the status date or today's date.
    *
    * @param val the amount to set
    */
   public void setBCWS(Number val)
   {
      set(TaskField.BCWS, val);
   }

   /**
    * The Confirmed field indicates whether all resources assigned to a task have
    * accepted or rejected the task assignment in response to a TeamAssign message
    * regarding their assignments.
    *
    * @param val boolean value
    */
   public void setConfirmed(boolean val)
   {
      set(TaskField.CONFIRMED, val);
   }

   /**
    * The Constraint Date field shows the specific date associated with certain
    * constraint types,
    *  such as Must Start On, Must Finish On, Start No Earlier Than,
    *  Start No Later Than,
    *  Finish No Earlier Than, and Finish No Later Than.
    *  SEE class constants
    *
    * @param val Date to be set
    */
   public void setConstraintDate(LocalDateTime val)
   {
      set(TaskField.CONSTRAINT_DATE, val);
   }

   /**
    * Set the secondary constraint date.
    *
    * @param date secondary constraint date
    */
   public void setSecondaryConstraintDate(LocalDateTime date)
   {
      set(TaskField.SECONDARY_CONSTRAINT_DATE, date);
   }

   /**
    * Private method for dealing with string parameters from File.
    *
    * @param type string constraint type
    */
   public void setConstraintType(ConstraintType type)
   {
      set(TaskField.CONSTRAINT_TYPE, type);
   }

   /**
    * Set the secondary constraint type.
    *
    * @param type secondary constraint type
    */
   public void setSecondaryConstraintType(ConstraintType type)
   {
      set(TaskField.SECONDARY_CONSTRAINT_TYPE, type);
   }

   /**
    * The Contact field contains the name of an individual
    * responsible for a task.
    *
    * @param val value to be set
    */
   public void setContact(String val)
   {
      set(TaskField.CONTACT, val);
   }

   /**
    * The Cost field shows the total scheduled, or projected, cost for a task,
    * based on costs already incurred for work performed by all resources assigned
    * to the task, in addition to the costs planned for the remaining work for the
    * assignment. This can also be referred to as estimate at completion (EAC).
    *
    * @param val amount
    */
   public void setCost(Number val)
   {
      set(TaskField.COST, val);
   }

   /**
    * Set a cost value.
    *
    * @param index cost index (1-10)
    * @param value cost value
    */
   public void setCost(int index, Number value)
   {
      set(selectField(TaskFieldLists.CUSTOM_COST, index), value);
   }

   /**
    * Retrieve a cost value.
    *
    * @param index cost index (1-10)
    * @return cost value
    */
   public Number getCost(int index)
   {
      return (Number) get(selectField(TaskFieldLists.CUSTOM_COST, index));
   }

   /**
    * The Cost Variance field shows the difference between the
    * baseline cost and total cost for a task. The total cost is the
    * current estimate of costs based on actual costs and remaining costs.
    *
    * @param val amount
    */
   public void setCostVariance(Number val)
   {
      set(TaskField.COST_VARIANCE, val);
   }

   /**
    * The Created field contains the date and time when a task was
    * added to the project.
    *
    * @param val date
    */
   public void setCreateDate(LocalDateTime val)
   {
      set(TaskField.CREATED, val);
   }

   /**
    * The Critical field indicates whether a task has any room in the
    * schedule to slip,
    * or if a task is on the critical path. The Critical field contains
    * Yes if the task
    * is critical and No if the task is not critical.
    *
    * @param val whether task is critical or not
    */
   public void setCritical(boolean val)
   {
      set(TaskField.CRITICAL, val);
   }

   /**
    * The CV (earned value cost variance) field shows the difference
    * between how much it should have cost to achieve the current level of
    * completion on the task, and how much it has actually cost to achieve the
    * current level of completion up to the status date or today's date.
    *
    * @param val value to set
    */
   public void setCV(Number val)
   {
      set(TaskField.CV, val);
   }

   /**
    * Set amount of delay as elapsed real time.
    *
    * @param val elapsed time
    */
   public void setLevelingDelay(Duration val)
   {
      set(TaskField.LEVELING_DELAY, val);
   }

   /**
    * The Duration field is the total span of active working time for a task.
    * This is generally the amount of time from the start to the finish of a task.
    * The default for new tasks is 1 day (1d).
    *
    * @param val duration
    */
   public void setDuration(Duration val)
   {
      set(TaskField.DURATION, val);
   }

   /**
    * Set the duration text used for a manually scheduled task.
    *
    * @param val text
    */
   public void setDurationText(String val)
   {
      set(TaskField.DURATION_TEXT, val);
   }

   /**
    * Set the manual duration attribute.
    *
    * @param dur manual duration
    */
   public void setManualDuration(Duration dur)
   {
      set(TaskField.MANUAL_DURATION, dur);
   }

   /**
    * Read the manual duration attribute.
    *
    * @return manual duration
    */
   public Duration getManualDuration()
   {
      return (Duration) get(TaskField.MANUAL_DURATION);
   }

   /**
    * The Duration Variance field contains the difference between the
    * baseline duration of a task and the forecast or actual duration
    * of the task.
    *
    * @param duration duration value
    */
   public void setDurationVariance(Duration duration)
   {
      set(TaskField.DURATION_VARIANCE, duration);
   }

   /**
    * The Early Finish field contains the earliest date that a task
    * could possibly finish, based on early finish dates of predecessor
    * and successor tasks, other constraints, and any leveling delay.
    *
    * @param date Date value
    */
   public void setEarlyFinish(LocalDateTime date)
   {
      set(TaskField.EARLY_FINISH, date);
   }

   /**
   * The date the resource is scheduled to finish the remaining work for the activity.
   *
   * @param date Date value
   */
   public void setRemainingEarlyFinish(LocalDateTime date)
   {
      set(TaskField.REMAINING_EARLY_FINISH, date);
   }

   /**
    * The Early Start field contains the earliest date that a task could
    * possibly begin, based on the early start dates of predecessor and
    * successor tasks, and other constraints.
    *
    * @param date Date value
    */
   public void setEarlyStart(LocalDateTime date)
   {
      set(TaskField.EARLY_START, date);
   }

   /**
   * The date the resource is scheduled to begin the remaining work for the activity.
   *
   * @param date Date value
   */
   public void setRemainingEarlyStart(LocalDateTime date)
   {
      set(TaskField.REMAINING_EARLY_START, date);
   }

   /**
    * The Finish field shows the date and time that a task is scheduled to be
    * completed. MS project allows a finish date to be entered, and will
    * calculate the duration, or a duration can be supplied and MS Project
    * will calculate the finish date.
    *
    * @param date Date value
    */
   public void setFinish(LocalDateTime date)
   {
      set(TaskField.FINISH, date);
   }

   /**
    * Set the finish text used for a manually scheduled task.
    *
    * @param val text
    */
   public void setFinishText(String val)
   {
      set(TaskField.FINISH_TEXT, val);
   }

   /**
    * The Finish Variance field contains the amount of time that represents the
    * difference between a task's baseline finish date and its forecast
    * or actual finish date.
    *
    * @param duration duration value
    */
   public void setFinishVariance(Duration duration)
   {
      set(TaskField.FINISH_VARIANCE, duration);
   }

   /**
    * The Fixed Cost field shows any task expense that is not associated
    * with a resource cost.
    *
    * @param val amount
    */
   public void setFixedCost(Number val)
   {
      set(TaskField.FIXED_COST, val);
   }

   /**
    * The Free Slack field contains the amount of time that a task can be
    * delayed without delaying any successor tasks. If the task has no
    * successors, free slack is the amount of time that a task can be delayed
    * without delaying the entire project's finish date.
    *
    * @param duration duration value
    */
   public void setFreeSlack(Duration duration)
   {
      set(TaskField.FREE_SLACK, duration);
   }

   /**
    * The Hide Bar flag indicates whether the Gantt bars and Calendar bars
    * for a task are hidden when this project's data is displayed in MS Project.
    *
    * @param flag boolean value
    */
   public void setHideBar(boolean flag)
   {
      set(TaskField.HIDE_BAR, flag);
   }

   /**
    * The ID field contains the identifier number that Microsoft Project
    * automatically assigns to each task as you add it to the project.
    * The ID indicates the position of a task with respect to the other tasks.
    *
    * @param val ID
    */
   @Override public void setID(Integer val)
   {
      ProjectFile parent = getParentFile();
      Integer previous = getID();

      if (previous != null)
      {
         parent.getTasks().unmapID(previous);
      }

      parent.getTasks().mapID(val, this);

      set(TaskField.ID, val);
   }

   /**
    * The Late Finish field contains the latest date that a task can finish
    * without delaying the finish of the project. This date is based on the
    * task's late start date, as well as the late start and late finish dates
    * of predecessor and successor tasks, and other constraints.
    *
    * @param date date value
    */
   public void setLateFinish(LocalDateTime date)
   {
      set(TaskField.LATE_FINISH, date);
   }

   /**
    * Set the remaining late finish value.
    *
    * @param date remaining late finish
    */
   public void setRemainingLateFinish(LocalDateTime date)
   {
      set(TaskField.REMAINING_LATE_FINISH, date);
   }

   /**
    * The Late Start field contains the latest date that a task can start
    * without delaying the finish of the project. This date is based on the
    * task's start date, as well as the late start and late finish dates of
    * predecessor and successor tasks, and other constraints.
    *
    * @param date date value
    */
   public void setLateStart(LocalDateTime date)
   {
      set(TaskField.LATE_START, date);
   }

   /**
    * Set the remaining late start value.
    *
    * @param date remaining late start
    */
   public void setRemainingLateStart(LocalDateTime date)
   {
      set(TaskField.REMAINING_LATE_START, date);
   }

   /**
    * The Linked Fields field indicates whether there are OLE links to the task,
    * either from elsewhere in the active project, another Microsoft Project
    * file, or from another program.
    *
    * @param flag boolean value
    */
   public void setLinkedFields(boolean flag)
   {
      set(TaskField.LINKED_FIELDS, flag);
   }

   /**
    * This is a user defined field used to mark a task for some form of
    * additional action.
    *
    * @param flag boolean value
    */
   public void setMarked(boolean flag)
   {
      set(TaskField.MARKED, flag);
   }

   /**
    * The Milestone field indicates whether a task is a milestone.
    *
    * @param flag boolean value
    */
   public void setMilestone(boolean flag)
   {
      set(TaskField.MILESTONE, flag);
   }

   /**
    * The Name field contains the name of a task.
    *
    * @param name task name
    */
   public void setName(String name)
   {
      set(TaskField.NAME, name);
   }

   /**
    * The Objects field contains the number of objects attached to a task.
    *
    * @param val - integer value
    */
   public void setObjects(Integer val)
   {
      set(TaskField.OBJECTS, val);
   }

   /**
    * The Outline Level field contains the number that indicates the level of
    * the task in the project outline hierarchy.
    *
    * @param val - int
    */
   public void setOutlineLevel(Integer val)
   {
      set(TaskField.OUTLINE_LEVEL, val);
   }

   /**
    * The Outline Number field contains the number of the task in the structure
    * of an outline. This number indicates the task's position within the
    * hierarchical structure of the project outline. The outline number is
    * similar to a WBS (work breakdown structure) number, except that the
    * outline number is automatically entered by Microsoft Project.
    *
    * @param val - text
    */
   public void setOutlineNumber(String val)
   {
      set(TaskField.OUTLINE_NUMBER, val);
   }

   /**
    * The Priority field provides choices for the level of importance
    * assigned to a task, which in turn indicates how readily a task can be
    * delayed or split during resource leveling.
    * The default priority is Medium. Those tasks with a priority
    * of Do Not Level are never delayed or split when Microsoft Project levels
    * tasks that have overallocated resources assigned.
    *
    * @param priority the priority value
    */
   public void setPriority(Priority priority)
   {
      set(TaskField.PRIORITY, priority);
   }

   /**
    * The Project field shows the name of the project from which a
    * task originated.
    * This can be the name of the active project file. If there are
    * other projects
    * inserted into the active project file, the name of the
    * inserted project appears
    * in this field for the task.
    *
    * @param val - text
    */
   public void setProject(String val)
   {
      set(TaskField.PROJECT, val);
   }

   /**
    * The Remaining Cost field shows the remaining scheduled expense of a task that
    * will be incurred in completing the remaining scheduled work by all resources
    * assigned to the task.
    *
    * @param val - currency amount
    */
   public void setRemainingCost(Number val)
   {
      set(TaskField.REMAINING_COST, val);
   }

   /**
    * The Remaining Duration field shows the amount of time required to complete
    * the unfinished portion of a task.
    *
    * @param val - duration.
    */
   public void setRemainingDuration(Duration val)
   {
      set(TaskField.REMAINING_DURATION, val);
   }

   /**
    * The Remaining Work field shows the amount of time, or person-hours,
    * still required by all assigned resources to complete a task.
    * @param val  - duration
    */
   public void setRemainingWork(Duration val)
   {
      set(TaskField.REMAINING_WORK, val);
   }

   /**
    * The Resource Group field contains the list of resource groups to which the
    * resources assigned to a task belong.
    *
    * @param val - String list
    */
   public void setResourceGroup(String val)
   {
      set(TaskField.RESOURCE_GROUP, val);
   }

   /**
    * The Resource Initials field lists the abbreviations for the names of
    * resources assigned to a task. These initials can serve as substitutes
    * for the names.
    *
    * Note that MS Project 98 does not normally populate this field when
    * it generates an MPX file, and will therefore not expect to see values
    * in this field when it reads an MPX file. Supplying values for this
    * field will cause MS Project 98, 2000, and 2002 to create new resources
    * and ignore any other resource assignments that have been defined
    * in the MPX file.
    *
    * @param val String containing a comma separated list of initials
    */
   public void setResourceInitials(String val)
   {
      set(TaskField.RESOURCE_INITIALS, val);
   }

   /**
    * The Resource Names field lists the names of all resources
    * assigned to a task.
    *
    * Note that MS Project 98 does not normally populate this field when
    * it generates an MPX file, and will therefore not expect to see values
    * in this field when it reads an MPX file. Supplying values for this
    * field when writing an MPX file will cause MS Project 98, 2000, and 2002
    * to create new resources and ignore any other resource assignments
    * that have been defined in the MPX file.
    *
    * @param val String containing a comma separated list of names
    */
   public void setResourceNames(String val)
   {
      set(TaskField.RESOURCE_NAMES, val);
   }

   /**
    * The Resume field shows the date that the remaining portion of a task is
    * scheduled to resume after you enter a new value for the % Complete field.
    * The Resume field is also recalculated when the remaining portion of a task
    * is moved to a new date.
    *
    * @param val - Date
    */
   public void setResume(LocalDateTime val)
   {
      set(TaskField.RESUME, val);
   }

   /**
    * For subtasks, the Rollup field indicates whether information on the subtask
    * Gantt bars will be rolled up to the summary task bar. For summary tasks, the
    * Rollup field indicates whether the summary task bar displays rolled up bars.
    * You must have the Rollup field for summary tasks set to Yes for any subtasks
    * to roll up to them.
    *
    * @param val - boolean
    */
   public void setRollup(boolean val)
   {
      set(TaskField.ROLLUP, val);
   }

   /**
    * The Start field shows the date and time that a task is scheduled to begin.
    * You can enter the start date you want, to indicate the date when the task
    * should begin. Or, you can have Microsoft Project calculate the start date.
    * @param val - Date
    */
   public void setStart(LocalDateTime val)
   {
      set(TaskField.START, val);
   }

   /**
    * Set the start text used for a manually scheduled task.
    *
    * @param val text
    */
   public void setStartText(String val)
   {
      set(TaskField.START_TEXT, val);
   }

   /**
    * The Start Variance field contains the amount of time that represents the
    * difference between a task's baseline start date and its currently
    * scheduled start date.
    *
    * @param val - duration
    */
   public void setStartVariance(Duration val)
   {
      set(TaskField.START_VARIANCE, val);
   }

   /**
    * The Stop field shows the date that represents the end of the actual
    * portion of a task. Typically, Microsoft Project calculates the stop date.
    * However, you can edit this date as well.
    *
    * @param val - Date
    */
   public void setStop(LocalDateTime val)
   {
      set(TaskField.STOP, val);
   }

   /**
    * The Subproject File field contains the external
    * project's path and file name.
    *
    * @param val - String
    */
   public void setSubprojectFile(String val)
   {
      set(TaskField.SUBPROJECT_FILE, val);
   }

   /**
    * Set the GUID of the linked subproject file.
    *
    * @param guid subproject GUID
    */
   public void setSubprojectGUID(UUID guid)
   {
      set(TaskField.SUBPROJECT_GUID, guid);
   }

   /**
    * Retrieve the GUID of the linked subproject file.
    *
    * @return subproject GUID
    */
   public UUID getSubprojectGUID()
   {
      return (UUID) get(TaskField.SUBPROJECT_GUID);
   }

   /**
    * The Summary field indicates whether a task is a summary task.
    *
    * @param val - boolean
    */
   public void setSummary(boolean val)
   {
      set(TaskField.SUMMARY, val);
   }

   /**
    * The SV (earned value schedule variance) field shows the difference
    * in cost terms between the current progress and the baseline plan
    * of the task up to the status date or today's date. You can use SV
    * to check costs to determine whether tasks are on schedule.
    * @param val - currency amount
    */
   public void setSV(Number val)
   {
      set(TaskField.SV, val);
   }

   /**
    * The Total Slack field contains the amount of time a task can be delayed
    * without delaying the project's finish date.
    *
    * @param val - duration
    */
   public void setTotalSlack(Duration val)
   {
      set(TaskField.TOTAL_SLACK, val);
   }

   /**
    * The Unique ID field contains the number that Microsoft Project
    * automatically designates whenever a new task is created.
    * This number indicates the sequence in which the task was created,
    * regardless of placement in the schedule.
    *
    * @param val unique ID
    */
   @Override public void setUniqueID(Integer val)
   {
      set(TaskField.UNIQUE_ID, val);
   }

   /**
    * The Update Needed field indicates whether a TeamUpdate message should
    * be sent to the assigned resources because of changes to the start date,
    * finish date, or resource reassignments of the task.
    *
    * @param val - boolean
    */
   public void setUpdateNeeded(boolean val)
   {
      set(TaskField.UPDATE_NEEDED, val);
   }

   /**
    * The work breakdown structure code. The WBS field contains an alphanumeric
    * code you can use to represent the task's position within the hierarchical
    * structure of the project. This field is similar to the outline number,
    * except that you can edit it.
    *
    * @param val - String
    */
   public void setWBS(String val)
   {
      set(TaskField.WBS, val);
   }

   /**
    * The Work field shows the total amount of work scheduled to be performed
    * on a task by all assigned resources. This field shows the total work,
    * or person-hours, for a task.
    *
    * @param val - duration
    */
   public void setWork(Duration val)
   {
      set(TaskField.WORK, val);
   }

   /**
    * The Work Variance field contains the difference between a task's baseline
    * work and the currently scheduled work.
    *
    * @param val - duration
    */
   public void setWorkVariance(Duration val)
   {
      set(TaskField.WORK_VARIANCE, val);
   }

   /**
    * The % Complete field contains the current status of a task,
    * expressed as the percentage of the task's duration that has been completed.
    * You can enter percent complete, or you can have Microsoft Project calculate
    * it for you based on actual duration.
    * @return percentage as float
    */
   public Number getPercentageComplete()
   {
      return (Number) get(TaskField.PERCENT_COMPLETE);
   }

   /**
    * The % Work Complete field contains the current status of a task,
    * expressed as the percentage of the task's work that has been completed.
    * You can enter percent work complete, or you can have Microsoft Project
    * calculate it for you based on actual work on the task.
    *
    * @return percentage as float
    */
   public Number getPercentageWorkComplete()
   {
      return (Number) get(TaskField.PERCENT_WORK_COMPLETE);
   }

   /**
    * The Actual Cost field shows costs incurred for work already performed
    * by all resources on a task, along with any other recorded costs associated
    * with the task. You can enter all the actual costs or have Microsoft Project
    * calculate them for you.
    *
    * @return currency amount as float
    */
   public Number getActualCost()
   {
      return (Number) get(TaskField.ACTUAL_COST);
   }

   /**
    * The Actual Duration field shows the span of actual working time for a
    * task so far, based on the scheduled duration and current remaining work
    * or completion percentage.
    *
    * @return duration string
    */
   public Duration getActualDuration()
   {
      return (Duration) get(TaskField.ACTUAL_DURATION);
   }

   /**
    * The Actual Finish field shows the date and time that a task actually
    * finished. Microsoft Project sets the Actual Finish field to the scheduled
    * finish date if the completion percentage is 100. This field contains "NA"
    * until you enter actual information or set the completion percentage to 100.
    * If "NA" is entered as value, arbitrary year zero Date is used. Date(0);
    *
    * @return Date
    */
   public LocalDateTime getActualFinish()
   {
      return (LocalDateTime) get(TaskField.ACTUAL_FINISH);
   }

   /**
    * The Actual Start field shows the date and time that a task actually began.
    * When a task is first created, the Actual Start field contains "NA." Once
    * you enter the first actual work or a completion percentage for a task,
    * Microsoft Project sets the actual start date to the scheduled start date.
    * If "NA" is entered as value, arbitrary year zero Date is used. Date(0);
    *
    * @return Date
    */
   public LocalDateTime getActualStart()
   {
      return (LocalDateTime) get(TaskField.ACTUAL_START);
   }

   /**
    * The Actual Work field shows the amount of work that has already been done
    * by the resources assigned to a task.
    *
    * @return duration string
    */
   public Duration getActualWork()
   {
      return (Duration) get(TaskField.ACTUAL_WORK);
   }

   /**
    * The Baseline Cost field shows the total planned cost for a task.
    * Baseline cost is also referred to as budget at completion (BAC).
    * @return currency amount as float
    */
   public Number getBaselineCost()
   {
      return (Number) get(TaskField.BASELINE_COST);
   }

   /**
    * The Baseline Duration field shows the original span of time planned
    * to complete a task.
    *
    * @return  - duration string
    */
   public Duration getBaselineDuration()
   {
      Object result = get(TaskField.BASELINE_DURATION);
      if (!(result instanceof Duration))
      {
         result = null;
      }
      return (Duration) result;
   }

   /**
    * Retrieves the text value for the baseline duration.
    *
    * @return baseline duration text
    */
   public String getBaselineDurationText()
   {
      Object result = get(TaskField.BASELINE_DURATION);
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline duration text value.
    *
    * @param value baseline duration text
    */
   public void setBaselineDurationText(String value)
   {
      set(TaskField.BASELINE_DURATION, value);
   }

   /**
    * The Baseline Finish field shows the planned completion date for a task
    * at the time you saved a baseline. Information in this field becomes
    * available when you set a baseline for a task.
    *
    * @return Date
    */
   public LocalDateTime getBaselineFinish()
   {
      Object result = get(TaskField.BASELINE_FINISH);
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Retrieves the baseline finish text value.
    *
    * @return baseline finish text
    */
   public String getBaselineFinishText()
   {
      Object result = get(TaskField.BASELINE_FINISH);
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline finish text value.
    *
    * @param value baseline finish text
    */
   public void setBaselineFinishText(String value)
   {
      set(TaskField.BASELINE_FINISH, value);
   }

   /**
    * The Baseline Start field shows the planned beginning date for a task at
    * the time you saved a baseline. Information in this field becomes available
    * when you set a baseline.
    *
    * @return Date
    */
   public LocalDateTime getBaselineStart()
   {
      Object result = get(TaskField.BASELINE_START);
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Retrieves the baseline start text value.
    *
    * @return baseline start value
    */
   public String getBaselineStartText()
   {
      Object result = get(TaskField.BASELINE_START);
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline start text value.
    *
    * @param value baseline start text
    */
   public void setBaselineStartText(String value)
   {
      set(TaskField.BASELINE_START, value);
   }

   /**
    * The Baseline Work field shows the originally planned amount of work to be
    * performed by all resources assigned to a task. This field shows the planned
    * person-hours scheduled for a task. Information in the Baseline Work field
    * becomes available when you set a baseline for the project.
    *
    * @return Duration
    */
   public Duration getBaselineWork()
   {
      return (Duration) get(TaskField.BASELINE_WORK);
   }

   /**
    * The BCWP (budgeted cost of work performed) field contains
    * the cumulative value of the assignment's timephased percent complete
    * multiplied by the assignment's timephased baseline cost.
    * BCWP is calculated up to the status date or today's date.
    * This information is also known as earned value.
    *
    * @return currency amount as float
    */
   public Number getBCWP()
   {
      return (Number) get(TaskField.BCWP);
   }

   /**
    * The BCWS (budgeted cost of work scheduled) field contains the cumulative
    * timephased baseline costs up to the status date or today's date.
    *
    * @return currency amount as float
    */
   public Number getBCWS()
   {
      return (Number) get(TaskField.BCWS);
   }

   /**
    * The Confirmed field indicates whether all resources assigned to a task
    * have accepted or rejected the task assignment in response to a TeamAssign
    * message regarding their assignments.
    *
    * @return boolean
    */
   public boolean getConfirmed()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.CONFIRMED)));
   }

   /**
    * The Constraint Date field shows the specific date associated with certain
    * constraint types, such as Must Start On, Must Finish On,
    * Start No Earlier Than,
    * Start No Later Than, Finish No Earlier Than, and Finish No Later Than.
    *
    * @return Date
    */
   public LocalDateTime getConstraintDate()
   {
      return (LocalDateTime) get(TaskField.CONSTRAINT_DATE);
   }

   /**
    * Retrieve the secondary constraint date.
    *
    * @return secondary constraint date
    */
   public LocalDateTime getSecondaryConstraintDate()
   {
      return (LocalDateTime) get(TaskField.SECONDARY_CONSTRAINT_DATE);
   }

   /**
    * The Constraint Type field provides choices for the type of constraint you
    * can apply for scheduling a task.
    *
    * @return constraint type
    */
   public ConstraintType getConstraintType()
   {
      return (ConstraintType) get(TaskField.CONSTRAINT_TYPE);
   }

   /**
    * Retrieve the secondary constraint type.
    *
    * @return secondary constraint type
    */
   public ConstraintType getSecondaryConstraintType()
   {
      return (ConstraintType) get(TaskField.SECONDARY_CONSTRAINT_TYPE);
   }

   /**
    * The Contact field contains the name of an individual
    * responsible for a task.
    *
    * @return String
    */
   public String getContact()
   {
      return (String) get(TaskField.CONTACT);
   }

   /**
    * The Cost field shows the total scheduled, or projected, cost for a task,
    * based on costs already incurred for work performed by all resources assigned
    * to the task, in addition to the costs planned for the remaining work for the
    * assignment. This can also be referred to as estimate at completion (EAC).
    *
    * @return cost amount
    */
   public Number getCost()
   {
      return (Number) get(TaskField.COST);
   }

   /**
    * The Cost Variance field shows the difference between the baseline cost
    * and total cost for a task. The total cost is the current estimate of costs
    * based on actual costs and remaining costs.
    *
    * @return amount
    */
   public Number getCostVariance()
   {
      return (Number) get(TaskField.COST_VARIANCE);
   }

   /**
    * The Created field contains the date and time when a task was added
    * to the project.
    *
    * @return Date
    */
   public LocalDateTime getCreateDate()
   {
      return (LocalDateTime) get(TaskField.CREATED);
   }

   /**
    * The Critical field indicates whether a task has any room in the schedule
    * to slip, or if a task is on the critical path. The Critical field contains
    * Yes if the task is critical and No if the task is not critical.
    *
    * @return boolean
    */
   public boolean getCritical()
   {
      return BooleanHelper.getBoolean((Boolean) get(TaskField.CRITICAL));
   }

   /**
    * The CV (earned value cost variance) field shows the difference between
    * how much it should have cost to achieve the current level of completion
    * on the task, and how much it has actually cost to achieve the current
    * level of completion up to the status date or today's date.
    * How Calculated   CV is the difference between BCWP
    * (budgeted cost of work performed) and ACWP
    * (actual cost of work performed). Microsoft Project calculates
    * the CV as follows: CV = BCWP - ACWP
    *
    * @return sum of earned value cost variance
    */
   public Number getCV()
   {
      return (Number) get(TaskField.CV);
   }

   /**
    * Delay , in MPX files as eg '0ed'. Use duration
    *
    * @return Duration
    */
   public Duration getLevelingDelay()
   {
      return (Duration) get(TaskField.LEVELING_DELAY);
   }

   /**
    * The Duration field is the total span of active working time for a task.
    * This is generally the amount of time from the start to the finish of a task.
    * The default for new tasks is 1 day (1d).
    *
    * @return Duration
    */
   public Duration getDuration()
   {
      return (Duration) get(TaskField.DURATION);
   }

   /**
    * Retrieves the duration text of a manually scheduled task.
    *
    * @return duration text
    */
   public String getDurationText()
   {
      return (String) get(TaskField.DURATION_TEXT);
   }

   /**
    * Set a duration value.
    *
    * @param index duration index (1-10)
    * @param value duration value
    */
   public void setDuration(int index, Duration value)
   {
      set(selectField(TaskFieldLists.CUSTOM_DURATION, index), value);
   }

   /**
    * Retrieve a duration value.
    *
    * @param index duration index (1-10)
    * @return duration value
    */
   public Duration getDuration(int index)
   {
      return (Duration) get(selectField(TaskFieldLists.CUSTOM_DURATION, index));
   }

   /**
    * The Duration Variance field contains the difference between the
    * baseline duration of a task and the total duration (current estimate)
    * of a task.
    *
    * @return Duration
    */
   public Duration getDurationVariance()
   {
      return (Duration) get(TaskField.DURATION_VARIANCE);
   }

   /**
    * The Early Finish field contains the earliest date that a task could
    * possibly finish, based on early finish dates of predecessor and
    * successor tasks, other constraints, and any leveling delay.
    *
    * @return Date
    */
   public LocalDateTime getEarlyFinish()
   {
      return (LocalDateTime) get(TaskField.EARLY_FINISH);
   }

   /**
   * The date the resource is scheduled to finish the remaining work for the activity.
   *
   * @return Date
   */
   public LocalDateTime getRemainingEarlyFinish()
   {
      return (LocalDateTime) get(TaskField.REMAINING_EARLY_FINISH);
   }

   /**
    * The Early Start field contains the earliest date that a task could
    * possibly begin, based on the early start dates of predecessor and
    * successor tasks, and other constraints.
    *
    * @return Date
    */
   public LocalDateTime getEarlyStart()
   {
      return (LocalDateTime) get(TaskField.EARLY_START);
   }

   /**
   * The date the resource is scheduled to start the remaining work for the activity.
   *
   * @return Date
   */
   public LocalDateTime getRemainingEarlyStart()
   {
      return (LocalDateTime) get(TaskField.REMAINING_EARLY_START);
   }

   /**
    * The Finish field shows the date and time that a task is scheduled to
    * be completed. You can enter the finish date you want, to indicate the
    * date when the task should be completed. Or, you can have Microsoft
    * Project calculate the finish date.
    *
    * @return Date
    */
   public LocalDateTime getFinish()
   {
      return (LocalDateTime) get(TaskField.FINISH);
   }

   /**
    * Retrieves the finish text of a manually scheduled task.
    *
    * @return finish text
    */
   public String getFinishText()
   {
      return (String) get(TaskField.FINISH_TEXT);
   }

   /**
    * Set a finish value.
    *
    * @param index finish index (1-10)
    * @param value finish value
    */
   public void setFinish(int index, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.CUSTOM_FINISH, index), value);
   }

   /**
    * Retrieve a finish value.
    *
    * @param index finish index (1-10)
    * @return finish value
    */
   public LocalDateTime getFinish(int index)
   {
      return (LocalDateTime) get(selectField(TaskFieldLists.CUSTOM_FINISH, index));
   }

   /**
    * Calculate the finish variance.
    *
    * @return finish variance
    */
   public Duration getFinishVariance()
   {
      return (Duration) get(TaskField.FINISH_VARIANCE);
   }

   /**
    * The Fixed Cost field shows any task expense that is not associated
    * with a resource cost.
    *
    * @return currency amount
    */
   public Number getFixedCost()
   {
      return (Number) get(TaskField.FIXED_COST);
   }

   /**
    * Set a flag value.
    *
    * @param index flag index (1-20)
    * @param value flag value
    */
   public void setFlag(int index, boolean value)
   {
      set(selectField(TaskFieldLists.CUSTOM_FLAG, index), value);
   }

   /**
    * Retrieve a flag value.
    *
    * @param index flag index (1-20)
    * @return flag value
    */
   public boolean getFlag(int index)
   {
      return BooleanHelper.getBoolean((Boolean) get(selectField(TaskFieldLists.CUSTOM_FLAG, index)));
   }

   /**
    * The Free Slack field contains the amount of time that a task can be
    * delayed without delaying any successor tasks. If the task has no
    * successors, free slack is the amount of time that a task can be
    * delayed without delaying the entire project's finish date.
    *
    * @return Duration
    */
   public Duration getFreeSlack()
   {
      return (Duration) get(TaskField.FREE_SLACK);
   }

   /**
    * The Hide Bar field indicates whether the Gantt bars and Calendar bars
    * for a task are hidden. Click Yes in the Hide Bar field to hide the
    * bar for the task. Click No in the Hide Bar field to show the bar
    * for the task.
    *
    * @return boolean
    */
   public boolean getHideBar()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.HIDE_BAR)));
   }

   /**
    * The ID field contains the identifier number that Microsoft Project
    * automatically assigns to each task as you add it to the project.
    * The ID indicates the position of a task with respect to the other tasks.
    *
    * @return the task ID
    */
   @Override public Integer getID()
   {
      return (Integer) get(TaskField.ID);
   }

   /**
    * The Late Finish field contains the latest date that a task can finish
    * without delaying the finish of the project. This date is based on the
    * task's late start date, as well as the late start and late finish
    * dates of predecessor and successor
    * tasks, and other constraints.
    *
    * @return Date
    */
   public LocalDateTime getLateFinish()
   {
      return (LocalDateTime) get(TaskField.LATE_FINISH);
   }

   /**
    * Retrieve the remaining late finish value.
    *
    * @return remaining late finish
    */
   public LocalDateTime getRemainingLateFinish()
   {
      return (LocalDateTime) get(TaskField.REMAINING_LATE_FINISH);
   }

   /**
    * The Late Start field contains the latest date that a task can start
    * without delaying the finish of the project. This date is based on
    * the task's start date, as well as the late start and late finish
    * dates of predecessor and successor tasks, and other constraints.
    *
    * @return Date
    */
   public LocalDateTime getLateStart()
   {
      return (LocalDateTime) get(TaskField.LATE_START);
   }

   /**
    * Retrieve the remaining late start value.
    *
    * @return remaining late start
    */
   public LocalDateTime getRemainingLateStart()
   {
      return (LocalDateTime) get(TaskField.REMAINING_LATE_START);
   }

   /**
    * The Linked Fields field indicates whether there are OLE links to the task,
    * either from elsewhere in the active project, another Microsoft Project file,
    * or from another program.
    *
    * @return boolean
    */
   public boolean getLinkedFields()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.LINKED_FIELDS)));
   }

   /**
    * The Marked field indicates whether a task is marked for further action or
    * identification of some kind. To mark a task, click Yes in the Marked field.
    * If you don't want a task marked, click No.
    *
    * @return true for marked
    */
   public boolean getMarked()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.MARKED)));
   }

   /**
    * The Milestone field indicates whether a task is a milestone.
    *
    * @return boolean
    */
   public boolean getMilestone()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.MILESTONE)));
   }

   /**
    * Retrieves the task name.
    *
    * @return task name
    */
   public String getName()
   {
      return (String) get(TaskField.NAME);
   }

   /**
    * Retrieve the plain text representation of the task notes.
    * Use the getNotesObject method to retrieve an object which
    * contains both the plain text notes and, if relevant,
    * the original formatted version of the notes.
    *
    * @return notes
    */
   public String getNotes()
   {
      Object notes = get(TaskField.NOTES);
      return notes == null ? "" : notes.toString();
   }

   /**
    * Retrieve an object which contains both the plain text notes
    * and, if relevant, the original formatted version of the notes.
    *
    * @return Notes instance
    */
   public Notes getNotesObject()
   {
      return (Notes) get(TaskField.NOTES);
   }

   /**
    * Set a number value.
    *
    * @param index number index (1-20)
    * @param value number value
    */
   public void setNumber(int index, Number value)
   {
      set(selectField(TaskFieldLists.CUSTOM_NUMBER, index), value);
   }

   /**
    * Retrieve a number value.
    *
    * @param index number index (1-20)
    * @return number value
    */
   public Number getNumber(int index)
   {
      return (Number) get(selectField(TaskFieldLists.CUSTOM_NUMBER, index));
   }

   /**
    * The Objects field contains the number of objects attached to a task.
    * Microsoft Project counts the number of objects linked or embedded to a task.
    * However, objects in the Notes box in the Resource Form are not included
    * in this count.
    *
    * @return int
    */
   public Integer getObjects()
   {
      return (Integer) get(TaskField.OBJECTS);
   }

   /**
    * The Outline Level field contains the number that indicates the level
    * of the task in the project outline hierarchy.
    *
    * @return int
    */
   public Integer getOutlineLevel()
   {
      return (Integer) get(TaskField.OUTLINE_LEVEL);
   }

   /**
    * The Outline Number field contains the number of the task in the structure
    * of an outline. This number indicates the task's position within the
    * hierarchical structure of the project outline. The outline number is
    * similar to a WBS (work breakdown structure) number,
    * except that the outline number is automatically entered by
    * Microsoft Project.
    *
    * @return String
    */
   public String getOutlineNumber()
   {
      return (String) get(TaskField.OUTLINE_NUMBER);
   }

   /**
    * Retrieves the list of predecessors for this task.
    *
    * @return list of predecessor Relation instances
    */
   @SuppressWarnings("unchecked") public List<Relation> getPredecessors()
   {
      return (List<Relation>) get(TaskField.PREDECESSORS);
   }

   /**
    * Retrieves the list of successors for this task.
    *
    * @return list of successor Relation instances
    */
   @SuppressWarnings("unchecked") public List<Relation> getSuccessors()
   {
      return (List<Relation>) get(TaskField.SUCCESSORS);
   }

   /**
    * The Priority field provides choices for the level of importance
    * assigned to a task, which in turn indicates how readily a task can be
    * delayed or split during resource leveling.
    * The default priority is Medium. Those tasks with a priority
    * of Do Not Level are never delayed or split when Microsoft Project levels
    * tasks that have overallocated resources assigned.
    *
    * @return priority class instance
    */
   public Priority getPriority()
   {
      return (Priority) get(TaskField.PRIORITY);
   }

   /**
    * The Project field shows the name of the project from which a task
    * originated.
    * This can be the name of the active project file. If there are other
    * projects inserted
    * into the active project file, the name of the inserted project appears
    * in this field
    * for the task.
    *
    * @return name of originating project
    */
   public String getProject()
   {
      return (String) get(TaskField.PROJECT);
   }

   /**
    * The Remaining Cost field shows the remaining scheduled expense of a
    * task that will be incurred in completing the remaining scheduled work
    * by all resources assigned to the task.
    *
    * @return remaining cost
    */
   public Number getRemainingCost()
   {
      return (Number) get(TaskField.REMAINING_COST);
   }

   /**
    * The Remaining Duration field shows the amount of time required
    * to complete the unfinished portion of a task.
    *
    * @return Duration
    */
   public Duration getRemainingDuration()
   {
      return (Duration) get(TaskField.REMAINING_DURATION);
   }

   /**
    * The Remaining Work field shows the amount of time, or person-hours,
    * still required by all assigned resources to complete a task.
    *
    * @return the amount of time still required to complete a task
    */
   public Duration getRemainingWork()
   {
      return (Duration) get(TaskField.REMAINING_WORK);
   }

   /**
    * The Resource Group field contains the list of resource groups to which
    * the resources assigned to a task belong.
    *
    * @return single string list of groups
    */
   public String getResourceGroup()
   {
      return (String) get(TaskField.RESOURCE_GROUP);
   }

   /**
    * The Resource Initials field lists the abbreviations for the names of
    * resources assigned to a task. These initials can serve as substitutes
    * for the names.
    *
    * Note that MS Project 98 does not export values for this field when
    * writing an MPX file, and the field is not currently populated by MPXJ
    * when reading an MPP file.
    *
    * @return String containing a comma separated list of initials
    */
   public String getResourceInitials()
   {
      return (String) get(TaskField.RESOURCE_INITIALS);
   }

   /**
    * The Resource Names field lists the names of all resources assigned
    * to a task.
    *
    * Note that MS Project 98 does not export values for this field when
    * writing an MPX file, and the field is not currently populated by MPXJ
    * when reading an MPP file.
    *
    * @return String containing a comma separated list of names
    */
   public String getResourceNames()
   {
      return (String) get(TaskField.RESOURCE_NAMES);
   }

   /**
    * The Resume field shows the date that the remaining portion of a task
    * is scheduled to resume after you enter a new value for the % Complete
    * field. The Resume field is also recalculated when the remaining portion
    * of a task is moved to a new date.
    *
    * @return Date
    */
   public LocalDateTime getResume()
   {
      return (LocalDateTime) get(TaskField.RESUME);
   }

   /**
    * For subtasks, the Rollup field indicates whether information on the
    * subtask Gantt bars
    * will be rolled up to the summary task bar. For summary tasks, the
    * Rollup field indicates
    * whether the summary task bar displays rolled up bars. You must
    * have the Rollup field for
    * summary tasks set to Yes for any subtasks to roll up to them.
    *
    * @return boolean
    */
   public boolean getRollup()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.ROLLUP)));
   }

   /**
    * The Start field shows the date and time that a task is scheduled to begin.
    * You can enter the start date you want, to indicate the date when the task
    * should begin. Or, you can have Microsoft Project calculate the start date.
    *
    * @return Date
    */
   public LocalDateTime getStart()
   {
      return (LocalDateTime) get(TaskField.START);
   }

   /**
    * Retrieve the start text for a manually scheduled task.
    *
    * @return start text
    */
   public String getStartText()
   {
      return (String) get(TaskField.START_TEXT);
   }

   /**
    * Set a start value.
    *
    * @param index start index (1-10)
    * @param value start value
    */
   public void setStart(int index, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.CUSTOM_START, index), value);
   }

   /**
    * Retrieve a start value.
    *
    * @param index start index (1-10)
    * @return start value
    */
   public LocalDateTime getStart(int index)
   {
      return (LocalDateTime) get(selectField(TaskFieldLists.CUSTOM_START, index));
   }

   /**
    * Calculate the start variance.
    *
    * @return start variance
    */
   public Duration getStartVariance()
   {
      return (Duration) get(TaskField.START_VARIANCE);
   }

   /**
    * The Stop field shows the date that represents the end of the actual
    * portion of a task. Typically, Microsoft Project calculates the stop date.
    * However, you can edit this date as well.
    *
    * @return Date
    */
   public LocalDateTime getStop()
   {
      return (LocalDateTime) get(TaskField.STOP);
   }

   /**
    * Contains the file name and path of the external project linked
    * to this task.
    *
    * @return subproject file path
    */
   public String getSubprojectFile()
   {
      return (String) get(TaskField.SUBPROJECT_FILE);
   }

   /**
    * The Summary field indicates whether a task is a summary task.
    *
    * @return boolean, true-is summary task
    */
   public boolean getSummary()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.SUMMARY)));
   }

   /**
    * The SV (earned value schedule variance) field shows the difference in
    * cost terms between the current progress and the baseline plan of the
    * task up to the status date or today's date. You can use SV to
    * check costs to determine whether tasks are on schedule.
    *
    * @return -earned value schedule variance
    */
   public Number getSV()
   {
      return (Number) get(TaskField.SV);
   }

   /**
    * Set a text value.
    *
    * @param index text index (1-30)
    * @param value text value
    */
   public void setText(int index, String value)
   {
      set(selectField(TaskFieldLists.CUSTOM_TEXT, index), value);
   }

   /**
    * Retrieve a text value.
    *
    * @param index text index (1-30)
    * @return text value
    */
   public String getText(int index)
   {
      return (String) get(selectField(TaskFieldLists.CUSTOM_TEXT, index));
   }

   /**
    * Set an outline code value.
    *
    * @param index outline code index (1-10)
    * @param value outline code value
    */
   public void setOutlineCode(int index, String value)
   {
      set(selectField(TaskFieldLists.CUSTOM_OUTLINE_CODE, index), value);
   }

   /**
    * Retrieve an outline code value.
    *
    * @param index outline code index (1-10)
    * @return outline code value
    */
   public String getOutlineCode(int index)
   {
      return (String) get(selectField(TaskFieldLists.CUSTOM_OUTLINE_CODE, index));
   }

   /**
    * The Total Slack field contains the amount of time a task can be
    * delayed without delaying the project's finish date.
    *
    * @return string representing duration
    */
   public Duration getTotalSlack()
   {
      return (Duration) get(TaskField.TOTAL_SLACK);
   }

   /**
    * The Unique ID field contains the number that Microsoft Project
    * automatically designates whenever a new task is created. This number
    * indicates the sequence in which the task was
    * created, regardless of placement in the schedule.
    *
    * @return String
    */
   @Override public Integer getUniqueID()
   {
      return (Integer) get(TaskField.UNIQUE_ID);
   }

   /**
    * The Update Needed field indicates whether a TeamUpdate message
    * should be sent to the assigned resources because of changes to the
    * start date, finish date, or resource reassignments of the task.
    *
    * @return true if needed.
    */
   public boolean getUpdateNeeded()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.UPDATE_NEEDED)));
   }

   /**
    * The work breakdown structure code. The WBS field contains an
    * alphanumeric code you can use to represent the task's position within
    * the hierarchical structure of the project. This field is similar to
    * the outline number, except that you can edit it.
    *
    * @return string
    */
   public String getWBS()
   {
      return (String) get(TaskField.WBS);
   }

   /**
    * The Work field shows the total amount of work scheduled to be performed
    * on a task by all assigned resources. This field shows the total work,
    * or person-hours, for a task.
    *
    * @return Duration representing duration .
    */
   public Duration getWork()
   {
      return (Duration) get(TaskField.WORK);
   }

   /**
    * The Work Variance field contains the difference between a task's
    * baseline work and the currently scheduled work.
    *
    * @return Duration representing duration.
    */
   public Duration getWorkVariance()
   {
      return (Duration) get(TaskField.WORK_VARIANCE);
   }

   /**
    * This method retrieves a reference to the parent of this task, as
    * defined by the outline level. If this task is at the top level,
    * this method will return null.
    *
    * @return parent task
    */
   public Task getParentTask()
   {
      return m_parent;
   }

   /**
    * Retrieve the unique ID of the parent task.
    *
    * @return parent task unique ID, or null if there is no parent class
    */
   public Integer getParentTaskUniqueID()
   {
      return (Integer) get(TaskField.PARENT_TASK_UNIQUE_ID);
   }

   /**
    * This method retrieves a list of child tasks relative to the
    * current task, as defined by the outline level. If there
    * are no child tasks, this method will return an empty list.
    *
    * @return child tasks
    */
   @Override public List<Task> getChildTasks()
   {
      return m_children;
   }

   /**
    * This method implements the only method in the Comparable interface.
    * This allows Tasks to be compared and sorted based on their ID value.
    * Note that if the MPX/MPP file has been generated by MSP, the ID value
    * will always be in the correct sequence. The Unique ID value will not
    * necessarily be in the correct sequence as task insertions and deletions
    * will change the order.
    *
    * @param o object to compare this instance with
    * @return result of comparison
    */
   @Override public int compareTo(Task o)
   {
      int id1 = NumberHelper.getInt(getID());
      int id2 = NumberHelper.getInt(o.getID());
      return (Integer.compare(id1, id2));
   }

   /**
    * This method retrieves a flag indicating whether the duration of the
    * task has only been estimated.
    *
    * @return boolean
    */
   public boolean getEstimated()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.ESTIMATED)));
   }

   /**
    * This method retrieves a flag indicating whether the duration of the
    * task has only been estimated.
    *
    * @param estimated Boolean flag
    */
   public void setEstimated(boolean estimated)
   {
      set(TaskField.ESTIMATED, estimated);
   }

   /**
    * This method retrieves the deadline for this task.
    *
    * @return Task deadline
    */
   public LocalDateTime getDeadline()
   {
      return (LocalDateTime) get(TaskField.DEADLINE);
   }

   /**
    * This method sets the deadline for this task.
    *
    * @param deadline deadline date
    */
   public void setDeadline(LocalDateTime deadline)
   {
      set(TaskField.DEADLINE, deadline);
   }

   /**
    * This method retrieves the task type.
    *
    * @return int representing the task type
    */
   public TaskType getType()
   {
      return (TaskType) get(TaskField.TYPE);
   }

   /**
    * This method sets the task type.
    *
    * @param type task type
    */
   public void setType(TaskType type)
   {
      set(TaskField.TYPE, type);
   }

   /**
    * Retrieves the flag indicating if this is a null task.
    *
    * @return boolean flag
    */
   public boolean getNull()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.NULL)));
   }

   /**
    * Sets the flag indicating if this is a null task.
    *
    * @param isNull boolean flag
    */
   public void setNull(boolean isNull)
   {
      set(TaskField.NULL, isNull);
   }

   /**
    * Retrieve the resume valid flag.
    *
    * @return resume valid flag
    */
   public boolean getResumeValid()
   {
      return BooleanHelper.getBoolean((Boolean) get(TaskField.RESUME_VALID));
   }

   /**
    * Set the resume valid flag.
    *
    * @param resumeValid resume valid flag
    */
   public void setResumeValid(boolean resumeValid)
   {
      set(TaskField.RESUME_VALID, resumeValid);
   }

   /**
    * Retrieve the recurring flag.
    *
    * @return recurring flag
    */
   public boolean getRecurring()
   {
      return BooleanHelper.getBoolean((Boolean) get(TaskField.RECURRING));
   }

   /**
    * Set the recurring flag.
    *
    * @param recurring recurring flag
    */
   public void setRecurring(boolean recurring)
   {
      set(TaskField.RECURRING, recurring);
   }

   /**
    * Retrieve the over allocated flag.
    *
    * @return over allocated flag
    */
   public boolean getOverAllocated()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.OVERALLOCATED)));
   }

   /**
    * Set the over allocated flag.
    *
    * @param overAllocated over allocated flag
    */
   public void setOverAllocated(boolean overAllocated)
   {
      set(TaskField.OVERALLOCATED, overAllocated);
   }

   /**
    * Where a task in an MPP file represents a task from a subproject,
    * this value will be non-zero. The value itself is the unique ID
    * value shown in the parent project. To retrieve the value of the
    * task unique ID in the child project, remove the top two bytes:
    *
    * taskID = (subprojectUniqueID &amp; 0xFFFF)
    *
    * @return sub project unique task ID
    */
   public Integer getSubprojectTaskUniqueID()
   {
      return (Integer) get(TaskField.SUBPROJECT_TASK_UNIQUE_ID);
   }

   /**
    * Sets the sub project unique task ID.
    *
    * @param subprojectUniqueTaskID subproject unique task ID
    */
   public void setSubprojectTaskUniqueID(Integer subprojectUniqueTaskID)
   {
      set(TaskField.SUBPROJECT_TASK_UNIQUE_ID, subprojectUniqueTaskID);
   }

   /**
    * Where a task in an MPP file represents a task from a subproject,
    * this value will be non-zero. The value itself is the ID
    * value shown in the parent project.
    *
    * @return sub project task ID
    */
   public Integer getSubprojectTaskID()
   {
      return (Integer) get(TaskField.SUBPROJECT_TASK_ID);
   }

   /**
    * Sets the sub project task ID.
    *
    * @param subprojectTaskID subproject task ID
    */
   public void setSubprojectTaskID(Integer subprojectTaskID)
   {
      set(TaskField.SUBPROJECT_TASK_ID, subprojectTaskID);
   }

   /**
    * Sets the offset added to unique task IDs from sub projects
    * to generate the task ID shown in the master project.
    *
    * @param offset unique ID offset
    */
   public void setSubprojectTasksUniqueIDOffset(Integer offset)
   {
      set(TaskField.SUBPROJECT_TASKS_UNIQUEID_OFFSET, offset);
   }

   /**
    * Retrieves the offset added to unique task IDs from sub projects
    * to generate the task ID shown in the master project.
    *
    * @return unique ID offset
    */
   public Integer getSubprojectTasksUniqueIDOffset()
   {
      return (Integer) get(TaskField.SUBPROJECT_TASKS_UNIQUEID_OFFSET);
   }

   /**
    * Retrieve the subproject read only flag.
    *
    * @return subproject read only flag
    */
   public boolean getSubprojectReadOnly()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.SUBPROJECT_READ_ONLY)));
   }

   /**
    * Set the subproject read only flag.
    *
    * @param subprojectReadOnly subproject read only flag
    */
   public void setSubprojectReadOnly(boolean subprojectReadOnly)
   {
      set(TaskField.SUBPROJECT_READ_ONLY, subprojectReadOnly);
   }

   /**
    * Retrieves the external task flag.
    *
    * @return external task flag
    */
   public boolean getExternalTask()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.EXTERNAL_TASK)));
   }

   /**
    * Sets the external task flag.
    *
    * @param externalTask external task flag
    */
   public void setExternalTask(boolean externalTask)
   {
      set(TaskField.EXTERNAL_TASK, externalTask);
   }

   /**
    * Retrieves the external project flag.
    *
    * @return true if this task represents an external project
    */
   public boolean getExternalProject()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.EXTERNAL_PROJECT)));
   }

   /**
    * Retrieve the ACWP value.
    *
    * @return ACWP value
    */
   public Number getACWP()
   {
      return (Number) get(TaskField.ACWP);
   }

   /**
    * Set the ACWP value.
    *
    * @param acwp ACWP value
    */
   public void setACWP(Number acwp)
   {
      set(TaskField.ACWP, acwp);
   }

   /**
    * Retrieve the leveling delay format.
    *
    * @return leveling delay  format
    */
   public TimeUnit getLevelingDelayFormat()
   {
      return (TimeUnit) get(TaskField.LEVELING_DELAY_UNITS);
   }

   /**
    * Set the leveling delay format.
    *
    * @param levelingDelayFormat leveling delay format
    */
   public void setLevelingDelayFormat(TimeUnit levelingDelayFormat)
   {
      set(TaskField.LEVELING_DELAY_UNITS, levelingDelayFormat);
   }

   /**
    * Retrieves the ignore resource calendar flag.
    *
    * @return ignore resource calendar flag
    */
   public boolean getIgnoreResourceCalendar()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.IGNORE_RESOURCE_CALENDAR)));
   }

   /**
    * Sets the ignore resource calendar flag.
    *
    * @param ignoreResourceCalendar ignore resource calendar flag
    */
   public void setIgnoreResourceCalendar(boolean ignoreResourceCalendar)
   {
      set(TaskField.IGNORE_RESOURCE_CALENDAR, ignoreResourceCalendar);
   }

   /**
    * Retrieves the physical percent complete value.
    *
    * @return physical percent complete value
    */
   public Number getPhysicalPercentComplete()
   {
      return (Number) get(TaskField.PHYSICAL_PERCENT_COMPLETE);
   }

   /**
    * Sets the physical percent complete value.
    *
    * @param physicalPercentComplete physical percent complete value
    */
   public void setPhysicalPercentComplete(Number physicalPercentComplete)
   {
      set(TaskField.PHYSICAL_PERCENT_COMPLETE, physicalPercentComplete);
   }

   /**
    * Retrieves the earned value method.
    *
    * @return earned value method
    */
   public EarnedValueMethod getEarnedValueMethod()
   {
      return (EarnedValueMethod) get(TaskField.EARNED_VALUE_METHOD);
   }

   /**
    * Sets the earned value method.
    *
    * @param earnedValueMethod earned value method
    */
   public void setEarnedValueMethod(EarnedValueMethod earnedValueMethod)
   {
      set(TaskField.EARNED_VALUE_METHOD, earnedValueMethod);
   }

   /**
    * Retrieves the actual work protected value.
    *
    * @return actual work protected value
    */
   public Duration getActualWorkProtected()
   {
      return (Duration) get(TaskField.ACTUAL_WORK_PROTECTED);
   }

   /**
    * Sets the actual work protected value.
    *
    * @param actualWorkProtected actual work protected value
    */
   public void setActualWorkProtected(Duration actualWorkProtected)
   {
      set(TaskField.ACTUAL_WORK_PROTECTED, actualWorkProtected);
   }

   /**
    * Retrieves the actual overtime work protected value.
    *
    * @return actual overtime work protected value
    */
   public Duration getActualOvertimeWorkProtected()
   {
      return (Duration) get(TaskField.ACTUAL_OVERTIME_WORK_PROTECTED);
   }

   /**
    * Sets the actual overtime work protected value.
    *
    * @param actualOvertimeWorkProtected actual overtime work protected value
    */
   public void setActualOvertimeWorkProtected(Duration actualOvertimeWorkProtected)
   {
      set(TaskField.ACTUAL_OVERTIME_WORK_PROTECTED, actualOvertimeWorkProtected);
   }

   /**
    * Retrieve the amount of regular work.
    *
    * @return amount of regular work
    */
   public Duration getRegularWork()
   {
      return (Duration) get(TaskField.REGULAR_WORK);
   }

   /**
    * Set the amount of regular work.
    *
    * @param regularWork amount of regular work
    */
   public void setRegularWork(Duration regularWork)
   {
      set(TaskField.REGULAR_WORK, regularWork);
   }

   /**
    * Sets the effort driven flag.
    *
    * @param flag value
    */
   public void setEffortDriven(boolean flag)
   {
      set(TaskField.EFFORT_DRIVEN, flag);
   }

   /**
    * Retrieves the effort driven flag.
    *
    * @return Flag value
    */
   public boolean getEffortDriven()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.EFFORT_DRIVEN)));
   }

   /**
    * Set a date value.
    *
    * @param index date index (1-10)
    * @param value date value
    */
   public void setDate(int index, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.CUSTOM_DATE, index), value);
   }

   /**
    * Retrieve a date value.
    *
    * @param index date index (1-10)
    * @return date value
    */
   public LocalDateTime getDate(int index)
   {
      return (LocalDateTime) get(selectField(TaskFieldLists.CUSTOM_DATE, index));
   }

   /**
    * Retrieves the overtime cost.
    *
    * @return Cost value
    */
   public Number getOvertimeCost()
   {
      return (Number) get(TaskField.OVERTIME_COST);
   }

   /**
    * Sets the overtime cost value.
    *
    * @param number Cost value
    */
   public void setOvertimeCost(Number number)
   {
      set(TaskField.OVERTIME_COST, number);
   }

   /**
    * Retrieves the actual overtime cost for this task.
    *
    * @return actual overtime cost
    */
   public Number getActualOvertimeCost()
   {
      return (Number) get(TaskField.ACTUAL_OVERTIME_COST);
   }

   /**
    * Sets the actual overtime cost for this task.
    *
    * @param cost actual overtime cost
    */
   public void setActualOvertimeCost(Number cost)
   {
      set(TaskField.ACTUAL_OVERTIME_COST, cost);
   }

   /**
    * Retrieves the actual overtime work value.
    *
    * @return actual overtime work value
    */
   public Duration getActualOvertimeWork()
   {
      return (Duration) get(TaskField.ACTUAL_OVERTIME_WORK);
   }

   /**
    * Sets the actual overtime work value.
    *
    * @param work actual overtime work value
    */
   public void setActualOvertimeWork(Duration work)
   {
      set(TaskField.ACTUAL_OVERTIME_WORK, work);
   }

   /**
    * Retrieves the fixed cost accrual flag value.
    *
    * @return fixed cost accrual flag
    */
   public AccrueType getFixedCostAccrual()
   {
      return (AccrueType) get(TaskField.FIXED_COST_ACCRUAL);
   }

   /**
    * Sets the fixed cost accrual flag value.
    *
    * @param type fixed cost accrual type
    */
   public void setFixedCostAccrual(AccrueType type)
   {
      set(TaskField.FIXED_COST_ACCRUAL, type);
   }

   /**
    * Retrieves the task hyperlink attribute.
    *
    * @return hyperlink attribute
    */
   public String getHyperlink()
   {
      return (String) get(TaskField.HYPERLINK);
   }

   /**
    * Retrieves the task hyperlink address attribute.
    *
    * @return hyperlink address attribute
    */
   public String getHyperlinkAddress()
   {
      return (String) get(TaskField.HYPERLINK_ADDRESS);
   }

   /**
    * Retrieves the task hyperlink sub-address attribute.
    *
    * @return hyperlink sub address attribute
    */
   public String getHyperlinkSubAddress()
   {
      return (String) get(TaskField.HYPERLINK_SUBADDRESS);
   }

   /**
    * Retrieves the task hyperlink screen tip attribute.
    *
    * @return hyperlink screen tip attribute
    */
   public String getHyperlinkScreenTip()
   {
      return (String) get(TaskField.HYPERLINK_SCREEN_TIP);
   }

   /**
    * Sets the task hyperlink attribute.
    *
    * @param text hyperlink attribute
    */
   public void setHyperlink(String text)
   {
      set(TaskField.HYPERLINK, text);
   }

   /**
    * Sets the task hyperlink address attribute.
    *
    * @param text hyperlink address attribute
    */
   public void setHyperlinkAddress(String text)
   {
      set(TaskField.HYPERLINK_ADDRESS, text);
   }

   /**
    * Sets the task hyperlink sub address attribute.
    *
    * @param text hyperlink sub address attribute
    */
   public void setHyperlinkSubAddress(String text)
   {
      set(TaskField.HYPERLINK_SUBADDRESS, text);
   }

   /**
    * Sets the task hyperlink screen tip attribute.
    *
    * @param text hyperlink screen tip attribute
    */
   public void setHyperlinkScreenTip(String text)
   {
      set(TaskField.HYPERLINK_SCREEN_TIP, text);
   }

   /**
    * Retrieves the level assignments flag.
    *
    * @return level assignments flag
    */
   public boolean getLevelAssignments()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.LEVEL_ASSIGNMENTS)));
   }

   /**
    * Sets the level assignments flag.
    *
    * @param flag level assignments flag
    */
   public void setLevelAssignments(boolean flag)
   {
      set(TaskField.LEVEL_ASSIGNMENTS, flag);
   }

   /**
    * Retrieves the leveling can split flag.
    *
    * @return leveling can split flag
    */
   public boolean getLevelingCanSplit()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.LEVELING_CAN_SPLIT)));
   }

   /**
    * Sets the leveling can split flag.
    *
    * @param flag leveling can split flag
    */
   public void setLevelingCanSplit(boolean flag)
   {
      set(TaskField.LEVELING_CAN_SPLIT, flag);
   }

   /**
    * Retrieves the overtime work attribute.
    *
    * @return overtime work value
    */
   public Duration getOvertimeWork()
   {
      return (Duration) get(TaskField.OVERTIME_WORK);
   }

   /**
    * Sets the overtime work attribute.
    *
    * @param work overtime work value
    */
   public void setOvertimeWork(Duration work)
   {
      set(TaskField.OVERTIME_WORK, work);
   }

   /**
    * Retrieves the preleveled start attribute.
    *
    * @return preleveled start
    */
   public LocalDateTime getPreleveledStart()
   {
      return (LocalDateTime) get(TaskField.PRELEVELED_START);
   }

   /**
    * Retrieves the preleveled finish attribute.
    *
    * @return preleveled finish
    */
   public LocalDateTime getPreleveledFinish()
   {
      return (LocalDateTime) get(TaskField.PRELEVELED_FINISH);
   }

   /**
    * Sets the preleveled start attribute.
    *
    * @param date preleveled start attribute
    */
   public void setPreleveledStart(LocalDateTime date)
   {
      set(TaskField.PRELEVELED_START, date);
   }

   /**
    * Sets the preleveled finish attribute.
    *
    * @param date preleveled finish attribute
    */
   public void setPreleveledFinish(LocalDateTime date)
   {
      set(TaskField.PRELEVELED_FINISH, date);
   }

   /**
    * Retrieves the remaining overtime work attribute.
    *
    * @return remaining overtime work
    */
   public Duration getRemainingOvertimeWork()
   {
      return (Duration) get(TaskField.REMAINING_OVERTIME_WORK);
   }

   /**
    * Sets the remaining overtime work attribute.
    *
    * @param work remaining overtime work
    */
   public void setRemainingOvertimeWork(Duration work)
   {
      set(TaskField.REMAINING_OVERTIME_WORK, work);
   }

   /**
    * Retrieves the remaining overtime cost.
    *
    * @return remaining overtime cost value
    */
   public Number getRemainingOvertimeCost()
   {
      return (Number) get(TaskField.REMAINING_OVERTIME_COST);
   }

   /**
    * Sets the remaining overtime cost value.
    *
    * @param cost overtime cost value
    */
   public void setRemainingOvertimeCost(Number cost)
   {
      set(TaskField.REMAINING_OVERTIME_COST, cost);
   }

   /**
    * Retrieves the calendar associated with this task.
    *
    * @return ProjectCalendar instance
    */
   public ProjectCalendar getCalendar()
   {
      return getParentFile().getCalendars().getByUniqueID(getCalendarUniqueID());
   }

   /**
    * Set the calendar unique ID.
    *
    * @param id calendar unique ID
    */
   public void setCalendarUniqueID(Integer id)
   {
      set(TaskField.CALENDAR_UNIQUE_ID, id);
   }

   /**
    * Retrieve the calendar unique ID.
    *
    * @return calendar unique ID
    */
   public Integer getCalendarUniqueID()
   {
      return (Integer) get(TaskField.CALENDAR_UNIQUE_ID);
   }

   /**
    * Sets the calendar associated with this task.
    *
    * @param calendar calendar instance
    */
   public void setCalendar(ProjectCalendar calendar)
   {
      setCalendarUniqueID(calendar == null ? null : calendar.getUniqueID());
   }

   /**
    * Retrieve a flag indicating if the task is shown as expanded
    * in MS Project. If this flag is set to true, any sub tasks
    * for this current task will be visible. If this is false,
    * any sub tasks will be hidden.
    *
    * @return boolean flag
    */
   public boolean getExpanded()
   {
      return BooleanHelper.getBoolean((Boolean) get(TaskField.EXPANDED));
   }

   /**
    * Set a flag indicating if the task is shown as expanded
    * in MS Project. If this flag is set to true, any sub tasks
    * for this current task will be visible. If this is false,
    * any sub tasks will be hidden.
    *
    * @param expanded boolean flag
    */
   public void setExpanded(boolean expanded)
   {
      set(TaskField.EXPANDED, expanded);
   }

   /**
    * Set the start slack.
    *
    * @param duration start slack
    */
   public void setStartSlack(Duration duration)
   {
      set(TaskField.START_SLACK, duration);
   }

   /**
    * Set the finish slack.
    *
    * @param duration finish slack
    */
   public void setFinishSlack(Duration duration)
   {
      set(TaskField.FINISH_SLACK, duration);
   }

   /**
    * Retrieve the start slack.
    *
    * @return start slack
    */
   public Duration getStartSlack()
   {
      return (Duration) get(TaskField.START_SLACK);
   }

   /**
    * Retrieve the finish slack.
    *
    * @return finish slack
    */
   public Duration getFinishSlack()
   {
      return (Duration) get(TaskField.FINISH_SLACK);
   }

   /**
    * Retrieve the value of a field using its alias.
    *
    * @param alias field alias
    * @return field value
    */
   public Object getFieldByAlias(String alias)
   {
      return get(getParentFile().getTasks().getFieldTypeByAlias(alias));
   }

   /**
    * Set the value of a field using its alias.
    *
    * @param alias field alias
    * @param value field value
    */
   public void setFieldByAlias(String alias, Object value)
   {
      set(getParentFile().getTasks().getFieldTypeByAlias(alias), value);
   }

   /**
    * This method retrieves a list of task splits. Each split is represented
    * by a DateRange instance. The list will always follow the pattern
    * task range, split range, task range and so on.
    *
    * Note that this method will return null if the task is not split.
    *
    * @return list of split times
    */
   @SuppressWarnings("unchecked") public List<LocalDateTimeRange> getSplits()
   {
      return (List<LocalDateTimeRange>) get(TaskField.SPLITS);
   }

   /**
    * Internal method used to set the list of splits.
    *
    * @param splits list of split times
    */
   public void setSplits(List<LocalDateTimeRange> splits)
   {
      set(TaskField.SPLITS, splits);
   }

   /**
    * Removes this task from the project.
    */
   public void remove()
   {
      getParentFile().removeTask(this);
   }

   /**
    * If this task represents an external project (subproject), calling this method
    * will attempt to read the subproject file, the link the tasks from
    * the subproject file as children of this current task.
    * <p/>
    * Calling this method on a task which does not represent an external project
    * will have no effect.
    *
    * @return a ProjectFile instance for the subproject, or null if no project was loaded
    */
   public ProjectFile expandSubproject()
   {
      // Do nothing if this is not an external project task, or we can't load the subproject
      if (!getExternalProject() || getSubprojectObject() == null)
      {
         return null;
      }

      // If the subproject contains a summary task we can ignore it
      // the current task is in effect the summary task.
      ProjectFile subproject = getSubprojectObject();
      Task summaryTask = subproject.getTaskByID(Integer.valueOf(0));
      if (summaryTask == null)
      {
         m_children.addAll(subproject.getChildTasks());
      }
      else
      {
         m_children.addAll(summaryTask.getChildTasks());
      }

      return subproject;
   }

   /**
    * If this task is an external project task or an external predecessor task,
    * attempt to load the project to which it refers. We will try the full path for the project
    * and either the process working directory, or the directory the caller supplied to
    * ProjectFile.setSubprojectWorkingDirectory.
    * <p/>
    * Note that the ProjectFile instance is cached once it has been read, so multiple calls
    * to this method don't incur the cost of finding and re-reading the project.
    *
    * @return ProjectFile instance or null if the project could not be located or read
    */
   public ProjectFile getSubprojectObject()
   {
      // we don't have a subproject or an external predecessor task
      if (!getExternalTask() && !getExternalProject())
      {
         return null;
      }

      return getParentFile().readExternalProject(getSubprojectFile());
   }

   /**
    * Where we have already read a project, this method is used to
    * attach it to the task.
    *
    * @param projectFile ProjectFile instance
    */
   public void setSubprojectObject(ProjectFile projectFile)
   {
      getParentFile().addExternalProject(getSubprojectFile(), projectFile);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public Number getEnterpriseCost(int index)
   {
      return (Number) get((selectField(TaskFieldLists.ENTERPRISE_CUSTOM_COST, index)));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseCost(int index, Number value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_COST, index), value);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public LocalDateTime getEnterpriseDate(int index)
   {
      return (LocalDateTime) get((selectField(TaskFieldLists.ENTERPRISE_CUSTOM_DATE, index)));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseDate(int index, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_DATE, index), value);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public Duration getEnterpriseDuration(int index)
   {
      return (Duration) get((selectField(TaskFieldLists.ENTERPRISE_CUSTOM_DURATION, index)));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseDuration(int index, Duration value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_DURATION, index), value);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public boolean getEnterpriseFlag(int index)
   {
      return (BooleanHelper.getBoolean((Boolean) get(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_FLAG, index))));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseFlag(int index, boolean value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_FLAG, index), value);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public Number getEnterpriseNumber(int index)
   {
      return (Number) get((selectField(TaskFieldLists.ENTERPRISE_CUSTOM_NUMBER, index)));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseNumber(int index, Number value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_NUMBER, index), value);
   }

   /**
    * Retrieve an enterprise field value.
    *
    * @param index field index
    * @return field value
    */
   public String getEnterpriseText(int index)
   {
      return (String) get((selectField(TaskFieldLists.ENTERPRISE_CUSTOM_TEXT, index)));
   }

   /**
    * Set an enterprise field value.
    *
    * @param index field index
    * @param value field value
    */
   public void setEnterpriseText(int index, String value)
   {
      set(selectField(TaskFieldLists.ENTERPRISE_CUSTOM_TEXT, index), value);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineCost(int baselineNumber, Number value)
   {
      set(selectField(TaskFieldLists.BASELINE_COSTS, baselineNumber), value);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineDuration(int baselineNumber, Duration value)
   {
      set(selectField(TaskFieldLists.BASELINE_DURATIONS, baselineNumber), value);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineFinish(int baselineNumber, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.BASELINE_FINISHES, baselineNumber), value);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineStart(int baselineNumber, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.BASELINE_STARTS, baselineNumber), value);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineWork(int baselineNumber, Duration value)
   {
      set(selectField(TaskFieldLists.BASELINE_WORKS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public Number getBaselineCost(int baselineNumber)
   {
      return (Number) get((selectField(TaskFieldLists.BASELINE_COSTS, baselineNumber)));
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public Duration getBaselineDuration(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_DURATIONS, baselineNumber));
      if (!(result instanceof Duration))
      {
         result = null;
      }
      return (Duration) result;
   }

   /**
    * Retrieves the baseline duration text value.
    *
    * @param baselineNumber baseline number
    * @return baseline duration text value
    */
   public String getBaselineDurationText(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_DURATIONS, baselineNumber));
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline duration text value.
    *
    * @param baselineNumber baseline number
    * @param value baseline duration text value
    */
   public void setBaselineDurationText(int baselineNumber, String value)
   {
      set(selectField(TaskFieldLists.BASELINE_DURATIONS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public LocalDateTime getBaselineFinish(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_FINISHES, baselineNumber));
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Retrieves the baseline finish text value.
    *
    * @param baselineNumber baseline number
    * @return baseline finish text value
    */
   public String getBaselineFinishText(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_FINISHES, baselineNumber));
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline finish text value.
    *
    * @param baselineNumber baseline number
    * @param value baseline finish text value
    */
   public void setBaselineFinishText(int baselineNumber, String value)
   {
      set(selectField(TaskFieldLists.BASELINE_FINISHES, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public LocalDateTime getBaselineStart(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_STARTS, baselineNumber));
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Retrieves the baseline start text value.
    *
    * @param baselineNumber baseline number
    * @return baseline start text value
    */
   public String getBaselineStartText(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_STARTS, baselineNumber));
      if (!(result instanceof String))
      {
         result = null;
      }
      return (String) result;
   }

   /**
    * Sets the baseline start text value.
    *
    * @param baselineNumber baseline number
    * @param value baseline start text value
    */
   public void setBaselineStartText(int baselineNumber, String value)
   {
      set(selectField(TaskFieldLists.BASELINE_STARTS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public Duration getBaselineWork(int baselineNumber)
   {
      return (Duration) get((selectField(TaskFieldLists.BASELINE_WORKS, baselineNumber)));
   }

   /**
    * Retrieve the "complete through" date. This is the date at which
    * the percent complete progress line on a task finishes.
    *
    * @return complete through date
    */
   public LocalDateTime getCompleteThrough()
   {
      return (LocalDateTime) get(TaskField.COMPLETE_THROUGH);
   }

   /**
    * Set the "complete through" date. This is the date at which
    * the percent complete progress line on a task finishes.
    *
    * @param value complete through date
    */
   public void setCompleteThrough(LocalDateTime value)
   {
      set(TaskField.COMPLETE_THROUGH, value);
   }

   /**
    * Retrieve the summary progress date.
    *
    * @return summary progress date
    */
   public LocalDateTime getSummaryProgress()
   {
      return (LocalDateTime) get(TaskField.SUMMARY_PROGRESS);
   }

   /**
    * Set the summary progress date.
    *
    * @param value summary progress date
    */
   public void setSummaryProgress(LocalDateTime value)
   {
      set(TaskField.SUMMARY_PROGRESS, value);
   }

   /**
    * Retrieve the task GUID.
    *
    * @return task GUID
    */
   public UUID getGUID()
   {
      return (UUID) get(TaskField.GUID);
   }

   /**
    * Set the task GUID.
    *
    * @param value task GUID
    */
   public void setGUID(UUID value)
   {
      set(TaskField.GUID, value);
   }

   /**
    * Retrieves the task mode.
    *
    * @return task mode
    */
   public TaskMode getTaskMode()
   {
      return (TaskMode) get(TaskField.TASK_MODE);
   }

   /**
    * Sets the task mode.
    *
    * @param mode task mode
    */
   public void setTaskMode(TaskMode mode)
   {
      set(TaskField.TASK_MODE, mode);
   }

   /**
    * Retrieves the active flag.
    *
    * @return active flag value
    */
   public boolean getActive()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.ACTIVE)));
   }

   /**
    * Sets the active flag.
    *
    * @param active active flag value
    */
   public void setActive(boolean active)
   {
      set(TaskField.ACTIVE, active);
   }

   /**
    * Retrieve the baseline estimated duration.
    *
    * @return baseline estimated duration
    */
   public Duration getBaselineEstimatedDuration()
   {
      return (Duration) get(TaskField.BASELINE_ESTIMATED_DURATION);
   }

   /**
    * Set the baseline estimated duration.
    *
    * @param duration baseline estimated duration
    */
   public void setBaselineEstimatedDuration(Duration duration)
   {
      set(TaskField.BASELINE_ESTIMATED_DURATION, duration);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineEstimatedDuration(int baselineNumber, Duration value)
   {
      set(selectField(TaskFieldLists.BASELINE_ESTIMATED_DURATIONS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public Duration getBaselineEstimatedDuration(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_ESTIMATED_DURATIONS, baselineNumber));
      if (!(result instanceof Duration))
      {
         result = null;
      }
      return (Duration) result;
   }

   /**
    * Retrieve the baseline estimated start.
    *
    * @return baseline estimated start
    */
   public LocalDateTime getBaselineEstimatedStart()
   {
      return (LocalDateTime) get(TaskField.BASELINE_ESTIMATED_START);
   }

   /**
    * Set the baseline estimated start.
    *
    * @param date baseline estimated start
    */
   public void setBaselineEstimatedStart(LocalDateTime date)
   {
      set(TaskField.BASELINE_ESTIMATED_START, date);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public LocalDateTime getBaselineEstimatedStart(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_ESTIMATED_STARTS, baselineNumber));
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineEstimatedStart(int baselineNumber, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.BASELINE_ESTIMATED_STARTS, baselineNumber), value);
   }

   /**
    * Retrieve the baseline estimated finish.
    *
    * @return baseline estimated finish
    */
   public LocalDateTime getBaselineEstimatedFinish()
   {
      return (LocalDateTime) get(TaskField.BASELINE_ESTIMATED_FINISH);
   }

   /**
    * Set the baseline estimated finish.
    *
    * @param date baseline estimated finish
    */
   public void setBaselineEstimatedFinish(LocalDateTime date)
   {
      set(TaskField.BASELINE_ESTIMATED_FINISH, date);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public LocalDateTime getBaselineEstimatedFinish(int baselineNumber)
   {
      Object result = get(selectField(TaskFieldLists.BASELINE_ESTIMATED_FINISHES, baselineNumber));
      if (!(result instanceof LocalDateTime))
      {
         result = null;
      }
      return (LocalDateTime) result;
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineEstimatedFinish(int baselineNumber, LocalDateTime value)
   {
      set(selectField(TaskFieldLists.BASELINE_ESTIMATED_FINISHES, baselineNumber), value);
   }

   /**
    * The Fixed Cost field shows any task expense that is not associated
    * with a resource cost.
    *
    * @param val amount
    */
   public void setBaselineFixedCost(Number val)
   {
      set(TaskField.BASELINE_FIXED_COST, val);
   }

   /**
    * The Fixed Cost field shows any task expense that is not associated
    * with a resource cost.
    *
    * @return currency amount
    */
   public Number getBaselineFixedCost()
   {
      return (Number) get(TaskField.BASELINE_FIXED_COST);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineFixedCost(int baselineNumber, Number value)
   {
      set(selectField(TaskFieldLists.BASELINE_FIXED_COSTS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public Number getBaselineFixedCost(int baselineNumber)
   {
      return (Number) get((selectField(TaskFieldLists.BASELINE_FIXED_COSTS, baselineNumber)));
   }

   /**
    * Retrieves the baseline fixed cost accrual.
    *
    * @return fixed cost accrual flag
    */
   public AccrueType getBaselineFixedCostAccrual()
   {
      return (AccrueType) get(TaskField.BASELINE_FIXED_COST_ACCRUAL);
   }

   /**
    * Sets the baseline fixed cost accrual.
    *
    * @param type fixed cost accrual type
    */
   public void setBaselineFixedCostAccrual(AccrueType type)
   {
      set(TaskField.BASELINE_FIXED_COST_ACCRUAL, type);
   }

   /**
    * Set a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @param value baseline value
    */
   public void setBaselineFixedCostAccrual(int baselineNumber, AccrueType value)
   {
      set(selectField(TaskFieldLists.BASELINE_FIXED_COST_ACCRUALS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline value.
    *
    * @param baselineNumber baseline index (1-10)
    * @return baseline value
    */
   public AccrueType getBaselineFixedCostAccrual(int baselineNumber)
   {
      return (AccrueType) get((selectField(TaskFieldLists.BASELINE_FIXED_COST_ACCRUALS, baselineNumber)));
   }

   /**
    * Retrieve expense items for this task.
    *
    * @return list of expense items
    */
   @SuppressWarnings("unchecked") public List<ExpenseItem> getExpenseItems()
   {
      return (List<ExpenseItem>) get(TaskField.EXPENSE_ITEMS);
   }

   /**
    * Set the expense items for this task.
    *
    * @param items list of expense items
    */
   public void setExpenseItems(List<ExpenseItem> items)
   {
      set(TaskField.EXPENSE_ITEMS, items);
   }

   /**
    * Set the stored material value for this task.
    *
    * @param value stored material value
    */
   public void setStoredMaterial(Number value)
   {
      set(TaskField.STORED_MATERIAL, value);
   }

   /**
    * Retrieve the stored material value for this task.
    *
    * @return stored material value
    */
   public Number getStoredMaterial()
   {
      return (Number) get(TaskField.STORED_MATERIAL);
   }

   /**
    * Set the feature of work field.
    *
    * @param value feature of work value
    */
   public void setFeatureOfWork(String value)
   {
      set(TaskField.FEATURE_OF_WORK, value);
   }

   /**
    * Retrieve the feature of work field.
    *
    * @return feature of work value
    */
   public String getFeatureOfWork()
   {
      return (String) get(TaskField.FEATURE_OF_WORK);
   }

   /**
    * Set the category of work field.
    *
    * @param value category of work value
    */
   public void setCategoryOfWork(String value)
   {
      set(TaskField.CATEGORY_OF_WORK, value);
   }

   /**
    * Retrieve the category of work field.
    *
    * @return category of work value
    */
   public String getCategoryOfWork()
   {
      return (String) get(TaskField.CATEGORY_OF_WORK);
   }

   /**
    * Set the phase of work field.
    *
    * @param value phase of work value
    */
   public void setPhaseOfWork(String value)
   {
      set(TaskField.PHASE_OF_WORK, value);
   }

   /**
    * Retrieve the phase of work field.
    *
    * @return phase of work value
    */
   public String getPhaseOfWork()
   {
      return (String) get(TaskField.PHASE_OF_WORK);
   }

   /**
    * Retrieve the bid item field.
    *
    * @param value bid item value
    */
   public void setBidItem(String value)
   {
      set(TaskField.BID_ITEM, value);
   }

   /**
    * Set the bid item field.
    *
    * @return bid item value
    */
   public String getBidItem()
   {
      return (String) get(TaskField.BID_ITEM);
   }

   /**
    * Retrieve the mod or claim number field.
    *
    * @param value mod or claim number value
    */
   public void setModOrClaimNumber(String value)
   {
      set(TaskField.MOD_OR_CLAIM_NUMBER, value);
   }

   /**
    * Retrieve the mod or claim number field.
    *
    * @return mod or claim number value
    */
   public String getModOrClaimNumber()
   {
      return (String) get(TaskField.MOD_OR_CLAIM_NUMBER);
   }

   /**
    * Set the work area code field.
    *
    * @param value work area code value
    */
   public void setWorkAreaCode(String value)
   {
      set(TaskField.WORK_AREA_CODE, value);
   }

   /**
    * Retrieve the work area code field.
    *
    * @return work area code value
    */
   public String getWorkAreaCode()
   {
      return (String) get(TaskField.WORK_AREA_CODE);
   }

   /**
    * Set the responsibility code field.
    *
    * @param value responsibility code value
    */
   public void setResponsibilityCode(String value)
   {
      set(TaskField.RESPONSIBILITY_CODE, value);
   }

   /**
    * Retrieve the responsibility code field.
    *
    * @return responsibility code value
    */
   public String getResponsibilityCode()
   {
      return (String) get(TaskField.RESPONSIBILITY_CODE);
   }

   /**
    * Set the workers per day field.
    *
    * @param value workers per day value
    */
   public void setWorkersPerDay(Integer value)
   {
      set(TaskField.WORKERS_PER_DAY, value);
   }

   /**
    * Retrieve the workers per day field.
    *
    * @return workers per day value
    */
   public Integer getWorkersPerDay()
   {
      return (Integer) get(TaskField.WORKERS_PER_DAY);
   }

   /**
    * Set the hammock code field.
    *
    * @param value hammock code value
    */
   public void setHammockCode(boolean value)
   {
      set(TaskField.HAMMOCK_CODE, value);
   }

   /**
    * Retrieve the hammock code field.
    *
    * @return hammock code value
    */
   public boolean getHammockCode()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.HAMMOCK_CODE)));
   }

   /**
    * Set the mail field.
    *
    * @param value mail value
    */
   public void setMail(String value)
   {
      set(TaskField.MAIL, value);
   }

   /**
    * Retrieve the mail field.
    *
    * @return mail value
    */
   public String getMail()
   {
      return (String) get(TaskField.MAIL);
   }

   /**
    * Set the section field.
    *
    * @param value section value
    */
   public void setSection(String value)
   {
      set(TaskField.SECTION, value);
   }

   /**
    * Retrieve the section field.
    *
    * @return section value
    */
   public String getSection()
   {
      return (String) get(TaskField.SECTION);
   }

   /**
    * Set the manager field.
    *
    * @param value manager value
    */
   public void setManager(String value)
   {
      set(TaskField.MANAGER, value);
   }

   /**
    * Retrieve the manager field.
    *
    * @return manager value
    */
   public String getManager()
   {
      return (String) get(TaskField.MANAGER);
   }

   /**
    * Set the department field.
    *
    * @param value department value
    */
   public void setDepartment(String value)
   {
      set(TaskField.DEPARTMENT, value);
   }

   /**
    * Retrieve the department field.
    *
    * @return department value
    */
   public String getDepartment()
   {
      return (String) get(TaskField.DEPARTMENT);
   }

   /**
    * Set the overall percent complete field.
    *
    * @param value overall percent complete value
    */
   public void setOverallPercentComplete(Number value)
   {
      set(TaskField.OVERALL_PERCENT_COMPLETE, value);
   }

   /**
    * Retrieve the overall percent complete field.
    *
    * @return overall percent complete value
    */
   public Number getOverallPercentComplete()
   {
      return (Number) get(TaskField.OVERALL_PERCENT_COMPLETE);
   }

   /**
    * Set the planned finish field.
    *
    * @param value planned finish value
    */
   public void setPlannedFinish(LocalDateTime value)
   {
      set(TaskField.PLANNED_FINISH, value);
   }

   /**
    * Retrieve the planned finish field.
    *
    * @return planned finish value
    */
   public LocalDateTime getPlannedFinish()
   {
      return (LocalDateTime) get(TaskField.PLANNED_FINISH);
   }

   /**
    * Set the planned start field.
    *
    * @param value planned start value
    */
   public void setPlannedStart(LocalDateTime value)
   {
      set(TaskField.PLANNED_START, value);
   }

   /**
    * Retrieve the planned start field.
    *
    * @return planned start value
    */
   public LocalDateTime getPlannedStart()
   {
      return (LocalDateTime) get(TaskField.PLANNED_START);
   }

   /**
    * Set the planned duration field.
    *
    * @param value planned duration value
    */
   public void setPlannedDuration(Duration value)
   {
      set(TaskField.PLANNED_DURATION, value);
   }

   /**
    * Retrieve the planned duration field.
    *
    * @return planned duration value
    */
   public Duration getPlannedDuration()
   {
      return (Duration) get(TaskField.PLANNED_DURATION);
   }

   /**
    * Set the planned work field.
    *
    * @param value planned work value
    */
   public void setPlannedWork(Duration value)
   {
      set(TaskField.PLANNED_WORK, value);
   }

   /**
    * Retrieve the planned work field.
    *
    * @return planned work value
    */
   public Duration getPlannedWork()
   {
      return (Duration) get(TaskField.PLANNED_WORK);
   }

   /**
    * Set the planned cost field.
    *
    * @param value planned cost value
    */
   public void setPlannedCost(Number value)
   {
      set(TaskField.PLANNED_COST, value);
   }

   /**
    * Retrieve the planned cost field.
    *
    * @return planned cost value
    */
   public Number getPlannedCost()
   {
      return (Number) get(TaskField.PLANNED_COST);
   }

   /**
    * Set the suspend date field.
    *
    * @param value suspend date value
    */
   public void setSuspendDate(LocalDateTime value)
   {
      set(TaskField.SUSPEND_DATE, value);
   }

   /**
    * Retrieve the suspend date field.
    *
    * @return suspend date value
    */
   public LocalDateTime getSuspendDate()
   {
      return (LocalDateTime) get(TaskField.SUSPEND_DATE);
   }

   /**
    * Set the primary resource unique ID.
    *
    * @param value primary resource unique ID
    */
   public void setPrimaryResourceUniqueID(Integer value)
   {
      set(TaskField.PRIMARY_RESOURCE_UNIQUE_ID, value);
   }

   /**
    * Retrieve the primary resource unique ID.
    *
    * @return primary resource unique ID
    */
   public Integer getPrimaryResourceUniqueID()
   {
      return (Integer) get(TaskField.PRIMARY_RESOURCE_UNIQUE_ID);
   }

   /**
    * Retrieve the primary resource for this task.
    *
    * @return primary resource
    */
   public Resource getPrimaryResource()
   {
      return getParentFile().getResourceByUniqueID(getPrimaryResourceUniqueID());
   }

   /**
    * Set the primary resource for this task.
    *
    * @param resource resource
    */
   public void setPrimaryResource(Resource resource)
   {
      setPrimaryResourceUniqueID(resource == null ? null : resource.getUniqueID());
   }

   /**
    * Set the activity ID.
    *
    * @param value activity ID value
    */
   public void setActivityID(String value)
   {
      set(TaskField.ACTIVITY_ID, value);
   }

   /**
    * Retrieve a "canonical" version of the Activity ID.
    * This method handles the case where the Activity ID for
    * a WBS entry will be prefixed with the Project ID.
    * This method replaces the Project ID with the text "PROJECT",
    * which allows WBS entries to be matched by Activity ID
    * across projects.
    *
    * @return canonical Activity ID value
    */
   public String getCanonicalActivityID()
   {
      String activityID = getActivityID();
      if (getSummary() && activityID != null)
      {
         String projectID = getParentFile().getProjectProperties().getProjectID();
         if (projectID != null && activityID.startsWith(projectID))
         {
            activityID = "PROJECT" + activityID.substring(projectID.length());
         }
      }
      return activityID;
   }

   /**
    * Retrieve the activity ID.
    *
    * @return activity ID value
    */
   public String getActivityID()
   {
      return (String) get(TaskField.ACTIVITY_ID);
   }

   /**
    * Set the percent complete type.
    *
    * @param value percent complete type
    */
   public void setPercentCompleteType(PercentCompleteType value)
   {
      set(TaskField.PERCENT_COMPLETE_TYPE, value);
   }

   /**
    * Retrieve the percent complete type.
    *
    * @return percent complete type
    */
   public PercentCompleteType getPercentCompleteType()
   {
      return (PercentCompleteType) get(TaskField.PERCENT_COMPLETE_TYPE);
   }

   /**
    * Retrieve the activity status.
    *
    * @return activity status
    */
   public ActivityStatus getActivityStatus()
   {
      return (ActivityStatus) get(TaskField.ACTIVITY_STATUS);
   }

   /**
    * Set the activity status.
    *
    * @param value activity status
    */
   public void setActivityStatus(ActivityStatus value)
   {
      set(TaskField.ACTIVITY_STATUS, value);
   }

   /**
    * Retrieve the activity type.
    *
    * @return activity type
    */
   public ActivityType getActivityType()
   {
      return (ActivityType) get(TaskField.ACTIVITY_TYPE);
   }

   /**
    * Set the activity type.
    *
    * @param value activity type
    */
   public void setActivityType(ActivityType value)
   {
      set(TaskField.ACTIVITY_TYPE, value);
   }

   /**
    * Retrieve the longest path.
    *
    * @return true if part of the longest path
    */
   public boolean getLongestPath()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.LONGEST_PATH)));
   }

   /**
    * Set the longest path.
    *
    * @param value true if part of the longest path
    */
   public void setLongestPath(boolean value)
   {
      set(TaskField.LONGEST_PATH, value);
   }

   /**
    * Retrieve the external early start date.
    *
    * @return external early start date
    */
   public LocalDateTime getExternalEarlyStart()
   {
      return (LocalDateTime) get(TaskField.EXTERNAL_EARLY_START);
   }

   /**
    * Set the external early start date.
    *
    * @param value external early start date
    */
   public void setExternalEarlyStart(LocalDateTime value)
   {
      set(TaskField.EXTERNAL_EARLY_START, value);
   }

   /**
    * Retrieve the external late finish date.
    *
    * @return external late finish date
    */
   public LocalDateTime getExternalLateFinish()
   {
      return (LocalDateTime) get(TaskField.EXTERNAL_LATE_FINISH);
   }

   /**
    * Set the external late finish date.
    *
    * @param value external late finish date
    */
   public void setExternalLateFinish(LocalDateTime value)
   {
      set(TaskField.EXTERNAL_LATE_FINISH, value);
   }

   /**
    * Retrieve the Sprint ID.
    *
    * @return sprint ID
    */
   public Integer getSprintID()
   {
      return (Integer) get(TaskField.SPRINT_ID);
   }

   /**
    * Set the sprint ID.
    *
    * @param value sprint ID
    */
   public void setSprintID(Integer value)
   {
      set(TaskField.SPRINT_ID, value);
   }

   /**
    * Retrieve the Board Status ID.
    *
    * @return board status ID
    */
   public Integer getBoardStatusID()
   {
      return (Integer) get(TaskField.BOARD_STATUS_ID);
   }

   /**
    * Set the Board Status ID.
    *
    * @param value board status ID
    */
   public void setBoardStatusID(Integer value)
   {
      set(TaskField.BOARD_STATUS_ID, value);
   }

   /**
    * Retrieve the response pending flag.
    *
    * @return response pending flag value
    */
   public boolean getResponsePending()
   {
      return BooleanHelper.getBoolean((Boolean) get(TaskField.RESPONSE_PENDING));
   }

   /**
    * Set the response pending flag.
    *
    * @param value response pending flag value
    */
   public void setResponsePending(boolean value)
   {
      set(TaskField.RESPONSE_PENDING, value);
   }

   /**
    * Retrieve the scheduled start.
    *
    * @return scheduled start value
    */
   public LocalDateTime getScheduledStart()
   {
      return (LocalDateTime) get(TaskField.SCHEDULED_START);
   }

   /**
    * Set the scheduled start.
    *
    * @param value scheduled start value
    */
   public void setScheduledStart(LocalDateTime value)
   {
      set(TaskField.SCHEDULED_START, value);
   }

   /**
    * Retrieve the scheduled finish.
    *
    * @return scheduled finish value
    */
   public LocalDateTime getScheduledFinish()
   {
      return (LocalDateTime) get(TaskField.SCHEDULED_FINISH);
   }

   /**
    *
    * Set the scheduled finish.
    *
    * @param value scheduled finish value
    */
   public void setScheduledFinish(LocalDateTime value)
   {
      set(TaskField.SCHEDULED_FINISH, value);
   }

   /**
    * Retrieve the scheduled duration.
    *
    * @return scheduled duration value
    */
   public Duration getScheduledDuration()
   {
      return (Duration) get(TaskField.SCHEDULED_DURATION);
   }

   /**
    * Set the scheduled duration.
    *
    * @param value scheduled duration value
    */
   public void setScheduledDuration(Duration value)
   {
      set(TaskField.SCHEDULED_DURATION, value);
   }

   /**
    * Retrieve the budget cost.
    *
    * @return budget cost value
    */
   public Number getBudgetCost()
   {
      return (Number) get(TaskField.BUDGET_COST);
   }

   /**
    * Set the budget cost.
    *
    * @param value budget cost value
    */
   public void setBudgetCost(Number value)
   {
      set(TaskField.BUDGET_COST, value);
   }

   /**
    * Retrieve the budget work.
    *
    * @return budget work value
    */
   public Duration getBudgetWork()
   {
      return (Duration) get(TaskField.BUDGET_WORK);
   }

   /**
    * Set the budget work.
    *
    * @param value budget work value
    */
   public void setBudgetWork(Duration value)
   {
      set(TaskField.BUDGET_WORK, value);
   }

   /**
    * Retrieve the baseline budget cost.
    *
    * @return baseline budget cost value
    */
   public Number getBaselineBudgetCost()
   {
      return (Number) get(TaskField.BASELINE_BUDGET_COST);
   }

   /**
    * Set the baseline budget cost.
    *
    * @param value baseline budget cost value
    */
   public void setBaselineBudgetCost(Number value)
   {
      set(TaskField.BASELINE_BUDGET_COST, value);
   }

   /**
    * Retrieve the baseline budget work.
    *
    * @return baseline budget work value
    */
   public Duration getBaselineBudgetWork()
   {
      return (Duration) get(TaskField.BASELINE_BUDGET_WORK);
   }

   /**
    * Set the baseline budget work.
    *
    * @param value baseline budget work value
    */
   public void setBaselineBudgetWork(Duration value)
   {
      set(TaskField.BASELINE_BUDGET_WORK, value);
   }

   /**
    * Retrieve a baseline budget cost.
    *
    * @param baselineNumber baseline number
    * @return baseline budget cost
    */
   public Number getBaselineBudgetCost(int baselineNumber)
   {
      return (Number) get(selectField(TaskFieldLists.BASELINE_BUDGET_COSTS, baselineNumber));
   }

   /**
    * Set a baseline budget cost.
    *
    * @param baselineNumber baseline number
    * @param value baseline budget cost value
    */
   public void setBaselineBudgetCost(int baselineNumber, Number value)
   {
      set(selectField(TaskFieldLists.BASELINE_BUDGET_COSTS, baselineNumber), value);
   }

   /**
    * Retrieve a baseline budget work.
    *
    * @param baselineNumber baseline number
    * @return baseline budget work value
    */
   public Duration getBaselineBudgetWork(int baselineNumber)
   {
      return (Duration) get(selectField(TaskFieldLists.BASELINE_BUDGET_WORKS, baselineNumber));
   }

   /**
    * Set a baseline budget work.
    *
    * @param baselineNumber baseline number
    * @param value baseline budget work value
    */
   public void setBaselineBudgetWork(int baselineNumber, Duration value)
   {
      set(selectField(TaskFieldLists.BASELINE_BUDGET_WORKS, baselineNumber), value);
   }

   /**
    * Set this task's sequence number.
    *
    * @param sequenceNumber task sequence number
    */
   public void setSequenceNumber(Integer sequenceNumber)
   {
      set(TaskField.SEQUENCE_NUMBER, sequenceNumber);
   }

   /**
    * Retrieve this task's sequence number.
    *
    * @return task sequence number
    */
   public Integer getSequenceNumber()
   {
      return (Integer) get(TaskField.SEQUENCE_NUMBER);
   }

   /**
    * Retrieve steps for this task.
    *
    * @return list of steps
    */
   @SuppressWarnings("unchecked") public List<Step> getSteps()
   {
      return (List<Step>) get(TaskField.STEPS);
   }

   /**
    * Set the steps for this task.
    *
    * @param steps list of steps
    */
   public void setSteps(List<Step> steps)
   {
      set(TaskField.STEPS, steps);
   }

   /**
    * Retrieves the location unique ID.
    *
    * @return location unique ID
    */
   public Integer getLocationUniqueID()
   {
      return (Integer) get(TaskField.LOCATION_UNIQUE_ID);
   }

   /**
    * Sets the location unique ID.
    *
    * @param uniqueID location unique ID
    */
   public void setLocationUniqueID(Integer uniqueID)
   {
      set(TaskField.LOCATION_UNIQUE_ID, uniqueID);
   }

   /**
    * Retrieves the location.
    *
    * @return location.
    */
   public Location getLocation()
   {
      return getParentFile().getLocations().getByUniqueID(getLocationUniqueID());
   }

   /**
    * Sets the location.
    *
    * @param location location
    */
   public void setLocation(Location location)
   {
      setLocationUniqueID(location == null ? null : location.getUniqueID());
   }

   /**
    * Retrieve the name of the Asta Powerproject bar to which this task belongs.
    *
    * @return bar name
    */
   public String getBarName()
   {
      return (String) get(TaskField.BAR_NAME);
   }

   /**
    * Set the name of the Asta Powerproject bar to which this task belongs.
    *
    * @param value bar name
    */
   public void setBarName(String value)
   {
      set(TaskField.BAR_NAME, value);
   }

   /**
    * Retrieve the expected finish date.
    *
    * @return expected finish date
    */
   public LocalDateTime getExpectedFinish()
   {
      return (LocalDateTime) get(TaskField.EXPECTED_FINISH);
   }

   /**
    * Set the expected finish date.
    *
    * @param value expected finish date
    */
   public void setExpectedFinish(LocalDateTime value)
   {
      set(TaskField.EXPECTED_FINISH, value);
   }

   /**
    * Set the labor component of the task's Actual Work.
    *
    * @param value work value
    */
   public void setActualWorkLabor(Duration value)
   {
      set(TaskField.ACTUAL_WORK_LABOR, value);
   }

   /**
    * Retrieve the labor component of the task's Actual Work.
    *
    * @return work value
    */
   public Duration getActualWorkLabor()
   {
      return (Duration) get(TaskField.ACTUAL_WORK_LABOR);
   }

   /**
    * Set the nonlabor component of the task's Actual Work.
    *
    * @param value work value
    */
   public void setActualWorkNonlabor(Duration value)
   {
      set(TaskField.ACTUAL_WORK_NONLABOR, value);
   }

   /**
    * Retrieve the nonlabor component of the task's Actual Work.
    *
    * @return work value
    */
   public Duration getActualWorkNonlabor()
   {
      return (Duration) get(TaskField.ACTUAL_WORK_NONLABOR);
   }

   /**
    * Set the labor component of the task's Planned Work.
    *
    * @param value work value
    */
   public void setPlannedWorkLabor(Duration value)
   {
      set(TaskField.PLANNED_WORK_LABOR, value);
   }

   /**
    * Retrieve the labor component of the task's Planned Work.
    *
    * @return work value
    */
   public Duration getPlannedWorkLabor()
   {
      return (Duration) get(TaskField.PLANNED_WORK_LABOR);
   }

   /**
    * Set the nonlabor component of the task's Planned Work.
    *
    * @param value work value
    */
   public void setPlannedWorkNonlabor(Duration value)
   {
      set(TaskField.PLANNED_WORK_NONLABOR, value);
   }

   /**
    * Retrieve the nonlabor component of the task's Planned Work.
    *
    * @return work value
    */
   public Duration getPlannedWorkNonlabor()
   {
      return (Duration) get(TaskField.PLANNED_WORK_NONLABOR);
   }

   /**
    * Set the labor component of the task's Remaining Work.
    *
    * @param value work value
    */
   public void setRemainingWorkLabor(Duration value)
   {
      set(TaskField.REMAINING_WORK_LABOR, value);
   }

   /**
    * Retrieve the labor component of the task's Remaining Work.
    *
    * @return work value
    */
   public Duration getRemainingWorkLabor()
   {
      return (Duration) get(TaskField.REMAINING_WORK_LABOR);
   }

   /**
    * Set the nonlabor component of the task's Remaining Work.
    *
    * @param value work value
    */
   public void setRemainingWorkNonlabor(Duration value)
   {
      set(TaskField.REMAINING_WORK_NONLABOR, value);
   }

   /**
    * Retrieve the nonlabor component of the task's Remaining Work.
    *
    * @return work value
    */
   public Duration getRemainingWorkNonlabor()
   {
      return (Duration) get(TaskField.REMAINING_WORK_NONLABOR);
   }

   /**
    * Returns true for manually scheduled tasks if the Start Text attribute should be
    * displayed to the user rather than the Start attribute.
    *
    * @return true if Start Text should be displayed
    */
   public boolean getShowStartText()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.SHOW_START_TEXT)));
   }

   /**
    * Returns true for manually scheduled tasks if the Finish Text attribute should be
    * displayed to the user rather than the Finish attribute.
    *
    * @return true if Finish Text should be displayed
    */
   public boolean getShowFinishText()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.SHOW_FINISH_TEXT)));
   }

   /**
    * Returns true for manually scheduled tasks if the Duration Text attribute should be
    * displayed to the user rather than the Duration attribute.
    *
    * @return true if Duration Text should be displayed
    */
   public boolean getShowDurationText()
   {
      return (BooleanHelper.getBoolean((Boolean) get(TaskField.SHOW_DURATION_TEXT)));
   }

   /**
    * This accessor method returns the percent complete value for this task
    * as defined by the Percent Complete Type attribute.
    *
    * @return activity percent complete
    */
   public Number getActivityPercentComplete()
   {
      return (Number) get(TaskField.ACTIVITY_PERCENT_COMPLETE);
   }

   /**
    * Retrieve the methodology GUID for this task.
    *
    * @return methodology GUID
    */
   public UUID getMethodologyGUID()
   {
      return (UUID) get(TaskField.METHODOLOGY_GUID);
   }

   /**
    * Set the methodology GUID for this task.
    *
    * @param value methodology GUID
    */
   public void setMethodologyGUID(UUID value)
   {
      set(TaskField.METHODOLOGY_GUID, value);
   }

   /**
    * Retrieve the float path number.
    *
    * @return float path number
    */
   public Integer getFloatPath()
   {
      return (Integer) get(TaskField.FLOAT_PATH);
   }

   /**
    * Set the float path number.
    *
    * @param value float path number
    */
   public void setFloatPath(Integer value)
   {
      set(TaskField.FLOAT_PATH, value);
   }

   /**
    * Retrieve the float path order.
    *
    * @return float path order
    */
   public Integer getFloatPathOrder()
   {
      return (Integer) get(TaskField.FLOAT_PATH_ORDER);
   }

   /**
    * Set the float path order.
    *
    * @param value float path order
    */
   public void setFloatPathOrder(Integer value)
   {
      set(TaskField.FLOAT_PATH_ORDER, value);
   }

   /**
    * Retrieve the effective calendar for this task. If the task does not have
    * a specific calendar associated with it, fall back to using the default calendar
    * for the project.
    *
    * @return ProjectCalendar instance
    */
   public ProjectCalendar getEffectiveCalendar()
   {
      ProjectCalendar result = getCalendar();
      if (result == null)
      {
         result = getParentFile().getDefaultCalendar();
      }
      return result;
   }

   /**
    * If the parent ProjectFile has one or more baseline ProjectFile instances,
    * this method will allow you to retrieve the baseline task associated
    * with this current task. If no baseline task is present this method will return null.
    *
    * @return baseline task or null
    */
   public Task getBaselineTask()
   {
      return getBaselineTask(0);
   }

   /**
    * If the parent ProjectFile has one or more baseline ProjectFile instances,
    * this method will allow you to retrieve the baseline task associated
    * with this current task. If no baseline task is present this method will return null.
    *
    * @param index baseline index
    * @return baseline task or null
    */
   public Task getBaselineTask(int index)
   {
      return getParentFile().getBaselineTaskMap(index).get(this);
   }

   /**
    * Maps a field index to a TaskField instance.
    *
    * @param fields array of fields used as the basis for the mapping.
    * @param index required field index
    * @return TaskField instance
    */
   private TaskField selectField(TaskField[] fields, int index)
   {
      if (index < 1 || index > fields.length)
      {
         throw new IllegalArgumentException(index + " is not a valid field index");
      }
      return (fields[index - 1]);
   }

   /**
    * Clear any cached calculated values which will be affected by this change.
    *
    * @param field modified field
    */
   @Override void handleFieldChange(FieldType field, Object oldValue, Object newValue)
   {
      if (field == TaskField.UNIQUE_ID)
      {
         getParentFile().getTasks().updateUniqueID(this, (Integer) oldValue, (Integer) newValue);
         return;
      }

      clearDependentFields(DEPENDENCY_MAP, field);
   }

   @Override boolean getAlwaysCalculatedField(FieldType field)
   {
      return ALWAYS_CALCULATED_FIELDS.contains(field);
   }

   @Override Function<Task, Object> getCalculationMethod(FieldType field)
   {
      return CALCULATED_FIELD_MAP.get(field);
   }

   /**
    * This method inserts a name value pair into internal storage.
    *
    * @param field task field
    * @param value attribute value
    */
   private void set(FieldType field, boolean value)
   {
      set(field, (value ? Boolean.TRUE : Boolean.FALSE));
   }

   @Override public String toString()
   {
      return ("[Task id=" + getID() + " uniqueID=" + getUniqueID() + (getActivityID() == null ? "" : " activityID=" + getActivityID()) + " name=" + getName() + (getExternalTask() ? " [EXTERNAL uid=" + getSubprojectTaskUniqueID() + " id=" + getSubprojectTaskID() + "]" : "]"));
   }

   /**
    * Utility method used to determine if the supplied task
    * is a predecessor of the current task.
    *
    * @param task potential predecessor task
    * @return Boolean flag
    */
   public boolean isPredecessor(Task task)
   {
      return task != null && getPredecessors().stream().anyMatch(p -> p.getPredecessorTask().getUniqueID().intValue() == task.getUniqueID().intValue());
   }

   /**
    * Utility method used to determine if the supplied task
    * is a successor of the current task.
    *
    * @param task potential successor task
    * @return Boolean flag
    */
   public boolean isSuccessor(Task task)
   {
      return task != null && task.isPredecessor(this);
   }

   /**
    * Used to determine if a task has child tasks.
    *
    * @return true if the task has child tasks
    */
   public boolean hasChildTasks()
   {
      return !m_children.isEmpty();
   }

   private Integer calculateParentTaskUniqueID()
   {
      return m_parent == null ? null : m_parent.getUniqueID();
   }

   private Duration calculateStartVariance()
   {
      TimeUnit format = getParentFile().getProjectProperties().getDefaultDurationUnits();
      return LocalDateTimeHelper.getVariance(getEffectiveCalendar(), getBaselineStart(), getStart(), format);
   }

   private Duration calculateFinishVariance()
   {
      TimeUnit format = getParentFile().getProjectProperties().getDefaultDurationUnits();
      return LocalDateTimeHelper.getVariance(getEffectiveCalendar(), getBaselineFinish(), getFinish(), format);
   }

   private Duration calculateStartSlack()
   {
      Duration duration = getDuration();
      LocalDateTime lateStart = getLateStart();
      LocalDateTime earlyStart = getEarlyStart();

      if (duration == null || lateStart == null || earlyStart == null)
      {
         return null;
      }

      return LocalDateTimeHelper.getVariance(getEffectiveCalendar(), earlyStart, lateStart, duration.getUnits());
   }

   private Duration calculateFinishSlack()
   {
      Duration duration = getDuration();
      LocalDateTime earlyFinish = getEarlyFinish();
      LocalDateTime lateFinish = getLateFinish();

      if (duration == null || earlyFinish == null || lateFinish == null)
      {
         return null;
      }

      return LocalDateTimeHelper.getVariance(getEffectiveCalendar(), earlyFinish, lateFinish, duration.getUnits());
   }

   private Double calculateCostVariance()
   {
      Number cost = getCost();
      Number baselineCost = getBaselineCost();
      if (cost == null || baselineCost == null)
      {
         return null;
      }

      return NumberHelper.getDouble(cost.doubleValue() - baselineCost.doubleValue());
   }

   private Duration calculateDurationVariance()
   {
      Duration duration = getDuration();
      Duration baselineDuration = getBaselineDuration();
      if (duration == null || baselineDuration == null)
      {
         return null;
      }

      return Duration.getInstance(duration.getDuration() - baselineDuration.convertUnits(duration.getUnits(), getParentFile().getProjectProperties()).getDuration(), duration.getUnits());
   }

   private Duration calculateWorkVariance()
   {
      Duration work = getWork();
      Duration baselineWork = getBaselineWork();
      if (work == null || baselineWork == null)
      {
         return null;
      }

      return Duration.getInstance(work.getDuration() - baselineWork.convertUnits(work.getUnits(), getParentFile().getProjectProperties()).getDuration(), work.getUnits());
   }

   private Double calculateCV()
   {
      return Double.valueOf(NumberHelper.getDouble(getBCWP()) - NumberHelper.getDouble(getACWP()));
   }

   private Double calculateSV()
   {
      Number bcwp = getBCWP();
      Number bcws = getBCWS();
      if (bcwp == null || bcws == null)
      {
         return null;
      }

      return NumberHelper.getDouble(bcwp.doubleValue() - bcws.doubleValue());
   }

   private Duration calculateTotalSlack()
   {
      // Calculate these first to avoid clearing our total slack value
      Duration duration = getDuration();
      Duration startSlack = getStartSlack();
      Duration finishSlack = getFinishSlack();

      TotalSlackCalculationType calculationType = getParentFile().getProjectProperties().getTotalSlackCalculationType();

      if (calculationType == TotalSlackCalculationType.START_SLACK)
      {
         return startSlack;
      }

      if (calculationType == TotalSlackCalculationType.FINISH_SLACK)
      {
         return finishSlack;
      }

      if (getActualStart() != null)
      {
         return finishSlack;
      }

      if (duration == null)
      {
         return null;
      }

      if (startSlack == null)
      {
         return null;
      }

      if (finishSlack == null)
      {
         return null;
      }

      TimeUnit units = duration.getUnits();
      if (startSlack.getUnits() != units)
      {
         startSlack = startSlack.convertUnits(units, getParentFile().getProjectProperties());
      }

      if (finishSlack.getUnits() != units)
      {
         finishSlack = finishSlack.convertUnits(units, getParentFile().getProjectProperties());
      }

      Duration totalSlack;
      double startSlackDuration = startSlack.getDuration();
      double finishSlackDuration = finishSlack.getDuration();

      if (startSlackDuration < finishSlackDuration)
      {
         totalSlack = startSlack;
      }
      else
      {
         totalSlack = finishSlack;
      }

      return totalSlack;
   }

   private Boolean calculateCritical()
   {
      if (getActualFinish() != null)
      {
         return Boolean.FALSE;
      }

      Duration totalSlack = getTotalSlack();
      if (totalSlack == null)
      {
         return Boolean.FALSE;
      }

      Duration criticalSlackLimit = getParentFile().getProjectProperties().getCriticalSlackLimit();
      if (criticalSlackLimit.getDuration() != 0 && totalSlack.getDuration() != 0 && totalSlack.getUnits() != criticalSlackLimit.getUnits())
      {
         totalSlack = totalSlack.convertUnits(criticalSlackLimit.getUnits(), getEffectiveCalendar());
      }
      return Boolean.valueOf(totalSlack.getDuration() <= criticalSlackLimit.getDuration() && NumberHelper.getInt(getPercentageComplete()) != 100 && ((getTaskMode() == TaskMode.AUTO_SCHEDULED) || (getDurationText() == null && getStartText() == null && getFinishText() == null)));
   }

   private LocalDateTime calculateCompleteThrough()
   {
      LocalDateTime value = null;
      int percentComplete = NumberHelper.getInt(getPercentageComplete());
      switch (percentComplete)
      {
         case 0:
         {
            break;
         }

         case 100:
         {
            value = getActualFinish();
            break;
         }

         default:
         {
            LocalDateTime actualStart = getActualStart();
            Duration duration = getDuration();
            if (actualStart != null && duration != null)
            {
               double durationValue = (duration.getDuration() * percentComplete) / 100d;
               duration = Duration.getInstance(durationValue, duration.getUnits());
               ProjectCalendar calendar = getEffectiveCalendar();
               value = calendar.getDate(actualStart, duration);
               if (getParentFile().getProjectConfig().getCompleteThroughIsNextWorkStart())
               {
                  value = calendar.getNextWorkStart(value);
               }
            }
            break;
         }
      }

      return value;
   }

   private Boolean calculateExternalProject()
   {
      return Boolean.valueOf(getSubprojectFile() != null && !getExternalTask());
   }

   private List<Relation> calculatePredecessors()
   {
      return getParentFile().getRelations().getPredecessors(this);
   }

   private List<Relation> calculateSuccessors()
   {
      return getParentFile().getRelations().getSuccessors(this);
   }

   private Number calculateActivityPercentComplete()
   {
      PercentCompleteType type = getPercentCompleteType();
      if (type == null)
      {
         return getPercentageComplete();
      }

      switch (type)
      {
         case UNITS:
         {
            return getPercentageWorkComplete();
         }

         case PHYSICAL:
         {
            return getPhysicalPercentComplete();
         }

         default:
         {
            return getPercentageComplete();
         }
      }
   }

   /**
    * Supply a default value for constraint type.
    *
    * @return constraint type default value
    */
   private ConstraintType defaultConstraintType()
   {
      return ConstraintType.AS_SOON_AS_POSSIBLE;
   }

   /**
    * Supply a default value for the active flag.
    *
    * @return active flag default value
    */
   private Boolean defaultActive()
   {
      return Boolean.TRUE;
   }

   /**
    * Supply a default value for the task type.
    *
    * @return task type default value
    */
   private TaskType defaultType()
   {
      return TaskType.FIXED_UNITS;
   }

   /**
    * Supply a default value for the task mode.
    *
    * @return task mode default value
    */
   private TaskMode defaultTaskMode()
   {
      return TaskMode.AUTO_SCHEDULED;
   }

   private Map<ActivityCode, ActivityCodeValue> defaultActivityCodeValues()
   {
      return new HashMap<>();
   }

   private List<ExpenseItem> defaultExpenseItems()
   {
      return new ArrayList<>();
   }

   private List<Step> defaultSteps()
   {
      return new ArrayList<>();
   }

   private Boolean defaultExpanded()
   {
      return Boolean.TRUE;
   }

   /**
    * This is a reference to the parent task, as specified by the
    * outline level.
    */
   private Task m_parent;

   /**
    * This list holds references to all tasks that are children of the
    * current task as specified by the outline level.
    */
   private final List<Task> m_children = new ArrayList<>();

   /**
    * List of resource assignments for this task.
    */
   private final List<ResourceAssignment> m_assignments = new ArrayList<>();

   /**
    * Recurring task details associated with this task.
    */
   private RecurringTask m_recurringTask;

   private static final Set<FieldType> ALWAYS_CALCULATED_FIELDS = new HashSet<>(Arrays.asList(TaskField.PARENT_TASK_UNIQUE_ID, TaskField.PREDECESSORS, TaskField.SUCCESSORS));

   private static final Map<FieldType, Function<Task, Object>> CALCULATED_FIELD_MAP = new HashMap<>();
   static
   {
      CALCULATED_FIELD_MAP.put(TaskField.PARENT_TASK_UNIQUE_ID, Task::calculateParentTaskUniqueID);
      CALCULATED_FIELD_MAP.put(TaskField.START_VARIANCE, Task::calculateStartVariance);
      CALCULATED_FIELD_MAP.put(TaskField.FINISH_VARIANCE, Task::calculateFinishVariance);
      CALCULATED_FIELD_MAP.put(TaskField.START_SLACK, Task::calculateStartSlack);
      CALCULATED_FIELD_MAP.put(TaskField.FINISH_SLACK, Task::calculateFinishSlack);
      CALCULATED_FIELD_MAP.put(TaskField.COST_VARIANCE, Task::calculateCostVariance);
      CALCULATED_FIELD_MAP.put(TaskField.DURATION_VARIANCE, Task::calculateDurationVariance);
      CALCULATED_FIELD_MAP.put(TaskField.WORK_VARIANCE, Task::calculateWorkVariance);
      CALCULATED_FIELD_MAP.put(TaskField.CV, Task::calculateCV);
      CALCULATED_FIELD_MAP.put(TaskField.SV, Task::calculateSV);
      CALCULATED_FIELD_MAP.put(TaskField.TOTAL_SLACK, Task::calculateTotalSlack);
      CALCULATED_FIELD_MAP.put(TaskField.CRITICAL, Task::calculateCritical);
      CALCULATED_FIELD_MAP.put(TaskField.COMPLETE_THROUGH, Task::calculateCompleteThrough);
      CALCULATED_FIELD_MAP.put(TaskField.EXTERNAL_PROJECT, Task::calculateExternalProject);
      CALCULATED_FIELD_MAP.put(TaskField.PREDECESSORS, Task::calculatePredecessors);
      CALCULATED_FIELD_MAP.put(TaskField.SUCCESSORS, Task::calculateSuccessors);
      CALCULATED_FIELD_MAP.put(TaskField.ACTIVITY_PERCENT_COMPLETE, Task::calculateActivityPercentComplete);
      CALCULATED_FIELD_MAP.put(TaskField.CONSTRAINT_TYPE, Task::defaultConstraintType);
      CALCULATED_FIELD_MAP.put(TaskField.ACTIVE, Task::defaultActive);
      CALCULATED_FIELD_MAP.put(TaskField.TYPE, Task::defaultType);
      CALCULATED_FIELD_MAP.put(TaskField.TASK_MODE, Task::defaultTaskMode);
      CALCULATED_FIELD_MAP.put(TaskField.ACTIVITY_CODE_VALUES, Task::defaultActivityCodeValues);
      CALCULATED_FIELD_MAP.put(TaskField.EXPENSE_ITEMS, Task::defaultExpenseItems);
      CALCULATED_FIELD_MAP.put(TaskField.STEPS, Task::defaultSteps);
      CALCULATED_FIELD_MAP.put(TaskField.EXPANDED, Task::defaultExpanded);
   }

   private static final Map<FieldType, List<FieldType>> DEPENDENCY_MAP = new HashMap<>();
   static
   {
      FieldContainerDependencies<FieldType> dependencies = new FieldContainerDependencies<>(DEPENDENCY_MAP);

      dependencies.calculatedField(TaskField.START_VARIANCE).dependsOn(TaskField.START, TaskField.BASELINE_START);
      dependencies.calculatedField(TaskField.FINISH_VARIANCE).dependsOn(TaskField.FINISH, TaskField.BASELINE_FINISH);
      dependencies.calculatedField(TaskField.START_SLACK).dependsOn(TaskField.EARLY_START, TaskField.LATE_START);
      dependencies.calculatedField(TaskField.FINISH_SLACK).dependsOn(TaskField.EARLY_FINISH, TaskField.LATE_FINISH);
      dependencies.calculatedField(TaskField.COST_VARIANCE).dependsOn(TaskField.COST, TaskField.BASELINE_COST);
      dependencies.calculatedField(TaskField.DURATION_VARIANCE).dependsOn(TaskField.DURATION, TaskField.BASELINE_DURATION);
      dependencies.calculatedField(TaskField.WORK_VARIANCE).dependsOn(TaskField.WORK, TaskField.BASELINE_WORK);
      dependencies.calculatedField(TaskField.CV).dependsOn(TaskField.BCWP, TaskField.ACWP);
      dependencies.calculatedField(TaskField.SV).dependsOn(TaskField.BCWP, TaskField.BCWS);
      dependencies.calculatedField(TaskField.TOTAL_SLACK).dependsOn(TaskField.START_SLACK, TaskField.FINISH_SLACK, TaskField.ACTUAL_START);
      dependencies.calculatedField(TaskField.CRITICAL).dependsOn(TaskField.TOTAL_SLACK, TaskField.ACTUAL_FINISH);
      dependencies.calculatedField(TaskField.COMPLETE_THROUGH).dependsOn(TaskField.DURATION, TaskField.ACTUAL_START, TaskField.PERCENT_COMPLETE);
      dependencies.calculatedField(TaskField.EXTERNAL_PROJECT).dependsOn(TaskField.SUBPROJECT_FILE, TaskField.EXTERNAL_TASK);
      dependencies.calculatedField(TaskField.ACTIVITY_PERCENT_COMPLETE).dependsOn(TaskField.PERCENT_COMPLETE_TYPE, TaskField.PERCENT_COMPLETE, TaskField.PERCENT_WORK_COMPLETE, TaskField.PHYSICAL_PERCENT_COMPLETE);
   }
}
