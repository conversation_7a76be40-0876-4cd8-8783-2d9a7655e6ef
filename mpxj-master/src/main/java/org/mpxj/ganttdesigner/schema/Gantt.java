//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:50 AM BST
//

package org.mpxj.ganttdesigner.schema;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.XmlValue;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.mpxj.Duration;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="Display"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Height" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Split" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="File"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Saved" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                 &lt;attribute name="Created" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                 &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="Physical" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Globalization"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Culture"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="UICulture"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="Currency"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Currency" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *                 &lt;attribute name="RegionInfo" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="FirstDay"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LastDay"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Padding"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Left" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Top" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Right" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Bottom" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Tasks"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Task" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="S" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                           &lt;attribute name="B" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="BC" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="D" type="{}ganttDesignerDuration" /&gt;
 *                           &lt;attribute name="H" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="U" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="VA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="In" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="C" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="PC" type="{}ganttDesignerPercent" /&gt;
 *                           &lt;attribute name="DL" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                           &lt;attribute name="P" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="L" type="{}ganttDesignerDuration" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Remarks" type="{}ganttDesignerRemark"/&gt;
 *         &lt;element name="Remarks1" type="{}ganttDesignerRemark"/&gt;
 *         &lt;element name="Remarks2" type="{}ganttDesignerRemark"/&gt;
 *         &lt;element name="Remarks3" type="{}ganttDesignerRemark"/&gt;
 *         &lt;element name="Remarks4" type="{}ganttDesignerRemark"/&gt;
 *         &lt;element name="TextStyles"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Font" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *                 &lt;attribute name="Flag" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="Deadline" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="BarStyles"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Color2" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Color3" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Columns"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Header" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="W" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="A" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="D" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="DA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="DNW" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Calendar"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="WorkDays" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="WeekStart" type="{}ganttDesignerDay" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DateHeader"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Tier" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Tick" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="Reference"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                           &lt;attribute name="Day0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Week0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Month0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Year0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *                 &lt;attribute name="Tiers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="TierHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="DayWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="DarkGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="LightGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="ColorBackground" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="ColorGridlines" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="AltLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="Lightness" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Holidays"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Holiday" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Headers"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="HeaderLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="HeadersFonts"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Font" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Footers"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="FooterLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="FootersFonts"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Font" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Print"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="leftColumnsOnPage1Only" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                 &lt;attribute name="fitToPage" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfPages" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fitHorizontal" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="NumberSequence" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrintToImageFile"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Copy"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ChartColor"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="ChartBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="BorderBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="LeftPanelBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="LeftPanelGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="RightPanelHGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="RightPanelVGridDark" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="RightPanelVGridLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="HolidayShade" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="SelectedRowBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="SelectedRowFore" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@SuppressWarnings("all") @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
{
   "display",
   "file",
   "globalization",
   "firstDay",
   "lastDay",
   "padding",
   "tasks",
   "remarks",
   "remarks1",
   "remarks2",
   "remarks3",
   "remarks4",
   "textStyles",
   "barStyles",
   "columns",
   "calendar",
   "dateHeader",
   "holidays",
   "headers",
   "headersFonts",
   "footers",
   "footersFonts",
   "print",
   "printToImageFile",
   "copy",
   "chartColor"
}) @XmlRootElement(name = "Gantt") public class Gantt
{

   @XmlElement(name = "Display", required = true) protected Gantt.Display display;
   @XmlElement(name = "File", required = true) protected Gantt.File file;
   @XmlElement(name = "Globalization", required = true) protected Gantt.Globalization globalization;
   @XmlElement(name = "FirstDay", required = true) protected Gantt.FirstDay firstDay;
   @XmlElement(name = "LastDay", required = true) protected Gantt.LastDay lastDay;
   @XmlElement(name = "Padding", required = true) protected Gantt.Padding padding;
   @XmlElement(name = "Tasks", required = true) protected Gantt.Tasks tasks;
   @XmlElement(name = "Remarks", required = true) protected GanttDesignerRemark remarks;
   @XmlElement(name = "Remarks1", required = true) protected GanttDesignerRemark remarks1;
   @XmlElement(name = "Remarks2", required = true) protected GanttDesignerRemark remarks2;
   @XmlElement(name = "Remarks3", required = true) protected GanttDesignerRemark remarks3;
   @XmlElement(name = "Remarks4", required = true) protected GanttDesignerRemark remarks4;
   @XmlElement(name = "TextStyles", required = true) protected Gantt.TextStyles textStyles;
   @XmlElement(name = "BarStyles", required = true) protected Gantt.BarStyles barStyles;
   @XmlElement(name = "Columns", required = true) protected Gantt.Columns columns;
   @XmlElement(name = "Calendar", required = true) protected Gantt.Calendar calendar;
   @XmlElement(name = "DateHeader", required = true) protected Gantt.DateHeader dateHeader;
   @XmlElement(name = "Holidays", required = true) protected Gantt.Holidays holidays;
   @XmlElement(name = "Headers", required = true) protected Gantt.Headers headers;
   @XmlElement(name = "HeadersFonts", required = true) protected Gantt.HeadersFonts headersFonts;
   @XmlElement(name = "Footers", required = true) protected Gantt.Footers footers;
   @XmlElement(name = "FootersFonts", required = true) protected Gantt.FootersFonts footersFonts;
   @XmlElement(name = "Print", required = true) protected Gantt.Print print;
   @XmlElement(name = "PrintToImageFile", required = true) protected Gantt.PrintToImageFile printToImageFile;
   @XmlElement(name = "Copy", required = true) protected Gantt.Copy copy;
   @XmlElement(name = "ChartColor", required = true) protected Gantt.ChartColor chartColor;
   @XmlAttribute(name = "Version") protected String version;

   /**
    * Gets the value of the display property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Display }
    *
    */
   public Gantt.Display getDisplay()
   {
      return display;
   }

   /**
    * Sets the value of the display property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Display }
    *
    */
   public void setDisplay(Gantt.Display value)
   {
      this.display = value;
   }

   /**
    * Gets the value of the file property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.File }
    *
    */
   public Gantt.File getFile()
   {
      return file;
   }

   /**
    * Sets the value of the file property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.File }
    *
    */
   public void setFile(Gantt.File value)
   {
      this.file = value;
   }

   /**
    * Gets the value of the globalization property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Globalization }
    *
    */
   public Gantt.Globalization getGlobalization()
   {
      return globalization;
   }

   /**
    * Sets the value of the globalization property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Globalization }
    *
    */
   public void setGlobalization(Gantt.Globalization value)
   {
      this.globalization = value;
   }

   /**
    * Gets the value of the firstDay property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.FirstDay }
    *
    */
   public Gantt.FirstDay getFirstDay()
   {
      return firstDay;
   }

   /**
    * Sets the value of the firstDay property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.FirstDay }
    *
    */
   public void setFirstDay(Gantt.FirstDay value)
   {
      this.firstDay = value;
   }

   /**
    * Gets the value of the lastDay property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.LastDay }
    *
    */
   public Gantt.LastDay getLastDay()
   {
      return lastDay;
   }

   /**
    * Sets the value of the lastDay property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.LastDay }
    *
    */
   public void setLastDay(Gantt.LastDay value)
   {
      this.lastDay = value;
   }

   /**
    * Gets the value of the padding property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Padding }
    *
    */
   public Gantt.Padding getPadding()
   {
      return padding;
   }

   /**
    * Sets the value of the padding property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Padding }
    *
    */
   public void setPadding(Gantt.Padding value)
   {
      this.padding = value;
   }

   /**
    * Gets the value of the tasks property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Tasks }
    *
    */
   public Gantt.Tasks getTasks()
   {
      return tasks;
   }

   /**
    * Sets the value of the tasks property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Tasks }
    *
    */
   public void setTasks(Gantt.Tasks value)
   {
      this.tasks = value;
   }

   /**
    * Gets the value of the remarks property.
    *
    * @return
    *     possible object is
    *     {@link GanttDesignerRemark }
    *
    */
   public GanttDesignerRemark getRemarks()
   {
      return remarks;
   }

   /**
    * Sets the value of the remarks property.
    *
    * @param value
    *     allowed object is
    *     {@link GanttDesignerRemark }
    *
    */
   public void setRemarks(GanttDesignerRemark value)
   {
      this.remarks = value;
   }

   /**
    * Gets the value of the remarks1 property.
    *
    * @return
    *     possible object is
    *     {@link GanttDesignerRemark }
    *
    */
   public GanttDesignerRemark getRemarks1()
   {
      return remarks1;
   }

   /**
    * Sets the value of the remarks1 property.
    *
    * @param value
    *     allowed object is
    *     {@link GanttDesignerRemark }
    *
    */
   public void setRemarks1(GanttDesignerRemark value)
   {
      this.remarks1 = value;
   }

   /**
    * Gets the value of the remarks2 property.
    *
    * @return
    *     possible object is
    *     {@link GanttDesignerRemark }
    *
    */
   public GanttDesignerRemark getRemarks2()
   {
      return remarks2;
   }

   /**
    * Sets the value of the remarks2 property.
    *
    * @param value
    *     allowed object is
    *     {@link GanttDesignerRemark }
    *
    */
   public void setRemarks2(GanttDesignerRemark value)
   {
      this.remarks2 = value;
   }

   /**
    * Gets the value of the remarks3 property.
    *
    * @return
    *     possible object is
    *     {@link GanttDesignerRemark }
    *
    */
   public GanttDesignerRemark getRemarks3()
   {
      return remarks3;
   }

   /**
    * Sets the value of the remarks3 property.
    *
    * @param value
    *     allowed object is
    *     {@link GanttDesignerRemark }
    *
    */
   public void setRemarks3(GanttDesignerRemark value)
   {
      this.remarks3 = value;
   }

   /**
    * Gets the value of the remarks4 property.
    *
    * @return
    *     possible object is
    *     {@link GanttDesignerRemark }
    *
    */
   public GanttDesignerRemark getRemarks4()
   {
      return remarks4;
   }

   /**
    * Sets the value of the remarks4 property.
    *
    * @param value
    *     allowed object is
    *     {@link GanttDesignerRemark }
    *
    */
   public void setRemarks4(GanttDesignerRemark value)
   {
      this.remarks4 = value;
   }

   /**
    * Gets the value of the textStyles property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.TextStyles }
    *
    */
   public Gantt.TextStyles getTextStyles()
   {
      return textStyles;
   }

   /**
    * Sets the value of the textStyles property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.TextStyles }
    *
    */
   public void setTextStyles(Gantt.TextStyles value)
   {
      this.textStyles = value;
   }

   /**
    * Gets the value of the barStyles property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.BarStyles }
    *
    */
   public Gantt.BarStyles getBarStyles()
   {
      return barStyles;
   }

   /**
    * Sets the value of the barStyles property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.BarStyles }
    *
    */
   public void setBarStyles(Gantt.BarStyles value)
   {
      this.barStyles = value;
   }

   /**
    * Gets the value of the columns property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Columns }
    *
    */
   public Gantt.Columns getColumns()
   {
      return columns;
   }

   /**
    * Sets the value of the columns property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Columns }
    *
    */
   public void setColumns(Gantt.Columns value)
   {
      this.columns = value;
   }

   /**
    * Gets the value of the calendar property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Calendar }
    *
    */
   public Gantt.Calendar getCalendar()
   {
      return calendar;
   }

   /**
    * Sets the value of the calendar property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Calendar }
    *
    */
   public void setCalendar(Gantt.Calendar value)
   {
      this.calendar = value;
   }

   /**
    * Gets the value of the dateHeader property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.DateHeader }
    *
    */
   public Gantt.DateHeader getDateHeader()
   {
      return dateHeader;
   }

   /**
    * Sets the value of the dateHeader property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.DateHeader }
    *
    */
   public void setDateHeader(Gantt.DateHeader value)
   {
      this.dateHeader = value;
   }

   /**
    * Gets the value of the holidays property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Holidays }
    *
    */
   public Gantt.Holidays getHolidays()
   {
      return holidays;
   }

   /**
    * Sets the value of the holidays property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Holidays }
    *
    */
   public void setHolidays(Gantt.Holidays value)
   {
      this.holidays = value;
   }

   /**
    * Gets the value of the headers property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Headers }
    *
    */
   public Gantt.Headers getHeaders()
   {
      return headers;
   }

   /**
    * Sets the value of the headers property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Headers }
    *
    */
   public void setHeaders(Gantt.Headers value)
   {
      this.headers = value;
   }

   /**
    * Gets the value of the headersFonts property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.HeadersFonts }
    *
    */
   public Gantt.HeadersFonts getHeadersFonts()
   {
      return headersFonts;
   }

   /**
    * Sets the value of the headersFonts property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.HeadersFonts }
    *
    */
   public void setHeadersFonts(Gantt.HeadersFonts value)
   {
      this.headersFonts = value;
   }

   /**
    * Gets the value of the footers property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Footers }
    *
    */
   public Gantt.Footers getFooters()
   {
      return footers;
   }

   /**
    * Sets the value of the footers property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Footers }
    *
    */
   public void setFooters(Gantt.Footers value)
   {
      this.footers = value;
   }

   /**
    * Gets the value of the footersFonts property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.FootersFonts }
    *
    */
   public Gantt.FootersFonts getFootersFonts()
   {
      return footersFonts;
   }

   /**
    * Sets the value of the footersFonts property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.FootersFonts }
    *
    */
   public void setFootersFonts(Gantt.FootersFonts value)
   {
      this.footersFonts = value;
   }

   /**
    * Gets the value of the print property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Print }
    *
    */
   public Gantt.Print getPrint()
   {
      return print;
   }

   /**
    * Sets the value of the print property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Print }
    *
    */
   public void setPrint(Gantt.Print value)
   {
      this.print = value;
   }

   /**
    * Gets the value of the printToImageFile property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.PrintToImageFile }
    *
    */
   public Gantt.PrintToImageFile getPrintToImageFile()
   {
      return printToImageFile;
   }

   /**
    * Sets the value of the printToImageFile property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.PrintToImageFile }
    *
    */
   public void setPrintToImageFile(Gantt.PrintToImageFile value)
   {
      this.printToImageFile = value;
   }

   /**
    * Gets the value of the copy property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.Copy }
    *
    */
   public Gantt.Copy getCopy()
   {
      return copy;
   }

   /**
    * Sets the value of the copy property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.Copy }
    *
    */
   public void setCopy(Gantt.Copy value)
   {
      this.copy = value;
   }

   /**
    * Gets the value of the chartColor property.
    *
    * @return
    *     possible object is
    *     {@link Gantt.ChartColor }
    *
    */
   public Gantt.ChartColor getChartColor()
   {
      return chartColor;
   }

   /**
    * Sets the value of the chartColor property.
    *
    * @param value
    *     allowed object is
    *     {@link Gantt.ChartColor }
    *
    */
   public void setChartColor(Gantt.ChartColor value)
   {
      this.chartColor = value;
   }

   /**
    * Gets the value of the version property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getVersion()
   {
      return version;
   }

   /**
    * Sets the value of the version property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setVersion(String value)
   {
      this.version = value;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Color2" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Color3" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class BarStyles
   {

      @XmlAttribute(name = "Type") protected Integer type;
      @XmlAttribute(name = "Color") protected Integer color;
      @XmlAttribute(name = "Color2") protected Integer color2;
      @XmlAttribute(name = "Color3") protected Integer color3;

      /**
       * Gets the value of the type property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getType()
      {
         return type;
      }

      /**
       * Sets the value of the type property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setType(Integer value)
      {
         this.type = value;
      }

      /**
       * Gets the value of the color property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getColor()
      {
         return color;
      }

      /**
       * Sets the value of the color property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setColor(Integer value)
      {
         this.color = value;
      }

      /**
       * Gets the value of the color2 property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getColor2()
      {
         return color2;
      }

      /**
       * Sets the value of the color2 property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setColor2(Integer value)
      {
         this.color2 = value;
      }

      /**
       * Gets the value of the color3 property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getColor3()
      {
         return color3;
      }

      /**
       * Sets the value of the color3 property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setColor3(Integer value)
      {
         this.color3 = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="WorkDays" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="WeekStart" type="{}ganttDesignerDay" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Calendar
   {

      @XmlAttribute(name = "WorkDays") protected String workDays;
      @XmlAttribute(name = "WeekStart") @XmlJavaTypeAdapter(Adapter5.class) protected DayOfWeek weekStart;

      /**
       * Gets the value of the workDays property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getWorkDays()
      {
         return workDays;
      }

      /**
       * Sets the value of the workDays property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setWorkDays(String value)
      {
         this.workDays = value;
      }

      /**
       * Gets the value of the weekStart property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public DayOfWeek getWeekStart()
      {
         return weekStart;
      }

      /**
       * Sets the value of the weekStart property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setWeekStart(DayOfWeek value)
      {
         this.weekStart = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="ChartBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="BorderBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="LeftPanelBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="LeftPanelGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="RightPanelHGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="RightPanelVGridDark" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="RightPanelVGridLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="HolidayShade" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="SelectedRowBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="SelectedRowFore" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class ChartColor
   {

      @XmlAttribute(name = "ChartBack") protected Integer chartBack;
      @XmlAttribute(name = "BorderBack") protected Integer borderBack;
      @XmlAttribute(name = "LeftPanelBack") protected Integer leftPanelBack;
      @XmlAttribute(name = "LeftPanelGrid") protected Integer leftPanelGrid;
      @XmlAttribute(name = "RightPanelHGrid") protected Integer rightPanelHGrid;
      @XmlAttribute(name = "RightPanelVGridDark") protected Integer rightPanelVGridDark;
      @XmlAttribute(name = "RightPanelVGridLight") protected Integer rightPanelVGridLight;
      @XmlAttribute(name = "HolidayShade") protected Integer holidayShade;
      @XmlAttribute(name = "SelectedRowBack") protected Integer selectedRowBack;
      @XmlAttribute(name = "SelectedRowFore") protected Integer selectedRowFore;

      /**
       * Gets the value of the chartBack property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getChartBack()
      {
         return chartBack;
      }

      /**
       * Sets the value of the chartBack property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setChartBack(Integer value)
      {
         this.chartBack = value;
      }

      /**
       * Gets the value of the borderBack property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getBorderBack()
      {
         return borderBack;
      }

      /**
       * Sets the value of the borderBack property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setBorderBack(Integer value)
      {
         this.borderBack = value;
      }

      /**
       * Gets the value of the leftPanelBack property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getLeftPanelBack()
      {
         return leftPanelBack;
      }

      /**
       * Sets the value of the leftPanelBack property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setLeftPanelBack(Integer value)
      {
         this.leftPanelBack = value;
      }

      /**
       * Gets the value of the leftPanelGrid property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getLeftPanelGrid()
      {
         return leftPanelGrid;
      }

      /**
       * Sets the value of the leftPanelGrid property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setLeftPanelGrid(Integer value)
      {
         this.leftPanelGrid = value;
      }

      /**
       * Gets the value of the rightPanelHGrid property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getRightPanelHGrid()
      {
         return rightPanelHGrid;
      }

      /**
       * Sets the value of the rightPanelHGrid property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setRightPanelHGrid(Integer value)
      {
         this.rightPanelHGrid = value;
      }

      /**
       * Gets the value of the rightPanelVGridDark property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getRightPanelVGridDark()
      {
         return rightPanelVGridDark;
      }

      /**
       * Sets the value of the rightPanelVGridDark property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setRightPanelVGridDark(Integer value)
      {
         this.rightPanelVGridDark = value;
      }

      /**
       * Gets the value of the rightPanelVGridLight property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getRightPanelVGridLight()
      {
         return rightPanelVGridLight;
      }

      /**
       * Sets the value of the rightPanelVGridLight property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setRightPanelVGridLight(Integer value)
      {
         this.rightPanelVGridLight = value;
      }

      /**
       * Gets the value of the holidayShade property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getHolidayShade()
      {
         return holidayShade;
      }

      /**
       * Sets the value of the holidayShade property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setHolidayShade(Integer value)
      {
         this.holidayShade = value;
      }

      /**
       * Gets the value of the selectedRowBack property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getSelectedRowBack()
      {
         return selectedRowBack;
      }

      /**
       * Sets the value of the selectedRowBack property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setSelectedRowBack(Integer value)
      {
         this.selectedRowBack = value;
      }

      /**
       * Gets the value of the selectedRowFore property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getSelectedRowFore()
      {
         return selectedRowFore;
      }

      /**
       * Sets the value of the selectedRowFore property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setSelectedRowFore(Integer value)
      {
         this.selectedRowFore = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Header" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="W" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="A" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="D" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="DA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="DNW" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "header"
   }) public static class Columns
   {

      @XmlElement(name = "Header", required = true) protected List<Gantt.Columns.Header> header;

      /**
       * Gets the value of the header property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the header property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getHeader().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.Columns.Header }
       *
       *
       */
      public List<Gantt.Columns.Header> getHeader()
      {
         if (header == null)
         {
            header = new ArrayList<>();
         }
         return this.header;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="W" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="A" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="D" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="DA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="DNW" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Header
      {

         @XmlAttribute(name = "Type") protected Integer type;
         @XmlAttribute(name = "W") protected Integer w;
         @XmlAttribute(name = "A") protected Integer a;
         @XmlAttribute(name = "D") protected Integer d;
         @XmlAttribute(name = "DA") protected Integer da;
         @XmlAttribute(name = "DNW") protected String dnw;

         /**
          * Gets the value of the type property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getType()
         {
            return type;
         }

         /**
          * Sets the value of the type property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setType(Integer value)
         {
            this.type = value;
         }

         /**
          * Gets the value of the w property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getW()
         {
            return w;
         }

         /**
          * Sets the value of the w property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setW(Integer value)
         {
            this.w = value;
         }

         /**
          * Gets the value of the a property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getA()
         {
            return a;
         }

         /**
          * Sets the value of the a property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setA(Integer value)
         {
            this.a = value;
         }

         /**
          * Gets the value of the d property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getD()
         {
            return d;
         }

         /**
          * Sets the value of the d property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setD(Integer value)
         {
            this.d = value;
         }

         /**
          * Gets the value of the da property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getDA()
         {
            return da;
         }

         /**
          * Sets the value of the da property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setDA(Integer value)
         {
            this.da = value;
         }

         /**
          * Gets the value of the dnw property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDNW()
         {
            return dnw;
         }

         /**
          * Sets the value of the dnw property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDNW(String value)
         {
            this.dnw = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *       &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Copy
   {

      @XmlAttribute(name = "allRows") protected Integer allRows;
      @XmlAttribute(name = "fromTask") protected Integer fromTask;
      @XmlAttribute(name = "toTask") protected Integer toTask;
      @XmlAttribute(name = "numberOfLeftColumnsCompDisp") protected Integer numberOfLeftColumnsCompDisp;
      @XmlAttribute(name = "numberOfLeftColumns") protected Integer numberOfLeftColumns;
      @XmlAttribute(name = "showTaskNumbers") protected Integer showTaskNumbers;
      @XmlAttribute(name = "dateOnRightCompDisp") protected Integer dateOnRightCompDisp;
      @XmlAttribute(name = "fromDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate fromDate;
      @XmlAttribute(name = "toDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate toDate;

      /**
       * Gets the value of the allRows property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getAllRows()
      {
         return allRows;
      }

      /**
       * Sets the value of the allRows property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setAllRows(Integer value)
      {
         this.allRows = value;
      }

      /**
       * Gets the value of the fromTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFromTask()
      {
         return fromTask;
      }

      /**
       * Sets the value of the fromTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFromTask(Integer value)
      {
         this.fromTask = value;
      }

      /**
       * Gets the value of the toTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getToTask()
      {
         return toTask;
      }

      /**
       * Sets the value of the toTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setToTask(Integer value)
      {
         this.toTask = value;
      }

      /**
       * Gets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumnsCompDisp()
      {
         return numberOfLeftColumnsCompDisp;
      }

      /**
       * Sets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumnsCompDisp(Integer value)
      {
         this.numberOfLeftColumnsCompDisp = value;
      }

      /**
       * Gets the value of the numberOfLeftColumns property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumns()
      {
         return numberOfLeftColumns;
      }

      /**
       * Sets the value of the numberOfLeftColumns property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumns(Integer value)
      {
         this.numberOfLeftColumns = value;
      }

      /**
       * Gets the value of the showTaskNumbers property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getShowTaskNumbers()
      {
         return showTaskNumbers;
      }

      /**
       * Sets the value of the showTaskNumbers property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setShowTaskNumbers(Integer value)
      {
         this.showTaskNumbers = value;
      }

      /**
       * Gets the value of the dateOnRightCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getDateOnRightCompDisp()
      {
         return dateOnRightCompDisp;
      }

      /**
       * Sets the value of the dateOnRightCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setDateOnRightCompDisp(Integer value)
      {
         this.dateOnRightCompDisp = value;
      }

      /**
       * Gets the value of the fromDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getFromDate()
      {
         return fromDate;
      }

      /**
       * Sets the value of the fromDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFromDate(LocalDate value)
      {
         this.fromDate = value;
      }

      /**
       * Gets the value of the toDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getToDate()
      {
         return toDate;
      }

      /**
       * Sets the value of the toDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setToDate(LocalDate value)
      {
         this.toDate = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Tier" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Tick" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="Reference"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *                 &lt;attribute name="Day0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Week0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Month0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Year0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *       &lt;attribute name="Tiers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="TierHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="DayWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="DarkGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="LightGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="ColorBackground" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="ColorGridlines" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="AltLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Lightness" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "tier",
      "reference"
   }) public static class DateHeader
   {

      @XmlElement(name = "Tier", required = true) protected List<Gantt.DateHeader.Tier> tier;
      @XmlElement(name = "Reference", required = true) protected Gantt.DateHeader.Reference reference;
      @XmlAttribute(name = "Tiers") protected Integer tiers;
      @XmlAttribute(name = "TierHeight") protected Integer tierHeight;
      @XmlAttribute(name = "DayWidth") protected Integer dayWidth;
      @XmlAttribute(name = "DarkGrid") protected Integer darkGrid;
      @XmlAttribute(name = "LightGrid") protected Integer lightGrid;
      @XmlAttribute(name = "ColorBackground") protected Integer colorBackground;
      @XmlAttribute(name = "ColorGridlines") protected Integer colorGridlines;
      @XmlAttribute(name = "AltLight") protected Integer altLight;
      @XmlAttribute(name = "Lightness") protected Double lightness;

      /**
       * Gets the value of the tier property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the tier property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getTier().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.DateHeader.Tier }
       *
       *
       */
      public List<Gantt.DateHeader.Tier> getTier()
      {
         if (tier == null)
         {
            tier = new ArrayList<>();
         }
         return this.tier;
      }

      /**
       * Gets the value of the reference property.
       *
       * @return
       *     possible object is
       *     {@link Gantt.DateHeader.Reference }
       *
       */
      public Gantt.DateHeader.Reference getReference()
      {
         return reference;
      }

      /**
       * Sets the value of the reference property.
       *
       * @param value
       *     allowed object is
       *     {@link Gantt.DateHeader.Reference }
       *
       */
      public void setReference(Gantt.DateHeader.Reference value)
      {
         this.reference = value;
      }

      /**
       * Gets the value of the tiers property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getTiers()
      {
         return tiers;
      }

      /**
       * Sets the value of the tiers property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setTiers(Integer value)
      {
         this.tiers = value;
      }

      /**
       * Gets the value of the tierHeight property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getTierHeight()
      {
         return tierHeight;
      }

      /**
       * Sets the value of the tierHeight property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setTierHeight(Integer value)
      {
         this.tierHeight = value;
      }

      /**
       * Gets the value of the dayWidth property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getDayWidth()
      {
         return dayWidth;
      }

      /**
       * Sets the value of the dayWidth property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setDayWidth(Integer value)
      {
         this.dayWidth = value;
      }

      /**
       * Gets the value of the darkGrid property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getDarkGrid()
      {
         return darkGrid;
      }

      /**
       * Sets the value of the darkGrid property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setDarkGrid(Integer value)
      {
         this.darkGrid = value;
      }

      /**
       * Gets the value of the lightGrid property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getLightGrid()
      {
         return lightGrid;
      }

      /**
       * Sets the value of the lightGrid property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setLightGrid(Integer value)
      {
         this.lightGrid = value;
      }

      /**
       * Gets the value of the colorBackground property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getColorBackground()
      {
         return colorBackground;
      }

      /**
       * Sets the value of the colorBackground property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setColorBackground(Integer value)
      {
         this.colorBackground = value;
      }

      /**
       * Gets the value of the colorGridlines property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getColorGridlines()
      {
         return colorGridlines;
      }

      /**
       * Sets the value of the colorGridlines property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setColorGridlines(Integer value)
      {
         this.colorGridlines = value;
      }

      /**
       * Gets the value of the altLight property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getAltLight()
      {
         return altLight;
      }

      /**
       * Sets the value of the altLight property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setAltLight(Integer value)
      {
         this.altLight = value;
      }

      /**
       * Gets the value of the lightness property.
       *
       * @return
       *     possible object is
       *     {@link Double }
       *
       */
      public Double getLightness()
      {
         return lightness;
      }

      /**
       * Sets the value of the lightness property.
       *
       * @param value
       *     allowed object is
       *     {@link Double }
       *
       */
      public void setLightness(Double value)
      {
         this.lightness = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       *       &lt;attribute name="Day0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Week0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Month0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Year0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Reference
      {

         @XmlAttribute(name = "value") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate value;
         @XmlAttribute(name = "Day0") protected String day0;
         @XmlAttribute(name = "Week0") protected String week0;
         @XmlAttribute(name = "Month0") protected String month0;
         @XmlAttribute(name = "Year0") protected String year0;

         /**
          * Gets the value of the value property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDate getValue()
         {
            return value;
         }

         /**
          * Sets the value of the value property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setValue(LocalDate value)
         {
            this.value = value;
         }

         /**
          * Gets the value of the day0 property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDay0()
         {
            return day0;
         }

         /**
          * Sets the value of the day0 property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDay0(String value)
         {
            this.day0 = value;
         }

         /**
          * Gets the value of the week0 property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getWeek0()
         {
            return week0;
         }

         /**
          * Sets the value of the week0 property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setWeek0(String value)
         {
            this.week0 = value;
         }

         /**
          * Gets the value of the month0 property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMonth0()
         {
            return month0;
         }

         /**
          * Sets the value of the month0 property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMonth0(String value)
         {
            this.month0 = value;
         }

         /**
          * Gets the value of the year0 property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getYear0()
         {
            return year0;
         }

         /**
          * Sets the value of the year0 property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setYear0(String value)
         {
            this.year0 = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Tick" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Tier
      {

         @XmlAttribute(name = "Type") protected Integer type;
         @XmlAttribute(name = "Align") protected Integer align;
         @XmlAttribute(name = "Tick") protected String tick;

         /**
          * Gets the value of the type property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getType()
         {
            return type;
         }

         /**
          * Sets the value of the type property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setType(Integer value)
         {
            this.type = value;
         }

         /**
          * Gets the value of the align property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getAlign()
         {
            return align;
         }

         /**
          * Sets the value of the align property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setAlign(Integer value)
         {
            this.align = value;
         }

         /**
          * Gets the value of the tick property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getTick()
         {
            return tick;
         }

         /**
          * Sets the value of the tick property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setTick(String value)
         {
            this.tick = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Height" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Split" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Display
   {

      @XmlAttribute(name = "Width") protected Integer width;
      @XmlAttribute(name = "Height") protected Integer height;
      @XmlAttribute(name = "Split") protected Integer split;

      /**
       * Gets the value of the width property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getWidth()
      {
         return width;
      }

      /**
       * Sets the value of the width property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setWidth(Integer value)
      {
         this.width = value;
      }

      /**
       * Gets the value of the height property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getHeight()
      {
         return height;
      }

      /**
       * Sets the value of the height property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setHeight(Integer value)
      {
         this.height = value;
      }

      /**
       * Gets the value of the split property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getSplit()
      {
         return split;
      }

      /**
       * Sets the value of the split property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setSplit(Integer value)
      {
         this.split = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Saved" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *       &lt;attribute name="Created" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *       &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="Physical" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class File
   {

      @XmlAttribute(name = "Saved") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime saved;
      @XmlAttribute(name = "Created") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime created;
      @XmlAttribute(name = "Name") protected String name;
      @XmlAttribute(name = "Physical") protected String physical;

      /**
       * Gets the value of the saved property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getSaved()
      {
         return saved;
      }

      /**
       * Sets the value of the saved property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setSaved(LocalDateTime value)
      {
         this.saved = value;
      }

      /**
       * Gets the value of the created property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getCreated()
      {
         return created;
      }

      /**
       * Sets the value of the created property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCreated(LocalDateTime value)
      {
         this.created = value;
      }

      /**
       * Gets the value of the name property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getName()
      {
         return name;
      }

      /**
       * Sets the value of the name property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setName(String value)
      {
         this.name = value;
      }

      /**
       * Gets the value of the physical property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getPhysical()
      {
         return physical;
      }

      /**
       * Sets the value of the physical property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPhysical(String value)
      {
         this.physical = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class FirstDay
   {

      @XmlAttribute(name = "Date") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate date;

      /**
       * Gets the value of the date property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getDate()
      {
         return date;
      }

      /**
       * Sets the value of the date property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setDate(LocalDate value)
      {
         this.date = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="FooterLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Footers
   {

      @XmlAttribute(name = "FooterLeft") protected String footerLeft;
      @XmlAttribute(name = "FooterCenter") protected String footerCenter;
      @XmlAttribute(name = "FooterRight") protected String footerRight;
      @XmlAttribute(name = "FooterLineSpace") protected Integer footerLineSpace;

      /**
       * Gets the value of the footerLeft property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterLeft()
      {
         return footerLeft;
      }

      /**
       * Sets the value of the footerLeft property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterLeft(String value)
      {
         this.footerLeft = value;
      }

      /**
       * Gets the value of the footerCenter property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterCenter()
      {
         return footerCenter;
      }

      /**
       * Sets the value of the footerCenter property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterCenter(String value)
      {
         this.footerCenter = value;
      }

      /**
       * Gets the value of the footerRight property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterRight()
      {
         return footerRight;
      }

      /**
       * Sets the value of the footerRight property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterRight(String value)
      {
         this.footerRight = value;
      }

      /**
       * Gets the value of the footerLineSpace property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFooterLineSpace()
      {
         return footerLineSpace;
      }

      /**
       * Sets the value of the footerLineSpace property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFooterLineSpace(Integer value)
      {
         this.footerLineSpace = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Font" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "font"
   }) public static class FootersFonts
   {

      @XmlElement(name = "Font", required = true) protected List<Gantt.FootersFonts.Font> font;

      /**
       * Gets the value of the font property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the font property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getFont().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.FootersFonts.Font }
       *
       *
       */
      public List<Gantt.FootersFonts.Font> getFont()
      {
         if (font == null)
         {
            font = new ArrayList<>();
         }
         return this.font;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Font
      {

         @XmlAttribute(name = "Style") protected Integer style;
         @XmlAttribute(name = "Size") protected Integer size;
         @XmlAttribute(name = "StrikeOut") protected Integer strikeOut;
         @XmlAttribute(name = "Underline") protected Integer underline;

         /**
          * Gets the value of the style property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getStyle()
         {
            return style;
         }

         /**
          * Sets the value of the style property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setStyle(Integer value)
         {
            this.style = value;
         }

         /**
          * Gets the value of the size property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getSize()
         {
            return size;
         }

         /**
          * Sets the value of the size property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setSize(Integer value)
         {
            this.size = value;
         }

         /**
          * Gets the value of the strikeOut property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getStrikeOut()
         {
            return strikeOut;
         }

         /**
          * Sets the value of the strikeOut property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setStrikeOut(Integer value)
         {
            this.strikeOut = value;
         }

         /**
          * Gets the value of the underline property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getUnderline()
         {
            return underline;
         }

         /**
          * Sets the value of the underline property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setUnderline(Integer value)
         {
            this.underline = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Culture"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="UICulture"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="Currency"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Currency" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *       &lt;attribute name="RegionInfo" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "culture",
      "uiCulture",
      "currency"
   }) public static class Globalization
   {

      @XmlElement(name = "Culture", required = true) protected Gantt.Globalization.Culture culture;
      @XmlElement(name = "UICulture", required = true) protected Gantt.Globalization.UICulture uiCulture;
      @XmlElement(name = "Currency", required = true) protected Gantt.Globalization.Currency currency;
      @XmlAttribute(name = "RegionInfo") protected String regionInfo;

      /**
       * Gets the value of the culture property.
       *
       * @return
       *     possible object is
       *     {@link Gantt.Globalization.Culture }
       *
       */
      public Gantt.Globalization.Culture getCulture()
      {
         return culture;
      }

      /**
       * Sets the value of the culture property.
       *
       * @param value
       *     allowed object is
       *     {@link Gantt.Globalization.Culture }
       *
       */
      public void setCulture(Gantt.Globalization.Culture value)
      {
         this.culture = value;
      }

      /**
       * Gets the value of the uiCulture property.
       *
       * @return
       *     possible object is
       *     {@link Gantt.Globalization.UICulture }
       *
       */
      public Gantt.Globalization.UICulture getUICulture()
      {
         return uiCulture;
      }

      /**
       * Sets the value of the uiCulture property.
       *
       * @param value
       *     allowed object is
       *     {@link Gantt.Globalization.UICulture }
       *
       */
      public void setUICulture(Gantt.Globalization.UICulture value)
      {
         this.uiCulture = value;
      }

      /**
       * Gets the value of the currency property.
       *
       * @return
       *     possible object is
       *     {@link Gantt.Globalization.Currency }
       *
       */
      public Gantt.Globalization.Currency getCurrency()
      {
         return currency;
      }

      /**
       * Sets the value of the currency property.
       *
       * @param value
       *     allowed object is
       *     {@link Gantt.Globalization.Currency }
       *
       */
      public void setCurrency(Gantt.Globalization.Currency value)
      {
         this.currency = value;
      }

      /**
       * Gets the value of the regionInfo property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getRegionInfo()
      {
         return regionInfo;
      }

      /**
       * Sets the value of the regionInfo property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRegionInfo(String value)
      {
         this.regionInfo = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Culture
      {

         @XmlAttribute(name = "LCID") protected Integer lcid;
         @XmlAttribute(name = "ISO") protected String iso;
         @XmlAttribute(name = "DN") protected String dn;
         @XmlAttribute(name = "Cal") protected String cal;
         @XmlAttribute(name = "Parent") protected String parent;

         /**
          * Gets the value of the lcid property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getLCID()
         {
            return lcid;
         }

         /**
          * Sets the value of the lcid property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setLCID(Integer value)
         {
            this.lcid = value;
         }

         /**
          * Gets the value of the iso property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getISO()
         {
            return iso;
         }

         /**
          * Sets the value of the iso property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setISO(String value)
         {
            this.iso = value;
         }

         /**
          * Gets the value of the dn property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDN()
         {
            return dn;
         }

         /**
          * Sets the value of the dn property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDN(String value)
         {
            this.dn = value;
         }

         /**
          * Gets the value of the cal property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getCal()
         {
            return cal;
         }

         /**
          * Sets the value of the cal property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCal(String value)
         {
            this.cal = value;
         }

         /**
          * Gets the value of the parent property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getParent()
         {
            return parent;
         }

         /**
          * Sets the value of the parent property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setParent(String value)
         {
            this.parent = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Currency" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Currency
      {

         @XmlAttribute(name = "LCID") protected Integer lcid;
         @XmlAttribute(name = "ISO") protected String iso;
         @XmlAttribute(name = "DN") protected String dn;
         @XmlAttribute(name = "Currency") protected String currency;

         /**
          * Gets the value of the lcid property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getLCID()
         {
            return lcid;
         }

         /**
          * Sets the value of the lcid property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setLCID(Integer value)
         {
            this.lcid = value;
         }

         /**
          * Gets the value of the iso property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getISO()
         {
            return iso;
         }

         /**
          * Sets the value of the iso property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setISO(String value)
         {
            this.iso = value;
         }

         /**
          * Gets the value of the dn property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDN()
         {
            return dn;
         }

         /**
          * Sets the value of the dn property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDN(String value)
         {
            this.dn = value;
         }

         /**
          * Gets the value of the currency property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getCurrency()
         {
            return currency;
         }

         /**
          * Sets the value of the currency property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCurrency(String value)
         {
            this.currency = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class UICulture
      {

         @XmlAttribute(name = "LCID") protected Integer lcid;
         @XmlAttribute(name = "ISO") protected String iso;
         @XmlAttribute(name = "DN") protected String dn;
         @XmlAttribute(name = "Cal") protected String cal;
         @XmlAttribute(name = "Parent") protected String parent;

         /**
          * Gets the value of the lcid property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getLCID()
         {
            return lcid;
         }

         /**
          * Sets the value of the lcid property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setLCID(Integer value)
         {
            this.lcid = value;
         }

         /**
          * Gets the value of the iso property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getISO()
         {
            return iso;
         }

         /**
          * Sets the value of the iso property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setISO(String value)
         {
            this.iso = value;
         }

         /**
          * Gets the value of the dn property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDN()
         {
            return dn;
         }

         /**
          * Sets the value of the dn property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDN(String value)
         {
            this.dn = value;
         }

         /**
          * Gets the value of the cal property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getCal()
         {
            return cal;
         }

         /**
          * Sets the value of the cal property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCal(String value)
         {
            this.cal = value;
         }

         /**
          * Gets the value of the parent property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getParent()
         {
            return parent;
         }

         /**
          * Sets the value of the parent property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setParent(String value)
         {
            this.parent = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="HeaderLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Headers
   {

      @XmlAttribute(name = "HeaderLeft") protected String headerLeft;
      @XmlAttribute(name = "HeaderCenter") protected String headerCenter;
      @XmlAttribute(name = "HeaderRight") protected String headerRight;
      @XmlAttribute(name = "HeaderLineSpace") protected Integer headerLineSpace;

      /**
       * Gets the value of the headerLeft property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderLeft()
      {
         return headerLeft;
      }

      /**
       * Sets the value of the headerLeft property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderLeft(String value)
      {
         this.headerLeft = value;
      }

      /**
       * Gets the value of the headerCenter property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderCenter()
      {
         return headerCenter;
      }

      /**
       * Sets the value of the headerCenter property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderCenter(String value)
      {
         this.headerCenter = value;
      }

      /**
       * Gets the value of the headerRight property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderRight()
      {
         return headerRight;
      }

      /**
       * Sets the value of the headerRight property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderRight(String value)
      {
         this.headerRight = value;
      }

      /**
       * Gets the value of the headerLineSpace property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getHeaderLineSpace()
      {
         return headerLineSpace;
      }

      /**
       * Sets the value of the headerLineSpace property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setHeaderLineSpace(Integer value)
      {
         this.headerLineSpace = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Font" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "font"
   }) public static class HeadersFonts
   {

      @XmlElement(name = "Font", required = true) protected List<Gantt.HeadersFonts.Font> font;

      /**
       * Gets the value of the font property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the font property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getFont().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.HeadersFonts.Font }
       *
       *
       */
      public List<Gantt.HeadersFonts.Font> getFont()
      {
         if (font == null)
         {
            font = new ArrayList<>();
         }
         return this.font;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Font
      {

         @XmlAttribute(name = "Style") protected Integer style;
         @XmlAttribute(name = "Size") protected Integer size;
         @XmlAttribute(name = "StrikeOut") protected Integer strikeOut;
         @XmlAttribute(name = "Underline") protected Integer underline;

         /**
          * Gets the value of the style property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getStyle()
         {
            return style;
         }

         /**
          * Sets the value of the style property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setStyle(Integer value)
         {
            this.style = value;
         }

         /**
          * Gets the value of the size property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getSize()
         {
            return size;
         }

         /**
          * Sets the value of the size property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setSize(Integer value)
         {
            this.size = value;
         }

         /**
          * Gets the value of the strikeOut property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getStrikeOut()
         {
            return strikeOut;
         }

         /**
          * Sets the value of the strikeOut property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setStrikeOut(Integer value)
         {
            this.strikeOut = value;
         }

         /**
          * Gets the value of the underline property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getUnderline()
         {
            return underline;
         }

         /**
          * Sets the value of the underline property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setUnderline(Integer value)
         {
            this.underline = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Holiday" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "holiday"
   }) public static class Holidays
   {

      @XmlElement(name = "Holiday", required = true) protected List<Gantt.Holidays.Holiday> holiday;

      /**
       * Gets the value of the holiday property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the holiday property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getHoliday().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.Holidays.Holiday }
       *
       *
       */
      public List<Gantt.Holidays.Holiday> getHoliday()
      {
         if (holiday == null)
         {
            holiday = new ArrayList<>();
         }
         return this.holiday;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "content"
      }) public static class Holiday
      {

         @XmlValue protected String content;
         @XmlAttribute(name = "Date") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate date;

         /**
          * Gets the value of the content property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getContent()
         {
            return content;
         }

         /**
          * Sets the value of the content property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setContent(String value)
         {
            this.content = value;
         }

         /**
          * Gets the value of the date property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDate getDate()
         {
            return date;
         }

         /**
          * Sets the value of the date property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDate(LocalDate value)
         {
            this.date = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class LastDay
   {

      @XmlAttribute(name = "Date") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate date;

      /**
       * Gets the value of the date property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getDate()
      {
         return date;
      }

      /**
       * Sets the value of the date property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setDate(LocalDate value)
      {
         this.date = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Left" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Top" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Right" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="Bottom" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Padding
   {

      @XmlAttribute(name = "Left") protected Integer left;
      @XmlAttribute(name = "Top") protected Integer top;
      @XmlAttribute(name = "Right") protected Integer right;
      @XmlAttribute(name = "Bottom") protected Integer bottom;

      /**
       * Gets the value of the left property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getLeft()
      {
         return left;
      }

      /**
       * Sets the value of the left property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setLeft(Integer value)
      {
         this.left = value;
      }

      /**
       * Gets the value of the top property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getTop()
      {
         return top;
      }

      /**
       * Sets the value of the top property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setTop(Integer value)
      {
         this.top = value;
      }

      /**
       * Gets the value of the right property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getRight()
      {
         return right;
      }

      /**
       * Sets the value of the right property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setRight(Integer value)
      {
         this.right = value;
      }

      /**
       * Gets the value of the bottom property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getBottom()
      {
         return bottom;
      }

      /**
       * Sets the value of the bottom property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setBottom(Integer value)
      {
         this.bottom = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="leftColumnsOnPage1Only" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *       &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *       &lt;attribute name="fitToPage" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfPages" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fitHorizontal" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="NumberSequence" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Print
   {

      @XmlAttribute(name = "allRows") protected Integer allRows;
      @XmlAttribute(name = "fromTask") protected Integer fromTask;
      @XmlAttribute(name = "toTask") protected Integer toTask;
      @XmlAttribute(name = "numberOfLeftColumnsCompDisp") protected Integer numberOfLeftColumnsCompDisp;
      @XmlAttribute(name = "numberOfLeftColumns") protected Integer numberOfLeftColumns;
      @XmlAttribute(name = "showTaskNumbers") protected Integer showTaskNumbers;
      @XmlAttribute(name = "leftColumnsOnPage1Only") protected Integer leftColumnsOnPage1Only;
      @XmlAttribute(name = "dateOnRightCompDisp") protected Integer dateOnRightCompDisp;
      @XmlAttribute(name = "fromDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate fromDate;
      @XmlAttribute(name = "toDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate toDate;
      @XmlAttribute(name = "fitToPage") protected Integer fitToPage;
      @XmlAttribute(name = "numberOfPages") protected Integer numberOfPages;
      @XmlAttribute(name = "fitHorizontal") protected Integer fitHorizontal;
      @XmlAttribute(name = "NumberSequence") protected Integer numberSequence;

      /**
       * Gets the value of the allRows property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getAllRows()
      {
         return allRows;
      }

      /**
       * Sets the value of the allRows property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setAllRows(Integer value)
      {
         this.allRows = value;
      }

      /**
       * Gets the value of the fromTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFromTask()
      {
         return fromTask;
      }

      /**
       * Sets the value of the fromTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFromTask(Integer value)
      {
         this.fromTask = value;
      }

      /**
       * Gets the value of the toTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getToTask()
      {
         return toTask;
      }

      /**
       * Sets the value of the toTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setToTask(Integer value)
      {
         this.toTask = value;
      }

      /**
       * Gets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumnsCompDisp()
      {
         return numberOfLeftColumnsCompDisp;
      }

      /**
       * Sets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumnsCompDisp(Integer value)
      {
         this.numberOfLeftColumnsCompDisp = value;
      }

      /**
       * Gets the value of the numberOfLeftColumns property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumns()
      {
         return numberOfLeftColumns;
      }

      /**
       * Sets the value of the numberOfLeftColumns property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumns(Integer value)
      {
         this.numberOfLeftColumns = value;
      }

      /**
       * Gets the value of the showTaskNumbers property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getShowTaskNumbers()
      {
         return showTaskNumbers;
      }

      /**
       * Sets the value of the showTaskNumbers property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setShowTaskNumbers(Integer value)
      {
         this.showTaskNumbers = value;
      }

      /**
       * Gets the value of the leftColumnsOnPage1Only property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getLeftColumnsOnPage1Only()
      {
         return leftColumnsOnPage1Only;
      }

      /**
       * Sets the value of the leftColumnsOnPage1Only property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setLeftColumnsOnPage1Only(Integer value)
      {
         this.leftColumnsOnPage1Only = value;
      }

      /**
       * Gets the value of the dateOnRightCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getDateOnRightCompDisp()
      {
         return dateOnRightCompDisp;
      }

      /**
       * Sets the value of the dateOnRightCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setDateOnRightCompDisp(Integer value)
      {
         this.dateOnRightCompDisp = value;
      }

      /**
       * Gets the value of the fromDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getFromDate()
      {
         return fromDate;
      }

      /**
       * Sets the value of the fromDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFromDate(LocalDate value)
      {
         this.fromDate = value;
      }

      /**
       * Gets the value of the toDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getToDate()
      {
         return toDate;
      }

      /**
       * Sets the value of the toDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setToDate(LocalDate value)
      {
         this.toDate = value;
      }

      /**
       * Gets the value of the fitToPage property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFitToPage()
      {
         return fitToPage;
      }

      /**
       * Sets the value of the fitToPage property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFitToPage(Integer value)
      {
         this.fitToPage = value;
      }

      /**
       * Gets the value of the numberOfPages property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfPages()
      {
         return numberOfPages;
      }

      /**
       * Sets the value of the numberOfPages property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfPages(Integer value)
      {
         this.numberOfPages = value;
      }

      /**
       * Gets the value of the fitHorizontal property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFitHorizontal()
      {
         return fitHorizontal;
      }

      /**
       * Sets the value of the fitHorizontal property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFitHorizontal(Integer value)
      {
         this.fitHorizontal = value;
      }

      /**
       * Gets the value of the numberSequence property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberSequence()
      {
         return numberSequence;
      }

      /**
       * Sets the value of the numberSequence property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberSequence(Integer value)
      {
         this.numberSequence = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *       &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class PrintToImageFile
   {

      @XmlAttribute(name = "allRows") protected Integer allRows;
      @XmlAttribute(name = "fromTask") protected Integer fromTask;
      @XmlAttribute(name = "toTask") protected Integer toTask;
      @XmlAttribute(name = "numberOfLeftColumnsCompDisp") protected Integer numberOfLeftColumnsCompDisp;
      @XmlAttribute(name = "numberOfLeftColumns") protected Integer numberOfLeftColumns;
      @XmlAttribute(name = "showTaskNumbers") protected Integer showTaskNumbers;
      @XmlAttribute(name = "dateOnRightCompDisp") protected Integer dateOnRightCompDisp;
      @XmlAttribute(name = "fromDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate fromDate;
      @XmlAttribute(name = "toDate") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate toDate;

      /**
       * Gets the value of the allRows property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getAllRows()
      {
         return allRows;
      }

      /**
       * Sets the value of the allRows property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setAllRows(Integer value)
      {
         this.allRows = value;
      }

      /**
       * Gets the value of the fromTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getFromTask()
      {
         return fromTask;
      }

      /**
       * Sets the value of the fromTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setFromTask(Integer value)
      {
         this.fromTask = value;
      }

      /**
       * Gets the value of the toTask property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getToTask()
      {
         return toTask;
      }

      /**
       * Sets the value of the toTask property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setToTask(Integer value)
      {
         this.toTask = value;
      }

      /**
       * Gets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumnsCompDisp()
      {
         return numberOfLeftColumnsCompDisp;
      }

      /**
       * Sets the value of the numberOfLeftColumnsCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumnsCompDisp(Integer value)
      {
         this.numberOfLeftColumnsCompDisp = value;
      }

      /**
       * Gets the value of the numberOfLeftColumns property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getNumberOfLeftColumns()
      {
         return numberOfLeftColumns;
      }

      /**
       * Sets the value of the numberOfLeftColumns property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setNumberOfLeftColumns(Integer value)
      {
         this.numberOfLeftColumns = value;
      }

      /**
       * Gets the value of the showTaskNumbers property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getShowTaskNumbers()
      {
         return showTaskNumbers;
      }

      /**
       * Sets the value of the showTaskNumbers property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setShowTaskNumbers(Integer value)
      {
         this.showTaskNumbers = value;
      }

      /**
       * Gets the value of the dateOnRightCompDisp property.
       *
       * @return
       *     possible object is
       *     {@link Integer }
       *
       */
      public Integer getDateOnRightCompDisp()
      {
         return dateOnRightCompDisp;
      }

      /**
       * Sets the value of the dateOnRightCompDisp property.
       *
       * @param value
       *     allowed object is
       *     {@link Integer }
       *
       */
      public void setDateOnRightCompDisp(Integer value)
      {
         this.dateOnRightCompDisp = value;
      }

      /**
       * Gets the value of the fromDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getFromDate()
      {
         return fromDate;
      }

      /**
       * Sets the value of the fromDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFromDate(LocalDate value)
      {
         this.fromDate = value;
      }

      /**
       * Gets the value of the toDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getToDate()
      {
         return toDate;
      }

      /**
       * Sets the value of the toDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setToDate(LocalDate value)
      {
         this.toDate = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Task" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="S" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *                 &lt;attribute name="B" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="BC" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="D" type="{}ganttDesignerDuration" /&gt;
    *                 &lt;attribute name="H" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="U" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="VA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="In" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="C" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="PC" type="{}ganttDesignerPercent" /&gt;
    *                 &lt;attribute name="DL" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *                 &lt;attribute name="P" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="L" type="{}ganttDesignerDuration" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "task"
   }) public static class Tasks
   {

      @XmlElement(name = "Task", required = true) protected List<Gantt.Tasks.Task> task;

      /**
       * Gets the value of the task property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the task property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getTask().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.Tasks.Task }
       *
       *
       */
      public List<Gantt.Tasks.Task> getTask()
      {
         if (task == null)
         {
            task = new ArrayList<>();
         }
         return this.task;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="S" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       *       &lt;attribute name="B" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="BC" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="D" type="{}ganttDesignerDuration" /&gt;
       *       &lt;attribute name="H" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="U" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="VA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="In" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="C" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="PC" type="{}ganttDesignerPercent" /&gt;
       *       &lt;attribute name="DL" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       *       &lt;attribute name="P" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="L" type="{}ganttDesignerDuration" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "content"
      }) public static class Task
      {

         @XmlValue protected String content;
         @XmlAttribute(name = "ID") protected String id;
         @XmlAttribute(name = "S") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate s;
         @XmlAttribute(name = "B") protected Integer b;
         @XmlAttribute(name = "BC") protected Integer bc;
         @XmlAttribute(name = "D") @XmlJavaTypeAdapter(Adapter3.class) protected Duration d;
         @XmlAttribute(name = "H") protected Integer h;
         @XmlAttribute(name = "U") protected Integer u;
         @XmlAttribute(name = "VA") protected Integer va;
         @XmlAttribute(name = "In") protected Integer in;
         @XmlAttribute(name = "C") protected Double c;
         @XmlAttribute(name = "PC") @XmlJavaTypeAdapter(Adapter4.class) protected Double pc;
         @XmlAttribute(name = "DL") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "date") protected LocalDate dl;
         @XmlAttribute(name = "P") protected String p;
         @XmlAttribute(name = "L") @XmlJavaTypeAdapter(Adapter3.class) protected Duration l;

         /**
          * Gets the value of the content property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getContent()
         {
            return content;
         }

         /**
          * Sets the value of the content property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setContent(String value)
         {
            this.content = value;
         }

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(String value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the s property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDate getS()
         {
            return s;
         }

         /**
          * Sets the value of the s property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setS(LocalDate value)
         {
            this.s = value;
         }

         /**
          * Gets the value of the b property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getB()
         {
            return b;
         }

         /**
          * Sets the value of the b property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setB(Integer value)
         {
            this.b = value;
         }

         /**
          * Gets the value of the bc property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getBC()
         {
            return bc;
         }

         /**
          * Sets the value of the bc property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setBC(Integer value)
         {
            this.bc = value;
         }

         /**
          * Gets the value of the d property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Duration getD()
         {
            return d;
         }

         /**
          * Sets the value of the d property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setD(Duration value)
         {
            this.d = value;
         }

         /**
          * Gets the value of the h property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getH()
         {
            return h;
         }

         /**
          * Sets the value of the h property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setH(Integer value)
         {
            this.h = value;
         }

         /**
          * Gets the value of the u property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getU()
         {
            return u;
         }

         /**
          * Sets the value of the u property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setU(Integer value)
         {
            this.u = value;
         }

         /**
          * Gets the value of the va property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getVA()
         {
            return va;
         }

         /**
          * Sets the value of the va property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setVA(Integer value)
         {
            this.va = value;
         }

         /**
          * Gets the value of the in property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getIn()
         {
            return in;
         }

         /**
          * Sets the value of the in property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setIn(Integer value)
         {
            this.in = value;
         }

         /**
          * Gets the value of the c property.
          *
          * @return
          *     possible object is
          *     {@link Double }
          *
          */
         public Double getC()
         {
            return c;
         }

         /**
          * Sets the value of the c property.
          *
          * @param value
          *     allowed object is
          *     {@link Double }
          *
          */
         public void setC(Double value)
         {
            this.c = value;
         }

         /**
          * Gets the value of the pc property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getPC()
         {
            return pc;
         }

         /**
          * Sets the value of the pc property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setPC(Double value)
         {
            this.pc = value;
         }

         /**
          * Gets the value of the dl property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDate getDL()
         {
            return dl;
         }

         /**
          * Sets the value of the dl property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDL(LocalDate value)
         {
            this.dl = value;
         }

         /**
          * Gets the value of the p property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getP()
         {
            return p;
         }

         /**
          * Sets the value of the p property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setP(String value)
         {
            this.p = value;
         }

         /**
          * Gets the value of the l property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Duration getL()
         {
            return l;
         }

         /**
          * Sets the value of the l property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setL(Duration value)
         {
            this.l = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Font" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *       &lt;attribute name="Flag" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="Deadline" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "font"
   }) public static class TextStyles
   {

      @XmlElement(name = "Font", required = true) protected List<Gantt.TextStyles.Font> font;
      @XmlAttribute(name = "Flag") protected String flag;
      @XmlAttribute(name = "Deadline") protected String deadline;

      /**
       * Gets the value of the font property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the font property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getFont().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Gantt.TextStyles.Font }
       *
       *
       */
      public List<Gantt.TextStyles.Font> getFont()
      {
         if (font == null)
         {
            font = new ArrayList<>();
         }
         return this.font;
      }

      /**
       * Gets the value of the flag property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFlag()
      {
         return flag;
      }

      /**
       * Sets the value of the flag property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFlag(String value)
      {
         this.flag = value;
      }

      /**
       * Gets the value of the deadline property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getDeadline()
      {
         return deadline;
      }

      /**
       * Sets the value of the deadline property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setDeadline(String value)
      {
         this.deadline = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Font
      {

         @XmlAttribute(name = "Style") protected Integer style;
         @XmlAttribute(name = "Size") protected Integer size;
         @XmlAttribute(name = "Color") protected Integer color;

         /**
          * Gets the value of the style property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getStyle()
         {
            return style;
         }

         /**
          * Sets the value of the style property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setStyle(Integer value)
         {
            this.style = value;
         }

         /**
          * Gets the value of the size property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getSize()
         {
            return size;
         }

         /**
          * Sets the value of the size property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setSize(Integer value)
         {
            this.size = value;
         }

         /**
          * Gets the value of the color property.
          *
          * @return
          *     possible object is
          *     {@link Integer }
          *
          */
         public Integer getColor()
         {
            return color;
         }

         /**
          * Sets the value of the color property.
          *
          * @param value
          *     allowed object is
          *     {@link Integer }
          *
          */
         public void setColor(Integer value)
         {
            this.color = value;
         }

      }

   }

}
