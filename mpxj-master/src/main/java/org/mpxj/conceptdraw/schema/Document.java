//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:49 AM BST
//

package org.mpxj.conceptdraw.schema;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import org.mpxj.CurrencySymbolPosition;
import org.mpxj.Priority;
import org.mpxj.RelationType;
import org.mpxj.ResourceType;
import org.mpxj.TaskType;
import org.mpxj.TimeUnit;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="WorkspaceProperties"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="CurrencySymbol" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="CurrencyPosition" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="CurrencyDigits" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="HoursPerDay" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="HoursPerWeek" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="DaysPerMonth" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="CalcCPForSubprojects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                   &lt;element name="MaximumSlack" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="PrintingProperties"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;all&gt;
 *                   &lt;element name="PrintOrientation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="MarginLeft" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="MarginRight" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="MarginTop" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="MarginBottom" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="MarginHeader" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="MarginFooter" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                   &lt;element name="FitTo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="PagesH" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="PagesV" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="TimescaleMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="DoPrintGrid" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *                   &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *                   &lt;element name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="LegendMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="FirstPageNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                   &lt;element name="PrintView" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="HeaderFooterFontName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                   &lt;element name="HeaderFooterFontSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                 &lt;/all&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ThemeID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ShowAssignedResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="Markers"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Marker" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="DisplayStyle" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ResourceUsageDiagram"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
 *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
 *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ActiveFilter" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Calendars"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Calendar" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="BaseCalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="WeekDays"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="Day"&gt;
 *                                                   &lt;simpleType&gt;
 *                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
 *                                                       &lt;enumeration value="0"/&gt;
 *                                                       &lt;enumeration value="1"/&gt;
 *                                                       &lt;enumeration value="2"/&gt;
 *                                                       &lt;enumeration value="3"/&gt;
 *                                                       &lt;enumeration value="4"/&gt;
 *                                                       &lt;enumeration value="5"/&gt;
 *                                                       &lt;enumeration value="6"/&gt;
 *                                                     &lt;/restriction&gt;
 *                                                   &lt;/simpleType&gt;
 *                                                 &lt;/element&gt;
 *                                                 &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="TimePeriods"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;sequence&gt;
 *                                                                     &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                     &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                   &lt;/sequence&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="ExceptedDays"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="ExceptedDay" maxOccurs="unbounded" minOccurs="0"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *                                                 &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="TimePeriods"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;sequence&gt;
 *                                                                     &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                     &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                   &lt;/sequence&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Resources"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
 *                   &lt;element name="Resource" maxOccurs="unbounded" minOccurs="0"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
 *                             &lt;element name="CalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="Type"&gt;
 *                               &lt;simpleType&gt;
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
 *                                   &lt;enumeration value="0"/&gt;
 *                                   &lt;enumeration value="1"/&gt;
 *                                 &lt;/restriction&gt;
 *                               &lt;/simpleType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="SubType"&gt;
 *                               &lt;simpleType&gt;
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *                                   &lt;enumeration value="work"/&gt;
 *                                   &lt;enumeration value="material"/&gt;
 *                                   &lt;enumeration value="cost"/&gt;
 *                                   &lt;enumeration value="equipment"/&gt;
 *                                   &lt;enumeration value="company"/&gt;
 *                                 &lt;/restriction&gt;
 *                               &lt;/simpleType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="EMail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                             &lt;element name="CostTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
 *                             &lt;element name="Group" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="ShowAssignedTasks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
 *                             &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Projects"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
 *                   &lt;element name="Project" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
 *                             &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ProjectProps"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
 *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
 *                             &lt;element name="Task" maxOccurs="unbounded" minOccurs="0"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
 *                                       &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                                       &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                                       &lt;element name="BaseStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                       &lt;element name="BaseFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                       &lt;element name="BaseDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
 *                                       &lt;element name="BaseDurationTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
 *                                       &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                       &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                       &lt;element name="ActualDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
 *                                       &lt;element name="TemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                                       &lt;element name="DeadlineTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                                       &lt;element name="BaselineStartTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                                       &lt;element name="BaselineFinishTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *                                       &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                                       &lt;element name="Cost1" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                                       &lt;element name="ValidatedByProject" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                                       &lt;element name="RecalcBase1"&gt;
 *                                         &lt;simpleType&gt;
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
 *                                             &lt;enumeration value="0"/&gt;
 *                                             &lt;enumeration value="1"/&gt;
 *                                             &lt;enumeration value="2"/&gt;
 *                                             &lt;enumeration value="3"/&gt;
 *                                           &lt;/restriction&gt;
 *                                         &lt;/simpleType&gt;
 *                                       &lt;/element&gt;
 *                                       &lt;element name="RecalcBase2"&gt;
 *                                         &lt;simpleType&gt;
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
 *                                             &lt;enumeration value="0"/&gt;
 *                                             &lt;enumeration value="1"/&gt;
 *                                             &lt;enumeration value="2"/&gt;
 *                                             &lt;enumeration value="3"/&gt;
 *                                           &lt;/restriction&gt;
 *                                         &lt;/simpleType&gt;
 *                                       &lt;/element&gt;
 *                                       &lt;element name="IsMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                       &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}BaselineGroup" minOccurs="0"/&gt;
 *                                       &lt;element name="Complete" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *                                       &lt;element name="IsHaveDeadline" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                       &lt;element name="SchedulingType"&gt;
 *                                         &lt;simpleType&gt;
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *                                             &lt;enumeration value="fixedDuration"/&gt;
 *                                             &lt;enumeration value="fixedUnits"/&gt;
 *                                             &lt;enumeration value="fixedWork"/&gt;
 *                                           &lt;/restriction&gt;
 *                                         &lt;/simpleType&gt;
 *                                       &lt;/element&gt;
 *                                       &lt;element name="IsEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Priority"/&gt;
 *                                       &lt;element name="MarkedByUser" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                       &lt;element name="ShowSubtasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
 *                                       &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
 *                                       &lt;element name="ResourceAssignments"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                                           &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                                           &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                                                           &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Callouts"/&gt;
 *                                       &lt;element name="DeadlineDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ProjectPortfolioView"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="CompleteJournalTrackingPeriod" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}LongTimeUnitType"/&gt;
 *                   &lt;element name="PPVItems" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}PPVItemsType"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Links"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Link" maxOccurs="unbounded" minOccurs="0"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;choice&gt;
 *                               &lt;sequence&gt;
 *                                 &lt;element name="SourceTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                 &lt;element name="DestinationTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                               &lt;/sequence&gt;
 *                               &lt;sequence&gt;
 *                                 &lt;element name="SourceProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                 &lt;element name="DestinationProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                               &lt;/sequence&gt;
 *                             &lt;/choice&gt;
 *                             &lt;element name="Type"&gt;
 *                               &lt;simpleType&gt;
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
 *                                   &lt;enumeration value="0"/&gt;
 *                                   &lt;enumeration value="1"/&gt;
 *                                   &lt;enumeration value="2"/&gt;
 *                                   &lt;enumeration value="3"/&gt;
 *                                 &lt;/restriction&gt;
 *                               &lt;/simpleType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="Lag" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
 *                             &lt;element name="LagUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Dashboards"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Dashboard" minOccurs="0"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="Application" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
{
   "workspaceProperties",
   "printingProperties",
   "themeID",
   "showAssignedResources",
   "markers",
   "resourceUsageDiagram",
   "calendars",
   "resources",
   "projects",
   "projectPortfolioView",
   "links",
   "dashboards"
}) @XmlRootElement(name = "Document") public class Document
{

   @XmlElement(name = "WorkspaceProperties", required = true) protected Document.WorkspaceProperties workspaceProperties;
   @XmlElement(name = "PrintingProperties", required = true) protected Document.PrintingProperties printingProperties;
   @XmlElement(name = "ThemeID", required = true) protected String themeID;
   @XmlElement(name = "ShowAssignedResources") protected boolean showAssignedResources;
   @XmlElement(name = "Markers", required = true) protected Document.Markers markers;
   @XmlElement(name = "ResourceUsageDiagram", required = true) protected Document.ResourceUsageDiagram resourceUsageDiagram;
   @XmlElement(name = "Calendars", required = true) protected Document.Calendars calendars;
   @XmlElement(name = "Resources", required = true) protected Document.Resources resources;
   @XmlElement(name = "Projects", required = true) protected Document.Projects projects;
   @XmlElement(name = "ProjectPortfolioView", required = true) protected Document.ProjectPortfolioView projectPortfolioView;
   @XmlElement(name = "Links", required = true) protected Document.Links links;
   @XmlElement(name = "Dashboards", required = true) protected Document.Dashboards dashboards;
   @XmlAttribute(name = "Application") protected String application;
   @XmlAttribute(name = "Version") @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer version;

   /**
    * Gets the value of the workspaceProperties property.
    *
    * @return
    *     possible object is
    *     {@link Document.WorkspaceProperties }
    *
    */
   public Document.WorkspaceProperties getWorkspaceProperties()
   {
      return workspaceProperties;
   }

   /**
    * Sets the value of the workspaceProperties property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.WorkspaceProperties }
    *
    */
   public void setWorkspaceProperties(Document.WorkspaceProperties value)
   {
      this.workspaceProperties = value;
   }

   /**
    * Gets the value of the printingProperties property.
    *
    * @return
    *     possible object is
    *     {@link Document.PrintingProperties }
    *
    */
   public Document.PrintingProperties getPrintingProperties()
   {
      return printingProperties;
   }

   /**
    * Sets the value of the printingProperties property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.PrintingProperties }
    *
    */
   public void setPrintingProperties(Document.PrintingProperties value)
   {
      this.printingProperties = value;
   }

   /**
    * Gets the value of the themeID property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getThemeID()
   {
      return themeID;
   }

   /**
    * Sets the value of the themeID property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setThemeID(String value)
   {
      this.themeID = value;
   }

   /**
    * Gets the value of the showAssignedResources property.
    *
    */
   public boolean isShowAssignedResources()
   {
      return showAssignedResources;
   }

   /**
    * Sets the value of the showAssignedResources property.
    *
    */
   public void setShowAssignedResources(boolean value)
   {
      this.showAssignedResources = value;
   }

   /**
    * Gets the value of the markers property.
    *
    * @return
    *     possible object is
    *     {@link Document.Markers }
    *
    */
   public Document.Markers getMarkers()
   {
      return markers;
   }

   /**
    * Sets the value of the markers property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Markers }
    *
    */
   public void setMarkers(Document.Markers value)
   {
      this.markers = value;
   }

   /**
    * Gets the value of the resourceUsageDiagram property.
    *
    * @return
    *     possible object is
    *     {@link Document.ResourceUsageDiagram }
    *
    */
   public Document.ResourceUsageDiagram getResourceUsageDiagram()
   {
      return resourceUsageDiagram;
   }

   /**
    * Sets the value of the resourceUsageDiagram property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.ResourceUsageDiagram }
    *
    */
   public void setResourceUsageDiagram(Document.ResourceUsageDiagram value)
   {
      this.resourceUsageDiagram = value;
   }

   /**
    * Gets the value of the calendars property.
    *
    * @return
    *     possible object is
    *     {@link Document.Calendars }
    *
    */
   public Document.Calendars getCalendars()
   {
      return calendars;
   }

   /**
    * Sets the value of the calendars property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Calendars }
    *
    */
   public void setCalendars(Document.Calendars value)
   {
      this.calendars = value;
   }

   /**
    * Gets the value of the resources property.
    *
    * @return
    *     possible object is
    *     {@link Document.Resources }
    *
    */
   public Document.Resources getResources()
   {
      return resources;
   }

   /**
    * Sets the value of the resources property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Resources }
    *
    */
   public void setResources(Document.Resources value)
   {
      this.resources = value;
   }

   /**
    * Gets the value of the projects property.
    *
    * @return
    *     possible object is
    *     {@link Document.Projects }
    *
    */
   public Document.Projects getProjects()
   {
      return projects;
   }

   /**
    * Sets the value of the projects property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Projects }
    *
    */
   public void setProjects(Document.Projects value)
   {
      this.projects = value;
   }

   /**
    * Gets the value of the projectPortfolioView property.
    *
    * @return
    *     possible object is
    *     {@link Document.ProjectPortfolioView }
    *
    */
   public Document.ProjectPortfolioView getProjectPortfolioView()
   {
      return projectPortfolioView;
   }

   /**
    * Sets the value of the projectPortfolioView property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.ProjectPortfolioView }
    *
    */
   public void setProjectPortfolioView(Document.ProjectPortfolioView value)
   {
      this.projectPortfolioView = value;
   }

   /**
    * Gets the value of the links property.
    *
    * @return
    *     possible object is
    *     {@link Document.Links }
    *
    */
   public Document.Links getLinks()
   {
      return links;
   }

   /**
    * Sets the value of the links property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Links }
    *
    */
   public void setLinks(Document.Links value)
   {
      this.links = value;
   }

   /**
    * Gets the value of the dashboards property.
    *
    * @return
    *     possible object is
    *     {@link Document.Dashboards }
    *
    */
   public Document.Dashboards getDashboards()
   {
      return dashboards;
   }

   /**
    * Sets the value of the dashboards property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Dashboards }
    *
    */
   public void setDashboards(Document.Dashboards value)
   {
      this.dashboards = value;
   }

   /**
    * Gets the value of the application property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getApplication()
   {
      return application;
   }

   /**
    * Sets the value of the application property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setApplication(String value)
   {
      this.application = value;
   }

   /**
    * Gets the value of the version property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Integer getVersion()
   {
      return version;
   }

   /**
    * Sets the value of the version property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setVersion(Integer value)
   {
      this.version = value;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Calendar" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="BaseCalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="WeekDays"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="Day"&gt;
    *                                         &lt;simpleType&gt;
    *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
    *                                             &lt;enumeration value="0"/&gt;
    *                                             &lt;enumeration value="1"/&gt;
    *                                             &lt;enumeration value="2"/&gt;
    *                                             &lt;enumeration value="3"/&gt;
    *                                             &lt;enumeration value="4"/&gt;
    *                                             &lt;enumeration value="5"/&gt;
    *                                             &lt;enumeration value="6"/&gt;
    *                                           &lt;/restriction&gt;
    *                                         &lt;/simpleType&gt;
    *                                       &lt;/element&gt;
    *                                       &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="TimePeriods"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;sequence&gt;
    *                                                           &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                           &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                         &lt;/sequence&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="ExceptedDays"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="ExceptedDay" maxOccurs="unbounded" minOccurs="0"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
    *                                       &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="TimePeriods"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;sequence&gt;
    *                                                           &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                           &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                         &lt;/sequence&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "calendar"
   }) public static class Calendars
   {

      @XmlElement(name = "Calendar", required = true) protected List<Document.Calendars.Calendar> calendar;

      /**
       * Gets the value of the calendar property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the calendar property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getCalendar().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Calendars.Calendar }
       *
       *
       */
      public List<Document.Calendars.Calendar> getCalendar()
      {
         if (calendar == null)
         {
            calendar = new ArrayList<>();
         }
         return this.calendar;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="BaseCalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="WeekDays"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="Day"&gt;
       *                               &lt;simpleType&gt;
       *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
       *                                   &lt;enumeration value="0"/&gt;
       *                                   &lt;enumeration value="1"/&gt;
       *                                   &lt;enumeration value="2"/&gt;
       *                                   &lt;enumeration value="3"/&gt;
       *                                   &lt;enumeration value="4"/&gt;
       *                                   &lt;enumeration value="5"/&gt;
       *                                   &lt;enumeration value="6"/&gt;
       *                                 &lt;/restriction&gt;
       *                               &lt;/simpleType&gt;
       *                             &lt;/element&gt;
       *                             &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="TimePeriods"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;sequence&gt;
       *                                                 &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                                 &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                               &lt;/sequence&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="ExceptedDays"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="ExceptedDay" maxOccurs="unbounded" minOccurs="0"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
       *                             &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="TimePeriods"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;sequence&gt;
       *                                                 &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                                 &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                               &lt;/sequence&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "id",
         "baseCalendarID",
         "name",
         "weekDays",
         "exceptedDays"
      }) public static class Calendar
      {

         @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlElement(name = "BaseCalendarID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer baseCalendarID;
         @XmlElement(name = "Name", required = true) protected String name;
         @XmlElement(name = "WeekDays", required = true) protected Document.Calendars.Calendar.WeekDays weekDays;
         @XmlElement(name = "ExceptedDays", required = true) protected Document.Calendars.Calendar.ExceptedDays exceptedDays;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the baseCalendarID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getBaseCalendarID()
         {
            return baseCalendarID;
         }

         /**
          * Sets the value of the baseCalendarID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaseCalendarID(Integer value)
         {
            this.baseCalendarID = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the weekDays property.
          *
          * @return
          *     possible object is
          *     {@link Document.Calendars.Calendar.WeekDays }
          *
          */
         public Document.Calendars.Calendar.WeekDays getWeekDays()
         {
            return weekDays;
         }

         /**
          * Sets the value of the weekDays property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.Calendars.Calendar.WeekDays }
          *
          */
         public void setWeekDays(Document.Calendars.Calendar.WeekDays value)
         {
            this.weekDays = value;
         }

         /**
          * Gets the value of the exceptedDays property.
          *
          * @return
          *     possible object is
          *     {@link Document.Calendars.Calendar.ExceptedDays }
          *
          */
         public Document.Calendars.Calendar.ExceptedDays getExceptedDays()
         {
            return exceptedDays;
         }

         /**
          * Sets the value of the exceptedDays property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.Calendars.Calendar.ExceptedDays }
          *
          */
         public void setExceptedDays(Document.Calendars.Calendar.ExceptedDays value)
         {
            this.exceptedDays = value;
         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="ExceptedDay" maxOccurs="unbounded" minOccurs="0"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
          *                   &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="TimePeriods"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;sequence&gt;
          *                                       &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                       &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                     &lt;/sequence&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "exceptedDay"
         }) public static class ExceptedDays
         {

            @XmlElement(name = "ExceptedDay") protected List<Document.Calendars.Calendar.ExceptedDays.ExceptedDay> exceptedDay;

            /**
             * Gets the value of the exceptedDay property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the exceptedDay property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getExceptedDay().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.Calendars.Calendar.ExceptedDays.ExceptedDay }
             *
             *
             */
            public List<Document.Calendars.Calendar.ExceptedDays.ExceptedDay> getExceptedDay()
            {
               if (exceptedDay == null)
               {
                  exceptedDay = new ArrayList<>();
               }
               return this.exceptedDay;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
             *         &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="TimePeriods"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;sequence&gt;
             *                             &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                             &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                           &lt;/sequence&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "date",
               "isDayWorking",
               "timePeriods"
            }) public static class ExceptedDay
            {

               @XmlElement(name = "Date", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "date") protected LocalDate date;
               @XmlElement(name = "IsDayWorking") protected boolean isDayWorking;
               @XmlElement(name = "TimePeriods", required = true) protected Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods timePeriods;

               /**
                * Gets the value of the date property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public LocalDate getDate()
               {
                  return date;
               }

               /**
                * Sets the value of the date property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDate(LocalDate value)
               {
                  this.date = value;
               }

               /**
                * Gets the value of the isDayWorking property.
                *
                */
               public boolean isIsDayWorking()
               {
                  return isDayWorking;
               }

               /**
                * Sets the value of the isDayWorking property.
                *
                */
               public void setIsDayWorking(boolean value)
               {
                  this.isDayWorking = value;
               }

               /**
                * Gets the value of the timePeriods property.
                *
                * @return
                *     possible object is
                *     {@link Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods }
                *
                */
               public Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods getTimePeriods()
               {
                  return timePeriods;
               }

               /**
                * Sets the value of the timePeriods property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods }
                *
                */
               public void setTimePeriods(Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods value)
               {
                  this.timePeriods = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;sequence&gt;
                *                   &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                   &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                 &lt;/sequence&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "timePeriod"
               }) public static class TimePeriods
               {

                  @XmlElement(name = "TimePeriod") protected List<Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod> timePeriod;

                  /**
                   * Gets the value of the timePeriod property.
                   *
                   * <p>
                   * This accessor method returns a reference to the live list,
                   * not a snapshot. Therefore any modification you make to the
                   * returned list will be present inside the Jakarta XML Binding object.
                   * This is why there is not a <CODE>set</CODE> method for the timePeriod property.
                   *
                   * <p>
                   * For example, to add a new item, do as follows:
                   * <pre>
                   *    getTimePeriod().add(newItem);
                   * </pre>
                   *
                   *
                   * <p>
                   * Objects of the following type(s) are allowed in the list
                   * {@link Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod }
                   *
                   *
                   */
                  public List<Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod> getTimePeriod()
                  {
                     if (timePeriod == null)
                     {
                        timePeriod = new ArrayList<>();
                     }
                     return this.timePeriod;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;sequence&gt;
                   *         &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *         &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *       &lt;/sequence&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
                  {
                     "from",
                     "to"
                  }) public static class TimePeriod
                  {

                     @XmlElement(name = "From", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "time") protected LocalTime from;
                     @XmlElement(name = "To", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "time") protected LocalTime to;

                     /**
                      * Gets the value of the from property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getFrom()
                     {
                        return from;
                     }

                     /**
                      * Sets the value of the from property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFrom(LocalTime value)
                     {
                        this.from = value;
                     }

                     /**
                      * Gets the value of the to property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getTo()
                     {
                        return to;
                     }

                     /**
                      * Sets the value of the to property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setTo(LocalTime value)
                     {
                        this.to = value;
                     }

                  }

               }

            }

         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="Day"&gt;
          *                     &lt;simpleType&gt;
          *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
          *                         &lt;enumeration value="0"/&gt;
          *                         &lt;enumeration value="1"/&gt;
          *                         &lt;enumeration value="2"/&gt;
          *                         &lt;enumeration value="3"/&gt;
          *                         &lt;enumeration value="4"/&gt;
          *                         &lt;enumeration value="5"/&gt;
          *                         &lt;enumeration value="6"/&gt;
          *                       &lt;/restriction&gt;
          *                     &lt;/simpleType&gt;
          *                   &lt;/element&gt;
          *                   &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="TimePeriods"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;sequence&gt;
          *                                       &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                       &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                     &lt;/sequence&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "weekDay"
         }) public static class WeekDays
         {

            @XmlElement(name = "WeekDay") protected List<Document.Calendars.Calendar.WeekDays.WeekDay> weekDay;

            /**
             * Gets the value of the weekDay property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the weekDay property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getWeekDay().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.Calendars.Calendar.WeekDays.WeekDay }
             *
             *
             */
            public List<Document.Calendars.Calendar.WeekDays.WeekDay> getWeekDay()
            {
               if (weekDay == null)
               {
                  weekDay = new ArrayList<>();
               }
               return this.weekDay;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="Day"&gt;
             *           &lt;simpleType&gt;
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
             *               &lt;enumeration value="0"/&gt;
             *               &lt;enumeration value="1"/&gt;
             *               &lt;enumeration value="2"/&gt;
             *               &lt;enumeration value="3"/&gt;
             *               &lt;enumeration value="4"/&gt;
             *               &lt;enumeration value="5"/&gt;
             *               &lt;enumeration value="6"/&gt;
             *             &lt;/restriction&gt;
             *           &lt;/simpleType&gt;
             *         &lt;/element&gt;
             *         &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="TimePeriods"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;sequence&gt;
             *                             &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                             &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                           &lt;/sequence&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "day",
               "isDayWorking",
               "timePeriods"
            }) public static class WeekDay
            {

               @XmlElement(name = "Day", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter14.class) protected DayOfWeek day;
               @XmlElement(name = "IsDayWorking") protected boolean isDayWorking;
               @XmlElement(name = "TimePeriods", required = true) protected Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods timePeriods;

               /**
                * Gets the value of the day property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public DayOfWeek getDay()
               {
                  return day;
               }

               /**
                * Sets the value of the day property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDay(DayOfWeek value)
               {
                  this.day = value;
               }

               /**
                * Gets the value of the isDayWorking property.
                *
                */
               public boolean isIsDayWorking()
               {
                  return isDayWorking;
               }

               /**
                * Sets the value of the isDayWorking property.
                *
                */
               public void setIsDayWorking(boolean value)
               {
                  this.isDayWorking = value;
               }

               /**
                * Gets the value of the timePeriods property.
                *
                * @return
                *     possible object is
                *     {@link Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods }
                *
                */
               public Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods getTimePeriods()
               {
                  return timePeriods;
               }

               /**
                * Sets the value of the timePeriods property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods }
                *
                */
               public void setTimePeriods(Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods value)
               {
                  this.timePeriods = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;sequence&gt;
                *                   &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                   &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                 &lt;/sequence&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "timePeriod"
               }) public static class TimePeriods
               {

                  @XmlElement(name = "TimePeriod") protected List<Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod> timePeriod;

                  /**
                   * Gets the value of the timePeriod property.
                   *
                   * <p>
                   * This accessor method returns a reference to the live list,
                   * not a snapshot. Therefore any modification you make to the
                   * returned list will be present inside the Jakarta XML Binding object.
                   * This is why there is not a <CODE>set</CODE> method for the timePeriod property.
                   *
                   * <p>
                   * For example, to add a new item, do as follows:
                   * <pre>
                   *    getTimePeriod().add(newItem);
                   * </pre>
                   *
                   *
                   * <p>
                   * Objects of the following type(s) are allowed in the list
                   * {@link Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod }
                   *
                   *
                   */
                  public List<Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod> getTimePeriod()
                  {
                     if (timePeriod == null)
                     {
                        timePeriod = new ArrayList<>();
                     }
                     return this.timePeriod;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;sequence&gt;
                   *         &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *         &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *       &lt;/sequence&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
                  {
                     "from",
                     "to"
                  }) public static class TimePeriod
                  {

                     @XmlElement(name = "From", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "time") protected LocalTime from;
                     @XmlElement(name = "To", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "time") protected LocalTime to;

                     /**
                      * Gets the value of the from property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getFrom()
                     {
                        return from;
                     }

                     /**
                      * Sets the value of the from property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFrom(LocalTime value)
                     {
                        this.from = value;
                     }

                     /**
                      * Gets the value of the to property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getTo()
                     {
                        return to;
                     }

                     /**
                      * Sets the value of the to property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setTo(LocalTime value)
                     {
                        this.to = value;
                     }

                  }

               }

            }

         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Dashboard" minOccurs="0"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "dashboard"
   }) public static class Dashboards
   {

      @XmlElement(name = "Dashboard") protected Document.Dashboards.Dashboard dashboard;

      /**
       * Gets the value of the dashboard property.
       *
       * @return
       *     possible object is
       *     {@link Document.Dashboards.Dashboard }
       *
       */
      public Document.Dashboards.Dashboard getDashboard()
      {
         return dashboard;
      }

      /**
       * Sets the value of the dashboard property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.Dashboards.Dashboard }
       *
       */
      public void setDashboard(Document.Dashboards.Dashboard value)
      {
         this.dashboard = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Dashboard
      {

         @XmlAttribute(name = "ID") protected String id;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(String value)
         {
            this.id = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Link" maxOccurs="unbounded" minOccurs="0"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;choice&gt;
    *                     &lt;sequence&gt;
    *                       &lt;element name="SourceTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                       &lt;element name="DestinationTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                     &lt;/sequence&gt;
    *                     &lt;sequence&gt;
    *                       &lt;element name="SourceProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                       &lt;element name="DestinationProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                     &lt;/sequence&gt;
    *                   &lt;/choice&gt;
    *                   &lt;element name="Type"&gt;
    *                     &lt;simpleType&gt;
    *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
    *                         &lt;enumeration value="0"/&gt;
    *                         &lt;enumeration value="1"/&gt;
    *                         &lt;enumeration value="2"/&gt;
    *                         &lt;enumeration value="3"/&gt;
    *                       &lt;/restriction&gt;
    *                     &lt;/simpleType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="Lag" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                   &lt;element name="LagUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "link"
   }) public static class Links
   {

      @XmlElement(name = "Link") protected List<Document.Links.Link> link;

      /**
       * Gets the value of the link property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the link property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getLink().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Links.Link }
       *
       *
       */
      public List<Document.Links.Link> getLink()
      {
         if (link == null)
         {
            link = new ArrayList<>();
         }
         return this.link;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;choice&gt;
       *           &lt;sequence&gt;
       *             &lt;element name="SourceTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *             &lt;element name="DestinationTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *           &lt;/sequence&gt;
       *           &lt;sequence&gt;
       *             &lt;element name="SourceProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *             &lt;element name="DestinationProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *           &lt;/sequence&gt;
       *         &lt;/choice&gt;
       *         &lt;element name="Type"&gt;
       *           &lt;simpleType&gt;
       *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
       *               &lt;enumeration value="0"/&gt;
       *               &lt;enumeration value="1"/&gt;
       *               &lt;enumeration value="2"/&gt;
       *               &lt;enumeration value="3"/&gt;
       *             &lt;/restriction&gt;
       *           &lt;/simpleType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="Lag" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *         &lt;element name="LagUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "id",
         "sourceTaskID",
         "destinationTaskID",
         "sourceProjectID",
         "destinationProjectID",
         "type",
         "lag",
         "lagUnit"
      }) public static class Link
      {

         @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlElement(name = "SourceTaskID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer sourceTaskID;
         @XmlElement(name = "DestinationTaskID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer destinationTaskID;
         @XmlElement(name = "SourceProjectID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer sourceProjectID;
         @XmlElement(name = "DestinationProjectID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer destinationProjectID;
         @XmlElement(name = "Type", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter9.class) protected RelationType type;
         @XmlElement(name = "Lag", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double lag;
         @XmlElement(name = "LagUnit", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter8.class) @XmlSchemaType(name = "int") protected TimeUnit lagUnit;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the sourceTaskID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getSourceTaskID()
         {
            return sourceTaskID;
         }

         /**
          * Sets the value of the sourceTaskID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSourceTaskID(Integer value)
         {
            this.sourceTaskID = value;
         }

         /**
          * Gets the value of the destinationTaskID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getDestinationTaskID()
         {
            return destinationTaskID;
         }

         /**
          * Sets the value of the destinationTaskID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDestinationTaskID(Integer value)
         {
            this.destinationTaskID = value;
         }

         /**
          * Gets the value of the sourceProjectID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getSourceProjectID()
         {
            return sourceProjectID;
         }

         /**
          * Sets the value of the sourceProjectID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSourceProjectID(Integer value)
         {
            this.sourceProjectID = value;
         }

         /**
          * Gets the value of the destinationProjectID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getDestinationProjectID()
         {
            return destinationProjectID;
         }

         /**
          * Sets the value of the destinationProjectID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDestinationProjectID(Integer value)
         {
            this.destinationProjectID = value;
         }

         /**
          * Gets the value of the type property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public RelationType getType()
         {
            return type;
         }

         /**
          * Sets the value of the type property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setType(RelationType value)
         {
            this.type = value;
         }

         /**
          * Gets the value of the lag property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getLag()
         {
            return lag;
         }

         /**
          * Sets the value of the lag property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setLag(Double value)
         {
            this.lag = value;
         }

         /**
          * Gets the value of the lagUnit property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public TimeUnit getLagUnit()
         {
            return lagUnit;
         }

         /**
          * Sets the value of the lagUnit property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setLagUnit(TimeUnit value)
         {
            this.lagUnit = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Marker" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="DisplayStyle" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "marker"
   }) public static class Markers
   {

      @XmlElement(name = "Marker", required = true) protected List<Document.Markers.Marker> marker;

      /**
       * Gets the value of the marker property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the marker property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getMarker().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Markers.Marker }
       *
       *
       */
      public List<Document.Markers.Marker> getMarker()
      {
         if (marker == null)
         {
            marker = new ArrayList<>();
         }
         return this.marker;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="DisplayStyle" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "id",
         "name",
         "displayStyle"
      }) public static class Marker
      {

         @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlElement(name = "Name", required = true) protected String name;
         @XmlElement(name = "DisplayStyle", required = true) protected String displayStyle;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the displayStyle property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getDisplayStyle()
         {
            return displayStyle;
         }

         /**
          * Sets the value of the displayStyle property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDisplayStyle(String value)
         {
            this.displayStyle = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;all&gt;
    *         &lt;element name="PrintOrientation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="MarginLeft" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="MarginRight" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="MarginTop" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="MarginBottom" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="MarginHeader" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="MarginFooter" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *         &lt;element name="FitTo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="PagesH" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="PagesV" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="TimescaleMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="DoPrintGrid" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
    *         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
    *         &lt;element name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="LegendMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="FirstPageNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="PrintView" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="HeaderFooterFontName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="HeaderFooterFontSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *       &lt;/all&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder = {

   }) public static class PrintingProperties
   {

      @XmlElement(name = "PrintOrientation", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer printOrientation;
      @XmlElement(name = "MarginLeft", required = true) protected BigDecimal marginLeft;
      @XmlElement(name = "MarginRight", required = true) protected BigDecimal marginRight;
      @XmlElement(name = "MarginTop", required = true) protected BigDecimal marginTop;
      @XmlElement(name = "MarginBottom", required = true) protected BigDecimal marginBottom;
      @XmlElement(name = "MarginHeader", required = true) protected BigDecimal marginHeader;
      @XmlElement(name = "MarginFooter", required = true) protected BigDecimal marginFooter;
      @XmlElement(name = "FitTo", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer fitTo;
      @XmlElement(name = "PagesH", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer pagesH;
      @XmlElement(name = "PagesV", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer pagesV;
      @XmlElement(name = "TimescaleMode", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer timescaleMode;
      @XmlElement(name = "DoPrintGrid") protected boolean doPrintGrid;
      @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "date") protected LocalDate startDate;
      @XmlElement(name = "FinishDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "date") protected LocalDate finishDate;
      @XmlElement(name = "HeaderLeft", required = true) protected String headerLeft;
      @XmlElement(name = "HeaderCenter", required = true) protected String headerCenter;
      @XmlElement(name = "HeaderRight", required = true) protected String headerRight;
      @XmlElement(name = "FooterLeft", required = true) protected String footerLeft;
      @XmlElement(name = "FooterCenter", required = true) protected String footerCenter;
      @XmlElement(name = "FooterRight", required = true) protected String footerRight;
      @XmlElement(name = "LegendMode", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer legendMode;
      @XmlElement(name = "FirstPageNumber", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer firstPageNumber;
      @XmlElement(name = "PrintView", required = true) protected String printView;
      @XmlElement(name = "HeaderFooterFontName", required = true) protected String headerFooterFontName;
      @XmlElement(name = "HeaderFooterFontSize", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer headerFooterFontSize;

      /**
       * Gets the value of the printOrientation property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getPrintOrientation()
      {
         return printOrientation;
      }

      /**
       * Sets the value of the printOrientation property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPrintOrientation(Integer value)
      {
         this.printOrientation = value;
      }

      /**
       * Gets the value of the marginLeft property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginLeft()
      {
         return marginLeft;
      }

      /**
       * Sets the value of the marginLeft property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginLeft(BigDecimal value)
      {
         this.marginLeft = value;
      }

      /**
       * Gets the value of the marginRight property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginRight()
      {
         return marginRight;
      }

      /**
       * Sets the value of the marginRight property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginRight(BigDecimal value)
      {
         this.marginRight = value;
      }

      /**
       * Gets the value of the marginTop property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginTop()
      {
         return marginTop;
      }

      /**
       * Sets the value of the marginTop property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginTop(BigDecimal value)
      {
         this.marginTop = value;
      }

      /**
       * Gets the value of the marginBottom property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginBottom()
      {
         return marginBottom;
      }

      /**
       * Sets the value of the marginBottom property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginBottom(BigDecimal value)
      {
         this.marginBottom = value;
      }

      /**
       * Gets the value of the marginHeader property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginHeader()
      {
         return marginHeader;
      }

      /**
       * Sets the value of the marginHeader property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginHeader(BigDecimal value)
      {
         this.marginHeader = value;
      }

      /**
       * Gets the value of the marginFooter property.
       *
       * @return
       *     possible object is
       *     {@link BigDecimal }
       *
       */
      public BigDecimal getMarginFooter()
      {
         return marginFooter;
      }

      /**
       * Sets the value of the marginFooter property.
       *
       * @param value
       *     allowed object is
       *     {@link BigDecimal }
       *
       */
      public void setMarginFooter(BigDecimal value)
      {
         this.marginFooter = value;
      }

      /**
       * Gets the value of the fitTo property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getFitTo()
      {
         return fitTo;
      }

      /**
       * Sets the value of the fitTo property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFitTo(Integer value)
      {
         this.fitTo = value;
      }

      /**
       * Gets the value of the pagesH property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getPagesH()
      {
         return pagesH;
      }

      /**
       * Sets the value of the pagesH property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPagesH(Integer value)
      {
         this.pagesH = value;
      }

      /**
       * Gets the value of the pagesV property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getPagesV()
      {
         return pagesV;
      }

      /**
       * Sets the value of the pagesV property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPagesV(Integer value)
      {
         this.pagesV = value;
      }

      /**
       * Gets the value of the timescaleMode property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getTimescaleMode()
      {
         return timescaleMode;
      }

      /**
       * Sets the value of the timescaleMode property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setTimescaleMode(Integer value)
      {
         this.timescaleMode = value;
      }

      /**
       * Gets the value of the doPrintGrid property.
       *
       */
      public boolean isDoPrintGrid()
      {
         return doPrintGrid;
      }

      /**
       * Sets the value of the doPrintGrid property.
       *
       */
      public void setDoPrintGrid(boolean value)
      {
         this.doPrintGrid = value;
      }

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStartDate(LocalDate value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the finishDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDate getFinishDate()
      {
         return finishDate;
      }

      /**
       * Sets the value of the finishDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFinishDate(LocalDate value)
      {
         this.finishDate = value;
      }

      /**
       * Gets the value of the headerLeft property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderLeft()
      {
         return headerLeft;
      }

      /**
       * Sets the value of the headerLeft property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderLeft(String value)
      {
         this.headerLeft = value;
      }

      /**
       * Gets the value of the headerCenter property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderCenter()
      {
         return headerCenter;
      }

      /**
       * Sets the value of the headerCenter property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderCenter(String value)
      {
         this.headerCenter = value;
      }

      /**
       * Gets the value of the headerRight property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderRight()
      {
         return headerRight;
      }

      /**
       * Sets the value of the headerRight property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderRight(String value)
      {
         this.headerRight = value;
      }

      /**
       * Gets the value of the footerLeft property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterLeft()
      {
         return footerLeft;
      }

      /**
       * Sets the value of the footerLeft property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterLeft(String value)
      {
         this.footerLeft = value;
      }

      /**
       * Gets the value of the footerCenter property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterCenter()
      {
         return footerCenter;
      }

      /**
       * Sets the value of the footerCenter property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterCenter(String value)
      {
         this.footerCenter = value;
      }

      /**
       * Gets the value of the footerRight property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getFooterRight()
      {
         return footerRight;
      }

      /**
       * Sets the value of the footerRight property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFooterRight(String value)
      {
         this.footerRight = value;
      }

      /**
       * Gets the value of the legendMode property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getLegendMode()
      {
         return legendMode;
      }

      /**
       * Sets the value of the legendMode property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLegendMode(Integer value)
      {
         this.legendMode = value;
      }

      /**
       * Gets the value of the firstPageNumber property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getFirstPageNumber()
      {
         return firstPageNumber;
      }

      /**
       * Sets the value of the firstPageNumber property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setFirstPageNumber(Integer value)
      {
         this.firstPageNumber = value;
      }

      /**
       * Gets the value of the printView property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getPrintView()
      {
         return printView;
      }

      /**
       * Sets the value of the printView property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPrintView(String value)
      {
         this.printView = value;
      }

      /**
       * Gets the value of the headerFooterFontName property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getHeaderFooterFontName()
      {
         return headerFooterFontName;
      }

      /**
       * Sets the value of the headerFooterFontName property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderFooterFontName(String value)
      {
         this.headerFooterFontName = value;
      }

      /**
       * Gets the value of the headerFooterFontSize property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getHeaderFooterFontSize()
      {
         return headerFooterFontSize;
      }

      /**
       * Sets the value of the headerFooterFontSize property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHeaderFooterFontSize(Integer value)
      {
         this.headerFooterFontSize = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="CompleteJournalTrackingPeriod" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}LongTimeUnitType"/&gt;
    *         &lt;element name="PPVItems" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}PPVItemsType"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "completeJournalTrackingPeriod",
      "ppvItems"
   }) public static class ProjectPortfolioView
   {

      @XmlElement(name = "CompleteJournalTrackingPeriod", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer completeJournalTrackingPeriod;
      @XmlElement(name = "PPVItems", required = true) protected PPVItemsType ppvItems;

      /**
       * Gets the value of the completeJournalTrackingPeriod property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getCompleteJournalTrackingPeriod()
      {
         return completeJournalTrackingPeriod;
      }

      /**
       * Sets the value of the completeJournalTrackingPeriod property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCompleteJournalTrackingPeriod(Integer value)
      {
         this.completeJournalTrackingPeriod = value;
      }

      /**
       * Gets the value of the ppvItems property.
       *
       * @return
       *     possible object is
       *     {@link PPVItemsType }
       *
       */
      public PPVItemsType getPPVItems()
      {
         return ppvItems;
      }

      /**
       * Sets the value of the ppvItems property.
       *
       * @param value
       *     allowed object is
       *     {@link PPVItemsType }
       *
       */
      public void setPPVItems(PPVItemsType value)
      {
         this.ppvItems = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
    *         &lt;element name="Project" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
    *                   &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ProjectProps"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
    *                   &lt;element name="Task" maxOccurs="unbounded" minOccurs="0"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
    *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                             &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                             &lt;element name="BaseStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                             &lt;element name="BaseFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                             &lt;element name="BaseDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
    *                             &lt;element name="BaseDurationTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
    *                             &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                             &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                             &lt;element name="ActualDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
    *                             &lt;element name="TemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
    *                             &lt;element name="DeadlineTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
    *                             &lt;element name="BaselineStartTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
    *                             &lt;element name="BaselineFinishTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
    *                             &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                             &lt;element name="Cost1" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                             &lt;element name="ValidatedByProject" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                             &lt;element name="RecalcBase1"&gt;
    *                               &lt;simpleType&gt;
    *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
    *                                   &lt;enumeration value="0"/&gt;
    *                                   &lt;enumeration value="1"/&gt;
    *                                   &lt;enumeration value="2"/&gt;
    *                                   &lt;enumeration value="3"/&gt;
    *                                 &lt;/restriction&gt;
    *                               &lt;/simpleType&gt;
    *                             &lt;/element&gt;
    *                             &lt;element name="RecalcBase2"&gt;
    *                               &lt;simpleType&gt;
    *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
    *                                   &lt;enumeration value="0"/&gt;
    *                                   &lt;enumeration value="1"/&gt;
    *                                   &lt;enumeration value="2"/&gt;
    *                                   &lt;enumeration value="3"/&gt;
    *                                 &lt;/restriction&gt;
    *                               &lt;/simpleType&gt;
    *                             &lt;/element&gt;
    *                             &lt;element name="IsMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                             &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}BaselineGroup" minOccurs="0"/&gt;
    *                             &lt;element name="Complete" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
    *                             &lt;element name="IsHaveDeadline" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                             &lt;element name="SchedulingType"&gt;
    *                               &lt;simpleType&gt;
    *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
    *                                   &lt;enumeration value="fixedDuration"/&gt;
    *                                   &lt;enumeration value="fixedUnits"/&gt;
    *                                   &lt;enumeration value="fixedWork"/&gt;
    *                                 &lt;/restriction&gt;
    *                               &lt;/simpleType&gt;
    *                             &lt;/element&gt;
    *                             &lt;element name="IsEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Priority"/&gt;
    *                             &lt;element name="MarkedByUser" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                             &lt;element name="ShowSubtasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
    *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
    *                             &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
    *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
    *                             &lt;element name="ResourceAssignments"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                                                 &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                                                 &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                                                 &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Callouts"/&gt;
    *                             &lt;element name="DeadlineDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "viewProperties",
      "project"
   }) public static class Projects
   {

      @XmlElement(name = "ViewProperties", required = true) protected ViewProperties viewProperties;
      @XmlElement(name = "Project", required = true) protected List<Document.Projects.Project> project;

      /**
       * Settings for multiproject grid view.
       *
       * @return
       *     possible object is
       *     {@link ViewProperties }
       *
       */
      public ViewProperties getViewProperties()
      {
         return viewProperties;
      }

      /**
       * Sets the value of the viewProperties property.
       *
       * @param value
       *     allowed object is
       *     {@link ViewProperties }
       *
       */
      public void setViewProperties(ViewProperties value)
      {
         this.viewProperties = value;
      }

      /**
       * Gets the value of the project property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the project property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getProject().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Projects.Project }
       *
       *
       */
      public List<Document.Projects.Project> getProject()
      {
         if (project == null)
         {
            project = new ArrayList<>();
         }
         return this.project;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
       *         &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ProjectProps"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
       *         &lt;element name="Task" maxOccurs="unbounded" minOccurs="0"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
       *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *                   &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *                   &lt;element name="BaseStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                   &lt;element name="BaseFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                   &lt;element name="BaseDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
       *                   &lt;element name="BaseDurationTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
       *                   &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                   &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                   &lt;element name="ActualDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
       *                   &lt;element name="TemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
       *                   &lt;element name="DeadlineTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
       *                   &lt;element name="BaselineStartTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
       *                   &lt;element name="BaselineFinishTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
       *                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *                   &lt;element name="Cost1" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *                   &lt;element name="ValidatedByProject" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *                   &lt;element name="RecalcBase1"&gt;
       *                     &lt;simpleType&gt;
       *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
       *                         &lt;enumeration value="0"/&gt;
       *                         &lt;enumeration value="1"/&gt;
       *                         &lt;enumeration value="2"/&gt;
       *                         &lt;enumeration value="3"/&gt;
       *                       &lt;/restriction&gt;
       *                     &lt;/simpleType&gt;
       *                   &lt;/element&gt;
       *                   &lt;element name="RecalcBase2"&gt;
       *                     &lt;simpleType&gt;
       *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
       *                         &lt;enumeration value="0"/&gt;
       *                         &lt;enumeration value="1"/&gt;
       *                         &lt;enumeration value="2"/&gt;
       *                         &lt;enumeration value="3"/&gt;
       *                       &lt;/restriction&gt;
       *                     &lt;/simpleType&gt;
       *                   &lt;/element&gt;
       *                   &lt;element name="IsMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                   &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}BaselineGroup" minOccurs="0"/&gt;
       *                   &lt;element name="Complete" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
       *                   &lt;element name="IsHaveDeadline" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                   &lt;element name="SchedulingType"&gt;
       *                     &lt;simpleType&gt;
       *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
       *                         &lt;enumeration value="fixedDuration"/&gt;
       *                         &lt;enumeration value="fixedUnits"/&gt;
       *                         &lt;enumeration value="fixedWork"/&gt;
       *                       &lt;/restriction&gt;
       *                     &lt;/simpleType&gt;
       *                   &lt;/element&gt;
       *                   &lt;element name="IsEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Priority"/&gt;
       *                   &lt;element name="MarkedByUser" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                   &lt;element name="ShowSubtasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
       *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
       *                   &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
       *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
       *                   &lt;element name="ResourceAssignments"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                                       &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                                       &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *                                       &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Callouts"/&gt;
       *                   &lt;element name="DeadlineDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "id",
         "outlineNumber",
         "name",
         "author",
         "company",
         "startDate",
         "finishDate",
         "budget",
         "goal",
         "site",
         "note",
         "baselineCost",
         "baselineStartDate",
         "baselineFinishDate",
         "showSubtasks",
         "priority",
         "isVisibleSubitems",
         "styleProject",
         "markerID",
         "hyperlinks",
         "viewProperties",
         "timeScale",
         "task"
      }) public static class Project
      {

         @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlElement(name = "OutlineNumber", required = true) protected String outlineNumber;
         @XmlElement(name = "Name", required = true) protected String name;
         @XmlElement(name = "Author", required = true) protected String author;
         @XmlElement(name = "Company", required = true) protected String company;
         @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
         @XmlElement(name = "FinishDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime finishDate;
         @XmlElement(name = "Budget", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double budget;
         @XmlElement(name = "Goal", required = true) protected String goal;
         @XmlElement(name = "Site", required = true) protected String site;
         @XmlElement(name = "Note", required = true) protected String note;
         @XmlElement(name = "BaselineCost", type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double baselineCost;
         @XmlElement(name = "BaselineStartDate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineStartDate;
         @XmlElement(name = "BaselineFinishDate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineFinishDate;
         @XmlElement(name = "ShowSubtasks") protected Boolean showSubtasks;
         @XmlElement(name = "Priority", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter7.class) protected Priority priority;
         @XmlElement(name = "IsVisibleSubitems") protected Boolean isVisibleSubitems;
         @XmlElement(name = "StyleProject", required = true) protected StyleProject styleProject;
         @XmlElement(name = "MarkerID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer markerID;
         @XmlElement(name = "Hyperlinks", required = true) protected Hyperlinks hyperlinks;
         @XmlElement(name = "ViewProperties", required = true) protected ViewProperties viewProperties;
         @XmlElement(name = "TimeScale", required = true) protected TimeScale timeScale;
         @XmlElement(name = "Task") protected List<Document.Projects.Project.Task> task;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * The outline number of the project. Indicates the exact
          *                           position of a project in the outline. For example, "7.2" indicates that a
          *                           project is the second subproject under the seventh top-level project
          *                           group.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getOutlineNumber()
         {
            return outlineNumber;
         }

         /**
          * Sets the value of the outlineNumber property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setOutlineNumber(String value)
         {
            this.outlineNumber = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the author property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getAuthor()
         {
            return author;
         }

         /**
          * Sets the value of the author property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setAuthor(String value)
         {
            this.author = value;
         }

         /**
          * Gets the value of the company property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getCompany()
         {
            return company;
         }

         /**
          * Sets the value of the company property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCompany(String value)
         {
            this.company = value;
         }

         /**
          * Gets the value of the startDate property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getStartDate()
         {
            return startDate;
         }

         /**
          * Sets the value of the startDate property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setStartDate(LocalDateTime value)
         {
            this.startDate = value;
         }

         /**
          * Gets the value of the finishDate property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getFinishDate()
         {
            return finishDate;
         }

         /**
          * Sets the value of the finishDate property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setFinishDate(LocalDateTime value)
         {
            this.finishDate = value;
         }

         /**
          * Gets the value of the budget property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getBudget()
         {
            return budget;
         }

         /**
          * Sets the value of the budget property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBudget(Double value)
         {
            this.budget = value;
         }

         /**
          * Gets the value of the goal property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getGoal()
         {
            return goal;
         }

         /**
          * Sets the value of the goal property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setGoal(String value)
         {
            this.goal = value;
         }

         /**
          * Gets the value of the site property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getSite()
         {
            return site;
         }

         /**
          * Sets the value of the site property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSite(String value)
         {
            this.site = value;
         }

         /**
          * Gets the value of the note property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getNote()
         {
            return note;
         }

         /**
          * Sets the value of the note property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setNote(String value)
         {
            this.note = value;
         }

         /**
          * Gets the value of the baselineCost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getBaselineCost()
         {
            return baselineCost;
         }

         /**
          * Sets the value of the baselineCost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaselineCost(Double value)
         {
            this.baselineCost = value;
         }

         /**
          * Gets the value of the baselineStartDate property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getBaselineStartDate()
         {
            return baselineStartDate;
         }

         /**
          * Sets the value of the baselineStartDate property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaselineStartDate(LocalDateTime value)
         {
            this.baselineStartDate = value;
         }

         /**
          * Gets the value of the baselineFinishDate property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getBaselineFinishDate()
         {
            return baselineFinishDate;
         }

         /**
          * Sets the value of the baselineFinishDate property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaselineFinishDate(LocalDateTime value)
         {
            this.baselineFinishDate = value;
         }

         /**
          * Gets the value of the showSubtasks property.
          *
          * @return
          *     possible object is
          *     {@link Boolean }
          *
          */
         public Boolean isShowSubtasks()
         {
            return showSubtasks;
         }

         /**
          * Sets the value of the showSubtasks property.
          *
          * @param value
          *     allowed object is
          *     {@link Boolean }
          *
          */
         public void setShowSubtasks(Boolean value)
         {
            this.showSubtasks = value;
         }

         /**
          * The priority of the project.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Priority getPriority()
         {
            return priority;
         }

         /**
          * Sets the value of the priority property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setPriority(Priority value)
         {
            this.priority = value;
         }

         /**
          * Gets the value of the isVisibleSubitems property.
          *
          * @return
          *     possible object is
          *     {@link Boolean }
          *
          */
         public Boolean isIsVisibleSubitems()
         {
            return isVisibleSubitems;
         }

         /**
          * Sets the value of the isVisibleSubitems property.
          *
          * @param value
          *     allowed object is
          *     {@link Boolean }
          *
          */
         public void setIsVisibleSubitems(Boolean value)
         {
            this.isVisibleSubitems = value;
         }

         /**
          * Gets the value of the styleProject property.
          *
          * @return
          *     possible object is
          *     {@link StyleProject }
          *
          */
         public StyleProject getStyleProject()
         {
            return styleProject;
         }

         /**
          * Sets the value of the styleProject property.
          *
          * @param value
          *     allowed object is
          *     {@link StyleProject }
          *
          */
         public void setStyleProject(StyleProject value)
         {
            this.styleProject = value;
         }

         /**
          * Gets the value of the markerID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getMarkerID()
         {
            return markerID;
         }

         /**
          * Sets the value of the markerID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMarkerID(Integer value)
         {
            this.markerID = value;
         }

         /**
          * Hyperlinks associated with the project.
          *
          * @return
          *     possible object is
          *     {@link Hyperlinks }
          *
          */
         public Hyperlinks getHyperlinks()
         {
            return hyperlinks;
         }

         /**
          * Sets the value of the hyperlinks property.
          *
          * @param value
          *     allowed object is
          *     {@link Hyperlinks }
          *
          */
         public void setHyperlinks(Hyperlinks value)
         {
            this.hyperlinks = value;
         }

         /**
          * Settings for project grid view.
          *
          * @return
          *     possible object is
          *     {@link ViewProperties }
          *
          */
         public ViewProperties getViewProperties()
         {
            return viewProperties;
         }

         /**
          * Sets the value of the viewProperties property.
          *
          * @param value
          *     allowed object is
          *     {@link ViewProperties }
          *
          */
         public void setViewProperties(ViewProperties value)
         {
            this.viewProperties = value;
         }

         /**
          * Settings for project view timescale.
          *
          * @return
          *     possible object is
          *     {@link TimeScale }
          *
          */
         public TimeScale getTimeScale()
         {
            return timeScale;
         }

         /**
          * Sets the value of the timeScale property.
          *
          * @param value
          *     allowed object is
          *     {@link TimeScale }
          *
          */
         public void setTimeScale(TimeScale value)
         {
            this.timeScale = value;
         }

         /**
          * Gets the value of the task property.
          *
          * <p>
          * This accessor method returns a reference to the live list,
          * not a snapshot. Therefore any modification you make to the
          * returned list will be present inside the Jakarta XML Binding object.
          * This is why there is not a <CODE>set</CODE> method for the task property.
          *
          * <p>
          * For example, to add a new item, do as follows:
          * <pre>
          *    getTask().add(newItem);
          * </pre>
          *
          *
          * <p>
          * Objects of the following type(s) are allowed in the list
          * {@link Document.Projects.Project.Task }
          *
          *
          */
         public List<Document.Projects.Project.Task> getTask()
         {
            if (task == null)
            {
               task = new ArrayList<>();
            }
            return this.task;
         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
          *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
          *         &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
          *         &lt;element name="BaseStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *         &lt;element name="BaseFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *         &lt;element name="BaseDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
          *         &lt;element name="BaseDurationTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
          *         &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *         &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *         &lt;element name="ActualDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
          *         &lt;element name="TemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
          *         &lt;element name="DeadlineTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
          *         &lt;element name="BaselineStartTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
          *         &lt;element name="BaselineFinishTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
          *         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
          *         &lt;element name="Cost1" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
          *         &lt;element name="ValidatedByProject" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
          *         &lt;element name="RecalcBase1"&gt;
          *           &lt;simpleType&gt;
          *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
          *               &lt;enumeration value="0"/&gt;
          *               &lt;enumeration value="1"/&gt;
          *               &lt;enumeration value="2"/&gt;
          *               &lt;enumeration value="3"/&gt;
          *             &lt;/restriction&gt;
          *           &lt;/simpleType&gt;
          *         &lt;/element&gt;
          *         &lt;element name="RecalcBase2"&gt;
          *           &lt;simpleType&gt;
          *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
          *               &lt;enumeration value="0"/&gt;
          *               &lt;enumeration value="1"/&gt;
          *               &lt;enumeration value="2"/&gt;
          *               &lt;enumeration value="3"/&gt;
          *             &lt;/restriction&gt;
          *           &lt;/simpleType&gt;
          *         &lt;/element&gt;
          *         &lt;element name="IsMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *         &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}BaselineGroup" minOccurs="0"/&gt;
          *         &lt;element name="Complete" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
          *         &lt;element name="IsHaveDeadline" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *         &lt;element name="SchedulingType"&gt;
          *           &lt;simpleType&gt;
          *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
          *               &lt;enumeration value="fixedDuration"/&gt;
          *               &lt;enumeration value="fixedUnits"/&gt;
          *               &lt;enumeration value="fixedWork"/&gt;
          *             &lt;/restriction&gt;
          *           &lt;/simpleType&gt;
          *         &lt;/element&gt;
          *         &lt;element name="IsEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Priority"/&gt;
          *         &lt;element name="MarkedByUser" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *         &lt;element name="ShowSubtasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
          *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
          *         &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
          *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
          *         &lt;element name="ResourceAssignments"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *                             &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *                             &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
          *                             &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Callouts"/&gt;
          *         &lt;element name="DeadlineDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "id",
            "outlineNumber",
            "name",
            "note",
            "baseStartDate",
            "baseFinishDate",
            "baseDuration",
            "baseDurationTimeUnit",
            "actualStartDate",
            "actualFinishDate",
            "actualDuration",
            "templateOffset",
            "deadlineTemplateOffset",
            "baselineStartTemplateOffset",
            "baselineFinishTemplateOffset",
            "actualCost",
            "cost1",
            "validatedByProject",
            "recalcBase1",
            "recalcBase2",
            "isMilestone",
            "baselineCost",
            "baselineStartDate",
            "baselineFinishDate",
            "complete",
            "isHaveDeadline",
            "schedulingType",
            "isEffortDriven",
            "priority",
            "markedByUser",
            "showSubtasks",
            "styleProject",
            "markerID",
            "hyperlinks",
            "resourceAssignments",
            "callouts",
            "deadlineDate"
         }) public static class Task
         {

            @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
            @XmlElement(name = "OutlineNumber", required = true) protected String outlineNumber;
            @XmlElement(name = "Name", required = true) protected String name;
            @XmlElement(name = "Note", required = true) protected String note;
            @XmlElement(name = "BaseStartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseStartDate;
            @XmlElement(name = "BaseFinishDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baseFinishDate;
            @XmlElement(name = "BaseDuration", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double baseDuration;
            @XmlElement(name = "BaseDurationTimeUnit", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter8.class) @XmlSchemaType(name = "int") protected TimeUnit baseDurationTimeUnit;
            @XmlElement(name = "ActualStartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime actualStartDate;
            @XmlElement(name = "ActualFinishDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime actualFinishDate;
            @XmlElement(name = "ActualDuration", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double actualDuration;
            @XmlElement(name = "TemplateOffset") protected List<String> templateOffset;
            @XmlElement(name = "DeadlineTemplateOffset") protected List<String> deadlineTemplateOffset;
            @XmlElement(name = "BaselineStartTemplateOffset") protected List<String> baselineStartTemplateOffset;
            @XmlElement(name = "BaselineFinishTemplateOffset") protected List<String> baselineFinishTemplateOffset;
            @XmlElement(name = "ActualCost", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double actualCost;
            @XmlElement(name = "Cost1", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double cost1;
            @XmlElement(name = "ValidatedByProject", required = true) protected String validatedByProject;
            @XmlElement(name = "RecalcBase1", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) protected Integer recalcBase1;
            @XmlElement(name = "RecalcBase2", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) protected Integer recalcBase2;
            @XmlElement(name = "IsMilestone") protected boolean isMilestone;
            @XmlElement(name = "BaselineCost", type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double baselineCost;
            @XmlElement(name = "BaselineStartDate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineStartDate;
            @XmlElement(name = "BaselineFinishDate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime baselineFinishDate;
            @XmlElement(name = "Complete", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter10.class) @XmlSchemaType(name = "decimal") protected Double complete;
            @XmlElement(name = "IsHaveDeadline") protected boolean isHaveDeadline;
            @XmlElement(name = "SchedulingType", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter11.class) protected TaskType schedulingType;
            @XmlElement(name = "IsEffortDriven") protected boolean isEffortDriven;
            @XmlElement(name = "Priority", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter7.class) protected Priority priority;
            @XmlElement(name = "MarkedByUser") protected boolean markedByUser;
            @XmlElement(name = "ShowSubtasks") protected Boolean showSubtasks;
            @XmlElement(name = "StyleProject", required = true) protected StyleProject styleProject;
            @XmlElement(name = "MarkerID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer markerID;
            @XmlElement(name = "Hyperlinks", required = true) protected Hyperlinks hyperlinks;
            @XmlElement(name = "ResourceAssignments", required = true) protected Document.Projects.Project.Task.ResourceAssignments resourceAssignments;
            @XmlElement(name = "Callouts", required = true) protected Callouts callouts;
            @XmlElement(name = "DeadlineDate", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime deadlineDate;

            /**
             * Gets the value of the id property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getID()
            {
               return id;
            }

            /**
             * Sets the value of the id property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setID(Integer value)
            {
               this.id = value;
            }

            /**
             * The outline number of the task. Indicates the exact
             *                                 position of a task in the outline. For example, "7.2" indicates that
             *                                 a task is the second subtask under the seventh top-level summary
             *                                 task.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getOutlineNumber()
            {
               return outlineNumber;
            }

            /**
             * Sets the value of the outlineNumber property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setOutlineNumber(String value)
            {
               this.outlineNumber = value;
            }

            /**
             * Gets the value of the name property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getName()
            {
               return name;
            }

            /**
             * Sets the value of the name property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setName(String value)
            {
               this.name = value;
            }

            /**
             * Gets the value of the note property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getNote()
            {
               return note;
            }

            /**
             * Sets the value of the note property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setNote(String value)
            {
               this.note = value;
            }

            /**
             * Gets the value of the baseStartDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getBaseStartDate()
            {
               return baseStartDate;
            }

            /**
             * Sets the value of the baseStartDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaseStartDate(LocalDateTime value)
            {
               this.baseStartDate = value;
            }

            /**
             * Gets the value of the baseFinishDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getBaseFinishDate()
            {
               return baseFinishDate;
            }

            /**
             * Sets the value of the baseFinishDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaseFinishDate(LocalDateTime value)
            {
               this.baseFinishDate = value;
            }

            /**
             * Gets the value of the baseDuration property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getBaseDuration()
            {
               return baseDuration;
            }

            /**
             * Sets the value of the baseDuration property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaseDuration(Double value)
            {
               this.baseDuration = value;
            }

            /**
             * Gets the value of the baseDurationTimeUnit property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public TimeUnit getBaseDurationTimeUnit()
            {
               return baseDurationTimeUnit;
            }

            /**
             * Sets the value of the baseDurationTimeUnit property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaseDurationTimeUnit(TimeUnit value)
            {
               this.baseDurationTimeUnit = value;
            }

            /**
             * Gets the value of the actualStartDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getActualStartDate()
            {
               return actualStartDate;
            }

            /**
             * Sets the value of the actualStartDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setActualStartDate(LocalDateTime value)
            {
               this.actualStartDate = value;
            }

            /**
             * Gets the value of the actualFinishDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getActualFinishDate()
            {
               return actualFinishDate;
            }

            /**
             * Sets the value of the actualFinishDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setActualFinishDate(LocalDateTime value)
            {
               this.actualFinishDate = value;
            }

            /**
             * Gets the value of the actualDuration property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getActualDuration()
            {
               return actualDuration;
            }

            /**
             * Sets the value of the actualDuration property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setActualDuration(Double value)
            {
               this.actualDuration = value;
            }

            /**
             * Gets the value of the templateOffset property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the templateOffset property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getTemplateOffset().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             *
             *
             */
            public List<String> getTemplateOffset()
            {
               if (templateOffset == null)
               {
                  templateOffset = new ArrayList<>();
               }
               return this.templateOffset;
            }

            /**
             * Gets the value of the deadlineTemplateOffset property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the deadlineTemplateOffset property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getDeadlineTemplateOffset().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             *
             *
             */
            public List<String> getDeadlineTemplateOffset()
            {
               if (deadlineTemplateOffset == null)
               {
                  deadlineTemplateOffset = new ArrayList<>();
               }
               return this.deadlineTemplateOffset;
            }

            /**
             * Gets the value of the baselineStartTemplateOffset property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the baselineStartTemplateOffset property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getBaselineStartTemplateOffset().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             *
             *
             */
            public List<String> getBaselineStartTemplateOffset()
            {
               if (baselineStartTemplateOffset == null)
               {
                  baselineStartTemplateOffset = new ArrayList<>();
               }
               return this.baselineStartTemplateOffset;
            }

            /**
             * Gets the value of the baselineFinishTemplateOffset property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the baselineFinishTemplateOffset property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getBaselineFinishTemplateOffset().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link String }
             *
             *
             */
            public List<String> getBaselineFinishTemplateOffset()
            {
               if (baselineFinishTemplateOffset == null)
               {
                  baselineFinishTemplateOffset = new ArrayList<>();
               }
               return this.baselineFinishTemplateOffset;
            }

            /**
             * Gets the value of the actualCost property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getActualCost()
            {
               return actualCost;
            }

            /**
             * Sets the value of the actualCost property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setActualCost(Double value)
            {
               this.actualCost = value;
            }

            /**
             * Gets the value of the cost1 property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getCost1()
            {
               return cost1;
            }

            /**
             * Sets the value of the cost1 property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setCost1(Double value)
            {
               this.cost1 = value;
            }

            /**
             * Gets the value of the validatedByProject property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getValidatedByProject()
            {
               return validatedByProject;
            }

            /**
             * Sets the value of the validatedByProject property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setValidatedByProject(String value)
            {
               this.validatedByProject = value;
            }

            /**
             * Gets the value of the recalcBase1 property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getRecalcBase1()
            {
               return recalcBase1;
            }

            /**
             * Sets the value of the recalcBase1 property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setRecalcBase1(Integer value)
            {
               this.recalcBase1 = value;
            }

            /**
             * Gets the value of the recalcBase2 property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getRecalcBase2()
            {
               return recalcBase2;
            }

            /**
             * Sets the value of the recalcBase2 property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setRecalcBase2(Integer value)
            {
               this.recalcBase2 = value;
            }

            /**
             * Gets the value of the isMilestone property.
             *
             */
            public boolean isIsMilestone()
            {
               return isMilestone;
            }

            /**
             * Sets the value of the isMilestone property.
             *
             */
            public void setIsMilestone(boolean value)
            {
               this.isMilestone = value;
            }

            /**
             * Gets the value of the baselineCost property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getBaselineCost()
            {
               return baselineCost;
            }

            /**
             * Sets the value of the baselineCost property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaselineCost(Double value)
            {
               this.baselineCost = value;
            }

            /**
             * Gets the value of the baselineStartDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getBaselineStartDate()
            {
               return baselineStartDate;
            }

            /**
             * Sets the value of the baselineStartDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaselineStartDate(LocalDateTime value)
            {
               this.baselineStartDate = value;
            }

            /**
             * Gets the value of the baselineFinishDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getBaselineFinishDate()
            {
               return baselineFinishDate;
            }

            /**
             * Sets the value of the baselineFinishDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBaselineFinishDate(LocalDateTime value)
            {
               this.baselineFinishDate = value;
            }

            /**
             * Gets the value of the complete property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Double getComplete()
            {
               return complete;
            }

            /**
             * Sets the value of the complete property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setComplete(Double value)
            {
               this.complete = value;
            }

            /**
             * Gets the value of the isHaveDeadline property.
             *
             */
            public boolean isIsHaveDeadline()
            {
               return isHaveDeadline;
            }

            /**
             * Sets the value of the isHaveDeadline property.
             *
             */
            public void setIsHaveDeadline(boolean value)
            {
               this.isHaveDeadline = value;
            }

            /**
             * Gets the value of the schedulingType property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public TaskType getSchedulingType()
            {
               return schedulingType;
            }

            /**
             * Sets the value of the schedulingType property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setSchedulingType(TaskType value)
            {
               this.schedulingType = value;
            }

            /**
             * Gets the value of the isEffortDriven property.
             *
             */
            public boolean isIsEffortDriven()
            {
               return isEffortDriven;
            }

            /**
             * Sets the value of the isEffortDriven property.
             *
             */
            public void setIsEffortDriven(boolean value)
            {
               this.isEffortDriven = value;
            }

            /**
             * The priority of the task.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Priority getPriority()
            {
               return priority;
            }

            /**
             * Sets the value of the priority property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setPriority(Priority value)
            {
               this.priority = value;
            }

            /**
             * Gets the value of the markedByUser property.
             *
             */
            public boolean isMarkedByUser()
            {
               return markedByUser;
            }

            /**
             * Sets the value of the markedByUser property.
             *
             */
            public void setMarkedByUser(boolean value)
            {
               this.markedByUser = value;
            }

            /**
             * Gets the value of the showSubtasks property.
             *
             * @return
             *     possible object is
             *     {@link Boolean }
             *
             */
            public Boolean isShowSubtasks()
            {
               return showSubtasks;
            }

            /**
             * Sets the value of the showSubtasks property.
             *
             * @param value
             *     allowed object is
             *     {@link Boolean }
             *
             */
            public void setShowSubtasks(Boolean value)
            {
               this.showSubtasks = value;
            }

            /**
             * Gets the value of the styleProject property.
             *
             * @return
             *     possible object is
             *     {@link StyleProject }
             *
             */
            public StyleProject getStyleProject()
            {
               return styleProject;
            }

            /**
             * Sets the value of the styleProject property.
             *
             * @param value
             *     allowed object is
             *     {@link StyleProject }
             *
             */
            public void setStyleProject(StyleProject value)
            {
               this.styleProject = value;
            }

            /**
             * Gets the value of the markerID property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getMarkerID()
            {
               return markerID;
            }

            /**
             * Sets the value of the markerID property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setMarkerID(Integer value)
            {
               this.markerID = value;
            }

            /**
             * Hyperlinks associated with the task.
             *
             *
             * @return
             *     possible object is
             *     {@link Hyperlinks }
             *
             */
            public Hyperlinks getHyperlinks()
            {
               return hyperlinks;
            }

            /**
             * Sets the value of the hyperlinks property.
             *
             * @param value
             *     allowed object is
             *     {@link Hyperlinks }
             *
             */
            public void setHyperlinks(Hyperlinks value)
            {
               this.hyperlinks = value;
            }

            /**
             * Gets the value of the resourceAssignments property.
             *
             * @return
             *     possible object is
             *     {@link Document.Projects.Project.Task.ResourceAssignments }
             *
             */
            public Document.Projects.Project.Task.ResourceAssignments getResourceAssignments()
            {
               return resourceAssignments;
            }

            /**
             * Sets the value of the resourceAssignments property.
             *
             * @param value
             *     allowed object is
             *     {@link Document.Projects.Project.Task.ResourceAssignments }
             *
             */
            public void setResourceAssignments(Document.Projects.Project.Task.ResourceAssignments value)
            {
               this.resourceAssignments = value;
            }

            /**
             * Gets the value of the callouts property.
             *
             * @return
             *     possible object is
             *     {@link Callouts }
             *
             */
            public Callouts getCallouts()
            {
               return callouts;
            }

            /**
             * Sets the value of the callouts property.
             *
             * @param value
             *     allowed object is
             *     {@link Callouts }
             *
             */
            public void setCallouts(Callouts value)
            {
               this.callouts = value;
            }

            /**
             * Gets the value of the deadlineDate property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public LocalDateTime getDeadlineDate()
            {
               return deadlineDate;
            }

            /**
             * Sets the value of the deadlineDate property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setDeadlineDate(LocalDateTime value)
            {
               this.deadlineDate = value;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
             *                   &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
             *                   &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
             *                   &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "resourceAssignment"
            }) public static class ResourceAssignments
            {

               @XmlElement(name = "ResourceAssignment") protected List<Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment> resourceAssignment;

               /**
                * Gets the value of the resourceAssignment property.
                *
                * <p>
                * This accessor method returns a reference to the live list,
                * not a snapshot. Therefore any modification you make to the
                * returned list will be present inside the Jakarta XML Binding object.
                * This is why there is not a <CODE>set</CODE> method for the resourceAssignment property.
                *
                * <p>
                * For example, to add a new item, do as follows:
                * <pre>
                *    getResourceAssignment().add(newItem);
                * </pre>
                *
                *
                * <p>
                * Objects of the following type(s) are allowed in the list
                * {@link Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment }
                *
                *
                */
               public List<Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment> getResourceAssignment()
               {
                  if (resourceAssignment == null)
                  {
                     resourceAssignment = new ArrayList<>();
                  }
                  return this.resourceAssignment;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                *         &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                *         &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                *         &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "id",
                  "resourceID",
                  "use",
                  "manHour"
               }) public static class ResourceAssignment
               {

                  @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
                  @XmlElement(name = "ResourceID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer resourceID;
                  @XmlElement(name = "Use", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double use;
                  @XmlElement(name = "ManHour", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double manHour;

                  /**
                   * Gets the value of the id property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Integer getID()
                  {
                     return id;
                  }

                  /**
                   * Sets the value of the id property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setID(Integer value)
                  {
                     this.id = value;
                  }

                  /**
                   * Gets the value of the resourceID property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Integer getResourceID()
                  {
                     return resourceID;
                  }

                  /**
                   * Sets the value of the resourceID property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setResourceID(Integer value)
                  {
                     this.resourceID = value;
                  }

                  /**
                   * Gets the value of the use property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Double getUse()
                  {
                     return use;
                  }

                  /**
                   * Sets the value of the use property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setUse(Double value)
                  {
                     this.use = value;
                  }

                  /**
                   * Gets the value of the manHour property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Double getManHour()
                  {
                     return manHour;
                  }

                  /**
                   * Sets the value of the manHour property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setManHour(Double value)
                  {
                     this.manHour = value;
                  }

               }

            }

         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
    *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
    *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ActiveFilter" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "timeScale",
      "viewProperties",
      "activeFilter"
   }) public static class ResourceUsageDiagram
   {

      @XmlElement(name = "TimeScale", required = true) protected TimeScale timeScale;
      @XmlElement(name = "ViewProperties", required = true) protected ViewProperties viewProperties;
      @XmlElement(name = "ActiveFilter") protected ActiveFilter activeFilter;

      /**
       * Gets the value of the timeScale property.
       *
       * @return
       *     possible object is
       *     {@link TimeScale }
       *
       */
      public TimeScale getTimeScale()
      {
         return timeScale;
      }

      /**
       * Sets the value of the timeScale property.
       *
       * @param value
       *     allowed object is
       *     {@link TimeScale }
       *
       */
      public void setTimeScale(TimeScale value)
      {
         this.timeScale = value;
      }

      /**
       * Settings for resource usage grid view.
       *
       * @return
       *     possible object is
       *     {@link ViewProperties }
       *
       */
      public ViewProperties getViewProperties()
      {
         return viewProperties;
      }

      /**
       * Sets the value of the viewProperties property.
       *
       * @param value
       *     allowed object is
       *     {@link ViewProperties }
       *
       */
      public void setViewProperties(ViewProperties value)
      {
         this.viewProperties = value;
      }

      /**
       * Gets the value of the activeFilter property.
       *
       * @return
       *     possible object is
       *     {@link ActiveFilter }
       *
       */
      public ActiveFilter getActiveFilter()
      {
         return activeFilter;
      }

      /**
       * Sets the value of the activeFilter property.
       *
       * @param value
       *     allowed object is
       *     {@link ActiveFilter }
       *
       */
      public void setActiveFilter(ActiveFilter value)
      {
         this.activeFilter = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
    *         &lt;element name="Resource" maxOccurs="unbounded" minOccurs="0"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
    *                   &lt;element name="CalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="Type"&gt;
    *                     &lt;simpleType&gt;
    *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
    *                         &lt;enumeration value="0"/&gt;
    *                         &lt;enumeration value="1"/&gt;
    *                       &lt;/restriction&gt;
    *                     &lt;/simpleType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="SubType"&gt;
    *                     &lt;simpleType&gt;
    *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
    *                         &lt;enumeration value="work"/&gt;
    *                         &lt;enumeration value="material"/&gt;
    *                         &lt;enumeration value="cost"/&gt;
    *                         &lt;enumeration value="equipment"/&gt;
    *                         &lt;enumeration value="company"/&gt;
    *                       &lt;/restriction&gt;
    *                     &lt;/simpleType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="EMail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *                   &lt;element name="CostTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
    *                   &lt;element name="Group" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="ShowAssignedTasks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
    *                   &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
    *                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "viewProperties",
      "resource"
   }) public static class Resources
   {

      @XmlElement(name = "ViewProperties", required = true) protected ViewProperties viewProperties;
      @XmlElement(name = "Resource") protected List<Document.Resources.Resource> resource;

      /**
       * Settings for resources grid view.
       *
       * @return
       *     possible object is
       *     {@link ViewProperties }
       *
       */
      public ViewProperties getViewProperties()
      {
         return viewProperties;
      }

      /**
       * Sets the value of the viewProperties property.
       *
       * @param value
       *     allowed object is
       *     {@link ViewProperties }
       *
       */
      public void setViewProperties(ViewProperties value)
      {
         this.viewProperties = value;
      }

      /**
       * Gets the value of the resource property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the resource property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getResource().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Resources.Resource }
       *
       *
       */
      public List<Document.Resources.Resource> getResource()
      {
         if (resource == null)
         {
            resource = new ArrayList<>();
         }
         return this.resource;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
       *         &lt;element name="CalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="Type"&gt;
       *           &lt;simpleType&gt;
       *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
       *               &lt;enumeration value="0"/&gt;
       *               &lt;enumeration value="1"/&gt;
       *             &lt;/restriction&gt;
       *           &lt;/simpleType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="SubType"&gt;
       *           &lt;simpleType&gt;
       *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
       *               &lt;enumeration value="work"/&gt;
       *               &lt;enumeration value="material"/&gt;
       *               &lt;enumeration value="cost"/&gt;
       *               &lt;enumeration value="equipment"/&gt;
       *               &lt;enumeration value="company"/&gt;
       *             &lt;/restriction&gt;
       *           &lt;/simpleType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="EMail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
       *         &lt;element name="CostTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
       *         &lt;element name="Group" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="ShowAssignedTasks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
       *         &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
       *         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "id",
         "outlineNumber",
         "calendarID",
         "name",
         "type",
         "subType",
         "eMail",
         "note",
         "cost",
         "costTimeUnit",
         "group",
         "showAssignedTasks",
         "styleProject",
         "markerID",
         "hyperlinks"
      }) public static class Resource
      {

         @XmlElement(name = "ID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlElement(name = "OutlineNumber", required = true) protected String outlineNumber;
         @XmlElement(name = "CalendarID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer calendarID;
         @XmlElement(name = "Name", required = true) protected String name;
         @XmlElement(name = "Type", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter12.class) protected ResourceType type;
         @XmlElement(name = "SubType", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter13.class) protected ResourceType subType;
         @XmlElement(name = "EMail", required = true) protected String eMail;
         @XmlElement(name = "Note", required = true) protected String note;
         @XmlElement(name = "Cost", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double cost;
         @XmlElement(name = "CostTimeUnit", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter8.class) @XmlSchemaType(name = "int") protected TimeUnit costTimeUnit;
         @XmlElement(name = "Group", required = true) protected String group;
         @XmlElement(name = "ShowAssignedTasks") protected boolean showAssignedTasks;
         @XmlElement(name = "StyleProject", required = true) protected StyleProject styleProject;
         @XmlElement(name = "MarkerID", type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer markerID;
         @XmlElement(name = "Hyperlinks", required = true) protected Hyperlinks hyperlinks;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * The outline number of the resource.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getOutlineNumber()
         {
            return outlineNumber;
         }

         /**
          * Sets the value of the outlineNumber property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setOutlineNumber(String value)
         {
            this.outlineNumber = value;
         }

         /**
          * Gets the value of the calendarID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getCalendarID()
         {
            return calendarID;
         }

         /**
          * Sets the value of the calendarID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCalendarID(Integer value)
         {
            this.calendarID = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the type property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public ResourceType getType()
         {
            return type;
         }

         /**
          * Sets the value of the type property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setType(ResourceType value)
         {
            this.type = value;
         }

         /**
          * Gets the value of the subType property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public ResourceType getSubType()
         {
            return subType;
         }

         /**
          * Sets the value of the subType property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSubType(ResourceType value)
         {
            this.subType = value;
         }

         /**
          * Gets the value of the eMail property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getEMail()
         {
            return eMail;
         }

         /**
          * Sets the value of the eMail property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setEMail(String value)
         {
            this.eMail = value;
         }

         /**
          * Gets the value of the note property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getNote()
         {
            return note;
         }

         /**
          * Sets the value of the note property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setNote(String value)
         {
            this.note = value;
         }

         /**
          * Gets the value of the cost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getCost()
         {
            return cost;
         }

         /**
          * Sets the value of the cost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCost(Double value)
         {
            this.cost = value;
         }

         /**
          * Gets the value of the costTimeUnit property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public TimeUnit getCostTimeUnit()
         {
            return costTimeUnit;
         }

         /**
          * Sets the value of the costTimeUnit property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCostTimeUnit(TimeUnit value)
         {
            this.costTimeUnit = value;
         }

         /**
          * Gets the value of the group property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getGroup()
         {
            return group;
         }

         /**
          * Sets the value of the group property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setGroup(String value)
         {
            this.group = value;
         }

         /**
          * Gets the value of the showAssignedTasks property.
          *
          */
         public boolean isShowAssignedTasks()
         {
            return showAssignedTasks;
         }

         /**
          * Sets the value of the showAssignedTasks property.
          *
          */
         public void setShowAssignedTasks(boolean value)
         {
            this.showAssignedTasks = value;
         }

         /**
          * Gets the value of the styleProject property.
          *
          * @return
          *     possible object is
          *     {@link StyleProject }
          *
          */
         public StyleProject getStyleProject()
         {
            return styleProject;
         }

         /**
          * Sets the value of the styleProject property.
          *
          * @param value
          *     allowed object is
          *     {@link StyleProject }
          *
          */
         public void setStyleProject(StyleProject value)
         {
            this.styleProject = value;
         }

         /**
          * Gets the value of the markerID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getMarkerID()
         {
            return markerID;
         }

         /**
          * Sets the value of the markerID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMarkerID(Integer value)
         {
            this.markerID = value;
         }

         /**
          * Hyperlinks associated with the resource.
          *
          *
          * @return
          *     possible object is
          *     {@link Hyperlinks }
          *
          */
         public Hyperlinks getHyperlinks()
         {
            return hyperlinks;
         }

         /**
          * Sets the value of the hyperlinks property.
          *
          * @param value
          *     allowed object is
          *     {@link Hyperlinks }
          *
          */
         public void setHyperlinks(Hyperlinks value)
         {
            this.hyperlinks = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="CurrencySymbol" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *         &lt;element name="CurrencyPosition" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="CurrencyDigits" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="HoursPerDay" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="HoursPerWeek" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="DaysPerMonth" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *         &lt;element name="CalcCPForSubprojects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *         &lt;element name="MaximumSlack" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "currencySymbol",
      "currencyPosition",
      "currencyDigits",
      "hoursPerDay",
      "hoursPerWeek",
      "daysPerMonth",
      "calcCPForSubprojects",
      "maximumSlack"
   }) public static class WorkspaceProperties
   {

      @XmlElement(name = "CurrencySymbol", required = true) protected String currencySymbol;
      @XmlElement(name = "CurrencyPosition", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter15.class) @XmlSchemaType(name = "int") protected CurrencySymbolPosition currencyPosition;
      @XmlElement(name = "CurrencyDigits", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer currencyDigits;
      @XmlElement(name = "HoursPerDay", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter16.class) @XmlSchemaType(name = "int") protected Integer hoursPerDay;
      @XmlElement(name = "HoursPerWeek", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer hoursPerWeek;
      @XmlElement(name = "DaysPerMonth", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter6.class) @XmlSchemaType(name = "int") protected Integer daysPerMonth;
      @XmlElement(name = "CalcCPForSubprojects") protected boolean calcCPForSubprojects;
      @XmlElement(name = "MaximumSlack", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "double") protected Double maximumSlack;

      /**
       * Gets the value of the currencySymbol property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getCurrencySymbol()
      {
         return currencySymbol;
      }

      /**
       * Sets the value of the currencySymbol property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCurrencySymbol(String value)
      {
         this.currencySymbol = value;
      }

      /**
       * Gets the value of the currencyPosition property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public CurrencySymbolPosition getCurrencyPosition()
      {
         return currencyPosition;
      }

      /**
       * Sets the value of the currencyPosition property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCurrencyPosition(CurrencySymbolPosition value)
      {
         this.currencyPosition = value;
      }

      /**
       * Gets the value of the currencyDigits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getCurrencyDigits()
      {
         return currencyDigits;
      }

      /**
       * Sets the value of the currencyDigits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCurrencyDigits(Integer value)
      {
         this.currencyDigits = value;
      }

      /**
       * Gets the value of the hoursPerDay property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getHoursPerDay()
      {
         return hoursPerDay;
      }

      /**
       * Sets the value of the hoursPerDay property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHoursPerDay(Integer value)
      {
         this.hoursPerDay = value;
      }

      /**
       * Gets the value of the hoursPerWeek property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getHoursPerWeek()
      {
         return hoursPerWeek;
      }

      /**
       * Sets the value of the hoursPerWeek property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setHoursPerWeek(Integer value)
      {
         this.hoursPerWeek = value;
      }

      /**
       * Gets the value of the daysPerMonth property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getDaysPerMonth()
      {
         return daysPerMonth;
      }

      /**
       * Sets the value of the daysPerMonth property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setDaysPerMonth(Integer value)
      {
         this.daysPerMonth = value;
      }

      /**
       * Gets the value of the calcCPForSubprojects property.
       *
       */
      public boolean isCalcCPForSubprojects()
      {
         return calcCPForSubprojects;
      }

      /**
       * Sets the value of the calcCPForSubprojects property.
       *
       */
      public void setCalcCPForSubprojects(boolean value)
      {
         this.calcCPForSubprojects = value;
      }

      /**
       * Gets the value of the maximumSlack property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getMaximumSlack()
      {
         return maximumSlack;
      }

      /**
       * Sets the value of the maximumSlack property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setMaximumSlack(Double value)
      {
         this.maximumSlack = value;
      }

   }

}
