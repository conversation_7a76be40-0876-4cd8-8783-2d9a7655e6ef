//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.11.28 at 04:58:44 PM GMT
//

package org.mpxj.mspdi.schema;

import java.util.UUID;
import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.mspdi.DatatypeConverter;

public class Adapter15
         extends
            XmlAdapter<String, UUID>
{

   @Override public UUID unmarshal(String value)
   {
      return (DatatypeConverter.parseUUID(value));
   }

   @Override public String marshal(UUID value)
   {
      return (DatatypeConverter.printUUID(value));
   }

}
