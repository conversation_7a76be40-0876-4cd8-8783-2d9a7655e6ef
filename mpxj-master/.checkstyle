<?xml version="1.0" encoding="UTF-8"?>

<fileset-config file-format-version="1.2.0" simple-config="false" sync-formatter="false">
  <local-check-config name="MPXJ" location="eclipse/checkstyle-config.xml" type="project" description="">
    <additional-data name="protect-config-file" value="false"/>
  </local-check-config>
  <fileset name="all" enabled="true" check-config-name="MPXJ" local="true">
    <file-match-pattern match-pattern=".java$" include-pattern="true"/>
  </fileset>
  <filter name="FilesFromPackage" enabled="true">
    <filter-data value="src/main/java/org/mpxj/conceptdraw/schema"/>
    <filter-data value="src/main/java/org/mpxj/edrawproject/schema"/>
    <filter-data value="src/main/java/org/mpxj/ganttdesigner/schema"/>
    <filter-data value="src/main/java/org/mpxj/ganttproject/schema"/>
    <filter-data value="src/main/java/org/mpxj/mspdi/schema"/>
    <filter-data value="src/main/java/org/mpxj/phoenix/schema"/>
    <filter-data value="src/main/java/org/mpxj/planner/schema"/>
    <filter-data value="src/main/java/org/mpxj/primavera/schema"/>
  </filter>
</fileset-config>
