site_name: MPXJ
site_url: http://www.mpxj.org

repo_url: https://github.com/joniles/mpxj
repo_name: mpxj
edit_uri: ""

theme:
  name: material
  logo: images/mpxj-white.svg
  favicon: images/favicon.png
  features:
    - content.tabs.link
    - content.code.copy

extra:
  analytics:
    provider: google
    property: G-9R48LPVHKE

markdown_extensions:
  - attr_list
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed:
      alternate_style: true

nav:
  - 'Introduction': 'index.md'
  - 'Changes': 'CHANGELOG.md'
  - 'Support': 'support.md'
  - 'File Formats': 'supported-formats.md'
  - 'Store': 'https://mpxj.teemill.com/collection/all-products/'
  - 'Getting Started':
    - 'Getting Started with Java': 'howto-start-java.md'
    - 'Getting Started with .Net': 'howto-dotnet.md'
    - 'Getting Started with Python': 'howto-start-python.md'
    - 'Getting Started with Ruby': 'howto-start-ruby.md'
    - 'MPXJ Basics': 'howto-start.md'
    - 'Building MPXJ': 'howto-build.md'
    - 'Converting Files': 'howto-convert.md'
  - 'How to Read...':
    - 'Asta files': 'howto-read-asta.md'
    - 'ConceptDraw PROJECT files': 'howto-read-conceptdraw.md'
    - 'Deltek Open Plan BK3 files': 'howto-read-openplan.md'
    - 'Edraw Project EDPX files': 'howto-read-edraw.md'
    - 'FastTrack files': 'howto-read-fasttrack.md'
    - 'Gantt Designer files': 'howto-read-ganttdesigner.md'
    - 'GanttProject files': 'howto-read-ganttproject.md'
    - 'Merlin files': 'howto-read-merlin.md'
    - 'MPD files': 'howto-read-mpd.md'
    - 'MPD databases': 'howto-read-mpd-database.md'
    - 'MPP files': 'howto-read-mpp.md'
    - 'MPX Files': 'howto-read-mpx.md'
    - 'MSPDI files': 'howto-read-mspdi.md'
    - 'P3 files': 'howto-read-p3.md'
    - 'P6 Databases': 'howto-read-primavera.md'
    - 'Phoenix files': 'howto-read-phoenix.md'
    - 'Planner files': 'howto-read-planner.md'
    - 'PLF files': 'howto-read-plf.md'
    - 'PMXML files': 'howto-read-pmxml.md'
    - 'Project Commander files': 'howto-read-projectcommander.md'
    - 'ProjectLibre files': 'howto-read-projectlibre.md'
    - 'Schedule Grid files': 'howto-read-schedule-grid.md'
    - 'SDEF files': 'howto-read-sdef.md'
    - 'SureTrak files': 'howto-read-suretrak.md'
    - 'Synchro Scheduler files': 'howto-read-synchro.md'
    - 'TurboProject files': 'howto-read-turboproject.md'
    - 'XER files': 'howto-read-xer.md'
  - 'How to Write...':
    - 'MPX files': 'howto-write-mpx.md'
    - 'MSPDI files': 'howto-write-mspdi.md'
    - 'Planner files' : 'howto-write-planner.md'
    - 'PMXML files' : 'howto-write-pmxml.md'
    - 'SDEF files' : 'howto-write-sdef.md'
    - 'XER files' : 'howto-write-xer.md'
  - 'How to Use...':
    - 'Baselines': 'howto-use-baselines.md'
    - 'Calendars': 'howto-use-calendars.md'
    - 'CPM Schedulers': 'howto-use-cpm.md'
    - 'External Projects': 'howto-use-external-projects.md'
    - 'Fields': 'howto-use-fields.md'
    - 'Universal Project Reader': 'howto-use-universal.md'
  - 'Field Guides...':
    - 'Field Guide': 'field-guide.md'
    - 'MPP Field Guide': 'mpp-field-guide.md'
  - 'JavaDoc': '../apidocs/'
  - 'FAQ': 'faq.md'
  - 'Users': 'users.md'
  - 'Maven Reports': '../summary.html'
