# Getting Started with Python

MPXJ is available as a Python Package, which can be installed using `pip`:

```
pip install mpxj
```

You can find some documentation for the Package
[here](https://pypi.org/project/mpxj/).
You'll need Java installed to make use of this package.

You'll find a general introduction to MPXJ's functionality
[here](howto-start.md),
and sample Python code using MPXJ [here](https://github.com/joniles/mpxj-python-samples).
