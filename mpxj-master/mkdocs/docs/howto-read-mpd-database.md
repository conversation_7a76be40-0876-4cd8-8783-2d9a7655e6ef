# How To: Read MPD Databases
Microsoft Project from Project 98 until Project 2003 could read and write
schedules as Microsoft Access database files with the extension MPD. Versions
of Microsoft Project after 2003 can import projects from MPD databases but
cannot create or write to them. Project 98 creates a database with a schema
known as MPD8, which MPXJ does not currently support reading. Project 2000
onward uses a schema called MPD9 which <PERSON><PERSON><PERSON> can read.

> Microsoft Project Server originally shared the same database
> schema as the MPD9 file format. This means that the `MPDDatabaseReader` class
> described below may also be used to read data from a Project Server
> SQL Server database.

## Reading MPD databases
The preferred way to read MPD files is using `UniversalProjectReader`
or the `MPDFileReader` reader. as described in the
[How To Read MPD files](howto-read-mpd.md) section.

You can if you wish read an MPD file via a database connection. Typically
you will need the JDBC-ODBC bridge driver, or an equivalent JDBC driver which
can work with a Microsoft Access database.

### Setting the database connection
The `MPDDatabaseReader` class provides two methods: `setConnection` and
`setDataSource` which allows you to supply a JDBC `Connection` instance or a
JDBC `DataSource` instance.


### Selecting a project
If the MPD file contains multiple projects, you can retrieve details of the
available projects using the `listProjects` method. This returns a map of
project IDs and project names. The sample code below illustrates how to retrieve
this list of projects, and select the specific project that you want to read. In
this case we read each project in the file in turn.

```java
package org.mpxj.howto.read;

import org.mpxj.mpd.MPDDatabaseReader;

import java.sql.Connection;
import java.util.Map;

public class MPDDatabase
{
   public void read(Connection connection) throws Exception
   {
      MPDDatabaseReader reader = new MPDDatabaseReader();
      reader.setConnection(connection);
      Map<Integer, String> projects = reader.listProjects();
      for (Map.Entry<Integer, String> entry : projects.entrySet())
      {
         System.out.println("Project name: " + entry.getValue());
         reader.setProjectID(entry.getKey());
         reader.read();
      }
   }
}

```
