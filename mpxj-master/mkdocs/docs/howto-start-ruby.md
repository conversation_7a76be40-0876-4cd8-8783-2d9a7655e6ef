# Getting Started with Ruby

MPXJ is available as a RubyGem, which can be installed using `gem`:

```
gem install mpxj
```

or included in you `Gemfile` and installed using `bundler`.

Note that the Ruby version of MPXJ is just a wrapper around the Java library,
and provides read-only access to schedule data. You will need Java installed to
make use of this Gem. You can find some documentation for the Gem
[here](https://rubygems.org/gems/mpxj)

You'll find a general introduction to MPXJ's functionality
[here](howto-start.md).
