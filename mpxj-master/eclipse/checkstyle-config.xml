<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN" "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    This configuration file was written by the eclipse-cs plugin configuration editor
-->
<!--
    Checkstyle-Configuration: MPXJ
    Description: none
-->
<module name="Checker">
  <property name="severity" value="warning"/>
  <module name="TreeWalker">
    <module name="ArrayTypeStyle"/>
    <module name="ModifierOrder"/>
    <module name="LeftCurly">
      <property name="option" value="nl"/>
      <property name="tokens" value="CLASS_DEF, CTOR_DEF, INTERFACE_DEF, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF, LITERAL_SWITCH, LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, METHOD_DEF"/>
    </module>
    <module name="NeedBraces">
      <property name="tokens" value="LITERAL_DO, LITERAL_ELSE, LITERAL_IF, LITERAL_FOR, LITERAL_WHILE"/>
    </module>
    <module name="RightCurly">
      <property name="option" value="alone"/>
      <property name="tokens" value="LITERAL_CATCH, LITERAL_ELSE, LITERAL_TRY"/>
    </module>
    <module name="EmptyStatement"/>
    <module name="EqualsHashCode"/>
    <module name="ExplicitInitialization"/>
    <module name="FallThrough"/>
    <module name="HiddenField">
      <property name="tokens" value="PARAMETER_DEF, VARIABLE_DEF"/>
    </module>
    <module name="FinalClass"/>
    <module name="AvoidStarImport">
      <property name="allowStaticMemberImports" value="true"/>
    </module>
    <module name="IllegalImport"/>
    <module name="RedundantImport"/>
    <module name="UnusedImports"/>
    <module name="JavadocMethod"/>
    <module name="JavadocType"/>
    <module name="ConstantName">
      <property name="format" value="^[A-Z](_?[A-Z0-9]+)*$"/>
    </module>
    <module name="LocalVariableName"/>
    <module name="MemberName">
      <property name="format" value="^m_[a-z][a-zA-Z0-9]*$"/>
    </module>
    <module name="MethodName"/>
    <module name="PackageName"/>
    <module name="ParameterName"/>
    <module name="StaticVariableName">
      <property name="format" value="^[A-Z0-9_]*$"/>
    </module>
    <module name="TypeName">
      <property name="format" value="^[A-Z][a-zA-Z0-9_]*$"/>
      <property name="tokens" value="CLASS_DEF"/>
    </module>
    <module name="TypeName">
      <property name="tokens" value="INTERFACE_DEF"/>
    </module>
    <module name="JavadocStyle">
      <property name="checkEmptyJavadoc" value="true"/>
    </module>
    <module name="JavadocStyle"/>
    <module name="MissingJavadocMethod"/>
  </module>
</module>
