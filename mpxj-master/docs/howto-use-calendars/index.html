
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
        <link rel="canonical" href="http://www.mpxj.org/howto-use-calendars/">
      
      
        <link rel="prev" href="../howto-use-baselines/">
      
      
        <link rel="next" href="../howto-use-cpm/">
      
      
      <link rel="icon" href="../images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.0, mkdocs-material-9.5.20">
    
    
      
        <title>Calendars - MPXJ</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.66ac8b77.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce((e,_)=>(e<<5)-e+_.charCodeAt(0),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      
  


  
  

<script id="__analytics">function __md_analytics(){function n(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],n("js",new Date),n("config","G-9R48LPVHKE"),document.addEventListener("DOMContentLoaded",function(){document.forms.search&&document.forms.search.query.addEventListener("blur",function(){this.value&&n("event","search",{search_term:this.value})}),document$.subscribe(function(){var a=document.forms.feedback;if(void 0!==a)for(var e of a.querySelectorAll("[type=submit]"))e.addEventListener("click",function(e){e.preventDefault();var t=document.location.pathname,e=this.getAttribute("data-md-value");n("event","feedback",{page:t,data:e}),a.firstElementChild.disabled=!0;e=a.querySelector(".md-feedback__note [data-md-value='"+e+"']");e&&(e.hidden=!1)}),a.hidden=!1}),location$.subscribe(function(e){n("config","G-9R48LPVHKE",{page_path:e.pathname})})});var e=document.createElement("script");e.async=!0,e.src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE",document.getElementById("__analytics").insertAdjacentElement("afterEnd",e)}</script>
  
    <script>"undefined"!=typeof __md_analytics&&__md_analytics()</script>
  

    
    
    
  </head>
  
  
    <body dir="ltr">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#how-to-use-calendars" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="MPXJ" class="md-header__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MPXJ
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Calendars
            
          </span>
        </div>
      </div>
    </div>
    
    
      <script>var media,input,key,value,palette=__md_get("__palette");if(palette&&palette.color){"(prefers-color-scheme)"===palette.color.media&&(media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']"),palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent"));for([key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12Z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="MPXJ" class="md-nav__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    MPXJ
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Introduction
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../CHANGELOG/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Changes
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../support/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Support
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../supported-formats/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    File Formats
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="https://mpxj.teemill.com/collection/all-products/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Store
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Getting Started
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-java/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Java
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-dotnet/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with .Net
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-python/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Python
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-ruby/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Ruby
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPXJ Basics
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-build/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Building MPXJ
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-convert/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Converting Files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Read...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            How to Read...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-asta/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Asta files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-conceptdraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ConceptDraw PROJECT files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-openplan/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Deltek Open Plan BK3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-edraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Edraw Project EDPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-fasttrack/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FastTrack files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttdesigner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Gantt Designer files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    GanttProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-merlin/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Merlin files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd-database/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpp/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX Files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-p3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-primavera/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P6 Databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-phoenix/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Phoenix files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-plf/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PLF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectcommander/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Project Commander files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectlibre/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ProjectLibre files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-schedule-grid/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Schedule Grid files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-suretrak/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SureTrak files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-synchro/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Synchro Scheduler files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-turboproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    TurboProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Write...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            How to Write...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Use...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            How to Use...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-baselines/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Baselines
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    Calendars
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    Calendars
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#calendars-in-mpxj" class="md-nav__link">
    <span class="md-ellipsis">
      Calendars in MPXJ
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-days" class="md-nav__link">
    <span class="md-ellipsis">
      Working Days
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-hours" class="md-nav__link">
    <span class="md-ellipsis">
      Working Hours
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#exceptions" class="md-nav__link">
    <span class="md-ellipsis">
      Exceptions
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-weeks" class="md-nav__link">
    <span class="md-ellipsis">
      Working Weeks
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#recurring-exceptions" class="md-nav__link">
    <span class="md-ellipsis">
      Recurring Exceptions
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-hierarchies" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Hierarchies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Calendar Hierarchies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-calendar-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a Calendar Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#working-with-a-calendar-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Working with a Calendar Hierarchy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#how-deep-is-your-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      How deep is your Hierarchy?
    </span>
  </a>
  
    <nav class="md-nav" aria-label="How deep is your Hierarchy?">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#microsoft-project" class="md-nav__link">
    <span class="md-ellipsis">
      Microsoft Project
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#primavera-p6" class="md-nav__link">
    <span class="md-ellipsis">
      Primavera P6
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#others" class="md-nav__link">
    <span class="md-ellipsis">
      Others
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-container" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Container
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-relationships" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Relationships
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-cpm/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    CPM Schedulers
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-external-projects/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    External Projects
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-fields/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Fields
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-universal/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Universal Project Reader
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_10" >
        
          
          <label class="md-nav__link" for="__nav_10" id="__nav_10_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Field Guides...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_10_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_10">
            <span class="md-nav__icon md-icon"></span>
            Field Guides...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Field Guide
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../mpp-field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP Field Guide
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../apidocs/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    JavaDoc
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../faq/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FAQ
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../users/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Users
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../summary.html" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Maven Reports
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#calendars-in-mpxj" class="md-nav__link">
    <span class="md-ellipsis">
      Calendars in MPXJ
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-days" class="md-nav__link">
    <span class="md-ellipsis">
      Working Days
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-hours" class="md-nav__link">
    <span class="md-ellipsis">
      Working Hours
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#exceptions" class="md-nav__link">
    <span class="md-ellipsis">
      Exceptions
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#working-weeks" class="md-nav__link">
    <span class="md-ellipsis">
      Working Weeks
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#recurring-exceptions" class="md-nav__link">
    <span class="md-ellipsis">
      Recurring Exceptions
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-hierarchies" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Hierarchies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Calendar Hierarchies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-calendar-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a Calendar Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#working-with-a-calendar-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Working with a Calendar Hierarchy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#how-deep-is-your-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      How deep is your Hierarchy?
    </span>
  </a>
  
    <nav class="md-nav" aria-label="How deep is your Hierarchy?">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#microsoft-project" class="md-nav__link">
    <span class="md-ellipsis">
      Microsoft Project
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#primavera-p6" class="md-nav__link">
    <span class="md-ellipsis">
      Primavera P6
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#others" class="md-nav__link">
    <span class="md-ellipsis">
      Others
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-container" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Container
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#calendar-relationships" class="md-nav__link">
    <span class="md-ellipsis">
      Calendar Relationships
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


<h1 id="how-to-use-calendars">How To: Use Calendars</h1>
<p>Calendars are the foundation on which schedules are built. They determine when
work can be carried out, and when work is not possible. Given some tasks we
need to plan, and knowing how much work each task will require, a calendar can
be used to decide when work on each task could start and how much elapsed time
will be required to complete the tasks.</p>
<h2 id="calendars-in-mpxj">Calendars in MPXJ</h2>
<p>Let's see how calendars work in MPXJ. First let's try creating one. As it
happens, the <code>ProjectFile</code> class provides a convenience method
<code>addDefaultBaseCalendar</code> to create a default calendar. The calendar it creates
is modelled on the <code>Standard</code> calendar you'd see in Microsoft Project if you
created a new project. This default calendar defines Monday to Friday as
working days, with 8 working hours each day (8am to noon, then 1pm to 5pm).</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectFile</span><span class="w"> </span><span class="n">file</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ProjectFile</span><span class="p">();</span>
<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addDefaultBaseCalendar</span><span class="p">();</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="s">&quot;The calendar name is &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getName</span><span class="p">());</span>
</code></pre></div>
<p>As you can see from the code above, the calendar also has a name which we can
set to distinguish between different calendars.</p>
<h2 id="working-days">Working Days</h2>
<p>Let's see what the calendar can tell us. First we'll use the <code>DayOfWeek</code>
enumeration to retrieve the working/non-working state for each day.</p>
<div class="highlight"><pre><span></span><code><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">DayOfWeek</span><span class="w"> </span><span class="n">day</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">values</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">   </span><span class="n">String</span><span class="w"> </span><span class="n">dayType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarDayType</span><span class="p">(</span><span class="n">day</span><span class="p">).</span><span class="na">toString</span><span class="p">();</span>
<span class="w">   </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">day</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; is a &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">dayType</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; day&quot;</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<p>Running the code shown above will produce output like this:</p>
<div class="highlight"><pre><span></span><code>MONDAY is a WORKING day
TUESDAY is a WORKING day
WEDNESDAY is a WORKING day
THURSDAY is a WORKING day
FRIDAY is a WORKING day
SATURDAY is a NON_WORKING day
SUNDAY is a NON_WORKING day
</code></pre></div>
<p>We can use the <code>setWorkingDay</code> method to change our pattern of working day.
Let's make Saturday a working day for our team, and make Monday a non-working
day to compensate.</p>
<div class="highlight"><pre><span></span><code><span class="n">calendar</span><span class="p">.</span><span class="na">setWorkingDay</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">,</span><span class="w"> </span><span class="kc">true</span><span class="p">);</span>
<span class="n">calendar</span><span class="p">.</span><span class="na">setWorkingDay</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">MONDAY</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">);</span>
</code></pre></div>
<p>Now if we use the loop we saw previously to inspect the week days, we'll see
this output:</p>
<div class="highlight"><pre><span></span><code>MONDAY is a NON_WORKING day
TUESDAY is a WORKING day
WEDNESDAY is a WORKING day
THURSDAY is a WORKING day
FRIDAY is a WORKING day
SATURDAY is a WORKING day
SUNDAY is a NON_WORKING day
</code></pre></div>
<h2 id="working-hours">Working Hours</h2>
<p>So far, all we have done is set a flag which tells us whether a day is working
or non-working. How do we know the working times on those days? We can use the
<code>getCalendarHours</code> method to find that information.</p>
<p>The <code>getCalendarHours</code> method returns a <code>List</code> of <code>LocalTimeRange</code> instances.
<code>LocalTimeRange</code> is a simple immutable class which represents a span of time
between a start time and an end time as an inclusive range. Let's try printing
these <code>LocalTimeRange</code> instances to our output to see what we get:</p>
<div class="highlight"><pre><span></span><code><span class="n">List</span><span class="o">&lt;</span><span class="n">LocalTimeRange</span><span class="o">&gt;</span><span class="w"> </span><span class="n">hours</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarHours</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">);</span>
<span class="n">hours</span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">::</span><span class="n">println</span><span class="p">);</span>
</code></pre></div>
<p>Here's the output:</p>
<div class="highlight"><pre><span></span><code>[LocalTimeRange start=08:00 end=12:00]
[LocalTimeRange start=13:00 end=17:00]
</code></pre></div>
<p>Let's add a method to format the hours of a day a little more concisely for
display:</p>
<div class="highlight"><pre><span></span><code><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="nf">formatLocalTimeRanges</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">LocalTimeRange</span><span class="o">&gt;</span><span class="w"> </span><span class="n">hours</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">   </span><span class="k">return</span><span class="w"> </span><span class="n">hours</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">      </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">h</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">h</span><span class="p">.</span><span class="na">getStart</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;-&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">h</span><span class="p">.</span><span class="na">getEnd</span><span class="p">())</span>
<span class="w">      </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">joining</span><span class="p">(</span><span class="s">&quot;, &quot;</span><span class="p">));</span>
<span class="p">}</span>
</code></pre></div>
<p>So now our output looks like this:</p>
<div class="highlight"><pre><span></span><code>08:00-12:00, 13:00-17:00
</code></pre></div>
<p>Let's use this method to take a look at the whole week again:</p>
<div class="highlight"><pre><span></span><code><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">DayOfWeek</span><span class="w"> </span><span class="n">day</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">values</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">   </span><span class="n">String</span><span class="w"> </span><span class="n">dayType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarDayType</span><span class="p">(</span><span class="n">day</span><span class="p">).</span><span class="na">toString</span><span class="p">();</span>
<span class="w">   </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">day</span>
<span class="w">      </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; is a &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">dayType</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; day (&quot;</span>
<span class="w">      </span><span class="o">+</span><span class="w"> </span><span class="n">formatLocalTimeRanges</span><span class="p">(</span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarHours</span><span class="p">(</span><span class="n">day</span><span class="p">))</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;)&quot;</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<p>Here's the output:</p>
<div class="highlight"><pre><span></span><code>MONDAY is a NON_WORKING day ()
TUESDAY is a WORKING day (08:00-12:00, 13:00-17:00)
WEDNESDAY is a WORKING day (08:00-12:00, 13:00-17:00)
THURSDAY is a WORKING day (08:00-12:00, 13:00-17:00)
FRIDAY is a WORKING day (08:00-12:00, 13:00-17:00)
SATURDAY is a WORKING day ()
SUNDAY is a NON_WORKING day ()
</code></pre></div>
<p>The one thing we're missing now is that although we have set Saturday to be a
working day, it doesn't have any working hours. MPXJ has some constants which
can be used to help us add some working hours:</p>
<div class="highlight"><pre><span></span><code><span class="n">hours</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarHours</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">);</span>
<span class="n">hours</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">ProjectCalendarDays</span><span class="p">.</span><span class="na">DEFAULT_WORKING_MORNING</span><span class="p">);</span>
<span class="n">hours</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">ProjectCalendarDays</span><span class="p">.</span><span class="na">DEFAULT_WORKING_AFTERNOON</span><span class="p">);</span>
</code></pre></div>
<p>Now when we examine our week this is what we see:</p>
<div class="highlight"><pre><span></span><code>MONDAY is a NON_WORKING day ()
TUESDAY is a WORKING day (08:00-12:00, 13:00-17:00)
WEDNESDAY is a WORKING day (08:00-12:00, 13:00-17:00)
THURSDAY is a WORKING day (08:00-12:00, 13:00-17:00)
FRIDAY is a WORKING day (08:00-12:00, 13:00-17:00)
SATURDAY is a WORKING day (08:00-12:00, 13:00-17:00)
SUNDAY is a NON_WORKING day ()
</code></pre></div>
<blockquote>
<p>The version of MPXJ at the time of writing (12.0.0) has a limitation
that if <code>setCalendarDayType</code> is used to make a day into a working day, we don't
automatically add working hours for it. This behaviour is likely to
change with the next major version of MPXJ.</p>
</blockquote>
<p>What if we want to supply some working hours different from the defaults we've
used so far? To set our own working hours we just need to create as many
<code>LocalTimeRange</code> instances as we need using a pair of <code>LocalTime</code> instances for
each one to represent the start and end times.</p>
<div class="highlight"><pre><span></span><code><span class="n">LocalTime</span><span class="w"> </span><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">LocalTime</span><span class="w"> </span><span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">14</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">);</span>
<span class="n">hours</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarHours</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">);</span>
<span class="n">hours</span><span class="p">.</span><span class="na">clear</span><span class="p">();</span>
<span class="n">hours</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">));</span>
</code></pre></div>
<p>Now when we look at the working hours for Saturday, this is what we see:</p>
<div class="highlight"><pre><span></span><code>SATURDAY is a WORKING day (09:00-14:30)
</code></pre></div>
<p>Now we've seen how we can create our own ranges of working time for a day, let's
tackle a slightly more challenging case: dealing with midnight. Our first step
is to take a look at the actual amount of working time we've set up on Saturday.
To do this we call the <code>getWork</code> method, as shown below.</p>
<div class="highlight"><pre><span></span><code><span class="n">Duration</span><span class="w"> </span><span class="n">duration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getWork</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">,</span><span class="w"> </span><span class="n">TimeUnit</span><span class="p">.</span><span class="na">HOURS</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">duration</span><span class="p">);</span>
</code></pre></div>
<p>This <code>getWork</code> method determines the total amount of work on the given day, and
returns this in the format we specify. In this case we've asked for hours, and
we'll be receiving the result as a <code>Duration</code> object. <code>Duration</code> simply
combines the duration amount with an instance of the <code>TimeUnit</code> enumeration so
we always know the units of the duration amount.</p>
<p>Running the code above give us this output:</p>
<div class="highlight"><pre><span></span><code>5.5h
</code></pre></div>
<p>As you can see, the <code>toString</code> method of <code>Duration</code> give us a
nicely formatted result, complete with an abbreviation for the units.</p>
<p>Let's try to change Saturday to be 24 hour working. First we'll configure a
midnight to midnight date range:</p>
<div class="highlight"><pre><span></span><code><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">MIDNIGHT</span><span class="p">;</span>
<span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">MIDNIGHT</span><span class="p">;</span>
<span class="n">hours</span><span class="p">.</span><span class="na">clear</span><span class="p">();</span>
<span class="n">hours</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">));</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">formatLocalTimeRanges</span><span class="p">(</span><span class="n">calendar</span><span class="p">.</span><span class="na">getCalendarHours</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">)));</span>
</code></pre></div>
<p>This looks reasonable:</p>
<div class="highlight"><pre><span></span><code>00:00-00:00
</code></pre></div>
<p>Now let's see how much work this represents:</p>
<div class="highlight"><pre><span></span><code><span class="n">duration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getWork</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">,</span><span class="w"> </span><span class="n">TimeUnit</span><span class="p">.</span><span class="na">HOURS</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">duration</span><span class="p">);</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code>24.0h
</code></pre></div>
<p>So we have our 24 hours of work on Saturday!</p>
<h2 id="exceptions">Exceptions</h2>
<p>After working a few of these 24 hour days on Saturdays, we might be in need of a
vacation! How can we add this to our calendar?</p>
<p>So far we've been working with the <code>DayOfWeek</code> class to make changes to days of
the week, rather than any specific date. Now we'll need to work with a specific
date, and add an "exception" for this date. The terminology here can be
slightly confusing when coming from a programming background, but the term
exception is often used by scheduling applications in the context of making
ad-hoc adjustments to a calendar.</p>
<div class="highlight"><pre><span></span><code><span class="n">LocalDate</span><span class="w"> </span><span class="n">exceptionDate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">);</span>

<span class="kt">boolean</span><span class="w"> </span><span class="n">workingDate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">isWorkingDate</span><span class="p">(</span><span class="n">exceptionDate</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">exceptionDate</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; is a &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="p">(</span><span class="n">workingDate</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="s">&quot;working&quot;</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s">&quot;non-working&quot;</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; day&quot;</span><span class="p">);</span>
</code></pre></div>
<p>In the code above we're creating a <code>LocalDate</code> instance to represent the date we want
to add an exception for. The code uses the <code>isWorkingDate</code> method to determine
whether or not the  given date is a working day. Before we add the exception,
here's the output we get:</p>
<div class="highlight"><pre><span></span><code>2022-05-10 is a working day
</code></pre></div>
<p>Now we can create our exception.</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectCalendarException</span><span class="w"> </span><span class="n">exception</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">addCalendarException</span><span class="p">(</span><span class="n">exceptionDate</span><span class="p">);</span>
<span class="n">exception</span><span class="p">.</span><span class="na">setName</span><span class="p">(</span><span class="s">&quot;A day off&quot;</span><span class="p">);</span>
</code></pre></div>
<p>The code above illustrates adding an exception for a single day. The code above
also shows that optionally an exception can be named, this can make it easier
to understand the purpose of each exception. Now if we re-run our code which
displays whether our chosen date is a working day, this is what we see:</p>
<div class="highlight"><pre><span></span><code>2022-05-10 is a non-working day
</code></pre></div>
<p>We have successfully added an exception to turn this date into a day off!</p>
<p>Perhaps we were being a little too generous in giving ourselves the entire day
off, perhaps in this case we should make this a half day instead. To do that, we
just need to add a time range to the exception:</p>
<div class="highlight"><pre><span></span><code><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">exception</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">));</span>
</code></pre></div>
<p>Now if we look at our chosen date, this is what we see:</p>
<div class="highlight"><pre><span></span><code>2022-05-10 is a working day
</code></pre></div>
<p>Let's take a closer look at what's happening on that day:</p>
<div class="highlight"><pre><span></span><code><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="s">&quot;Working time on Tuesdays is normally &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getWork</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">,</span><span class="w"> </span><span class="n">TimeUnit</span><span class="p">.</span><span class="na">HOURS</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; but on &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">exceptionDate</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; it is &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getWork</span><span class="p">(</span><span class="n">exceptionDate</span><span class="p">,</span><span class="w"> </span><span class="n">TimeUnit</span><span class="p">.</span><span class="na">HOURS</span><span class="p">));</span>
</code></pre></div>
<p>The code above shows how we use the <code>getWork</code> method which takes a <code>DayOfWeek</code>
as an argument to look at what the default working hours are on a Tuesday, then
we use the <code>getWork</code> method which takes a <code>LocalDate</code> instance as an argument
to see what's happening on the specific Tuesday of our exception. Here's the
output we get:</p>
<div class="highlight"><pre><span></span><code>Working time on Tuesdays is normally 8.0h but on 2022-05-10 it is 4.0h
</code></pre></div>
<p>We can see the effect of adding a <code>LocalTimeRange</code> to our exception: we've gone
from an exception which changes a working day into a non-working day to an
exception which just changes the number of working hours in the day. This same
approach can be used to change a date which falls on a day that's typically
non-working (for example a Sunday) into a working day, just by adding an
exception with some working hours.</p>
<p>We can also use a single exception to affect a number of days. First let's
write a little code to see the number of working hours over a range of days:</p>
<div class="highlight"><pre><span></span><code><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">dateDump</span><span class="p">(</span><span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar</span><span class="p">,</span><span class="w"> </span><span class="n">LocalDate</span><span class="w"> </span><span class="n">startDate</span><span class="p">,</span><span class="w"> </span><span class="n">LocalDate</span><span class="w"> </span><span class="n">endDate</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">   </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">LocalDate</span><span class="w"> </span><span class="n">date</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">startDate</span><span class="p">;</span><span class="w"> </span><span class="n">date</span><span class="p">.</span><span class="na">isBefore</span><span class="p">(</span><span class="n">endDate</span><span class="p">);</span><span class="w"> </span><span class="n">date</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">date</span><span class="p">.</span><span class="na">plusDays</span><span class="p">(</span><span class="mi">1</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">date</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;\t&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">getWork</span><span class="p">(</span><span class="n">date</span><span class="p">,</span><span class="w"> </span><span class="n">TimeUnit</span><span class="p">.</span><span class="na">HOURS</span><span class="p">));</span>
<span class="w">   </span><span class="p">}</span>
<span class="w">   </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">();</span>
<span class="p">}</span>
</code></pre></div>
<p>Running this code with our calendar as its stands produces this output for the
example week we're using:</p>
<div class="highlight"><pre><span></span><code>2022-05-23  0.0h
2022-05-24  8.0h
2022-05-25  8.0h
2022-05-26  8.0h
2022-05-27  8.0h
</code></pre></div>
<p>Let's add an exception which covers Tuesday to Thursday that week (24th to
26th), and changes the working hours, so there are now only four hours of work
per day (9am to 12pm):</p>
<div class="highlight"><pre><span></span><code><span class="n">LocalDate</span><span class="w"> </span><span class="n">exceptionStartDate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">24</span><span class="p">);</span>
<span class="n">LocalDate</span><span class="w"> </span><span class="n">exceptionEndDate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">26</span><span class="p">);</span>
<span class="n">exception</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">addCalendarException</span><span class="p">(</span><span class="n">exceptionStartDate</span><span class="p">,</span><span class="w"> </span><span class="n">exceptionEndDate</span><span class="p">);</span>
<span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">13</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">exception</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">));</span>
</code></pre></div>
<p>Here we can see that we're using a different version of the
<code>addCalendarException</code> method which takes a start and an end date, rather that
just a single date. Running our code again to print out the working hours for
each day now gives us this output:</p>
<div class="highlight"><pre><span></span><code>2022-05-23  0.0h
2022-05-24  4.0h
2022-05-25  4.0h
2022-05-26  4.0h
2022-05-27  8.0h
</code></pre></div>
<p>As we can see, we've changed multiple days with this single exception.</p>
<h2 id="working-weeks">Working Weeks</h2>
<p>So far we've looked at using <code>ProjectCalendarException</code>, which can make one
change (add working hours, change working hours, or make days non-working) and
apply that change to one day or a contiguous range of days. What if we want to
make more complex changes to the working pattern of a calendar?</p>
<p>Let's imagine that our project has a three week "crunch" period at the beginning
of October where we will need to work 16 hour days, Monday through Friday, and
8 hour days at weekends. (I hope this is a fictional example and you'd don't
have to work at such a high intensity in real life!). We <em>could</em> construct this
work pattern using exceptions: we'd need six in total, one for each of the
three sets of weekend days, and one for each of the three sets of week days.</p>
<p>An alternative way to do this is to set up a new working week, using the
<code>ProjectCalendarWeek</code> class. "Working Week" is perhaps a slightly misleading
name, as a <code>ProjectCalendarWeek</code> can be set up for an arbitrary range of dates,
from a few days to many weeks. What it represents is the pattern of working an
non-working time over the seven days of a week, and this pattern is applied
from the start to the end of the date range we configure.</p>
<p>The <code>ProjectCalendar</code> we've been working with so
far is actually already a form of working week (they share a common parent
class). The main differences between the two are that a <code>ProjectCalendarWeek</code>
allows us to specify the range of dates over which it is effective, and a
<code>ProjectCalendarWeek</code> does not have exceptions: exceptions are only added to
a <code>ProjectCalendar</code>. </p>
<p>For a fresh start, we'll create a new <code>ProjectCalendar</code> instance. With this
we'll add a new working week definition and give it a name, to make it easily
identifiable. Now we'll set the dates for which this work pattern is valid
(in this case the first three weeks of October). Finally we mark every day as a
working day. Here's how our example looks in code:</p>
<div class="highlight"><pre><span></span><code><span class="n">LocalDate</span><span class="w"> </span><span class="n">weekStart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>
<span class="n">LocalDate</span><span class="w"> </span><span class="n">weekEnd</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2022</span><span class="p">,</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">21</span><span class="p">);</span>
<span class="n">calendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addDefaultBaseCalendar</span><span class="p">();</span>
<span class="n">ProjectCalendarWeek</span><span class="w"> </span><span class="n">week</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">addWorkWeek</span><span class="p">();</span>
<span class="n">week</span><span class="p">.</span><span class="na">setName</span><span class="p">(</span><span class="s">&quot;Crunch Time!&quot;</span><span class="p">);</span>
<span class="n">week</span><span class="p">.</span><span class="na">setDateRange</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalDateRange</span><span class="p">(</span><span class="n">weekStart</span><span class="p">,</span><span class="w"> </span><span class="n">weekEnd</span><span class="p">));</span>
<span class="n">Arrays</span><span class="p">.</span><span class="na">stream</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">values</span><span class="p">()).</span><span class="na">forEach</span><span class="p">(</span><span class="n">d</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">week</span><span class="p">.</span><span class="na">setWorkingDay</span><span class="p">(</span><span class="n">d</span><span class="p">,</span><span class="w"> </span><span class="kc">true</span><span class="p">));</span>
</code></pre></div>
<p>Next we can set up our weekend 9am to 5pm working pattern:</p>
<div class="highlight"><pre><span></span><code><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">17</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">LocalTimeRange</span><span class="w"> </span><span class="n">weekendHours</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">);</span>
<span class="n">Stream</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SATURDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">SUNDAY</span><span class="p">)</span>
<span class="w">   </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">d</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">week</span><span class="p">.</span><span class="na">addCalendarHours</span><span class="p">(</span><span class="n">d</span><span class="p">).</span><span class="na">add</span><span class="p">(</span><span class="n">weekendHours</span><span class="p">));</span>
</code></pre></div>
<p>Finally we can set up our weekday 5am to 9pm pattern:</p>
<div class="highlight"><pre><span></span><code><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">21</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">LocalTimeRange</span><span class="w"> </span><span class="n">weekdayHours</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">);</span>
<span class="n">Stream</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">MONDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">WEDNESDAY</span><span class="p">,</span>
<span class="w">      </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">THURSDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">FRIDAY</span><span class="p">)</span>
<span class="w">   </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">d</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">week</span><span class="p">.</span><span class="na">addCalendarHours</span><span class="p">(</span><span class="n">d</span><span class="p">).</span><span class="na">add</span><span class="p">(</span><span class="n">weekdayHours</span><span class="p">));</span>
</code></pre></div>
<p>As <code>ProjectCalendar</code> and <code>ProjectCalendarWeek</code> are both derived from the same
parent class, we can use the same code we did previously to examine how our new
<code>ProjectCalendarWeek</code> instance looks:</p>
<div class="highlight"><pre><span></span><code>MONDAY is a WORKING day (05:00-21:00)
TUESDAY is a WORKING day (05:00-21:00)
WEDNESDAY is a WORKING day (05:00-21:00)
THURSDAY is a WORKING day (05:00-21:00)
FRIDAY is a WORKING day (05:00-21:00)
SATURDAY is a WORKING day (09:00-17:00)
SUNDAY is a WORKING day (09:00-17:00)
</code></pre></div>
<p>To see the effect that our new working week has had on the calendar, let's first
take a look at the week running up to the start of our crunch period. Using the
same code we worked with previously to present working hours for a range of
dates we see this output:</p>
<div class="highlight"><pre><span></span><code>2022-09-24  0.0h
2022-09-25  0.0h
2022-09-26  8.0h
2022-09-27  8.0h
2022-09-28  8.0h
2022-09-29  8.0h
2022-09-30  8.0h
</code></pre></div>
<p>So starting from Saturday 24th we can see that we have that standard working
pattern: weekends are non-working (zero working hours), and week days have 8
hours of working time.</p>
<p>Now let's look at the first week of our crunch period:</p>
<div class="highlight"><pre><span></span><code>2022-10-01  8.0h
2022-10-02  8.0h
2022-10-03  16.0h
2022-10-04  16.0h
2022-10-05  16.0h
2022-10-06  16.0h
2022-10-07  16.0h
</code></pre></div>
<p>We can see that the crunch is in full effect, we're working 8 hour days at the
weekend, and 16 hour days for the rest of the week - not something I'd like to
try for any length of time!</p>
<p>To summarise: the <code>ProjectCalendar</code> instance itself defines the <em>default</em>
working and non-working pattern for the seven week days. Additional working
weeks can be added to the calendar which override this pattern for specific
date ranges.</p>
<h2 id="recurring-exceptions">Recurring Exceptions</h2>
<p>So far we've seen how exceptions can be used to override the default working
pattern established by a calendar for either a single day, or for a contiguous
range of days. We've also seen how an entirely new seven-day working pattern
can be applied across a range of dates by using working weeks. But what if we
want to represent a regularly occurring exception which will change our default
working pattern such as, for example, Christmas Day or Thanksgiving? To deal
with this we can use recurring exceptions.</p>
<p>A recurring exception can be created simply by passing an instance of
<code>RecurringData</code> to the <code>addCalendarException</code> method.</p>
<div class="highlight"><pre><span></span><code><span class="n">RecurringData</span><span class="w"> </span><span class="n">recurringData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RecurringData</span><span class="p">();</span>
<span class="n">exception</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calendar</span><span class="p">.</span><span class="na">addCalendarException</span><span class="p">(</span><span class="n">recurringData</span><span class="p">);</span>
</code></pre></div>
<p>Let's create a simple recurence for 1st January for five years:</p>
<div class="highlight"><pre><span></span><code><span class="n">recurringData</span><span class="p">.</span><span class="na">setRecurrenceType</span><span class="p">(</span><span class="n">RecurrenceType</span><span class="p">.</span><span class="na">YEARLY</span><span class="p">);</span>
<span class="n">recurringData</span><span class="p">.</span><span class="na">setOccurrences</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span>
<span class="n">recurringData</span><span class="p">.</span><span class="na">setDayNumber</span><span class="p">(</span><span class="n">Integer</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">1</span><span class="p">));</span>
<span class="n">recurringData</span><span class="p">.</span><span class="na">setMonthNumber</span><span class="p">(</span><span class="n">Integer</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">1</span><span class="p">));</span>
<span class="n">recurringData</span><span class="p">.</span><span class="na">setStartDate</span><span class="p">(</span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2023</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">));</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">recurringData</span><span class="p">);</span>
</code></pre></div>
<p>The <code>toString</code> method on the <code>RecurringData</code> class tries to describe the
recurrence as best it can, here's the output we'll see from the code above:</p>
<div class="highlight"><pre><span></span><code><span class="go">[RecurringData Yearly on the 1 January From 2023-01-01 For 5 occurrences]</span>
</code></pre></div>
<p>The example above shows a very simple configuration. Full details of how to use
<code>RecurringData</code> are provided elsewhere as they are beyond the scope of this
section.</p>
<p>Before we move on from recurring exceptions, one useful feature of the
<code>ProjectCalendarException</code> class is the <code>getExpandedExceptions</code> method. This
will convert a recurring exception into a list of individual exceptions
representing each date or range of dates the recurring exception will affect
the calendar. You may find this useful if you need to display or pass this data
on for consumption elsewhere.</p>
<h2 id="calendar-hierarchies">Calendar Hierarchies</h2>
<p>Now we've seen how to set up an individual calendar, perhaps we could go ahead
and create calendars for all of the people who will be working on our project?
What we'd quickly find is that a considerable amount of the information in each
calendar will be the same: the same working week pattern, the same public
holidays and so on. We could set all of this up programmatically of course, but
wouldn't it be great if we could change this kind of detail in just one place,
and have all of our other calendars inherit it?</p>
<h3 id="creating-a-calendar-hierarchy">Creating a Calendar Hierarchy</h3>
<p>As it happens, we can do this as our calendars can be organised into a
hierarchy, with each "child" calendar inheriting its configuration from
a "parent" calendar and overriding that configuration as required rather like a
class hierarchy in a programing language). This will allow us to have one
shared "base" calendar for everyone, with derived calendars used for
individuals on our team where we need to add variation, for example personal
vacation time and so on.</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectFile</span><span class="w"> </span><span class="n">file</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ProjectFile</span><span class="p">();</span>
<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">parentCalendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addDefaultBaseCalendar</span><span class="p">();</span>
<span class="n">LocalDate</span><span class="w"> </span><span class="n">christmasDay</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">2023</span><span class="p">,</span><span class="w"> </span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">25</span><span class="p">);</span>
<span class="n">parentCalendar</span><span class="p">.</span><span class="na">addCalendarException</span><span class="p">(</span><span class="n">christmasDay</span><span class="p">);</span>
</code></pre></div>
<p>In the example above we've used the familiar <code>addDefaultBaseCalendar</code> method to
create a simple calendar, and called <code>addCalendarException</code> to add an
exception for Christmas Day 2023.</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">childCalendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addDefaultDerivedCalendar</span><span class="p">();</span>
<span class="n">childCalendar</span><span class="p">.</span><span class="na">setParent</span><span class="p">(</span><span class="n">parentCalendar</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">christmasDay</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; is a working day: &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">childCalendar</span><span class="p">.</span><span class="na">isWorkingDate</span><span class="p">(</span><span class="n">christmasDay</span><span class="p">));</span>
</code></pre></div>
<p>Now we've created <code>childCalendar</code>, using a method we've not seen before,
<code>addDefaultBaseCalendar</code> (we'll talk about this method in more detail in a
minute), and we've used the new calendar's <code>setParent</code> method to attach
<code>parentCalendar</code> as its parent. We can see the effect of this when we check to
see if Christmas Day 2023 is a working day. This is a Monday so by default it
will be a working day, but as <code>childCalendar</code> is inheriting from
<code>parentCalendar</code> it picks up the exception defined in <code>parentCalendar</code> and
makes Christmas Day a non-working day.</p>
<p>Here's the output when our code is executed:</p>
<div class="highlight"><pre><span></span><code>2023-12-25 is a working day: false
</code></pre></div>
<p>We can also do the same thing with day types:</p>
<div class="highlight"><pre><span></span><code><span class="n">parentCalendar</span><span class="p">.</span><span class="na">setCalendarDayType</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayType</span><span class="p">.</span><span class="na">NON_WORKING</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="s">&quot;Is &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; a working day: &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">childCalendar</span><span class="p">.</span><span class="na">isWorkingDay</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">));</span>
</code></pre></div>
<p>In the example above we've set Tuesday to be a non-working day in the parent
calendar, and we can see that this is inherited by the child calendar. Here's
the output we see when we execute our code:</p>
<div class="highlight"><pre><span></span><code>Is TUESDAY a working day: false
</code></pre></div>
<p>So what's special about the "derived calendar" we've just created
(<code>childCalendar</code>), why is it different to the normal calendar, and what's the
difference between the <code>addDefaultBaseCalendar</code> and <code>addDefaultDerivedCalendar</code>
methods?</p>
<p>The answer to this question lies in the <code>DayType</code> enumeration. Let's
take a look at the day types for <code>parentCalendar</code>.</p>
<div class="highlight"><pre><span></span><code>SUNDAY is a NON_WORKING day
MONDAY is a WORKING day
TUESDAY is a NON_WORKING day
WEDNESDAY is a WORKING day
THURSDAY is a WORKING day
FRIDAY is a WORKING day
SATURDAY is a NON_WORKING day
</code></pre></div>
<p>So far so good, we have a mixture of working an non-working days, and we can see
that as part of our last example we set Tuesday to be a non-working day. Now
let's take a look at <code>childCalendar</code>:</p>
<div class="highlight"><pre><span></span><code>SUNDAY is a DEFAULT day
MONDAY is a DEFAULT day
TUESDAY is a DEFAULT day
WEDNESDAY is a DEFAULT day
THURSDAY is a DEFAULT day
FRIDAY is a DEFAULT day
SATURDAY is a DEFAULT day
</code></pre></div>
<p>Ah-ha! Here we can see that the <code>DayType</code> enumeration actually has a third value
alongside <code>WORKING</code> and <code>NON_WORKING</code>: <code>DEFAULT</code>. The <code>DEFAULT</code> value simply
means that we should inherit the parent calendar's settings for this particular
day: so whether the day is working, non-working, what the working hours are,
and so on.</p>
<p>We can override the day type we're inheriting from the base calendar:</p>
<div class="highlight"><pre><span></span><code><span class="n">childCalendar</span><span class="p">.</span><span class="na">setCalendarDayType</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">,</span><span class="w"> </span><span class="n">DayType</span><span class="p">.</span><span class="na">WORKING</span><span class="p">);</span>
<span class="n">LocalTime</span><span class="w"> </span><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="n">LocalTime</span><span class="w"> </span><span class="n">finishTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">);</span>
<span class="n">childCalendar</span><span class="p">.</span><span class="na">addCalendarHours</span><span class="p">(</span><span class="n">DayOfWeek</span><span class="p">.</span><span class="na">TUESDAY</span><span class="p">)</span>
<span class="w">   </span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">LocalTimeRange</span><span class="p">(</span><span class="n">startTime</span><span class="p">,</span><span class="w"> </span><span class="n">finishTime</span><span class="p">));</span>
</code></pre></div>
<p>In the code above we're explicitly setting Tuesday to be a working day, rather
than inheriting the settings for Tuesday from the parent calendar, then we're
adding the working hours we want for Tuesday.</p>
<p>Earlier we said we come back and look at the <code>addDefaultDerivedCalendar</code> method
in a little more detail. The main difference between
<code>addDefaultDerivedCalendar</code> and <code>addDefaultBaseCalendar</code> is that the calendar
created by <code>addDefaultDerivedCalendar</code> has no working hours defined, and all
day types are set to <code>DEFAULT</code> so everything is inherited from the parent
calendar.</p>
<h3 id="working-with-a-calendar-hierarchy">Working with a Calendar Hierarchy</h3>
<p>In general when working with a calendar hierarchy, if we use a calendar to
determine working/non-working time, working hours, and so on for a given date,
anything configured in a child calendar will always override what we find in
the parent calendar. So for example if we have exceptions or working weeks
configured in a child calendar, these will override anything found in a parent
calendar.</p>
<p>If we're asking the calendar a question about a particular day
(rather than a date), for example Monday, Tuesday and so on, we'll use
information from the child calendar if the day type is <code>WORKING</code> or
<code>NON_WORKING</code>, otherwise we'll work our way up the calendar hierarchy until we
find the first ancestor calendar which does not specify the day type as
<code>DEFAULT</code>, and we'll use the configuration for the day in question from that
calendar.</p>
<p>This brings us on to an interesting question: how do we know if we ask the
calendar for a piece of information, whether that's come from the calendar
whose method we've just called, or if the response we've received has come from
another calendar somewhere further up the calendar hierarchy?</p>
<p>As it happens there are only a small number of attributes for which this is
relevant. These are summarised by the table below.</p>
<table>
<thead>
<tr>
<th>Attribute</th>
<th>Set</th>
<th>Get</th>
<th>Get with Hierarchy</th>
</tr>
</thead>
<tbody>
<tr>
<td>Day Type</td>
<td><code>setCalendarDayType</code></td>
<td><code>getCalendarDayType</code></td>
<td><code>getDayType</code></td>
</tr>
<tr>
<td>Hours</td>
<td><code>addCalendarHours</code></td>
<td><code>getCalendarHours</code></td>
<td><code>getHours</code></td>
</tr>
<tr>
<td>Minutes Per Day</td>
<td><code>setCalendarMinutesPerDay</code></td>
<td><code>getCalendarMinutesPerDay</code></td>
<td><code>getMinutesPerDay</code></td>
</tr>
<tr>
<td>Minutes Per Week</td>
<td><code>setCalendarMinutesPerWeek</code></td>
<td><code>getCalendarMinutesPerWeek</code></td>
<td><code>getMinutesPerWeek</code></td>
</tr>
<tr>
<td>Minutes Per Month</td>
<td><code>setCalendarMinutesPerMonth</code></td>
<td><code>getCalendarMinutesPerMonth</code></td>
<td><code>getMinutesPerWeek</code></td>
</tr>
<tr>
<td>Minutes Per Year</td>
<td><code>setCalendarMinutesPerYear</code></td>
<td><code>getCalendarMinutesPerYear</code></td>
<td><code>getMinutesPerYear</code></td>
</tr>
</tbody>
</table>
<p>The first column give us the name of the attribute, and the second column give
the name of the method we'd call to set that attribute for the current
calendar. The third column gives us the name of the method we'd use to retrieve
the attribute <em>from the current calendar only</em> (i.e this will ignore any parent
calendars). Finally the last column gives us the name of the method we'd call
to retrieve the attribute from the current calendar, or inherit that attribute
from a parent calendar if it is not present in the current calendar.</p>
<blockquote>
<p>We haven't looked at the <em>Minutes Per X</em> attributes so far. The values
they contain are used when calculating working time. One interesting 
point to note is that if no calendars in a hierarchy define these values
the default values will be retrieved from from the <code>ProjectFile</code>
configuration, which is represented by the <code>ProjectConfig</code> class.</p>
</blockquote>
<h2 id="how-deep-is-your-hierarchy">How deep is your Hierarchy?</h2>
<p>MPXJ will allow you to create an arbitrarily deep hierarchy of calendars if you
wish by establishing parent-child relationships between the calendars you
create. Most schedule application file formats will only support a limited
hierarchy of calendars, which you will see when you read files of this type
when using MPXJ. The notes below briefly outlines how calendar hierarchies
operate in some of the applications MPXJ can work with.</p>
<p>If you are using MPXJ to create or modify schedule data, when you write the
results to a file MPXJ will attempt to ensure that the calendars it writes to
the file format you have chosen reflect what the target application is
expecting. This means that MPXJ may end up "flattening" or otherwise
simplifying a set of calendars and their hierarchy to ensure that they are read
correctly by the target application and are "functionally equivalent" in use.</p>
<h3 id="microsoft-project">Microsoft Project</h3>
<p>Microsoft Project uses two tiers of calendars. The first tier of calendars are
referred to as "base calendars", one of which is marked as the default calendar
for the project. Work is scheduled based on the default calendar, unless a task
explicitly selects a different base calendar to use when being scheduled, or
resources with their own calendars have been assigned to the task. Each
resource will have its own calendar, which is always derived from a base
calendar.</p>
<blockquote>
<p>Note that, as you might expect, material resources don't have a calendar!</p>
</blockquote>
<h3 id="primavera-p6">Primavera P6</h3>
<p>The situation with P6 is a little more complicated, although it's still a
two tier arrangement. P6 has the concept of Global calendars (broadly similar
to base calendars in Microsoft Project). These can be assigned to activities in
any project. Global calendars are never derived from other calendars.</p>
<p>You can also have Project calendars which, as their name suggests, can only be
assigned to activities in the project to which they belong. Project calendars
can be derived from a Global Calendar, or they can have no parent calendar.</p>
<p>Finally you can have two types of resource calendar: Shared, or Personal.
These can either be derived from a Global calendar, or can have no parent.
A Shared resource calendar can be assigned to multiple resources, but a Personal
resource calendar can only be assigned to a single resource.</p>
<p>When reading a P6 schedule, the <code>ProjectCalendar</code> method <code>getType</code> can be used
to retrieve the calendar type (Global, Shared, or Personal), while the
<code>getPersonal</code> method returns a Boolean flag indicating if the calendar is a
Personal resource calendar.</p>
<h3 id="others">Others</h3>
<p>ConceptDraw, Planner, SureTrak and TurboProject all support some form of
calendar hierarchy, although Planner is the only one which definitely supports
an arbitrarily deep nested calendar structure.</p>
<h2 id="calendar-container">Calendar Container</h2>
<p>So far we've looked at creating and configuring calendars, and lining them
together in a hierarchy. If we've just read a schedule in from a file, how can
we examine the calendars it contains? Let's set up some calendars and take a
look:</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectFile</span><span class="w"> </span><span class="n">file</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ProjectFile</span><span class="p">();</span>
<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addCalendar</span><span class="p">();</span>
<span class="n">calendar1</span><span class="p">.</span><span class="na">setName</span><span class="p">(</span><span class="s">&quot;Calendar 1&quot;</span><span class="p">);</span>

<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addCalendar</span><span class="p">();</span>
<span class="n">calendar2</span><span class="p">.</span><span class="na">setName</span><span class="p">(</span><span class="s">&quot;Calendar 2&quot;</span><span class="p">);</span>

<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addCalendar</span><span class="p">();</span>
<span class="n">calendar3</span><span class="p">.</span><span class="na">setName</span><span class="p">(</span><span class="s">&quot;Calendar 3&quot;</span><span class="p">);</span>
</code></pre></div>
<p>Our sample code above creates three calendars, each with a distinct name. To see
what calendars our file contains we can use the <code>ProjectFile</code> method
<code>getCalendars</code>:</p>
<div class="highlight"><pre><span></span><code><span class="n">file</span><span class="p">.</span><span class="na">getCalendars</span><span class="p">().</span><span class="na">forEach</span><span class="p">(</span><span class="n">c</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">c</span><span class="p">.</span><span class="na">getName</span><span class="p">()));</span>
</code></pre></div>
<p>Which gives us the following output, as we'd expect:</p>
<div class="highlight"><pre><span></span><code>Calendar 1
Calendar 2
Calendar 3
</code></pre></div>
<p>The <code>getCalendars</code> method returns an object which implements the
<code>List&lt;ProjectCalendar&gt;</code> interface, but it also does more for us than just that.
The actual object being returned is a <code>ProjectCalendarContainer</code>, which is in
charge of managing the calendars in the file and making it easy to access
them.</p>
<p>The typical way this is done is through the use of the calendar's Unique ID
attribute. Each calendar has an <code>Integer</code> Unique ID, typically this is
read as part of the calendar information from a schedule file, or if you are
creating a schedule yourself, the default is for the Unique ID to be
automatically populated. Let's see:</p>
<div class="highlight"><pre><span></span><code><span class="n">file</span><span class="p">.</span><span class="na">getCalendars</span><span class="p">().</span><span class="na">forEach</span><span class="p">(</span><span class="n">c</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">c</span><span class="p">.</span><span class="na">getName</span><span class="p">()</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; (Unique ID: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">c</span><span class="p">.</span><span class="na">getUniqueID</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;)&quot;</span><span class="p">));</span>
</code></pre></div>
<p>Here's what we get:</p>
<div class="highlight"><pre><span></span><code>Calendar 1 (Unique ID: 1)
Calendar 2 (Unique ID: 2)
Calendar 3 (Unique ID: 3)
</code></pre></div>
<p>Let's use a Unique ID to retrieve a calendar:</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">getCalendars</span><span class="p">().</span><span class="na">getByUniqueID</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="n">calendar</span><span class="p">.</span><span class="na">getName</span><span class="p">());</span>
</code></pre></div>
<p>Here's the result of running this code:</p>
<div class="highlight"><pre><span></span><code>Calendar 2
</code></pre></div>
<blockquote>
<p>The <code>ProjectCalendarContainer</code> class also allows us to retrieve calendars by
name, although that's not recommended as MPXJ doesn't enforce presence or
uniqueness constraints on calendar names.</p>
</blockquote>
<p>Most of the time accessing a calendar from some other part of MPXJ is handled
for you, for example to retrieve a resource's calendar you just need to call
the <code>Resource</code> method <code>getCalendar</code> rather than having to use
<code>ProjectCalendarContainer</code> to retrieve it by Unique ID.</p>
<h2 id="calendar-relationships">Calendar Relationships</h2>
<p>The <code>ProjectCalendar</code> class provides a variety of methods to allow us to explore
how it relates to other calendars and the rest of the schedule.</p>
<p>As we've been discussing the hierarchy of calendars, the first method we can try
is <code>isDerived</code>, which will return <code>true</code> if this calendar has been derived from
a parent calendar. Alongside this we can also use the <code>getParent</code> method to 
retrieve this calendar's parent. We can traverse a hierarchy of calendars using
this method until <code>getParent</code> returns <code>null</code> at which point we know we have
reached a "base" calendar and can go no further.</p>
<p>Calendars can also be assigned to both Tasks and Resources. The <code>getTasks</code> and
<code>getResources</code> methods will each retrieve a list of the tasks and resources
which explicitly use this calendar.</p>
<p>Finally, earlier in this section we mentioned the idea of the default calendar
for a project. We can set or retrieve the default calendar using the
<code>ProjectFile</code> methods <code>setDefaultCalendar</code> and <code>getDefaultCalendar</code>, as
illustrated below.</p>
<div class="highlight"><pre><span></span><code><span class="n">ProjectFile</span><span class="w"> </span><span class="n">file</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ProjectFile</span><span class="p">();</span>
<span class="n">ProjectCalendar</span><span class="w"> </span><span class="n">calendar</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">addDefaultBaseCalendar</span><span class="p">();</span>
<span class="n">file</span><span class="p">.</span><span class="na">setDefaultCalendar</span><span class="p">(</span><span class="n">calendar</span><span class="p">);</span>
<span class="n">System</span><span class="p">.</span><span class="na">out</span><span class="p">.</span><span class="na">println</span><span class="p">(</span><span class="s">&quot;The default calendar name is &quot;</span>
<span class="w">   </span><span class="o">+</span><span class="w"> </span><span class="n">file</span><span class="p">.</span><span class="na">getDefaultCalendar</span><span class="p">().</span><span class="na">getName</span><span class="p">());</span>
</code></pre></div>
<p>As the name suggests, the default calendar will be used for all date, time,
duration and work calculations if no other calendar has been assigned
explicitly.</p>









  




                
              </article>
            </div>
          
          
  <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var tab,labels=set.querySelector(".tabbed-labels");for(tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script>

<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["content.tabs.link", "content.code.copy"], "search": "../assets/javascripts/workers/search.b8dbb3d2.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../assets/javascripts/bundle.dd8806f2.min.js"></script>
      
    
  </body>
</html>