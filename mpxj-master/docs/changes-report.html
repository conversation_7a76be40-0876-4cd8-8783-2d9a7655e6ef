<!DOCTYPE html>
<!--
 | Generated by Apache Maven Doxia Site Renderer 1.9.2 from org.apache.maven.plugins:maven-changes-plugin:2.12.1:changes-report
 | Rendered using Apache Maven Fluido Skin 1.9
-->
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content="Apache Maven Doxia Site Renderer 1.9.2" />
    <meta name="author" content="<PERSON>" />
    <title>MPXJ &#x2013; MPXJ Changes</title>
    <link rel="stylesheet" href="./css/apache-maven-fluido-1.9.min.css" />
    <link rel="stylesheet" href="./css/site.css" />
    <link rel="stylesheet" href="./css/print.css" media="print" />
    <script src="./js/apache-maven-fluido-1.9.min.js"></script>
  </head>
  <body class="topBarDisabled">
    <a href="https://github.com/joniles/mpxj">
      <img style="position: absolute; top: 0; right: 0; border: 0; z-index: 10000;"
        src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png"
        alt="Fork me on GitHub">
    </a>
    <div class="container-fluid">
      <header>
        <div id="banner">
          <div class="pull-left"><div id="bannerLeft"><h2>MPXJ</h2>
</div>
</div>
          <div class="pull-right"></div>
          <div class="clear"><hr/></div>
        </div>

        <div id="breadcrumbs">
          <ul class="breadcrumb">
        <li id="publishDate">Last Published: 2025-06-05</li>
          </ul>
        </div>
      </header>
      <div class="row-fluid">
        <header id="leftColumn" class="span3">
          <nav class="well sidebar-nav">
  <ul class="nav nav-list">
   <li class="nav-header">MPXJ</li>
    <li><a href="summary.html" title="Summary"><span class="none"></span>Summary</a></li>
    <li class="active"><a href="#"><span class="none"></span>Changes</a></li>
    <li><a href="team.html" title="Team"><span class="none"></span>Team</a></li>
    <li><a href="mailing-lists.html" title="Mailing List"><span class="none"></span>Mailing List</a></li>
    <li><a href="issue-management.html" title="Issues"><span class="none"></span>Issues</a></li>
    <li><a href="scm.html" title="Source"><span class="none"></span>Source</a></li>
    <li><a href="apidocs/index.html" title="Javadoc"><span class="none"></span>Javadoc</a></li>
    <li><a href="dependency-updates-report.html" title="Dependencies"><span class="none"></span>Dependencies</a></li>
    <li><a href="plugin-updates-report.html" title="Plugins"><span class="none"></span>Plugins</a></li>
  </ul>
          </nav>
          <div class="well sidebar-nav">
            <hr />
            <div id="poweredBy">
              <div class="clear"></div>
              <div class="clear"></div>
              <div class="clear"></div>
<a href="http://maven.apache.org/" title="Built by Maven" class="poweredBy"><img class="builtBy" alt="Built by Maven" src="./images/logos/maven-feather.png" /></a>
            </div>
          </div>
        </header>
        <main id="bodyColumn"  class="span9" >
<section>
<h2><a name="MPXJ_Changes"></a>MPXJ Changes</h2><section>

<p>Get the RSS feed of the last changes:&#160;<a href="changes.rss"><img src="images/rss.png" alt="rss feed" title="rss feed" /></a></p></section><section>
<h3 id="a14.2.0">Release 14.2.0 &#x2013; unreleased</h3>
<p>No changes in this release.</p></section><section>
<h3 id="a14.1.0">Release 14.1.0 &#x2013; 2025-06-05</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 5.4.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve actual duration calculation for activities with suspend and resume dates when reading XER files and Primavera P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added Enable Summarization and Enable Publication flags to `ProjectProperties`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for Enable Summarization and Enable Publication flags when reading and writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Actual Start and Actual Finish attributes in `ProjectProperties` are populated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve retrieval of Enterprise Custom Field names when reading MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the JSON writer to use Jackson.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a14.0.0">Release 14.0.0 &#x2013; 2025-05-07</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>**NEW FEATURES**</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>MPXJ can now schedule projects using CPM (Critical Path Method)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Two new classes (`MicrosoftScheduler` and `PrimaveraScheduler`) allow MPXJ to schedule a project in a way which follows the approach of either Microsoft Project or Primavera P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading Edraw Project EDPX files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>**CHANGES**</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improvements to accuracy of reading text UDF values from Powerproject PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected conversion of elapsed durations when writing JSON files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Relation#lag_units` method to the ruby gem.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>**BREAKING CHANGES - .Net**</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `net.sf.mpxj`, `net.sf.mpxj-for-csharp`, and `net.sf.mpxj-for-vb` NuGet packages are no longer being distributed. You must migrate your code to use the `MPXJ.Net` NuGet package instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>**BREAKING CHANGES - Java, Python**</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The name of the package containing MPXJ's Java classes has changed from `net.sf.mpxj` to `org.mpxj`. You will need to update your code by searching for `net.sf.mpxj` and replace this with `org.mpxj`. NOTE: for Java applications using Maven, the Maven Group ID **has not changed**, you will still retrieve MPXJ using the Group ID `net.sf.mpxj`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The constant `TaskField.PRIMARY_RESOURCE_ID` has been renamed to `TaskField.PRIMARY_RESOURCE_UNIQUE_ID`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `RelationContainer#getRawSuccessors` method has been removed. Use the `RelationContainer#getSuccessors` method instead. This method now returns the same data `getRawSuccessors` returned previously.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `UserDefinedField` constructors have been removed, use `UserDefinedField.Builder` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `UserDefinedField#setDataType` method has been removed, use the `UserDefinedField.Builder#dataType` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `StructuredNotes` constructor has been removed, use the `StructuredNotes` constructor taking a `ProjectFile` instance instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Relation#getSourceTask` and `Relation#getTargetTask` methods have been removed, use `Relation#getPredecessorTask` and `Relation#getSuccessorTask` methods instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Relation.Builder#sourceTask` and `Relation.Builder#targetTask` methods have been removed, use `Relation.Builder#predecessorTask` and `Relation.Builder#successorTask` methods instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue#getType` method has been removed. Use the `ActivityCodeValue#getParentCode` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue#getActivityCode` method has been removed. Use the `ActivityCodeValue#getParentCode` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue#getParent` method has been removed. Use the `ActivityCodeValue#getParentValue` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue#getParentUniqueID` method has been removed. Use the `ActivityCodeValue#getParentValueUniqueID` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue.Builder#type` method has been removed. Use the `ActivityCodeValue.Builder#activityCode` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue.Builder#parent` method has been removed. Use the `ActivityCodeValue.Builder#parentValue` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task#addActivityCode` method has been removed. Use the `Task#addActivityCodeValue` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `GanttBarStyleException#getBarStyleIndex` method has been removed. Use `GanttBarStyleException#getGanttBarStyleID` to retrieve the bar style ID, and `GanttChartView#getGanttBarStyleByID` to retrieve the style</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated constant `TaskField.ACTIVITY_CODE_LIST` has been removed. Use `TaskField.ACTIVITY_CODE_VALUES` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task#getActivityCodes` method has been removed. Use the `Task#getActivityCodeValues` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task#setPrimaryResourceID` method has been removed. Use the `Task#setPrimaryResourceUniqueID` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task#getPrimaryResourceID` method has been removed. Use the `Task#getPrimaryResourceUniqueID` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task#isSucessor` method has been removed. Use the `Task#isSuccessor` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The common `MPPUtility` static methods `getShort`, `getInt`and `getLong` have been moved to the `ByteArrayHelper` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>**BREAKING CHANGES - Ruby**</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated Ruby attribute `Relation#task_unique_id` has been removed, use `Relation#predecessor_task_unique_id` and `Relation#successor_task_unique_id` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.12.0">Release 13.12.0 &#x2013; 2025-04-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading Float Path and Float Path Order from XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for writing Float Path and Float Path Order to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading baselines from Phoenix schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve date arithmetic when using the `ProjectCalendar#getDate()` method with elapsed durations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Include units percent complete when writing resource assignments to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of resource assignment remaining units when writing XER and PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MSPDI files, calculate resource assignment remaining work if not present.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.11.0">Release 13.11.0 &#x2013; 2025-03-10</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading the WBS and Activity Methodology GUID attribute from XER files and P6 databases, and for writing this to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of resource assignment start and finsh dates when reading XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fixed an issue reading resource code value hierarchy from XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve retrieval of Gantt Bar Styles from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `GanttBarStyleException` methods `getGanttBarStyleID()` and `setGanttBarStyleID()`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `GanttChartView` method `getGanttBarStyleByID()`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `GanttBarStyleException#getBarStyleIndex()` method as deprecated. Use the `GanttBarStyleException#getGanttBarStyleID()` method to retrieve the Gantt Bar Style ID, then use the view's `getGanttBarStyleByID()` method to retrieve a list of matching styles.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Duration#negate()` method to simplify negating a duration.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve provision of default values for Project Planned Start date and Activity Planned Duration when writing XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.10.0">Release 13.10.0 &#x2013; 2025-02-07</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading the P6 EPS using the `listEps()` method provided by the `PrimaveraDatabaseReader` and `PrimaveraDatabaseFileReader` classes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of Activity Type attribute when reading PMXML files written by Primavera P6 6.x.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the External Early Start and External Late Finish attributes are written to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix a NPE when calling `PrimaveraXERFileReader.listProjects()`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid unnecessary data storage and type conversion to improve efficiency when calling `PrimaveraXERFileReader.listProjects()`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provide additional `ResourceAssignment` methods to allow `List&lt;TimephasedWork&gt;` to be used to add timephased work, rather than requiring a `TimephasedWorkContainer`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve identification of tasks when reading certain Asta Powerproject PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.9.0">Release 13.9.0 &#x2013; 2025-01-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 5.4.0</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML schema to version 24.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing currencies for Primavera P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve recognition of dates displayed as NA in Microsoft Project when reading certain MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ignore invalid cost rate table entries when reading certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.8.0">Release 13.8.0 &#x2013; 2024-12-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing Project Codes, Resource Codes, Role Codes and Resource Assignment Codes for Primavera P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing PMXML files, improve handling of P6 schedules where activity code sequence numbers are missing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added an *experimental* feature to `MSPDIWriter` to allow the writer to generate timephased data when none is present. Disabled by default, call the `setGenerateMissingTimephasedData` and pass `true` to enable.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>To improve consistency, the methods `Task.getPrimaryResourceID()` and `Task.setPrimaryResourceID()` have been marked as deprecated. Use the new `Task.getPrimaryResourceUniqueID()` and `Task.setPrimaryResourceUniqueID()` methods instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the methods `Task.getPrimaryResource()` and `Task.setPrimaryResource()`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved accuracy of retrieving the resource assignment GUID attribute when reading MPP files (Contributed by Fabian Schmidt).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve population of Task Start and Finish attributes when reading Primavera P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue.getParent()` method as deprecated. Use `ActivityCodeValue.getParentValue()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue.getParentUniqueID()` method as deprecated. Use `ActivityCodeValue.getParentValueUniqueID()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue.Builder.parent()` method as deprecated. Use `ActivityCodeValue.Builder.parentValue()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue.getActivityCode()` method as deprecated. Use `ActivityCodeValue.getParentCode()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.7.0">Release 13.7.0 &#x2013; 2024-11-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update the MPXJ ruby gem to allow access to calendar data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Mark the `ActivityCodeValue.getType()` method as deprecated. For clarity this method has been replaced by the new `ActivityCodeValue.getActivityCode()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Mark the `ActivityCodeValue.Builder.type()` method as deprecated. For clarity this method has been replaced by the new `ActivityCodeValue.Builder.activityCode()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Task.getActivityCodeValues()` method, which returns a `Map` of `ActivityCodeValue` instances, keyed by `ActivityCode`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Task.getActivityCodes()` method as deprecated. Replaced with the `Task.getActivityCodeValues()` method which is more clearly named, and presents the activity code values in a more flexible form.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Task.addActivityCodeValue()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Task.addActivityCode()` method as deprecated. Replaced with the `Task.addActivityCodeValue()` method which is more clearly named.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Further improvements to retrieval of custom field values read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that missing resource assignment and task start and finish dates are handled gracefully when working with calendars for manually scheduled tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.6.0">Release 13.6.0 &#x2013; 2024-11-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Task.getBaselineTask()` methods. For applications where a separate baseline schedule is present or a baseline has been manually added to the `ProjectFile` instance, these methods will allow you to access the underlying baseline task instance from the current task instance.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Activity Percent Complete attribute to the `Task` class. The value of this attribute will be the Duration, Physical or Units percent complete value, based on the Percent Complete Type setting. This attribute is provided as a convenience to match the Activity Percent Complete type value shown in P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve retrieval of custom field values for certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of PMXML files with more than 11 baselines.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of unexpected data types when writing JSON files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Relation.getPredecessorTask()` and `Relation.getSuccessorTask()` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Relation.getSourceTask()` and `Relation.getTargetTask()` methods as deprecated, use the `Relation.getPredecessorTask()` and `Relation.getSuccessorTask()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that with &quot;Link Cross Project Relations&quot; enabled when reading XER or PMXML files, the predecessor and successor lists for both tasks related acrosss projects are correctly populated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.5.1">Release 13.5.1 &#x2013; 2024-10-28</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix CVE-2024-49771: Potential Path Traversal Vulnerability (Contributed by yyjLF and sprinkle).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.5.0">Release 13.5.0 &#x2013; 2024-10-17</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing Resource Role Assignments for Primavera schedules. The `Resource.getRoleAssignments()` method retrieves a map representing the roles a resource is assigned to, along with the skill level for each assignment. The `Resource.addRoleAssignment()` and `Resource.removeRoleAssignment()` methods allow role assignments to be added and removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for the Resource Primary Role attribute, which is read from and written to Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling Boolean attributes with default values when reading XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `getShowStartText`, `getShowFinishText` and `getShowDurationText` methods to the `Task` class. When working with manually scheduled tasks in Microsoft Project, users can potentially supply arbitrary text for the Start, Finish and Duration attributes. Microsoft Project still stores appropriate values for these attributes, which can be accessed in MPXJ as Start, Finish and Duration, but where the user has supplied text, these attributes are available as Start Text, Finish Text, and Duration Text. The methods added by this change allow the caller to determine which version of each attribute should be shown to the user in order to replicate what they see in Microsoft Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.4.2">Release 13.4.2 &#x2013; 2024-10-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ProjectCalendarDays.getCalendarHours()` method to allow direct access to the `ProjectCalendarHours` instances for each day of the week.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.4.1">Release 13.4.1 &#x2013; 2024-10-07</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ProjectCalendarDays.getCalendarDayTypes()` method to allow direct access to the `DayType` instances for each day of the week.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.4.0">Release 13.4.0 &#x2013; 2024-09-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing resource shifts for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Scheduling Progressed Activities project property is populated when reading Phoenix schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading milestones from an Asta schedule, ensure that the Activity Type attribute is populated to allow start milestones and finish milestones to be differentiated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue which occurred when writing MSPDI files with manually scheduled tasks starting on non-working days where their timephased data is split as days.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.3.1">Release 13.3.1 &#x2013; 2024-08-30</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle duplicate custom field value unique IDs when reading MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle missing remaining early start date when reading timephased data from a P6 schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.3.0">Release 13.3.0 &#x2013; 2024-08-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading multiple Primavera schedules from the same source, MPXJ now ensures that instances of activity code definitions, user defined field definitions, locations, units of measure, expense categories, cost accounts, work contours, and notes topics are shared across projects. This will allow you to, for example, filter tasks from multiple projects using a `Location` instance. Previously each project had its own independent instances for each of these types, which could not be used across multiple projects.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading Powerproject schedules, ensure that the Activity ID attribute for WBS entries is populated using Powerproject's Unique Task ID attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading timephased planned work from MPP files for manually scheduled tasks (Contributed by Fabian Schmidt).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.2.2">Release 13.2.2 &#x2013; 2024-08-14</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add missing constructors to `TimephasedCost` and `TimephasedWork` in MPXJ.Net.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.2.1">Release 13.2.1 &#x2013; 2024-08-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Make the MPXJ.Net assembly strong named.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.2.0">Release 13.2.0 &#x2013; 2024-08-12</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `UserDefinedField.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `UserDefinedField` constructor as deprecated. Use the builder class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `UserDefinedField.setDataType()` method as deprecated. Use the builder class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to address an issue when writing XER files where a project does not have an explicit Unique ID value, and there are project UDF values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the convenience method `ActivityCode.addValue` to make it easier to add a value to an activity code.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.1.0">Release 13.1.0 &#x2013; 2024-07-26</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 5.3.0</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading and writing timephased data for activities in P6 schedules which have a &quot;manual&quot; curve. (Note: MPXJ does not currently support translating timephased data between different applications, so timephased data read from an MPP file won't be written to a P6 schedule and vice versa).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add an attribute to the `ResourceAssignment` class to represent timephased planned work. This is read from/written to P6 as Budgeted Work.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update Phoenix schemas to ensure that cost types are represented as doubles.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to avoid reading apparently invalid resources from Project Commander files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correct the `Finish` attribute for resource assignments when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of the `RemainingDuration` attribute for resource assignments when writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve recognition of non-working days when reading calendars certain PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for the Resource Assignment field Remaining Units. (Note: this field defaults to the same value as Units if it is not explicitly populated).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Resource Assignment field Remaining Units is read from and written to P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of invalid calendar exception data when reading P6 schedules from XER files or a P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve the implementation of the Unique ID sequence generator used by MPXJ to avoid issues when multiple classloaders are used.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the original `StructuredNotes` constructor. A new version of the constructor takes an additional `ProjectFile` argument.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the original `UserDefinedField` constructor. A new version of the constructor takes an additional `ProjectFile` argument.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading and writing the Project Website URL attribute for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for the Notes attribute as part of the `ProjectProperties` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that project notes are read from and written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Usability improvements to the Notes class hierarchy to make it easier to update notes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improvements to notes handling when writing PMXML files to make it easier to construct structured notes using plain text.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.0.2">Release 13.0.2 &#x2013; 2024-07-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing XER files, provide a default value for the Resource ID if it is not populated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.0.1">Release 13.0.1 &#x2013; 2024-07-04</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For XER files, ignore the &quot;null&quot; resource when writing resource rates.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading MPP files, ensure that Enterprise Custom Field Unique IDs are unique across entities.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a13.0.0">Release 13.0.0 &#x2013; 2024-06-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>NOTE: this is a major release containing breaking changes. When updating from a 12.x release it is recommended that you first update to the most recent 12.x release and deal with any deprecation warnings before moving to this release.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>NOTE: the [original `net.sf.mpxj` NuGet packages](https://www.nuget.org/packages?q=net.sf.mpxj) are now deprecated and will be replaced by the [MPXJ.Net NuGet Package](https://www.nuget.org/packages/MPXJ.Net) in the next major MPXJ release. The `net.sf.mpxj` packages will continue to be maintained until then, at which point they will no longer be distributed. Please migrate your code to use MPXJ.Net at the earliest opportunity, and open an issue in the GitHub issue tracker if you encounter problems.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use JAXB3. Among other things this change ensures compatibility with Spring Boot 3. Note that this may be a breaking change for you if you own application uses JAXB2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading P6 schedules, the custom properties (as retrieved using `ProjectProperties.getCustomProperties`) will no longer contain scheduling options. These are now all available as attributes of the `ProjectProperties` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed redundant `setUniqueID` methods from immutable objects. These previously threw `UnsupportedOperationException` when called.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectEntityWithUniqueID` interface no longer contains the `setUniqueID` method. Entities with a mutable Unique ID attribute now implement the `ProjectEntityWithMutableUniqueID` interface, which inherits from the `ProjectEntityWithUniqueID` interface.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `MSPDIReader` and `PrimaveraXERFileReader` classes no longer provide getter and setter methods for `Encoding`, use the `Charset` getter and setter methods instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed the `XerFieldType` class and replaced usages of it with the `DataType` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCode()` constructor and `addValue` method have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ActivityCodeValue()` constructor and `setParent` method have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `CostAccount()` constructor and `getDescription` method have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `CustomFieldValueItem` methods `getParent` and `setParent` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ExpenseCategory()` constructor has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ExpenseItem(Task)` constructor and all setter methods have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `JsonWriter` methods `setEncoding` and `getEncoding` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Location.Builder()` constructor has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `NotesTopic()` constructor has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ObjectSequence` method `reset` has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `PlannerWriter` methods `setEncoding` and `getEncoding` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `PrimaveraXERFileWriter` method `setEncoding` has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectCalendar` method `getDate` has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectCalendarHelper` method `getExpandedExceptionsWithWorkWeeks` has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectEntityContainer` methods `getNextUniqueID`, `renumberUniqueIDs` and `updateUniqueIdCounter` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectFile` methods `expandSubprojects` and `updateUniqueIdCounters` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectReader` method `setProperties` and `setCharset` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ProjectWriterUtility` class has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `RateHelper` methods accepting a `ProjectFile` argument have veen removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Relation(Task,Task,RelationType,Duration)` constructor has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `RelationContainer.addPredecessor(Task,Task,RelationType,Duration)` method has been removed</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Resource` methods `setAvailableFrom`, `setAvailableTo`, `setMaterialLabel` and `setMaxUnits` have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `ResourceAssignment` method `getCalendar` has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Step(Task)` constructor and all setter methods have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `Task` method `addPredecessor(Task,RelationType,Duration)` has been removed</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `TimephasedUtility` methods `segmentBaselineWork(ProjectFile, ...)` and `segmentBaselineCost(ProjectFile, ...)` methods have been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The deprecated `UnitOfMeasure.Builder()` constructor has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.10.3">Release 12.10.3 &#x2013; 2024-06-14</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add new project property `IsProjectBaseline`. When using the `readAll` method to retrieve a set of schedules, if the data source contains both schedules and baselines this property will be true for the `ProjectFile` instances which represent a baseline.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.10.2">Release 12.10.2 &#x2013; 2024-06-03</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added a missing unique ID mapping when writing resource assignment resource unique IDs to MSPDI files (Contributed by Alex Matatov)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle null field type when reading outline code values from an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.10.1">Release 12.10.1 &#x2013; 2024-05-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ignore missing `PropertySet`s when reading MPP files (Contributed by Fabian Schmidt).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected handling of the &quot;24 Hour Calendar&quot; Relationship Lag Calendar setting when reading and writing XER files (Based on a contribution by Alex Matatov)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.10.0">Release 12.10.0 &#x2013; 2024-05-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When a baseline is added using one of the `ProjectFile.setBaseline` methods, ensure that the relevant baseline date is set in `ProjectProperties`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `JsonWriter` methods `setEncoding` and `getEncoding` as deprecated, use `setCharset` and `getCharset` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `PlannerWriter` methods `setEncoding` and `getEncoding` as deprecated, use `setCharset` and `getCharset` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `PrimaveraXERFileWriter` method `setEncoding` as deprecated, use `setCharset` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ProjectCalendarHelper` method `getExpandedExceptionsWithWorkWeeks` as deprecated, use `ProjectCalendar.getExpandedCalendarExceptionsWithWorkWeeks` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ProjectReader` method `setCharset` as deprecated. Readers which support setting the Charset now implement the `HasCharset` interface, which includes Charset getter and setter methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `UniversalProjectWriter` class. This complements the `UniversalProjectReader` class by providing a simple way for MPXJ users to write project files without having to be concerned with details of the individual `ProjectWriter` classes. This is intended to replace the `ProjectWriterUtility` class. Note that the `ProjectWriterUtility` has a somewhat brittle mechanism to determine the output file format from the supplied output file name. This is not replicated by `UniversalProjectWriter`, users are expected to provide their own code to determine the appropriate file format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ProjectWriterUtility` class as deprecated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.9.3">Release 12.9.3 &#x2013; 2024-04-24</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of non-standard timestamp formats in XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.9.2">Release 12.9.2 &#x2013; 2024-04-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure calendars in Asta schedules have the correct name.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve assignment of calendars to summary tasks when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Preserve calendar hierarchy when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.9.1">Release 12.9.1 &#x2013; 2024-04-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue where `UniversalProjectReader` would raise an exception when handling an unknown file type.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that resource type is included as part of the resource assignment data when writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.9.0">Release 12.9.0 &#x2013; 2024-04-11</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `UniversalProjectReader` to add `getProjectReaderProxy` methods to allow access to the instance of the reader class which will be used to read a schedule, prior to the schedule being read. This will allow the reader to be configured, or schedule to be ignored without reading its content.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `ProjectReader.setProperties` method. This method was originally implemented to allow settings to be passed to reader classes when using `UniversalProjectReader`. You can now use `UniversalProjectReader.getProjectReaderProxy` to achieve this.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add `from` method to all `Builder` classes to allow initialisation from existing objects.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `CostAccount.Builder` class now provides two `notes` methods to allow formatted or unformatted notes to be added to cost accounts.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `CostAccount` method `getDescription()` has been marked as deprecated. Use the `getNotes()` or `getNotesObject()` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `CustomFieldValueItem` methods `getParent` and `setParent` have been marked as deprecated. Use the `getParentUniqueID` and `setParentUniqueID` methods instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>JSON output from MPXJ now includes more detail for custom field definitions read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading a PMXML file, populate the Early/Late Start/Finish date attributes from the Remaining Early/Late Start/Finish date attributes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading WBS ID for P3 and SureTrak schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.8.1">Release 12.8.1 &#x2013; 2024-03-11</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading resource assignments from certain FastTrack FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.8.0">Release 12.8.0 &#x2013; 2024-03-04</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add experimental support for reading Deltek Open Plan BK3 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `Relation.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Relation(Task,Task,RelationType,Duration)` constructor as deprecated, use the `Relation.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `RelationContainer.addPredecessor(Task,Task,RelationType,Duration)` method as deprecated, use the `RelationContainer.addPredecessor(Relation.Builder)` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Task.addPredecessor(Task,RelationType,Duration)` method as deprecated, use the `Task.addPredecessor(Relation.Builder)` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add a notes attribute to the `Relation` class and ensure that it is read from and written to P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read the Relationship Lag Calendar setting from Phoenix 5 files. (Contributed by Rohit Sinha)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write a material label to an MSPDI file for a resource which isn't a material.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update representation of Work Variance when writing MSPDI files to more closely match output from Microsoft Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to ensure that when schedules are read from XER files or P6 databases, labor and nonlabor work amounts are combined for the Actual, Remaining and Planned work attributes. This is now consistent with the existing behavior when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for new Task attributes Actual Work Labor, Actual Work Nonlabor, Remaining Work Labor, Remaining Work Nonlabor, Planned Work Labor, Planned Work Nonlabor, when reading and writing P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update default `readAll` method on reader classes to ensure that if the reader is unable to read any schedule data, an empty list is returned rather than a list containing `null`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that Task Start and Finish dates are both the same when reading milestones from PMXML files, and that the correct date is used depending on whether we have a Start Milestone or a Finish Milestone.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.7.0">Release 12.7.0 &#x2013; 2024-02-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing the project property Baseline Calendar Name to and from MPP and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Start Variance and Finish Variance are read from and written to MSPDI files in the correct format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of large Work Variance values read from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for the Calendar GUID attribute, which is read from MPP and MSPDI files, and written to MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Activity Codes are available when reading Phoenix PPX files even if they are also being used to construct the task hierarchy.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Activity Codes Values are populated when reading Phoenix PPX files. (Contributed by Rohit Sinha)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing an MSPDI file, derive the TimephasedData Unit attribute from the duration of the timephased data item.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fixed an issue with the `ProjectCalendar.getPreviousWorkFinish` method when called with a time which was already at the end of a period of work.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the `proj_node_flag` is set for the root WBS node when writing XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.6.0">Release 12.6.0 &#x2013; 2024-01-22</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML schema to version 23.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that baselines in PMXML files written by Oracle Primavera Cloud are read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading certain XER files and P6 databases where activities lost the relationship with their parent WBS entry.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `ResourceAssignment.getEffectiveCalendar` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated `ResourceAssignment.getCalendar` method, use `getEffectiveCalendar` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved reading timephased baseline work from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added new versions of the `TimephasedUtility.segmentBaselineWork` and `segmentBaselineCost` methods which take a `ProjectCalendar` instance as the first argument rather than a `ProjectFile` instance.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `TimephasedUtility.segmentBaselineWork` and `segmentBaselineCost` methods which take a `ProjectFile` instance as the first argument.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added a new version of the `ProjectCalendar.getDate()` method which just takes a date and a duration as its arguments. This method handles both positive and negative durations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the original version of the `ProjectCalendar.getDate()` method as deprecated. Use the new version instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve recognition of task splits when reading MPP and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.5.0">Release 12.5.0 &#x2013; 2023-12-18</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for the following Resource Assignment attributes: Remaining Early Start, Remaining Early Finish, Remaining Late Start, and Remaining Late Finish.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the Resource Assignment attributes Remaining Early Start and Remaining Early Finish are read from and written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the Resource Assignment attributes Remaining Early Start, Remaining Early Finish, Remaining Late Start, and Remaining Late Finish are read from and written to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of reading and writing the `ProjectProperties` Relationship Lag Calendar attribute for PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All P6 scheduling and leveling options which were previously made available via the `ProjectProperties` custom properties map are now deprecated. These properties now have individual getter and setter methods available on the `ProjectProperties` class. Note: this may be a breaking change if you were creating schedules from scratch, populating the custom properties map, then writing PMXML or XER files. In this case you will need to update your code, for all other use cases your code will continue to work unchanged until the next major version of MPXJ.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing the `ProjectProperties` attributes Baseline Type Name, Baseline Type Unique ID, and Last Baseline Update Date for baseline projects in PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading projects from PMXML files, if the creation date attribute is not present in the file fall back to populating the `ProjectProperties` creation date attribute with the PMXML date added attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing PMXML files, ensure the date added attribute for projects is populated with the creation date.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add the `CustomFieldContainer.remove` method to allow field configurations to be removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the `UserDefinedFieldContainer.remove` method to ensure that any associated field configuration is removed from the `CustomFieldContainer`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that Microsoft Project's &quot;unknown&quot; resource (with Unique ID zero) is not exported to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that resource assignments which are not associated with an Activity or a Resource are not written to XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Durations are written to PMXML files in hours. We now round to 2 decimal places to allow minutes to be represented, and avoid unnecessary precision.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Currency amounts written to PMXML files are now rounded to 8 decimal places to more closely match the behavior of P6, and avoid unnecessary precision.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Decimal amounts other than currency and duration are written to PMXML files with 15 decimal places to more closely match the behavior of P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading ConceptDraw calendars.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fixed a misspelled field name in the JSON output (Contributed by Daniel Taylor).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved handling of the Resource Assignment Planned and Remaining Units and Units per Time attributes read from and written to P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for the following project properties: Activity ID Prefix, Activity ID Suffix, Activity ID Increment and Activity ID Based On Selected Activity, and ensure these are read from and written to P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.4.0">Release 12.4.0 &#x2013; 2023-11-23</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for the WBS Code Separator attribute to `ProjectProperties`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid creating duplicate `ActivityCodeValue` instances when reading Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added a new version of the `ProjectFile.expandSubprojects` method which takes a `boolean` argument indicating if external tasks should be removed. Passing `true` to this method will recreate predecessor and successor relationships using the original tasks rather than the placeholder external tasks, and will remove the external tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ProjectFile.expandSubprojects()` method as deprecated, use the new version which takes a `boolean` argument instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the `ProjectProperties` name attribute is set correctly when reading XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectEntityContainer` method `renumberUniqueIDs` has been marked as deprecated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectEntityContainer` method `getNextUniqueID` has been marked as deprecated. Use `ProjectFile.getUniqueIdObjectSequence(class).getNext()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectEntityContainer` method `updateUniqueIdCounter` has been marked as deprecated as it is no longer required.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectFile` method `updateUniqueIdCounters` has been marked as deprecated as it is no longer required.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ObjectSequence` method `reset` has been marked as deprecated as it is no longer required.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When creating a `Location` instance using the `Builder` class, a Unique ID will be generated if one is not supplied.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The no-arg `Location.Builder` constructor has been marked a deprecated. Use the constructor which requires a `ProjectFile` instance instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `ExpenseItem.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ExpenseItem(task)` constructor as deprecated, use the `ExpenseItem.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked all `ExpenseItem` setter methods a deprecated. The `ExpenseItem` class will be immutable in the next major release.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked no-arg `UnitOfMeasure.Builder()` constructor as deprecated, use the `UnitOfMeasure.Builder(ProjectFile)` constructor instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `Step.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Step(task)` constructor as deprecated, use the `Step.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked all `Step` setter methods a deprecated. The `Step` class will be immutable in the next major release.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `NotesTopic` constructor as deprecated, use the `NotesTopic.Builder(ProjectFile)` constructor instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `ExpenseCategory.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ExpenseCategory` constructor as deprecated, use the `ExpenseCategory.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `CostAccount.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `CostAccount` constructor as deprecated, use the `CostAccount.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `ActivityCodeValue.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue` constructor as deprecated, use the `ActivityCodeValue.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCodeValue.setParent` method as deprecated, use the `ActivityCodeValue.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCode.addValue` method as deprecated, use the `ActivityCodeValue.Builder` class instead to create an `ActivityCodeValue` instance and add it directly to the list held by the parent `ActivityCode`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the `ActivityCode.Builder` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `ActivityCode` constructor as deprecated, use the `ActivityCode.Builder` class instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Only predecessor `Relation` instances are now stored in `RelationContainer`, successors are generated dynamically. You will only notice a difference if you are iterating over the `RelationContainer` collection directly, in which case you will only see predecessors.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.3.0">Release 12.3.0 &#x2013; 2023-11-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Retrieve role availability data when reading a schedule from a P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate the project's Name and Title attributes when exporting an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Project ID attribute is populated when writing an XER file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't include null tasks (blank tasks) when writing an XER file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Strip control characters from entity names written to MSPDI files and XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure resource material labels written to MSPDI files meet Microsoft Project's naming requirements.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the activity code value Name attribute is populated when read from an Asta PP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't allow multiple values for an activity code when writing XER and PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The MSPDI and MPX writers now dynamically renumber Unique ID values which are too large for Microsoft Project. The original schedule is no longer modified to achieve this.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.2.0">Release 12.2.0 &#x2013; 2023-10-12</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add the `UnitOfMeasure` class to represent the unit of measure for a material resource. The unit of measure corresponds to the current &quot;material label&quot; attribute of a resource. The `Resource.getMaterialLabel()` method will now retrieve the label from the `UnitOfMeasure` instance associated with the resource. The `Resource.setMaterialLabel()` method is now deprecated, the `Resource.setUnitOfMeasure()` or `Resource.setUnitOfMeasureUniqueID()` methods should be used instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Unit of measure for material resources are now read from and written to Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve task duration and percent completion calculation for Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve date parsing when reading XER files written by older versions of P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `setIgnoreErrors` method to the Primavera database reader class, and MSPDI, Schedule Grid, and SDEF file reader classes. The current default behavior of ignoring data type parse errors is unchanged. Calling `setIgnoreErrors(false)` on one of these reader classes will ensure that an exception is raised when a data type parse error is encountered.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ProjectFile.getIgnoredErrors()` method. The default behavior for MPXJ reader classes is to ignore data type parse errors. If any errors have been ignored when reading a schedule, details of these errors can be retrieved by calling the `ProjectFile.getIgnoredErrors()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle duplicate relation unique IDs when reading schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Include resource availability table in JSON output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add the Resource field Default Units, and ensure this field is read and written for P6 Schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the Resource attribute Max Units to ensure that this is calculated from the resource's availability table. Note that the `Resource.getMaxUnits()` method will return the resource's Max Units attribute for the current date. To retrieve the Max Units for a different date, use the `AvailabilityTable.getEntryByDate()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Resource.setMaxUnits()` method as deprecated. The Max Units attribute is derived from the resource's availability table. Changes to Max Units should now be made by modifying the availability table.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the Resource attribute Available From to ensure that this is calculated from the resource's availability table. Note that the `Resource.getAvailableFrom()` method will return the resource's Available From attribute for the current date. To retrieve the Available From attribute for a different date, use the `AvailabilityTable.availableFrom()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Resource.setAvailableFrom()` method as deprecated. The Available From attribute is derived from the resource's availability table. Changes to the Available From attribute  should now be made by modifying the availability table.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the Resource attribute Available To to ensure that this is calculated from the resource's availability table. Note that the `Resource.getAvailableTo()` method will return the resource's Available To attribute for the current date. To retrieve the Available To attribute for a different date, use the `AvailabilityTable.availableTo()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the `Resource.setAvailableTo()` method as deprecated. The Available To attribute is derived from the resource's availability table. Changes to the Available To attribute  should now be made by modifying the availability table.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.1.3">Release 12.1.3 &#x2013; 2023-09-25</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Project Properties attribute Relationship Lag Calendar and implemented read and write support for this for P6 schedules. (Contributed by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve compatibility of PMXML files with P6 EPPM by moving the Schedule Options tag.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Baseline Projects in PMXML files include Schedule Options and Location Object ID.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.1.2">Release 12.1.2 &#x2013; 2023-09-21</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updates to improve compatibility with versions of Java after Java 8.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure timestamps with fractional sections are read correctly from Phoenix PPX files (Based on a contribution by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of double quotes when reading and writing XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>To allow XER files written by MPXJ to be imported correctly by P6, ensure that they have a single top level WBS entry (Based on a contribution by Alex Matatov)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that `ProjectProperties.getCustomProperties()` returns an empty Map rather than returning `null` if no custom properties have been configured.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure project calendars and project activity codes are nested within the project tag of PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.1.1">Release 12.1.1 &#x2013; 2023-08-23</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue preventing native SQLite library from loading when using the .Net version of MPXJ on macOS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.1.0">Release 12.1.0 &#x2013; 2023-08-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Write schedule options to PMXML and XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an arithmetic error in RateHelper when converting a rate from minutes to hours.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced new methods to RateHelper accepting a `TimeUnitDefaultsContainer` argument rather than a `ProjectFile` for greater flexibility. Marked methods taking a `ProjectFile` argument as deprecated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Early Finish and Late Finish are populated for Asta milestones and tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't attempt to calculate total slack if start slack or finish slack are missing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure completed tasks are not marked as critical.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of non-standard Boolean values in MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve Total Slack calculation for P6 projects.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle finish milestones with `null` actual start date for actual duration calculation when reading PMXML files (Contributed by Andrew Marks).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.0.2">Release 12.0.2 &#x2013; 2023-07-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the Fixed Cost attribute is rolled up from activities to WBS entries when reading P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.0.1">Release 12.0.1 &#x2013; 2023-07-21</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve resource hierarchy handling.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of external tasks read from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of resource assignments read from Asta PP files containing multiple baselines.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve filtering to ignore hammock tasks in Asta PP files and ensure that non-hammock items are not incorrectly ignored.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of bars without additional linked data read from Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that invalid duplicate Unique ID values encountered when reading schedule data are renumbered to maintain uniqueness.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading certain FastTrack FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Roll up the expense item at completion values read from P6 schedules to the task Fixed Cost attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a12.0.0">Release 12.0.0 &#x2013; 2023-06-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>NOTE: this is a major version release, breaking changes have been made to the MPXJ API as documented below.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Timestamps, dates, and times are now represented by `java.time.LocalDateTime`, `java.time.LocalDate` and `java.time.LocalTime` respectively, rather than `java.util.Date` as they were originally.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For .Net users, new `ToDateTime` and `ToNullableDateTime` extension methods have been provided to convert `java.time.LocalDateTime`, `java.time.LocalDate`, `java.time.LocalTime` to `DateTime` instances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For .Net users, new `ToJavaLocalDateTime`, `ToJavaLocalDate` and `ToJavaLocalTime` extension methods have been provided to convert `DateTime` instances to `java.time.LocalDateTime`, `java.time.LocalDate`, and `java.time.LocalTime`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The class `net.sf.mpxj.Day` has been replaced by `java.time.DayOfWeek`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All code previously marked as deprecated has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing the Activity attribute &quot;Expected Finish&quot; for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.5.4">Release 11.5.4 &#x2013; 2023-06-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of dates read from Synchro, Suretrak and Turboproject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>By default ignore errors in individual records read from XER files. This matches P6's behavior when importing XER files. Use the `PrimaveraXERFileReader.setIgnoreErrors` method to change the behavior.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.5.3">Release 11.5.3 &#x2013; 2023-06-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing an XER file, provide the necessary default values to allow non-P6 schedules to be successfully imported into P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure multi-day exceptions are written to XER files correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure GanttProject exception dates are read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>More closely match the Planner predecessor lag calculation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that `java.sql.Date` values are correctly formatted when writing XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading from a P6 database, check to ensure the location table is present before attemting to read locations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.5.2">Release 11.5.2 &#x2013; 2023-06-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of calendar data read from certain Powerproject schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of unusual XER files with calendar time ranges expressed in 12-hour format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly parse midnight represented as 24:00:00 from MSPDI files written by certain non-Microsoft Project applications.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For MSPDI files produced by applications other than Microsoft Project which have an incorrectly nested calendar hierarchy, avoid pruning derived calendars which are referenced elsewhere in the hierarchy.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.5.1">Release 11.5.1 &#x2013; 2023-05-24</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve read performance when working with large schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve read and write performance of code handling resource calendars.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use sqlite-jdbc ********</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.5.0">Release 11.5.0 &#x2013; 2023-05-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the ability to read Subproject data embedded in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the ability to read timephased baseline work and cost from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the ability to write timephased baseline work and cost to MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of timephased baseline work read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that non-recurring calendar exceptions take precedence over recurring calendar exceptions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid creating duplicate calendar exceptions when reading Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Bar Name attribute to Task, which is accessed using the `getBarName` and `setBarName` methods. This is populated with the name of the bar to which a task belongs when reading an Asta Powerproject schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading schedules from XER files and P6 databases, ensure durations without a value are returned as `null` rather than as a zero duration.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.4.0">Release 11.4.0 &#x2013; 2023-05-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the &quot;Resource Pool File&quot; attribute to ProjectProperties, which represents the full path of the resource pool used by an MPP file. This attribute is accessible via the `getResourcePoolFile` and `setResourcePoolFile` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `getResourcePoolObject` method to allow the resource pool file to be located and read</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading the task attribute Subproject GUID from MPP files. This attribute can be accessed via the `getSubprojectGUID` and `setSubprojectGUID` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for the task attribute &quot;External Project&quot;. When this attribute is true it indicates that the task represents a subproject. The attribute is accessed via the `getExternalProject` and `setExternalProject` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading an MSPDI file with external task predecessors, MPXJ now attempts to recreate the placeholder external tasks which would be present if the equivalent MPP file was read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>External task predecessors are now represented when writing an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Task method `getSubprojectObject` which allows the caller to retrieve a ProjectFile instance representing the external project linked to a task.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Task method `expandSubproject`. For task which represent an external project, this method automatically loads the external project and attaches the tasks it contains as children of the current task. This is analogous to the behavior in Microsoft Project where a subproject is expanded to reveal the tasks it contains.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the ProjectFile method `expandSubprojects` which identifies any tasks in the project which represent an external project and expands them, linking the tasks from the external project as children of the task in the parent project. Note that the method works recursively so multiple levels of external tasks will be expanded.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to ensure that the `internal_name` attribute of a `UserdefinedField` is generated if not present.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to avoid an exception when reading notebook topics from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the Task method `setSubprojectName` as deprecated. Use the `setSubProjectFile` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the Task method `getSubprojectName` as deprecated. Use `getSubprojectFile` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the Task method `setExternalTaskProject` as deprecated. Use the `setSubprojectFile` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the Task method `getExternalTaskProject` as deprecated. Use the `getSubprojectFile` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the ProjectFile method `getSubProjects` as deprecated. Use the subproject attributes on individual tasks instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Marked the Task methods `getSubProject` and `setSubProject` as deprecated. Use the subproject attributes instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.3.2">Release 11.3.2 &#x2013; 2023-04-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve default values provided for P6 calendars with missing data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implement both &quot;planned dates&quot; and &quot;current dates&quot; strategies for populating P6 baselines.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Project GUID is read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.3.1">Release 11.3.1 &#x2013; 2023-04-21</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of resource assignment Actual Start and Actual Finish dates when reading MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid generating timephased data for zero duration tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve preservation of custom timephased data start and end times.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.3.0">Release 11.3.0 &#x2013; 2023-04-12</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented `PrimaveraXERFileWriter` to allow MPXJ to write XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the `ActivityCode` class to ensure that both the scope Project ID and EPS ID can be represented when reading a P6 schedule. (Potentially breaking change if you were using this class).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure secondary constraint date and type are written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure leveling priority is written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure WBS UDF values are written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure integer UDF values are read correctly from XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add methods to allow the project's default calendar unique ID to be set and retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add method to allow a calendar's parent calendar unique ID to be retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add method to allow a task's parent task unique ID to be retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add methods to allow a resource assignment's role unique ID to be set and retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add methods to allow a resource assignment's cost account unique ID to be set and retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add method to allow a cost account's parent unique ID to be retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add method to allow an expense item's cost account unique ID to be retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add method to allow an expense item's category unique ID to be retrieved.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `WorkContour.isDefault()` method to allow &quot;built in&quot; resource curves/work contours to be distinguished from user defined curves.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to retrieve the project's start date from Phoenix PPX files (Contributed by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provide access to notebook topics from P6 schedules via the `ProjectFile.getNotesTopics()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Capture unique ID of Activity and WBS notes from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve the calculation used to determine At Completion Duration of activities when reading XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve representation of certain duration values written to MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of certain work calculations where the specified time period does not start with a working day.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue which caused negative timephased work values to be generated when reading certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading XER files where the `critical_drtn_hr_cnt` field is expressed a decimal rather than an integer.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue populating the WBS attribute for activities read from certain XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.2.0">Release 11.2.0 &#x2013; 2023-03-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The project property Critical Slack Limit is now represented as a `Duration` rather than as an `Integer`. (Potentially breaking change if you were using this property directly).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>`TaskType` is now a simple enum with all Microsoft Project specific functionality moved into `TaskTypeHelper`. (Potentially breaking change if you were using the `TaskType` methods `getInstance` or `getValue` in your code)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading the task type from P6 schedule the mapping to the MPXJ `TaskType` enum has been updated to more closely match P6. The main changes are that the P6 type &quot;Fixed Units&quot; now maps to `TaskType.FIXED_WORK` and the &quot;Fixed Duration &amp; Units&quot; type now maps to a new enumeration value `TaskType.FIXED_DURATION_AND_UNITS`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading project calendar exceptions from Phoenix schedules (based on a contribution by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The Resource attribute Active now defaults to true if the schedule being read doesn't support or contain a value for this attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading and writing the Resource's Active flag for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading and writing the Resource's Default Units/Time value for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading and writing the Project's Critical Slack Limit value for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fixed an issue reading certain types of Enterprise Custom Fields containing date values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure activity code value parent can be set to null.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved existing .Net extension methods and added support for more types.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added NuGet package icon</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Simplified  NuGet packaging</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.1.0">Release 11.1.0 &#x2013; 2023-02-15</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Write activity code definitions and activity code assignments to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for &quot;secure&quot; and &quot;max length&quot; attributes to the `ActivityCode` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `getChildCodes` method to `ActivityCode` and `ActivityCodeValue` to make it easier to traverse activity code values hierarchically.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `setDescription` method to `Step` class to make it simpler to add a plan text description.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a11.0.0">Release 11.0.0 &#x2013; 2023-02-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>User defined fields read from P6, Asta and GanttProject schedules are now represented by instances of `UserDefinedField`. They will no longer be mapped to custom field instances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Enterprise Custom Fields read from MPP and MSPDI files are now represented by instances of `UserDefinedField`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MSPDI files, UserDefinedField instances which were originally read from enterprise custom fields will be written to the MSPDI file as enterprise custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MSPDI files, UserDefinedField instances which were from applications other than Microsoft Project will automatically be mapped to available custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MPX files, UserDefinedField instances will automatically be mapped to available custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `UserDefinedField` type implements the `FieldType` interface and so can be used with the `FieldContainer` `get` and `set` methods to work with the contents of the user defined fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectFile.getUserDefinedFields()` method has been added to provide access to all user defined fields defined in the project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `CustomFieldContainer` returned by `ProjectFile.getCustomFields()` will contain entries for all `UserDefinedField` instances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The various `getFieldTypeByAlias` and `getFieldByAlias` methods will retrieve user defined fields by name.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the convenience method `ProjectFile.getPopulatedFields()` to retrieve details of all populated fields across the project. This avoids the caller having to individually retrieve the populated fields from the tasks container, resource container and so on.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the `getPopulatedFields` methods to return a `Set` of `FieldType` rather than a `Set` of `TaskField`, `ResourceField` etc.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The various `getPopulatedFields` methods will include instances of `UserDefinedField` in the returned collection if relevant.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All `ENTERPRISE_CUSTOM_FIELDn` values have been removed from the `TaskField`, `ResourceField`, `AssignmentField` and `ProjectField` enumerations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `getEnterpriseCustomField` and `setEnterpriseCustomField` methods have been removed from `ProjectProperties`, Task`, `Resource` and `ResourceAssignment`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Project UDFs are now read from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Project UDFs are now written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All code previously marked as deprecated has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.16.2">Release 10.16.2 &#x2013; 2023-01-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to improve reading resource attributes from certain MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.16.1">Release 10.16.1 &#x2013; 2023-01-26</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make resource curve definitions (work contours) available in the `WorkContourContainer`. This container is accessed using the `ProjectFile.getWorkContours()` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.16.0">Release 10.16.0 &#x2013; 2023-01-24</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy when normalising timephased data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading activity steps from XER files, PMXML files and Primavera databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for writing activity steps to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML schema to version 22.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated methods in the `GanttBarCommonStyle` and `GanttBarStyle` classes to use a `FieldType` instance rather than a `TaskField` instance to allow more flexibility. (Note: this may be a breaking change if you are currently using these classes.)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Optionally include some Microsoft Project layout data in JSON output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.15.0">Release 10.15.0 &#x2013; 2023-01-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid writing invalid characters to PMXML, MSPDI and Planner XML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of slack values for schedules which only contain a value for total slack.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading constraint type and constraint date from Phoenix schedules (based on a contribution by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve timephased data calculation when assignment has zero units.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of very large duration values when reading and writing MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Task attributes Active, Constraint Type, Task Mode, and Type always have a value.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Resource attributes Type, Calculate Costs from Units, and Role always have a value.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the Resource Assignment attributes Calculate Costs from Units, Rate Index, and Rate Source always have a value.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add version number constant to the Java source, accessible as `MPXJ.VERSION`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that UDF values are read for WBS entries in PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid writing duplicate resource assignments to MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.14.1">Release 10.14.1 &#x2013; 2022-11-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix CVE-2022-41954: Temporary File Information Disclosure Vulnerability (Contributed by Jonathan Leitschuh)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.14.0">Release 10.14.0 &#x2013; 2022-11-21</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle missing default calendar when reading a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading an MPP file using a file name or `File` instance, ensure a more memory-efficient approach is used.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading certain FastTrack FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve generation of timephased data where working time ends at midnight.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve generation of timephased data for tasks with a calendar assigned.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.13.0">Release 10.13.0 &#x2013; 2022-11-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading a resource assignment's cost account from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing a resource assignment's cost account to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read resource assignment custom field definitions present in MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve identification of deleted resources when reading MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure tasks with task calendars in MPP files are handled correctly when generating timephased data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve generation of timephased data for material resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of timephased data when reading certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.12.0">Release 10.12.0 &#x2013; 2022-11-01</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Resource Assignment attribute Calculate Costs From Units, and added read and write support for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Resource attribute Calculate Costs From Units, and added read and write support for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the Resource and Role attribute Sequence Number, and added read and write support for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the WBS attribute Sequence Number, and added read and write support for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure activity type is read from Phoenix schedules. (Contributed by Christopher John)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecate the `CostAccount` method `getSequence` and replace with `getSequenceNumber` to improve naming consistency.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecate the `ExpenseCategory` method `getSequence` and replace with `getSequenceNumber` to improve naming consistency.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid possible ArrayIndexOutOfBoundsException when reading GUID values from MPP files (Contributed by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.11.0">Release 10.11.0 &#x2013; 2022-09-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `Resource` methods `getParentID` and `setParentID`. Replaced with `getParentResourceUniqueID` and `setParentResourceUniqueID` for clarity and consistency.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `Resource` methods `setParent` and `getParent`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ChildResourceContainer` interface and `ResourceContainer.updateStructure` method to ensure that resources can be accessed hierarchically when reading a schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ResourceAssignment` methods `getFieldByAlias` and `setFieldByAlias` to simplify working with custom fields, and mkae the API consistent with existing methods on `Task` and `Resource`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `TaskContainer` methods `getCustomFields` and `getFieldTypeByAlias` to simplify access to task custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ResourceContainer` methods `getCustomFields` and `getFieldTypeByAlias` to simplify access to resource  custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ResourceAssignmentContainer` methods `getCustomFields` and `getFieldTypeByAlias` to simplify access to resource assignment custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `getCustomFieldsByFieldTypeClass` method to `CustomFieldContainer` to allow retrieval of custom field details by parent class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `CustomFieldContainer` method `getFieldByAlias` to be replaced by `getFieldTypeByAlias` to provide a more consistent method name.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't attempt to write unknown extended attributes to MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't populate graphical indicator data if the graphical indicator is not enabled.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't set custom field aliases to empty strings.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `CustomFieldContainer` method `add`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `CustomFieldContainer` method `getCustomField`, which is replaced by the `get` method (which returns `null` if the field type is not configured) and the `getOrCreate` method (which will return an existing configuration or create a new one if the requested field does not yet have a configuration).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.10.0">Release 10.10.0 &#x2013; 2022-09-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add an option to import Phoenix schedules as a flat set of tasks with separate activity codes, rather than creating a hierarchy of tasks from the activity codes. Note the default is to disable this behavior so existing functionality is unchanged. (Contributed by Christopher John)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add a `setProperties` method to reader classes to allow configuration to be supplied via a `Properties` instance rather than having to call setter methods. Properties passed to the `UniversalProjectReader` version of this method will be forwarded to the reader class `UniversalProjectReader` chooses to reader the supplied file. Properties for multiple reader classes can be included in the `Properties` instance, each reader class will ignore irrelevant properties.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `get` method to `Task`, `Resource`, `ResourceAssignment` and `ProjectProperties` as a replacement for the `getCurrentValue` method. The new `get` method is paired with the existing `set` method to provide read and write access to attributes of these classes. This change is intended to improve the interfaces to these classes by making them more consistent, and thus easier to understand.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecated the `getCurrentValue` method on the `Task`, `Resource`, `ResourceAssignment` and `ProjectProperties` classes. Use the new `get` method instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add getter and setter methods for the Resource attributes Cost Center, Budget Cost, Budget Work, Baseline Budget Cost, Baseline Budget Work, Baseline Budget Cost 1-10, and Baseline Budget Work 1-10.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add getter and setter methods for the Task attributes Response Pending, Scheduled Start,  Scheduled Finish, Scheduled Duration, Budget Cost, Budget Work, Baseline Budget Cost, Baseline Budget Work, Baseline Budget Cost 1-10, and Baseline Budget Work 1-10.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for the Resource Cost Centre attribute for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Move MPP file-specific functionality for determining baseline values from the Task class into the MPP reader class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of the TaskMode attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't set a Task's Critical attribute unless we have valid slack values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure `ResourceAssignment` calculated fields are returned correctly when using the `getCurrentValue` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure `ProjectProperties` calculated fields are returned correctly when using the `getCurrentValue` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use jsoup 1.15.3</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.9.1">Release 10.9.1 &#x2013; 2022-08-31</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure monthly and yearly recurrences are calculated correctly when the supplied start date is the same as the first recurrence date (Contributed by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading task calendars from Phoenix files (Contributed by Rohit Sinha).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reliability of ProjectCleanUtility when using the replacement strategy.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.9.0">Release 10.9.0 &#x2013; 2022-08-23</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added the `ResourceAssignment.getEffectiveRate` method to allow the cost rate effective on a given date for a resource assignment to be calculated. For P6 schedules this will take account of the cost rate configuration included as part of the resource assignment.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For P6 schedules, the `ResourceAssignment.getCostRateTable` method now takes in account any cost rate configuration details from the resource assignment when determining which table to return.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>A resource's Standard Rate, Overtime Rate and Cost per Use are now all derived from the resource's cost rate table, and not stored as attributes of the resource itself.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The resource methods `setStandardRate`, `setOvertimeRate`, and `setCostPerUse` have been deprecated. These attributes can now only be set or updated by modifying the resource's cost rate table.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MPX files, only include attributes which have a non-empty, non-default value in at least one task or resource.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing MPX files, ensure attributes which have calculated values are used.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading a resource assignment's rate type from P6 schedules. The rate type is accessed via the `ResourceAssignment.getRateIndex` method. The value returned by this method can be used to select the required rate using the `CostRateTableEntry,getRate` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing a resource assignment's rate type to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading a resource assignment's role from P6 schedules. The role is accessed via the `ResourceAssignment.getRole` and `ResourceAssignment.setRole` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing a resource assignment's role to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading a resource assignment's override rate (Price / Unit) from P6 schedules. The rate is accessed via the `ResourceAssignment.getOverrideRate` and `ResourceAssignment.setOverrideRate` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing a resource assignment's override rate (Price / Unit) to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading a resource assignment's rate source from P6 schedules. The rate source is accessed via the `ResourceAssignment.getRateSource` and `ResourceAssignment.setRateSource` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing a resource assignment's rate source to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.8.0">Release 10.8.0 &#x2013; 2022-08-17</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading P6 schedules, all five cost rates for a resource are now available via the `CostRateTableEntry.getRate` method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All five rates from each cost rate table entry can now be written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading files written by Microsoft Project, resource rate values now use the same units as seen by the end user rather than defaulting to hours as was the case previously. (For example, if the user sees $8/day in the source application, you will receive a Rate instance of $8/day rather than $1/hr).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The values for a resource's standard rate, overtime rate, and cost per use attributes are now derived from the cost rate table. The values stored on the resource itself are only used if a cost rate table for the resource is not present.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The Resource methods `getStandardRateUnits` and `getOvertimeRateUnits` are deprecated. Use the `getStandardRate` and `getOvertimeRate` methods to retrieve a `Rate` instance which will include the units for these rates.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The Resource methods `setStandardRateUnits` and `setOvertimeRateUnits` are deprecated. Supply `Rate` instances to the `setStandardRate` and `setOvertimeRate` methods with the required units instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The CostRateTableEntry methods `getStandardRateUnits` and `getOvertimeRateUnits` are deprecated. Use the `getStandardRate` and `getOvertimeRate` methods to retrieve a `Rate` instance which will include the units for these rates.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure rates are formatted &quot;per hour&quot; when writing MSPDI and PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Include cost rate tables in JSON output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.7.0">Release 10.7.0 &#x2013; 2022-08-09</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use Jackcess to read Asta MDB and Microsoft Project MPD files. This allows these file to be read on platforms other than Windows.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading correctly typed values for enterprise custom fields from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve array index validation when reading GUID values from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.6.2">Release 10.6.2 &#x2013; 2022-06-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure `ProjectCleanUtility` can load dictionary words from distribution jar.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of calendars without days read from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.6.1">Release 10.6.1 &#x2013; 2022-06-14</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 5.2.2</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use sqlite-jdbc ********</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use jsoup 1.15.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.6.0">Release 10.6.0 &#x2013; 2022-06-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing the unique ID of P6 user defined fields via new `getUniqueID` and `setUniqueID` methods on `CustomField (based on a suggestion by Wes Lund).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing scope, scope ID, and sequence number attributes for activity codes (based on a suggestion by Wes Lund).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading and writing sequence number and color attributes for activity code values (based on a suggestion by Wes Lund).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `isWorking` method to `ProjectCalendarException` to make it clearer how to determine if the exception changes the dates it is applied to into working or non-working days.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading task start from certain Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading predecessor lag values from Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure calendar hierarchy is written correctly to Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write null tasks to Planner files as Planner will not read files which contain them.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing Planner file, ignore constraint types which Planner can't represent.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write emply predecessor lists to Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of lag duration when writing Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve ProjectCalendar start date calculation when we have long runs of non-working days.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Performance enhancement for timephased data normalisation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.5.0">Release 10.5.0 &#x2013; 2022-05-24</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendarWeek` methods `addCalendarHours()`, `attachHoursToDay`, `removeHoursFromDay` have been removed. Use `addCalendarHours(day)`, `removeCalendarHours(day)` instead. (Note: this will be a breaking change if you were using the original methods to create or modify a schedule)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendar` methods `attachHoursToDay` and `removeHoursFromDay` have been removed. Use the `addCalendarHours` and `removeCalendarHours` methods instead. (Note: this will be a breaking change if you were using the original methods to create or modify a schedule)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The class hierarchy for `ProjectCalendarHours` and `ProjectCalendarException` has been simplified, but there should be no impact for uses of these classes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendarHours` class now implements the `List` interface. Methods in this class not part ofthe `List` interface have been deprecated in favour of the equivalent `List` methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `MPXWriter` to ensure: calendar names are quoted if necessary, all calendars have names, all calendar names are unique.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `MPXReader` to recognise `wk` as a valid time unit.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `MPXWriter`, `PrimaveraPMFileWriter`, `SDEFWriter` and `PlannerWriter` to ensure any working weeks defined by a calendar are represented by exceptons.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `MSPDIWriter` to ensure any working weeks defined by a calendar are represented in the &quot;legacy&quot; exception definition used by Microsoft Project prior to 2007.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `SDEFWriter` to ensure: only relevant calendars are written, and derived calendars are flattened.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading Planner schedules MPXJ will no longer create an &quot;artificial&quot; resource calendar for each resource. Resources will be linked directly to the calendar used in the original schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for reading the P6 calendar type and personal calendar flag from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add support for writing the calendar type and personal calendar flag to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the calendar class hierarchy: `ProjectCalendar` and `ProjectCalendarWeek` both now inherit from a new class `ProjectCalendarDays`. Note that `ProjectCalendar` is no longer a subclass of `ProjectCalendarWeek`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `getHours` and `isWorkingDay` methods have been moved up to `ProjectCalendar` from the `ProjectCalendarWeek` class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendar` method `copy` has been deprecated, without replacement.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added a `getWork` method to `ProjectCalendar` which calculates the amount of work given a `Day` instance.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `removeWorkWeek` and `removeCalendarException` methods to `ProjectCalendar`.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Recurring exceptions are now added to a `ProjectCalendar` using the `addCalendarException` method which takes a `recurringData` instance its argument.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendarException` method `setRecurringData` has been removed, recurring exceptions should be added using the `addCalendarExcepton` method described above. (Note: this will be a breaking change if you were creating recurring exceptions)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.4.0">Release 10.4.0 &#x2013; 2022-05-05</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Remove `getParent`, `setParent`, and `isDerived` from `ProjectCalendarWeek`. (Note: this will be a breaking change if you were working with `ProjectCalendarWeek` directly).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectProperties` methods `getDefaultCalendarName()` and `setDefaultCalendarName()` have been deprecated. Use `getDefaultCalendar()` and `setDefaultCalendar()` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that percent complete values can be read from MSPDI files even if the values are decimals.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of the default calendar when reading certain MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading certain Phoenix PPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading certain FastTrack FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve formatting of time project properties when written to JSON.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading MPP files generated by Microsoft Project 16.0.15128.20158 and later versions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.3.0">Release 10.3.0 &#x2013; 2022-04-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>General improvements to make calendar data read from different file formats more consistent.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading P6 and Powerproject schedules MPXJ will no longer create an &quot;artificial&quot; resource calendar for each resource. Resources will be linked directly to the calendars they use in the original schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update `MPXWriter` and `MSPDIWriter` to ensure that, when written, calendars are correctly structured in the form required by Microsoft Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>`JsonWriter` now includes calendar data as part of its output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `ProjectCalendar` methods `setMinutesPerDay`, `setMinutesPerWeek`, `setMinutesPerMonth` and `setMinutesPerYear` have been deprecated, use `setCalendarMinutesPerDay`, `setCalendarMinutesPerWeek`, `setCalendarMinutesPerMonth` and `setCalendarMinutesPerYear` instead.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The ProjectCalendar method `setResource` has been deprecated and will not be replaced. Use the Resource method `setCalendar` or `setCalendarUniqueID` to link a calendar with a resource.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The ProjectCalendar method `getResource` has been deprecated. Use the `getResources` method instead to retrieve all resources linked with a calendar.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The `Resource` methods `addResourceCalendar`, `setResourceCalendar`, `getResourceCalendar`, `setResourceCalendarUniqueID` and `getResourceCalendarUniqueID` have been deprecated and replaced by `addCalendar`, `setCalendar`, `getCalendar`, `setCalendarUniqueID` and `getCalendarUniqueID` respectively.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.2.0">Release 10.2.0 &#x2013; 2022-03-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improvements to writing currency, rate and units amounts to MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading MPP and MSPDI files, calendar exceptions representing a single range of days, but defined as a recurring exception are converted to a range of days, removing the unnecessary recurring definition.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added `StructuredTextParser` to replace original code handling calendar data, project properties and curve data read from XER files and Primavera databases. Can also be used to extract data from Primavera Layout Files (PLF).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve recognition of contoured resource assignments read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve retrieval of resource assignment confirmed, response pending, linked fields, and team status pending flags from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.1.0">Release 10.1.0 &#x2013; 2022-01-29</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve PMXML file compatibility with P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Strip any trailing invalid characters from text read from FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure all tasks read from Powerproject and Project Commander have unique IDs.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correct expansion of exceptions from a weekly recurring calendar exception.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that expanded calendar exceptions are written to file formats which do not support recurring exceptions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that start and finish dates are set when reading milestones from GanttProject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.5">Release 10.0.5 &#x2013; 2022-01-11</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure `Task.getActivityCodes()` returns an empty list rather than `null` when no activity code values have been assigned.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Default to using ASCII when reading and writing SDEF files, as per the SDEF specification.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provide methods to set and get the charset used when reading and writing SDEF files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.4">Release 10.0.4 &#x2013; 2022-01-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Code Library values (as Activity Codes) from Powerproject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated `ProjectCleanUtility` to provide a &quot;replace&quot; strategy alongside the original &quot;redact&quot; strategy.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.3">Release 10.0.3 &#x2013; 2021-12-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix issue with null tasks from certain MPP files introduced in 10.0.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.2">Release 10.0.2 &#x2013; 2021-12-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve identification of null tasks for certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.1">Release 10.0.1 &#x2013; 2021-12-10</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid false positives when detecting password protected MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a10.0.0">Release 10.0.0 &#x2013; 2021-12-01</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for .NET Core 3.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Nuget packages now explicitly target .NET Framework 4.5 (`net45`) and .NET Core 3.1 (`netcoreapp3.1`)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.8.3">Release 9.8.3 &#x2013; 2021-11-30</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reliability when reading certain Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure multiple trailing nul characters are stripped from text when reading schedules from a Primavera database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.8.2">Release 9.8.2 &#x2013; 2021-11-01</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of identifying null tasks in certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of identifying valid tasks in certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure hierarchical outline code values are read correctly from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for files produced by recent versions of FastTrack.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.8.1">Release 9.8.1 &#x2013; 2021-10-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Phoenix 5 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of null tasks read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.8.0">Release 9.8.0 &#x2013; 2021-09-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Introduced the BaselineStrategy interface and implementing classes. (Note: this includes a breaking change if you were using the ProjectFile.setBaseline method and supplying a lambda. You will now need to implement a BaselineStrategy and set this in ProjectConfig before setting a baseline).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved accuracy of baseline attributes for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.7.0">Release 9.7.0 &#x2013; 2021-09-28</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add Sprint ID and Board Status ID attributes to task.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Introduce the TimeUnitDefaultsContainer to allow constants for time unit conversions to be obtained from both project properties and calendars.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Duration attributes are no longer returned as Duration instances by the ruby gem, they are now returned as floating point numbers. By default, durations are expressed in seconds. A new optional argument to MPXJ::Reader.read allows you to change the units used to express durations. (Note: this is a breaking change for users of the ruby gem)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update JsonWriter to use a relevant calendar when converting durations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure default calendar is set correctly when reading XER and PMXML files, and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use default hours per day/week/month/year when reading P6 XER files or databases if these values are not present.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the minutes per day/week/month/year attributes are copied when a calendar is copied.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading P6 schedules, roll up calendar for WBS entries when child activities all share the same calendar.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Generate missing minutes per day/week/month/year for calendars read from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Inherit minutes per day/week/month/year from base calendars (Note: minor method signature changes on ProjectProperties and ProjectCalendar).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Allow explicit values to be set for project minutes per week and minutes per year.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fall back on defaults for project minutes per day/week/month/year attributes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.6.0">Release 9.6.0 &#x2013; 2021-09-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add Planned Start and Scheduled Finish to project properties.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add attribute_types method to Ruby classes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 5.0.0.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected source of Must Finish By project property when reading XER files or P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading PMXML files, ensure that the activity calendar is set before calculating slack.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Remove unused field TaskField.PARENT_TASK.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure task Unique ID and task Parent Unique ID attributes are treated as mandatory when written to JSON.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue with Ruby gem where a task's parent was not being retrieved correctly in some circumstances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.5.2">Release 9.5.2 &#x2013; 2021-08-22</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add Must Finish By date to project properties.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for the task attributes Longest Path, External Early Start and External Early Finish, and ensure they can be read from P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rename ProjectFile.getStartDate() and ProjectFile.getFinishDate() methods for clarity. Original method names are marked as deprecated</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that all activities in a PMXML file have a CalendarID attribute to ensure compatibility with older versions of P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the user's selected progress period is used to set the project's status date attribute when reading Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that a task's Complete Through attribute is not advanced to the start of the next working day (the behaviour of Microsoft Project prior to 2007 was to report Complete Through as the start of the next working day. This change ensures MPXJ matches versions of Microsoft Project from 2007 onwards. Previous behaviour can be restored using the ProjectConfig.setCompleteThroughIsNextWorkStart() method).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecate task getSplitCompleteDuration() and setSplitCompleteDuration() in favour of getCompleteThrough() and setCompleteThrough().</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved the implementation of the TaskContainer.synchronizeTaskIDToHierarchy method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update jsoup to 1.14.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.5.1">Release 9.5.1 &#x2013; 2021-07-01</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When applying a baseline using ProjectFile.setBaseline, gracefully handle duplicate task key values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle missing values populating cost rate table from an MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.5.0">Release 9.5.0 &#x2013; 2021-06-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading baseline data from embedded baselines in PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correct resource assignment percent complete values read from PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>JsonWriter no longer writes attribute type information by default. (The original behaviour can be restored by calling setWriteAttributeTypes(true) on your JsonWriter instance).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The MPXJ Ruby Gem now generates explicit methods to access attributes rather than relying on &quot;method_missing&quot; to intercept and act on attribute access.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write Assignment Task GUID, Assignment Resource GUID or Resource Calendar GUID to JSON.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write a value for Assignment Work Contour to JSON if the contour is the default value (i.e. flat).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write a value for Assignment Resource Request Type to JSON if the type is the default value (i.e. none).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write a value for Task Earned Value Method to JSON if the method matches the project default.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write a value for Task Type to JSON if the type matches the project default.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Stop writing a default value (-1) for Parent Task ID to JSON if the task does not have a parent.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Stop writing a default value (-1) for Task Calendar ID to JSON if the task does not have a calendar.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading resource assignments from an MPP file, don't record Project's internal representation of a null resource ID (-65535), record the resource ID explicitly as null.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For MPX and Planner files, don't write resource assignments for the &quot;null&quot; resource.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle missing status date when reading P6 schedules from XER files or database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading MPP files, treat UUIDs which are all zeros as null.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Deprecate the 10 Resource Outline Code get and set methods and replace with get and set methods which take an index argument.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provide a helper method (PrimaveraHelper.baselineKey) to encapsulate key generation for setting Primavera baselines.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.4.0">Release 9.4.0 &#x2013; 2021-06-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read custom value lists for resource custom fields from MPP files (based on a suggestion by Markus H&#xf6;ger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added support for reading custom fields from Asta Powerproject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure short data type values are written to JSON files as numeric values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure delay data type values are written to JSON files as duration values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write zero rates to JSON files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced a separator into rate values when written to a JSON file to make it clear that the value is a rate not a duration (for example: 5.00h is now 5.00/h).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When writing an enum value of a JSON file, ensure we write the original enum name rather than the value return by toString. This provides more meaningful output (Potentially breaking change if you use the Ruby gem or consume the JSON output directly. Affected attributes are project properties: currency symbol position, time format, date format, bar text date format, schedule from, mpx file version; resource attribute: type).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure invalid cost rate table data is handled gracefully when reading from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle missing data when reading MSPDI files (based on a contribution by Lord Helmchen).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve population of summary task names when reading from Powerproject PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly read hierarchical resource outline codes from MPP files (based on a suggestion by Markus H&#xf6;ger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.3.1">Release 9.3.1 &#x2013; 2021-05-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Preserve multiple assignments between an activity and a resource when reading P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed WorkContour.isFlat to isContourFlat and WorkContour.isContoured to isContourManual.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Include an entry for 0% in the WorkContour curve definition.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue where non-working days were not being treated correctly in date calculations if they happen to still have time ranges attached.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.3.0">Release 9.3.0 &#x2013; 2021-05-06</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading roles from P6 databases, XER and PMXML files, and for writing roles to PMXML files. Roles are represented as resources. The new resource Boolean attribute &quot;Role&quot; is used to distinguish between Resource instances which represent resources and those which represent roles.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading resource curves from P6 databases, XER and PMXML files, and for writing resource curves to PMXML files. The WorkContour enum is now a class, and instance of this class are used to represent resource curves. The curves are available via the work contour attribute of resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected the data type of the task physical percent complete attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of non-standard relationship type representations encountered in XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.6">Release 9.2.6 &#x2013; 2021-04-26</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle invalid baseline numbers when reading MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve custom field handling when reading GanttProject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.5">Release 9.2.5 &#x2013; 2021-04-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add launcher batch file and shell script.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of calculated task attributes when writing a project to a different format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that dates are rolled up to summary tasks when reading FastTrack files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Synchro 6.3 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.4">Release 9.2.4 &#x2013; 2021-04-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading resource rate information GanttProject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.3">Release 9.2.3 &#x2013; 2021-04-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue reading Planned Duration from P6 databases and XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure Duration and Actual Duration are populated for WBS entries when reading P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.2">Release 9.2.2 &#x2013; 2021-04-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix issue with WBS ordering when writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.1">Release 9.2.1 &#x2013; 2021-04-04</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve Task critical flag calculation when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Synchro 6.3 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.2.0">Release 9.2.0 &#x2013; 2021-03-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy when reading subprojects from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add Remaining Late Start and Remaining Late Finish attributes to Task.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add Critical Activity Type attribute to Project Properties</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read Remaining Early Start, Remaining Late Start, Remaining Early Finish and Remaining Late finish from and write to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read Remaining Late Start and Remaining Late finish from P6 database and XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that WBS entries without child activities are not marked as critical.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't attempt to set the critical flag when reading XER and PMXML files where the schedule is using &quot;longest path&quot; to determine critical activities. (MPXJ currently doesn't have enough information to be able to determine the correct value for the critical flag in this situation).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure cost, duration, date and work attributes are rolled up to WBS entries for P6 schedules read from PMXML files, XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate baseline cost, duration, finish, start and work when reading from XER files, PMXML files and P6 databases where the &quot;Project Baseline&quot; has been set to &quot;Current Project&quot;.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.1.0">Release 9.1.0 &#x2013; 2021-03-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add methods to the ProjectFile class to attach a ProjectFile instance as a baseline. The baselines attached to the ProjectFile will be used to populate the relevant baseline attributes in the current schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added experimental support for writing baseline projects to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the Project GUID attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading PMXML files, the list of projects returned by the readAll method will include any baseline projects present in the file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading PMXML files which include the current baseline project, use this to populate the relevant baseline attributes in the main schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The Project Unique ID property is now an integer rather than a string.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading Primavera schedules, populate the project properties Project ID and Baseline Project Unique ID.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle Primavera resource rates which don't have a start or finish date.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle MSPDI files with resource availability tables which don't have a start or finish date.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the Activity ID field is populated consistently for WBS entries in PMXML files compared to the same schedule read from an XER file or P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure duration of manually scheduled tasks in MPP files is represented correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a9.0.0">Release 9.0.0 &#x2013; 2020-02-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>NOTE: this release introduces breaking changes!</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All fields which are non-user defined, but were previously being returned by MPXJ as custom fields are now represented as explicit field types. Custom fields now only contain values for user-defined custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>All code previously marked as deprecated has been removed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading an XER file or a P6 database, some custom project property names have been updated. LagCalendar is now CalendarForSchedulingRelationshipLag, RetainedLogic is now WhenSchedulingProgressedActivitiesUseRetainedLogic, ProgressOverride is now WhenSchedulingProgressedActivitiesUseProgressOverride, IgnoreOtherProjectRelationships is now WhenSchedulingProgressedActivitiesUseProgressOverride, and StartToStartLagCalculationType is now ComputeStartToStartLagFromEarlyStart.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML schema to version 20.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue where GUID values were not being read correctly from XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Percent complete type is now available as a task attribute for P6 schedules from any source.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that percent complete values are stored in the appropriate attributes when reading P6 schedules. (NOTE: Previously the &quot;reported&quot; percent complete value was stored as the tasks &quot;percent complete&quot; attribute. Now this holds the schedule percent complete value, and the percent work complete and physical percent complete attributes are also populated. To determine which value should be reported for a task, see the &quot;percent complete type&quot; extended field attribute.)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly handle default calendar when reading and writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update the sort order of WBS entries and activities in PMXML files to match the order exported by P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Match the way P6 exports the WBS code attribute for PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update the representation of Boolean values when writing PMXML files to match the form exported by P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Set the task type attribute when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve duration and actual duration calculations when reading XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue where resource assignment costs were not being read correctly from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read and write the suspend date and resume date attributes for PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>General improvements to the SDEF writer.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.16.0.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.5.1">Release 8.5.1 &#x2013; 2021-01-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't write unused enterprise custom field definitions to MSPDI files. This ensures that MS Project will open these files correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.5.0">Release 8.5.0 &#x2013; 2021-01-06</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Notes in their original format (HTML from P6, RTF from MS Project) can now be retrieved via the getNotesObject method on Task, Resource, and ResourceAssignment. Plain text notes can still be retrieved via the getNotes method. If you were previously using the &quot;preserve note formatting&quot; flag to retrieve the original formated version of a note, you will now need to use the getNotesObject method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Write WBS and Activity notes to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML compatibility improvements to ensure files can be successfully imported into P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.4.0">Release 8.4.0 &#x2013; 2020-12-29</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Previously when reading PMXML files, XER files, and P6 databases, a set of baseline attributes on tasks and assignments (including Start, Finish, Duration, Cost and Work) were being populated with planned values rather than baseline values. These baseline attributes are no longer being set. The values they previously contained are now available as custom fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read activity notepad entries for XER, PMXML files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read schedule and leveling options from PMXML files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading activity cost and work from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.5">Release 8.3.5 &#x2013; 2020-12-15</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix CVE-2020-35460: zip slip vulnerability (with thanks to Sangeetha Rajesh S, ZOHO Corporation)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.4">Release 8.3.4 &#x2013; 2020-12-10</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML schema to version 19.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that we always set the activity planned start and planned finish dates when writing a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the getPopulatedFields methods to ignore fields with default values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Made the Resource ID attribute available as a resource's TEXT1 custom field, with the alias &quot;Resource ID&quot; when reading PMXML and XER files, or from a P6 database. (Note that presently for XER files and P6 databases, the Resource ID value is also read into the initials attribute. This behaviour is deprecated and will be removed in the next major MPXJ release).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate the Resource ID with the value read from a P6 schedule when writing a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the hours per day, week, month and year attributes are read from and written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue causing the hours per day calendar attribute to be read inaccurately from XER files and P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read assignment actual overtime cost and work attributes from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update calculation of assignment work, cost and units attributes for PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.3">Release 8.3.3 &#x2013; 2020-11-24</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added cost rate table support when reading from and writing to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a getPopulatedFields method to the TaskContainer, ResourceContainer and ResourceAssignmentContainer classes. This will retrieve the set of fields which are populated with a non-null value across the whole project for Tasks, Resources, and ResourceAssignments respectively.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add START_ON, FINISH_ON constraint types. &#xa7; MANDATORY_START, MANDATORY_FINISH constraint types. MANDATORY_START/FINISH are now represented as MUST_START/FINISH_ON. This change allows users to distinguish between START/FINISH_ON and the MANDATORY_* constraints when reading P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of cost rate tables and availability tables when writing to an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle P6 databases and XER files with user defined fields of type FT_FLOAT.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Align invalid XER record behaviour with P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle Planner files which don't contain an allocations tag.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle MPP files with missing view or table data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.2">Release 8.3.2 &#x2013; 2020-10-22</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for &quot;new tasks are manual&quot; project property (Contributed by Rohit Sinha)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved support for reading and writing outline codes and extended attributes for MSPDI files (Based on a contribution by Dave McKay)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved handling of enterprise custom fields when reading MPP files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update Primavera database and XER readers to avoid potential type conversion errors when the caller provides their own field mappings.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of some MPP12 MPP file variants.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid error when reading timephased data from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle MPP files with missing view data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update junit to 4.13.1.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.1">Release 8.3.1 &#x2013; 2020-10-14</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Minor updates to PlannerReader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.3.0">Release 8.3.0 &#x2013; 2020-10-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add the &quot;userDefined&quot; attribute to the CustomField class to allow caller to determine if the field has been created by a user or MPXJ.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading expense items, expense categories and cost accounts from XER files, PMXML files and Primavera databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for writing expense items, expense categories and cost accounts to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the XER file reader to ignore invalid records rather than reporting an error, matching the behaviour of P6</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the XER file reader to ensure that activity suspend and resume dates are read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the XER file reader to ensure that if the reader returns the project selected by the caller when the caller supplies a value for project ID.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML reader to avoid user defined field collisions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated PMXML reader to add setProjectID and listProjects methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update the .net extension method ToIEnumerable to work with java.lang.Iterable rather than java.util.Collection</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.2.0">Release 8.2.0 &#x2013; 2020-09-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>All readers, including the UniversalProjectReader, now support a readAll method. If a file or database contains more than one project the readAll method can be used to retrieve them all in one operation. If the file format doesn't support multiple schedules, readAll will just return a single schedule.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add PrimaveraDatabaseFileReader to encapsulate access to SQLite Primavera databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the summary flag is true for WBS items in Primavera schedules, even if they have no child activities.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that the critical flag is rolled up appropriately to WBS items when reading Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Set export flag property when reading projects from a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected data type of resource assignment Work Contour field.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected data type of resource fields: BCWS, BCWP, ACWP, SV, CV, and Work Contour.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Corrected data type of task fields: CV, ACWP, VAC, CPI, EAC, SPI, TCPI, and Work Contour.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.1.4">Release 8.1.4 &#x2013; 2020-08-31</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix CVE-2020-25020: XXE vulnerability (with thanks to Sangeetha Rajesh S, ZOHO Corporation)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Import milestone constraints from Asta schedules (Contributed by Dave McKay)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle elapsed durations in Asta schedules (Based on a contribution by Dave McKay)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly determine the constraint type for tasks with ALAP placement with or without predecessors when reading from Asta schedules (Contributed by Dave McKay)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle a missing table name when reading an XER file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle an unexpected calendar data when reading an XER file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly handle XER files with multibyte character encoding.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Import all schedule and leveling options from XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure project calendars are read from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added readAll methods to PrimaveraPMFileReader to allow all projects contained in a PMXML file to be read in a single pass.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.1.3">Release 8.1.3 &#x2013; 2020-06-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reliability when reading custom field values from certain MPP12 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of activity percent complete when reading from certain XER files or P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of WBS percent complete when reading from certain XER files or P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of task durations when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue handling the end date of calendar exceptions when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue with correctly identifying the calendar applied to summary tasks when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate percent complete, duration, actual start, actual finish, early start, late start, early finish and late finish attributes for summary tasks when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>The percent complete value reported for tasks when reading Asta schedules is now Duration Percent Complete. The Overall Percent Complete value originally being returned is available in a custom field.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.1.2">Release 8.1.2 &#x2013; 2020-06-18</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve detection of unusual MSPDI file variants.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to read task notes from FastTrack FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.1.1">Release 8.1.1 &#x2013; 2020-06-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Synchro 6.2 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.1.0">Release 8.1.0 &#x2013; 2020-06-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Experimental support for reading Project Commander schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update to use JAXB 2.3.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid failures caused by unreadable OLE compound documents when the UniversalProjectReader is trying to determine the file type.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Strip trailing ASCII NUL characters from text fields when reading from a Primavera database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of task order when reading Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of task data when reading some MPP file variants.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reliability when reading certain SureTrak files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.8">Release 8.0.8 &#x2013; 2020-04-20</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of numeric character references invalid for XML 1.0 in PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of resource calendars read from Planner files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of resource calendars read from MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ignore the milestone flag when reading MPX files if the task has a non-zero duration.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure JSON files can be written when Unique ID predecessor/successor attributes have been read from an MPX file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.7">Release 8.0.7 &#x2013; 2020-04-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.15.0.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of PMXML files with empty calendar exception time ranges.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.6">Release 8.0.6 &#x2013; 2020-03-05</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 4.1.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of some XER file variants.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.5">Release 8.0.5 &#x2013; 2020-02-07</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Allow users to determine WBS attribute content with &quot;wbs is full path&quot; flag for Primavera readers.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure summary task start and finish dates are populated when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use baseline start and finish dates as planned start and finish dates when writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Late start and late finish dates are now written to PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.4">Release 8.0.4 &#x2013; 2020-02-06</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update sqlite-jdbc dependency to 3.30.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of characters invalid for XML 1.0 in PMXML files generated by P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.3">Release 8.0.3 &#x2013; 2020-01-27</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of zero value durations, costs and units from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve percent complete calculation for certain XER file and P6 Database schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve percent complete calculation for certain P3 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of incorrectly encoded characters in PMXML files generated by P6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that negative durations can be written to and read from MSPDI files in the format understood by MS Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.2">Release 8.0.2 &#x2013; 2020-01-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of zero duration tasks read from Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.1">Release 8.0.1 &#x2013; 2020-01-05</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add missing nuget dependency</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a8.0.0">Release 8.0.0 &#x2013; 2020-01-02</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>MPXJ now requires Java 8 or later.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed deprecated methods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 4.1.1.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 8.1.5717.0.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.8">Release 7.9.8 &#x2013; 2019-12-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading and writing outline code/custom field lookup tables for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added sample code to demonstrate creation of timephased work.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate project status date attribute when reading Asta schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate parent attribute when reading activity code values from Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve configurability of PrimaveraDatabaseReader and PrimaveraXERFileReader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Made JAXB JARs an explicit dependency to avoid issues with recent Java versions which do not include them.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.7">Release 7.9.7 &#x2013; 2019-11-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Round percent complete values read from Asta files to two decimal places to avoid values like 99.9999999%.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.6">Release 7.9.6 &#x2013; 2019-11-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for FastTrack files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.5">Release 7.9.5 &#x2013; 2019-11-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added flag to manage compliance with password protection. (Contributed by ztravis)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Synchro 6.1 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue where the task hierarchy was not correctly represented when reading a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.4">Release 7.9.4 &#x2013; 2019-11-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading Sage 100 Contractor schedule grid files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure attribute names are valid when exporting JSON.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of custom field lookup values (Based on a contribution by Nick Darlington).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue when copying a calendar which has exceptions defined.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.3">Release 7.9.3 &#x2013; 2019-09-10</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading task early finish and late finish attributes from Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure XER files containing secondary constraints can be read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Preserve calendar IDs when reading from XER files and P6 database (Based on a contribution by forenpm).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure base calendars are read correctly for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure MPP files with unexpected auto filter definition data are handled gracefully.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Preserve leveling delay format when reading tasks from MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure unexpected structure of timephased data is handled gracefully when reading MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.2">Release 7.9.2 &#x2013; 2019-08-19</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading and writing secondary constraints from P6 schedules (Based on a contribution by Sruthi-Ganesh)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Synchro SP files containing blank tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Make constraint type mapping consistent when reading and writing PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of leveling delay units and actual duration units (Based in a contribution by Daniel Schmidt).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain types of malformed MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain types of malformed SDEF files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Map P6 Equipment resource type to cost rather than work (Contributed by forenpm)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain MPP files containing large numbers of blank tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain MPX files containing trailing delimiters.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.1">Release 7.9.1 &#x2013; 2019-07-01</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Set task start, finish and percent complete when reading SDEF files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.9.0">Release 7.9.0 &#x2013; 2019-07-01</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading SDEF files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.8.4">Release 7.8.4 &#x2013; 2019-06-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading data links (linked fields) configuration from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to avoid an infinite loop when processing certain corrupt files (Contributed by ninthwaveltd).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update MSPDI generation to ensure MS Project correctly recognises complete tasks without resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that activity codes are read for P6 schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading custom field values derived from custom field lookup tables in MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for MPP files written with the June 2019 update of Microsoft Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.8.3">Release 7.8.3 &#x2013; 2019-05-24</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of task baseline start, start, baseline finish, finish and slack fields read from FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.8.2">Release 7.8.2 &#x2013; 2019-05-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of MPP files with missing Props.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of custom field lookup tables for MPP12 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly write activity duration type to a PMXML file (Contributed by Sebastian Stock)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of Activity Type and Activity ID when writing PMXML files (Based on a contribution by Sebastian Stock)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update PMXML file reader for greater consistency with XER and P6 database readers (Activity ID, Activity Type, Status, and Primary Resource ID)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain FTS files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of task notes from MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>More accurately read predecessors and successors from Asta PP files (Based on a contribution by Dave McKay)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When a schedule is read from P6, P3, or SureTrak, Task.getSummary will return true only if a task is part of the WBS</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading the Synchro Scheduler 2018 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Task.hasChildTasks() method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Splits data coming in as null for all tasks. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/330">330</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.8.1">Release 7.8.1 &#x2013; 2019-02-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Improve support for reading the Synchro Scheduler 2018 SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading Gantt Designer GNT files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of non-standard MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of non-standard GanttProject files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update MSPDI generation to ensure MS Project correctly recognises complete milestones without resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading user defined fields from PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ignore hammock tasks when reading PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.8.0">Release 7.8.0 &#x2013; 2019-01-18</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading and writing GUIDs for Tasks, Resources, and Assignments in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Java build to use Maven</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to provide a general performance improvement (Based on a contribution by Tiago de Mello)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to fix an issue when the Microsoft JDBC driver is used to access a P6 database in SQL Server 2005</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Asta lag sign incorrect (Based on a contribution by Dave McKay). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/332">332</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Asta constraints lost (Contributed by Dave McKay). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/333">333</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MSDPI into Asta doesn't import Calendar exceptions (Contributed by Dave McKay). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/335">335</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.7.1">Release 7.7.1 &#x2013; 2018-10-23</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Read additional schedule options from XER files. (Contributed by forenpm)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of some types of MPP file with missing resource assignment data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that resource assignment flag fields are read correctly for all MPP file types (Based on a contribution by Vadim Gerya).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that timephased actual work is handled correctly for material resources (Contributed by Vadim Gerya).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy when reading resource type from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve compatibility of generated MSPDI files with Asta Powerproject (Contributed by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.7.0">Release 7.7.0 &#x2013; 2018-10-12</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading the Synchro Scheduler SP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading the activity code (ID) from Asta files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>When reading a Phoenix file, set the project's status date to the data date from the storepoint.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle MSPDI files with timephased assignments that don't specify a start and end date.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.6.3">Release 7.6.3 &#x2013; 2018-10-04</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading Remaining Early Start and Remaining Early Finish task attributes from P6. (Contributed by forenpm)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading Retained Logic and Progressive Override project attributes from P6. (Contributed by forenpm)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix incorrect sign when calculating start and finish slack (Contributed by Brian Leach).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly read predecessors and successors from Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.6.2">Release 7.6.2 &#x2013; 2018-08-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for nvarchar columns when reading from a P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to correctly read percent lag durations from MSPDI files (based on a contribution by Lord Helmchen).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the data type for the ValueGUID tag in an MSPDI file (based on a contribution by Lord Helmchen).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.6.1">Release 7.6.1 &#x2013; 2018-08-29</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of MPP files where MPXJ is unable to read the filter definitions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of SureTrak projects without a WBS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of SureTrak and P3 WBS extraction.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle unsupported ProjectLibre POD files more gracefully.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve detection of non MS Project compound OLE documents.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle XER files which contain no projects.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.6.0">Release 7.6.0 &#x2013; 2018-07-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading ConceptDraw PROJECT CDPX, CPDZ and CPDTZ files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading the export_flag attribute from XER files. (Contributed by forenpm)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use correct licence details in Maven pom.xml (contributed by Mark Atwood).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve UniversalProjectReader's handling of XER files containing multiple projects.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.5.0">Release 7.5.0 &#x2013; 2018-06-19</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading activity codes from P6 databases, XER files, and PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading user defined values from a P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for PRX files which contain a SureTrak database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading the resource &quot;enterprise&quot; attribute from MPP12 and MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve performance when reading user defined values from XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved support for older Primavera PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.11.0 for improved RTF parsing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.4.4">Release 7.4.4 &#x2013; 2018-06-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of calendar exceptions in MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of MPP files with large numbers of null tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness when reading timephased data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly sort Primavera schedules containing WBS entries with no child activities.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.4.3">Release 7.4.3 &#x2013; 2018-05-25</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading the resource &quot;generic&quot; attribute from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add a Unique ID attribute to the Relation class and populate for schedule types which support this concept.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Store the Primavera Project ID as Unique ID in the project properties.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update MerlinReader to ensure support for Merlin Project Pro 5.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.4.2">Release 7.4.2 &#x2013; 2018-04-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle malformed duration values in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle unexpected calendar exception data structure in certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of certain unusual MPP12 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>More work to gracefully handle POI issue 61677, allowing affected MPP files to be read successfully.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.4.1">Release 7.4.1 &#x2013; 2018-04-16</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add methods to list projects available in P3 and SureTrak database directories.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid NPE when a work pattern can't be located in an Asta Powerproject PP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid array bounds exception when reading certain PRX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read outline code value lists from MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle SureTrak projects without a WBS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.4.0">Release 7.4.0 &#x2013; 2018-03-23</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera SureTrak databases from directories, zip files, and STX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for PP files generated by Asta Powerproject from version ********</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.3.0">Release 7.3.0 &#x2013; 2018-03-12</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera P3 databases from directories, zip files, and PRX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness when reading MPP files containing apparently invalid custom field data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve UniversalProjectReader byte order mark handling.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fields with lookup unreadable when a field has custom name. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/324">324</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.2.1">Release 7.2.1 &#x2013; 2018-01-26</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>More work to gracefully handle POI issue 61677, allowing affected MPP files to be read successfully.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid divide by zero when calculating percent complete from certain Primavera PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>UniversalProjectReader updated to recognise MPX files with non-default separator characters.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update FastTrack reader to handle invalid percentage values on resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update FastTrack reader to handle variations in UUID format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read the full project name from XER files and the Primavera database and store it in the project title attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.2.0">Release 7.2.0 &#x2013; 2018-01-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading TurboProject PEP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle numeric values with leading spaces in XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix array bounds error when reading constraints from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.1.0">Release 7.1.0 &#x2013; 2018-01-03</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading GanttProject GAN files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Ensure that calendar exception dates are read correctly from XER files and P6 databases regardless of the user's timezone.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read working day calendar exceptions from XER files and P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Mark some ProjectFile methods as deprecated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.0.3">Release 7.0.3 &#x2013; 2017-12-21</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use the Windows-1252 character set as the default when reading XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle POI issue 61677 to allow MPP affected MPP files to be read successfully.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle recurring calendar exceptions read from MSPDI files without an occurrence count.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness of FastTrack schedule reader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid reading empty calendar exceptions from MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.0.2">Release 7.0.2 &#x2013; 2017-11-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Further improvements to task pruning for Asta PP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.0.1">Release 7.0.1 &#x2013; 2017-11-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness when reading MPP files when using certain 64-bit Java runtimes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate the project's comments property when reading an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Ensure that tasks are not discarded when reading PP files from older Asta versions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong date ranges for split tasks. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/319">319</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getDefaultTaskType() not returning correct default task type. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/222">222</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a7.0.0">Release 7.0.0 &#x2013; 2017-11-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading recurring exceptions from MPP and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated RecurringTask class interface (Note: this is a breaking API change)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>MSPDI writer now uses save version 14 by default (Note: this may affect applications which consume MSPDI files you generate)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly handle MSPDI files with Byte Order Marks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle MSPDI files with varying namespaces.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness Merlin file reader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve extraction of task start and finish dates from PMXML files only containing partial data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Prevent POI from closing the input stream when using UniversalProjectReader</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Cannot read mpp file using getProjectReader. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/321">321</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.2.1">Release 6.2.1 &#x2013; 2017-10-11</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle corrupt MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve reading and writing slack values for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve activity hierarchy extraction from Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MSPDI Slack values not correctly set while loading. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/243">243</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.2.0">Release 6.2.0 &#x2013; 2017-10-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Work Weeks from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for calendar exception names for MPP and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.17.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy of calendar exception dates read from XER files and P6 database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Only write non-default user-defined field values to a PMXML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use Primavera P6 17.7 XML schema.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle corrupt document summary information in MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Don't duplicate exceptions when reading from an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPP DataType: Non-unique enumeration value. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/231">231</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Calendar Work Week missing from MPP data extraction. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/258">258</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>TimephasedWork Negative TotalAmount. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/318">318</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Date conversion fails in PrimaveraReader. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/320">320</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.1.2">Release 6.1.2 &#x2013; 2017-09-12</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle incomplete records in XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.1.1">Release 6.1.1 &#x2013; 2017-08-30</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Ensure all classes in the gem are required</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.1.0">Release 6.1.0 &#x2013; 2017-07-28</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Provide Task.getEffectiveCalendar() method</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Populate missing finish dates in MSPDI files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a6.0.0">Release 6.0.0 &#x2013; 2017-07-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Gracefully handle invalid calendar data in XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Handle XER files containing blank lines.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading resource rates and availability tables from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Include overtime in work and cost fields when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read default project calendar from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read resource rate and assignment units from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Set ignore resource calendar flag for tasks from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Change P6 costs to be calculated from resource assignment to support XER files without the cost table (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Map anticipated end date to deadline for P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update task work to include actual and remaining work when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Calculate summary task work fields by summing up children when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Set task project name when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix &quot;00:00&quot; calendar finish times to parse as end of day when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add default working hours if a calendar does not specify any hours when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read fiscal year start month from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix bug in rollup of child task dates containing null values that could set incorrect end date when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix date offset in parse of P6 calendar exceptions (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix count of P6 UDFs that map to same data type (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading Resource and Assignment UDFs from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update P6 UDFs to fill into multiple field types to expand storage capacity, for example into TEXT and ENTERPRISE_TEXT (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Use only the WBS as activity code for WBS tasks instead of also appending name for P6 tasks (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add the ability to link task Relations that cross project boundaries in XER files (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add function to clear all exceptions from ProjectCalendar instances (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Reading the lag calendar scheduling option as the &quot;LagCalendar&quot; custom project property when reading from P6 (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated UDF parsing to handle values as booleans if the user chooses to map them to Flag fields (Contributed by Brandon Herzog).</td>
<td><a href="team-list.html#brandonherzog">brandonherzog</a></td></tr></table></section><section>
<h3 id="a5.14.0">Release 5.14.0 &#x2013; 2017-07-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of activity codes read from Phoenix files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Calculate percent complete for tasks read from Phoenix files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Populate task duration with Original Duration attribute when reading from XER files or P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that task finish dates are read correctly from Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve UniversalProjectReader's handling of non-MPP OLE compound documents.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve task hierarchy and ordering when reading some MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.13.0">Release 5.13.0 &#x2013; 2017-06-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Further improve handling of WBS, bar, and task structure from Asta files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.12.0">Release 5.12.0 &#x2013; 2017-06-26</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of WBS, bar, and task structure from Asta files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.11.0">Release 5.11.0 &#x2013; 2017-06-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of malformed durations in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve performance when reading MPP files with certain kinds of timephased data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Raise a specific &quot;password protected&quot; exception type from the Ruby gem.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fix an issue with the storage of the &quot;earned value method&quot; task attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.10.0">Release 5.10.0 &#x2013; 2017-05-23</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of deleted tasks in MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of invalid predecessor tasks in MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of invalid saved view state in MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Empty baseline dates populated with garbage date instead of null. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/313">313</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.9.0">Release 5.9.0 &#x2013; 2017-04-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading ProjectLibre POD files (from ProjectLibre version 1.5.5 onwards).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correct getter method name for &quot;file application&quot; project property.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.8.0">Release 5.8.0 &#x2013; 2017-04-21</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.16 (note new dependency on Apache Commons Collections required by POI).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for estimated durations in Merlin files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Read task notes from Asta files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading resource rates from Phoenix files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add &quot;file application&quot; and &quot;file type&quot; to project properties to determine source of schedule data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.7.1">Release 5.7.1 &#x2013; 2017-03-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for Phoenix Project Manager XML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.7.0">Release 5.7.0 &#x2013; 2017-03-20</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for FastTrack Schedule files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that timephased data calculations correctly handle entry to and exit from DST.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Microsoft Project 2016:  Issue with assignment 'Work Contour' attribute. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/306">306</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.5">Release 5.6.5 &#x2013; 2017-03-07</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of invalid calendar data in MSPDI files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of XER files containing multi-line records</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve handling of malformed MPX files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Add support for elapsed percent to MSPDI writer. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/308">308</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPX percent lag incorrect. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/310">310</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.4">Release 5.6.4 &#x2013; 2017-02-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>UniversalProjectReader now recognises and handles byte order marks</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>TimeUnit.ELAPSED_PERCENT read incorrectly from MPP files. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/307">307</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.3">Release 5.6.3 &#x2013; 2017-02-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added a parameter to the Ruby gem to allow the maximum JVM memory size to be set.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.10.0 for improved RTF parsing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.2">Release 5.6.2 &#x2013; 2017-02-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Failed to Parse error with Primavera 15.2 or 16.1 XML files. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/305">305</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.1">Release 5.6.1 &#x2013; 2017-02-03</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Correct resource assignment handling for Phoenix Project Manager schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.6.0">Release 5.6.0 &#x2013; 2017-01-29</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for Phoenix Project Manager schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.9">Release 5.5.9 &#x2013; 2017-01-27</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve robustness of date parsing for MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.8">Release 5.5.8 &#x2013; 2017-01-23</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix NPE when reading graphical indicators with unknown field type.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.7">Release 5.5.7 &#x2013; 2017-01-13</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix percent complete NaN value for some Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.6">Release 5.5.6 &#x2013; 2017-01-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix incorrectly set critical flag for primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.5">Release 5.5.5 &#x2013; 2017-01-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.9.0 for improved RTF parsing</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve calendar exception parsing for Primavera XER and database readers.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure the task summary flag is set correctly for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rollup baseline, early and late start and finish dates to WBS for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rollup baseline duration, remaining duration and percent complete to WBS for Primavera schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Use the project's critical slack limit value when setting the critical flag on a task.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Experimental support for reading Merlin Project schedules.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.4">Release 5.5.4 &#x2013; 2016-12-01</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Default to UTF-8 encoding when generating JSON files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.3">Release 5.5.3 &#x2013; 2016-11-29</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Correctly read text from MPP files when default charset is not UTF-8.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve accuracy when reading MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.2">Release 5.5.2 &#x2013; 2016-11-02</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Add Primavera Parent Resource ID as a specific resource attribute (Based on a contribution by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML writer generates currency record (Based on a contribution by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML writer defaults Activity PercentCompleteType to Duration (Based on a contribution by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML writer records currency and parent attributes for Resource (Based on a contribution by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML writer resource assignments include RateSource and ActualOvertimeUnits attributes(Based on a contribution by Dave McKay).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>MSPDI reader: gracefully handle invalid calendar exceptions..</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>PMXML writer: gracefully handle missing data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Planner writer: gracefully handle missing data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.1">Release 5.5.1 &#x2013; 2016-10-14</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Update universal project reader to support zip files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update ruby to align error handling with universal project reader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.5.0">Release 5.5.0 &#x2013; 2016-10-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Universal project reader.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid NPE when reading PMXML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Missing extended attributes. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/297">297</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>CrossProject field omission causes issues when importing to P6. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/300">300</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.4.0">Release 5.4.0 &#x2013; 2016-10-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.15.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.3.3">Release 5.3.3 &#x2013; 2016-08-31</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Avoid NPE when field type is unknown.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve Ruby error reporting.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for non-standard time formats in MPX files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for MPP14 files with very large numbers of blank tasks</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.3.2">Release 5.3.2 &#x2013; 2016-08-31</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>When reading an XER file, treat FT_STATICTPYE user defined fields as text.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.3.1">Release 5.3.1 &#x2013; 2016-07-01</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add data date attribute to PMXML output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update PMXML writer to avoid NPE.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Update PMXML writer to allow task field used for Activity ID to be chosen.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to avoid NPE when reading an XER file where project not under EPS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Generate Task IDs if missing from MSPDI file</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.3.0">Release 5.3.0 &#x2013; 2016-06-10</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for PP files generated by Asta Powerproject from version ******** onwards</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Minor improvements to SDEF support.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.8.0</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve finish time handling in PMXML files (contributed by lobmeleon)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.2.2">Release 5.2.2 &#x2013; 2016-03-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for resource assignment Stop and Resume attributes for MPP and MSPDI files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>PrimaveraPMFileWriter.write fails with java.lang.IllegalArgumentException. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/291">291</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Microsoft Project 2016 : Need to set 'Stop' and 'Resume' properties for org.mpxj.ResourceAssignment. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/292">292</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.2.1">Release 5.2.1 &#x2013; 2016-02-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for PP files generated by Asta Powerproject up to version ********</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.2.0">Release 5.2.0 &#x2013; 2016-02-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for PP files generated by Asta Powerproject 11, Powerproject 12, Easyplan 2, Easyplan 3, Easyplan 4, Easyplan 5 and Easyplan 6</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unsupported encoding command ansicpg949. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/285">285</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>AvailabilityTable getEntryByDate does not work properly. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/288">288</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.18">Release 5.1.18 &#x2013; 2016-01-25</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unsupported encoding command ansicpg1254. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/285">285</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>NullPointerException in CriteriaReader.getConstantValue. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/286">286</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Allow a character encoding to be specified when reading an XER file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/287">287</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Write Primavera Primary Resource Unique ID to Task field Number1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.17">Release 5.1.17 &#x2013; 2015-12-30</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading MPP files generated by Project 2016</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Handle missing time component of a time stamp field when reading an MPX file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.16">Release 5.1.16 &#x2013; 2015-12-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve support for reading MPX files generated by SureTrak</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.15">Release 5.1.15 &#x2013; 2015-12-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix WBS and Activity ordering for tasks from Primavera.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.14">Release 5.1.14 &#x2013; 2015-12-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Strip unescaped control characters from JSON output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.13">Release 5.1.13 &#x2013; 2015-11-26</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>For schedules imported from Primavera ensure tasks representing activities are ordered by Activity ID within the WBS to match Primavera.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.12">Release 5.1.12 &#x2013; 2015-11-16</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Avoid NPE when writing MSPDI files with timephased data  (contributed by Bruno Gasnier)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve resource assignment constructor (based on a contribution by Bruno Gasnier)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve MPX French translations (contributed by Bruno Gasnier)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add calendar specific minutes per day, week, month, and year (based on a contribution by Bruno Gasnier)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Add support for reading and writing GUID attribute for PMXML, XER files and Primavera database.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.11">Release 5.1.11 &#x2013; 2015-11-12</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Avoid NPE when reading MPP14 custom properties.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Ensure calculated task attributes are present in JSON output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Handle MSPDI files written by German versions of Microsoft Project (based on a contribution by Lord Helmchen)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>synchronizeTaskIDToHierarchy clears list of tasks. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/277">277</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>PrimaveraPMFileWriter throws Exception at write(..). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/273">273</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Parent task is always null when reading a Primavera XER file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/281">281</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Ensure that Task.getSuccesors() and Task.getPredecessors() return an empty list rather than null.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.10">Release 5.1.10 &#x2013; 2015-09-09</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improve FixedMeta2 block size heuristic to improve reliability when reading MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.9">Release 5.1.9 &#x2013; 2015-08-29</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Ensure Resource BookingType is read correctly from MPP files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added basic custom field attributes to JSON output</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Ruby methods to work with custom field aliases</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix to infinite loop condition when writing calendar (contributed by lobmeleon)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPXJ getNotes() API returns garbled value for multibyte characters. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/274">274</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unsupported encoding error when reading resource notes. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/268">268</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Incorrect resource types are read (contributed by Colin Rodriguez). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/256">256</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Symmetry between Primavera PM reader/writer (contributed by lobmeleon)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Added UDF support to PMXML file reader and writer(contributed by lobmeleon)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to rtfparserkit 1.4.0</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.8">Release 5.1.8 &#x2013; 2015-07-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Another attempt at getting tzinfo-data dependency working</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.7">Release 5.1.7 &#x2013; 2015-07-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated ruby gem to make tzinfo-data dependency conditional on platform</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.6">Release 5.1.6 &#x2013; 2015-07-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated ruby gem to allow timezone to be provided</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.5">Release 5.1.5 &#x2013; 2015-06-05</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 8.0.5449.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.4">Release 5.1.4 &#x2013; 2015-06-03</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to generate Activity ID for Primavera WBS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to correct Primavera duration percent complete calculation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.3">Release 5.1.3 &#x2013; 2015-05-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to ensure Ruby reads Boolean attributes correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.2">Release 5.1.2 &#x2013; 2015-05-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to ensure Ruby recognises short type as an integer.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.1">Release 5.1.1 &#x2013; 2015-05-18</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use ruby-duration gem to avoid conflict with ActiveSupport::Duration.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.1.0">Release 5.1.0 &#x2013; 2015-05-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to ensure that PrimaveraDatabaseReader.setSchema accepts null or empty string</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Ensure conversion to/from .Net DateTime takes account of timezone and daylight savings (based on a contribution by Timour Koupeev)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.12.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getTaskFieldAliases, replaced by ProjectFile.getCustomField().getFieldByAlias</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getResourceFieldAliases, replaced by ProjectFile.getCustomField().getFieldByAlias</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a5.0.0">Release 5.0.0 &#x2013; 2015-05-06</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added project properties to the JSON output</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for project properties to the Ruby wrapper</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading data from a standalone Primavera P6 SQLite database</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>XXE security vulnerability. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/267">267</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Task Number fields not saved to file if the value would floor to zero. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/266">266</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Not all project calendars are read in for Project 2013 files (based on a contribution by Colin Rodriguez). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/255">255</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed TaskContainer class to ChildTaskContainer</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectHeader class to ProjectProperties</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced ProjectConfig class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced TaskContainer class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced ResourceContainer class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced ResourceAssignmentContainer class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced ProjectCalendarContainer class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getProjectHeader to getProjectProperties</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getCalendar to getDefaultCalendar</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.setCalendar to setDefaultCalendar</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed MppReader.getReadHeaderOnly to getReadPropertiesOnly</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed MppReader.setReadHeaderOnly to setReadPropertiesOnly</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getCalendarUniqueID to ProjectConfig.getNextCalendarUniqueID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getResourceUniqueID to ProjectConfig.getNextResourceUniqueID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getTaskUniqueID to ProjectConfig.getNextTaskUniqueID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getAssignmentUniqueID to ProjectConfig.getNextAssignmentUniqueID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getResourceID to ProjectConfig.getNextResourceID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectFile.getTaskID to ProjectConfig.getNextTaskID</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectHeader.getApplicationName to getShortApplicationName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectHeader.setApplicationName to setShortApplicationName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectHeader.setCalendarName to setDefaultCalendarName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Renamed ProjectHeader.getCalendarName to getDefaultCalendarName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.getProjectFilePath to ProjectHeader.getProjectFilePath</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.setProjectFilePath to ProjectHeader.setProjectFilePath</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.getApplicationName to ProjectHeader.getFullApplicationName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.setApplicationName to ProjectHeader.setFullApplicationName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.setDelimiter to ProjectHeader.setMpxDelimiter</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.getDelimiter to ProjectHeader.getMpxDelimiter</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.setProgramName to ProjectHeader.setMpxProgramName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.getProgramName to ProjectHeader.getMpxProgramName</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.setFileVersion to ProjectHeader.setMpxFileVersion</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.getFileVersion to ProjectHeader.getMpxFileVersion</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.setCodePage to ProjectHeader.setMpxCodePage</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved FileCreationRecord.getCodePage to ProjectHeader.getMpxCodePage</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.getMppFileType to ProjectHeader.getMppFileType</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.setMppFileType to ProjectHeader.setMppFileType</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.getApplicationVersion to ProjectHeader.getApplicationVersion</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.setApplicationVersion to ProjectHeader.setApplicationVersion</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.setAutoFilter to ProjectHeader.setAutoFilter</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Moved ProjectFile.getAutoFilter to ProjectHeader.getAutoFilter</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAliasTaskField, replaced by ProjectFile.getTaskFieldAliases().getField()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAliasResourceField, replaced by ProjectFile.getResourceFieldAliases().getField()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getTaskFieldAlias, replaced by ProjectFile.getTaskFieldAliases().getAlias()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.setTaskFieldAlias, replaced by ProjectFile.getTaskFieldAliases().setAlias()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getResourceFieldAlias, replaced by ProjectFile.getResourceFieldAliases().getAlias()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.setResourceFieldAlias, replaced by ProjectFile.getResourceFieldAliases().setAlias()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getTaskFieldAliasMap, replaced by ProjectFile.getTaskFieldAliases</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getResourceFieldAliasMap, replaced by ProjectFile.getResourceFieldAliases</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addTable, replaced by ProjectFile.getTables().add()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getTaskTableByName, replaced by ProjectFile.getTables().getTaskTableByName()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getResourceTableByName, replaced by ProjectFile.getTables().getResourceTableByName()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addFilter, replaced by ProjectFile.getFilters().addFilter()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.removeFilter, replaced by ProjectFile.getFilters().rmoveFilter()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAllResourceFilters, replaced by ProjectFile.getFilters().getResourceFilters()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAllTaskFilters, replaced by ProjectFile.getFilters().getTaskFilters()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getFilterByName, replaced by ProjectFile.getFilters().getFilterByName()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getFilterByID, replaced by ProjectFile.getFilters().getFilterByID()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAllGroups, replaced by ProjectFile.getGroups()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getGroupByName, replaced by ProjectFile.getGroups().getByName()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addGroups, replaced by ProjectFile.getGroups().add()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addView, replaced by ProjectFile.getViews().add()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.setViewState, replaced by ProjectFile.getViews().setViewState()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getViewState, replaced by ProjectFile.getViews().getViewState()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getResourceSubProject, replaced by ProjectFile.getSubProjects().getResourceSubProject()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.setResourceSubProject, replaced by ProjectFile.getSubProjects().setResourceSubProject()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addSubProject, replaced by ProjectFile.getSubProjects().add()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getAllSubProjects, replaced by ProjectFile.getSubProjects</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireTaskReadEvent, replaced by ProjectFile.getEventManager().fireTaskReadEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireTaskWrittenEvent, replaced by ProjectFile.getEventManager().fireTaskWrittenEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireResourceReadEvent, replaced by ProjectFile.getEventManager().fireResourceReadEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireResourceWrittenEvent, replaced by ProjectFile.getEventManager().fireResourceWrittenEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireCalendarReadEvent, replaced by ProjectFile.getEventManager().fireCalendarReadEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireAssignmentReadEvent, replaced by ProjectFile.getEventManager().fireAssignmentReadEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireAssignmentWrittenEvent, replaced by ProjectFile.getEventManager().fireAssignmentWrittenEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireRelationReadEvent, replaced by ProjectFile.getEventManager().fireRelationReadEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireRelationWrittenEvent, replaced by ProjectFile.getEventManager().fireRelationWrittenEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.fireCalendarWrittenEvent, replaced by ProjectFile.getEventManager().fireCalendarWrittenEvent()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addProjectListener, replaced by ProjectFile.getEventManager().addProjectListener()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addProjectListeners, replaced by ProjectFile.getEventManager().addProjectListeners()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.removeProjectListener, replaced by ProjectFile.getEventManager().removeProjectListener()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.addGraphicalIndicator</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed ProjectFile.getGraphicalIndicator, replaced by ProjectFile.getCustomFields().getCustomField().getGraphicalIndicator()</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.6">Release 4.7.6 &#x2013; 2015-03-18</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a Ruby wrapper for MPXJ</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the ability to export project data as JSON, to make it easier to work with in languages other than Java</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the Assignment attribute Resource Request Type</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Primavera database and XER readers updated to match WBS visible in Primavera for each task. Previous behaviour of generating a unique WBS for each task can be restored using a flag set on the readers.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Avoid NPE when calculating Task Completed Through</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Read Task Earned Value Method correctly from MPP files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fix issue where some floating point attributes were returning NaN</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.5">Release 4.7.5 &#x2013; 2015-02-27</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Handle invalid Primavera calendar data gracefully</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.4">Release 4.7.4 &#x2013; 2015-02-25</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Failed to read project containing CodePage 1250 text. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/257">257</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MS Project 2010: tasks with null baseline dates. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/259">259</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Incorrect task end date read from Primavera XER and database</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Incorrect percent complete read from Primavera XER, database, and PMXML files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Failed to read fields held at the end of a fixed data block</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Task Baseline Estimated Duration, Baseline Estimated Start, Baseline Estimated Finish, Baseline Fixed Cost, and Baseline Fixed Cost Accrual</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the ability to customise the fields read from a Primavera database or XER file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Task Activity Type and Task Status as additional fields read from Primavera database and XER and files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Changed Task physical percent complete methods for consistency to use Number rather than Integer</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.3">Release 4.7.3 &#x2013; 2014-12-23</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.11.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use rtfparserkit 1.1.0 for Java 6 compatibility.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.2">Release 4.7.2 &#x2013; 2014-12-15</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix Maven dependency issue.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.1">Release 4.7.1 &#x2013; 2014-12-08</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a flag to MPPReader to indicate that only the project header should be read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.7.0">Release 4.7.0 &#x2013; 2014-12-04</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented new RTF parser for stripping RTF to improve performance and accuracy</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Removed non-API code from the top level package</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Improved support for reading built-in and custom project properties from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Improved resilience of MPP file reading to unknown data structures</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed issue which could cause an infinite loop when ordering tasks in a file containing multiple consecutive blank tasks</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed issue where free text versions of task start, finish, and duration fields were not being read correctly from MPP14 files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.6.2">Release 4.6.2 &#x2013; 2014-11-11</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed issue with custom duration field units not read correctly from MSPDI files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Problems with the lag calculated in the relation. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/223">223</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Outline code not read correctly from MPP file written by Project 2013</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Defensive changes to avoid exceptions when reading MPP files. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/239">239</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Deleted tasks being read from mpp file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/250">250</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added DotNetInputStream and DotNetOutputStream classes for ease of use under .Net.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to automatically generate and package MpxjUtilities.dll</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.6.1">Release 4.6.1 &#x2013; 2014-10-17</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed NuGet metadata</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.6.0">Release 4.6.0 &#x2013; 2014-10-17</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for NuGet.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed an issue where the ID and Unique ID resource attributes were being read incorrectly from MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed an issue where the project's default duration format was not being used</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Reading .MPP file using MPXJ 4.2 reads extra unintentional ResourceAssignment with the task which is not seen in Task Sheet in Microsoft Project. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/248">248</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>All resources have &quot;Material&quot; property. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/235">235</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to capture the Project ID to align with data read from XER file/database (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to ensure task percent complete supports Physical Percent, Duration Percent and Units Percent (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to ensure task baseline values match values read from XER file/database (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to ensure task actual duration to matches value read from XER file/database (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to read the task duration (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to read task LateStart, LateFinish, EarlyStart, EarlyFinish attributes correctly (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to read task Start and End correctly (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to identify milestones (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to set the task Critical attribute (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera PM XML file reader to include costs (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Primavera XER/Database readers to read task Start and End correctly (contributed by Nathaniel Marrin). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/247">247</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Migrated tests to JUnit 4</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.5.0">Release 4.5.0 &#x2013; 2014-03-01</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the ability to call the .Net version of MPXJ from COM.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support Primavera decimal database columns.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for user defined task fields (contributed by Mario Fuentes).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added POM for current Maven versions (contributed by Nick Burch)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unable to load mpp from project-2013. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/213">213</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Primavera currency files without currency information. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/226">226</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>PrimaveraReader cannot handle files with more than 30 user defined fields. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/227">227</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>setMilestone() issue. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/224">224</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPXJ 4.4 and 2013 files - invalid load of task data. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/210">210</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue with Asta Powerproject PP file tokenization</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where valid WBS values containing .0 are corrupted</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to allow Primavera hours per day to be a decimal value</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to support Primavera PM XML files generated by Primavera versions up to P6v8.3 (contributed by Mario Fuentes)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to set the StatusDate attribute in the project header from a Primavera database, XER file or PM XML file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use (a patched version of) POI 3.10.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.4.0">Release 4.4.0 &#x2013; 2013-03-14</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for writing Primavera PM XML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Asta Powerproject PP and MDB files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for writing SDEF files (Contributed by William Iverson).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Enterprise Custom Fields 1-50 for Task, Resources, and Resource Assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added MpxjExtensionMethods assembly to simplify working with Java types in .Net (Contributed by Kyle Patmore)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provided two new .Net DLL versions in addition to the original version. These allow properties to be accessed in a &quot;.Net style&quot;, and for languages apart from VB, provide .Net style method names.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to remove the distinction between base calendar and resource calendars in the ProjectFile class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to improve support for custom outline codes (Contributed by Gary McKenney)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getTimephasedOvertimeWork can return TimephasedWork with NaN. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/189">189</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Support for timephased cost for cost type resources. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/190">190</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Rolled Up tasks don't use default duration units. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/195">195</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Extract Primavera Task ID. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/199">199</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where the resource assignment delay attribute was not being read from or written to MSPDI files correctly</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where derived calendars were not being read correctly from MPP files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 7.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.3.0">Release 4.3.0 &#x2013; 2012-02-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera PM XML files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading timephased cost, and timephased baseline cost and baseline work from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Work Weeks in MSPDI files (SourceForge feature request 23).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 7.0.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix SourceForge bug 3290224: Incorrect order of tasks when writing an MSPDI file (contributed by Jonathan Besanceney).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>ResourceAssignment.getTaskUniqueID() returns null. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/161">161</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong project name in MPX file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/169">169</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong title in XML file when importing from XER file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/170">170</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong record number for resource calendar in MPX file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/168">168</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>In the XML file the element field SaveVersion is missing. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/171">171</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Loop when import task with 0% on units of works in resources. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/167">167</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>French locale NA incorrect. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/163">163</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Invalid dependency between child and parent. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/175">175</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Missing tasks from MS Project 2010 mpp file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/174">174</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong WBS code and WBS when converting a Primavera XER file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/179">179</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Error reading XER file with German localisation for numbers. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/177">177</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>TimephasedResourceAssignments with negative TotalWork. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/166">166</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong currency symbol in the exported file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/181">181</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>TimephasedResourceAssignment end date not correct. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/104">104</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Calendar hours are incorrect. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/116">116</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>NullReferenceException with getTimephasedBaselineWork. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/188">188</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Outline number is null when opening Project 2003 MPP file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/191">191</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unable to parse note (unknown locale). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/192">192</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPP9Reader marks all tasks after a null task as null. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/193">193</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where the Task critical attribute was incorrectly calculated for some manually scheduled tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where the Task summary attribute was not set correctly when using certain methods to add or remove child tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix an issue where subprojects were not read correctly (Contributed by Gary McKenney).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.2.0">Release 4.2.0 &#x2013; 2011-06-23</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for resource assignment fields Baseline Cost 1-n, Baseline Work 1-n, Baseline Start 1-n, Baseline Finish 1-n, Start 1-n, Finish 1-n, Date 1-n, Duration 1-n, Cost 1-n, Text 1-n, Number 1-n, Flag 1-n, for MPP, MPD, and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for task suspend date, task resume date, and task code read from Primavera, and represented in MS Project custom fields Date1, Date2, and Text1 respectively.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for retrieving the table associated with any view.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Error converting Mpp to planner. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/158">158</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MSPDI Linklag for TimeUnit.Percent. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/157">157</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Error reading calendars for 2010 files. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/156">156</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Duplication of calendar id. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/159">159</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong task start. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/153">153</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Wrong start and finish dates for 2010 files. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/156">156</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.1.0">Release 4.1.0 &#x2013; 2011-05-30</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Updated ProjectFile class to change default value for &quot;auto&quot; flags to simplify programmatic creation of project files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Manual, Start Text, Finish Text, and Duration Text attributes in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support cost resource type for MPP12, MPP14 and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Task.removePredecessor method (contributed by Leslie Damon).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added &quot;read presentation data&quot; flag to MPPReader - allows clients to save time and memory when MPP presentation data not required.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera calendars (contributed by Bruno Gasnier).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for resource assignment leveling delay for MPP, MPD, and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for &quot;unassigned&quot; resource assignments.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for task manual duration attribute for manually scheduled tasks in MPP14 and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for resource NT account attribute for MPP9, MPP12, and MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for physical % complete for MPP9, MPP12, and MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPXJ API returns the incorrect start date of a manual task. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/120">120</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Task id incorrect after importing from MPP14 file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/123">123</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPXJ 4.0 fails to work with Project 2010 format. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/124">124</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Index was outside the bounds of the array. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/128">128</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>header.getHonorConstraints() is not working in case of MPP. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/131">131</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Empty notes appear for all tasks when saving in XML format. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/139">139</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>All Extended Attributes always added when using MSPDIWriter. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/122">122</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Baseline/Actual Work in 2010 MPP missing. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/144">144</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>ResourceAssignment getCalendar not using IgnoreResourceCalendar flag. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/114">114</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>ExternalTaskProject value missing. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/146">146</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Deleted Primavera tasks handling problem. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/137">137</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Latest CVS version gives wrong values for inactive field. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/143">143</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Task ID order when creating a project file is not correct. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/125">125</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Invalid tasks that should not be there. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/106">106</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix task calendars read incorrectly from MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect month duration assumption (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect number format in MSPDI file in non-English locales (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect resource assignment actual work attribute for MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect task leveling delay attribute for MPP9, MPP12, and MPP14 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix leveling delay and link lag when writing an MSPDI file (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect assignment actual start date when writing an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to improve support for material resources in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to reduce overall size of MSPDI files by not writing default values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 0.46.0.1.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.7.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make task, resource, and assignment fields read from MPP files data-driven, rather than hard coded.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a4.0.0">Release 4.0.0 &#x2013; 2010-05-25</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Microsoft Project 2010 MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera P6 XER files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Primavera P6 databases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to target Java 1.6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Russian locale (Contributed by Roman Bilous).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Relation.getDuration() is always giving result in 'HOUR' fmt.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a3.2.0">Release 3.2.0 &#x2013; 2010-01-20</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Resource cost rate tables (Based on code by Andrei Missine).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Resource availability (Based on code by Andrei Missine).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for successors (Based on an idea by John D. Lewis).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for task and resource GUIDs.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a flag to allow raw timephased data to be retrieved from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix logical operator read issue in MPP auto filters (Contributed by Andrei Missine).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPXJ Issue: Related to Project Calendar. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/94">94</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>POI License in legal folder of download wrong. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/90">90</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix Steelray bug 15468: Null Pointer Exception reading task constraints.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Planner writer causes Null Pointer exception. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/102">102</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getRecurring() task is not working. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/100">100</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getStandardRateFormat() is returning 'null'. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/98">98</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getWeekStartDay() is not working. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/97">97</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>getDaysPerMonth() is not working. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/96">96</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Resource.getNotes() not working for MPP12 file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/101">101</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MPP: getEditableActualCosts() is not behaving correctly. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/105">105</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use POI 3.6.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use IKVM 0.42.0.3.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make MPX duration parsing more lenient (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make MPP Var2Data extraction more robust (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to implement MSPDI context caching to improve performance (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to improve MPP file task structure validation. (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to improve MPX file parsing. (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to automatically populate missing WBS attributes. (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to refactor the Relation class (note minor method name changes).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to add default calendar to Planner output.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a3.1.0">Release 3.1.0 &#x2013; 2009-05-20</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Plan file fails to load. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/73">73</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Resource Assignment Normaliser rounding problem. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/72">72</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Column alignment values are incorrect. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/78">78</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>NullPointerException in parseExtendedAttribute() (Contributed by Paul Pogonyshev). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/76">76</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>.0 at the end of WBS code and outline number (Contributed by Paul Pogonyshev). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/74">74</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Too strict net.sf.mpxj.mpd.ResultSetRow. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/79">79</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Generated planner file can't be opened. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/80">80</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Support for loading global.mpt. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/82">82</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Lowercase table name won't work with db on linux machines. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/81">81</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Standard Calendar localization import problem. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/71">71</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Strange duration conversion from database. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/83">83</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>FilterCriteria not being read in properly (Contributed by James Styles). Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/86">86</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix Steelray bug 12335: Infinite loop when reading an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix Steelray bug 8469: Subproject flag not set correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix potential NPEs (Suggested by Steve Jonik).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated EncryptedDocumentInputStream to wrap rather than extend the POI DocumentInputStream to allow use with POI 3.5. (Contributed by Josh Micich)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to provide strong names for .Net DLLs.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a3.0.0">Release 3.0.0 &#x2013; 2009-01-25</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to the Project 2007 MSPDI schema.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 3.2.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use the SAX parser with JAXB rather than DOM to reduce memory consumption.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MPX output to prevent Project 2007 complaining.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Task getNumber*() methods return inaccurate large values. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/68">68</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Duplicate task in file.getChildTasks() when opening MPX. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/56">56</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Relation.getTask returns null. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/57">57</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Task.getSplits() not consistent. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/58">58</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>WBS Field not imported Mpp12. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/60">60</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>There are some conflict in TaskField. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/63">63</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MSPDIReader is not setting calendarName in projectHeader. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/66">66</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Write resource calendar with exceptions only. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/67">67</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>File loses predecessors. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/69">69</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Resources not bring read. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/70">70</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix incorrect duration calculations where minutes per week were not being used (Contributed by Jonas Tampier).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated split task implementation to represent splits as DateRange instances rather than as hours.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added .Net DLLs using IKVM.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading timephased resource assignment data from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support CurrencyCode, CreationDate, LastSaved and HyperlinkBase project header fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading recurring task data from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added methods to MPXReader and MPXWriter to allow the caller to determine the supported locales.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added Spanish locale (Contributed by Agustin Barto).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for durations with percentage time lag (Contributed by Jonas Tampier).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support MSPDI file split tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a2.1.0">Release 2.1.0 &#x2013; 2008-03-23</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 3.0.2</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to address an out of memory exception raised when processing certain MPP12 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix a problem caused by duplicate ID values in MPP12 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix a problem with the subproject unique ID calculation (Contributed by Jari Niskala).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Import from Project 2007 ignores some tasks. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/48">48</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Crash on priority not set in MSPDI-file. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/52">52</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Resource start/finish dates with MSP 2007. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/51">51</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>MS Project 2007: Calendar exceptions dates are wrong. Fixes <a class="externalLink" href="https://sourceforge.net/p/mpxj/bugs/51">51</a>.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Enterprise task and resource fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Baseline task and resource fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for extracting non-English (i.e. character set encoded) text from note fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Chinese MPX files (contributed by Felix Tian).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading project start and end dates from all MPP file types (Bug #1827633).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for password protected MPP9 files (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for calendar exceptions for MPP12 files (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for value lists and descriptions for custom fields (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for timescale formats (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the project file path attribute (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the ignore resource calendar attribute (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the resource actual overtime work attribute (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the resource material label attribute (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the resource NT account attribute (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Improved support for hyperlinks (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Improved support for custom fields in MPP12 files (Contributed by Jari Niskala)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a2.0.0">Release 2.0.0 &#x2013; 2007-10-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Migrated to Java 5</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced generics</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Introduced enums</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to POI 3.0.1</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to JAXB 2.1.4</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Changed company details from Tapster Rock to Packwood Software</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a1.0.0">Release 1.0.0 &#x2013; 2007-08-30</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading MPD files via JDBC</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading Planner files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for over allocated flag to all MPP file formats.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for calculating duration variance from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for calculating start and finish variance from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for attribute change listeners for Task and Resource classes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for start slack, finish slack, free slack and total slack read from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for external tasks.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added unique ID generation for calendars read from MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the status date property of the project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a timezone related bug when handling dates for calendar exceptions (Contributed by Todd Brannam).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect calculation of lag times for some MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed missing predecessor tasks in certain rare MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect MPX file AM/PM text setting in certain locales.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed an ArrayIndexOutOfBoundsException.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a ClassCastException.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a zero length string error.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a duration rounding error when reading MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect &quot;as late as possible&quot; constraint handling.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Incorrect late start date read from an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Incorrect total slack calculation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Added a default for the task constraint type attribute to prevent a possible NPE when writing an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Added a default resource calendar name where the resource name is empty.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the Column.getTitle method to take account of user defined column aliases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to add another condition to the test for deleted tasks in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to significantly improve the performance of writing MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.9.2">Release 0.9.2 &#x2013; 2006-03-07</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for split views.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for graphical indicators.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a workaround for a bug in MS Project which is seen when calendar exceptions are exported to an MSPDI file. If the exception contained seconds and milliseconds, MS Project marked every day as being affected by the exception, not the day or range of days specified.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make date/time/number formats generic, and thus available to end users. For example, this allows users to format currencies in line with the settings in the project file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Standardised on minutes per day and minutes per week, rather than hours per day and hours per week.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Provided additional time ranges for calendar exceptions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Refactored Task and Resource to use TaskField and ResourceField to identify fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to automatically generate WBS for tasks read from MPP files when no WBS information is present in the file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug when reading MPP files where task finish dates appeared before the start date where a &quot;start no later than&quot; constraint was in use.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug which resulted in invalid MPX files being generated when a project either had no tasks, or it had no resources.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a long-standing bug where the calendar records were being written into MPX files after they were referred to in the project summary record.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where WBS and Outline Levels were not being auto generated correctly when an MPP file contained a project summary task.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where split tasks were not being reported correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.9.1">Release 0.9.1 &#x2013; 2006-01-26</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Major API rewrite.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a flag called &quot;expanded&quot; to the Task class to represent whether a task in an MPP9 file is shown as expanded or collapsed by MS Project.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug in the relation code in MpxjQuery (contributed by Shlomo Swidler).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Modified MPXDateFormat, MPXTimeFormat and MPXCurrencyFormat to derive them from DateFormat and NumberFormat.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for MPT files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug which could case an NPE when reading certain MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the &quot;marked&quot; attribute for MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading split task data from MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading calculate multiple critical paths flag.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug which could case an array out of bounds exception in the Priority (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug #1346735 &quot;Priorities of the tasks are exported incorrectly&quot;.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added code to allow tasks, resources, resource assignments and calendars to be removed from the data structure.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented Italian MPX file format translation (contributed by Elio Zoggia).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Cleaned up calendar usage.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for retrieval of custom document summary fields from the project header (contributed by Wade Golden).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use checkstyle 4.0 and fixed warnings.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rationalised duration conversions into a set of methods in the MPXDuration class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Replaced various file format conversion utilities with the general purpose MpxjConvert utility.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed an issue where tasks with a percent complete value, but no resource assignments, would not write correctly to an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added an accessor method for resource calendars.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Unique ID generation was not correct for tasks, resources and calendars if these entities were added to an existing project file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a compatibility issue with POI3</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added an event listener to the project file to allow notifications of resources and tasks being read and written to and from a file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a compiler warning when build with JDK5.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where a project start date was not being set correctly in the project header.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading the project header &quot;calendar name&quot;, &quot;schedule from&quot; and &quot;revision&quot; values from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed split task support.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Enhanced TableFontStyle implementation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.25">Release 0.0.25 &#x2013; 2005-08-11</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading all properties from an MPP9 file which define the visual appearance of the Gantt Chart view shown in Microsoft Project (development funding courtesy of Steelray).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Tidied up constructors. Added no-argument constructors to the MPPFile and MSPDIFile classes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Fixed incorrect value in WorkGroup enumerated type.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented the resource assignment work contour property (contributed by Wade Golden).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Implemented correct handling for MPX files using different character set encodings (suggested by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed task duration calculation when importing an MPP file with a &quot;non-standard&quot; hours-per-day setting (contributed by Wade Golden).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to ensure that the MPX task fixed attribute, and the MPP/MSPDI task type attribute are correctly handled.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to implement the remaining project header attributes supported by the MSPDI file format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to add support for reading the MPX 3.0 files generated by Primavera (courtesy of CapitalSoft).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect assumptions about conversion of durations to hours when writing MPX files (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to calculate remaining work for resource assignments on import, to allow MSPDI export of this data to work correctly (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to add another condition to the test for deleted tasks in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix a problem with reading assignment data from MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rationalised the location of the JUnit tests and the sample files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a problem where the project start and end dates reported in the project header were incorrect.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed an array out of bounds exception when reading an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to allow MPXCalendarHours to accept an arbitrary number of time periods.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Introduced the Day class to replace the use of arbitrary integers to represent days of the week.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the ability to query the task assignments for a resource using the Resource.getTaskAssignments() method.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a problem with number formats in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the MPP View class to extract the view type.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to ensure that duration values read from an MSPDI file are converted into the appropriate duration units, rather than being left as hours as the durations are represented in the MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented French MPX file format translation (contributed by Benoit Baranne).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug reading assignment work contour attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make failure more graceful when a Microsoft Project 4.0 MPP file is encountered.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where deleted constraints in an MPP9 file were not being ignored.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make replace the int relation type in the Relation class with instances of the RelationType class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to derive RelationList from AbstractList.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added sample code to MpxjQuery to illustrate retrieval of information from Relation instances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MpqjQuery to parse MSPDI files as well as MPP and MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for early start, early finish, late start, late finish to MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MPP9 file support to handle start as late as possible constraints.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for subproject file information in MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where occasionally a task in MPP9 files were not being read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a NegativeArrayIndexException thrown when reading certain MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Reduced the memory used by MPXJ by anything up to 60%, particularly when reading large MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug when reading MPX files where the field delimiter was not comma, and task relation lists contained more than one entry.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Updated to fix unreliable retrieval of project start and end dates from certain MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed schedule from value in MSPDI files (contributed by Frank Illenberger).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug when reading durations in elapsed days from an MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Tasks can now have arbitrary priority values. These values are mapped to/from the fixed MPP8/MPX priority values where necessary.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.24">Release 0.0.24 &#x2013; 2005-01-10</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug (again!) where deleted resource assignments in MPP9 files were still seen by MPXJ.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use class instances instead of primitives to represent some enumerated types.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to implement support for reading and writing all the basic Resource attributes found in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to implement support for reading and writing all the basic Task attributes found in MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to implement support for reading and writing all the basic Project Header attributes from MPP8 and MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Made MSPDI file parsing more robust to allow it by default to cope with non-schema-compliant XML in the same manner as MS Project. Implemented a new compatibility flag to allow this behaviour to be disabled in favour of strict parsing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Merged DateTimeSettings, CurrencySettings, and DefaultSettings into the ProjectHeader class. This change makes the project header data easier to use as it is in a single place. It also makes the entities used to describe a project consistent with the contents of the MPP and MSPDI file formats.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.23">Release 0.0.23 &#x2013; 2004-11-17</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where MPXJ was still using the default locale of the user's machine to create localised MPX files when a normal international MPX file was expected.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where the incorrect record delimiter was being used in by the MPX RelationList class.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where the method Task.getText21 was not retrieving the correct text value.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where the task unique ID values were being truncated unnecessarily.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where calendar exceptions were not testing the range of dates between the start and end date correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where the priority of a task was being escalated when converting between an MPP9 file and an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where a deadline was incorrectly being added to a task when importing data from an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where deleted resource assignments in MPP9 files were still seen by MPXJ.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where MPXFile attributes were not being correctly copied by the copy constructor.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a rounding error in MPXCalendar.getDaysInRange (contributed by Wade Golden)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to make MPXJ more robust in the face of unexpected offsets in MPP8 file format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated support for password-protected files to allow write-reserved files to be read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use the latest version of JAXB, as shipped in Sun's Java Web Services Developer Pack (JWSDP) version  1.4.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the distribution to include the redistributable files from the JWSDP JAXB implementation. Users will no longer need to download JWSDP separately in order to make use of MPXJ's MSPDI functionality.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to prevent empty notes records being added to tasks and resources when reading an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to improve accuracy when converting an MPP file to an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for blank task rows in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for blank resource rows in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for Portuguese MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support reading and writing extended attributes (apart from outline codes) for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the Resource Type attribute.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.22">Release 0.0.22 &#x2013; 2004-07-27</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where task data was not being read correctly from very large MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where certain MPP8 files were not read correctly when no constraint data is present.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where certain MPP9 files were not read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where MPP9 files containing invalid resource data were not read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where MPXJ was using the default locale of the user's machine to create localised MPX files when a normal international MPX file was expected.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where MPXJ not correctly handling embedded line breaks when reading and writing MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Removed arbitrary restrictions on the number of various entities, originally taken from the MPX specification.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MPX documentation for Task.getFixed and Task.setFixed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MPP9 file code to improve handling invalid offset values.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to remove leading and trailing spaces from MPX task field names before processing.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to detect password protected files and raise a suitable exception.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented an enhancement to improve file loading speed by an order of magnitude for files with a large number of tasks or resources (based on a contribution by Brian Leach).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented support for Maven.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Updated MpxCreate utility to allow it to create both MPX and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added new JUnit test for confidential customer data.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the resource assignment remaining work attribute for MPP8, MPP9 and MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.21">Release 0.0.21 &#x2013; 2004-05-06</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where the task start date attribute was not always correct for MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing valid tasks to be incorrectly identified as being deleted in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing an exception when reading certain MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to allow localised MPX files to be written and read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented support for German MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented generic mechanism for dealing with task field aliases.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented task field alias read/write for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented task field alias read for MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented resource field alias read/write for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented resource field alias read for MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.20">Release 0.0.20 &#x2013; 2004-03-15</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where alternative decimal delimiters and thousands separators were not being handled correctly when reading and writing MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing a null pointer exception when writing an MSPDI file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug in MSPDI files where default values were being written incorrectly for some task attributes.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug with MSPDI file date handling in non GMT time zones.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug in processing calendar data where data block is not a multiple of 12 bytes</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug processing tables where column data is null</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed checkstyle code warnings.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed Eclipse code warnings.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to include version 2.5 of the POI library.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for task calendars.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.19">Release 0.0.19 &#x2013; 2003-12-02</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug reading table data from certain MPP8 files</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated MSPDI support to use the latest version of JAXB (from JWSDP-1.3)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Re-implemented base and resource calendars as a single MPXCalendar class</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated support for base calendars and resource calendars for all file formats</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved MPXException to print details of any nested exception when a stack trace is printed.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/remove.gif" alt="Remove" title="Remove" /></td>
<td>Removed unnecessary use of ByteArray.java</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the following task fields: ActualOvertimeCost, ActualOvertimeWork, FixedCostAccrual, Hyperlink, HyperlinkAddress, HyperlinkSubAddress, LevelAssignments, LevelingCanSplit, LevelingDelay, PreleveledStart, PreleveledFinish, RemainingOvertimeCost, RemainingOvertimeWork.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.18">Release 0.0.18 &#x2013; 2003-11-13</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug with writing MS Project compatible MSPDI XML files in non-GMT timezones.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug with writing MSPDI XML files in non-GMT timezones.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing an exception when zero length calendar names were present</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing MPP8 flags to be read incorrectly. Note that flag 20 is still not read correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug with the &quot;Microsoft Project Compatible Output&quot; flag for MSPDI files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug reading task text 10.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added new MPXFile.setIgnoreTextModel() method to allow MPXJ to ignore faulty text version of task or resource model records in MPX files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Improved invalid MPX data error handling and reporting.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added BaseCalendar.getDate method to allow end dates to be calculated based on a start date and a duration of working time.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Made MPXDateFormat implement java.io.Serializable to allow MPXDate to serialize correctly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated the ant build file to allow MPXJ to be built without the components that depend on JAXB</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Rationalised setDefaultStartTime and setDefaultEndTime methods</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added MppXml utility</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for querying view information held in MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for querying table information held in MPP files. (NB This allows the user to retrieve column information, including user defined column names)</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for outlinecode1-10 fields in MPP9 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for resource &quot;available from&quot; and &quot;available to&quot; fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Verified that MPXJ will read MPP files written by Microsoft Project 2003 (they are still MPP9 files).</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.17">Release 0.0.17 &#x2013; 2003-08-05</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where a decimal point was being appended to the currency format even if no decimal digits were required.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug where special characters appearing in the currency symbol were not being quoted.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug that caused resource assignments to be incorrectly read from some MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added a new write method to MPXFile allowing the user control over the character encoding used when writing an MPX file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.16">Release 0.0.16 &#x2013; 2003-07-04</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug causing some extended boolean attributes to be read incorrectly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug causing MPP8 file task data to be read incorrectly under certain circumstances.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated calendar duration code to account for calendar exceptions.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.15">Release 0.0.15 &#x2013; 2003-06-17</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing resource assignments to be duplicated in an MPX file created programmatically.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing an incorrect duration value to be read from an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing invalid MPX files to be written in locales which don't use a period as the decimal separator.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug causing embedded quote and comma characters in task and resource notes to be handled incorrectly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added simple JUnit test to demonstrate iteration through relationships.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added an example of programmatically creating a partially complete task to the MPXCreate.java example.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added default values to the MPX project header.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for reading the RemainingDuration field from an MPP9 file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated predecessor and successor method documentation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated Task.get/set ResourceInitials and Task.get/set ResourceNames method documentation.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from resource assignment data in MPP files which were previously not imported: ActualCost, ActualWork, Cost, Finish, Start, Units, Work.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.14">Release 0.0.14 &#x2013; 2003-05-28</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from resource data in an MPP9 file which were previously not imported: Flag1-Flag20.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the method MPPFile.getFileType to allow the type of MPP file (MPP8: 98, MPP9: 2000,2002) to be determined.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated API to make classes final and constructors package access only where appropriate.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to use of 6 byte long int fields for cost and work values for MPP8.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed error in reading task fixed cost for MPP8.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from task data in an MPP8 file which were previously not imported: Contact, Cost1-Cost10, Date1-Date10, Duration1-Duration10, EffortDriven, Finish1-Finish10, Flag1-Flag20, HideBar, Milestone, Number1-Number20, Rollup, Start1-Start10, Text1-Text30, Type, WBS.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from resource data in an MPP8 file which were previously not imported: Code, Cost1-Cost10, Date1-Date10, Duration1-Duration10, EmailAddress, Finish1-Finish10, Number1-Number20, Start1-Start10, Text1-Text30</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for task and resource note fields in MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for the OvertimeCost task attribute for all file formats.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract calendar data from MPP8 files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated resource notes to fix end of line handling problem.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added functionality to read default settings and currency settings data from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.13">Release 0.0.13 &#x2013; 2003-05-22</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Implemented support for the Microsoft Project 98 file format.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed a bug that prevented task and resource note text from being read.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to remove a Java 1.4 dependency introduced in 0.0.12. Will now work with Java 1.3.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to correct handling of carriage returns embedded in note fields.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.12">Release 0.0.12 &#x2013; 2003-05-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect handling of timezones and daylight saving time.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed incorrect task structure generated from outline levels.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the notes fields from tasks and resources read from an MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the option to remove or preserve the RTF formatting from the note fields from an MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from task data in an MPP file which were previously not imported: Text11-Text30, Number6-Number20, Duration4-Duration10, Date1-Date10, Cost4-Cost10, Start6-Start10, Finish6-Finish10</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from resource data in an MPP file which were previously not imported: Text6-Text30, Start1-Start10, Finish1-Finish10, Number1-Number20, Duration1-Duration10, Date1-Date10, Cost1-Cost10</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.11">Release 0.0.11 &#x2013; 2003-04-15</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed error in format string used in one of the example files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed error where double byte characters were being read incorrectly.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed error where deleted constraints were being resurrected when read from an MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from task data in an MPP file which were previously not imported: Flag11-Flag20, Rollup, HideBar, EffortDriven.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.10">Release 0.0.10 &#x2013; 2003-04-08</h3>
<table border="0" class="table table-striped">
<tr class="b">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Corrected Actual Start and Actual End fields from MPP file.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug where time values were being broken by daylight saving time in the user's default locale.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from task data in an MPP file which were previously not imported: Actual Work, Baseline Work, Cost Variance, Deadline, Remaining Work, Work.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/update.gif" alt="Update" title="Update" /></td>
<td>Updated to extract the following fields from resource data in an MPP file which were previously not imported: Actual Cost, Actual Overtime Cost, Actual Work, Baseline Work, Cost, Cost Variance, Max Units, Overtime Cost, Overtime Rate, Overtime Work, Peak, Regular work, Remaining Cost, Remaining Overtime Cost, Remaining Work, Standard Rate, Work, Work Variance</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.9">Release 0.0.9 &#x2013; 2003-04-03</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug when handling certain types of modified MPP file where resources have been updated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added sample MPP files for bugs to the JUnit tests.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for summary flag import from MPP files.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added automatic summary flag update when creating an MPX file programmatically.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added new constructor to the MSPDIFile class to allow MSPDI files to be created from scratch.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section><section>
<h3 id="a0.0.8">Release 0.0.8 &#x2013; 2003-03-27</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Type</th>
<th>Changes</th>
<th>By</th></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added support for estimated durations.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="a">
<td><img src="images/fix.gif" alt="Fix" title="Fix" /></td>
<td>Fixed bug in handling certain types of modified MPP file where tasks have been updated.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr>
<tr class="b">
<td><img src="images/add.gif" alt="Add" title="Add" /></td>
<td>Added the facility to auto generate outline numbers.</td>
<td><a href="team-list.html#joniles">joniles</a></td></tr></table></section></section>
        </main>
      </div>
    </div>
    <hr/>
    <footer>
      <div class="container-fluid">
        <div class="row-fluid">
            <p>&#169;      2000&#x2013;2025
<a href="http://mpxj.org">MPXJ</a>
</p>
        </div>
      </div>
    </footer>
  </body>
</html>
