
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
        <link rel="canonical" href="http://www.mpxj.org/supported-formats/">
      
      
        <link rel="prev" href="../support/">
      
      
        <link rel="next" href="../howto-start-java/">
      
      
      <link rel="icon" href="../images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.0, mkdocs-material-9.5.20">
    
    
      
        <title>File Formats - MPXJ</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.66ac8b77.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce((e,_)=>(e<<5)-e+_.charCodeAt(0),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      
  


  
  

<script id="__analytics">function __md_analytics(){function n(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],n("js",new Date),n("config","G-9R48LPVHKE"),document.addEventListener("DOMContentLoaded",function(){document.forms.search&&document.forms.search.query.addEventListener("blur",function(){this.value&&n("event","search",{search_term:this.value})}),document$.subscribe(function(){var a=document.forms.feedback;if(void 0!==a)for(var e of a.querySelectorAll("[type=submit]"))e.addEventListener("click",function(e){e.preventDefault();var t=document.location.pathname,e=this.getAttribute("data-md-value");n("event","feedback",{page:t,data:e}),a.firstElementChild.disabled=!0;e=a.querySelector(".md-feedback__note [data-md-value='"+e+"']");e&&(e.hidden=!1)}),a.hidden=!1}),location$.subscribe(function(e){n("config","G-9R48LPVHKE",{page_path:e.pathname})})});var e=document.createElement("script");e.async=!0,e.src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE",document.getElementById("__analytics").insertAdjacentElement("afterEnd",e)}</script>
  
    <script>"undefined"!=typeof __md_analytics&&__md_analytics()</script>
  

    
    
    
  </head>
  
  
    <body dir="ltr">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#supported-formats" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="MPXJ" class="md-header__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MPXJ
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              File Formats
            
          </span>
        </div>
      </div>
    </div>
    
    
      <script>var media,input,key,value,palette=__md_get("__palette");if(palette&&palette.color){"(prefers-color-scheme)"===palette.color.media&&(media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']"),palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent"));for([key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12Z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="MPXJ" class="md-nav__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    MPXJ
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Introduction
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../CHANGELOG/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Changes
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../support/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Support
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    File Formats
  </span>
  

      </a>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="https://mpxj.teemill.com/collection/all-products/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Store
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Getting Started
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-java/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Java
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-dotnet/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with .Net
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-python/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Python
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-ruby/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Ruby
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPXJ Basics
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-build/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Building MPXJ
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-convert/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Converting Files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Read...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            How to Read...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-asta/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Asta files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-conceptdraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ConceptDraw PROJECT files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-openplan/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Deltek Open Plan BK3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-edraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Edraw Project EDPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-fasttrack/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FastTrack files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttdesigner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Gantt Designer files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    GanttProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-merlin/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Merlin files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd-database/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpp/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX Files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-p3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-primavera/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P6 Databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-phoenix/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Phoenix files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-plf/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PLF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectcommander/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Project Commander files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectlibre/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ProjectLibre files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-schedule-grid/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Schedule Grid files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-suretrak/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SureTrak files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-synchro/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Synchro Scheduler files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-turboproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    TurboProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Write...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            How to Write...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Use...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            How to Use...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-baselines/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Baselines
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-calendars/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Calendars
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-cpm/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    CPM Schedulers
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-external-projects/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    External Projects
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-fields/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Fields
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-universal/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Universal Project Reader
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_10" >
        
          
          <label class="md-nav__link" for="__nav_10" id="__nav_10_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Field Guides...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_10_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_10">
            <span class="md-nav__icon md-icon"></span>
            Field Guides...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Field Guide
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../mpp-field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP Field Guide
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../apidocs/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    JavaDoc
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../faq/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FAQ
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../users/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Users
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../summary.html" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Maven Reports
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


<h1 id="supported-formats">Supported Formats</h1>
<ul>
<li>
<p><strong>MPX:</strong> The MPX file format can be read by versions of Microsoft
Project up to and including Microsoft Project 2010, and written by versions of
Microsoft Project up to Microsoft Project 98. Applications other than Microsoft
Project also commonly write MPX files as a way of sharing project data. MPXJ
can read and write MPX files.
See <a href="https://support.microsoft.com/en-gb/help/270139">this Microsoft support article</a>
for a definition of the file format.</p>
</li>
<li>
<p><strong>MPP:</strong> Microsoft Project by default stores projects as MPP files.
MPXJ supports read only access to MPP files produced by Microsoft
Project from Microsoft Project 98 onwards (Microsoft Project 98, Microsoft
Project 2000, Microsoft  Project 2002, Microsoft Project 2003, Microsoft
Project 2007, Microsoft Project 2010, Microsoft Project 2013, Microsoft Project
2016, and Microsoft Project 2019). MPP template files, with the suffix MPT can
also be read by MPXJ.</p>
</li>
<li>
<p><strong>MSPDI:</strong> The MSPDI file format is Microsoft's XML file format for
sharing project data. Versions of Microsoft Project from 2002 onwards can read
and write MSPDI files.  Applications other than Microsoft Project also
commonly write MSPDI files as a way of sharing project data. 
MPXJ can read and write MSPDI files.
The MSDPI file format has remained
broadly unchanged since it was introduced, although several versions of
Microsoft Project have tweaked the file format slightly, and have their own
updated documentation.
Documentation is <a href="https://docs.microsoft.com/en-us/office-project/xml-data-interchange/project-xml-data-interchange-schema-reference">available online here</a>.
Documentation for the Project 2003 MSPDI file format can be downloaded as
part of the <a href="https://www.microsoft.com/en-us/download/details.aspx?id=101">Office 2003 XML Reference Schemas</a> package.
Documentation for the Project 2007 MSPDI file format can be downloaded as part
of the <a href="https://www.microsoft.com/en-us/download/details.aspx?id=2432">Project 2007 SDK</a>. Documentation
for the Project 2010 MSPDI file format can be downloaded as part of the
<a href="https://www.microsoft.com/en-us/download/details.aspx?id=15511">Project 2010 Reference: Software Development Kit</a>.
Documentation for the Project 2013 MSPDI file format can be downloaded as part
of the <a href="https://www.microsoft.com/en-us/download/details.aspx?id=30435">Project 2013 SDK</a>.</p>
</li>
<li>
<p><strong>MPD:</strong> The Microsoft Project MPD file format is a Microsoft Access database
used to store one or more projects. Versions of
Microsoft Project from Microsoft Project 98 to Microsoft Project 2003 can write
MPD files. Later versions of Microsoft Project can read MPD files but can't
write them. MPXJ can read MPD files natively, without using a JDBC driver, or
via a JDBC connection. MPXJ supports reading MPD files written by versions of
Microsoft Project 2000 onwards.</p>
</li>
<li>
<p><strong>PLANNER:</strong> <a href="https://wiki.gnome.org/Apps/Planner">Gnome Planner</a>
is a cross-platform Open Source project management tool which uses an XML file
format to store project data. MPXJ can read and write Planner files.</p>
</li>
<li>
<p><strong>PRIMAVERA P6:</strong> <a href="https://www.oracle.com/uk/construction-engineering/primavera-p6/">Primavera P6</a>
is an industry-leading tool favoured by users with complex planning
requirements. It can export project data in the form of XER or PMXML files,
both of which MPXJ can read and write. It is also possible for MPXJ to connect
directly to a P6 database via JDBC to read project data, or if a standalone
SQLite P6 database is being used, MPXJ can read projects from this natively
without using JDBC. The PMXML schema forms part of the P6 distribution media,
which can be downloaded from the
<a href="https://edelivery.oracle.com/">Oracle Software Delivery Cloud</a>.</p>
</li>
<li>
<p><strong>PRIMAVERA P3:</strong> Primavera P3 (Primavera Project Planner) is the forerunner
of P6. It stores each project as a directory containing Btrieve database files
which MPXJ can read from the directory itself or from a zip archive of the
directory. MPXJ can also read P3 data from PRX backup files.</p>
</li>
<li>
<p><strong>PRIMAVERA SURETRAK:</strong> Primavera SureTrak is an early iteration of the
application which eventually became Primavera P6. SureTrak stores each project
as a directory containing Btrieve database files which MPXJ can read from the
directory itself or from a zip archive of the directory. MPXJ can also read
SureTrak data from STX backup files.</p>
</li>
<li>
<p><strong>POWERPROJECT:</strong> <a href="https://elecosoft.com/products/asta/asta-powerproject/">Asta Powerproject</a>
is a planning tool used in a number of industries, particularly construction.
Powerproject saves data to PP files. MPXJ can read PP files produced by
Powerproject version 8 onwards (although earlier versions may also be
supported). Powerproject can also write one or more projects to MDB
(Microsoft Access) database files which MPXJ can read natively without a JDBC
driver, or via a JDBC connection.</p>
</li>
<li>
<p><strong>PHOENIX:</strong> <a href="https://www.phoenixcpm.com/">Phoenix Project Manager</a>
is an easy-to-use critical path method scheduling tool aimed primarily at the
construction industry. Phoenix stores projects as XML files with the file
extension PPX. MPXJ can read PPX files written by Phoenix from version 4
onwards.</p>
</li>
<li>
<p><strong>FASTTRACK:</strong> <a href="https://www.aecsoftware.com/">Fasttrack Schedule</a>
is general purpose planning tool. FastTrack stores projects as FTX files. MPXJ
can read FTX files written by Fasttrack version 10 onwards, although FTX files
written by earlier versions may be supported.</p>
</li>
<li>
<p><strong>GANTTPROJECT:</strong> <a href="https://github.com/bardsoftware/ganttproject/releases">GanttProject</a>
is an open source general purpose planning tool. GanttProject stores projects
as GAN files, which can be read by MPXJ.</p>
</li>
<li>
<p><strong>TURBOPROJECT:</strong> <a href="https://www.turbocad.com/turboproject/turboproject.html">TurboProject</a>
is general purpose planning tool. TurboProject store projects as PEP files,
which can be read by MPXJ.</p>
</li>
<li>
<p><strong>CONECPTDRAW PROJECT:</strong> <a href="https://www.conceptdraw.com/products/project-management-software">ConceptDraw PROJECT</a>
is general purpose planning tool. ConceptDraw PROJECT writes CDPX, CDPZ and
CDPTZ files which MPXJ can read.</p>
</li>
<li>
<p><strong>SYNCHRO SCHEDULER:</strong> Synchro Scheduler is general purpose planning tool
from <a href="https://www.bentley.com/">Bentley Systems</a>.
Synchro Scheduler stores projects as SP files. MPXJ can read SP files written
by Synchro Scheduler version 6 and onwards, although SP files written by earlier
versions may be supported.</p>
</li>
<li>
<p><strong>GANTT DESIGNER:</strong> Gantt Designer is a simple Gantt chart drawing tool. Gantt
Designer stores projects as GNT files, which can be read using MPXJ.</p>
</li>
<li>
<p><strong>SDEF:</strong> SDEF is the <a href="https://www.publications.usace.army.mil/Portals/76/Publications/EngineerRegulations/ER_1-1-11.pdf">Standard Data Exchange Format</a>,
as defined by the United States Army Corps of Engineers (USACE). SDEF is a fixed
column format text file, used to export a project schedule to the QCS
(Quality Control System) software from USACE. MPXJ can read and write SDEF
files.</p>
</li>
<li>
<p><strong>SCHEDULE_GRID:</strong> <a href="https://www.sage.com/en-us/products/sage-100-contractor/">Sage 100 Contractor</a>
is an application for small to medium sized companies in the construction
industry, providing accounting, and estimating and project management
functionality. Project plans managed in Sage 100 Contractor can be exported as
schedule grid files, which can be read by MPXJ.</p>
</li>
<li>
<p><strong>PROJECT COMMANDER:</strong> <a href="http://projectcommander.co.uk/pmschome/homepage.html">Project Commander</a>
is a general purpose project planning application. Project Commander stores
projects as PC files. PC files written by Project Commander version 7 and
onwards can be read by MPXJ, although PC files written by earlier versions may
be supported.</p>
</li>
<li>
<p><strong>DELTEK OPEN PLAN:</strong> <a href="https://www.deltek.com/en/project-and-portfolio-management/open-plan">Deltek Open Plan</a>
Deltek Open Plan is an enterprise project management application offering
resource management, critical path analysis, and customizable reporting.
Projects managed in Open Plan can be exported to BK3 files, which can be read
by MPXJ.</p>
</li>
<li>
<p><strong>EDRAW PROJECT:</strong> <a href="https://www.edrawsoft.com/edraw-project/">Edraw Project</a>
is an easy to use tool which allows users to rapidly create and
maintain professional looking project plans. MPXJ reads the EDPX format
written by Edraw Project.</p>
</li>
</ul>









  




                
              </article>
            </div>
          
          
  <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var tab,labels=set.querySelector(".tabbed-labels");for(tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script>

<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["content.tabs.link", "content.code.copy"], "search": "../assets/javascripts/workers/search.b8dbb3d2.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../assets/javascripts/bundle.dd8806f2.min.js"></script>
      
    
  </body>
</html>