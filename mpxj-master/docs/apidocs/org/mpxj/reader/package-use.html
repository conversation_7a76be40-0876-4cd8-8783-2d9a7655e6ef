<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.mpxj.reader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.mpxj.reader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.mpxj.reader" class="title">Uses of Package<br>org.mpxj.reader</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.asta">org.mpxj.asta</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw">org.mpxj.conceptdraw</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.edrawproject">org.mpxj.edrawproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.fasttrack">org.mpxj.fasttrack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.ganttdesigner">org.mpxj.ganttdesigner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.ganttproject">org.mpxj.ganttproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.merlin">org.mpxj.merlin</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpd">org.mpxj.mpd</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpx">org.mpxj.mpx</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.openplan">org.mpxj.openplan</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.phoenix">org.mpxj.phoenix</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.planner">org.mpxj.planner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera">org.mpxj.primavera</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera.p3">org.mpxj.primavera.p3</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.suretrak">org.mpxj.primavera.suretrak</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.projectcommander">org.mpxj.projectcommander</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.projectlibre">org.mpxj.projectlibre</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.reader">org.mpxj.reader</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.sage">org.mpxj.sage</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.sdef">org.mpxj.sdef</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.synchro">org.mpxj.synchro</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.turboproject">org.mpxj.turboproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.asta">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.asta">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.asta">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.asta">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.asta">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.conceptdraw">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.conceptdraw">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.conceptdraw">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.edrawproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.edrawproject">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.edrawproject">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.edrawproject">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.fasttrack">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/fasttrack/package-summary.html">org.mpxj.fasttrack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.fasttrack">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.fasttrack">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.fasttrack">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttdesigner">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.ganttdesigner">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.ganttdesigner">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.ganttdesigner">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.ganttproject">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.ganttproject">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.ganttproject">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.merlin">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/merlin/package-summary.html">org.mpxj.merlin</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.merlin">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.merlin">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.merlin">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpd">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.mpd">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.mpd">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.mpd">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.mpp">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.mpp">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.mpp">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpx">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.mpx">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.mpx">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.mpx">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.mspdi">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.mspdi">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.mspdi">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.openplan">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.openplan">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.openplan">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.openplan">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.phoenix">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.phoenix">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.phoenix">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.phoenix">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.planner">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.planner">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.planner">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.planner">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.primavera">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.primavera">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.primavera">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.primavera">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.p3">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.primavera.p3">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.primavera.p3">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.primavera.p3">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.primavera.p3">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.suretrak">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectFileReader.html#org.mpxj.primavera.suretrak">AbstractProjectFileReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.primavera.suretrak">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.primavera.suretrak">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.primavera.suretrak">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectcommander">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.projectcommander">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.projectcommander">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.projectcommander">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectlibre">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.projectlibre">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.projectlibre">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.projectlibre">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.reader">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.reader">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.reader">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/UniversalProjectReader.ProjectReaderProxy.html#org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a>
<div class="block">The classes implementing this interface provide access to an instance of
 the <code>ProjectReader</code> class (via the <code>getProjectReader</code> method)
 which is the class that <code>UniversalProjectReader</code> has determined
 should be used to read the file or stream you have passed it.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sage">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.sage">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.sage">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.sage">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sdef">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.sdef">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.sdef">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.sdef">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.synchro">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.synchro">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.synchro">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.synchro">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.turboproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> used by <a href="../../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectReader.html#org.mpxj.turboproject">AbstractProjectReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/AbstractProjectStreamReader.html#org.mpxj.turboproject">AbstractProjectStreamReader</a>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../org/mpxj/reader/class-use/ProjectReader.html#org.mpxj.turboproject">ProjectReader</a>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
