<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AbstractProjectFileReader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractProjectFileReader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AbstractProjectFileReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/AbstractProjectFileReader.html" target="_top">Frames</a></li>
<li><a href="AbstractProjectFileReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.reader</div>
<h2 title="Class AbstractProjectFileReader" class="title">Class AbstractProjectFileReader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">org.mpxj.reader.AbstractProjectReader</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.reader.AbstractProjectFileReader</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/mpxj/asta/AstaJdbcReader.html" title="class in org.mpxj.asta">AstaJdbcReader</a>, <a href="../../../org/mpxj/asta/AstaMdbReader.html" title="class in org.mpxj.asta">AstaMdbReader</a>, <a href="../../../org/mpxj/asta/AstaSqliteReader.html" title="class in org.mpxj.asta">AstaSqliteReader</a>, <a href="../../../org/mpxj/fasttrack/FastTrackReader.html" title="class in org.mpxj.fasttrack">FastTrackReader</a>, <a href="../../../org/mpxj/merlin/MerlinReader.html" title="class in org.mpxj.merlin">MerlinReader</a>, <a href="../../../org/mpxj/mpd/MPDDatabaseReader.html" title="class in org.mpxj.mpd">MPDDatabaseReader</a>, <a href="../../../org/mpxj/mpd/MPDFileReader.html" title="class in org.mpxj.mpd">MPDFileReader</a>, <a href="../../../org/mpxj/primavera/p3/P3DatabaseReader.html" title="class in org.mpxj.primavera.p3">P3DatabaseReader</a>, <a href="../../../org/mpxj/primavera/PrimaveraDatabaseFileReader.html" title="class in org.mpxj.primavera">PrimaveraDatabaseFileReader</a>, <a href="../../../org/mpxj/primavera/suretrak/SureTrakDatabaseReader.html" title="class in org.mpxj.primavera.suretrak">SureTrakDatabaseReader</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">AbstractProjectFileReader</span>
extends <a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">AbstractProjectReader</a></pre>
<div class="block">Abstract implementation of the ProjectReader interface
 for readers which consume a file.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#AbstractProjectFileReader--">AbstractProjectFileReader</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Read a single schedule from a file where the contents of the project file
 are supplied via an input stream.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Read a single schedule from a file where the file name is supplied.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Default implementation of readAll.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.reader.AbstractProjectReader">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.reader.<a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">AbstractProjectReader</a></h3>
<code><a href="../../../org/mpxj/reader/AbstractProjectReader.html#addListenersToProject-org.mpxj.ProjectFile-">addListenersToProject</a>, <a href="../../../org/mpxj/reader/AbstractProjectReader.html#addListenersToReader-org.mpxj.reader.ProjectReader-">addListenersToReader</a>, <a href="../../../org/mpxj/reader/AbstractProjectReader.html#addProjectListener-org.mpxj.listener.ProjectListener-">addProjectListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.reader.ProjectReader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.mpxj.reader.<a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a></h3>
<code><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.File-">read</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractProjectFileReader--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractProjectFileReader</h4>
<pre>public&nbsp;AbstractProjectFileReader()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                 throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.lang.String-">ProjectReader</a></code></span></div>
<div class="block">Read a single schedule from a file where the file name is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - file name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAll</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - file name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="read-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)
                 throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.InputStream-">ProjectReader</a></code></span></div>
<div class="block">Read a single schedule from a file where the contents of the project file
 are supplied via an input stream.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputStream</code> - InputStream instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAll</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)
                          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputStream</code> - InputStream instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.io.File-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readAll</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Default implementation of readAll. Reads a single project,
 if successful, returns a list with a single entry. If unsuccessful,
 returns an empty list.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of projects</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AbstractProjectFileReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/AbstractProjectFileReader.html" target="_top">Frames</a></li>
<li><a href="AbstractProjectFileReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
