<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ResourceAssignment (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResourceAssignment (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAssignment.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/Resource.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ResourceAssignmentCode.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ResourceAssignment.html" target="_top">Frames</a></li>
<li><a href="ResourceAssignment.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class ResourceAssignment" class="title">Class ResourceAssignment</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">org.mpxj.AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.ResourceAssignment</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>, <a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a>, <a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a>, <a href="../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ResourceAssignment</span>
extends <a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;
implements <a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a>, <a href="../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></pre>
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#DEFAULT_UNITS">DEFAULT_UNITS</a></span></code>
<div class="block">Default units value: 100%.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#ResourceAssignment-org.mpxj.ProjectFile-org.mpxj.Task-">ResourceAssignment</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                  <a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#addResourceAssignmentCodeValue-org.mpxj.ResourceAssignmentCodeValue-">addResourceAssignmentCodeValue</a></span>(<a href="../../org/mpxj/ResourceAssignmentCodeValue.html" title="class in org.mpxj">ResourceAssignmentCodeValue</a>&nbsp;value)</code>
<div class="block">Assign a resource assignment code value to this resource assignment.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceAssignmentWorkgroupFields.html" title="class in org.mpxj">ResourceAssignmentWorkgroupFields</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#addWorkgroupAssignment--">addWorkgroupAssignment</a></span>()</code>
<div class="block">This method allows a resource assignment workgroup fields record
 to be added to the current resource assignment.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Returns the actual cost for this resource assignment.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualFinish--">getActualFinish</a></span>()</code>
<div class="block">Retrieve the actual finish date.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Returns the actual overtime cost of this resource assignment.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualOvertimeWork--">getActualOvertimeWork</a></span>()</code>
<div class="block">Returns the actual overtime work of this resource assignment.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualStart--">getActualStart</a></span>()</code>
<div class="block">Retrieve the actual start date.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getActualWork--">getActualWork</a></span>()</code>
<div class="block">Returns the actual completed work of this resource assignment.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getACWP--">getACWP</a></span>()</code>
<div class="block">Retrieve the ACWP value.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineBudgetCost--">getBaselineBudgetCost</a></span>()</code>
<div class="block">Retrieves the baseline budget cost.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineBudgetCost-int-">getBaselineBudgetCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineBudgetWork--">getBaselineBudgetWork</a></span>()</code>
<div class="block">Retrieves the baseline budget work value.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineBudgetWork-int-">getBaselineBudgetWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">Returns the planned cost for this resource assignment.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineCost-int-">getBaselineCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineFinish--">getBaselineFinish</a></span>()</code>
<div class="block">Retrieve the baseline finish date.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineFinish-int-">getBaselineFinish</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineStart--">getBaselineStart</a></span>()</code>
<div class="block">Retrieve the baseline start date.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineStart-int-">getBaselineStart</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineWork--">getBaselineWork</a></span>()</code>
<div class="block">Returns the baseline work of this resource assignment.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBaselineWork-int-">getBaselineWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBCWP--">getBCWP</a></span>()</code>
<div class="block">The BCWP (budgeted cost of work performed) field contains
 the cumulative value of the assignment's timephased percent complete
 multiplied by the assignment's timephased baseline cost.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBCWS--">getBCWS</a></span>()</code>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBudgetCost--">getBudgetCost</a></span>()</code>
<div class="block">Retrieves the budget cost.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getBudgetWork--">getBudgetWork</a></span>()</code>
<div class="block">Retrieves the budget work value.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCalculateCostsFromUnits--">getCalculateCostsFromUnits</a></span>()</code>
<div class="block">Retrieve the calculate costs from units flag.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getConfirmed--">getConfirmed</a></span>()</code>
<div class="block">The Confirmed field indicates whether all resources assigned to a task
 have accepted or rejected the task assignment in response to a TeamAssign
 message regarding their assignments.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCost--">getCost</a></span>()</code>
<div class="block">Returns the cost  of this resource assignment.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCost-int-">getCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a cost value.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/CostAccount.html" title="class in org.mpxj">CostAccount</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCostAccount--">getCostAccount</a></span>()</code>
<div class="block">Retrieve the cost account for this resource assignment.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCostAccountUniqueID--">getCostAccountUniqueID</a></span>()</code>
<div class="block">Retrieve the cost account unique ID for this resource assignment.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCostRateTable--">getCostRateTable</a></span>()</code>
<div class="block">Returns the cost rate table for this assignment.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCostRateTableIndex--">getCostRateTableIndex</a></span>()</code>
<div class="block">Returns the cost rate table index for this assignment.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCostVariance--">getCostVariance</a></span>()</code>
<div class="block">The Cost Variance field shows the difference between the baseline cost
 and total cost for a task.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCreateDate--">getCreateDate</a></span>()</code>
<div class="block">The Created field contains the date and time when a task was added
 to the project.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getCV--">getCV</a></span>()</code>
<div class="block">The CV (earned value cost variance) field shows the difference between
 how much it should have cost to achieve the current level of completion
 on the task, and how much it has actually cost to achieve the current
 level of completion up to the status date or today's date.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getDate-int-">getDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a date value.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getDelay--">getDelay</a></span>()</code>
<div class="block">Returns the delay for this resource assignment.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getDuration-int-">getDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a duration value.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEffectiveCalendar--">getEffectiveCalendar</a></span>()</code>
<div class="block">Retrieves the effective calendar used for this resource assignment.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEffectiveRate-java.time.LocalDateTime-">getEffectiveRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Based on the configuration data for this resource assignment,
 return the cost rate effective on the supplied date.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseCost-int-">getEnterpriseCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise cost value.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseDate-int-">getEnterpriseDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise date value.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseDuration-int-">getEnterpriseDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise duration value.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseFlag-int-">getEnterpriseFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise flag value.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseNumber-int-">getEnterpriseNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise number value.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getEnterpriseText-int-">getEnterpriseText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise text value.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getFieldByAlias-java.lang.String-">getFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the value of a field using its alias.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getFinish--">getFinish</a></span>()</code>
<div class="block">Returns the finish date for this resource assignment.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getFinish-int-">getFinish</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a finish value.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getFinishVariance--">getFinishVariance</a></span>()</code>
<div class="block">Calculate the finish variance.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getFlag-int-">getFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a flag value.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Retrieve the task GUID.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getHasTimephasedData--">getHasTimephasedData</a></span>()</code>
<div class="block">Retrieve a flag indicating if this resource assignment has timephased
 data associated with it.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getHyperlink--">getHyperlink</a></span>()</code>
<div class="block">Retrieves the task hyperlink attribute.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getHyperlinkAddress--">getHyperlinkAddress</a></span>()</code>
<div class="block">Retrieves the task hyperlink address attribute.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getHyperlinkScreenTip--">getHyperlinkScreenTip</a></span>()</code>
<div class="block">Retrieves the hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getHyperlinkSubAddress--">getHyperlinkSubAddress</a></span>()</code>
<div class="block">Retrieves the task hyperlink sub-address attribute.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getLevelingDelay--">getLevelingDelay</a></span>()</code>
<div class="block">Retrieves the leveling delay for this resource assignment.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getLinkedFields--">getLinkedFields</a></span>()</code>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project file,
 or from another program.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Retrieve the plain text representation of the assignment notes.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getNotesObject--">getNotesObject</a></span>()</code>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getNumber-int-">getNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a number value.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getOverrideRate--">getOverrideRate</a></span>()</code>
<div class="block">Retrieve the rate to use in place of the value from the cost rate table.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getOvertimeCost--">getOvertimeCost</a></span>()</code>
<div class="block">Returns the overtime cost of this resource assignment.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getOvertimeWork--">getOvertimeWork</a></span>()</code>
<div class="block">Returns the overtime work done of this resource assignment.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getParentFile--">getParentFile</a></span>()</code>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getPercentageWorkComplete--">getPercentageWorkComplete</a></span>()</code>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the percentage of the task's work that has been completed.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getPlannedCost--">getPlannedCost</a></span>()</code>
<div class="block">Retrieve the planned cost field.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getPlannedFinish--">getPlannedFinish</a></span>()</code>
<div class="block">Retrieve the planned finish value.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getPlannedStart--">getPlannedStart</a></span>()</code>
<div class="block">Set the planned start field.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getPlannedWork--">getPlannedWork</a></span>()</code>
<div class="block">Retrieve the planned work field.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRateIndex--">getRateIndex</a></span>()</code>
<div class="block">Retrieve the index of the rate in the cost rate table used
 to calculate the cost for this resource assignment.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/RateSource.html" title="enum in org.mpxj">RateSource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRateSource--">getRateSource</a></span>()</code>
<div class="block">Retrieve the source of the cost rate to be used for this resource assignment.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRegularWork--">getRegularWork</a></span>()</code>
<div class="block">Returns the regular work of this resource assignment.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Returns the remaining cost of this resource assignment.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingEarlyFinish--">getRemainingEarlyFinish</a></span>()</code>
<div class="block">Retrieve the remaining early finish value.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingEarlyStart--">getRemainingEarlyStart</a></span>()</code>
<div class="block">Retrieve the remaining early start value.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingLateFinish--">getRemainingLateFinish</a></span>()</code>
<div class="block">Retrieve the remaining late finish value.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingLateStart--">getRemainingLateStart</a></span>()</code>
<div class="block">Retrieve the remaining late start value.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingOvertimeCost--">getRemainingOvertimeCost</a></span>()</code>
<div class="block">Returns the remaining overtime cost of this resource assignment.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingOvertimeWork--">getRemainingOvertimeWork</a></span>()</code>
<div class="block">Returns the remaining overtime work of this resource assignment.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingUnits--">getRemainingUnits</a></span>()</code>
<div class="block">Returns the remaining units of this resource assignment.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRemainingWork--">getRemainingWork</a></span>()</code>
<div class="block">Returns the remaining work for this resource assignment.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResource--">getResource</a></span>()</code>
<div class="block">This method retrieves a reference to the resource with which this
 assignment is associated.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ResourceAssignmentCode.html" title="class in org.mpxj">ResourceAssignmentCode</a>,<a href="../../org/mpxj/ResourceAssignmentCodeValue.html" title="class in org.mpxj">ResourceAssignmentCodeValue</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResourceAssignmentCodeValues--">getResourceAssignmentCodeValues</a></span>()</code>
<div class="block">Retrieve the resource assignment code values associated with this resource assignment.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResourceRequestType--">getResourceRequestType</a></span>()</code>
<div class="block">Retrieves the resource request type attribute.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResourceUniqueID--">getResourceUniqueID</a></span>()</code>
<div class="block">Returns the resources unique id for this resource assignment.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResponsePending--">getResponsePending</a></span>()</code>
<div class="block">Retrieves a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getResume--">getResume</a></span>()</code>
<div class="block">Retrieve the resume date.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRole--">getRole</a></span>()</code>
<div class="block">Retrieve the role in which this resource assignment is being performed.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getRoleUniqueID--">getRoleUniqueID</a></span>()</code>
<div class="block">Retrieve the role unique ID in which this resource assignment is being performed.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getStart--">getStart</a></span>()</code>
<div class="block">Returns the start of this resource assignment.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getStart-int-">getStart</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a start value.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getStartVariance--">getStartVariance</a></span>()</code>
<div class="block">Calculate the start variance.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getStop--">getStop</a></span>()</code>
<div class="block">Retrieve the stop date.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getSV--">getSV</a></span>()</code>
<div class="block">The SV (earned value schedule variance) field shows the difference in
 cost terms between the current progress and the baseline plan of the
 task up to the status date or today's date.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTask--">getTask</a></span>()</code>
<div class="block">This method retrieves a reference to the task with which this
 assignment is associated.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTaskUniqueID--">getTaskUniqueID</a></span>()</code>
<div class="block">Retrieve the parent task unique ID.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTeamStatusPending--">getTeamStatusPending</a></span>()</code>
<div class="block">Retrieves a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getText-int-">getText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a text value.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedActualCost--">getTimephasedActualCost</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of actual cost.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedActualOvertimeWork--">getTimephasedActualOvertimeWork</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of the actual overtime work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedActualWork--">getTimephasedActualWork</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of the completed work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedBaselineCost-int-">getTimephasedBaselineCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve timephased baseline cost.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedBaselineWork-int-">getTimephasedBaselineWork</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve timephased baseline work.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedCost--">getTimephasedCost</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of cost.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedOvertimeWork--">getTimephasedOvertimeWork</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of the planned overtime work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedPlannedWork--">getTimephasedPlannedWork</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getTimephasedWork--">getTimephasedWork</a></span>()</code>
<div class="block">Retrieves the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getUniqueID--">getUniqueID</a></span>()</code>
<div class="block">Retrieve the unique ID of this resource assignment.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getUnits--">getUnits</a></span>()</code>
<div class="block">Returns the units of this resource assignment.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getUpdateNeeded--">getUpdateNeeded</a></span>()</code>
<div class="block">The Update Needed field indicates whether a TeamUpdate message
 should be sent to the assigned resources because of changes to the
 start date, finish date, or resource reassignments of the task.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getVAC--">getVAC</a></span>()</code>
<div class="block">Returns the VAC for this resource assignment.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getVariableRateUnits--">getVariableRateUnits</a></span>()</code>
<div class="block">Retrieve the variable rate time units, null if fixed rate.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getWork--">getWork</a></span>()</code>
<div class="block">Returns the work of this resource assignment.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getWorkContour--">getWorkContour</a></span>()</code>
<div class="block">This method returns the Work Contour type of this Assignment.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceAssignmentWorkgroupFields.html" title="class in org.mpxj">ResourceAssignmentWorkgroupFields</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getWorkgroupAssignment--">getWorkgroupAssignment</a></span>()</code>
<div class="block">Gets the Resource Assignment Workgroup Fields if one exists.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#getWorkVariance--">getWorkVariance</a></span>()</code>
<div class="block">The Work Variance field contains the difference between a task's
 baseline work and the currently scheduled work.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">handleFieldChange</a></span>(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</code>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#remove--">remove</a></span>()</code>
<div class="block">Removes this resource assignment from the project.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualCost-java.lang.Number-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</code>
<div class="block">Sets the actual cost so far incurred for this resource assignment.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualFinish-java.time.LocalDateTime-">setActualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finish)</code>
<div class="block">Set the actual finish date.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualOvertimeCost-java.lang.Number-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the actual overtime cost for this resource assignment.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualOvertimeWork-org.mpxj.Duration-">setActualOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Sets the actual overtime work for this resource assignment.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualStart-java.time.LocalDateTime-">setActualStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;start)</code>
<div class="block">Set the actual start date.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setActualWork-org.mpxj.Duration-">setActualWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">Sets the actual completed work for this resource assignment.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setACWP-java.lang.Number-">setACWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</code>
<div class="block">Set the ACWP value.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineBudgetCost-int-java.lang.Number-">setBaselineBudgetCost</a></span>(int&nbsp;baselineNumber,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineBudgetCost-java.lang.Number-">setBaselineBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the baseline budget cost.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineBudgetWork-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the baseline budget work value.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineBudgetWork-int-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(int&nbsp;baselineNumber,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineCost-int-java.lang.Number-">setBaselineCost</a></span>(int&nbsp;baselineNumber,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineCost-java.lang.Number-">setBaselineCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">Sets the planned cost for this resource assignment.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineFinish-int-java.time.LocalDateTime-">setBaselineFinish</a></span>(int&nbsp;baselineNumber,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineFinish-java.time.LocalDateTime-">setBaselineFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finish)</code>
<div class="block">Set the baseline finish date.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineStart-int-java.time.LocalDateTime-">setBaselineStart</a></span>(int&nbsp;baselineNumber,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineStart-java.time.LocalDateTime-">setBaselineStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;start)</code>
<div class="block">Set the baseline start date.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineWork-org.mpxj.Duration-">setBaselineWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">Sets the baseline work for this resource assignment.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBaselineWork-int-org.mpxj.Duration-">setBaselineWork</a></span>(int&nbsp;baselineNumber,
               <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBCWP-java.lang.Number-">setBCWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The BCWP (budgeted cost of work performed) field contains the
 cumulative value
 of the assignment's timephased percent complete multiplied by
 the assignments
 timephased baseline cost.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBCWS-java.lang.Number-">setBCWS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBudgetCost-java.lang.Number-">setBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the budget cost.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setBudgetWork-org.mpxj.Duration-">setBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the budget work value.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCalculateCostsFromUnits-boolean-">setCalculateCostsFromUnits</a></span>(boolean&nbsp;calculateCostsFromUnits)</code>
<div class="block">Set the calculate costs from units flag.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setConfirmed-boolean-">setConfirmed</a></span>(boolean&nbsp;val)</code>
<div class="block">The Confirmed field indicates whether all resources assigned to a task have
 accepted or rejected the task assignment in response to a TeamAssign message
 regarding their assignments.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCost-int-java.lang.Number-">setCost</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a cost value.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCost-java.lang.Number-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the cost for this resource assignment.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCostAccount-org.mpxj.CostAccount-">setCostAccount</a></span>(<a href="../../org/mpxj/CostAccount.html" title="class in org.mpxj">CostAccount</a>&nbsp;costAccount)</code>
<div class="block">Set the cost account for this resource assignment.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCostAccountUniqueID-java.lang.Integer-">setCostAccountUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the cost account unique ID for this resource assignment.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCostRateTableIndex-int-">setCostRateTableIndex</a></span>(int&nbsp;index)</code>
<div class="block">Sets the index of the cost rate table for this assignment.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCostVariance-java.lang.Number-">setCostVariance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Cost Variance field shows the difference between the
 baseline cost and total cost for a task.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCreateDate-java.time.LocalDateTime-">setCreateDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Created field contains the date and time when a task was
 added to the project.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setCV-java.lang.Number-">setCV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The CV (earned value cost variance) field shows the difference
 between how much it should have cost to achieve the current level of
 completion on the task, and how much it has actually cost to achieve the
 current level of completion up to the status date or today's date.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setDate-int-java.time.LocalDateTime-">setDate</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a date value.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setDelay-org.mpxj.Duration-">setDelay</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Sets the delay for this resource assignment.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setDuration-int-org.mpxj.Duration-">setDuration</a></span>(int&nbsp;index,
           <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a duration value.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseCost-int-java.lang.Number-">setEnterpriseCost</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise cost value.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseDate-int-java.time.LocalDateTime-">setEnterpriseDate</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set an enterprise date value.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseDuration-int-org.mpxj.Duration-">setEnterpriseDuration</a></span>(int&nbsp;index,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set an enterprise duration value.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseFlag-int-boolean-">setEnterpriseFlag</a></span>(int&nbsp;index,
                 boolean&nbsp;value)</code>
<div class="block">Set an enterprise flag value.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseNumber-int-java.lang.Number-">setEnterpriseNumber</a></span>(int&nbsp;index,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise number value.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setEnterpriseText-int-java.lang.String-">setEnterpriseText</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set an enterprise text value.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setFieldByAlias-java.lang.String-java.lang.Object-">setFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Set the value of a field using its alias.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setFinish-int-java.time.LocalDateTime-">setFinish</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a finish value.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setFinish-java.time.LocalDateTime-">setFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">Sets the finish date for this resource assignment.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setFinishVariance-org.mpxj.Duration-">setFinishVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">The Finish Variance field contains the amount of time that represents the
 difference between a task's baseline finish date and its forecast
 or actual finish date.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setFlag-int-boolean-">setFlag</a></span>(int&nbsp;index,
       boolean&nbsp;value)</code>
<div class="block">Set a flag value.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Set the task GUID.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setHyperlink-java.lang.String-">setHyperlink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink attribute.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setHyperlinkAddress-java.lang.String-">setHyperlinkAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink address attribute.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setHyperlinkScreenTip-java.lang.String-">setHyperlinkScreenTip</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setHyperlinkSubAddress-java.lang.String-">setHyperlinkSubAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink sub address attribute.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setLevelingDelay-org.mpxj.Duration-">setLevelingDelay</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;levelingDelay)</code>
<div class="block">Sets the leveling delay for this resource assignment.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setLinkedFields-boolean-">setLinkedFields</a></span>(boolean&nbsp;flag)</code>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project
 file, or from another program.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</code>
<div class="block">This method is used to add notes to the current task.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setNotesObject-org.mpxj.Notes-">setNotesObject</a></span>(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</code>
<div class="block">Set the Notes instance representing the assignment notes.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setNumber-int-java.lang.Number-">setNumber</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a number value.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setOverrideRate-org.mpxj.Rate-">setOverrideRate</a></span>(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</code>
<div class="block">Set the rate to use in place of the value from the cost rate table.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setOvertimeCost-java.lang.Number-">setOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the overtime cost for this resource assignment.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setOvertimeWork-org.mpxj.Duration-">setOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;overtimeWork)</code>
<div class="block">Sets the overtime work for this resource assignment.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setPercentageWorkComplete-java.lang.Number-">setPercentageWorkComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the
 percentage of the task's work that has been completed.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setPlannedCost-java.lang.Number-">setPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the planned cost field.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setPlannedFinish-java.time.LocalDateTime-">setPlannedFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the planned finish value.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setPlannedStart-java.time.LocalDateTime-">setPlannedStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Retrieve the planned start field.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setPlannedWork-org.mpxj.Duration-">setPlannedWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the planned work field.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRateIndex-java.lang.Integer-">setRateIndex</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;index)</code>
<div class="block">Set the index of the rate in the cost rate table used
 to calculate the cost for this resource assignment.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRateSource-org.mpxj.RateSource-">setRateSource</a></span>(<a href="../../org/mpxj/RateSource.html" title="enum in org.mpxj">RateSource</a>&nbsp;source)</code>
<div class="block">Set the source of the cost rate to be used for this resource assignment.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRegularWork-org.mpxj.Duration-">setRegularWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Sets the regular work for this resource assignment.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingCost-java.lang.Number-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the remaining cost for this resource assignment.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingEarlyFinish-java.time.LocalDateTime-">setRemainingEarlyFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining early finish value.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingEarlyStart-java.time.LocalDateTime-">setRemainingEarlyStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining early start value.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingLateFinish-java.time.LocalDateTime-">setRemainingLateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining late finish value.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingLateStart-java.time.LocalDateTime-">setRemainingLateStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining late start value.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingOvertimeCost-java.lang.Number-">setRemainingOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the remaining overtime cost for this resource assignment.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingOvertimeWork-org.mpxj.Duration-">setRemainingOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Sets the remaining overtime work for this resource assignment.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingUnits-java.lang.Number-">setRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">Sets the remaining units for this resource assignment.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRemainingWork-org.mpxj.Duration-">setRemainingWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;remainingWork)</code>
<div class="block">Sets the remaining work for this resource assignment.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setResourceRequestType-org.mpxj.ResourceRequestType-">setResourceRequestType</a></span>(<a href="../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a>&nbsp;type)</code>
<div class="block">Sets the resource request type attribute.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setResourceUniqueID-java.lang.Integer-">setResourceUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">Sets the resources unique id for this resource assignment.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setResponsePending-boolean-">setResponsePending</a></span>(boolean&nbsp;val)</code>
<div class="block">Sets a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setResume-java.time.LocalDateTime-">setResume</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;resume)</code>
<div class="block">Set the resume date.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRole-org.mpxj.Resource-">setRole</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</code>
<div class="block">Set the role in which this resource assignment is being performed.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setRoleUniqueID-java.lang.Integer-">setRoleUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the unique ID of the role in which this resource assignment is being performed.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setStart-int-java.time.LocalDateTime-">setStart</a></span>(int&nbsp;index,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a start value.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setStart-java.time.LocalDateTime-">setStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">Sets the start date for this resource assignment.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setStartVariance-org.mpxj.Duration-">setStartVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Start Variance field contains the amount of time that represents the
 difference between a task's baseline start date and its currently
 scheduled start date.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setStop-java.time.LocalDateTime-">setStop</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;stop)</code>
<div class="block">Set the stop date.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setSV-java.lang.Number-">setSV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The SV (earned value schedule variance) field shows the difference
 in cost terms between the current progress and the baseline plan
 of the task up to the status date or today's date.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTaskUniqueID-java.lang.Integer-">setTaskUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the parent task unique ID.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTeamStatusPending-boolean-">setTeamStatusPending</a></span>(boolean&nbsp;val)</code>
<div class="block">Sets a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setText-int-java.lang.String-">setText</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set a text value.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedActualOvertimeWork-java.util.List-">setTimephasedActualOvertimeWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the actual overtime work
 for this assignment.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedActualOvertimeWork-org.mpxj.TimephasedWorkContainer-">setTimephasedActualOvertimeWork</a></span>(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the actual overtime work
 for this assignment.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedActualWork-java.util.List-">setTimephasedActualWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the completed work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedActualWork-org.mpxj.TimephasedWorkContainer-">setTimephasedActualWork</a></span>(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the completed work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedBaselineCost-int-java.util.List-">setTimephasedBaselineCost</a></span>(int&nbsp;index,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;data)</code>
<div class="block">Set timephased baseline cost.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedBaselineCost-int-org.mpxj.TimephasedCostContainer-">setTimephasedBaselineCost</a></span>(int&nbsp;index,
                         <a href="../../org/mpxj/TimephasedCostContainer.html" title="interface in org.mpxj">TimephasedCostContainer</a>&nbsp;data)</code>
<div class="block">Set timephased baseline cost.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedBaselineWork-int-java.util.List-">setTimephasedBaselineWork</a></span>(int&nbsp;index,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</code>
<div class="block">Set timephased baseline work.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedBaselineWork-int-org.mpxj.TimephasedWorkContainer-">setTimephasedBaselineWork</a></span>(int&nbsp;index,
                         <a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</code>
<div class="block">Set timephased baseline work.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedPlannedWork-java.util.List-">setTimephasedPlannedWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedPlannedWork-org.mpxj.TimephasedWorkContainer-">setTimephasedPlannedWork</a></span>(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedWork-java.util.List-">setTimephasedWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setTimephasedWork-org.mpxj.TimephasedWorkContainer-">setTimephasedWork</a></span>(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</code>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setUniqueID-java.lang.Integer-">setUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Set the unique ID of this resource assignment.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setUnits-java.lang.Number-">setUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">Sets the units for this resource assignment.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setUpdateNeeded-boolean-">setUpdateNeeded</a></span>(boolean&nbsp;val)</code>
<div class="block">The Update Needed field indicates whether a TeamUpdate message should
 be sent to the assigned resources because of changes to the start date,
 finish date, or resource reassignments of the task.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setVAC-java.lang.Number-">setVAC</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Sets VAC for this resource assignment.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setVariableRateUnits-org.mpxj.TimeUnit-">setVariableRateUnits</a></span>(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;variableRateUnits)</code>
<div class="block">Set the variable rate time units, null if fixed rate.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setWork-org.mpxj.Duration-">setWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Sets the work for this resource assignment.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setWorkContour-org.mpxj.WorkContour-">setWorkContour</a></span>(<a href="../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;workContour)</code>
<div class="block">This method sets the Work Contour type of this Assignment.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#setWorkVariance-org.mpxj.Duration-">setWorkVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Work Variance field contains the difference between a task's baseline
 work and the currently scheduled work.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ResourceAssignment.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.AbstractFieldContainer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a></h3>
<code><a href="../../org/mpxj/AbstractFieldContainer.html#addFieldListener-org.mpxj.listener.FieldListener-">addFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#disableEvents--">disableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#enableEvents--">enableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#get-org.mpxj.FieldType-">get</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#removeFieldListener-org.mpxj.listener.FieldListener-">removeFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_UNITS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_UNITS</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> DEFAULT_UNITS</pre>
<div class="block">Default units value: 100%.</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResourceAssignment-org.mpxj.ProjectFile-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResourceAssignment</h4>
<pre>public&nbsp;ResourceAssignment(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                          <a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">Constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - The parent file to which this record belongs.</dd>
<dd><code>task</code> - The task to which this assignment is being made</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addWorkgroupAssignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWorkgroupAssignment</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceAssignmentWorkgroupFields.html" title="class in org.mpxj">ResourceAssignmentWorkgroupFields</a>&nbsp;addWorkgroupAssignment()
                                                         throws <a href="../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">This method allows a resource assignment workgroup fields record
 to be added to the current resource assignment. A maximum of
 one of these records can be added to a resource assignment record.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ResourceAssignmentWorkgroupFields object</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code> - if MSP defined limit of 1 is exceeded</dd>
</dl>
</li>
</ul>
<a name="getUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUniqueID()</pre>
<div class="block">Retrieve the unique ID of this resource assignment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html#getUniqueID--">getUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource assignment unique ID</dd>
</dl>
</li>
</ul>
<a name="setUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUniqueID</h4>
<pre>public&nbsp;void&nbsp;setUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Set the unique ID of this resource assignment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html#setUniqueID-java.lang.Integer-">setUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - resource assignment unique ID</dd>
</dl>
</li>
</ul>
<a name="getUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getUnits()</pre>
<div class="block">Returns the units of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>units</dd>
</dl>
</li>
</ul>
<a name="setUnits-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnits</h4>
<pre>public&nbsp;void&nbsp;setUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">Sets the units for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - units</dd>
</dl>
</li>
</ul>
<a name="getRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingUnits()</pre>
<div class="block">Returns the remaining units of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining units</dd>
</dl>
</li>
</ul>
<a name="setRemainingUnits-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">Sets the remaining units for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - remaining units</dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWork()</pre>
<div class="block">Returns the work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work</dd>
</dl>
</li>
</ul>
<a name="setWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Sets the work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - work</dd>
</dl>
</li>
</ul>
<a name="getBaselineStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineStart()</pre>
<div class="block">Retrieve the baseline start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline start date</dd>
</dl>
</li>
</ul>
<a name="setBaselineStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;start)</pre>
<div class="block">Set the baseline start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>start</code> - baseline start date</dd>
</dl>
</li>
</ul>
<a name="getActualStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualStart()</pre>
<div class="block">Retrieve the actual start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual start date</dd>
</dl>
</li>
</ul>
<a name="setActualStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualStart</h4>
<pre>public&nbsp;void&nbsp;setActualStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;start)</pre>
<div class="block">Set the actual start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>start</code> - actual start date</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineFinish()</pre>
<div class="block">Retrieve the baseline finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline finish date</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finish)</pre>
<div class="block">Set the baseline finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>finish</code> - baseline finish</dd>
</dl>
</li>
</ul>
<a name="getActualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualFinish()</pre>
<div class="block">Retrieve the actual finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual finish date</dd>
</dl>
</li>
</ul>
<a name="setActualFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualFinish</h4>
<pre>public&nbsp;void&nbsp;setActualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finish)</pre>
<div class="block">Set the actual finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>finish</code> - actual finish</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork()</pre>
<div class="block">Returns the baseline work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned work</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">Sets the baseline work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - planned work</dd>
</dl>
</li>
</ul>
<a name="getActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWork()</pre>
<div class="block">Returns the actual completed work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>completed work</dd>
</dl>
</li>
</ul>
<a name="setActualWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWork</h4>
<pre>public&nbsp;void&nbsp;setActualWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">Sets the actual completed work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - actual completed work</dd>
</dl>
</li>
</ul>
<a name="getOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getOvertimeWork()</pre>
<div class="block">Returns the overtime work done of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overtime work</dd>
</dl>
</li>
</ul>
<a name="setOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;overtimeWork)</pre>
<div class="block">Sets the overtime work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overtimeWork</code> - overtime work</dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost()</pre>
<div class="block">Returns the cost  of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost</dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost()</pre>
<div class="block">Returns the planned cost for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned cost</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">Sets the planned cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - planned cost</dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualCost()</pre>
<div class="block">Returns the actual cost for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual cost</dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</pre>
<div class="block">Sets the actual cost so far incurred for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualCost</code> - actual cost</dd>
</dl>
</li>
</ul>
<a name="getStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart()</pre>
<div class="block">Returns the start of this resource assignment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimePeriodEntity.html#getStart--">getStart</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start date</dd>
</dl>
</li>
</ul>
<a name="setStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">Sets the start date for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - start date</dd>
</dl>
</li>
</ul>
<a name="getFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish()</pre>
<div class="block">Returns the finish date for this resource assignment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimePeriodEntity.html#getFinish--">getFinish</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish date</dd>
</dl>
</li>
</ul>
<a name="setFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">Sets the finish date for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - finish date</dd>
</dl>
</li>
</ul>
<a name="getDelay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDelay</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDelay()</pre>
<div class="block">Returns the delay for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>delay</dd>
</dl>
</li>
</ul>
<a name="setDelay-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDelay</h4>
<pre>public&nbsp;void&nbsp;setDelay(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Sets the delay for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - delay</dd>
</dl>
</li>
</ul>
<a name="getResourceUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getResourceUniqueID()</pre>
<div class="block">Returns the resources unique id for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resources unique id</dd>
</dl>
</li>
</ul>
<a name="setResourceUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceUniqueID</h4>
<pre>public&nbsp;void&nbsp;setResourceUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">Sets the resources unique id for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - resources unique id</dd>
</dl>
</li>
</ul>
<a name="getWorkgroupAssignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkgroupAssignment</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceAssignmentWorkgroupFields.html" title="class in org.mpxj">ResourceAssignmentWorkgroupFields</a>&nbsp;getWorkgroupAssignment()</pre>
<div class="block">Gets the Resource Assignment Workgroup Fields if one exists.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>workgroup assignment object</dd>
</dl>
</li>
</ul>
<a name="getTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;getTask()</pre>
<div class="block">This method retrieves a reference to the task with which this
 assignment is associated.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task</dd>
</dl>
</li>
</ul>
<a name="getResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResource</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;getResource()</pre>
<div class="block">This method retrieves a reference to the resource with which this
 assignment is associated.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource</dd>
</dl>
</li>
</ul>
<a name="getWorkContour--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkContour</h4>
<pre>public&nbsp;<a href="../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;getWorkContour()</pre>
<div class="block">This method returns the Work Contour type of this Assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Work Contour type</dd>
</dl>
</li>
</ul>
<a name="setWorkContour-org.mpxj.WorkContour-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkContour</h4>
<pre>public&nbsp;void&nbsp;setWorkContour(<a href="../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;workContour)</pre>
<div class="block">This method sets the Work Contour type of this Assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>workContour</code> - the Work Contour type</dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()</pre>
<div class="block">Removes this resource assignment from the project.</div>
</li>
</ul>
<a name="getRemainingWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingWork()</pre>
<div class="block">Returns the remaining work for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining work</dd>
</dl>
</li>
</ul>
<a name="setRemainingWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;remainingWork)</pre>
<div class="block">Sets the remaining work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>remainingWork</code> - remaining work</dd>
</dl>
</li>
</ul>
<a name="getLevelingDelay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelingDelay</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getLevelingDelay()</pre>
<div class="block">Retrieves the leveling delay for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>leveling delay</dd>
</dl>
</li>
</ul>
<a name="setLevelingDelay-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelingDelay</h4>
<pre>public&nbsp;void&nbsp;setLevelingDelay(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;levelingDelay)</pre>
<div class="block">Sets the leveling delay for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>levelingDelay</code> - leveling delay</dd>
</dl>
</li>
</ul>
<a name="getRateIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRateIndex</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRateIndex()</pre>
<div class="block">Retrieve the index of the rate in the cost rate table used
 to calculate the cost for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>rate index</dd>
</dl>
</li>
</ul>
<a name="setRateIndex-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRateIndex</h4>
<pre>public&nbsp;void&nbsp;setRateIndex(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;index)</pre>
<div class="block">Set the index of the rate in the cost rate table used
 to calculate the cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - rate index</dd>
</dl>
</li>
</ul>
<a name="getRoleUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRoleUniqueID()</pre>
<div class="block">Retrieve the role unique ID in which this resource assignment is being performed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Resource unique ID representing a role</dd>
</dl>
</li>
</ul>
<a name="setRoleUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoleUniqueID</h4>
<pre>public&nbsp;void&nbsp;setRoleUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the unique ID of the role in which this resource assignment is being performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Resource unique ID representing a role</dd>
</dl>
</li>
</ul>
<a name="getRole--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRole</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;getRole()</pre>
<div class="block">Retrieve the role in which this resource assignment is being performed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Resource instance representing a role</dd>
</dl>
</li>
</ul>
<a name="setRole-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRole</h4>
<pre>public&nbsp;void&nbsp;setRole(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</pre>
<div class="block">Set the role in which this resource assignment is being performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>role</code> - Resource instance representing a role</dd>
</dl>
</li>
</ul>
<a name="getOverrideRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverrideRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getOverrideRate()</pre>
<div class="block">Retrieve the rate to use in place of the value from the cost rate table.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>override rate</dd>
</dl>
</li>
</ul>
<a name="setOverrideRate-org.mpxj.Rate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverrideRate</h4>
<pre>public&nbsp;void&nbsp;setOverrideRate(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</pre>
<div class="block">Set the rate to use in place of the value from the cost rate table.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rate</code> - override rate</dd>
</dl>
</li>
</ul>
<a name="getRateSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRateSource</h4>
<pre>public&nbsp;<a href="../../org/mpxj/RateSource.html" title="enum in org.mpxj">RateSource</a>&nbsp;getRateSource()</pre>
<div class="block">Retrieve the source of the cost rate to be used for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>rate source</dd>
</dl>
</li>
</ul>
<a name="setRateSource-org.mpxj.RateSource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRateSource</h4>
<pre>public&nbsp;void&nbsp;setRateSource(<a href="../../org/mpxj/RateSource.html" title="enum in org.mpxj">RateSource</a>&nbsp;source)</pre>
<div class="block">Set the source of the cost rate to be used for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - rate source</dd>
</dl>
</li>
</ul>
<a name="getTimephasedPlannedWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedPlannedWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedPlannedWork()</pre>
<div class="block">Retrieves the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased planned work</dd>
</dl>
</li>
</ul>
<a name="setTimephasedPlannedWork-org.mpxj.TimephasedWorkContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedPlannedWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedPlannedWork(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedPlannedWork-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedPlannedWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedPlannedWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="getTimephasedActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedActualWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedActualWork()</pre>
<div class="block">Retrieves the timephased breakdown of the completed work for this
 resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased completed work</dd>
</dl>
</li>
</ul>
<a name="setTimephasedActualWork-org.mpxj.TimephasedWorkContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedActualWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedActualWork(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the completed work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedActualWork-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedActualWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedActualWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the completed work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="getTimephasedWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedWork()</pre>
<div class="block">Retrieves the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased planned work</dd>
</dl>
</li>
</ul>
<a name="setTimephasedWork-org.mpxj.TimephasedWorkContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedWork(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedWork-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the planned work for this
 resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="getTimephasedOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedOvertimeWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedOvertimeWork()</pre>
<div class="block">Retrieves the timephased breakdown of the planned overtime work for this
 resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased planned work</dd>
</dl>
</li>
</ul>
<a name="setTimephasedActualOvertimeWork-org.mpxj.TimephasedWorkContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedActualOvertimeWork(<a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the actual overtime work
 for this assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased work</dd>
</dl>
</li>
</ul>
<a name="setTimephasedActualOvertimeWork-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedActualOvertimeWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</pre>
<div class="block">Sets the timephased breakdown of the actual overtime work
 for this assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - timephased work</dd>
</dl>
</li>
</ul>
<a name="getTimephasedActualOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedActualOvertimeWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedActualOvertimeWork()</pre>
<div class="block">Retrieves the timephased breakdown of the actual overtime work for this
 resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased planned work</dd>
</dl>
</li>
</ul>
<a name="getTimephasedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;getTimephasedCost()</pre>
<div class="block">Retrieves the timephased breakdown of cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased cost</dd>
</dl>
</li>
</ul>
<a name="getTimephasedActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;getTimephasedActualCost()</pre>
<div class="block">Retrieves the timephased breakdown of actual cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased actual cost</dd>
</dl>
</li>
</ul>
<a name="getHasTimephasedData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHasTimephasedData</h4>
<pre>public&nbsp;boolean&nbsp;getHasTimephasedData()</pre>
<div class="block">Retrieve a flag indicating if this resource assignment has timephased
 data associated with it.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this resource assignment has timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedBaselineWork-int-org.mpxj.TimephasedWorkContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedBaselineWork(int&nbsp;index,
                                      <a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a>&nbsp;data)</pre>
<div class="block">Set timephased baseline work. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedBaselineWork-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setTimephasedBaselineWork(int&nbsp;index,
                                      <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;data)</pre>
<div class="block">Set timephased baseline work. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedBaselineCost-int-org.mpxj.TimephasedCostContainer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setTimephasedBaselineCost(int&nbsp;index,
                                      <a href="../../org/mpxj/TimephasedCostContainer.html" title="interface in org.mpxj">TimephasedCostContainer</a>&nbsp;data)</pre>
<div class="block">Set timephased baseline cost. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="setTimephasedBaselineCost-int-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimephasedBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setTimephasedBaselineCost(int&nbsp;index,
                                      <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;data)</pre>
<div class="block">Set timephased baseline cost. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dd><code>data</code> - timephased data</dd>
</dl>
</li>
</ul>
<a name="getTimephasedBaselineWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedBaselineWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;getTimephasedBaselineWork(int&nbsp;index)</pre>
<div class="block">Retrieve timephased baseline work. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased work, or null if no baseline is present</dd>
</dl>
</li>
</ul>
<a name="getTimephasedBaselineCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimephasedBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;getTimephasedBaselineCost(int&nbsp;index)</pre>
<div class="block">Retrieve timephased baseline cost. Note that index 0 represents "Baseline",
 index 1 represents "Baseline1" and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timephased work, or null if no baseline is present</dd>
</dl>
</li>
</ul>
<a name="getEffectiveCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectiveCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;getEffectiveCalendar()</pre>
<div class="block">Retrieves the effective calendar used for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectCalendar instance</dd>
</dl>
</li>
</ul>
<a name="getVariableRateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVariableRateUnits</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;getVariableRateUnits()</pre>
<div class="block">Retrieve the variable rate time units, null if fixed rate.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>variable rate time units</dd>
</dl>
</li>
</ul>
<a name="setVariableRateUnits-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVariableRateUnits</h4>
<pre>public&nbsp;void&nbsp;setVariableRateUnits(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;variableRateUnits)</pre>
<div class="block">Set the variable rate time units, null if fixed rate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>variableRateUnits</code> - variable rate units</dd>
</dl>
</li>
</ul>
<a name="setTaskUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskUniqueID</h4>
<pre>public&nbsp;void&nbsp;setTaskUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the parent task unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - task unique ID</dd>
</dl>
</li>
</ul>
<a name="getTaskUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getTaskUniqueID()</pre>
<div class="block">Retrieve the parent task unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task unique ID</dd>
</dl>
</li>
</ul>
<a name="getBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBudgetCost()</pre>
<div class="block">Retrieves the budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget cost</dd>
</dl>
</li>
</ul>
<a name="setBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - budget cost</dd>
</dl>
</li>
</ul>
<a name="getBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBudgetWork()</pre>
<div class="block">Retrieves the budget work value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget work</dd>
</dl>
</li>
</ul>
<a name="setBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the budget work value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - budget work</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost()</pre>
<div class="block">Retrieves the baseline budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget cost</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - baseline budget cost</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork()</pre>
<div class="block">Retrieves the baseline budget work value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget work</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the baseline budget work value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - baseline budget work</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(int&nbsp;baselineNumber,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(int&nbsp;baselineNumber,
                            <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineStart(int&nbsp;baselineNumber,
                             <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineStart(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinish(int&nbsp;baselineNumber,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineFinish(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(int&nbsp;baselineNumber,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(int&nbsp;baselineNumber,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dd><code>value</code> - text value</dd>
</dl>
</li>
</ul>
<a name="getText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getText(int&nbsp;index)</pre>
<div class="block">Retrieve a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>text value</dd>
</dl>
</li>
</ul>
<a name="setStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(int&nbsp;index,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dd><code>value</code> - start value</dd>
</dl>
</li>
</ul>
<a name="getStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart(int&nbsp;index)</pre>
<div class="block">Retrieve a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start value</dd>
</dl>
</li>
</ul>
<a name="setFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dd><code>value</code> - finish value</dd>
</dl>
</li>
</ul>
<a name="getFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish(int&nbsp;index)</pre>
<div class="block">Retrieve a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish value</dd>
</dl>
</li>
</ul>
<a name="setDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dd><code>value</code> - date value</dd>
</dl>
</li>
</ul>
<a name="getDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDate(int&nbsp;index)</pre>
<div class="block">Retrieve a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>date value</dd>
</dl>
</li>
</ul>
<a name="setNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumber</h4>
<pre>public&nbsp;void&nbsp;setNumber(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dd><code>value</code> - number value</dd>
</dl>
</li>
</ul>
<a name="getNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getNumber(int&nbsp;index)</pre>
<div class="block">Retrieve a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number value</dd>
</dl>
</li>
</ul>
<a name="setDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;void&nbsp;setDuration(int&nbsp;index,
                        <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dd><code>value</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDuration(int&nbsp;index)</pre>
<div class="block">Retrieve a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration value</dd>
</dl>
</li>
</ul>
<a name="setCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dd><code>value</code> - cost value</dd>
</dl>
</li>
</ul>
<a name="getCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost(int&nbsp;index)</pre>
<div class="block">Retrieve a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost value</dd>
</dl>
</li>
</ul>
<a name="setFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFlag</h4>
<pre>public&nbsp;void&nbsp;setFlag(int&nbsp;index,
                    boolean&nbsp;value)</pre>
<div class="block">Set a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dd><code>value</code> - flag value</dd>
</dl>
</li>
</ul>
<a name="getFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlag</h4>
<pre>public&nbsp;boolean&nbsp;getFlag(int&nbsp;index)</pre>
<div class="block">Retrieve a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>flag value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseCost</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseCost(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-30)</dd>
<dd><code>value</code> - cost value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseCost(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDate</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDate(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set an enterprise date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-30)</dd>
<dd><code>value</code> - date value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEnterpriseDate(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>date value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDuration</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDuration(int&nbsp;index,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set an enterprise duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-30)</dd>
<dd><code>value</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getEnterpriseDuration(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseFlag</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseFlag(int&nbsp;index,
                              boolean&nbsp;value)</pre>
<div class="block">Set an enterprise flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dd><code>value</code> - flag value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseFlag</h4>
<pre>public&nbsp;boolean&nbsp;getEnterpriseFlag(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>flag value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseNumber</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseNumber(int&nbsp;index,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-40)</dd>
<dd><code>value</code> - number value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseNumber(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-40)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseText</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseText(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set an enterprise text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-40)</dd>
<dd><code>value</code> - text value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEnterpriseText(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-40)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>text value</dd>
</dl>
</li>
</ul>
<a name="getRegularWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegularWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRegularWork()</pre>
<div class="block">Returns the regular work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work</dd>
</dl>
</li>
</ul>
<a name="setRegularWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegularWork</h4>
<pre>public&nbsp;void&nbsp;setRegularWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Sets the regular work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - work</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualOvertimeWork()</pre>
<div class="block">Returns the actual overtime work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Sets the actual overtime work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - work</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingOvertimeWork()</pre>
<div class="block">Returns the remaining overtime work of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Sets the remaining overtime work for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - work</dd>
</dl>
</li>
</ul>
<a name="getOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getOvertimeCost()</pre>
<div class="block">Returns the overtime cost of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost</dd>
</dl>
</li>
</ul>
<a name="setOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the overtime cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost</dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingCost()</pre>
<div class="block">Returns the remaining cost of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost</dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the remaining cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Returns the actual overtime cost of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the actual overtime cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingOvertimeCost()</pre>
<div class="block">Returns the remaining overtime cost of this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the remaining overtime cost for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost</dd>
</dl>
</li>
</ul>
<a name="setBCWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWP</h4>
<pre>public&nbsp;void&nbsp;setBCWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The BCWP (budgeted cost of work performed) field contains the
 cumulative value
 of the assignment's timephased percent complete multiplied by
 the assignments
 timephased baseline cost. BCWP is calculated up to the status
 date or today's
 date. This information is also known as earned value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the amount to be set</dd>
</dl>
</li>
</ul>
<a name="getBCWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWP()</pre>
<div class="block">The BCWP (budgeted cost of work performed) field contains
 the cumulative value of the assignment's timephased percent complete
 multiplied by the assignment's timephased baseline cost.
 BCWP is calculated up to the status date or today's date.
 This information is also known as earned value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="setBCWS-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWS</h4>
<pre>public&nbsp;void&nbsp;setBCWS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the amount to set</dd>
</dl>
</li>
</ul>
<a name="getBCWS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWS()</pre>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="getACWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getACWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getACWP()</pre>
<div class="block">Retrieve the ACWP value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ACWP value</dd>
</dl>
</li>
</ul>
<a name="setACWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setACWP</h4>
<pre>public&nbsp;void&nbsp;setACWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</pre>
<div class="block">Set the ACWP value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>acwp</code> - ACWP value</dd>
</dl>
</li>
</ul>
<a name="setSV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSV</h4>
<pre>public&nbsp;void&nbsp;setSV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The SV (earned value schedule variance) field shows the difference
 in cost terms between the current progress and the baseline plan
 of the task up to the status date or today's date. You can use SV
 to check costs to determine whether tasks are on schedule.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - currency amount</dd>
</dl>
</li>
</ul>
<a name="getSV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getSV()</pre>
<div class="block">The SV (earned value schedule variance) field shows the difference in
 cost terms between the current progress and the baseline plan of the
 task up to the status date or today's date. You can use SV to
 check costs to determine whether tasks are on schedule.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>-earned value schedule variance</dd>
</dl>
</li>
</ul>
<a name="setCV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCV</h4>
<pre>public&nbsp;void&nbsp;setCV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The CV (earned value cost variance) field shows the difference
 between how much it should have cost to achieve the current level of
 completion on the task, and how much it has actually cost to achieve the
 current level of completion up to the status date or today's date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to set</dd>
</dl>
</li>
</ul>
<a name="getCV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCV()</pre>
<div class="block">The CV (earned value cost variance) field shows the difference between
 how much it should have cost to achieve the current level of completion
 on the task, and how much it has actually cost to achieve the current
 level of completion up to the status date or today's date.
 How Calculated   CV is the difference between BCWP
 (budgeted cost of work performed) and ACWP
 (actual cost of work performed). Microsoft Project calculates
 the CV as follows: CV = BCWP - ACWP</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sum of earned value cost variance</dd>
</dl>
</li>
</ul>
<a name="setCostVariance-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostVariance</h4>
<pre>public&nbsp;void&nbsp;setCostVariance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Cost Variance field shows the difference between the
 baseline cost and total cost for a task. The total cost is the
 current estimate of costs based on actual costs and remaining costs.
 This is also referred to as variance at completion (VAC).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - amount</dd>
</dl>
</li>
</ul>
<a name="getCostVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostVariance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCostVariance()</pre>
<div class="block">The Cost Variance field shows the difference between the baseline cost
 and total cost for a task. The total cost is the current estimate of costs
 based on actual costs and remaining costs. This is also referred to as
 variance at completion (VAC).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>amount</dd>
</dl>
</li>
</ul>
<a name="setPercentageWorkComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentageWorkComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentageWorkComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the
 percentage of the task's work that has been completed. You can enter
 percent work
 complete, or you can have Microsoft Project calculate it for you
 based on actual
 work on the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="getPercentageWorkComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentageWorkComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentageWorkComplete()</pre>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the percentage of the task's work that has been completed.
 You can enter percent work complete, or you can have Microsoft Project
 calculate it for you based on actual work on the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percentage as float</dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</pre>
<div class="block">This method is used to add notes to the current task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - notes to be added</dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Retrieve the plain text representation of the assignment notes.
 Use the getNotesObject method to retrieve an object which
 contains both the plain text notes and, if relevant,
 the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>notes</dd>
</dl>
</li>
</ul>
<a name="setNotesObject-org.mpxj.Notes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotesObject</h4>
<pre>public&nbsp;void&nbsp;setNotesObject(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</pre>
<div class="block">Set the Notes instance representing the assignment notes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - Notes instance</dd>
</dl>
</li>
</ul>
<a name="getNotesObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;getNotesObject()</pre>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Notes instance</dd>
</dl>
</li>
</ul>
<a name="setConfirmed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfirmed</h4>
<pre>public&nbsp;void&nbsp;setConfirmed(boolean&nbsp;val)</pre>
<div class="block">The Confirmed field indicates whether all resources assigned to a task have
 accepted or rejected the task assignment in response to a TeamAssign message
 regarding their assignments.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="getConfirmed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfirmed</h4>
<pre>public&nbsp;boolean&nbsp;getConfirmed()</pre>
<div class="block">The Confirmed field indicates whether all resources assigned to a task
 have accepted or rejected the task assignment in response to a TeamAssign
 message regarding their assignments.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="setUpdateNeeded-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdateNeeded</h4>
<pre>public&nbsp;void&nbsp;setUpdateNeeded(boolean&nbsp;val)</pre>
<div class="block">The Update Needed field indicates whether a TeamUpdate message should
 be sent to the assigned resources because of changes to the start date,
 finish date, or resource reassignments of the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - boolean</dd>
</dl>
</li>
</ul>
<a name="getUpdateNeeded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdateNeeded</h4>
<pre>public&nbsp;boolean&nbsp;getUpdateNeeded()</pre>
<div class="block">The Update Needed field indicates whether a TeamUpdate message
 should be sent to the assigned resources because of changes to the
 start date, finish date, or resource reassignments of the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if needed.</dd>
</dl>
</li>
</ul>
<a name="setLinkedFields-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkedFields</h4>
<pre>public&nbsp;void&nbsp;setLinkedFields(boolean&nbsp;flag)</pre>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project
 file, or from another program.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="getLinkedFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkedFields</h4>
<pre>public&nbsp;boolean&nbsp;getLinkedFields()</pre>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project file,
 or from another program.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getHyperlink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlink()</pre>
<div class="block">Retrieves the task hyperlink attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkAddress()</pre>
<div class="block">Retrieves the task hyperlink address attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink address attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkSubAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkSubAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkSubAddress()</pre>
<div class="block">Retrieves the task hyperlink sub-address attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink sub address attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlink-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlink</h4>
<pre>public&nbsp;void&nbsp;setHyperlink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink address attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink address attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkSubAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkSubAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkSubAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink sub address attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink sub address attribute</dd>
</dl>
</li>
</ul>
<a name="setWorkVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkVariance</h4>
<pre>public&nbsp;void&nbsp;setWorkVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Work Variance field contains the difference between a task's baseline
 work and the currently scheduled work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="getWorkVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWorkVariance()</pre>
<div class="block">The Work Variance field contains the difference between a task's
 baseline work and the currently scheduled work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration representing duration.</dd>
</dl>
</li>
</ul>
<a name="setStartVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartVariance</h4>
<pre>public&nbsp;void&nbsp;setStartVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Start Variance field contains the amount of time that represents the
 difference between a task's baseline start date and its currently
 scheduled start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="getStartVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getStartVariance()</pre>
<div class="block">Calculate the start variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start variance</dd>
</dl>
</li>
</ul>
<a name="setFinishVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishVariance</h4>
<pre>public&nbsp;void&nbsp;setFinishVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">The Finish Variance field contains the amount of time that represents the
 difference between a task's baseline finish date and its forecast
 or actual finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getFinishVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getFinishVariance()</pre>
<div class="block">Calculate the finish variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish variance</dd>
</dl>
</li>
</ul>
<a name="getCreateDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreateDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreateDate()</pre>
<div class="block">The Created field contains the date and time when a task was added
 to the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="setCreateDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreateDate</h4>
<pre>public&nbsp;void&nbsp;setCreateDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Created field contains the date and time when a task was
 added to the project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - date</dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Retrieve the task GUID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task GUID</dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Set the task GUID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - task GUID</dd>
</dl>
</li>
</ul>
<a name="setResponsePending-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResponsePending</h4>
<pre>public&nbsp;void&nbsp;setResponsePending(boolean&nbsp;val)</pre>
<div class="block">Sets a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="getResponsePending--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponsePending</h4>
<pre>public&nbsp;boolean&nbsp;getResponsePending()</pre>
<div class="block">Retrieves a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean value</dd>
</dl>
</li>
</ul>
<a name="setTeamStatusPending-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTeamStatusPending</h4>
<pre>public&nbsp;void&nbsp;setTeamStatusPending(boolean&nbsp;val)</pre>
<div class="block">Sets a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="getTeamStatusPending--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeamStatusPending</h4>
<pre>public&nbsp;boolean&nbsp;getTeamStatusPending()</pre>
<div class="block">Retrieves a flag to indicate if a response has been received from a resource
 assigned to a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean value</dd>
</dl>
</li>
</ul>
<a name="setVAC-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVAC</h4>
<pre>public&nbsp;void&nbsp;setVAC(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Sets VAC for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - VAC value</dd>
</dl>
</li>
</ul>
<a name="getVAC--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVAC</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getVAC()</pre>
<div class="block">Returns the VAC for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>VAC value</dd>
</dl>
</li>
</ul>
<a name="setCostRateTableIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostRateTableIndex</h4>
<pre>public&nbsp;void&nbsp;setCostRateTableIndex(int&nbsp;index)</pre>
<div class="block">Sets the index of the cost rate table for this assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost rate table index</dd>
</dl>
</li>
</ul>
<a name="getCostRateTableIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostRateTableIndex</h4>
<pre>public&nbsp;int&nbsp;getCostRateTableIndex()</pre>
<div class="block">Returns the cost rate table index for this assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost rate table index</dd>
</dl>
</li>
</ul>
<a name="getCostRateTable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostRateTable</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a>&nbsp;getCostRateTable()</pre>
<div class="block">Returns the cost rate table for this assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost rate table index</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkScreenTip--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkScreenTip</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkScreenTip()</pre>
<div class="block">Retrieves the hyperlink screen tip attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkScreenTip-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkScreenTip</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkScreenTip(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the hyperlink screen tip attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="getResourceRequestType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceRequestType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a>&nbsp;getResourceRequestType()</pre>
<div class="block">Retrieves the resource request type attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource request type</dd>
</dl>
</li>
</ul>
<a name="setResourceRequestType-org.mpxj.ResourceRequestType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceRequestType</h4>
<pre>public&nbsp;void&nbsp;setResourceRequestType(<a href="../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a>&nbsp;type)</pre>
<div class="block">Sets the resource request type attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - resource request type</dd>
</dl>
</li>
</ul>
<a name="getStop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStop</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStop()</pre>
<div class="block">Retrieve the stop date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>stop date</dd>
</dl>
</li>
</ul>
<a name="setStop-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStop</h4>
<pre>public&nbsp;void&nbsp;setStop(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;stop)</pre>
<div class="block">Set the stop date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>stop</code> - stop date</dd>
</dl>
</li>
</ul>
<a name="getResume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResume</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getResume()</pre>
<div class="block">Retrieve the resume date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resume date</dd>
</dl>
</li>
</ul>
<a name="setResume-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResume</h4>
<pre>public&nbsp;void&nbsp;setResume(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;resume)</pre>
<div class="block">Set the resume date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resume</code> - resume date</dd>
</dl>
</li>
</ul>
<a name="getPlannedWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPlannedWork()</pre>
<div class="block">Retrieve the planned work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned work value</dd>
</dl>
</li>
</ul>
<a name="setPlannedWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedWork</h4>
<pre>public&nbsp;void&nbsp;setPlannedWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the planned work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned work value</dd>
</dl>
</li>
</ul>
<a name="getPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPlannedCost()</pre>
<div class="block">Retrieve the planned cost field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned cost value</dd>
</dl>
</li>
</ul>
<a name="setPlannedCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the planned cost field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned cost value</dd>
</dl>
</li>
</ul>
<a name="getPlannedStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedStart()</pre>
<div class="block">Set the planned start field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned start value</dd>
</dl>
</li>
</ul>
<a name="setPlannedStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedStart</h4>
<pre>public&nbsp;void&nbsp;setPlannedStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Retrieve the planned start field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned start value</dd>
</dl>
</li>
</ul>
<a name="getPlannedFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedFinish()</pre>
<div class="block">Retrieve the planned finish value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planed finish value</dd>
</dl>
</li>
</ul>
<a name="setPlannedFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedFinish</h4>
<pre>public&nbsp;void&nbsp;setPlannedFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the planned finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned finish value</dd>
</dl>
</li>
</ul>
<a name="getCalculateCostsFromUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateCostsFromUnits</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateCostsFromUnits()</pre>
<div class="block">Retrieve the calculate costs from units flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculate costs from units flag</dd>
</dl>
</li>
</ul>
<a name="setCalculateCostsFromUnits-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateCostsFromUnits</h4>
<pre>public&nbsp;void&nbsp;setCalculateCostsFromUnits(boolean&nbsp;calculateCostsFromUnits)</pre>
<div class="block">Set the calculate costs from units flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calculateCostsFromUnits</code> - calculate costs from units flag</dd>
</dl>
</li>
</ul>
<a name="getCostAccountUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostAccountUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCostAccountUniqueID()</pre>
<div class="block">Retrieve the cost account unique ID for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost account unique ID</dd>
</dl>
</li>
</ul>
<a name="setCostAccountUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostAccountUniqueID</h4>
<pre>public&nbsp;void&nbsp;setCostAccountUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the cost account unique ID for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - cost account unique ID</dd>
</dl>
</li>
</ul>
<a name="getCostAccount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostAccount</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CostAccount.html" title="class in org.mpxj">CostAccount</a>&nbsp;getCostAccount()</pre>
<div class="block">Retrieve the cost account for this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>CostAccount instance for this resource assignment</dd>
</dl>
</li>
</ul>
<a name="setCostAccount-org.mpxj.CostAccount-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostAccount</h4>
<pre>public&nbsp;void&nbsp;setCostAccount(<a href="../../org/mpxj/CostAccount.html" title="class in org.mpxj">CostAccount</a>&nbsp;costAccount)</pre>
<div class="block">Set the cost account for this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>costAccount</code> - cost account for this resource assignment</dd>
</dl>
</li>
</ul>
<a name="getRemainingLateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateFinish()</pre>
<div class="block">Retrieve the remaining late finish value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining late finish</dd>
</dl>
</li>
</ul>
<a name="setRemainingLateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateFinish</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining late finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining late finish</dd>
</dl>
</li>
</ul>
<a name="getRemainingLateStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateStart()</pre>
<div class="block">Retrieve the remaining late start value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining late start</dd>
</dl>
</li>
</ul>
<a name="setRemainingLateStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateStart</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining late start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining late start</dd>
</dl>
</li>
</ul>
<a name="getRemainingEarlyFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingEarlyFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingEarlyFinish()</pre>
<div class="block">Retrieve the remaining early finish value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining early finish</dd>
</dl>
</li>
</ul>
<a name="setRemainingEarlyFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingEarlyFinish</h4>
<pre>public&nbsp;void&nbsp;setRemainingEarlyFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining early finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining early finish</dd>
</dl>
</li>
</ul>
<a name="getRemainingEarlyStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingEarlyStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingEarlyStart()</pre>
<div class="block">Retrieve the remaining early start value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining early start</dd>
</dl>
</li>
</ul>
<a name="setRemainingEarlyStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingEarlyStart</h4>
<pre>public&nbsp;void&nbsp;setRemainingEarlyStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining early start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining early start</dd>
</dl>
</li>
</ul>
<a name="getEffectiveRate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectiveRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getEffectiveRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Based on the configuration data for this resource assignment,
 return the cost rate effective on the supplied date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - target date</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost rate effective on the target date</dd>
</dl>
</li>
</ul>
<a name="getResourceAssignmentCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceAssignmentCodeValues</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ResourceAssignmentCode.html" title="class in org.mpxj">ResourceAssignmentCode</a>,<a href="../../org/mpxj/ResourceAssignmentCodeValue.html" title="class in org.mpxj">ResourceAssignmentCodeValue</a>&gt;&nbsp;getResourceAssignmentCodeValues()</pre>
<div class="block">Retrieve the resource assignment code values associated with this resource assignment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>map of resource assignment code values</dd>
</dl>
</li>
</ul>
<a name="addResourceAssignmentCodeValue-org.mpxj.ResourceAssignmentCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceAssignmentCodeValue</h4>
<pre>public&nbsp;void&nbsp;addResourceAssignmentCodeValue(<a href="../../org/mpxj/ResourceAssignmentCodeValue.html" title="class in org.mpxj">ResourceAssignmentCodeValue</a>&nbsp;value)</pre>
<div class="block">Assign a resource assignment code value to this resource assignment.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - resource assignment code value</dd>
</dl>
</li>
</ul>
<a name="getFieldByAlias-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldByAlias</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</pre>
<div class="block">Retrieve the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setFieldByAlias-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldByAlias</h4>
<pre>public&nbsp;void&nbsp;setFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Set the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>handleFieldChange</h4>
<pre>protected&nbsp;void&nbsp;handleFieldChange(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</pre>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>field</code> - modified field</dd>
<dd><code>oldValue</code> - old value of the updated field</dd>
<dd><code>newValue</code> - new value of the updated field</dd>
</dl>
</li>
</ul>
<a name="getParentFile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentFile</h4>
<pre>public final&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getParentFile()</pre>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>reference to this the parent ProjectFile instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAssignment.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/Resource.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ResourceAssignmentCode.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ResourceAssignment.html" target="_top">Frames</a></li>
<li><a href="ResourceAssignment.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
