<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivityCodeValue.Builder (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivityCodeValue.Builder (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivityCodeValue.Builder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ActivityCodeValue.Builder.html" target="_top">Frames</a></li>
<li><a href="ActivityCodeValue.Builder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class ActivityCodeValue.Builder" class="title">Class ActivityCodeValue.Builder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.ActivityCodeValue.Builder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ActivityCodeValue.Builder</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">ActivityCodeValue builder.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#activityCode-org.mpxj.ActivityCode-">activityCode</a></span>(<a href="../../org/mpxj/ActivityCode.html" title="class in org.mpxj">ActivityCode</a>&nbsp;value)</code>
<div class="block">Add parent activity code.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#build--">build</a></span>()</code>
<div class="block">Build an ActivityCodeValue instance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#color-java.awt.Color-">color</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;value)</code>
<div class="block">Add color.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#description-java.lang.String-">description</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Add description.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#from-org.mpxj.ActivityCodeValue-">from</a></span>(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</code>
<div class="block">Initialise the builder from an existing ActivityCodeValue instance.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#name-java.lang.String-">name</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Add name.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#parentValue-org.mpxj.ActivityCodeValue-">parentValue</a></span>(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</code>
<div class="block">Add parent value.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#sequenceNumber-java.lang.Integer-">sequenceNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Add sequence number.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ActivityCodeValue.Builder.html#uniqueID-java.lang.Integer-">uniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Add unique ID.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Builder-org.mpxj.UniqueIdObjectSequenceProvider-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Builder</h4>
<pre>public&nbsp;Builder(<a href="../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</pre>
<div class="block">Constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sequenceProvider</code> - parent project file</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="from-org.mpxj.ActivityCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>from</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;from(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</pre>
<div class="block">Initialise the builder from an existing ActivityCodeValue instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - ActivityCodeValue instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="activityCode-org.mpxj.ActivityCode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityCode</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;activityCode(<a href="../../org/mpxj/ActivityCode.html" title="class in org.mpxj">ActivityCode</a>&nbsp;value)</pre>
<div class="block">Add parent activity code.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity code</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="uniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uniqueID</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;uniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Add unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - unique ID</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="sequenceNumber-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sequenceNumber</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;sequenceNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Add sequence number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - sequence number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="name-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;name(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Add name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="description-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>description</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;description(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Add description.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - description</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="color-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>color</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;color(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;value)</pre>
<div class="block">Add color.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - color</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="parentValue-org.mpxj.ActivityCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parentValue</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a>&nbsp;parentValue(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</pre>
<div class="block">Add parent value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - parent value</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>builder</dd>
</dl>
</li>
</ul>
<a name="build--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>build</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;build()</pre>
<div class="block">Build an ActivityCodeValue instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ActivityCodeValue instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivityCodeValue.Builder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ActivityCodeValue.Builder.html" target="_top">Frames</a></li>
<li><a href="ActivityCodeValue.Builder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
