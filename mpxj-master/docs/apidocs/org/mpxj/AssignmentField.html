<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AssignmentField (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AssignmentField (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AssignmentField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/Availability.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/AssignmentField.html" target="_top">Frames</a></li>
<li><a href="AssignmentField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Enum AssignmentField" class="title">Enum AssignmentField</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.AssignmentField</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&gt;, <a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>, <a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">AssignmentField</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&gt;
implements <a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></pre>
<div class="block">Instances of this type represent Assignment fields.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_COST">ACTUAL_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_FINISH">ACTUAL_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_OVERTIME_COST">ACTUAL_OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_OVERTIME_WORK">ACTUAL_OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_OVERTIME_WORK_PROTECTED">ACTUAL_OVERTIME_WORK_PROTECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_START">ACTUAL_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_WORK">ACTUAL_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACTUAL_WORK_PROTECTED">ACTUAL_WORK_PROTECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ACWP">ACWP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ASSIGNMENT_DELAY">ASSIGNMENT_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ASSIGNMENT_RESOURCE_GUID">ASSIGNMENT_RESOURCE_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ASSIGNMENT_TASK_GUID">ASSIGNMENT_TASK_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ASSIGNMENT_UNITS">ASSIGNMENT_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_BUDGET_COST">BASELINE_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_BUDGET_WORK">BASELINE_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_COST">BASELINE_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_FINISH">BASELINE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_START">BASELINE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE_WORK">BASELINE_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_BUDGET_COST">BASELINE1_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_BUDGET_WORK">BASELINE1_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_COST">BASELINE1_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_FINISH">BASELINE1_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_START">BASELINE1_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE1_WORK">BASELINE1_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_BUDGET_COST">BASELINE10_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_BUDGET_WORK">BASELINE10_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_COST">BASELINE10_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_FINISH">BASELINE10_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_START">BASELINE10_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE10_WORK">BASELINE10_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_BUDGET_COST">BASELINE2_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_BUDGET_WORK">BASELINE2_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_COST">BASELINE2_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_FINISH">BASELINE2_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_START">BASELINE2_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE2_WORK">BASELINE2_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_BUDGET_COST">BASELINE3_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_BUDGET_WORK">BASELINE3_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_COST">BASELINE3_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_FINISH">BASELINE3_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_START">BASELINE3_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE3_WORK">BASELINE3_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_BUDGET_COST">BASELINE4_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_BUDGET_WORK">BASELINE4_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_COST">BASELINE4_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_FINISH">BASELINE4_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_START">BASELINE4_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE4_WORK">BASELINE4_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_BUDGET_COST">BASELINE5_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_BUDGET_WORK">BASELINE5_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_COST">BASELINE5_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_FINISH">BASELINE5_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_START">BASELINE5_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE5_WORK">BASELINE5_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_BUDGET_COST">BASELINE6_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_BUDGET_WORK">BASELINE6_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_COST">BASELINE6_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_FINISH">BASELINE6_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_START">BASELINE6_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE6_WORK">BASELINE6_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_BUDGET_COST">BASELINE7_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_BUDGET_WORK">BASELINE7_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_COST">BASELINE7_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_FINISH">BASELINE7_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_START">BASELINE7_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE7_WORK">BASELINE7_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_BUDGET_COST">BASELINE8_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_BUDGET_WORK">BASELINE8_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_COST">BASELINE8_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_FINISH">BASELINE8_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_START">BASELINE8_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE8_WORK">BASELINE8_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_BUDGET_COST">BASELINE9_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_BUDGET_WORK">BASELINE9_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_COST">BASELINE9_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_FINISH">BASELINE9_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_START">BASELINE9_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BASELINE9_WORK">BASELINE9_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BCWP">BCWP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BCWS">BCWS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BUDGET_COST">BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#BUDGET_WORK">BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#CALCULATE_COSTS_FROM_UNITS">CALCULATE_COSTS_FROM_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#CONFIRMED">CONFIRMED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST">COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST_ACCOUNT_UNIQUE_ID">COST_ACCOUNT_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST_RATE_TABLE">COST_RATE_TABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST_VARIANCE">COST_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST1">COST1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST10">COST10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST2">COST2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST3">COST3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST4">COST4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST5">COST5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST6">COST6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST7">COST7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST8">COST8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#COST9">COST9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#CREATED">CREATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#CV">CV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE1">DATE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE10">DATE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE2">DATE2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE3">DATE3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE4">DATE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE5">DATE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE6">DATE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE7">DATE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE8">DATE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DATE9">DATE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION1">DURATION1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION1_UNITS">DURATION1_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION10">DURATION10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION10_UNITS">DURATION10_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION2">DURATION2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION2_UNITS">DURATION2_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION3">DURATION3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION3_UNITS">DURATION3_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION4">DURATION4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION4_UNITS">DURATION4_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION5">DURATION5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION5_UNITS">DURATION5_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION6">DURATION6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION6_UNITS">DURATION6_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION7">DURATION7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION7_UNITS">DURATION7_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION8">DURATION8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION8_UNITS">DURATION8_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION9">DURATION9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#DURATION9_UNITS">DURATION9_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST1">ENTERPRISE_COST1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST10">ENTERPRISE_COST10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST2">ENTERPRISE_COST2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST3">ENTERPRISE_COST3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST4">ENTERPRISE_COST4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST5">ENTERPRISE_COST5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST6">ENTERPRISE_COST6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST7">ENTERPRISE_COST7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST8">ENTERPRISE_COST8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_COST9">ENTERPRISE_COST9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE1">ENTERPRISE_DATE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE10">ENTERPRISE_DATE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE11">ENTERPRISE_DATE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE12">ENTERPRISE_DATE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE13">ENTERPRISE_DATE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE14">ENTERPRISE_DATE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE15">ENTERPRISE_DATE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE16">ENTERPRISE_DATE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE17">ENTERPRISE_DATE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE18">ENTERPRISE_DATE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE19">ENTERPRISE_DATE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE2">ENTERPRISE_DATE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE20">ENTERPRISE_DATE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE21">ENTERPRISE_DATE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE22">ENTERPRISE_DATE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE23">ENTERPRISE_DATE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE24">ENTERPRISE_DATE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE25">ENTERPRISE_DATE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE26">ENTERPRISE_DATE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE27">ENTERPRISE_DATE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE28">ENTERPRISE_DATE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE29">ENTERPRISE_DATE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE3">ENTERPRISE_DATE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE30">ENTERPRISE_DATE30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE4">ENTERPRISE_DATE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE5">ENTERPRISE_DATE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE6">ENTERPRISE_DATE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE7">ENTERPRISE_DATE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE8">ENTERPRISE_DATE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DATE9">ENTERPRISE_DATE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION1">ENTERPRISE_DURATION1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION10">ENTERPRISE_DURATION10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION2">ENTERPRISE_DURATION2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION3">ENTERPRISE_DURATION3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION4">ENTERPRISE_DURATION4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION5">ENTERPRISE_DURATION5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION6">ENTERPRISE_DURATION6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION7">ENTERPRISE_DURATION7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION8">ENTERPRISE_DURATION8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_DURATION9">ENTERPRISE_DURATION9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG1">ENTERPRISE_FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG10">ENTERPRISE_FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG11">ENTERPRISE_FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG12">ENTERPRISE_FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG13">ENTERPRISE_FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG14">ENTERPRISE_FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG15">ENTERPRISE_FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG16">ENTERPRISE_FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG17">ENTERPRISE_FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG18">ENTERPRISE_FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG19">ENTERPRISE_FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG2">ENTERPRISE_FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG20">ENTERPRISE_FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG3">ENTERPRISE_FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG4">ENTERPRISE_FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG5">ENTERPRISE_FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG6">ENTERPRISE_FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG7">ENTERPRISE_FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG8">ENTERPRISE_FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_FLAG9">ENTERPRISE_FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER1">ENTERPRISE_NUMBER1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER10">ENTERPRISE_NUMBER10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER11">ENTERPRISE_NUMBER11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER12">ENTERPRISE_NUMBER12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER13">ENTERPRISE_NUMBER13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER14">ENTERPRISE_NUMBER14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER15">ENTERPRISE_NUMBER15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER16">ENTERPRISE_NUMBER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER17">ENTERPRISE_NUMBER17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER18">ENTERPRISE_NUMBER18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER19">ENTERPRISE_NUMBER19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER2">ENTERPRISE_NUMBER2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER20">ENTERPRISE_NUMBER20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER21">ENTERPRISE_NUMBER21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER22">ENTERPRISE_NUMBER22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER23">ENTERPRISE_NUMBER23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER24">ENTERPRISE_NUMBER24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER25">ENTERPRISE_NUMBER25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER26">ENTERPRISE_NUMBER26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER27">ENTERPRISE_NUMBER27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER28">ENTERPRISE_NUMBER28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER29">ENTERPRISE_NUMBER29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER3">ENTERPRISE_NUMBER3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER30">ENTERPRISE_NUMBER30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER31">ENTERPRISE_NUMBER31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER32">ENTERPRISE_NUMBER32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER33">ENTERPRISE_NUMBER33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER34">ENTERPRISE_NUMBER34</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER35">ENTERPRISE_NUMBER35</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER36">ENTERPRISE_NUMBER36</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER37">ENTERPRISE_NUMBER37</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER38">ENTERPRISE_NUMBER38</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER39">ENTERPRISE_NUMBER39</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER4">ENTERPRISE_NUMBER4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER40">ENTERPRISE_NUMBER40</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER5">ENTERPRISE_NUMBER5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER6">ENTERPRISE_NUMBER6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER7">ENTERPRISE_NUMBER7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER8">ENTERPRISE_NUMBER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_NUMBER9">ENTERPRISE_NUMBER9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE20">ENTERPRISE_RESOURCE_MULTI_VALUE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE21">ENTERPRISE_RESOURCE_MULTI_VALUE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE22">ENTERPRISE_RESOURCE_MULTI_VALUE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE23">ENTERPRISE_RESOURCE_MULTI_VALUE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE24">ENTERPRISE_RESOURCE_MULTI_VALUE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE25">ENTERPRISE_RESOURCE_MULTI_VALUE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE26">ENTERPRISE_RESOURCE_MULTI_VALUE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE27">ENTERPRISE_RESOURCE_MULTI_VALUE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE28">ENTERPRISE_RESOURCE_MULTI_VALUE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_MULTI_VALUE29">ENTERPRISE_RESOURCE_MULTI_VALUE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE1">ENTERPRISE_RESOURCE_OUTLINE_CODE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE10">ENTERPRISE_RESOURCE_OUTLINE_CODE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE11">ENTERPRISE_RESOURCE_OUTLINE_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE12">ENTERPRISE_RESOURCE_OUTLINE_CODE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE13">ENTERPRISE_RESOURCE_OUTLINE_CODE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE14">ENTERPRISE_RESOURCE_OUTLINE_CODE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE15">ENTERPRISE_RESOURCE_OUTLINE_CODE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE16">ENTERPRISE_RESOURCE_OUTLINE_CODE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE17">ENTERPRISE_RESOURCE_OUTLINE_CODE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE18">ENTERPRISE_RESOURCE_OUTLINE_CODE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE19">ENTERPRISE_RESOURCE_OUTLINE_CODE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE2">ENTERPRISE_RESOURCE_OUTLINE_CODE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE20">ENTERPRISE_RESOURCE_OUTLINE_CODE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE21">ENTERPRISE_RESOURCE_OUTLINE_CODE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE22">ENTERPRISE_RESOURCE_OUTLINE_CODE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE23">ENTERPRISE_RESOURCE_OUTLINE_CODE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE24">ENTERPRISE_RESOURCE_OUTLINE_CODE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE25">ENTERPRISE_RESOURCE_OUTLINE_CODE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE26">ENTERPRISE_RESOURCE_OUTLINE_CODE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE27">ENTERPRISE_RESOURCE_OUTLINE_CODE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE28">ENTERPRISE_RESOURCE_OUTLINE_CODE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE29">ENTERPRISE_RESOURCE_OUTLINE_CODE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE3">ENTERPRISE_RESOURCE_OUTLINE_CODE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE4">ENTERPRISE_RESOURCE_OUTLINE_CODE4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE5">ENTERPRISE_RESOURCE_OUTLINE_CODE5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE6">ENTERPRISE_RESOURCE_OUTLINE_CODE6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE7">ENTERPRISE_RESOURCE_OUTLINE_CODE7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE8">ENTERPRISE_RESOURCE_OUTLINE_CODE8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_OUTLINE_CODE9">ENTERPRISE_RESOURCE_OUTLINE_CODE9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_RESOURCE_RBS">ENTERPRISE_RESOURCE_RBS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEAM_MEMBER">ENTERPRISE_TEAM_MEMBER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT1">ENTERPRISE_TEXT1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT10">ENTERPRISE_TEXT10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT11">ENTERPRISE_TEXT11</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT12">ENTERPRISE_TEXT12</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT13">ENTERPRISE_TEXT13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT14">ENTERPRISE_TEXT14</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT15">ENTERPRISE_TEXT15</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT16">ENTERPRISE_TEXT16</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT17">ENTERPRISE_TEXT17</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT18">ENTERPRISE_TEXT18</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT19">ENTERPRISE_TEXT19</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT2">ENTERPRISE_TEXT2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT20">ENTERPRISE_TEXT20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT21">ENTERPRISE_TEXT21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT22">ENTERPRISE_TEXT22</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT23">ENTERPRISE_TEXT23</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT24">ENTERPRISE_TEXT24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT25">ENTERPRISE_TEXT25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT26">ENTERPRISE_TEXT26</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT27">ENTERPRISE_TEXT27</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT28">ENTERPRISE_TEXT28</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT29">ENTERPRISE_TEXT29</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT3">ENTERPRISE_TEXT3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT30">ENTERPRISE_TEXT30</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT31">ENTERPRISE_TEXT31</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT32">ENTERPRISE_TEXT32</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT33">ENTERPRISE_TEXT33</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT34">ENTERPRISE_TEXT34</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT35">ENTERPRISE_TEXT35</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT36">ENTERPRISE_TEXT36</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT37">ENTERPRISE_TEXT37</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT38">ENTERPRISE_TEXT38</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT39">ENTERPRISE_TEXT39</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT4">ENTERPRISE_TEXT4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT40">ENTERPRISE_TEXT40</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT5">ENTERPRISE_TEXT5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT6">ENTERPRISE_TEXT6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT7">ENTERPRISE_TEXT7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT8">ENTERPRISE_TEXT8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ENTERPRISE_TEXT9">ENTERPRISE_TEXT9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH">FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH_VARIANCE">FINISH_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH1">FINISH1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH10">FINISH10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH2">FINISH2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH3">FINISH3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH4">FINISH4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH5">FINISH5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH6">FINISH6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH7">FINISH7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH8">FINISH8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FINISH9">FINISH9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FIXED_MATERIAL_ASSIGNMENT">FIXED_MATERIAL_ASSIGNMENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG1">FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG10">FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG11">FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG12">FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG13">FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG14">FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG15">FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG16">FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG17">FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG18">FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG19">FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG2">FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG20">FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG3">FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG4">FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG5">FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG6">FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG7">FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG8">FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#FLAG9">FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#GUID">GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK">HYPERLINK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK_ADDRESS">HYPERLINK_ADDRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK_DATA">HYPERLINK_DATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK_HREF">HYPERLINK_HREF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK_SCREEN_TIP">HYPERLINK_SCREEN_TIP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#HYPERLINK_SUBADDRESS">HYPERLINK_SUBADDRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#INDEX">INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#LEVELING_DELAY">LEVELING_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#LEVELING_DELAY_UNITS">LEVELING_DELAY_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#LINKED_FIELDS">LINKED_FIELDS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NOTES">NOTES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER1">NUMBER1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER10">NUMBER10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER11">NUMBER11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER12">NUMBER12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER13">NUMBER13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER14">NUMBER14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER15">NUMBER15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER16">NUMBER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER17">NUMBER17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER18">NUMBER18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER19">NUMBER19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER2">NUMBER2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER20">NUMBER20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER3">NUMBER3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER4">NUMBER4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER5">NUMBER5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER6">NUMBER6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER7">NUMBER7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER8">NUMBER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#NUMBER9">NUMBER9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#OVERALLOCATED">OVERALLOCATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#OVERRIDE_RATE">OVERRIDE_RATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#OVERTIME_COST">OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#OVERTIME_WORK">OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#OWNER">OWNER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PEAK">PEAK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PERCENT_WORK_COMPLETE">PERCENT_WORK_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PLANNED_COST">PLANNED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PLANNED_FINISH">PLANNED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PLANNED_START">PLANNED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PLANNED_WORK">PLANNED_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#PROJECT">PROJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RATE_INDEX">RATE_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RATE_SOURCE">RATE_SOURCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REGULAR_WORK">REGULAR_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_ASSIGNMENT_UNITS">REMAINING_ASSIGNMENT_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_COST">REMAINING_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_EARLY_FINISH">REMAINING_EARLY_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_EARLY_START">REMAINING_EARLY_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_LATE_FINISH">REMAINING_LATE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_LATE_START">REMAINING_LATE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_OVERTIME_COST">REMAINING_OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_OVERTIME_WORK">REMAINING_OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#REMAINING_WORK">REMAINING_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_ASSIGNMENT_CODE_VALUES">RESOURCE_ASSIGNMENT_CODE_VALUES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_ID">RESOURCE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_NAME">RESOURCE_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_REQUEST_TYPE">RESOURCE_REQUEST_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_TYPE">RESOURCE_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESOURCE_UNIQUE_ID">RESOURCE_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESPONSE_PENDING">RESPONSE_PENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#RESUME">RESUME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#ROLE_UNIQUE_ID">ROLE_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START">START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START_VARIANCE">START_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START1">START1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START10">START10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START2">START2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START3">START3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START4">START4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START5">START5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START6">START6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START7">START7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START8">START8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#START9">START9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#STOP">STOP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#SUMMARY">SUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#SV">SV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TASK_ID">TASK_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TASK_NAME">TASK_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TASK_OUTLINE_NUMBER">TASK_OUTLINE_NUMBER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TASK_SUMMARY_NAME">TASK_SUMMARY_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TASK_UNIQUE_ID">TASK_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEAM_STATUS_PENDING">TEAM_STATUS_PENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT1">TEXT1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT10">TEXT10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT11">TEXT11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT12">TEXT12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT13">TEXT13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT14">TEXT14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT15">TEXT15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT16">TEXT16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT17">TEXT17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT18">TEXT18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT19">TEXT19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT2">TEXT2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT20">TEXT20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT21">TEXT21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT22">TEXT22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT23">TEXT23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT24">TEXT24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT25">TEXT25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT26">TEXT26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT27">TEXT27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT28">TEXT28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT29">TEXT29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT3">TEXT3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT30">TEXT30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT4">TEXT4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT5">TEXT5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT6">TEXT6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT7">TEXT7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT8">TEXT8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TEXT9">TEXT9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_ACTUAL_OVERTIME_WORK">TIMEPHASED_ACTUAL_OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_ACTUAL_WORK">TIMEPHASED_ACTUAL_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE_COST">TIMEPHASED_BASELINE_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE_WORK">TIMEPHASED_BASELINE_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE1_COST">TIMEPHASED_BASELINE1_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE1_WORK">TIMEPHASED_BASELINE1_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE10_COST">TIMEPHASED_BASELINE10_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE10_WORK">TIMEPHASED_BASELINE10_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE2_COST">TIMEPHASED_BASELINE2_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE2_WORK">TIMEPHASED_BASELINE2_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE3_COST">TIMEPHASED_BASELINE3_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE3_WORK">TIMEPHASED_BASELINE3_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE4_COST">TIMEPHASED_BASELINE4_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE4_WORK">TIMEPHASED_BASELINE4_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE5_COST">TIMEPHASED_BASELINE5_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE5_WORK">TIMEPHASED_BASELINE5_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE6_COST">TIMEPHASED_BASELINE6_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE6_WORK">TIMEPHASED_BASELINE6_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE7_COST">TIMEPHASED_BASELINE7_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE7_WORK">TIMEPHASED_BASELINE7_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE8_COST">TIMEPHASED_BASELINE8_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE8_WORK">TIMEPHASED_BASELINE8_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE9_COST">TIMEPHASED_BASELINE9_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_BASELINE9_WORK">TIMEPHASED_BASELINE9_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#TIMEPHASED_WORK">TIMEPHASED_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#UNAVAILABLE">UNAVAILABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#UNIQUE_ID">UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#UPDATE_NEEDED">UPDATE_NEEDED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#VAC">VAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#VARIABLE_RATE_UNITS">VARIABLE_RATE_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#WBS">WBS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#WORK">WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#WORK_CONTOUR">WORK_CONTOUR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#WORK_VARIANCE">WORK_VARIANCE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#MAX_VALUE">MAX_VALUE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getDataType--">getDataType</a></span>()</code>
<div class="block">Retrieve the data type of this field.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getFieldTypeClass--">getFieldTypeClass</a></span>()</code>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getInstance-int-">getInstance</a></span>(int&nbsp;type)</code>
<div class="block">This method takes the integer enumeration of a resource field
 and returns an appropriate class instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getName--">getName</a></span>()</code>
<div class="block">Retrieve the name of this field using the default locale.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getName-java.util.Locale-">getName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</code>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getUnitsType--">getUnitsType</a></span>()</code>
<div class="block">Retrieve the associated units field, if any.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#getValue--">getValue</a></span>()</code>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#toString--">toString</a></span>()</code>
<div class="block">Retrieves the string representation of this instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AssignmentField.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.FieldType">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.mpxj.<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></h3>
<code><a href="../../org/mpxj/FieldType.html#name--">name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START</pre>
</li>
</ul>
<a name="DURATION1_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION1_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION1_UNITS</pre>
</li>
</ul>
<a name="DURATION2_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION2_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION2_UNITS</pre>
</li>
</ul>
<a name="DURATION3_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION3_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION3_UNITS</pre>
</li>
</ul>
<a name="DURATION4_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION4_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION4_UNITS</pre>
</li>
</ul>
<a name="DURATION5_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION5_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION5_UNITS</pre>
</li>
</ul>
<a name="DURATION6_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION6_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION6_UNITS</pre>
</li>
</ul>
<a name="DURATION7_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION7_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION7_UNITS</pre>
</li>
</ul>
<a name="DURATION8_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION8_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION8_UNITS</pre>
</li>
</ul>
<a name="DURATION9_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION9_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION9_UNITS</pre>
</li>
</ul>
<a name="DURATION10_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION10_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION10_UNITS</pre>
</li>
</ul>
<a name="ACTUAL_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_COST</pre>
</li>
</ul>
<a name="ACTUAL_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_WORK</pre>
</li>
</ul>
<a name="COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST</pre>
</li>
</ul>
<a name="ASSIGNMENT_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_DELAY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ASSIGNMENT_DELAY</pre>
</li>
</ul>
<a name="VARIABLE_RATE_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VARIABLE_RATE_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> VARIABLE_RATE_UNITS</pre>
</li>
</ul>
<a name="ASSIGNMENT_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ASSIGNMENT_UNITS</pre>
</li>
</ul>
<a name="WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> WORK</pre>
</li>
</ul>
<a name="BASELINE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_START</pre>
</li>
</ul>
<a name="ACTUAL_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_START</pre>
</li>
</ul>
<a name="BASELINE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_FINISH</pre>
</li>
</ul>
<a name="ACTUAL_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_FINISH</pre>
</li>
</ul>
<a name="BASELINE_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_WORK</pre>
</li>
</ul>
<a name="OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> OVERTIME_WORK</pre>
</li>
</ul>
<a name="BASELINE_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_COST</pre>
</li>
</ul>
<a name="WORK_CONTOUR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_CONTOUR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> WORK_CONTOUR</pre>
</li>
</ul>
<a name="REMAINING_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_WORK</pre>
</li>
</ul>
<a name="LEVELING_DELAY_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVELING_DELAY_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> LEVELING_DELAY_UNITS</pre>
</li>
</ul>
<a name="LEVELING_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVELING_DELAY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> LEVELING_DELAY</pre>
</li>
</ul>
<a name="UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> UNIQUE_ID</pre>
</li>
</ul>
<a name="TASK_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TASK_UNIQUE_ID</pre>
</li>
</ul>
<a name="RESOURCE_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_UNIQUE_ID</pre>
</li>
</ul>
<a name="TIMEPHASED_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_ACTUAL_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_ACTUAL_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_ACTUAL_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_ACTUAL_OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_ACTUAL_OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_ACTUAL_OVERTIME_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE1_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE1_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE1_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE2_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE2_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE2_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE3_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE3_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE3_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE4_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE4_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE4_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE5_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE5_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE5_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE6_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE6_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE6_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE7_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE7_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE7_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE8_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE8_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE8_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE9_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE9_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE9_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE10_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE10_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE10_WORK</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE1_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE1_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE1_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE2_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE2_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE2_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE3_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE3_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE3_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE4_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE4_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE4_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE5_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE5_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE5_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE6_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE6_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE6_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE7_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE7_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE7_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE8_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE8_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE8_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE9_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE9_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE9_COST</pre>
</li>
</ul>
<a name="TIMEPHASED_BASELINE10_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TIMEPHASED_BASELINE10_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TIMEPHASED_BASELINE10_COST</pre>
</li>
</ul>
<a name="TASK_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TASK_ID</pre>
</li>
</ul>
<a name="RESOURCE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_ID</pre>
</li>
</ul>
<a name="TASK_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TASK_NAME</pre>
</li>
</ul>
<a name="RESOURCE_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_NAME</pre>
</li>
</ul>
<a name="REGULAR_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REGULAR_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REGULAR_WORK</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_OVERTIME_WORK</pre>
</li>
</ul>
<a name="REMAINING_OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_OVERTIME_WORK</pre>
</li>
</ul>
<a name="PEAK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PEAK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PEAK</pre>
</li>
</ul>
<a name="OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> OVERTIME_COST</pre>
</li>
</ul>
<a name="REMAINING_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_COST</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_OVERTIME_COST</pre>
</li>
</ul>
<a name="REMAINING_OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_OVERTIME_COST</pre>
</li>
</ul>
<a name="BCWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BCWS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BCWS</pre>
</li>
</ul>
<a name="BCWP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BCWP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BCWP</pre>
</li>
</ul>
<a name="ACWP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACWP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACWP</pre>
</li>
</ul>
<a name="SV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SV</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> SV</pre>
</li>
</ul>
<a name="COST_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST_VARIANCE</pre>
</li>
</ul>
<a name="PERCENT_WORK_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PERCENT_WORK_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PERCENT_WORK_COMPLETE</pre>
</li>
</ul>
<a name="PROJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROJECT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PROJECT</pre>
</li>
</ul>
<a name="NOTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOTES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NOTES</pre>
</li>
</ul>
<a name="CONFIRMED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONFIRMED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> CONFIRMED</pre>
</li>
</ul>
<a name="RESPONSE_PENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESPONSE_PENDING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESPONSE_PENDING</pre>
</li>
</ul>
<a name="UPDATE_NEEDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UPDATE_NEEDED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> UPDATE_NEEDED</pre>
</li>
</ul>
<a name="TEAM_STATUS_PENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEAM_STATUS_PENDING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEAM_STATUS_PENDING</pre>
</li>
</ul>
<a name="COST_RATE_TABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST_RATE_TABLE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST_RATE_TABLE</pre>
</li>
</ul>
<a name="TEXT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT1</pre>
</li>
</ul>
<a name="TEXT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT2</pre>
</li>
</ul>
<a name="TEXT3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT3</pre>
</li>
</ul>
<a name="TEXT4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT4</pre>
</li>
</ul>
<a name="TEXT5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT5</pre>
</li>
</ul>
<a name="TEXT6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT6</pre>
</li>
</ul>
<a name="TEXT7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT7</pre>
</li>
</ul>
<a name="TEXT8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT8</pre>
</li>
</ul>
<a name="TEXT9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT9</pre>
</li>
</ul>
<a name="TEXT10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT10</pre>
</li>
</ul>
<a name="START1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START1</pre>
</li>
</ul>
<a name="START2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START2</pre>
</li>
</ul>
<a name="START3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START3</pre>
</li>
</ul>
<a name="START4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START4</pre>
</li>
</ul>
<a name="START5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START5</pre>
</li>
</ul>
<a name="FINISH1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH1</pre>
</li>
</ul>
<a name="FINISH2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH2</pre>
</li>
</ul>
<a name="FINISH3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH3</pre>
</li>
</ul>
<a name="FINISH4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH4</pre>
</li>
</ul>
<a name="FINISH5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH5</pre>
</li>
</ul>
<a name="NUMBER1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER1</pre>
</li>
</ul>
<a name="NUMBER2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER2</pre>
</li>
</ul>
<a name="NUMBER3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER3</pre>
</li>
</ul>
<a name="NUMBER4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER4</pre>
</li>
</ul>
<a name="NUMBER5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER5</pre>
</li>
</ul>
<a name="DURATION1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION1</pre>
</li>
</ul>
<a name="DURATION2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION2</pre>
</li>
</ul>
<a name="DURATION3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION3</pre>
</li>
</ul>
<a name="COST1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST1</pre>
</li>
</ul>
<a name="COST2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST2</pre>
</li>
</ul>
<a name="COST3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST3</pre>
</li>
</ul>
<a name="FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG10</pre>
</li>
</ul>
<a name="FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG1</pre>
</li>
</ul>
<a name="FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG2</pre>
</li>
</ul>
<a name="FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG3</pre>
</li>
</ul>
<a name="FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG4</pre>
</li>
</ul>
<a name="FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG5</pre>
</li>
</ul>
<a name="FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG6</pre>
</li>
</ul>
<a name="FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG7</pre>
</li>
</ul>
<a name="FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG8</pre>
</li>
</ul>
<a name="FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG9</pre>
</li>
</ul>
<a name="LINKED_FIELDS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINKED_FIELDS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> LINKED_FIELDS</pre>
</li>
</ul>
<a name="OVERALLOCATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERALLOCATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> OVERALLOCATED</pre>
</li>
</ul>
<a name="TASK_SUMMARY_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_SUMMARY_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TASK_SUMMARY_NAME</pre>
</li>
</ul>
<a name="HYPERLINK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK</pre>
</li>
</ul>
<a name="HYPERLINK_ADDRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_ADDRESS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK_ADDRESS</pre>
</li>
</ul>
<a name="HYPERLINK_SUBADDRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_SUBADDRESS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK_SUBADDRESS</pre>
</li>
</ul>
<a name="HYPERLINK_HREF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_HREF</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK_HREF</pre>
</li>
</ul>
<a name="COST4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST4</pre>
</li>
</ul>
<a name="COST5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST5</pre>
</li>
</ul>
<a name="COST6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST6</pre>
</li>
</ul>
<a name="COST7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST7</pre>
</li>
</ul>
<a name="COST8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST8</pre>
</li>
</ul>
<a name="COST9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST9</pre>
</li>
</ul>
<a name="COST10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST10</pre>
</li>
</ul>
<a name="DATE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE1</pre>
</li>
</ul>
<a name="DATE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE2</pre>
</li>
</ul>
<a name="DATE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE3</pre>
</li>
</ul>
<a name="DATE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE4</pre>
</li>
</ul>
<a name="DATE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE5</pre>
</li>
</ul>
<a name="DATE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE6</pre>
</li>
</ul>
<a name="DATE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE7</pre>
</li>
</ul>
<a name="DATE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE8</pre>
</li>
</ul>
<a name="DATE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE9</pre>
</li>
</ul>
<a name="DATE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DATE10</pre>
</li>
</ul>
<a name="DURATION4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION4</pre>
</li>
</ul>
<a name="DURATION5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION5</pre>
</li>
</ul>
<a name="DURATION6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION6</pre>
</li>
</ul>
<a name="DURATION7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION7</pre>
</li>
</ul>
<a name="DURATION8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION8</pre>
</li>
</ul>
<a name="DURATION9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION9</pre>
</li>
</ul>
<a name="DURATION10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> DURATION10</pre>
</li>
</ul>
<a name="FINISH6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH6</pre>
</li>
</ul>
<a name="FINISH7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH7</pre>
</li>
</ul>
<a name="FINISH8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH8</pre>
</li>
</ul>
<a name="FINISH9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH9</pre>
</li>
</ul>
<a name="FINISH10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH10</pre>
</li>
</ul>
<a name="FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG11</pre>
</li>
</ul>
<a name="FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG12</pre>
</li>
</ul>
<a name="FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG13</pre>
</li>
</ul>
<a name="FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG14</pre>
</li>
</ul>
<a name="FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG15</pre>
</li>
</ul>
<a name="FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG16</pre>
</li>
</ul>
<a name="FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG17</pre>
</li>
</ul>
<a name="FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG18</pre>
</li>
</ul>
<a name="FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG19</pre>
</li>
</ul>
<a name="FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FLAG20</pre>
</li>
</ul>
<a name="NUMBER6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER6</pre>
</li>
</ul>
<a name="NUMBER7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER7</pre>
</li>
</ul>
<a name="NUMBER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER8</pre>
</li>
</ul>
<a name="NUMBER9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER9</pre>
</li>
</ul>
<a name="NUMBER10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER10</pre>
</li>
</ul>
<a name="NUMBER11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER11</pre>
</li>
</ul>
<a name="NUMBER12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER12</pre>
</li>
</ul>
<a name="NUMBER13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER13</pre>
</li>
</ul>
<a name="NUMBER14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER14</pre>
</li>
</ul>
<a name="NUMBER15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER15</pre>
</li>
</ul>
<a name="NUMBER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER16</pre>
</li>
</ul>
<a name="NUMBER17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER17</pre>
</li>
</ul>
<a name="NUMBER18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER18</pre>
</li>
</ul>
<a name="NUMBER19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER19</pre>
</li>
</ul>
<a name="NUMBER20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> NUMBER20</pre>
</li>
</ul>
<a name="START6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START6</pre>
</li>
</ul>
<a name="START7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START7</pre>
</li>
</ul>
<a name="START8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START8</pre>
</li>
</ul>
<a name="START9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START9</pre>
</li>
</ul>
<a name="START10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START10</pre>
</li>
</ul>
<a name="TEXT11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT11</pre>
</li>
</ul>
<a name="TEXT12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT12</pre>
</li>
</ul>
<a name="TEXT13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT13</pre>
</li>
</ul>
<a name="TEXT14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT14</pre>
</li>
</ul>
<a name="TEXT15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT15</pre>
</li>
</ul>
<a name="TEXT16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT16</pre>
</li>
</ul>
<a name="TEXT17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT17</pre>
</li>
</ul>
<a name="TEXT18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT18</pre>
</li>
</ul>
<a name="TEXT19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT19</pre>
</li>
</ul>
<a name="TEXT20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT20</pre>
</li>
</ul>
<a name="TEXT21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT21</pre>
</li>
</ul>
<a name="TEXT22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT22</pre>
</li>
</ul>
<a name="TEXT23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT23</pre>
</li>
</ul>
<a name="TEXT24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT24</pre>
</li>
</ul>
<a name="TEXT25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT25</pre>
</li>
</ul>
<a name="TEXT26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT26</pre>
</li>
</ul>
<a name="TEXT27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT27</pre>
</li>
</ul>
<a name="TEXT28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT28</pre>
</li>
</ul>
<a name="TEXT29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT29</pre>
</li>
</ul>
<a name="TEXT30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TEXT30</pre>
</li>
</ul>
<a name="INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> INDEX</pre>
</li>
</ul>
<a name="CV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> CV</pre>
</li>
</ul>
<a name="WORK_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> WORK_VARIANCE</pre>
</li>
</ul>
<a name="START_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> START_VARIANCE</pre>
</li>
</ul>
<a name="FINISH_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH_VARIANCE</pre>
</li>
</ul>
<a name="VAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VAC</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> VAC</pre>
</li>
</ul>
<a name="FIXED_MATERIAL_ASSIGNMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FIXED_MATERIAL_ASSIGNMENT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FIXED_MATERIAL_ASSIGNMENT</pre>
</li>
</ul>
<a name="RESOURCE_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_TYPE</pre>
</li>
</ul>
<a name="HYPERLINK_SCREEN_TIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_SCREEN_TIP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK_SCREEN_TIP</pre>
</li>
</ul>
<a name="WBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WBS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> WBS</pre>
</li>
</ul>
<a name="BASELINE1_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_WORK</pre>
</li>
</ul>
<a name="BASELINE1_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_COST</pre>
</li>
</ul>
<a name="BASELINE1_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_START</pre>
</li>
</ul>
<a name="BASELINE1_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_FINISH</pre>
</li>
</ul>
<a name="BASELINE2_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_WORK</pre>
</li>
</ul>
<a name="BASELINE2_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_COST</pre>
</li>
</ul>
<a name="BASELINE2_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_START</pre>
</li>
</ul>
<a name="BASELINE2_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_FINISH</pre>
</li>
</ul>
<a name="BASELINE3_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_WORK</pre>
</li>
</ul>
<a name="BASELINE3_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_COST</pre>
</li>
</ul>
<a name="BASELINE3_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_START</pre>
</li>
</ul>
<a name="BASELINE3_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_FINISH</pre>
</li>
</ul>
<a name="BASELINE4_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_WORK</pre>
</li>
</ul>
<a name="BASELINE4_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_COST</pre>
</li>
</ul>
<a name="BASELINE4_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_START</pre>
</li>
</ul>
<a name="BASELINE4_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_FINISH</pre>
</li>
</ul>
<a name="BASELINE5_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_WORK</pre>
</li>
</ul>
<a name="BASELINE5_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_COST</pre>
</li>
</ul>
<a name="BASELINE5_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_START</pre>
</li>
</ul>
<a name="BASELINE5_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_FINISH</pre>
</li>
</ul>
<a name="BASELINE6_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_WORK</pre>
</li>
</ul>
<a name="BASELINE6_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_COST</pre>
</li>
</ul>
<a name="BASELINE6_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_START</pre>
</li>
</ul>
<a name="BASELINE6_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_FINISH</pre>
</li>
</ul>
<a name="BASELINE7_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_WORK</pre>
</li>
</ul>
<a name="BASELINE7_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_COST</pre>
</li>
</ul>
<a name="BASELINE7_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_START</pre>
</li>
</ul>
<a name="BASELINE7_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_FINISH</pre>
</li>
</ul>
<a name="BASELINE8_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_WORK</pre>
</li>
</ul>
<a name="BASELINE8_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_COST</pre>
</li>
</ul>
<a name="BASELINE8_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_START</pre>
</li>
</ul>
<a name="BASELINE8_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_FINISH</pre>
</li>
</ul>
<a name="BASELINE9_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_WORK</pre>
</li>
</ul>
<a name="BASELINE9_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_COST</pre>
</li>
</ul>
<a name="BASELINE9_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_START</pre>
</li>
</ul>
<a name="BASELINE9_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_FINISH</pre>
</li>
</ul>
<a name="BASELINE10_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_WORK</pre>
</li>
</ul>
<a name="BASELINE10_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_COST</pre>
</li>
</ul>
<a name="BASELINE10_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_START</pre>
</li>
</ul>
<a name="BASELINE10_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_FINISH</pre>
</li>
</ul>
<a name="TASK_OUTLINE_NUMBER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_OUTLINE_NUMBER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> TASK_OUTLINE_NUMBER</pre>
</li>
</ul>
<a name="ENTERPRISE_COST1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST1</pre>
</li>
</ul>
<a name="ENTERPRISE_COST2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST2</pre>
</li>
</ul>
<a name="ENTERPRISE_COST3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST3</pre>
</li>
</ul>
<a name="ENTERPRISE_COST4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST4</pre>
</li>
</ul>
<a name="ENTERPRISE_COST5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST5</pre>
</li>
</ul>
<a name="ENTERPRISE_COST6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST6</pre>
</li>
</ul>
<a name="ENTERPRISE_COST7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST7</pre>
</li>
</ul>
<a name="ENTERPRISE_COST8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST8</pre>
</li>
</ul>
<a name="ENTERPRISE_COST9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST9</pre>
</li>
</ul>
<a name="ENTERPRISE_COST10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_COST10</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE1</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE2</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE3</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE4</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE5</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE6</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE7</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE8</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE9</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE10</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE11</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE12</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE13</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE14</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE15</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE16</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE17</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE18</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE19</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE20</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE21</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE22</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE23</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE24</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE25</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE26</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE27</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE28</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE29</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DATE30</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION1</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION2</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION3</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION4</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION5</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION6</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION7</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION8</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION9</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_DURATION10</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG1</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG2</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG3</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG4</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG5</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG6</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG7</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG8</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG9</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG10</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG11</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG12</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG13</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG14</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG15</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG16</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG17</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG18</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG19</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_FLAG20</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER1</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER2</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER3</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER4</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER5</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER6</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER7</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER8</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER9</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER10</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER11</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER12</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER13</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER14</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER15</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER16</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER17</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER18</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER19</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER20</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER21</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER22</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER23</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER24</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER25</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER26</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER27</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER28</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER29</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER30</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER31</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER32</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER33</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER34</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER35</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER36</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER37</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER38</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER39</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_NUMBER40</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT1</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT2</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT3</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT4</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT5</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT6</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT7</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT8</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT9</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT10</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT11</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT12</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT13</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT14</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT15</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT16</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT17</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT18</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT19</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT20</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT21</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT22</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT23</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT24</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT25</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT26</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT27</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT28</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT29</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT30</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT31</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT32</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT33</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT34</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT35</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT36</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT37</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT38</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT39</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEXT40</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE1</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE2</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE3</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE4</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE5</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE6</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE7</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE8</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE9</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE10</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE11</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE12</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE13</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE14</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE15</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE16</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE17</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE18</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE19</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE20</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE21</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE22</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE23</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE24</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE25</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE26</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE27</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE28</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_OUTLINE_CODE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_OUTLINE_CODE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_OUTLINE_CODE29</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_RBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_RBS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_RBS</pre>
</li>
</ul>
<a name="RESOURCE_REQUEST_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_REQUEST_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_REQUEST_TYPE</pre>
</li>
</ul>
<a name="ENTERPRISE_TEAM_MEMBER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEAM_MEMBER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_TEAM_MEMBER</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE20</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE21</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE22</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE23</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE24</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE25</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE26</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE27</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE28</pre>
</li>
</ul>
<a name="ENTERPRISE_RESOURCE_MULTI_VALUE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_RESOURCE_MULTI_VALUE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ENTERPRISE_RESOURCE_MULTI_VALUE29</pre>
</li>
</ul>
<a name="ACTUAL_WORK_PROTECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK_PROTECTED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_WORK_PROTECTED</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_WORK_PROTECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_WORK_PROTECTED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ACTUAL_OVERTIME_WORK_PROTECTED</pre>
</li>
</ul>
<a name="CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> CREATED</pre>
</li>
</ul>
<a name="GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> GUID</pre>
</li>
</ul>
<a name="ASSIGNMENT_TASK_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_TASK_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ASSIGNMENT_TASK_GUID</pre>
</li>
</ul>
<a name="ASSIGNMENT_RESOURCE_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_RESOURCE_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ASSIGNMENT_RESOURCE_GUID</pre>
</li>
</ul>
<a name="SUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUMMARY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> SUMMARY</pre>
</li>
</ul>
<a name="OWNER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OWNER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> OWNER</pre>
</li>
</ul>
<a name="BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BUDGET_WORK</pre>
</li>
</ul>
<a name="BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE1_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE1_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE1_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE2_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE2_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE2_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE3_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE3_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE3_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE4_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE4_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE4_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE5_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE5_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE5_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE6_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE6_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE6_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE7_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE7_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE7_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE8_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE8_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE8_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE9_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE9_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE9_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE10_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE10_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> BASELINE10_BUDGET_COST</pre>
</li>
</ul>
<a name="UNAVAILABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNAVAILABLE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> UNAVAILABLE</pre>
</li>
</ul>
<a name="HYPERLINK_DATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_DATA</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> HYPERLINK_DATA</pre>
</li>
</ul>
<a name="RESUME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESUME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESUME</pre>
</li>
</ul>
<a name="STOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> STOP</pre>
</li>
</ul>
<a name="PLANNED_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PLANNED_WORK</pre>
</li>
</ul>
<a name="PLANNED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PLANNED_COST</pre>
</li>
</ul>
<a name="PLANNED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PLANNED_START</pre>
</li>
</ul>
<a name="PLANNED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> PLANNED_FINISH</pre>
</li>
</ul>
<a name="RATE_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RATE_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RATE_INDEX</pre>
</li>
</ul>
<a name="ROLE_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROLE_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> ROLE_UNIQUE_ID</pre>
</li>
</ul>
<a name="OVERRIDE_RATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERRIDE_RATE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> OVERRIDE_RATE</pre>
</li>
</ul>
<a name="RATE_SOURCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RATE_SOURCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RATE_SOURCE</pre>
</li>
</ul>
<a name="CALCULATE_COSTS_FROM_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALCULATE_COSTS_FROM_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> CALCULATE_COSTS_FROM_UNITS</pre>
</li>
</ul>
<a name="COST_ACCOUNT_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST_ACCOUNT_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> COST_ACCOUNT_UNIQUE_ID</pre>
</li>
</ul>
<a name="REMAINING_EARLY_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_EARLY_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_EARLY_START</pre>
</li>
</ul>
<a name="REMAINING_EARLY_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_EARLY_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_EARLY_FINISH</pre>
</li>
</ul>
<a name="REMAINING_LATE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_LATE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_LATE_START</pre>
</li>
</ul>
<a name="REMAINING_LATE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_LATE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_LATE_FINISH</pre>
</li>
</ul>
<a name="REMAINING_ASSIGNMENT_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_ASSIGNMENT_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> REMAINING_ASSIGNMENT_UNITS</pre>
</li>
</ul>
<a name="RESOURCE_ASSIGNMENT_CODE_VALUES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ASSIGNMENT_CODE_VALUES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> RESOURCE_ASSIGNMENT_CODE_VALUES</pre>
</li>
</ul>
<a name="FINISH">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a> FINISH</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MAX_VALUE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MAX_VALUE</h4>
<pre>public static final&nbsp;int MAX_VALUE</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (AssignmentField c : AssignmentField.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&nbsp;valueOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getFieldTypeClass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldTypeClass</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a>&nbsp;getFieldTypeClass()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">FieldType</a></code></span></div>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">getFieldTypeClass</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field type class</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName--">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the default locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="getName-java.util.Locale-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>locale</code> - target locale</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html#getValue--">MpxjEnum</a></code></span></div>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/MpxjEnum.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum value</dd>
</dl>
</li>
</ul>
<a name="getDataType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;getDataType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getDataType--">FieldType</a></code></span></div>
<div class="block">Retrieve the data type of this field.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getDataType--">getDataType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>data type</dd>
</dl>
</li>
</ul>
<a name="getUnitsType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitsType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;getUnitsType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getUnitsType--">FieldType</a></code></span></div>
<div class="block">Retrieve the associated units field, if any.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getUnitsType--">getUnitsType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>units field</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Retrieves the string representation of this instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representation</dd>
</dl>
</li>
</ul>
<a name="getInstance-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a>&nbsp;getInstance(int&nbsp;type)</pre>
<div class="block">This method takes the integer enumeration of a resource field
 and returns an appropriate class instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - integer resource field enumeration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ResourceField instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AssignmentField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/Availability.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/AssignmentField.html" target="_top">Frames</a></li>
<li><a href="AssignmentField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
