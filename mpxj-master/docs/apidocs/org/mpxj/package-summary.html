<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.mpxj (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.mpxj (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../org/mpxj/asta/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.mpxj</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></td>
<td class="colLast">
<div class="block">Classes implementing this interface manage population of baseline attributes
 in one schedule by comparing it to another schedule.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ChildResourceContainer.html" title="interface in org.mpxj">ChildResourceContainer</a></td>
<td class="colLast">
<div class="block">Interface implemented by classes which have child resources.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ChildTaskContainer.html" title="interface in org.mpxj">ChildTaskContainer</a></td>
<td class="colLast">
<div class="block">Interface implemented by classes which have child tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Code.html" title="interface in org.mpxj">Code</a></td>
<td class="colLast">
<div class="block">Interface implemented by classes representing Primavera P6 codes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CodeValue.html" title="interface in org.mpxj">CodeValue</a></td>
<td class="colLast">
<div class="block">Implemented by classes which represent a value forming part of a Primavera P6 code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a></td>
<td class="colLast">
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></td>
<td class="colLast">
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/HasCharset.html" title="interface in org.mpxj">HasCharset</a></td>
<td class="colLast">
<div class="block">This interface is implemented by reader classes which allow
 a Charset to be specified before reading.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></td>
<td class="colLast">
<div class="block">This interface defines the common features of enums used by MPXJ.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a></td>
<td class="colLast">
<div class="block">Implemented by entities which can be identified by an ID.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a></td>
<td class="colLast">
<div class="block">Implemented by entities which can be identified by a mutable Unique ID.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></td>
<td class="colLast">
<div class="block">Implemented by entities which can be identified by a Unique ID.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></td>
<td class="colLast">
<div class="block">Classes implementing this interface represent a period of time
 between a start LocalDateTime and a finish LocalDateTime.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TimephasedCostContainer.html" title="interface in org.mpxj">TimephasedCostContainer</a></td>
<td class="colLast">
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TimephasedWorkContainer.html" title="interface in org.mpxj">TimephasedWorkContainer</a></td>
<td class="colLast">
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></td>
<td class="colLast">
<div class="block">Classes implementing this interface provide access to the defaults used
 when converting duration time units.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></td>
<td class="colLast">
<div class="block">Classes implementing this interface provide a method which allows
 unique ID object sequences to be retrieved for the requested class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/View.html" title="interface in org.mpxj">View</a></td>
<td class="colLast">
<div class="block">This interface represents a view of a set of project data that has been
 instantiated within an MS Project file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/AbstractBaselineStrategy.html" title="class in org.mpxj">AbstractBaselineStrategy</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">Implementation of common functionality for the FieldContainer interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCode.html" title="class in org.mpxj">ActivityCode</a></td>
<td class="colLast">
<div class="block">Activity code type definition, contains a list of the valid
 values for this activity code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCode.Builder.html" title="class in org.mpxj">ActivityCode.Builder</a></td>
<td class="colLast">
<div class="block">ActivityCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCodeContainer.html" title="class in org.mpxj">ActivityCodeContainer</a></td>
<td class="colLast">
<div class="block">Container for activity code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a></td>
<td class="colLast">
<div class="block">Represents an individual activity code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCodeValue.Builder.html" title="class in org.mpxj">ActivityCodeValue.Builder</a></td>
<td class="colLast">
<div class="block">ActivityCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Availability.html" title="class in org.mpxj">Availability</a></td>
<td class="colLast">
<div class="block">This class represents a row from a resource's availability table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/AvailabilityTable.html" title="class in org.mpxj">AvailabilityTable</a></td>
<td class="colLast">
<div class="block">This class represents a resource's availability table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Column.html" title="class in org.mpxj">Column</a></td>
<td class="colLast">
<div class="block">This class represents a column in an MS Project table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CostAccount.html" title="class in org.mpxj">CostAccount</a></td>
<td class="colLast">
<div class="block">Cost account definition.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CostAccount.Builder.html" title="class in org.mpxj">CostAccount.Builder</a></td>
<td class="colLast">
<div class="block">CostAccount builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CostAccountContainer.html" title="class in org.mpxj">CostAccountContainer</a></td>
<td class="colLast">
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a></td>
<td class="colLast">
<div class="block">This class represents a resource's cost rate table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CostRateTableEntry.html" title="class in org.mpxj">CostRateTableEntry</a></td>
<td class="colLast">
<div class="block">This class represents a row from a resource's cost rate table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Currency.html" title="class in org.mpxj">Currency</a></td>
<td class="colLast">
<div class="block">Represents a currency.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Currency.Builder.html" title="class in org.mpxj">Currency.Builder</a></td>
<td class="colLast">
<div class="block">Currency builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CurrencyContainer.html" title="class in org.mpxj">CurrencyContainer</a></td>
<td class="colLast">
<div class="block">Represents the currencies available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CustomField.html" title="class in org.mpxj">CustomField</a></td>
<td class="colLast">
<div class="block">Configuration detail for a field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CustomFieldContainer.html" title="class in org.mpxj">CustomFieldContainer</a></td>
<td class="colLast">
<div class="block">Container holding configuration details for all custom fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CustomFieldLookupTable.html" title="class in org.mpxj">CustomFieldLookupTable</a></td>
<td class="colLast">
<div class="block">Lookup table definition for a custom field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CustomFieldValueMask.html" title="class in org.mpxj">CustomFieldValueMask</a></td>
<td class="colLast">
<div class="block">One element of the mask used to define the structured content of a custom field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/DataLink.html" title="class in org.mpxj">DataLink</a></td>
<td class="colLast">
<div class="block">Represents a link between two fields, either in the same project or across projects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/DataLinkContainer.html" title="class in org.mpxj">DataLinkContainer</a></td>
<td class="colLast">
<div class="block">Manages the data link definitions belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/DefaultBaselineStrategy.html" title="class in org.mpxj">DefaultBaselineStrategy</a></td>
<td class="colLast">
<div class="block">Handles setting baseline fields in one project using values read from another project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></td>
<td class="colLast">
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/EPS.html" title="class in org.mpxj">EPS</a></td>
<td class="colLast">
<div class="block">Represents the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/EpsNode.html" title="class in org.mpxj">EpsNode</a></td>
<td class="colLast">
<div class="block">Represents a node in the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/EpsProjectNode.html" title="class in org.mpxj">EpsProjectNode</a></td>
<td class="colLast">
<div class="block">Represents a project node in the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/EventManager.html" title="class in org.mpxj">EventManager</a></td>
<td class="colLast">
<div class="block">Provides subscriptions to events raised when project files are written and read.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ExpenseCategory.html" title="class in org.mpxj">ExpenseCategory</a></td>
<td class="colLast">
<div class="block">Expense category definition.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ExpenseCategory.Builder.html" title="class in org.mpxj">ExpenseCategory.Builder</a></td>
<td class="colLast">
<div class="block">ExpenseCategory builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ExpenseCategoryContainer.html" title="class in org.mpxj">ExpenseCategoryContainer</a></td>
<td class="colLast">
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ExpenseItem.html" title="class in org.mpxj">ExpenseItem</a></td>
<td class="colLast">
<div class="block">Expense item definition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ExpenseItem.Builder.html" title="class in org.mpxj">ExpenseItem.Builder</a></td>
<td class="colLast">
<div class="block">Expense item builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a></td>
<td class="colLast">
<div class="block">This class represents a filter which may be applied to a
 task or resource view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/FilterContainer.html" title="class in org.mpxj">FilterContainer</a></td>
<td class="colLast">
<div class="block">Manages filter definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/GenericCriteria.html" title="class in org.mpxj">GenericCriteria</a></td>
<td class="colLast">
<div class="block">This class represents the criteria used as part of an evaluation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/GenericCriteriaPrompt.html" title="class in org.mpxj">GenericCriteriaPrompt</a></td>
<td class="colLast">
<div class="block">Represents a prompt to the user as part of filter criteria.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/GraphicalIndicator.html" title="class in org.mpxj">GraphicalIndicator</a></td>
<td class="colLast">
<div class="block">This class represents the set of information which defines how
 a Graphical Indicator will be presented for a single column in
 a table within Microsoft Project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/GraphicalIndicatorCriteria.html" title="class in org.mpxj">GraphicalIndicatorCriteria</a></td>
<td class="colLast">
<div class="block">This class represents the criteria used to determine if a graphical
 indicator is displayed in place of an attribute value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Group.html" title="class in org.mpxj">Group</a></td>
<td class="colLast">
<div class="block">This class represents the definition of the grouping used
 to organise data in a view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/GroupClause.html" title="class in org.mpxj">GroupClause</a></td>
<td class="colLast">
<div class="block">This class represents a clause from a definition of a group.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/GroupContainer.html" title="class in org.mpxj">GroupContainer</a></td>
<td class="colLast">
<div class="block">Manages the group definitions belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/HtmlNotes.html" title="class in org.mpxj">HtmlNotes</a></td>
<td class="colLast">
<div class="block">Represents notes formatted as RTF.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ListWithCallbacks.html" title="class in org.mpxj">ListWithCallbacks</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">Class implementing a list interface, backed by an ArrayList instance with callbacks
 which can be overridden by subclasses for notification of added and removed items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/LocalDateRange.html" title="class in org.mpxj">LocalDateRange</a></td>
<td class="colLast">
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/LocalDateTimeRange.html" title="class in org.mpxj">LocalDateTimeRange</a></td>
<td class="colLast">
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/LocaleData.html" title="class in org.mpxj">LocaleData</a></td>
<td class="colLast">
<div class="block">Locale data for MPXJ.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/LocaleData_en.html" title="class in org.mpxj">LocaleData_en</a></td>
<td class="colLast">
<div class="block">This class defines the English resources required by MPXJ.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/LocalTimeRange.html" title="class in org.mpxj">LocalTimeRange</a></td>
<td class="colLast">
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a></td>
<td class="colLast">
<div class="block">Represents a location, use to tag projects, resources and activities.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Location.Builder.html" title="class in org.mpxj">Location.Builder</a></td>
<td class="colLast">
<div class="block">Location builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/LocationContainer.html" title="class in org.mpxj">LocationContainer</a></td>
<td class="colLast">
<div class="block">Represents the locations available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ManuallyScheduledTaskCalendar.html" title="class in org.mpxj">ManuallyScheduledTaskCalendar</a></td>
<td class="colLast">
<div class="block">This class represents a Calendar Definition record for an MPP Manually Scheduled task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/MPXJ.html" title="class in org.mpxj">MPXJ</a></td>
<td class="colLast">
<div class="block">Class containing globally relevant constants.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a></td>
<td class="colLast">
<div class="block">Represents plain text notes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/NotesTopic.html" title="class in org.mpxj">NotesTopic</a></td>
<td class="colLast">
<div class="block">Represents a topic, used by P6 to organise notes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/NotesTopic.Builder.html" title="class in org.mpxj">NotesTopic.Builder</a></td>
<td class="colLast">
<div class="block">NotesTopic builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/NotesTopicContainer.html" title="class in org.mpxj">NotesTopicContainer</a></td>
<td class="colLast">
<div class="block">Represents the notes topics available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ParentNotes.html" title="class in org.mpxj">ParentNotes</a></td>
<td class="colLast">
<div class="block">Represents a note which is composed of one or more child notes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a></td>
<td class="colLast">
<div class="block">This class is used to represent a priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></td>
<td class="colLast">
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendarContainer.html" title="class in org.mpxj">ProjectCalendarContainer</a></td>
<td class="colLast">
<div class="block">Manages the collection of calendars belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendarDays.html" title="class in org.mpxj">ProjectCalendarDays</a></td>
<td class="colLast">
<div class="block">This class represents a basic working week, with no exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></td>
<td class="colLast">
<div class="block">This class represents instances of Calendar Exception records from
 an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendarHours.html" title="class in org.mpxj">ProjectCalendarHours</a></td>
<td class="colLast">
<div class="block">This class is used to represent the records in an MPX file that define
 working hours in a calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCalendarWeek.html" title="class in org.mpxj">ProjectCalendarWeek</a></td>
<td class="colLast">
<div class="block">This class represents a basic working week, with no exceptions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCode.html" title="class in org.mpxj">ProjectCode</a></td>
<td class="colLast">
<div class="block">Project code type definition, contains a list of the valid
 values for this project code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCode.Builder.html" title="class in org.mpxj">ProjectCode.Builder</a></td>
<td class="colLast">
<div class="block">ProjectCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCodeContainer.html" title="class in org.mpxj">ProjectCodeContainer</a></td>
<td class="colLast">
<div class="block">Container for project code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCodeValue.html" title="class in org.mpxj">ProjectCodeValue</a></td>
<td class="colLast">
<div class="block">Represents an individual project code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectCodeValue.Builder.html" title="class in org.mpxj">ProjectCodeValue.Builder</a></td>
<td class="colLast">
<div class="block">ProjectCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectConfig.html" title="class in org.mpxj">ProjectConfig</a></td>
<td class="colLast">
<div class="block">Container for configuration details used to control the behaviour of the ProjectFile class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a>&lt;T extends <a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a>&gt;</td>
<td class="colLast">
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectEntityWithIDContainer.html" title="class in org.mpxj">ProjectEntityWithIDContainer</a>&lt;T extends <a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a> &amp; <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;&gt;</td>
<td class="colLast">
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></td>
<td class="colLast">
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectFileSharedData.html" title="class in org.mpxj">ProjectFileSharedData</a></td>
<td class="colLast">
<div class="block">Implements a container for common data which can be shared across multiple ProjectFile instances.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectProperties.html" title="class in org.mpxj">ProjectProperties</a></td>
<td class="colLast">
<div class="block">This class represents a collection of properties relevant to the whole project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></td>
<td class="colLast">
<div class="block">This class represents a currency rate per period of time (for example $10/h)
 as found in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RecurringData.html" title="class in org.mpxj">RecurringData</a></td>
<td class="colLast">
<div class="block">This class provides a description of a recurring event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RecurringTask.html" title="class in org.mpxj">RecurringTask</a></td>
<td class="colLast">
<div class="block">This class represents the Recurring Task Record as found in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a></td>
<td class="colLast">
<div class="block">This class represents the relationship between two tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Relation.Builder.html" title="class in org.mpxj">Relation.Builder</a></td>
<td class="colLast">
<div class="block">Relation builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RelationContainer.html" title="class in org.mpxj">RelationContainer</a></td>
<td class="colLast">
<div class="block">Represents Relation instances from the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></td>
<td class="colLast">
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a></td>
<td class="colLast">
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentCode.html" title="class in org.mpxj">ResourceAssignmentCode</a></td>
<td class="colLast">
<div class="block">ResourceAssignment code type definition, contains a list of the valid
 values for this assignment code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentCode.Builder.html" title="class in org.mpxj">ResourceAssignmentCode.Builder</a></td>
<td class="colLast">
<div class="block">ResourceAssignmentCode builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentCodeContainer.html" title="class in org.mpxj">ResourceAssignmentCodeContainer</a></td>
<td class="colLast">
<div class="block">Container for assignment code definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentCodeValue.html" title="class in org.mpxj">ResourceAssignmentCodeValue</a></td>
<td class="colLast">
<div class="block">Represents an individual assignment code value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentCodeValue.Builder.html" title="class in org.mpxj">ResourceAssignmentCodeValue.Builder</a></td>
<td class="colLast">
<div class="block">ResourceAssignmentCodeValue builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentContainer.html" title="class in org.mpxj">ResourceAssignmentContainer</a></td>
<td class="colLast">
<div class="block">Manages the collection of resource assignments belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceAssignmentWorkgroupFields.html" title="class in org.mpxj">ResourceAssignmentWorkgroupFields</a></td>
<td class="colLast">
<div class="block">This class represents a resource assignment workgroup fields record
 from an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceCode.html" title="class in org.mpxj">ResourceCode</a></td>
<td class="colLast">
<div class="block">Resource code type definition, contains a list of the valid
 values for this resource code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceCode.Builder.html" title="class in org.mpxj">ResourceCode.Builder</a></td>
<td class="colLast">
<div class="block">ResourceCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceCodeContainer.html" title="class in org.mpxj">ResourceCodeContainer</a></td>
<td class="colLast">
<div class="block">Container for resource code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceCodeValue.html" title="class in org.mpxj">ResourceCodeValue</a></td>
<td class="colLast">
<div class="block">Represents an individual resource code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceCodeValue.Builder.html" title="class in org.mpxj">ResourceCodeValue.Builder</a></td>
<td class="colLast">
<div class="block">ResourceCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceContainer.html" title="class in org.mpxj">ResourceContainer</a></td>
<td class="colLast">
<div class="block">Manages the collection of resources belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RoleCode.html" title="class in org.mpxj">RoleCode</a></td>
<td class="colLast">
<div class="block">Role code type definition, contains a list of the valid
 values for this role code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RoleCode.Builder.html" title="class in org.mpxj">RoleCode.Builder</a></td>
<td class="colLast">
<div class="block">RoleCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RoleCodeContainer.html" title="class in org.mpxj">RoleCodeContainer</a></td>
<td class="colLast">
<div class="block">Container for role code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RoleCodeValue.html" title="class in org.mpxj">RoleCodeValue</a></td>
<td class="colLast">
<div class="block">Represents an individual role code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RoleCodeValue.Builder.html" title="class in org.mpxj">RoleCodeValue.Builder</a></td>
<td class="colLast">
<div class="block">RoleCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RtfNotes.html" title="class in org.mpxj">RtfNotes</a></td>
<td class="colLast">
<div class="block">Represents notes formatted as RTF.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a></td>
<td class="colLast">
<div class="block">Represents a Resource Shift.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Shift.Builder.html" title="class in org.mpxj">Shift.Builder</a></td>
<td class="colLast">
<div class="block">Shift builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ShiftContainer.html" title="class in org.mpxj">ShiftContainer</a></td>
<td class="colLast">
<div class="block">Represents the shifts available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ShiftPeriod.html" title="class in org.mpxj">ShiftPeriod</a></td>
<td class="colLast">
<div class="block">Represents a Resource Shift Period.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ShiftPeriod.Builder.html" title="class in org.mpxj">ShiftPeriod.Builder</a></td>
<td class="colLast">
<div class="block">ShiftPeriod builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ShiftPeriodContainer.html" title="class in org.mpxj">ShiftPeriodContainer</a></td>
<td class="colLast">
<div class="block">Represents the shift periods available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/Step.html" title="class in org.mpxj">Step</a></td>
<td class="colLast">
<div class="block">Represents an activity step.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Step.Builder.html" title="class in org.mpxj">Step.Builder</a></td>
<td class="colLast">
<div class="block">Step builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/StructuredNotes.html" title="class in org.mpxj">StructuredNotes</a></td>
<td class="colLast">
<div class="block">Represents a note which belongs to a topic.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Table.html" title="class in org.mpxj">Table</a></td>
<td class="colLast">
<div class="block">This class represents the definition of a table of data from an MPP file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TableContainer.html" title="class in org.mpxj">TableContainer</a></td>
<td class="colLast">
<div class="block">Manages the table definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></td>
<td class="colLast">
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TaskContainer.html" title="class in org.mpxj">TaskContainer</a></td>
<td class="colLast">
<div class="block">Manages the collection of tasks belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TemporaryCalendar.html" title="class in org.mpxj">TemporaryCalendar</a></td>
<td class="colLast">
<div class="block">Represents a temporary calendar which is not intended to form part of the
 schedule.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a></td>
<td class="colLast">
<div class="block">Represents timephased cost.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TimephasedItem.html" title="class in org.mpxj">TimephasedItem</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">This class represents an amount, spread over a period of time.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a></td>
<td class="colLast">
<div class="block">Represents timephased work.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/UnitOfMeasure.html" title="class in org.mpxj">UnitOfMeasure</a></td>
<td class="colLast">
<div class="block">Class representing a unit of measure.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/UnitOfMeasure.Builder.html" title="class in org.mpxj">UnitOfMeasure.Builder</a></td>
<td class="colLast">
<div class="block">Unit of measure builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/UnitOfMeasureContainer.html" title="class in org.mpxj">UnitOfMeasureContainer</a></td>
<td class="colLast">
<div class="block">Represents units of measure available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/UserDefinedField.html" title="class in org.mpxj">UserDefinedField</a></td>
<td class="colLast">
<div class="block">Represents a user defined field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/UserDefinedField.Builder.html" title="class in org.mpxj">UserDefinedField.Builder</a></td>
<td class="colLast">
<div class="block">User defined field builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/UserDefinedFieldContainer.html" title="class in org.mpxj">UserDefinedFieldContainer</a></td>
<td class="colLast">
<div class="block">Manages the collection of user defined fields belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ViewContainer.html" title="class in org.mpxj">ViewContainer</a></td>
<td class="colLast">
<div class="block">Manages the sub projects belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ViewState.html" title="class in org.mpxj">ViewState</a></td>
<td class="colLast">
<div class="block">This class represents the state of a view which has been saved
 as part of a project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated work contour values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/WorkContourContainer.html" title="class in org.mpxj">WorkContourContainer</a></td>
<td class="colLast">
<div class="block">Represents the work contours (resource curves) used by the current project.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></td>
<td class="colLast">
<div class="block">This class is used to represent an accrue type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityCodeScope.html" title="enum in org.mpxj">ActivityCodeScope</a></td>
<td class="colLast">
<div class="block">Represents the scope of an activity code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj">ActivityStatus</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj">ActivityType</a></td>
<td class="colLast">
<div class="block">P6/PPX Activity type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a></td>
<td class="colLast">
<div class="block">Instances of this type represent Assignment fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a></td>
<td class="colLast">
<div class="block">Enumeration representing booking types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CalendarType.html" title="enum in org.mpxj">CalendarType</a></td>
<td class="colLast">
<div class="block">P6 Calendar Types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CodePage.html" title="enum in org.mpxj">CodePage</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated code page values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ConstraintField.html" title="enum in org.mpxj">ConstraintField</a></td>
<td class="colLast">
<div class="block">Instances of this type represent constraint fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a></td>
<td class="colLast">
<div class="block">This class is used to represent a constraint type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CriticalActivityType.html" title="enum in org.mpxj">CriticalActivityType</a></td>
<td class="colLast">
<div class="block">Determines how the critical flag is set for tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/CustomFieldValueDataType.html" title="enum in org.mpxj">CustomFieldValueDataType</a></td>
<td class="colLast">
<div class="block">Enumeration used  by custom field value items to represent their data type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a></td>
<td class="colLast">
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated date order values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/DayType.html" title="enum in org.mpxj">DayType</a></td>
<td class="colLast">
<div class="block">This class is used to represent the day type used by the project calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated earned value method values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a></td>
<td class="colLast">
<div class="block">Represents the type of entity to which a FieldType instance can belong.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated file version values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj">PercentCompleteType</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated date format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectField.html" title="enum in org.mpxj">ProjectField</a></td>
<td class="colLast">
<div class="block">Instances of this type represent project properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated time format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RateSource.html" title="enum in org.mpxj">RateSource</a></td>
<td class="colLast">
<div class="block">Represents the source of cost rate for a resource assignment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RecurrenceType.html" title="enum in org.mpxj">RecurrenceType</a></td>
<td class="colLast">
<div class="block">Represents the recurrence type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/RelationshipLagCalendar.html" title="enum in org.mpxj">RelationshipLagCalendar</a></td>
<td class="colLast">
<div class="block">Represents the calendar to be used when making date calculations
 using the relationship lag value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/RelationType.html" title="enum in org.mpxj">RelationType</a></td>
<td class="colLast">
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceField.html" title="enum in org.mpxj">ResourceField</a></td>
<td class="colLast">
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated resource request type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated schedule from values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/SchedulingProgressedActivities.html" title="enum in org.mpxj">SchedulingProgressedActivities</a></td>
<td class="colLast">
<div class="block">Represents the method used to schedule progressed activities.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/SkillLevel.html" title="enum in org.mpxj">SkillLevel</a></td>
<td class="colLast">
<div class="block">Represents the skill level of a resource
 when assigned to a role.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a></td>
<td class="colLast">
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a></td>
<td class="colLast">
<div class="block">Represents task mode values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a></td>
<td class="colLast">
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TestOperator.html" title="enum in org.mpxj">TestOperator</a></td>
<td class="colLast">
<div class="block">This class represents the set of operators used to perform a test
 between two or more operands.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></td>
<td class="colLast">
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/TotalSlackCalculationType.html" title="enum in org.mpxj">TotalSlackCalculationType</a></td>
<td class="colLast">
<div class="block">Enum representing the different calculation types which can be used to determine total slack.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../org/mpxj/ViewType.html" title="enum in org.mpxj">ViewType</a></td>
<td class="colLast">
<div class="block">This class represents the enumeration of the valid types of view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a></td>
<td class="colLast">
<div class="block">Instances of this class represent enumerated work group values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></td>
<td class="colLast">
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../../org/mpxj/asta/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
