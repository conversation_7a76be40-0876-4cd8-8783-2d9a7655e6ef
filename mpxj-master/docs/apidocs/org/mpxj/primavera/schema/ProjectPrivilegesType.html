<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectPrivilegesType (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectPrivilegesType (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectPrivilegesType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectPrivilegesType.html" target="_top">Frames</a></li>
<li><a href="ProjectPrivilegesType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ProjectPrivilegesType" class="title">Class ProjectPrivilegesType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ProjectPrivilegesType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ProjectPrivilegesType</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for ProjectPrivilegesType complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType name="ProjectPrivilegesType"&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="AddEditDeleteRisks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEPSActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditActivitiesExceptRelationships" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditProjectLevelLayouts" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteTemplateDocuments" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddProjectActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AdministerProjectExternalApplications" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ApplyActuals" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ApproveTimesheetsAsProjectManager" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AssignProjectBaselines" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="CheckInAndCheckOutProjects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddProjects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditWorkgroups" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteEPSActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteActivities" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteProjectActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteProjectDataWithTimesheetActuals" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteProjects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteWorkgroups" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditActivityId" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditCommittedFlagForResourcePlanning" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditEPSActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteEPSExceptCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditEPSCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditFuturePeriods" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditPeriodPerformance" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditProjectActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteActivityRelationships" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteProjectCalendars" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditContractManagementProjectLink" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditProjectDetailsExceptCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteExpenses" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditProjectReports" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteIssuesAndIssueThreshold" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteWBSExceptCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditWBSCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteWorkProductsAndDocuments" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteResourceAssignmentsForResourcePlanning" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteRoleAssignmentsForResourcePlanning" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ImportAndViewContractManagerData" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="LevelResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteProjectBaselines" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditWorkspaceAndWorkgroupPreferences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="MonitorProjectThresholds" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditActivityResourceRequests" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="PublishProjectWebsite" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="RunBaselineUpdate" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="RunGlobalChange" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ScheduleProjects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="StorePeriodPerformance" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="SummarizeProjects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ViewProjectCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteIssuesandIssueThresholds" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AllowIntegrationwithERPSystem" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditPublicationPriority" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditActivitiesExceptRelationships">addEditActivitiesExceptRelationships</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditActivityResourceRequests">addEditActivityResourceRequests</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteActivityRelationships">addEditDeleteActivityRelationships</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteEPSExceptCostsAndFinancials">addEditDeleteEPSExceptCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteExpenses">addEditDeleteExpenses</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteIssuesAndIssueThreshold">addEditDeleteIssuesAndIssueThreshold</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteIssuesandIssueThresholds">addEditDeleteIssuesandIssueThresholds</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteProjectBaselines">addEditDeleteProjectBaselines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteProjectCalendars">addEditDeleteProjectCalendars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteResourceAssignmentsForResourcePlanning">addEditDeleteResourceAssignmentsForResourcePlanning</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteRisks">addEditDeleteRisks</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteRoleAssignmentsForResourcePlanning">addEditDeleteRoleAssignmentsForResourcePlanning</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteTemplateDocuments">addEditDeleteTemplateDocuments</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteWBSExceptCostsAndFinancials">addEditDeleteWBSExceptCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditDeleteWorkProductsAndDocuments">addEditDeleteWorkProductsAndDocuments</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditProjectLevelLayouts">addEditProjectLevelLayouts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEditWorkgroups">addEditWorkgroups</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addEPSActivityCodes">addEPSActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addProjectActivityCodes">addProjectActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#addProjects">addProjects</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#administerProjectExternalApplications">administerProjectExternalApplications</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#allowIntegrationwithERPSystem">allowIntegrationwithERPSystem</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#applyActuals">applyActuals</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#approveTimesheetsAsProjectManager">approveTimesheetsAsProjectManager</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#assignProjectBaselines">assignProjectBaselines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#checkInAndCheckOutProjects">checkInAndCheckOutProjects</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteActivities">deleteActivities</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteEPSActivityCodes">deleteEPSActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteProjectActivityCodes">deleteProjectActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteProjectDataWithTimesheetActuals">deleteProjectDataWithTimesheetActuals</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteProjects">deleteProjects</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#deleteWorkgroups">deleteWorkgroups</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editActivityId">editActivityId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editCommittedFlagForResourcePlanning">editCommittedFlagForResourcePlanning</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editContractManagementProjectLink">editContractManagementProjectLink</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editEPSActivityCodes">editEPSActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editEPSCostsAndFinancials">editEPSCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editFuturePeriods">editFuturePeriods</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editPeriodPerformance">editPeriodPerformance</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editProjectActivityCodes">editProjectActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editProjectDetailsExceptCostsAndFinancials">editProjectDetailsExceptCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editProjectReports">editProjectReports</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editPublicationPriority">editPublicationPriority</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editWBSCostsAndFinancials">editWBSCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#editWorkspaceAndWorkgroupPreferences">editWorkspaceAndWorkgroupPreferences</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#importAndViewContractManagerData">importAndViewContractManagerData</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#levelResources">levelResources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#monitorProjectThresholds">monitorProjectThresholds</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#publishProjectWebsite">publishProjectWebsite</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#runBaselineUpdate">runBaselineUpdate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#runGlobalChange">runGlobalChange</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#scheduleProjects">scheduleProjects</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#storePeriodPerformance">storePeriodPerformance</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#summarizeProjects">summarizeProjects</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#viewProjectCostsAndFinancials">viewProjectCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#ProjectPrivilegesType--">ProjectPrivilegesType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditActivitiesExceptRelationships--">isAddEditActivitiesExceptRelationships</a></span>()</code>
<div class="block">Gets the value of the addEditActivitiesExceptRelationships property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditActivityResourceRequests--">isAddEditActivityResourceRequests</a></span>()</code>
<div class="block">Gets the value of the addEditActivityResourceRequests property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteActivityRelationships--">isAddEditDeleteActivityRelationships</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteActivityRelationships property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteEPSExceptCostsAndFinancials--">isAddEditDeleteEPSExceptCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteEPSExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteExpenses--">isAddEditDeleteExpenses</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteExpenses property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteIssuesAndIssueThreshold--">isAddEditDeleteIssuesAndIssueThreshold</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteIssuesAndIssueThreshold property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteIssuesandIssueThresholds--">isAddEditDeleteIssuesandIssueThresholds</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteIssuesandIssueThresholds property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteProjectBaselines--">isAddEditDeleteProjectBaselines</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteProjectBaselines property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteProjectCalendars--">isAddEditDeleteProjectCalendars</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteProjectCalendars property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteResourceAssignmentsForResourcePlanning--">isAddEditDeleteResourceAssignmentsForResourcePlanning</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteResourceAssignmentsForResourcePlanning property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteRisks--">isAddEditDeleteRisks</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteRisks property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteRoleAssignmentsForResourcePlanning--">isAddEditDeleteRoleAssignmentsForResourcePlanning</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteRoleAssignmentsForResourcePlanning property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteTemplateDocuments--">isAddEditDeleteTemplateDocuments</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteTemplateDocuments property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteWBSExceptCostsAndFinancials--">isAddEditDeleteWBSExceptCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteWBSExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditDeleteWorkProductsAndDocuments--">isAddEditDeleteWorkProductsAndDocuments</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteWorkProductsAndDocuments property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditProjectLevelLayouts--">isAddEditProjectLevelLayouts</a></span>()</code>
<div class="block">Gets the value of the addEditProjectLevelLayouts property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEditWorkgroups--">isAddEditWorkgroups</a></span>()</code>
<div class="block">Gets the value of the addEditWorkgroups property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddEPSActivityCodes--">isAddEPSActivityCodes</a></span>()</code>
<div class="block">Gets the value of the addEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddProjectActivityCodes--">isAddProjectActivityCodes</a></span>()</code>
<div class="block">Gets the value of the addProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAddProjects--">isAddProjects</a></span>()</code>
<div class="block">Gets the value of the addProjects property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAdministerProjectExternalApplications--">isAdministerProjectExternalApplications</a></span>()</code>
<div class="block">Gets the value of the administerProjectExternalApplications property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAllowIntegrationwithERPSystem--">isAllowIntegrationwithERPSystem</a></span>()</code>
<div class="block">Gets the value of the allowIntegrationwithERPSystem property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isApplyActuals--">isApplyActuals</a></span>()</code>
<div class="block">Gets the value of the applyActuals property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isApproveTimesheetsAsProjectManager--">isApproveTimesheetsAsProjectManager</a></span>()</code>
<div class="block">Gets the value of the approveTimesheetsAsProjectManager property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isAssignProjectBaselines--">isAssignProjectBaselines</a></span>()</code>
<div class="block">Gets the value of the assignProjectBaselines property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isCheckInAndCheckOutProjects--">isCheckInAndCheckOutProjects</a></span>()</code>
<div class="block">Gets the value of the checkInAndCheckOutProjects property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteActivities--">isDeleteActivities</a></span>()</code>
<div class="block">Gets the value of the deleteActivities property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteEPSActivityCodes--">isDeleteEPSActivityCodes</a></span>()</code>
<div class="block">Gets the value of the deleteEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteProjectActivityCodes--">isDeleteProjectActivityCodes</a></span>()</code>
<div class="block">Gets the value of the deleteProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteProjectDataWithTimesheetActuals--">isDeleteProjectDataWithTimesheetActuals</a></span>()</code>
<div class="block">Gets the value of the deleteProjectDataWithTimesheetActuals property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteProjects--">isDeleteProjects</a></span>()</code>
<div class="block">Gets the value of the deleteProjects property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isDeleteWorkgroups--">isDeleteWorkgroups</a></span>()</code>
<div class="block">Gets the value of the deleteWorkgroups property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditActivityId--">isEditActivityId</a></span>()</code>
<div class="block">Gets the value of the editActivityId property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditCommittedFlagForResourcePlanning--">isEditCommittedFlagForResourcePlanning</a></span>()</code>
<div class="block">Gets the value of the editCommittedFlagForResourcePlanning property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditContractManagementProjectLink--">isEditContractManagementProjectLink</a></span>()</code>
<div class="block">Gets the value of the editContractManagementProjectLink property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditEPSActivityCodes--">isEditEPSActivityCodes</a></span>()</code>
<div class="block">Gets the value of the editEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditEPSCostsAndFinancials--">isEditEPSCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the editEPSCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditFuturePeriods--">isEditFuturePeriods</a></span>()</code>
<div class="block">Gets the value of the editFuturePeriods property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditPeriodPerformance--">isEditPeriodPerformance</a></span>()</code>
<div class="block">Gets the value of the editPeriodPerformance property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditProjectActivityCodes--">isEditProjectActivityCodes</a></span>()</code>
<div class="block">Gets the value of the editProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditProjectDetailsExceptCostsAndFinancials--">isEditProjectDetailsExceptCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the editProjectDetailsExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditProjectReports--">isEditProjectReports</a></span>()</code>
<div class="block">Gets the value of the editProjectReports property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditPublicationPriority--">isEditPublicationPriority</a></span>()</code>
<div class="block">Gets the value of the editPublicationPriority property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditWBSCostsAndFinancials--">isEditWBSCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the editWBSCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isEditWorkspaceAndWorkgroupPreferences--">isEditWorkspaceAndWorkgroupPreferences</a></span>()</code>
<div class="block">Gets the value of the editWorkspaceAndWorkgroupPreferences property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isImportAndViewContractManagerData--">isImportAndViewContractManagerData</a></span>()</code>
<div class="block">Gets the value of the importAndViewContractManagerData property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isLevelResources--">isLevelResources</a></span>()</code>
<div class="block">Gets the value of the levelResources property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isMonitorProjectThresholds--">isMonitorProjectThresholds</a></span>()</code>
<div class="block">Gets the value of the monitorProjectThresholds property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isPublishProjectWebsite--">isPublishProjectWebsite</a></span>()</code>
<div class="block">Gets the value of the publishProjectWebsite property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isRunBaselineUpdate--">isRunBaselineUpdate</a></span>()</code>
<div class="block">Gets the value of the runBaselineUpdate property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isRunGlobalChange--">isRunGlobalChange</a></span>()</code>
<div class="block">Gets the value of the runGlobalChange property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isScheduleProjects--">isScheduleProjects</a></span>()</code>
<div class="block">Gets the value of the scheduleProjects property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isStorePeriodPerformance--">isStorePeriodPerformance</a></span>()</code>
<div class="block">Gets the value of the storePeriodPerformance property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isSummarizeProjects--">isSummarizeProjects</a></span>()</code>
<div class="block">Gets the value of the summarizeProjects property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#isViewProjectCostsAndFinancials--">isViewProjectCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the viewProjectCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditActivitiesExceptRelationships-java.lang.Boolean-">setAddEditActivitiesExceptRelationships</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditActivitiesExceptRelationships property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditActivityResourceRequests-java.lang.Boolean-">setAddEditActivityResourceRequests</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditActivityResourceRequests property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteActivityRelationships-java.lang.Boolean-">setAddEditDeleteActivityRelationships</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteActivityRelationships property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteEPSExceptCostsAndFinancials-java.lang.Boolean-">setAddEditDeleteEPSExceptCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteEPSExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteExpenses-java.lang.Boolean-">setAddEditDeleteExpenses</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteExpenses property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteIssuesAndIssueThreshold-java.lang.Boolean-">setAddEditDeleteIssuesAndIssueThreshold</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteIssuesAndIssueThreshold property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteIssuesandIssueThresholds-java.lang.Boolean-">setAddEditDeleteIssuesandIssueThresholds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteIssuesandIssueThresholds property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteProjectBaselines-java.lang.Boolean-">setAddEditDeleteProjectBaselines</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteProjectBaselines property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteProjectCalendars-java.lang.Boolean-">setAddEditDeleteProjectCalendars</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteProjectCalendars property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteResourceAssignmentsForResourcePlanning-java.lang.Boolean-">setAddEditDeleteResourceAssignmentsForResourcePlanning</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteResourceAssignmentsForResourcePlanning property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteRisks-java.lang.Boolean-">setAddEditDeleteRisks</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteRisks property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteRoleAssignmentsForResourcePlanning-java.lang.Boolean-">setAddEditDeleteRoleAssignmentsForResourcePlanning</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteRoleAssignmentsForResourcePlanning property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteTemplateDocuments-java.lang.Boolean-">setAddEditDeleteTemplateDocuments</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteTemplateDocuments property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteWBSExceptCostsAndFinancials-java.lang.Boolean-">setAddEditDeleteWBSExceptCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteWBSExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditDeleteWorkProductsAndDocuments-java.lang.Boolean-">setAddEditDeleteWorkProductsAndDocuments</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteWorkProductsAndDocuments property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditProjectLevelLayouts-java.lang.Boolean-">setAddEditProjectLevelLayouts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditProjectLevelLayouts property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEditWorkgroups-java.lang.Boolean-">setAddEditWorkgroups</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditWorkgroups property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddEPSActivityCodes-java.lang.Boolean-">setAddEPSActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddProjectActivityCodes-java.lang.Boolean-">setAddProjectActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAddProjects-java.lang.Boolean-">setAddProjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addProjects property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAdministerProjectExternalApplications-java.lang.Boolean-">setAdministerProjectExternalApplications</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the administerProjectExternalApplications property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAllowIntegrationwithERPSystem-java.lang.Boolean-">setAllowIntegrationwithERPSystem</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the allowIntegrationwithERPSystem property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setApplyActuals-java.lang.Boolean-">setApplyActuals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the applyActuals property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setApproveTimesheetsAsProjectManager-java.lang.Boolean-">setApproveTimesheetsAsProjectManager</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the approveTimesheetsAsProjectManager property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setAssignProjectBaselines-java.lang.Boolean-">setAssignProjectBaselines</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the assignProjectBaselines property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setCheckInAndCheckOutProjects-java.lang.Boolean-">setCheckInAndCheckOutProjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the checkInAndCheckOutProjects property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteActivities-java.lang.Boolean-">setDeleteActivities</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteActivities property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteEPSActivityCodes-java.lang.Boolean-">setDeleteEPSActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteProjectActivityCodes-java.lang.Boolean-">setDeleteProjectActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteProjectDataWithTimesheetActuals-java.lang.Boolean-">setDeleteProjectDataWithTimesheetActuals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteProjectDataWithTimesheetActuals property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteProjects-java.lang.Boolean-">setDeleteProjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteProjects property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setDeleteWorkgroups-java.lang.Boolean-">setDeleteWorkgroups</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteWorkgroups property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditActivityId-java.lang.Boolean-">setEditActivityId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editActivityId property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditCommittedFlagForResourcePlanning-java.lang.Boolean-">setEditCommittedFlagForResourcePlanning</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editCommittedFlagForResourcePlanning property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditContractManagementProjectLink-java.lang.Boolean-">setEditContractManagementProjectLink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editContractManagementProjectLink property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditEPSActivityCodes-java.lang.Boolean-">setEditEPSActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editEPSActivityCodes property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditEPSCostsAndFinancials-java.lang.Boolean-">setEditEPSCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editEPSCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditFuturePeriods-java.lang.Boolean-">setEditFuturePeriods</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editFuturePeriods property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditPeriodPerformance-java.lang.Boolean-">setEditPeriodPerformance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editPeriodPerformance property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditProjectActivityCodes-java.lang.Boolean-">setEditProjectActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editProjectActivityCodes property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditProjectDetailsExceptCostsAndFinancials-java.lang.Boolean-">setEditProjectDetailsExceptCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editProjectDetailsExceptCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditProjectReports-java.lang.Boolean-">setEditProjectReports</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editProjectReports property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditPublicationPriority-java.lang.Boolean-">setEditPublicationPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editPublicationPriority property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditWBSCostsAndFinancials-java.lang.Boolean-">setEditWBSCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editWBSCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setEditWorkspaceAndWorkgroupPreferences-java.lang.Boolean-">setEditWorkspaceAndWorkgroupPreferences</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editWorkspaceAndWorkgroupPreferences property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setImportAndViewContractManagerData-java.lang.Boolean-">setImportAndViewContractManagerData</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the importAndViewContractManagerData property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setLevelResources-java.lang.Boolean-">setLevelResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the levelResources property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setMonitorProjectThresholds-java.lang.Boolean-">setMonitorProjectThresholds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the monitorProjectThresholds property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setPublishProjectWebsite-java.lang.Boolean-">setPublishProjectWebsite</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the publishProjectWebsite property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setRunBaselineUpdate-java.lang.Boolean-">setRunBaselineUpdate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the runBaselineUpdate property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setRunGlobalChange-java.lang.Boolean-">setRunGlobalChange</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the runGlobalChange property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setScheduleProjects-java.lang.Boolean-">setScheduleProjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the scheduleProjects property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setStorePeriodPerformance-java.lang.Boolean-">setStorePeriodPerformance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the storePeriodPerformance property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setSummarizeProjects-java.lang.Boolean-">setSummarizeProjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the summarizeProjects property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html#setViewProjectCostsAndFinancials-java.lang.Boolean-">setViewProjectCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the viewProjectCostsAndFinancials property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="addEditDeleteRisks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteRisks</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteRisks</pre>
</li>
</ul>
<a name="addEPSActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEPSActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEPSActivityCodes</pre>
</li>
</ul>
<a name="addEditActivitiesExceptRelationships">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditActivitiesExceptRelationships</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditActivitiesExceptRelationships</pre>
</li>
</ul>
<a name="addEditProjectLevelLayouts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditProjectLevelLayouts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditProjectLevelLayouts</pre>
</li>
</ul>
<a name="addEditDeleteTemplateDocuments">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteTemplateDocuments</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteTemplateDocuments</pre>
</li>
</ul>
<a name="addProjectActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addProjectActivityCodes</pre>
</li>
</ul>
<a name="administerProjectExternalApplications">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>administerProjectExternalApplications</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> administerProjectExternalApplications</pre>
</li>
</ul>
<a name="applyActuals">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>applyActuals</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> applyActuals</pre>
</li>
</ul>
<a name="approveTimesheetsAsProjectManager">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approveTimesheetsAsProjectManager</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> approveTimesheetsAsProjectManager</pre>
</li>
</ul>
<a name="assignProjectBaselines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignProjectBaselines</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> assignProjectBaselines</pre>
</li>
</ul>
<a name="checkInAndCheckOutProjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkInAndCheckOutProjects</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> checkInAndCheckOutProjects</pre>
</li>
</ul>
<a name="addProjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjects</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addProjects</pre>
</li>
</ul>
<a name="addEditWorkgroups">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditWorkgroups</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditWorkgroups</pre>
</li>
</ul>
<a name="deleteEPSActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteEPSActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteEPSActivityCodes</pre>
</li>
</ul>
<a name="deleteActivities">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteActivities</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteActivities</pre>
</li>
</ul>
<a name="deleteProjectActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteProjectActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteProjectActivityCodes</pre>
</li>
</ul>
<a name="deleteProjectDataWithTimesheetActuals">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteProjectDataWithTimesheetActuals</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteProjectDataWithTimesheetActuals</pre>
</li>
</ul>
<a name="deleteProjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteProjects</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteProjects</pre>
</li>
</ul>
<a name="deleteWorkgroups">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteWorkgroups</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteWorkgroups</pre>
</li>
</ul>
<a name="editActivityId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editActivityId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editActivityId</pre>
</li>
</ul>
<a name="editCommittedFlagForResourcePlanning">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editCommittedFlagForResourcePlanning</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editCommittedFlagForResourcePlanning</pre>
</li>
</ul>
<a name="editEPSActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editEPSActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editEPSActivityCodes</pre>
</li>
</ul>
<a name="addEditDeleteEPSExceptCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteEPSExceptCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteEPSExceptCostsAndFinancials</pre>
</li>
</ul>
<a name="editEPSCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editEPSCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editEPSCostsAndFinancials</pre>
</li>
</ul>
<a name="editFuturePeriods">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editFuturePeriods</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editFuturePeriods</pre>
</li>
</ul>
<a name="editPeriodPerformance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editPeriodPerformance</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editPeriodPerformance</pre>
</li>
</ul>
<a name="editProjectActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editProjectActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editProjectActivityCodes</pre>
</li>
</ul>
<a name="addEditDeleteActivityRelationships">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteActivityRelationships</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteActivityRelationships</pre>
</li>
</ul>
<a name="addEditDeleteProjectCalendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteProjectCalendars</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteProjectCalendars</pre>
</li>
</ul>
<a name="editContractManagementProjectLink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editContractManagementProjectLink</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editContractManagementProjectLink</pre>
</li>
</ul>
<a name="editProjectDetailsExceptCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editProjectDetailsExceptCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editProjectDetailsExceptCostsAndFinancials</pre>
</li>
</ul>
<a name="addEditDeleteExpenses">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteExpenses</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteExpenses</pre>
</li>
</ul>
<a name="editProjectReports">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editProjectReports</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editProjectReports</pre>
</li>
</ul>
<a name="addEditDeleteIssuesAndIssueThreshold">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteIssuesAndIssueThreshold</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteIssuesAndIssueThreshold</pre>
</li>
</ul>
<a name="addEditDeleteWBSExceptCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteWBSExceptCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteWBSExceptCostsAndFinancials</pre>
</li>
</ul>
<a name="editWBSCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editWBSCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editWBSCostsAndFinancials</pre>
</li>
</ul>
<a name="addEditDeleteWorkProductsAndDocuments">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteWorkProductsAndDocuments</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteWorkProductsAndDocuments</pre>
</li>
</ul>
<a name="addEditDeleteResourceAssignmentsForResourcePlanning">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteResourceAssignmentsForResourcePlanning</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteResourceAssignmentsForResourcePlanning</pre>
</li>
</ul>
<a name="addEditDeleteRoleAssignmentsForResourcePlanning">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteRoleAssignmentsForResourcePlanning</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteRoleAssignmentsForResourcePlanning</pre>
</li>
</ul>
<a name="importAndViewContractManagerData">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importAndViewContractManagerData</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> importAndViewContractManagerData</pre>
</li>
</ul>
<a name="levelResources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>levelResources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> levelResources</pre>
</li>
</ul>
<a name="addEditDeleteProjectBaselines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteProjectBaselines</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteProjectBaselines</pre>
</li>
</ul>
<a name="editWorkspaceAndWorkgroupPreferences">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editWorkspaceAndWorkgroupPreferences</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editWorkspaceAndWorkgroupPreferences</pre>
</li>
</ul>
<a name="monitorProjectThresholds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>monitorProjectThresholds</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> monitorProjectThresholds</pre>
</li>
</ul>
<a name="addEditActivityResourceRequests">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditActivityResourceRequests</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditActivityResourceRequests</pre>
</li>
</ul>
<a name="publishProjectWebsite">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>publishProjectWebsite</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> publishProjectWebsite</pre>
</li>
</ul>
<a name="runBaselineUpdate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runBaselineUpdate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> runBaselineUpdate</pre>
</li>
</ul>
<a name="runGlobalChange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runGlobalChange</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> runGlobalChange</pre>
</li>
</ul>
<a name="scheduleProjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scheduleProjects</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> scheduleProjects</pre>
</li>
</ul>
<a name="storePeriodPerformance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storePeriodPerformance</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> storePeriodPerformance</pre>
</li>
</ul>
<a name="summarizeProjects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>summarizeProjects</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> summarizeProjects</pre>
</li>
</ul>
<a name="viewProjectCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewProjectCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> viewProjectCostsAndFinancials</pre>
</li>
</ul>
<a name="addEditDeleteIssuesandIssueThresholds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteIssuesandIssueThresholds</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteIssuesandIssueThresholds</pre>
</li>
</ul>
<a name="allowIntegrationwithERPSystem">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allowIntegrationwithERPSystem</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> allowIntegrationwithERPSystem</pre>
</li>
</ul>
<a name="editPublicationPriority">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>editPublicationPriority</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editPublicationPriority</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProjectPrivilegesType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProjectPrivilegesType</h4>
<pre>public&nbsp;ProjectPrivilegesType()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isAddEditDeleteRisks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteRisks</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteRisks()</pre>
<div class="block">Gets the value of the addEditDeleteRisks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteRisks-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteRisks</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteRisks(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteRisks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEPSActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEPSActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEPSActivityCodes()</pre>
<div class="block">Gets the value of the addEPSActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEPSActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEPSActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setAddEPSActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEPSActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditActivitiesExceptRelationships--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditActivitiesExceptRelationships</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditActivitiesExceptRelationships()</pre>
<div class="block">Gets the value of the addEditActivitiesExceptRelationships property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditActivitiesExceptRelationships-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditActivitiesExceptRelationships</h4>
<pre>public&nbsp;void&nbsp;setAddEditActivitiesExceptRelationships(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditActivitiesExceptRelationships property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditProjectLevelLayouts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditProjectLevelLayouts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditProjectLevelLayouts()</pre>
<div class="block">Gets the value of the addEditProjectLevelLayouts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditProjectLevelLayouts-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditProjectLevelLayouts</h4>
<pre>public&nbsp;void&nbsp;setAddEditProjectLevelLayouts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditProjectLevelLayouts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteTemplateDocuments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteTemplateDocuments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteTemplateDocuments()</pre>
<div class="block">Gets the value of the addEditDeleteTemplateDocuments property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteTemplateDocuments-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteTemplateDocuments</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteTemplateDocuments(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteTemplateDocuments property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddProjectActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddProjectActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddProjectActivityCodes()</pre>
<div class="block">Gets the value of the addProjectActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddProjectActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddProjectActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setAddProjectActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addProjectActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdministerProjectExternalApplications--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdministerProjectExternalApplications</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAdministerProjectExternalApplications()</pre>
<div class="block">Gets the value of the administerProjectExternalApplications property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdministerProjectExternalApplications-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdministerProjectExternalApplications</h4>
<pre>public&nbsp;void&nbsp;setAdministerProjectExternalApplications(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the administerProjectExternalApplications property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isApplyActuals--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isApplyActuals</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isApplyActuals()</pre>
<div class="block">Gets the value of the applyActuals property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setApplyActuals-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplyActuals</h4>
<pre>public&nbsp;void&nbsp;setApplyActuals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the applyActuals property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isApproveTimesheetsAsProjectManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isApproveTimesheetsAsProjectManager</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isApproveTimesheetsAsProjectManager()</pre>
<div class="block">Gets the value of the approveTimesheetsAsProjectManager property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setApproveTimesheetsAsProjectManager-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApproveTimesheetsAsProjectManager</h4>
<pre>public&nbsp;void&nbsp;setApproveTimesheetsAsProjectManager(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the approveTimesheetsAsProjectManager property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAssignProjectBaselines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssignProjectBaselines</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAssignProjectBaselines()</pre>
<div class="block">Gets the value of the assignProjectBaselines property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssignProjectBaselines-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignProjectBaselines</h4>
<pre>public&nbsp;void&nbsp;setAssignProjectBaselines(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assignProjectBaselines property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isCheckInAndCheckOutProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCheckInAndCheckOutProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isCheckInAndCheckOutProjects()</pre>
<div class="block">Gets the value of the checkInAndCheckOutProjects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCheckInAndCheckOutProjects-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCheckInAndCheckOutProjects</h4>
<pre>public&nbsp;void&nbsp;setCheckInAndCheckOutProjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the checkInAndCheckOutProjects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddProjects()</pre>
<div class="block">Gets the value of the addProjects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddProjects-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddProjects</h4>
<pre>public&nbsp;void&nbsp;setAddProjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addProjects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditWorkgroups--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditWorkgroups</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditWorkgroups()</pre>
<div class="block">Gets the value of the addEditWorkgroups property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditWorkgroups-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditWorkgroups</h4>
<pre>public&nbsp;void&nbsp;setAddEditWorkgroups(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditWorkgroups property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteEPSActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteEPSActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteEPSActivityCodes()</pre>
<div class="block">Gets the value of the deleteEPSActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteEPSActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteEPSActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteEPSActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteEPSActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteActivities--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteActivities</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteActivities()</pre>
<div class="block">Gets the value of the deleteActivities property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteActivities-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteActivities</h4>
<pre>public&nbsp;void&nbsp;setDeleteActivities(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteActivities property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteProjectActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteProjectActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteProjectActivityCodes()</pre>
<div class="block">Gets the value of the deleteProjectActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteProjectActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteProjectActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteProjectActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteProjectActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteProjectDataWithTimesheetActuals--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteProjectDataWithTimesheetActuals</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteProjectDataWithTimesheetActuals()</pre>
<div class="block">Gets the value of the deleteProjectDataWithTimesheetActuals property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteProjectDataWithTimesheetActuals-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteProjectDataWithTimesheetActuals</h4>
<pre>public&nbsp;void&nbsp;setDeleteProjectDataWithTimesheetActuals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteProjectDataWithTimesheetActuals property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteProjects()</pre>
<div class="block">Gets the value of the deleteProjects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteProjects-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteProjects</h4>
<pre>public&nbsp;void&nbsp;setDeleteProjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteProjects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteWorkgroups--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteWorkgroups</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteWorkgroups()</pre>
<div class="block">Gets the value of the deleteWorkgroups property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteWorkgroups-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteWorkgroups</h4>
<pre>public&nbsp;void&nbsp;setDeleteWorkgroups(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteWorkgroups property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditActivityId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditActivityId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditActivityId()</pre>
<div class="block">Gets the value of the editActivityId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditActivityId-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditActivityId</h4>
<pre>public&nbsp;void&nbsp;setEditActivityId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editActivityId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditCommittedFlagForResourcePlanning--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditCommittedFlagForResourcePlanning</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditCommittedFlagForResourcePlanning()</pre>
<div class="block">Gets the value of the editCommittedFlagForResourcePlanning property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditCommittedFlagForResourcePlanning-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditCommittedFlagForResourcePlanning</h4>
<pre>public&nbsp;void&nbsp;setEditCommittedFlagForResourcePlanning(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editCommittedFlagForResourcePlanning property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditEPSActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditEPSActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditEPSActivityCodes()</pre>
<div class="block">Gets the value of the editEPSActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditEPSActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditEPSActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setEditEPSActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editEPSActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteEPSExceptCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteEPSExceptCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteEPSExceptCostsAndFinancials()</pre>
<div class="block">Gets the value of the addEditDeleteEPSExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteEPSExceptCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteEPSExceptCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteEPSExceptCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteEPSExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditEPSCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditEPSCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditEPSCostsAndFinancials()</pre>
<div class="block">Gets the value of the editEPSCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditEPSCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditEPSCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setEditEPSCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editEPSCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditFuturePeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditFuturePeriods</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditFuturePeriods()</pre>
<div class="block">Gets the value of the editFuturePeriods property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditFuturePeriods-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditFuturePeriods</h4>
<pre>public&nbsp;void&nbsp;setEditFuturePeriods(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editFuturePeriods property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditPeriodPerformance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditPeriodPerformance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditPeriodPerformance()</pre>
<div class="block">Gets the value of the editPeriodPerformance property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditPeriodPerformance-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditPeriodPerformance</h4>
<pre>public&nbsp;void&nbsp;setEditPeriodPerformance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editPeriodPerformance property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditProjectActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditProjectActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditProjectActivityCodes()</pre>
<div class="block">Gets the value of the editProjectActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditProjectActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditProjectActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setEditProjectActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editProjectActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteActivityRelationships--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteActivityRelationships</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteActivityRelationships()</pre>
<div class="block">Gets the value of the addEditDeleteActivityRelationships property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteActivityRelationships-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteActivityRelationships</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteActivityRelationships(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteActivityRelationships property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteProjectCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteProjectCalendars</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteProjectCalendars()</pre>
<div class="block">Gets the value of the addEditDeleteProjectCalendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteProjectCalendars-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteProjectCalendars</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteProjectCalendars(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteProjectCalendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditContractManagementProjectLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditContractManagementProjectLink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditContractManagementProjectLink()</pre>
<div class="block">Gets the value of the editContractManagementProjectLink property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditContractManagementProjectLink-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditContractManagementProjectLink</h4>
<pre>public&nbsp;void&nbsp;setEditContractManagementProjectLink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editContractManagementProjectLink property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditProjectDetailsExceptCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditProjectDetailsExceptCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditProjectDetailsExceptCostsAndFinancials()</pre>
<div class="block">Gets the value of the editProjectDetailsExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditProjectDetailsExceptCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditProjectDetailsExceptCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setEditProjectDetailsExceptCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editProjectDetailsExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteExpenses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteExpenses</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteExpenses()</pre>
<div class="block">Gets the value of the addEditDeleteExpenses property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteExpenses-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteExpenses</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteExpenses(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteExpenses property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditProjectReports--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditProjectReports</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditProjectReports()</pre>
<div class="block">Gets the value of the editProjectReports property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditProjectReports-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditProjectReports</h4>
<pre>public&nbsp;void&nbsp;setEditProjectReports(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editProjectReports property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteIssuesAndIssueThreshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteIssuesAndIssueThreshold</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteIssuesAndIssueThreshold()</pre>
<div class="block">Gets the value of the addEditDeleteIssuesAndIssueThreshold property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteIssuesAndIssueThreshold-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteIssuesAndIssueThreshold</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteIssuesAndIssueThreshold(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteIssuesAndIssueThreshold property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteWBSExceptCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteWBSExceptCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteWBSExceptCostsAndFinancials()</pre>
<div class="block">Gets the value of the addEditDeleteWBSExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteWBSExceptCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteWBSExceptCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteWBSExceptCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteWBSExceptCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditWBSCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditWBSCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditWBSCostsAndFinancials()</pre>
<div class="block">Gets the value of the editWBSCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditWBSCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditWBSCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setEditWBSCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editWBSCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteWorkProductsAndDocuments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteWorkProductsAndDocuments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteWorkProductsAndDocuments()</pre>
<div class="block">Gets the value of the addEditDeleteWorkProductsAndDocuments property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteWorkProductsAndDocuments-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteWorkProductsAndDocuments</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteWorkProductsAndDocuments(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteWorkProductsAndDocuments property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteResourceAssignmentsForResourcePlanning--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteResourceAssignmentsForResourcePlanning</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteResourceAssignmentsForResourcePlanning()</pre>
<div class="block">Gets the value of the addEditDeleteResourceAssignmentsForResourcePlanning property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteResourceAssignmentsForResourcePlanning-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteResourceAssignmentsForResourcePlanning</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteResourceAssignmentsForResourcePlanning(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteResourceAssignmentsForResourcePlanning property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteRoleAssignmentsForResourcePlanning--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteRoleAssignmentsForResourcePlanning</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteRoleAssignmentsForResourcePlanning()</pre>
<div class="block">Gets the value of the addEditDeleteRoleAssignmentsForResourcePlanning property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteRoleAssignmentsForResourcePlanning-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteRoleAssignmentsForResourcePlanning</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteRoleAssignmentsForResourcePlanning(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteRoleAssignmentsForResourcePlanning property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isImportAndViewContractManagerData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isImportAndViewContractManagerData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isImportAndViewContractManagerData()</pre>
<div class="block">Gets the value of the importAndViewContractManagerData property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setImportAndViewContractManagerData-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImportAndViewContractManagerData</h4>
<pre>public&nbsp;void&nbsp;setImportAndViewContractManagerData(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the importAndViewContractManagerData property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isLevelResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLevelResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isLevelResources()</pre>
<div class="block">Gets the value of the levelResources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLevelResources-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelResources</h4>
<pre>public&nbsp;void&nbsp;setLevelResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the levelResources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteProjectBaselines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteProjectBaselines</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteProjectBaselines()</pre>
<div class="block">Gets the value of the addEditDeleteProjectBaselines property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteProjectBaselines-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteProjectBaselines</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteProjectBaselines(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteProjectBaselines property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditWorkspaceAndWorkgroupPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditWorkspaceAndWorkgroupPreferences</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditWorkspaceAndWorkgroupPreferences()</pre>
<div class="block">Gets the value of the editWorkspaceAndWorkgroupPreferences property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditWorkspaceAndWorkgroupPreferences-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditWorkspaceAndWorkgroupPreferences</h4>
<pre>public&nbsp;void&nbsp;setEditWorkspaceAndWorkgroupPreferences(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editWorkspaceAndWorkgroupPreferences property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMonitorProjectThresholds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMonitorProjectThresholds</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMonitorProjectThresholds()</pre>
<div class="block">Gets the value of the monitorProjectThresholds property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMonitorProjectThresholds-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMonitorProjectThresholds</h4>
<pre>public&nbsp;void&nbsp;setMonitorProjectThresholds(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the monitorProjectThresholds property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditActivityResourceRequests--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditActivityResourceRequests</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditActivityResourceRequests()</pre>
<div class="block">Gets the value of the addEditActivityResourceRequests property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditActivityResourceRequests-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditActivityResourceRequests</h4>
<pre>public&nbsp;void&nbsp;setAddEditActivityResourceRequests(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditActivityResourceRequests property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isPublishProjectWebsite--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPublishProjectWebsite</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isPublishProjectWebsite()</pre>
<div class="block">Gets the value of the publishProjectWebsite property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPublishProjectWebsite-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPublishProjectWebsite</h4>
<pre>public&nbsp;void&nbsp;setPublishProjectWebsite(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the publishProjectWebsite property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isRunBaselineUpdate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRunBaselineUpdate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isRunBaselineUpdate()</pre>
<div class="block">Gets the value of the runBaselineUpdate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRunBaselineUpdate-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRunBaselineUpdate</h4>
<pre>public&nbsp;void&nbsp;setRunBaselineUpdate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the runBaselineUpdate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isRunGlobalChange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRunGlobalChange</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isRunGlobalChange()</pre>
<div class="block">Gets the value of the runGlobalChange property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRunGlobalChange-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRunGlobalChange</h4>
<pre>public&nbsp;void&nbsp;setRunGlobalChange(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the runGlobalChange property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isScheduleProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isScheduleProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isScheduleProjects()</pre>
<div class="block">Gets the value of the scheduleProjects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setScheduleProjects-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduleProjects</h4>
<pre>public&nbsp;void&nbsp;setScheduleProjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the scheduleProjects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isStorePeriodPerformance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isStorePeriodPerformance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isStorePeriodPerformance()</pre>
<div class="block">Gets the value of the storePeriodPerformance property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStorePeriodPerformance-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStorePeriodPerformance</h4>
<pre>public&nbsp;void&nbsp;setStorePeriodPerformance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the storePeriodPerformance property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isSummarizeProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSummarizeProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isSummarizeProjects()</pre>
<div class="block">Gets the value of the summarizeProjects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSummarizeProjects-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSummarizeProjects</h4>
<pre>public&nbsp;void&nbsp;setSummarizeProjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the summarizeProjects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isViewProjectCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewProjectCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isViewProjectCostsAndFinancials()</pre>
<div class="block">Gets the value of the viewProjectCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setViewProjectCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewProjectCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setViewProjectCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the viewProjectCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteIssuesandIssueThresholds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteIssuesandIssueThresholds</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteIssuesandIssueThresholds()</pre>
<div class="block">Gets the value of the addEditDeleteIssuesandIssueThresholds property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteIssuesandIssueThresholds-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteIssuesandIssueThresholds</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteIssuesandIssueThresholds(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteIssuesandIssueThresholds property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAllowIntegrationwithERPSystem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAllowIntegrationwithERPSystem</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAllowIntegrationwithERPSystem()</pre>
<div class="block">Gets the value of the allowIntegrationwithERPSystem property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAllowIntegrationwithERPSystem-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllowIntegrationwithERPSystem</h4>
<pre>public&nbsp;void&nbsp;setAllowIntegrationwithERPSystem(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the allowIntegrationwithERPSystem property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditPublicationPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditPublicationPriority</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditPublicationPriority()</pre>
<div class="block">Gets the value of the editPublicationPriority property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditPublicationPriority-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setEditPublicationPriority</h4>
<pre>public&nbsp;void&nbsp;setEditPublicationPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editPublicationPriority property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectPrivilegesType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectPrivilegesType.html" target="_top">Frames</a></li>
<li><a href="ProjectPrivilegesType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
