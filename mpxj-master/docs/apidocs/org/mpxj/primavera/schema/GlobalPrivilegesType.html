<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GlobalPrivilegesType (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GlobalPrivilegesType (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GlobalPrivilegesType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/GlobalPrivilegesType.html" target="_top">Frames</a></li>
<li><a href="GlobalPrivilegesType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class GlobalPrivilegesType" class="title">Class GlobalPrivilegesType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.GlobalPrivilegesType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">GlobalPrivilegesType</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for GlobalPrivilegesType complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType name="GlobalPrivilegesType"&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="AddDeleteSecureCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteProjectTemplates" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteRiskCategoriesMatricesAndThresholds" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalActivityAndAssignmentViewsAndFilters" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddGlobalActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddGlobalIssueCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddProjectCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddResourceCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddRoleCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AdministerGlobalExternalApplications" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AdministerProjectScheduledServices" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ApproveResourceTimesheets" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AssignSecureCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteGlobalActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteGlobalIssueCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteProjectCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteResourceCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteRoleCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="DeleteResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteActivityStepTemplates" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteCategoriesAndOverheadCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteCostAccounts" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteCurrencies" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteFinancialPeriodDates" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteFundingSources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditGlobalActivityCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalCalendars" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditGlobalChangeDefinitions" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalDashboards" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditGlobalIssueCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalPortfolios" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalReports" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalResourceAndRoleTeams" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteGlobalScenarios" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditGlobalTrackingLayouts" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteIssueForms" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteMSPTemplates" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteOBS" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditProjectCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditProjectsFromScorecards" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteResourceCalendars" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditResourceCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditRoleCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteResourceCurves" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteRoles" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditSecureCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteSecurityProfiles" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteTimesheetPeriodDates" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteUserDefinedFields" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteUserInterfaceViews" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteUsers" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ImportXERMPPMPXandP3" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ImportXML" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ImportXLS" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ViewAllGlobalAndProjectDataViaSDK" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ViewResourceAndRoleCostsAndFinancials" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ViewResourceRoleProficiency" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ViewSecureCodes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteRateTypesandUnitsofMeasure" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="EditApplicationSettings" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="ProvisionUsersfromLDAP" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AdministerGlobalScheduledServices" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="AddEditDeleteLocations" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addDeleteSecureCodes">addDeleteSecureCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteActivityStepTemplates">addEditDeleteActivityStepTemplates</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteCategoriesAndOverheadCodes">addEditDeleteCategoriesAndOverheadCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteCostAccounts">addEditDeleteCostAccounts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteCurrencies">addEditDeleteCurrencies</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteFinancialPeriodDates">addEditDeleteFinancialPeriodDates</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteFundingSources">addEditDeleteFundingSources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalActivityAndAssignmentViewsAndFilters">addEditDeleteGlobalActivityAndAssignmentViewsAndFilters</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalCalendars">addEditDeleteGlobalCalendars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalDashboards">addEditDeleteGlobalDashboards</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalPortfolios">addEditDeleteGlobalPortfolios</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters">addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalReports">addEditDeleteGlobalReports</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalResourceAndRoleTeams">addEditDeleteGlobalResourceAndRoleTeams</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteGlobalScenarios">addEditDeleteGlobalScenarios</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteIssueForms">addEditDeleteIssueForms</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteLocations">addEditDeleteLocations</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteMSPTemplates">addEditDeleteMSPTemplates</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteOBS">addEditDeleteOBS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteProjectTemplates">addEditDeleteProjectTemplates</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteRateTypesandUnitsofMeasure">addEditDeleteRateTypesandUnitsofMeasure</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteResourceCalendars">addEditDeleteResourceCalendars</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteResourceCurves">addEditDeleteResourceCurves</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteRiskCategoriesMatricesAndThresholds">addEditDeleteRiskCategoriesMatricesAndThresholds</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteRoles">addEditDeleteRoles</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteSecurityProfiles">addEditDeleteSecurityProfiles</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteTimesheetPeriodDates">addEditDeleteTimesheetPeriodDates</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteUserDefinedFields">addEditDeleteUserDefinedFields</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteUserInterfaceViews">addEditDeleteUserInterfaceViews</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addEditDeleteUsers">addEditDeleteUsers</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addGlobalActivityCodes">addGlobalActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addGlobalIssueCodes">addGlobalIssueCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addProjectCodes">addProjectCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addResourceCodes">addResourceCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addResources">addResources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#addRoleCodes">addRoleCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#administerGlobalExternalApplications">administerGlobalExternalApplications</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#administerGlobalScheduledServices">administerGlobalScheduledServices</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#administerProjectScheduledServices">administerProjectScheduledServices</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#approveResourceTimesheets">approveResourceTimesheets</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#assignSecureCodes">assignSecureCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteGlobalActivityCodes">deleteGlobalActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteGlobalIssueCodes">deleteGlobalIssueCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteProjectCodes">deleteProjectCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteResourceCodes">deleteResourceCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteResources">deleteResources</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#deleteRoleCodes">deleteRoleCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editApplicationSettings">editApplicationSettings</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editGlobalActivityCodes">editGlobalActivityCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editGlobalChangeDefinitions">editGlobalChangeDefinitions</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editGlobalIssueCodes">editGlobalIssueCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editGlobalTrackingLayouts">editGlobalTrackingLayouts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editProjectCodes">editProjectCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editProjectsFromScorecards">editProjectsFromScorecards</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editResourceCodes">editResourceCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editResources">editResources</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editRoleCodes">editRoleCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#editSecureCodes">editSecureCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#importXERMPPMPXandP3">importXERMPPMPXandP3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#importXLS">importXLS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#importXML">importXML</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#provisionUsersfromLDAP">provisionUsersfromLDAP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#viewAllGlobalAndProjectDataViaSDK">viewAllGlobalAndProjectDataViaSDK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#viewResourceAndRoleCostsAndFinancials">viewResourceAndRoleCostsAndFinancials</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#viewResourceRoleProficiency">viewResourceRoleProficiency</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#viewSecureCodes">viewSecureCodes</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#GlobalPrivilegesType--">GlobalPrivilegesType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddDeleteSecureCodes--">isAddDeleteSecureCodes</a></span>()</code>
<div class="block">Gets the value of the addDeleteSecureCodes property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteActivityStepTemplates--">isAddEditDeleteActivityStepTemplates</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteActivityStepTemplates property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteCategoriesAndOverheadCodes--">isAddEditDeleteCategoriesAndOverheadCodes</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteCategoriesAndOverheadCodes property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteCostAccounts--">isAddEditDeleteCostAccounts</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteCostAccounts property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteCurrencies--">isAddEditDeleteCurrencies</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteCurrencies property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteFinancialPeriodDates--">isAddEditDeleteFinancialPeriodDates</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteFinancialPeriodDates property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteFundingSources--">isAddEditDeleteFundingSources</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteFundingSources property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters--">isAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalActivityAndAssignmentViewsAndFilters property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalCalendars--">isAddEditDeleteGlobalCalendars</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalCalendars property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalDashboards--">isAddEditDeleteGlobalDashboards</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalDashboards property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalPortfolios--">isAddEditDeleteGlobalPortfolios</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalPortfolios property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters--">isAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalReports--">isAddEditDeleteGlobalReports</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalReports property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalResourceAndRoleTeams--">isAddEditDeleteGlobalResourceAndRoleTeams</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalResourceAndRoleTeams property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteGlobalScenarios--">isAddEditDeleteGlobalScenarios</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteGlobalScenarios property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteIssueForms--">isAddEditDeleteIssueForms</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteIssueForms property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteLocations--">isAddEditDeleteLocations</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteLocations property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteMSPTemplates--">isAddEditDeleteMSPTemplates</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteMSPTemplates property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteOBS--">isAddEditDeleteOBS</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteOBS property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteProjectTemplates--">isAddEditDeleteProjectTemplates</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteProjectTemplates property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteRateTypesandUnitsofMeasure--">isAddEditDeleteRateTypesandUnitsofMeasure</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteRateTypesandUnitsofMeasure property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteResourceCalendars--">isAddEditDeleteResourceCalendars</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteResourceCalendars property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteResourceCurves--">isAddEditDeleteResourceCurves</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteResourceCurves property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteRiskCategoriesMatricesAndThresholds--">isAddEditDeleteRiskCategoriesMatricesAndThresholds</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteRiskCategoriesMatricesAndThresholds property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteRoles--">isAddEditDeleteRoles</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteRoles property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteSecurityProfiles--">isAddEditDeleteSecurityProfiles</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteSecurityProfiles property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteTimesheetPeriodDates--">isAddEditDeleteTimesheetPeriodDates</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteTimesheetPeriodDates property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteUserDefinedFields--">isAddEditDeleteUserDefinedFields</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteUserDefinedFields property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteUserInterfaceViews--">isAddEditDeleteUserInterfaceViews</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteUserInterfaceViews property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddEditDeleteUsers--">isAddEditDeleteUsers</a></span>()</code>
<div class="block">Gets the value of the addEditDeleteUsers property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddGlobalActivityCodes--">isAddGlobalActivityCodes</a></span>()</code>
<div class="block">Gets the value of the addGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddGlobalIssueCodes--">isAddGlobalIssueCodes</a></span>()</code>
<div class="block">Gets the value of the addGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddProjectCodes--">isAddProjectCodes</a></span>()</code>
<div class="block">Gets the value of the addProjectCodes property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddResourceCodes--">isAddResourceCodes</a></span>()</code>
<div class="block">Gets the value of the addResourceCodes property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddResources--">isAddResources</a></span>()</code>
<div class="block">Gets the value of the addResources property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAddRoleCodes--">isAddRoleCodes</a></span>()</code>
<div class="block">Gets the value of the addRoleCodes property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAdministerGlobalExternalApplications--">isAdministerGlobalExternalApplications</a></span>()</code>
<div class="block">Gets the value of the administerGlobalExternalApplications property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAdministerGlobalScheduledServices--">isAdministerGlobalScheduledServices</a></span>()</code>
<div class="block">Gets the value of the administerGlobalScheduledServices property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAdministerProjectScheduledServices--">isAdministerProjectScheduledServices</a></span>()</code>
<div class="block">Gets the value of the administerProjectScheduledServices property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isApproveResourceTimesheets--">isApproveResourceTimesheets</a></span>()</code>
<div class="block">Gets the value of the approveResourceTimesheets property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isAssignSecureCodes--">isAssignSecureCodes</a></span>()</code>
<div class="block">Gets the value of the assignSecureCodes property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteGlobalActivityCodes--">isDeleteGlobalActivityCodes</a></span>()</code>
<div class="block">Gets the value of the deleteGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteGlobalIssueCodes--">isDeleteGlobalIssueCodes</a></span>()</code>
<div class="block">Gets the value of the deleteGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteProjectCodes--">isDeleteProjectCodes</a></span>()</code>
<div class="block">Gets the value of the deleteProjectCodes property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteResourceCodes--">isDeleteResourceCodes</a></span>()</code>
<div class="block">Gets the value of the deleteResourceCodes property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteResources--">isDeleteResources</a></span>()</code>
<div class="block">Gets the value of the deleteResources property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isDeleteRoleCodes--">isDeleteRoleCodes</a></span>()</code>
<div class="block">Gets the value of the deleteRoleCodes property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditApplicationSettings--">isEditApplicationSettings</a></span>()</code>
<div class="block">Gets the value of the editApplicationSettings property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditGlobalActivityCodes--">isEditGlobalActivityCodes</a></span>()</code>
<div class="block">Gets the value of the editGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditGlobalChangeDefinitions--">isEditGlobalChangeDefinitions</a></span>()</code>
<div class="block">Gets the value of the editGlobalChangeDefinitions property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditGlobalIssueCodes--">isEditGlobalIssueCodes</a></span>()</code>
<div class="block">Gets the value of the editGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditGlobalTrackingLayouts--">isEditGlobalTrackingLayouts</a></span>()</code>
<div class="block">Gets the value of the editGlobalTrackingLayouts property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditProjectCodes--">isEditProjectCodes</a></span>()</code>
<div class="block">Gets the value of the editProjectCodes property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditProjectsFromScorecards--">isEditProjectsFromScorecards</a></span>()</code>
<div class="block">Gets the value of the editProjectsFromScorecards property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditResourceCodes--">isEditResourceCodes</a></span>()</code>
<div class="block">Gets the value of the editResourceCodes property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditResources--">isEditResources</a></span>()</code>
<div class="block">Gets the value of the editResources property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditRoleCodes--">isEditRoleCodes</a></span>()</code>
<div class="block">Gets the value of the editRoleCodes property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isEditSecureCodes--">isEditSecureCodes</a></span>()</code>
<div class="block">Gets the value of the editSecureCodes property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isImportXERMPPMPXandP3--">isImportXERMPPMPXandP3</a></span>()</code>
<div class="block">Gets the value of the importXERMPPMPXandP3 property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isImportXLS--">isImportXLS</a></span>()</code>
<div class="block">Gets the value of the importXLS property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isImportXML--">isImportXML</a></span>()</code>
<div class="block">Gets the value of the importXML property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isProvisionUsersfromLDAP--">isProvisionUsersfromLDAP</a></span>()</code>
<div class="block">Gets the value of the provisionUsersfromLDAP property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isViewAllGlobalAndProjectDataViaSDK--">isViewAllGlobalAndProjectDataViaSDK</a></span>()</code>
<div class="block">Gets the value of the viewAllGlobalAndProjectDataViaSDK property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isViewResourceAndRoleCostsAndFinancials--">isViewResourceAndRoleCostsAndFinancials</a></span>()</code>
<div class="block">Gets the value of the viewResourceAndRoleCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isViewResourceRoleProficiency--">isViewResourceRoleProficiency</a></span>()</code>
<div class="block">Gets the value of the viewResourceRoleProficiency property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#isViewSecureCodes--">isViewSecureCodes</a></span>()</code>
<div class="block">Gets the value of the viewSecureCodes property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddDeleteSecureCodes-java.lang.Boolean-">setAddDeleteSecureCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addDeleteSecureCodes property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteActivityStepTemplates-java.lang.Boolean-">setAddEditDeleteActivityStepTemplates</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteActivityStepTemplates property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteCategoriesAndOverheadCodes-java.lang.Boolean-">setAddEditDeleteCategoriesAndOverheadCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteCategoriesAndOverheadCodes property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteCostAccounts-java.lang.Boolean-">setAddEditDeleteCostAccounts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteCostAccounts property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteCurrencies-java.lang.Boolean-">setAddEditDeleteCurrencies</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteCurrencies property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteFinancialPeriodDates-java.lang.Boolean-">setAddEditDeleteFinancialPeriodDates</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteFinancialPeriodDates property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteFundingSources-java.lang.Boolean-">setAddEditDeleteFundingSources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteFundingSources property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters-java.lang.Boolean-">setAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalActivityAndAssignmentViewsAndFilters property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalCalendars-java.lang.Boolean-">setAddEditDeleteGlobalCalendars</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalCalendars property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalDashboards-java.lang.Boolean-">setAddEditDeleteGlobalDashboards</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalDashboards property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalPortfolios-java.lang.Boolean-">setAddEditDeleteGlobalPortfolios</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalPortfolios property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters-java.lang.Boolean-">setAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalReports-java.lang.Boolean-">setAddEditDeleteGlobalReports</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalReports property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalResourceAndRoleTeams-java.lang.Boolean-">setAddEditDeleteGlobalResourceAndRoleTeams</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalResourceAndRoleTeams property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteGlobalScenarios-java.lang.Boolean-">setAddEditDeleteGlobalScenarios</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteGlobalScenarios property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteIssueForms-java.lang.Boolean-">setAddEditDeleteIssueForms</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteIssueForms property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteLocations-java.lang.Boolean-">setAddEditDeleteLocations</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteLocations property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteMSPTemplates-java.lang.Boolean-">setAddEditDeleteMSPTemplates</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteMSPTemplates property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteOBS-java.lang.Boolean-">setAddEditDeleteOBS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteOBS property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteProjectTemplates-java.lang.Boolean-">setAddEditDeleteProjectTemplates</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteProjectTemplates property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteRateTypesandUnitsofMeasure-java.lang.Boolean-">setAddEditDeleteRateTypesandUnitsofMeasure</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteRateTypesandUnitsofMeasure property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteResourceCalendars-java.lang.Boolean-">setAddEditDeleteResourceCalendars</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteResourceCalendars property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteResourceCurves-java.lang.Boolean-">setAddEditDeleteResourceCurves</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteResourceCurves property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteRiskCategoriesMatricesAndThresholds-java.lang.Boolean-">setAddEditDeleteRiskCategoriesMatricesAndThresholds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteRiskCategoriesMatricesAndThresholds property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteRoles-java.lang.Boolean-">setAddEditDeleteRoles</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteRoles property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteSecurityProfiles-java.lang.Boolean-">setAddEditDeleteSecurityProfiles</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteSecurityProfiles property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteTimesheetPeriodDates-java.lang.Boolean-">setAddEditDeleteTimesheetPeriodDates</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteTimesheetPeriodDates property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteUserDefinedFields-java.lang.Boolean-">setAddEditDeleteUserDefinedFields</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteUserDefinedFields property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteUserInterfaceViews-java.lang.Boolean-">setAddEditDeleteUserInterfaceViews</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteUserInterfaceViews property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddEditDeleteUsers-java.lang.Boolean-">setAddEditDeleteUsers</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addEditDeleteUsers property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddGlobalActivityCodes-java.lang.Boolean-">setAddGlobalActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddGlobalIssueCodes-java.lang.Boolean-">setAddGlobalIssueCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddProjectCodes-java.lang.Boolean-">setAddProjectCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addProjectCodes property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddResourceCodes-java.lang.Boolean-">setAddResourceCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addResourceCodes property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddResources-java.lang.Boolean-">setAddResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addResources property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAddRoleCodes-java.lang.Boolean-">setAddRoleCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the addRoleCodes property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAdministerGlobalExternalApplications-java.lang.Boolean-">setAdministerGlobalExternalApplications</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the administerGlobalExternalApplications property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAdministerGlobalScheduledServices-java.lang.Boolean-">setAdministerGlobalScheduledServices</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the administerGlobalScheduledServices property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAdministerProjectScheduledServices-java.lang.Boolean-">setAdministerProjectScheduledServices</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the administerProjectScheduledServices property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setApproveResourceTimesheets-java.lang.Boolean-">setApproveResourceTimesheets</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the approveResourceTimesheets property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setAssignSecureCodes-java.lang.Boolean-">setAssignSecureCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the assignSecureCodes property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteGlobalActivityCodes-java.lang.Boolean-">setDeleteGlobalActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteGlobalIssueCodes-java.lang.Boolean-">setDeleteGlobalIssueCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteProjectCodes-java.lang.Boolean-">setDeleteProjectCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteProjectCodes property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteResourceCodes-java.lang.Boolean-">setDeleteResourceCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteResourceCodes property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteResources-java.lang.Boolean-">setDeleteResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteResources property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setDeleteRoleCodes-java.lang.Boolean-">setDeleteRoleCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the deleteRoleCodes property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditApplicationSettings-java.lang.Boolean-">setEditApplicationSettings</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editApplicationSettings property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditGlobalActivityCodes-java.lang.Boolean-">setEditGlobalActivityCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editGlobalActivityCodes property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditGlobalChangeDefinitions-java.lang.Boolean-">setEditGlobalChangeDefinitions</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editGlobalChangeDefinitions property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditGlobalIssueCodes-java.lang.Boolean-">setEditGlobalIssueCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editGlobalIssueCodes property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditGlobalTrackingLayouts-java.lang.Boolean-">setEditGlobalTrackingLayouts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editGlobalTrackingLayouts property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditProjectCodes-java.lang.Boolean-">setEditProjectCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editProjectCodes property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditProjectsFromScorecards-java.lang.Boolean-">setEditProjectsFromScorecards</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editProjectsFromScorecards property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditResourceCodes-java.lang.Boolean-">setEditResourceCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editResourceCodes property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditResources-java.lang.Boolean-">setEditResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editResources property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditRoleCodes-java.lang.Boolean-">setEditRoleCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editRoleCodes property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setEditSecureCodes-java.lang.Boolean-">setEditSecureCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editSecureCodes property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setImportXERMPPMPXandP3-java.lang.Boolean-">setImportXERMPPMPXandP3</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the importXERMPPMPXandP3 property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setImportXLS-java.lang.Boolean-">setImportXLS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the importXLS property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setImportXML-java.lang.Boolean-">setImportXML</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the importXML property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setProvisionUsersfromLDAP-java.lang.Boolean-">setProvisionUsersfromLDAP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the provisionUsersfromLDAP property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setViewAllGlobalAndProjectDataViaSDK-java.lang.Boolean-">setViewAllGlobalAndProjectDataViaSDK</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the viewAllGlobalAndProjectDataViaSDK property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setViewResourceAndRoleCostsAndFinancials-java.lang.Boolean-">setViewResourceAndRoleCostsAndFinancials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the viewResourceAndRoleCostsAndFinancials property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setViewResourceRoleProficiency-java.lang.Boolean-">setViewResourceRoleProficiency</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the viewResourceRoleProficiency property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html#setViewSecureCodes-java.lang.Boolean-">setViewSecureCodes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the viewSecureCodes property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="addDeleteSecureCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addDeleteSecureCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addDeleteSecureCodes</pre>
</li>
</ul>
<a name="addEditDeleteProjectTemplates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteProjectTemplates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteProjectTemplates</pre>
</li>
</ul>
<a name="addEditDeleteRiskCategoriesMatricesAndThresholds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteRiskCategoriesMatricesAndThresholds</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteRiskCategoriesMatricesAndThresholds</pre>
</li>
</ul>
<a name="addEditDeleteGlobalActivityAndAssignmentViewsAndFilters">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalActivityAndAssignmentViewsAndFilters</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalActivityAndAssignmentViewsAndFilters</pre>
</li>
</ul>
<a name="addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</pre>
</li>
</ul>
<a name="addGlobalActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGlobalActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addGlobalActivityCodes</pre>
</li>
</ul>
<a name="addGlobalIssueCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addGlobalIssueCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addGlobalIssueCodes</pre>
</li>
</ul>
<a name="addProjectCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addProjectCodes</pre>
</li>
</ul>
<a name="addResourceCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addResourceCodes</pre>
</li>
</ul>
<a name="addRoleCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoleCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addRoleCodes</pre>
</li>
</ul>
<a name="addResources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addResources</pre>
</li>
</ul>
<a name="administerGlobalExternalApplications">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>administerGlobalExternalApplications</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> administerGlobalExternalApplications</pre>
</li>
</ul>
<a name="administerProjectScheduledServices">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>administerProjectScheduledServices</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> administerProjectScheduledServices</pre>
</li>
</ul>
<a name="approveResourceTimesheets">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>approveResourceTimesheets</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> approveResourceTimesheets</pre>
</li>
</ul>
<a name="assignSecureCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignSecureCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> assignSecureCodes</pre>
</li>
</ul>
<a name="deleteGlobalActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGlobalActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteGlobalActivityCodes</pre>
</li>
</ul>
<a name="deleteGlobalIssueCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteGlobalIssueCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteGlobalIssueCodes</pre>
</li>
</ul>
<a name="deleteProjectCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteProjectCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteProjectCodes</pre>
</li>
</ul>
<a name="deleteResourceCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteResourceCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteResourceCodes</pre>
</li>
</ul>
<a name="deleteRoleCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteRoleCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteRoleCodes</pre>
</li>
</ul>
<a name="deleteResources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteResources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> deleteResources</pre>
</li>
</ul>
<a name="addEditDeleteActivityStepTemplates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteActivityStepTemplates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteActivityStepTemplates</pre>
</li>
</ul>
<a name="addEditDeleteCategoriesAndOverheadCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteCategoriesAndOverheadCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteCategoriesAndOverheadCodes</pre>
</li>
</ul>
<a name="addEditDeleteCostAccounts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteCostAccounts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteCostAccounts</pre>
</li>
</ul>
<a name="addEditDeleteCurrencies">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteCurrencies</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteCurrencies</pre>
</li>
</ul>
<a name="addEditDeleteFinancialPeriodDates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteFinancialPeriodDates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteFinancialPeriodDates</pre>
</li>
</ul>
<a name="addEditDeleteFundingSources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteFundingSources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteFundingSources</pre>
</li>
</ul>
<a name="editGlobalActivityCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editGlobalActivityCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editGlobalActivityCodes</pre>
</li>
</ul>
<a name="addEditDeleteGlobalCalendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalCalendars</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalCalendars</pre>
</li>
</ul>
<a name="editGlobalChangeDefinitions">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editGlobalChangeDefinitions</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editGlobalChangeDefinitions</pre>
</li>
</ul>
<a name="addEditDeleteGlobalDashboards">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalDashboards</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalDashboards</pre>
</li>
</ul>
<a name="editGlobalIssueCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editGlobalIssueCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editGlobalIssueCodes</pre>
</li>
</ul>
<a name="addEditDeleteGlobalPortfolios">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalPortfolios</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalPortfolios</pre>
</li>
</ul>
<a name="addEditDeleteGlobalReports">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalReports</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalReports</pre>
</li>
</ul>
<a name="addEditDeleteGlobalResourceAndRoleTeams">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalResourceAndRoleTeams</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalResourceAndRoleTeams</pre>
</li>
</ul>
<a name="addEditDeleteGlobalScenarios">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteGlobalScenarios</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteGlobalScenarios</pre>
</li>
</ul>
<a name="editGlobalTrackingLayouts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editGlobalTrackingLayouts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editGlobalTrackingLayouts</pre>
</li>
</ul>
<a name="addEditDeleteIssueForms">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteIssueForms</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteIssueForms</pre>
</li>
</ul>
<a name="addEditDeleteMSPTemplates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteMSPTemplates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteMSPTemplates</pre>
</li>
</ul>
<a name="addEditDeleteOBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteOBS</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteOBS</pre>
</li>
</ul>
<a name="editProjectCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editProjectCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editProjectCodes</pre>
</li>
</ul>
<a name="editProjectsFromScorecards">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editProjectsFromScorecards</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editProjectsFromScorecards</pre>
</li>
</ul>
<a name="addEditDeleteResourceCalendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteResourceCalendars</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteResourceCalendars</pre>
</li>
</ul>
<a name="editResourceCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editResourceCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editResourceCodes</pre>
</li>
</ul>
<a name="editRoleCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editRoleCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editRoleCodes</pre>
</li>
</ul>
<a name="addEditDeleteResourceCurves">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteResourceCurves</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteResourceCurves</pre>
</li>
</ul>
<a name="editResources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editResources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editResources</pre>
</li>
</ul>
<a name="addEditDeleteRoles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteRoles</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteRoles</pre>
</li>
</ul>
<a name="editSecureCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editSecureCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editSecureCodes</pre>
</li>
</ul>
<a name="addEditDeleteSecurityProfiles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteSecurityProfiles</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteSecurityProfiles</pre>
</li>
</ul>
<a name="addEditDeleteTimesheetPeriodDates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteTimesheetPeriodDates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteTimesheetPeriodDates</pre>
</li>
</ul>
<a name="addEditDeleteUserDefinedFields">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteUserDefinedFields</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteUserDefinedFields</pre>
</li>
</ul>
<a name="addEditDeleteUserInterfaceViews">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteUserInterfaceViews</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteUserInterfaceViews</pre>
</li>
</ul>
<a name="addEditDeleteUsers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteUsers</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteUsers</pre>
</li>
</ul>
<a name="importXERMPPMPXandP3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importXERMPPMPXandP3</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> importXERMPPMPXandP3</pre>
</li>
</ul>
<a name="importXML">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importXML</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> importXML</pre>
</li>
</ul>
<a name="importXLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importXLS</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> importXLS</pre>
</li>
</ul>
<a name="viewAllGlobalAndProjectDataViaSDK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewAllGlobalAndProjectDataViaSDK</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> viewAllGlobalAndProjectDataViaSDK</pre>
</li>
</ul>
<a name="viewResourceAndRoleCostsAndFinancials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewResourceAndRoleCostsAndFinancials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> viewResourceAndRoleCostsAndFinancials</pre>
</li>
</ul>
<a name="viewResourceRoleProficiency">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewResourceRoleProficiency</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> viewResourceRoleProficiency</pre>
</li>
</ul>
<a name="viewSecureCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewSecureCodes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> viewSecureCodes</pre>
</li>
</ul>
<a name="addEditDeleteRateTypesandUnitsofMeasure">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addEditDeleteRateTypesandUnitsofMeasure</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteRateTypesandUnitsofMeasure</pre>
</li>
</ul>
<a name="editApplicationSettings">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editApplicationSettings</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editApplicationSettings</pre>
</li>
</ul>
<a name="provisionUsersfromLDAP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>provisionUsersfromLDAP</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> provisionUsersfromLDAP</pre>
</li>
</ul>
<a name="administerGlobalScheduledServices">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>administerGlobalScheduledServices</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> administerGlobalScheduledServices</pre>
</li>
</ul>
<a name="addEditDeleteLocations">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addEditDeleteLocations</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> addEditDeleteLocations</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GlobalPrivilegesType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GlobalPrivilegesType</h4>
<pre>public&nbsp;GlobalPrivilegesType()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isAddDeleteSecureCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddDeleteSecureCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddDeleteSecureCodes()</pre>
<div class="block">Gets the value of the addDeleteSecureCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddDeleteSecureCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddDeleteSecureCodes</h4>
<pre>public&nbsp;void&nbsp;setAddDeleteSecureCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addDeleteSecureCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteProjectTemplates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteProjectTemplates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteProjectTemplates()</pre>
<div class="block">Gets the value of the addEditDeleteProjectTemplates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteProjectTemplates-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteProjectTemplates</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteProjectTemplates(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteProjectTemplates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteRiskCategoriesMatricesAndThresholds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteRiskCategoriesMatricesAndThresholds</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteRiskCategoriesMatricesAndThresholds()</pre>
<div class="block">Gets the value of the addEditDeleteRiskCategoriesMatricesAndThresholds property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteRiskCategoriesMatricesAndThresholds-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteRiskCategoriesMatricesAndThresholds</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteRiskCategoriesMatricesAndThresholds(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteRiskCategoriesMatricesAndThresholds property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalActivityAndAssignmentViewsAndFilters property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalActivityAndAssignmentViewsAndFilters(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalActivityAndAssignmentViewsAndFilters property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalProjectWBSPortfolioViewsAndFilters(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalProjectWBSPortfolioViewsAndFilters property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddGlobalActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddGlobalActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddGlobalActivityCodes()</pre>
<div class="block">Gets the value of the addGlobalActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddGlobalActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddGlobalActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setAddGlobalActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addGlobalActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddGlobalIssueCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddGlobalIssueCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddGlobalIssueCodes()</pre>
<div class="block">Gets the value of the addGlobalIssueCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddGlobalIssueCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddGlobalIssueCodes</h4>
<pre>public&nbsp;void&nbsp;setAddGlobalIssueCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addGlobalIssueCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddProjectCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddProjectCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddProjectCodes()</pre>
<div class="block">Gets the value of the addProjectCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddProjectCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddProjectCodes</h4>
<pre>public&nbsp;void&nbsp;setAddProjectCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addProjectCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddResourceCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddResourceCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddResourceCodes()</pre>
<div class="block">Gets the value of the addResourceCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddResourceCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddResourceCodes</h4>
<pre>public&nbsp;void&nbsp;setAddResourceCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addResourceCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddRoleCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddRoleCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddRoleCodes()</pre>
<div class="block">Gets the value of the addRoleCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddRoleCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddRoleCodes</h4>
<pre>public&nbsp;void&nbsp;setAddRoleCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addRoleCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddResources()</pre>
<div class="block">Gets the value of the addResources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddResources-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddResources</h4>
<pre>public&nbsp;void&nbsp;setAddResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addResources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdministerGlobalExternalApplications--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdministerGlobalExternalApplications</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAdministerGlobalExternalApplications()</pre>
<div class="block">Gets the value of the administerGlobalExternalApplications property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdministerGlobalExternalApplications-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdministerGlobalExternalApplications</h4>
<pre>public&nbsp;void&nbsp;setAdministerGlobalExternalApplications(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the administerGlobalExternalApplications property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdministerProjectScheduledServices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdministerProjectScheduledServices</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAdministerProjectScheduledServices()</pre>
<div class="block">Gets the value of the administerProjectScheduledServices property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdministerProjectScheduledServices-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdministerProjectScheduledServices</h4>
<pre>public&nbsp;void&nbsp;setAdministerProjectScheduledServices(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the administerProjectScheduledServices property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isApproveResourceTimesheets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isApproveResourceTimesheets</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isApproveResourceTimesheets()</pre>
<div class="block">Gets the value of the approveResourceTimesheets property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setApproveResourceTimesheets-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApproveResourceTimesheets</h4>
<pre>public&nbsp;void&nbsp;setApproveResourceTimesheets(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the approveResourceTimesheets property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAssignSecureCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAssignSecureCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAssignSecureCodes()</pre>
<div class="block">Gets the value of the assignSecureCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssignSecureCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignSecureCodes</h4>
<pre>public&nbsp;void&nbsp;setAssignSecureCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assignSecureCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteGlobalActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteGlobalActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteGlobalActivityCodes()</pre>
<div class="block">Gets the value of the deleteGlobalActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteGlobalActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteGlobalActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteGlobalActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteGlobalActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteGlobalIssueCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteGlobalIssueCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteGlobalIssueCodes()</pre>
<div class="block">Gets the value of the deleteGlobalIssueCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteGlobalIssueCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteGlobalIssueCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteGlobalIssueCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteGlobalIssueCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteProjectCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteProjectCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteProjectCodes()</pre>
<div class="block">Gets the value of the deleteProjectCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteProjectCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteProjectCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteProjectCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteProjectCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteResourceCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteResourceCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteResourceCodes()</pre>
<div class="block">Gets the value of the deleteResourceCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteResourceCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteResourceCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteResourceCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteResourceCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteRoleCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteRoleCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteRoleCodes()</pre>
<div class="block">Gets the value of the deleteRoleCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteRoleCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteRoleCodes</h4>
<pre>public&nbsp;void&nbsp;setDeleteRoleCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteRoleCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDeleteResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeleteResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDeleteResources()</pre>
<div class="block">Gets the value of the deleteResources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDeleteResources-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeleteResources</h4>
<pre>public&nbsp;void&nbsp;setDeleteResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the deleteResources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteActivityStepTemplates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteActivityStepTemplates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteActivityStepTemplates()</pre>
<div class="block">Gets the value of the addEditDeleteActivityStepTemplates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteActivityStepTemplates-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteActivityStepTemplates</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteActivityStepTemplates(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteActivityStepTemplates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteCategoriesAndOverheadCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteCategoriesAndOverheadCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteCategoriesAndOverheadCodes()</pre>
<div class="block">Gets the value of the addEditDeleteCategoriesAndOverheadCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteCategoriesAndOverheadCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteCategoriesAndOverheadCodes</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteCategoriesAndOverheadCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteCategoriesAndOverheadCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteCostAccounts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteCostAccounts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteCostAccounts()</pre>
<div class="block">Gets the value of the addEditDeleteCostAccounts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteCostAccounts-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteCostAccounts</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteCostAccounts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteCostAccounts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteCurrencies--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteCurrencies</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteCurrencies()</pre>
<div class="block">Gets the value of the addEditDeleteCurrencies property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteCurrencies-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteCurrencies</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteCurrencies(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteCurrencies property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteFinancialPeriodDates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteFinancialPeriodDates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteFinancialPeriodDates()</pre>
<div class="block">Gets the value of the addEditDeleteFinancialPeriodDates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteFinancialPeriodDates-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteFinancialPeriodDates</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteFinancialPeriodDates(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteFinancialPeriodDates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteFundingSources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteFundingSources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteFundingSources()</pre>
<div class="block">Gets the value of the addEditDeleteFundingSources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteFundingSources-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteFundingSources</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteFundingSources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteFundingSources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditGlobalActivityCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditGlobalActivityCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditGlobalActivityCodes()</pre>
<div class="block">Gets the value of the editGlobalActivityCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditGlobalActivityCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditGlobalActivityCodes</h4>
<pre>public&nbsp;void&nbsp;setEditGlobalActivityCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editGlobalActivityCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalCalendars</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalCalendars()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalCalendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalCalendars-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalCalendars</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalCalendars(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalCalendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditGlobalChangeDefinitions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditGlobalChangeDefinitions</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditGlobalChangeDefinitions()</pre>
<div class="block">Gets the value of the editGlobalChangeDefinitions property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditGlobalChangeDefinitions-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditGlobalChangeDefinitions</h4>
<pre>public&nbsp;void&nbsp;setEditGlobalChangeDefinitions(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editGlobalChangeDefinitions property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalDashboards--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalDashboards</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalDashboards()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalDashboards property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalDashboards-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalDashboards</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalDashboards(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalDashboards property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditGlobalIssueCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditGlobalIssueCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditGlobalIssueCodes()</pre>
<div class="block">Gets the value of the editGlobalIssueCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditGlobalIssueCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditGlobalIssueCodes</h4>
<pre>public&nbsp;void&nbsp;setEditGlobalIssueCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editGlobalIssueCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalPortfolios--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalPortfolios</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalPortfolios()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalPortfolios property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalPortfolios-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalPortfolios</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalPortfolios(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalPortfolios property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalReports--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalReports</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalReports()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalReports property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalReports-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalReports</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalReports(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalReports property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalResourceAndRoleTeams--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalResourceAndRoleTeams</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalResourceAndRoleTeams()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalResourceAndRoleTeams property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalResourceAndRoleTeams-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalResourceAndRoleTeams</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalResourceAndRoleTeams(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalResourceAndRoleTeams property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteGlobalScenarios--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteGlobalScenarios</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteGlobalScenarios()</pre>
<div class="block">Gets the value of the addEditDeleteGlobalScenarios property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteGlobalScenarios-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteGlobalScenarios</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteGlobalScenarios(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteGlobalScenarios property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditGlobalTrackingLayouts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditGlobalTrackingLayouts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditGlobalTrackingLayouts()</pre>
<div class="block">Gets the value of the editGlobalTrackingLayouts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditGlobalTrackingLayouts-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditGlobalTrackingLayouts</h4>
<pre>public&nbsp;void&nbsp;setEditGlobalTrackingLayouts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editGlobalTrackingLayouts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteIssueForms--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteIssueForms</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteIssueForms()</pre>
<div class="block">Gets the value of the addEditDeleteIssueForms property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteIssueForms-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteIssueForms</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteIssueForms(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteIssueForms property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteMSPTemplates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteMSPTemplates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteMSPTemplates()</pre>
<div class="block">Gets the value of the addEditDeleteMSPTemplates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteMSPTemplates-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteMSPTemplates</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteMSPTemplates(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteMSPTemplates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteOBS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteOBS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteOBS()</pre>
<div class="block">Gets the value of the addEditDeleteOBS property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteOBS-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteOBS</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteOBS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteOBS property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditProjectCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditProjectCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditProjectCodes()</pre>
<div class="block">Gets the value of the editProjectCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditProjectCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditProjectCodes</h4>
<pre>public&nbsp;void&nbsp;setEditProjectCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editProjectCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditProjectsFromScorecards--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditProjectsFromScorecards</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditProjectsFromScorecards()</pre>
<div class="block">Gets the value of the editProjectsFromScorecards property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditProjectsFromScorecards-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditProjectsFromScorecards</h4>
<pre>public&nbsp;void&nbsp;setEditProjectsFromScorecards(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editProjectsFromScorecards property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteResourceCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteResourceCalendars</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteResourceCalendars()</pre>
<div class="block">Gets the value of the addEditDeleteResourceCalendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteResourceCalendars-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteResourceCalendars</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteResourceCalendars(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteResourceCalendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditResourceCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditResourceCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditResourceCodes()</pre>
<div class="block">Gets the value of the editResourceCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditResourceCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditResourceCodes</h4>
<pre>public&nbsp;void&nbsp;setEditResourceCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editResourceCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditRoleCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditRoleCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditRoleCodes()</pre>
<div class="block">Gets the value of the editRoleCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditRoleCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditRoleCodes</h4>
<pre>public&nbsp;void&nbsp;setEditRoleCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editRoleCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteResourceCurves--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteResourceCurves</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteResourceCurves()</pre>
<div class="block">Gets the value of the addEditDeleteResourceCurves property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteResourceCurves-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteResourceCurves</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteResourceCurves(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteResourceCurves property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditResources()</pre>
<div class="block">Gets the value of the editResources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditResources-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditResources</h4>
<pre>public&nbsp;void&nbsp;setEditResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editResources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteRoles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteRoles</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteRoles()</pre>
<div class="block">Gets the value of the addEditDeleteRoles property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteRoles-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteRoles</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteRoles(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteRoles property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditSecureCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditSecureCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditSecureCodes()</pre>
<div class="block">Gets the value of the editSecureCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditSecureCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditSecureCodes</h4>
<pre>public&nbsp;void&nbsp;setEditSecureCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editSecureCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteSecurityProfiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteSecurityProfiles</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteSecurityProfiles()</pre>
<div class="block">Gets the value of the addEditDeleteSecurityProfiles property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteSecurityProfiles-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteSecurityProfiles</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteSecurityProfiles(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteSecurityProfiles property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteTimesheetPeriodDates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteTimesheetPeriodDates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteTimesheetPeriodDates()</pre>
<div class="block">Gets the value of the addEditDeleteTimesheetPeriodDates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteTimesheetPeriodDates-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteTimesheetPeriodDates</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteTimesheetPeriodDates(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteTimesheetPeriodDates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteUserDefinedFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteUserDefinedFields</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteUserDefinedFields()</pre>
<div class="block">Gets the value of the addEditDeleteUserDefinedFields property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteUserDefinedFields-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteUserDefinedFields</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteUserDefinedFields(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteUserDefinedFields property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteUserInterfaceViews--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteUserInterfaceViews</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteUserInterfaceViews()</pre>
<div class="block">Gets the value of the addEditDeleteUserInterfaceViews property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteUserInterfaceViews-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteUserInterfaceViews</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteUserInterfaceViews(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteUserInterfaceViews property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteUsers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteUsers</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteUsers()</pre>
<div class="block">Gets the value of the addEditDeleteUsers property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteUsers-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteUsers</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteUsers(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteUsers property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isImportXERMPPMPXandP3--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isImportXERMPPMPXandP3</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isImportXERMPPMPXandP3()</pre>
<div class="block">Gets the value of the importXERMPPMPXandP3 property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setImportXERMPPMPXandP3-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImportXERMPPMPXandP3</h4>
<pre>public&nbsp;void&nbsp;setImportXERMPPMPXandP3(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the importXERMPPMPXandP3 property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isImportXML--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isImportXML</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isImportXML()</pre>
<div class="block">Gets the value of the importXML property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setImportXML-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImportXML</h4>
<pre>public&nbsp;void&nbsp;setImportXML(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the importXML property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isImportXLS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isImportXLS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isImportXLS()</pre>
<div class="block">Gets the value of the importXLS property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setImportXLS-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImportXLS</h4>
<pre>public&nbsp;void&nbsp;setImportXLS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the importXLS property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isViewAllGlobalAndProjectDataViaSDK--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewAllGlobalAndProjectDataViaSDK</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isViewAllGlobalAndProjectDataViaSDK()</pre>
<div class="block">Gets the value of the viewAllGlobalAndProjectDataViaSDK property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setViewAllGlobalAndProjectDataViaSDK-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewAllGlobalAndProjectDataViaSDK</h4>
<pre>public&nbsp;void&nbsp;setViewAllGlobalAndProjectDataViaSDK(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the viewAllGlobalAndProjectDataViaSDK property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isViewResourceAndRoleCostsAndFinancials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewResourceAndRoleCostsAndFinancials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isViewResourceAndRoleCostsAndFinancials()</pre>
<div class="block">Gets the value of the viewResourceAndRoleCostsAndFinancials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setViewResourceAndRoleCostsAndFinancials-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewResourceAndRoleCostsAndFinancials</h4>
<pre>public&nbsp;void&nbsp;setViewResourceAndRoleCostsAndFinancials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the viewResourceAndRoleCostsAndFinancials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isViewResourceRoleProficiency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewResourceRoleProficiency</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isViewResourceRoleProficiency()</pre>
<div class="block">Gets the value of the viewResourceRoleProficiency property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setViewResourceRoleProficiency-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewResourceRoleProficiency</h4>
<pre>public&nbsp;void&nbsp;setViewResourceRoleProficiency(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the viewResourceRoleProficiency property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isViewSecureCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewSecureCodes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isViewSecureCodes()</pre>
<div class="block">Gets the value of the viewSecureCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setViewSecureCodes-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewSecureCodes</h4>
<pre>public&nbsp;void&nbsp;setViewSecureCodes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the viewSecureCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteRateTypesandUnitsofMeasure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteRateTypesandUnitsofMeasure</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteRateTypesandUnitsofMeasure()</pre>
<div class="block">Gets the value of the addEditDeleteRateTypesandUnitsofMeasure property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteRateTypesandUnitsofMeasure-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAddEditDeleteRateTypesandUnitsofMeasure</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteRateTypesandUnitsofMeasure(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteRateTypesandUnitsofMeasure property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditApplicationSettings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditApplicationSettings</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditApplicationSettings()</pre>
<div class="block">Gets the value of the editApplicationSettings property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditApplicationSettings-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditApplicationSettings</h4>
<pre>public&nbsp;void&nbsp;setEditApplicationSettings(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editApplicationSettings property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isProvisionUsersfromLDAP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProvisionUsersfromLDAP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isProvisionUsersfromLDAP()</pre>
<div class="block">Gets the value of the provisionUsersfromLDAP property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProvisionUsersfromLDAP-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProvisionUsersfromLDAP</h4>
<pre>public&nbsp;void&nbsp;setProvisionUsersfromLDAP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the provisionUsersfromLDAP property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdministerGlobalScheduledServices--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdministerGlobalScheduledServices</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAdministerGlobalScheduledServices()</pre>
<div class="block">Gets the value of the administerGlobalScheduledServices property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdministerGlobalScheduledServices-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdministerGlobalScheduledServices</h4>
<pre>public&nbsp;void&nbsp;setAdministerGlobalScheduledServices(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the administerGlobalScheduledServices property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAddEditDeleteLocations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAddEditDeleteLocations</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAddEditDeleteLocations()</pre>
<div class="block">Gets the value of the addEditDeleteLocations property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAddEditDeleteLocations-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAddEditDeleteLocations</h4>
<pre>public&nbsp;void&nbsp;setAddEditDeleteLocations(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the addEditDeleteLocations property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GlobalPrivilegesType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/GlobalPrivilegesType.html" target="_top">Frames</a></li>
<li><a href="GlobalPrivilegesType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
