<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ObjectFactory (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectFactory (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ObjectFactory" class="title">Class ObjectFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ObjectFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ObjectFactory</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This object contains factory methods for each
 Java content interface and Java element interface
 generated in the org.mpxj.primavera.schema package.
 <p>An ObjectFactory allows you to programatically
 construct new instances of the Java representation
 for XML content. The Java representation of XML
 content can consist of schema derived interfaces
 and classes representing the binding of schema
 type definitions, element declarations and model
 groups.  Factory methods for each of these are
 provided in this class.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#ObjectFactory--">ObjectFactory</a></span>()</code>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.primavera.schema</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ActivityCodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityCodeAssignmentType--">createActivityCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityCodeType.html" title="class in org.mpxj.primavera.schema">ActivityCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityCodeType--">createActivityCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeType</code></a></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityCodeTypeType.html" title="class in org.mpxj.primavera.schema">ActivityCodeTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityCodeTypeType--">createActivityCodeTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeTypeType</code></a></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityCodeUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityCodeUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityCodeUpdateType--">createActivityCodeUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeUpdateType</code></a></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityCommentType.html" title="class in org.mpxj.primavera.schema">ActivityCommentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityCommentType--">createActivityCommentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCommentType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCommentType</code></a></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityExpenseType.html" title="class in org.mpxj.primavera.schema">ActivityExpenseType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityExpenseType--">createActivityExpenseType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityExpenseType.html" title="class in org.mpxj.primavera.schema"><code>ActivityExpenseType</code></a></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityFilterType.html" title="class in org.mpxj.primavera.schema">ActivityFilterType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityFilterType--">createActivityFilterType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityFilterType.html" title="class in org.mpxj.primavera.schema"><code>ActivityFilterType</code></a></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityNoteType.html" title="class in org.mpxj.primavera.schema">ActivityNoteType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityNoteType--">createActivityNoteType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityNoteType.html" title="class in org.mpxj.primavera.schema"><code>ActivityNoteType</code></a></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityNoteUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityNoteUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityNoteUpdateType--">createActivityNoteUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityNoteUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityNoteUpdateType</code></a></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityOwnerType.html" title="class in org.mpxj.primavera.schema">ActivityOwnerType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityOwnerType--">createActivityOwnerType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityOwnerType.html" title="class in org.mpxj.primavera.schema"><code>ActivityOwnerType</code></a></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityPeriodActualType.html" title="class in org.mpxj.primavera.schema">ActivityPeriodActualType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityPeriodActualType--">createActivityPeriodActualType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityPeriodActualType.html" title="class in org.mpxj.primavera.schema"><code>ActivityPeriodActualType</code></a></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityRiskType.html" title="class in org.mpxj.primavera.schema">ActivityRiskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityRiskType--">createActivityRiskType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityRiskType.html" title="class in org.mpxj.primavera.schema"><code>ActivityRiskType</code></a></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema">ActivitySpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivitySpreadType--">createActivitySpreadType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema"><code>ActivitySpreadType</code></a></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html" title="class in org.mpxj.primavera.schema">ActivitySpreadType.Period</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivitySpreadTypePeriod--">createActivitySpreadTypePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ActivitySpreadType.Period</code></a></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema">ActivityStepCreateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepCreateType--">createActivityStepCreateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepCreateType</code></a></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepDeleteType.html" title="class in org.mpxj.primavera.schema">ActivityStepDeleteType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepDeleteType--">createActivityStepDeleteType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepDeleteType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepDeleteType</code></a></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateItemType.html" title="class in org.mpxj.primavera.schema">ActivityStepTemplateItemType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepTemplateItemType--">createActivityStepTemplateItemType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateItemType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepTemplateItemType</code></a></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateType.html" title="class in org.mpxj.primavera.schema">ActivityStepTemplateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepTemplateType--">createActivityStepTemplateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepTemplateType</code></a></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepType.html" title="class in org.mpxj.primavera.schema">ActivityStepType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepType--">createActivityStepType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepType</code></a></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityStepUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityStepUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityStepUpdateType--">createActivityStepUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepUpdateType</code></a></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityType.html" title="class in org.mpxj.primavera.schema">ActivityType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityType--">createActivityType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityType.html" title="class in org.mpxj.primavera.schema"><code>ActivityType</code></a></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ActivityUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createActivityUpdateType--">createActivityUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityUpdateType</code></a></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/AlertType.html" title="class in org.mpxj.primavera.schema">AlertType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createAlertType--">createAlertType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/AlertType.html" title="class in org.mpxj.primavera.schema"><code>AlertType</code></a></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/APIBusinessObjects.html" title="class in org.mpxj.primavera.schema">APIBusinessObjects</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createAPIBusinessObjects--">createAPIBusinessObjects</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/APIBusinessObjects.html" title="class in org.mpxj.primavera.schema"><code>APIBusinessObjects</code></a></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/AutovueAttrType.html" title="class in org.mpxj.primavera.schema">AutovueAttrType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createAutovueAttrType--">createAutovueAttrType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/AutovueAttrType.html" title="class in org.mpxj.primavera.schema"><code>AutovueAttrType</code></a></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/BaselineProjectType.html" title="class in org.mpxj.primavera.schema">BaselineProjectType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createBaselineProjectType--">createBaselineProjectType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/BaselineProjectType.html" title="class in org.mpxj.primavera.schema"><code>BaselineProjectType</code></a></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/BaselineTypeType.html" title="class in org.mpxj.primavera.schema">BaselineTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createBaselineTypeType--">createBaselineTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/BaselineTypeType.html" title="class in org.mpxj.primavera.schema"><code>BaselineTypeType</code></a></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CalendarType.html" title="class in org.mpxj.primavera.schema">CalendarType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCalendarType--">createCalendarType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.html" title="class in org.mpxj.primavera.schema"><code>CalendarType</code></a></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.html" title="class in org.mpxj.primavera.schema">CalendarType.HolidayOrExceptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCalendarTypeHolidayOrExceptions--">createCalendarTypeHolidayOrExceptions</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.HolidayOrExceptions</code></a></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.HolidayOrException.html" title="class in org.mpxj.primavera.schema">CalendarType.HolidayOrExceptions.HolidayOrException</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCalendarTypeHolidayOrExceptionsHolidayOrException--">createCalendarTypeHolidayOrExceptionsHolidayOrException</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.HolidayOrException.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.HolidayOrExceptions.HolidayOrException</code></a></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.html" title="class in org.mpxj.primavera.schema">CalendarType.StandardWorkWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCalendarTypeStandardWorkWeek--">createCalendarTypeStandardWorkWeek</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.StandardWorkWeek</code></a></div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.StandardWorkHours.html" title="class in org.mpxj.primavera.schema">CalendarType.StandardWorkWeek.StandardWorkHours</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCalendarTypeStandardWorkWeekStandardWorkHours--">createCalendarTypeStandardWorkWeekStandardWorkHours</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.StandardWorkHours.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.StandardWorkWeek.StandardWorkHours</code></a></div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CBSDurationSummaryType.html" title="class in org.mpxj.primavera.schema">CBSDurationSummaryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCBSDurationSummaryType--">createCBSDurationSummaryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CBSDurationSummaryType.html" title="class in org.mpxj.primavera.schema"><code>CBSDurationSummaryType</code></a></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CBSType.html" title="class in org.mpxj.primavera.schema">CBSType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCBSType--">createCBSType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CBSType.html" title="class in org.mpxj.primavera.schema"><code>CBSType</code></a></div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ChangeSetType.html" title="class in org.mpxj.primavera.schema">ChangeSetType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createChangeSetType--">createChangeSetType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ChangeSetType.html" title="class in org.mpxj.primavera.schema"><code>ChangeSetType</code></a></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCodeAssignmentType--">createCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>CodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CostAccountType.html" title="class in org.mpxj.primavera.schema">CostAccountType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCostAccountType--">createCostAccountType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CostAccountType.html" title="class in org.mpxj.primavera.schema"><code>CostAccountType</code></a></div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/CurrencyType.html" title="class in org.mpxj.primavera.schema">CurrencyType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createCurrencyType--">createCurrencyType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CurrencyType.html" title="class in org.mpxj.primavera.schema"><code>CurrencyType</code></a></div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/DisplayCurrencyType.html" title="class in org.mpxj.primavera.schema">DisplayCurrencyType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createDisplayCurrencyType--">createDisplayCurrencyType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DisplayCurrencyType.html" title="class in org.mpxj.primavera.schema"><code>DisplayCurrencyType</code></a></div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/DocumentCategoryType.html" title="class in org.mpxj.primavera.schema">DocumentCategoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createDocumentCategoryType--">createDocumentCategoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentCategoryType.html" title="class in org.mpxj.primavera.schema"><code>DocumentCategoryType</code></a></div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/DocumentStatusCodeType.html" title="class in org.mpxj.primavera.schema">DocumentStatusCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createDocumentStatusCodeType--">createDocumentStatusCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentStatusCodeType.html" title="class in org.mpxj.primavera.schema"><code>DocumentStatusCodeType</code></a></div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/DocumentType.html" title="class in org.mpxj.primavera.schema">DocumentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createDocumentType--">createDocumentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentType.html" title="class in org.mpxj.primavera.schema"><code>DocumentType</code></a></div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema">EPSBudgetChangeLogType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSBudgetChangeLogType--">createEPSBudgetChangeLogType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><code>EPSBudgetChangeLogType</code></a></div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSFundingType.html" title="class in org.mpxj.primavera.schema">EPSFundingType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSFundingType--">createEPSFundingType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSFundingType.html" title="class in org.mpxj.primavera.schema"><code>EPSFundingType</code></a></div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSNoteType.html" title="class in org.mpxj.primavera.schema">EPSNoteType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSNoteType--">createEPSNoteType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSNoteType.html" title="class in org.mpxj.primavera.schema"><code>EPSNoteType</code></a></div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema">EPSProjectWBSSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSProjectWBSSpreadType--">createEPSProjectWBSSpreadType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema"><code>EPSProjectWBSSpreadType</code></a></div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" title="class in org.mpxj.primavera.schema">EPSProjectWBSSpreadType.Period</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSProjectWBSSpreadTypePeriod--">createEPSProjectWBSSpreadTypePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>EPSProjectWBSSpreadType.Period</code></a></div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema">EPSSpendingPlanType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSSpendingPlanType--">createEPSSpendingPlanType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><code>EPSSpendingPlanType</code></a></div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/EPSType.html" title="class in org.mpxj.primavera.schema">EPSType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createEPSType--">createEPSType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSType.html" title="class in org.mpxj.primavera.schema"><code>EPSType</code></a></div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ExpenseCategoryType.html" title="class in org.mpxj.primavera.schema">ExpenseCategoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createExpenseCategoryType--">createExpenseCategoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ExpenseCategoryType.html" title="class in org.mpxj.primavera.schema"><code>ExpenseCategoryType</code></a></div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/FinancialPeriodTemplateType.html" title="class in org.mpxj.primavera.schema">FinancialPeriodTemplateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createFinancialPeriodTemplateType--">createFinancialPeriodTemplateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FinancialPeriodTemplateType.html" title="class in org.mpxj.primavera.schema"><code>FinancialPeriodTemplateType</code></a></div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/FinancialPeriodType.html" title="class in org.mpxj.primavera.schema">FinancialPeriodType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createFinancialPeriodType--">createFinancialPeriodType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FinancialPeriodType.html" title="class in org.mpxj.primavera.schema"><code>FinancialPeriodType</code></a></div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/FundingSourceType.html" title="class in org.mpxj.primavera.schema">FundingSourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createFundingSourceType--">createFundingSourceType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FundingSourceType.html" title="class in org.mpxj.primavera.schema"><code>FundingSourceType</code></a></div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/GatewayDeploymentType.html" title="class in org.mpxj.primavera.schema">GatewayDeploymentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createGatewayDeploymentType--">createGatewayDeploymentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GatewayDeploymentType.html" title="class in org.mpxj.primavera.schema"><code>GatewayDeploymentType</code></a></div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema">GlobalPreferencesType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createGlobalPreferencesType--">createGlobalPreferencesType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema"><code>GlobalPreferencesType</code></a></div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html" title="class in org.mpxj.primavera.schema">GlobalPrivilegesType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createGlobalPrivilegesType--">createGlobalPrivilegesType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html" title="class in org.mpxj.primavera.schema"><code>GlobalPrivilegesType</code></a></div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema">GlobalProfileType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createGlobalProfileType--">createGlobalProfileType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema"><code>GlobalProfileType</code></a></div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/GlobalReplaceType.html" title="class in org.mpxj.primavera.schema">GlobalReplaceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createGlobalReplaceType--">createGlobalReplaceType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalReplaceType.html" title="class in org.mpxj.primavera.schema"><code>GlobalReplaceType</code></a></div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ImportOptionsTemplateType.html" title="class in org.mpxj.primavera.schema">ImportOptionsTemplateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createImportOptionsTemplateType--">createImportOptionsTemplateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ImportOptionsTemplateType.html" title="class in org.mpxj.primavera.schema"><code>ImportOptionsTemplateType</code></a></div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/IssueHistoryType.html" title="class in org.mpxj.primavera.schema">IssueHistoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createIssueHistoryType--">createIssueHistoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/IssueHistoryType.html" title="class in org.mpxj.primavera.schema"><code>IssueHistoryType</code></a></div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/JobServiceType.html" title="class in org.mpxj.primavera.schema">JobServiceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createJobServiceType--">createJobServiceType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/JobServiceType.html" title="class in org.mpxj.primavera.schema"><code>JobServiceType</code></a></div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/LeanTaskType.html" title="class in org.mpxj.primavera.schema">LeanTaskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createLeanTaskType--">createLeanTaskType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/LeanTaskType.html" title="class in org.mpxj.primavera.schema"><code>LeanTaskType</code></a></div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/LocationType.html" title="class in org.mpxj.primavera.schema">LocationType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createLocationType--">createLocationType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/LocationType.html" title="class in org.mpxj.primavera.schema"><code>LocationType</code></a></div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/MSPTemplateType.html" title="class in org.mpxj.primavera.schema">MSPTemplateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createMSPTemplateType--">createMSPTemplateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/MSPTemplateType.html" title="class in org.mpxj.primavera.schema"><code>MSPTemplateType</code></a></div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema">NotebookTopicType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createNotebookTopicType--">createNotebookTopicType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema"><code>NotebookTopicType</code></a></div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema">OBSType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createOBSType--">createOBSType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema"><code>OBSType</code></a></div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/OverheadCodeType.html" title="class in org.mpxj.primavera.schema">OverheadCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createOverheadCodeType--">createOverheadCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/OverheadCodeType.html" title="class in org.mpxj.primavera.schema"><code>OverheadCodeType</code></a></div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/PAuditXType.html" title="class in org.mpxj.primavera.schema">PAuditXType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createPAuditXType--">createPAuditXType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/PAuditXType.html" title="class in org.mpxj.primavera.schema"><code>PAuditXType</code></a></div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/PortfolioTeamMemberType.html" title="class in org.mpxj.primavera.schema">PortfolioTeamMemberType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createPortfolioTeamMemberType--">createPortfolioTeamMemberType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/PortfolioTeamMemberType.html" title="class in org.mpxj.primavera.schema"><code>PortfolioTeamMemberType</code></a></div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProfileType.html" title="class in org.mpxj.primavera.schema">ProfileType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProfileType--">createProfileType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProfileType.html" title="class in org.mpxj.primavera.schema"><code>ProfileType</code></a></div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema">ProjectBudgetChangeLogType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectBudgetChangeLogType--">createProjectBudgetChangeLogType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><code>ProjectBudgetChangeLogType</code></a></div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ProjectCodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectCodeAssignmentType--">createProjectCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectCodeType.html" title="class in org.mpxj.primavera.schema">ProjectCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectCodeType--">createProjectCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeType</code></a></div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectCodeTypeType.html" title="class in org.mpxj.primavera.schema">ProjectCodeTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectCodeTypeType--">createProjectCodeTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeTypeType</code></a></div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectDeploymentType.html" title="class in org.mpxj.primavera.schema">ProjectDeploymentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectDeploymentType--">createProjectDeploymentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectDeploymentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectDeploymentType</code></a></div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectDocumentType.html" title="class in org.mpxj.primavera.schema">ProjectDocumentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectDocumentType--">createProjectDocumentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectDocumentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectDocumentType</code></a></div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectFundingType.html" title="class in org.mpxj.primavera.schema">ProjectFundingType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectFundingType--">createProjectFundingType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectFundingType.html" title="class in org.mpxj.primavera.schema"><code>ProjectFundingType</code></a></div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectIssueType.html" title="class in org.mpxj.primavera.schema">ProjectIssueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectIssueType--">createProjectIssueType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectIssueType.html" title="class in org.mpxj.primavera.schema"><code>ProjectIssueType</code></a></div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectListType.html" title="class in org.mpxj.primavera.schema">ProjectListType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectListType--">createProjectListType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType</code></a></div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.html" title="class in org.mpxj.primavera.schema">ProjectListType.Project</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectListTypeProject--">createProjectListTypeProject</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType.Project</code></a></div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.BaselineProject.html" title="class in org.mpxj.primavera.schema">ProjectListType.Project.BaselineProject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectListTypeProjectBaselineProject--">createProjectListTypeProjectBaselineProject</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.BaselineProject.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType.Project.BaselineProject</code></a></div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectNoteType.html" title="class in org.mpxj.primavera.schema">ProjectNoteType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectNoteType--">createProjectNoteType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectNoteType.html" title="class in org.mpxj.primavera.schema"><code>ProjectNoteType</code></a></div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema">ProjectPortfolioType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectPortfolioType--">createProjectPortfolioType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema"><code>ProjectPortfolioType</code></a></div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html" title="class in org.mpxj.primavera.schema">ProjectPrivilegesType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectPrivilegesType--">createProjectPrivilegesType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html" title="class in org.mpxj.primavera.schema"><code>ProjectPrivilegesType</code></a></div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema">ProjectProfileType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectProfileType--">createProjectProfileType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema"><code>ProjectProfileType</code></a></div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectResourceCategoryType.html" title="class in org.mpxj.primavera.schema">ProjectResourceCategoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectResourceCategoryType--">createProjectResourceCategoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceCategoryType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceCategoryType</code></a></div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema">ProjectResourceQuantityType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectResourceQuantityType--">createProjectResourceQuantityType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceQuantityType</code></a></div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectResourceSpreadType--">createProjectResourceSpreadType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceSpreadType</code></a></div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectResourceSpreadTypePeriod--">createProjectResourceSpreadTypePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceSpreadType.Period</code></a></div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectResourceType.html" title="class in org.mpxj.primavera.schema">ProjectResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectResourceType--">createProjectResourceType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceType</code></a></div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema">ProjectRoleSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectRoleSpreadType--">createProjectRoleSpreadType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ProjectRoleSpreadType</code></a></div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectRoleSpreadType.Period</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectRoleSpreadTypePeriod--">createProjectRoleSpreadTypePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ProjectRoleSpreadType.Period</code></a></div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema">ProjectSpendingPlanType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectSpendingPlanType--">createProjectSpendingPlanType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><code>ProjectSpendingPlanType</code></a></div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectThresholdType.html" title="class in org.mpxj.primavera.schema">ProjectThresholdType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectThresholdType--">createProjectThresholdType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectThresholdType.html" title="class in org.mpxj.primavera.schema"><code>ProjectThresholdType</code></a></div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ProjectType.html" title="class in org.mpxj.primavera.schema">ProjectType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createProjectType--">createProjectType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectType.html" title="class in org.mpxj.primavera.schema"><code>ProjectType</code></a></div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RelationshipType.html" title="class in org.mpxj.primavera.schema">RelationshipType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRelationshipType--">createRelationshipType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RelationshipType.html" title="class in org.mpxj.primavera.schema"><code>RelationshipType</code></a></div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAccessType.html" title="class in org.mpxj.primavera.schema">ResourceAccessType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAccessType--">createResourceAccessType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAccessType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAccessType</code></a></div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentCodeAssignmentType--">createResourceAssignmentCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentCodeType--">createResourceAssignmentCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeType</code></a></div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeTypeType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentCodeTypeType--">createResourceAssignmentCodeTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeTypeType</code></a></div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCreateType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCreateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentCreateType--">createResourceAssignmentCreateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCreateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCreateType</code></a></div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentPeriodActualType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentPeriodActualType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentPeriodActualType--">createResourceAssignmentPeriodActualType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentPeriodActualType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentPeriodActualType</code></a></div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentSpreadType--">createResourceAssignmentSpreadType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType</code></a></div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType.Period</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentSpreadTypePeriod--">createResourceAssignmentSpreadTypePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType.Period</code></a></div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentType--">createResourceAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentType</code></a></div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceAssignmentUpdateType--">createResourceAssignmentUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentUpdateType</code></a></div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceCodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceCodeAssignmentType--">createResourceCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceCodeType.html" title="class in org.mpxj.primavera.schema">ResourceCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceCodeType--">createResourceCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeType</code></a></div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceCodeTypeType.html" title="class in org.mpxj.primavera.schema">ResourceCodeTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceCodeTypeType--">createResourceCodeTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeTypeType</code></a></div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceCurveType.html" title="class in org.mpxj.primavera.schema">ResourceCurveType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceCurveType--">createResourceCurveType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCurveType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCurveType</code></a></div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceCurveValuesType.html" title="class in org.mpxj.primavera.schema">ResourceCurveValuesType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceCurveValuesType--">createResourceCurveValuesType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCurveValuesType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCurveValuesType</code></a></div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceHourType.html" title="class in org.mpxj.primavera.schema">ResourceHourType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceHourType--">createResourceHourType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceHourType.html" title="class in org.mpxj.primavera.schema"><code>ResourceHourType</code></a></div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceLocationType.html" title="class in org.mpxj.primavera.schema">ResourceLocationType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceLocationType--">createResourceLocationType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceLocationType.html" title="class in org.mpxj.primavera.schema"><code>ResourceLocationType</code></a></div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceRateType.html" title="class in org.mpxj.primavera.schema">ResourceRateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceRateType--">createResourceRateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRateType</code></a></div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceRequestType--">createResourceRequestType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType</code></a></div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.ResourceRequestCriterion.html" title="class in org.mpxj.primavera.schema">ResourceRequestType.ResourceRequestCriterion</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceRequestTypeResourceRequestCriterion--">createResourceRequestTypeResourceRequestCriterion</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.ResourceRequestCriterion.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType.ResourceRequestCriterion</code></a></div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceRoleType.html" title="class in org.mpxj.primavera.schema">ResourceRoleType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceRoleType--">createResourceRoleType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRoleType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRoleType</code></a></div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceTeamType.html" title="class in org.mpxj.primavera.schema">ResourceTeamType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceTeamType--">createResourceTeamType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceTeamType.html" title="class in org.mpxj.primavera.schema"><code>ResourceTeamType</code></a></div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceType.html" title="class in org.mpxj.primavera.schema">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createResourceType--">createResourceType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceType.html" title="class in org.mpxj.primavera.schema"><code>ResourceType</code></a></div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskCategoryType.html" title="class in org.mpxj.primavera.schema">RiskCategoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskCategoryType--">createRiskCategoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskCategoryType.html" title="class in org.mpxj.primavera.schema"><code>RiskCategoryType</code></a></div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskImpactType.html" title="class in org.mpxj.primavera.schema">RiskImpactType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskImpactType--">createRiskImpactType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskImpactType.html" title="class in org.mpxj.primavera.schema"><code>RiskImpactType</code></a></div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskMatrixScoreType.html" title="class in org.mpxj.primavera.schema">RiskMatrixScoreType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskMatrixScoreType--">createRiskMatrixScoreType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixScoreType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixScoreType</code></a></div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskMatrixThresholdType.html" title="class in org.mpxj.primavera.schema">RiskMatrixThresholdType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskMatrixThresholdType--">createRiskMatrixThresholdType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixThresholdType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixThresholdType</code></a></div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskMatrixType.html" title="class in org.mpxj.primavera.schema">RiskMatrixType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskMatrixType--">createRiskMatrixType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixType</code></a></div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskResponseActionImpactType.html" title="class in org.mpxj.primavera.schema">RiskResponseActionImpactType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskResponseActionImpactType--">createRiskResponseActionImpactType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponseActionImpactType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponseActionImpactType</code></a></div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskResponseActionType.html" title="class in org.mpxj.primavera.schema">RiskResponseActionType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskResponseActionType--">createRiskResponseActionType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponseActionType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponseActionType</code></a></div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskResponsePlanType.html" title="class in org.mpxj.primavera.schema">RiskResponsePlanType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskResponsePlanType--">createRiskResponsePlanType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponsePlanType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponsePlanType</code></a></div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskThresholdLevelType.html" title="class in org.mpxj.primavera.schema">RiskThresholdLevelType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskThresholdLevelType--">createRiskThresholdLevelType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskThresholdLevelType.html" title="class in org.mpxj.primavera.schema"><code>RiskThresholdLevelType</code></a></div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskThresholdType.html" title="class in org.mpxj.primavera.schema">RiskThresholdType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskThresholdType--">createRiskThresholdType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskThresholdType.html" title="class in org.mpxj.primavera.schema"><code>RiskThresholdType</code></a></div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RiskType.html" title="class in org.mpxj.primavera.schema">RiskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRiskType--">createRiskType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskType.html" title="class in org.mpxj.primavera.schema"><code>RiskType</code></a></div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">RoleCodeAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleCodeAssignmentType--">createRoleCodeAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeAssignmentType</code></a></div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleCodeType.html" title="class in org.mpxj.primavera.schema">RoleCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleCodeType--">createRoleCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeType</code></a></div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleCodeTypeType.html" title="class in org.mpxj.primavera.schema">RoleCodeTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleCodeTypeType--">createRoleCodeTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeTypeType</code></a></div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleLimitType.html" title="class in org.mpxj.primavera.schema">RoleLimitType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleLimitType--">createRoleLimitType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleLimitType.html" title="class in org.mpxj.primavera.schema"><code>RoleLimitType</code></a></div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleRateType.html" title="class in org.mpxj.primavera.schema">RoleRateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleRateType--">createRoleRateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleRateType.html" title="class in org.mpxj.primavera.schema"><code>RoleRateType</code></a></div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleTeamType.html" title="class in org.mpxj.primavera.schema">RoleTeamType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleTeamType--">createRoleTeamType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleTeamType.html" title="class in org.mpxj.primavera.schema"><code>RoleTeamType</code></a></div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/RoleType.html" title="class in org.mpxj.primavera.schema">RoleType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createRoleType--">createRoleType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleType.html" title="class in org.mpxj.primavera.schema"><code>RoleType</code></a></div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ScheduleCheckOptionType.html" title="class in org.mpxj.primavera.schema">ScheduleCheckOptionType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createScheduleCheckOptionType--">createScheduleCheckOptionType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ScheduleCheckOptionType.html" title="class in org.mpxj.primavera.schema"><code>ScheduleCheckOptionType</code></a></div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ScheduleOptionsType.html" title="class in org.mpxj.primavera.schema">ScheduleOptionsType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createScheduleOptionsType--">createScheduleOptionsType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ScheduleOptionsType.html" title="class in org.mpxj.primavera.schema"><code>ScheduleOptionsType</code></a></div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ShiftPeriodType.html" title="class in org.mpxj.primavera.schema">ShiftPeriodType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createShiftPeriodType--">createShiftPeriodType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ShiftPeriodType.html" title="class in org.mpxj.primavera.schema"><code>ShiftPeriodType</code></a></div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ShiftType.html" title="class in org.mpxj.primavera.schema">ShiftType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createShiftType--">createShiftType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ShiftType.html" title="class in org.mpxj.primavera.schema"><code>ShiftType</code></a></div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/StepUserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema">StepUserDefinedValueUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createStepUserDefinedValueUpdateType--">createStepUserDefinedValueUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/StepUserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><code>StepUserDefinedValueUpdateType</code></a></div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ThresholdParameterType.html" title="class in org.mpxj.primavera.schema">ThresholdParameterType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createThresholdParameterType--">createThresholdParameterType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ThresholdParameterType.html" title="class in org.mpxj.primavera.schema"><code>ThresholdParameterType</code></a></div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/TimesheetAuditType.html" title="class in org.mpxj.primavera.schema">TimesheetAuditType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createTimesheetAuditType--">createTimesheetAuditType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetAuditType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetAuditType</code></a></div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/TimesheetDelegateType.html" title="class in org.mpxj.primavera.schema">TimesheetDelegateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createTimesheetDelegateType--">createTimesheetDelegateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetDelegateType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetDelegateType</code></a></div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/TimesheetPeriodType.html" title="class in org.mpxj.primavera.schema">TimesheetPeriodType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createTimesheetPeriodType--">createTimesheetPeriodType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetPeriodType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetPeriodType</code></a></div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/TimesheetType.html" title="class in org.mpxj.primavera.schema">TimesheetType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createTimesheetType--">createTimesheetType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetType</code></a></div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUDFAssignmentType--">createUDFAssignmentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>UDFAssignmentType</code></a></div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UDFCodeType.html" title="class in org.mpxj.primavera.schema">UDFCodeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUDFCodeType--">createUDFCodeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFCodeType.html" title="class in org.mpxj.primavera.schema"><code>UDFCodeType</code></a></div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UDFTypeType.html" title="class in org.mpxj.primavera.schema">UDFTypeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUDFTypeType--">createUDFTypeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFTypeType.html" title="class in org.mpxj.primavera.schema"><code>UDFTypeType</code></a></div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UDFValueType.html" title="class in org.mpxj.primavera.schema">UDFValueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUDFValueType--">createUDFValueType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFValueType.html" title="class in org.mpxj.primavera.schema"><code>UDFValueType</code></a></div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UnitOfMeasureType.html" title="class in org.mpxj.primavera.schema">UnitOfMeasureType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUnitOfMeasureType--">createUnitOfMeasureType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UnitOfMeasureType.html" title="class in org.mpxj.primavera.schema"><code>UnitOfMeasureType</code></a></div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UpdateBaselineOptionType.html" title="class in org.mpxj.primavera.schema">UpdateBaselineOptionType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUpdateBaselineOptionType--">createUpdateBaselineOptionType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UpdateBaselineOptionType.html" title="class in org.mpxj.primavera.schema"><code>UpdateBaselineOptionType</code></a></div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserConsentType.html" title="class in org.mpxj.primavera.schema">UserConsentType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserConsentType--">createUserConsentType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserConsentType.html" title="class in org.mpxj.primavera.schema"><code>UserConsentType</code></a></div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema">UserDefinedValueUpdateType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserDefinedValueUpdateType--">createUserDefinedValueUpdateType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><code>UserDefinedValueUpdateType</code></a></div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserFieldTitleType.html" title="class in org.mpxj.primavera.schema">UserFieldTitleType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserFieldTitleType--">createUserFieldTitleType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserFieldTitleType.html" title="class in org.mpxj.primavera.schema"><code>UserFieldTitleType</code></a></div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserInterfaceViewType.html" title="class in org.mpxj.primavera.schema">UserInterfaceViewType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserInterfaceViewType--">createUserInterfaceViewType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserInterfaceViewType.html" title="class in org.mpxj.primavera.schema"><code>UserInterfaceViewType</code></a></div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserLicenseType.html" title="class in org.mpxj.primavera.schema">UserLicenseType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserLicenseType--">createUserLicenseType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserLicenseType.html" title="class in org.mpxj.primavera.schema"><code>UserLicenseType</code></a></div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserOBSType.html" title="class in org.mpxj.primavera.schema">UserOBSType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserOBSType--">createUserOBSType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserOBSType.html" title="class in org.mpxj.primavera.schema"><code>UserOBSType</code></a></div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserType.html" title="class in org.mpxj.primavera.schema">UserType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserType--">createUserType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserType.html" title="class in org.mpxj.primavera.schema"><code>UserType</code></a></div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/UserType.ResourceRequests.html" title="class in org.mpxj.primavera.schema">UserType.ResourceRequests</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createUserTypeResourceRequests--">createUserTypeResourceRequests</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserType.ResourceRequests.html" title="class in org.mpxj.primavera.schema"><code>UserType.ResourceRequests</code></a></div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/WBSCategoryType.html" title="class in org.mpxj.primavera.schema">WBSCategoryType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createWBSCategoryType--">createWBSCategoryType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSCategoryType.html" title="class in org.mpxj.primavera.schema"><code>WBSCategoryType</code></a></div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/WBSMilestoneType.html" title="class in org.mpxj.primavera.schema">WBSMilestoneType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createWBSMilestoneType--">createWBSMilestoneType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSMilestoneType.html" title="class in org.mpxj.primavera.schema"><code>WBSMilestoneType</code></a></div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/WbsReviewersType.html" title="class in org.mpxj.primavera.schema">WbsReviewersType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createWbsReviewersType--">createWbsReviewersType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WbsReviewersType.html" title="class in org.mpxj.primavera.schema"><code>WbsReviewersType</code></a></div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/WBSType.html" title="class in org.mpxj.primavera.schema">WBSType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createWBSType--">createWBSType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSType.html" title="class in org.mpxj.primavera.schema"><code>WBSType</code></a></div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/WorkTimeType.html" title="class in org.mpxj.primavera.schema">WorkTimeType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html#createWorkTimeType--">createWorkTimeType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WorkTimeType.html" title="class in org.mpxj.primavera.schema"><code>WorkTimeType</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ObjectFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ObjectFactory</h4>
<pre>public&nbsp;ObjectFactory()</pre>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.primavera.schema</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createProjectRoleSpreadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectRoleSpreadType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema">ProjectRoleSpreadType</a>&nbsp;createProjectRoleSpreadType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ProjectRoleSpreadType</code></a></div>
</li>
</ul>
<a name="createProjectResourceSpreadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourceSpreadType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType</a>&nbsp;createProjectResourceSpreadType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceSpreadType</code></a></div>
</li>
</ul>
<a name="createActivitySpreadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivitySpreadType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema">ActivitySpreadType</a>&nbsp;createActivitySpreadType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema"><code>ActivitySpreadType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentSpreadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentSpreadType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a>&nbsp;createResourceAssignmentSpreadType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType</code></a></div>
</li>
</ul>
<a name="createEPSProjectWBSSpreadType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSProjectWBSSpreadType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema">EPSProjectWBSSpreadType</a>&nbsp;createEPSProjectWBSSpreadType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema"><code>EPSProjectWBSSpreadType</code></a></div>
</li>
</ul>
<a name="createResourceRequestType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceRequestType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a>&nbsp;createResourceRequestType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType</code></a></div>
</li>
</ul>
<a name="createUserType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserType.html" title="class in org.mpxj.primavera.schema">UserType</a>&nbsp;createUserType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserType.html" title="class in org.mpxj.primavera.schema"><code>UserType</code></a></div>
</li>
</ul>
<a name="createCalendarType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalendarType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CalendarType.html" title="class in org.mpxj.primavera.schema">CalendarType</a>&nbsp;createCalendarType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.html" title="class in org.mpxj.primavera.schema"><code>CalendarType</code></a></div>
</li>
</ul>
<a name="createCalendarTypeHolidayOrExceptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalendarTypeHolidayOrExceptions</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.html" title="class in org.mpxj.primavera.schema">CalendarType.HolidayOrExceptions</a>&nbsp;createCalendarTypeHolidayOrExceptions()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.HolidayOrExceptions</code></a></div>
</li>
</ul>
<a name="createCalendarTypeStandardWorkWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalendarTypeStandardWorkWeek</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.html" title="class in org.mpxj.primavera.schema">CalendarType.StandardWorkWeek</a>&nbsp;createCalendarTypeStandardWorkWeek()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.StandardWorkWeek</code></a></div>
</li>
</ul>
<a name="createProjectListType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectListType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectListType.html" title="class in org.mpxj.primavera.schema">ProjectListType</a>&nbsp;createProjectListType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType</code></a></div>
</li>
</ul>
<a name="createProjectListTypeProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectListTypeProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.html" title="class in org.mpxj.primavera.schema">ProjectListType.Project</a>&nbsp;createProjectListTypeProject()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType.Project</code></a></div>
</li>
</ul>
<a name="createAPIBusinessObjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAPIBusinessObjects</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/APIBusinessObjects.html" title="class in org.mpxj.primavera.schema">APIBusinessObjects</a>&nbsp;createAPIBusinessObjects()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/APIBusinessObjects.html" title="class in org.mpxj.primavera.schema"><code>APIBusinessObjects</code></a></div>
</li>
</ul>
<a name="createDisplayCurrencyType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDisplayCurrencyType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/DisplayCurrencyType.html" title="class in org.mpxj.primavera.schema">DisplayCurrencyType</a>&nbsp;createDisplayCurrencyType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DisplayCurrencyType.html" title="class in org.mpxj.primavera.schema"><code>DisplayCurrencyType</code></a></div>
</li>
</ul>
<a name="createProjectResourceCategoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourceCategoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceCategoryType.html" title="class in org.mpxj.primavera.schema">ProjectResourceCategoryType</a>&nbsp;createProjectResourceCategoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceCategoryType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceCategoryType</code></a></div>
</li>
</ul>
<a name="createUnitOfMeasureType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUnitOfMeasureType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UnitOfMeasureType.html" title="class in org.mpxj.primavera.schema">UnitOfMeasureType</a>&nbsp;createUnitOfMeasureType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UnitOfMeasureType.html" title="class in org.mpxj.primavera.schema"><code>UnitOfMeasureType</code></a></div>
</li>
</ul>
<a name="createCostAccountType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCostAccountType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CostAccountType.html" title="class in org.mpxj.primavera.schema">CostAccountType</a>&nbsp;createCostAccountType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CostAccountType.html" title="class in org.mpxj.primavera.schema"><code>CostAccountType</code></a></div>
</li>
</ul>
<a name="createCurrencyType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCurrencyType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CurrencyType.html" title="class in org.mpxj.primavera.schema">CurrencyType</a>&nbsp;createCurrencyType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CurrencyType.html" title="class in org.mpxj.primavera.schema"><code>CurrencyType</code></a></div>
</li>
</ul>
<a name="createUDFTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUDFTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UDFTypeType.html" title="class in org.mpxj.primavera.schema">UDFTypeType</a>&nbsp;createUDFTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFTypeType.html" title="class in org.mpxj.primavera.schema"><code>UDFTypeType</code></a></div>
</li>
</ul>
<a name="createLocationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLocationType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/LocationType.html" title="class in org.mpxj.primavera.schema">LocationType</a>&nbsp;createLocationType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/LocationType.html" title="class in org.mpxj.primavera.schema"><code>LocationType</code></a></div>
</li>
</ul>
<a name="createUDFCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUDFCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UDFCodeType.html" title="class in org.mpxj.primavera.schema">UDFCodeType</a>&nbsp;createUDFCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFCodeType.html" title="class in org.mpxj.primavera.schema"><code>UDFCodeType</code></a></div>
</li>
</ul>
<a name="createExpenseCategoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createExpenseCategoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ExpenseCategoryType.html" title="class in org.mpxj.primavera.schema">ExpenseCategoryType</a>&nbsp;createExpenseCategoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ExpenseCategoryType.html" title="class in org.mpxj.primavera.schema"><code>ExpenseCategoryType</code></a></div>
</li>
</ul>
<a name="createNotebookTopicType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createNotebookTopicType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema">NotebookTopicType</a>&nbsp;createNotebookTopicType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema"><code>NotebookTopicType</code></a></div>
</li>
</ul>
<a name="createWBSCategoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWBSCategoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/WBSCategoryType.html" title="class in org.mpxj.primavera.schema">WBSCategoryType</a>&nbsp;createWBSCategoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSCategoryType.html" title="class in org.mpxj.primavera.schema"><code>WBSCategoryType</code></a></div>
</li>
</ul>
<a name="createFundingSourceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFundingSourceType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/FundingSourceType.html" title="class in org.mpxj.primavera.schema">FundingSourceType</a>&nbsp;createFundingSourceType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FundingSourceType.html" title="class in org.mpxj.primavera.schema"><code>FundingSourceType</code></a></div>
</li>
</ul>
<a name="createThresholdParameterType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createThresholdParameterType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ThresholdParameterType.html" title="class in org.mpxj.primavera.schema">ThresholdParameterType</a>&nbsp;createThresholdParameterType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ThresholdParameterType.html" title="class in org.mpxj.primavera.schema"><code>ThresholdParameterType</code></a></div>
</li>
</ul>
<a name="createOBSType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createOBSType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema">OBSType</a>&nbsp;createOBSType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema"><code>OBSType</code></a></div>
</li>
</ul>
<a name="createShiftPeriodType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createShiftPeriodType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ShiftPeriodType.html" title="class in org.mpxj.primavera.schema">ShiftPeriodType</a>&nbsp;createShiftPeriodType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ShiftPeriodType.html" title="class in org.mpxj.primavera.schema"><code>ShiftPeriodType</code></a></div>
</li>
</ul>
<a name="createShiftType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createShiftType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ShiftType.html" title="class in org.mpxj.primavera.schema">ShiftType</a>&nbsp;createShiftType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ShiftType.html" title="class in org.mpxj.primavera.schema"><code>ShiftType</code></a></div>
</li>
</ul>
<a name="createProjectCodeTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCodeTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectCodeTypeType.html" title="class in org.mpxj.primavera.schema">ProjectCodeTypeType</a>&nbsp;createProjectCodeTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeTypeType</code></a></div>
</li>
</ul>
<a name="createProjectCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectCodeType.html" title="class in org.mpxj.primavera.schema">ProjectCodeType</a>&nbsp;createProjectCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeType</code></a></div>
</li>
</ul>
<a name="createResourceCodeTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceCodeTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceCodeTypeType.html" title="class in org.mpxj.primavera.schema">ResourceCodeTypeType</a>&nbsp;createResourceCodeTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeTypeType</code></a></div>
</li>
</ul>
<a name="createResourceCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceCodeType.html" title="class in org.mpxj.primavera.schema">ResourceCodeType</a>&nbsp;createResourceCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeType</code></a></div>
</li>
</ul>
<a name="createRoleCodeTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleCodeTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleCodeTypeType.html" title="class in org.mpxj.primavera.schema">RoleCodeTypeType</a>&nbsp;createRoleCodeTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeTypeType</code></a></div>
</li>
</ul>
<a name="createRoleCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleCodeType.html" title="class in org.mpxj.primavera.schema">RoleCodeType</a>&nbsp;createRoleCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeType</code></a></div>
</li>
</ul>
<a name="createResourceCurveType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceCurveType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceCurveType.html" title="class in org.mpxj.primavera.schema">ResourceCurveType</a>&nbsp;createResourceCurveType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCurveType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCurveType</code></a></div>
</li>
</ul>
<a name="createRoleType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleType.html" title="class in org.mpxj.primavera.schema">RoleType</a>&nbsp;createRoleType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleType.html" title="class in org.mpxj.primavera.schema"><code>RoleType</code></a></div>
</li>
</ul>
<a name="createRoleRateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleRateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleRateType.html" title="class in org.mpxj.primavera.schema">RoleRateType</a>&nbsp;createRoleRateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleRateType.html" title="class in org.mpxj.primavera.schema"><code>RoleRateType</code></a></div>
</li>
</ul>
<a name="createRoleLimitType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleLimitType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleLimitType.html" title="class in org.mpxj.primavera.schema">RoleLimitType</a>&nbsp;createRoleLimitType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleLimitType.html" title="class in org.mpxj.primavera.schema"><code>RoleLimitType</code></a></div>
</li>
</ul>
<a name="createResourceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceType.html" title="class in org.mpxj.primavera.schema">ResourceType</a>&nbsp;createResourceType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceType.html" title="class in org.mpxj.primavera.schema"><code>ResourceType</code></a></div>
</li>
</ul>
<a name="createResourceRateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceRateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRateType.html" title="class in org.mpxj.primavera.schema">ResourceRateType</a>&nbsp;createResourceRateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRateType</code></a></div>
</li>
</ul>
<a name="createActivityCodeTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCodeTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityCodeTypeType.html" title="class in org.mpxj.primavera.schema">ActivityCodeTypeType</a>&nbsp;createActivityCodeTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeTypeType</code></a></div>
</li>
</ul>
<a name="createActivityCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityCodeType.html" title="class in org.mpxj.primavera.schema">ActivityCodeType</a>&nbsp;createActivityCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentCodeTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentCodeTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeTypeType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeTypeType</a>&nbsp;createResourceAssignmentCodeTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeTypeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeTypeType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeType</a>&nbsp;createResourceAssignmentCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeType</code></a></div>
</li>
</ul>
<a name="createFinancialPeriodType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFinancialPeriodType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/FinancialPeriodType.html" title="class in org.mpxj.primavera.schema">FinancialPeriodType</a>&nbsp;createFinancialPeriodType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FinancialPeriodType.html" title="class in org.mpxj.primavera.schema"><code>FinancialPeriodType</code></a></div>
</li>
</ul>
<a name="createResourceRoleType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceRoleType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRoleType.html" title="class in org.mpxj.primavera.schema">ResourceRoleType</a>&nbsp;createResourceRoleType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRoleType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRoleType</code></a></div>
</li>
</ul>
<a name="createEPSType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSType.html" title="class in org.mpxj.primavera.schema">EPSType</a>&nbsp;createEPSType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSType.html" title="class in org.mpxj.primavera.schema"><code>EPSType</code></a></div>
</li>
</ul>
<a name="createDocumentCategoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCategoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/DocumentCategoryType.html" title="class in org.mpxj.primavera.schema">DocumentCategoryType</a>&nbsp;createDocumentCategoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentCategoryType.html" title="class in org.mpxj.primavera.schema"><code>DocumentCategoryType</code></a></div>
</li>
</ul>
<a name="createDocumentStatusCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentStatusCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/DocumentStatusCodeType.html" title="class in org.mpxj.primavera.schema">DocumentStatusCodeType</a>&nbsp;createDocumentStatusCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentStatusCodeType.html" title="class in org.mpxj.primavera.schema"><code>DocumentStatusCodeType</code></a></div>
</li>
</ul>
<a name="createRiskCategoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskCategoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskCategoryType.html" title="class in org.mpxj.primavera.schema">RiskCategoryType</a>&nbsp;createRiskCategoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskCategoryType.html" title="class in org.mpxj.primavera.schema"><code>RiskCategoryType</code></a></div>
</li>
</ul>
<a name="createRiskThresholdType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskThresholdType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskThresholdType.html" title="class in org.mpxj.primavera.schema">RiskThresholdType</a>&nbsp;createRiskThresholdType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskThresholdType.html" title="class in org.mpxj.primavera.schema"><code>RiskThresholdType</code></a></div>
</li>
</ul>
<a name="createRiskThresholdLevelType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskThresholdLevelType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskThresholdLevelType.html" title="class in org.mpxj.primavera.schema">RiskThresholdLevelType</a>&nbsp;createRiskThresholdLevelType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskThresholdLevelType.html" title="class in org.mpxj.primavera.schema"><code>RiskThresholdLevelType</code></a></div>
</li>
</ul>
<a name="createRiskMatrixType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskMatrixType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskMatrixType.html" title="class in org.mpxj.primavera.schema">RiskMatrixType</a>&nbsp;createRiskMatrixType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixType</code></a></div>
</li>
</ul>
<a name="createRiskMatrixScoreType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskMatrixScoreType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskMatrixScoreType.html" title="class in org.mpxj.primavera.schema">RiskMatrixScoreType</a>&nbsp;createRiskMatrixScoreType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixScoreType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixScoreType</code></a></div>
</li>
</ul>
<a name="createRiskMatrixThresholdType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskMatrixThresholdType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskMatrixThresholdType.html" title="class in org.mpxj.primavera.schema">RiskMatrixThresholdType</a>&nbsp;createRiskMatrixThresholdType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskMatrixThresholdType.html" title="class in org.mpxj.primavera.schema"><code>RiskMatrixThresholdType</code></a></div>
</li>
</ul>
<a name="createActivityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityType.html" title="class in org.mpxj.primavera.schema">ActivityType</a>&nbsp;createActivityType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityType.html" title="class in org.mpxj.primavera.schema"><code>ActivityType</code></a></div>
</li>
</ul>
<a name="createActivityCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ActivityCodeAssignmentType</a>&nbsp;createActivityCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createActivityCodeUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCodeUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityCodeUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityCodeUpdateType</a>&nbsp;createActivityCodeUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCodeUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCodeUpdateType</code></a></div>
</li>
</ul>
<a name="createActivityCommentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityCommentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityCommentType.html" title="class in org.mpxj.primavera.schema">ActivityCommentType</a>&nbsp;createActivityCommentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityCommentType.html" title="class in org.mpxj.primavera.schema"><code>ActivityCommentType</code></a></div>
</li>
</ul>
<a name="createActivityExpenseType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityExpenseType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityExpenseType.html" title="class in org.mpxj.primavera.schema">ActivityExpenseType</a>&nbsp;createActivityExpenseType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityExpenseType.html" title="class in org.mpxj.primavera.schema"><code>ActivityExpenseType</code></a></div>
</li>
</ul>
<a name="createActivityFilterType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityFilterType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityFilterType.html" title="class in org.mpxj.primavera.schema">ActivityFilterType</a>&nbsp;createActivityFilterType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityFilterType.html" title="class in org.mpxj.primavera.schema"><code>ActivityFilterType</code></a></div>
</li>
</ul>
<a name="createActivityNoteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityNoteType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityNoteType.html" title="class in org.mpxj.primavera.schema">ActivityNoteType</a>&nbsp;createActivityNoteType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityNoteType.html" title="class in org.mpxj.primavera.schema"><code>ActivityNoteType</code></a></div>
</li>
</ul>
<a name="createActivityNoteUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityNoteUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityNoteUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityNoteUpdateType</a>&nbsp;createActivityNoteUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityNoteUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityNoteUpdateType</code></a></div>
</li>
</ul>
<a name="createActivityOwnerType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityOwnerType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityOwnerType.html" title="class in org.mpxj.primavera.schema">ActivityOwnerType</a>&nbsp;createActivityOwnerType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityOwnerType.html" title="class in org.mpxj.primavera.schema"><code>ActivityOwnerType</code></a></div>
</li>
</ul>
<a name="createActivityPeriodActualType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityPeriodActualType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityPeriodActualType.html" title="class in org.mpxj.primavera.schema">ActivityPeriodActualType</a>&nbsp;createActivityPeriodActualType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityPeriodActualType.html" title="class in org.mpxj.primavera.schema"><code>ActivityPeriodActualType</code></a></div>
</li>
</ul>
<a name="createActivityRiskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityRiskType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityRiskType.html" title="class in org.mpxj.primavera.schema">ActivityRiskType</a>&nbsp;createActivityRiskType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityRiskType.html" title="class in org.mpxj.primavera.schema"><code>ActivityRiskType</code></a></div>
</li>
</ul>
<a name="createActivityStepType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepType.html" title="class in org.mpxj.primavera.schema">ActivityStepType</a>&nbsp;createActivityStepType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepType</code></a></div>
</li>
</ul>
<a name="createActivityStepCreateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepCreateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema">ActivityStepCreateType</a>&nbsp;createActivityStepCreateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepCreateType</code></a></div>
</li>
</ul>
<a name="createActivityStepDeleteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepDeleteType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepDeleteType.html" title="class in org.mpxj.primavera.schema">ActivityStepDeleteType</a>&nbsp;createActivityStepDeleteType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepDeleteType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepDeleteType</code></a></div>
</li>
</ul>
<a name="createActivityStepTemplateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepTemplateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateType.html" title="class in org.mpxj.primavera.schema">ActivityStepTemplateType</a>&nbsp;createActivityStepTemplateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepTemplateType</code></a></div>
</li>
</ul>
<a name="createActivityStepTemplateItemType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepTemplateItemType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateItemType.html" title="class in org.mpxj.primavera.schema">ActivityStepTemplateItemType</a>&nbsp;createActivityStepTemplateItemType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateItemType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepTemplateItemType</code></a></div>
</li>
</ul>
<a name="createActivityStepUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityStepUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityStepUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityStepUpdateType</a>&nbsp;createActivityStepUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityStepUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityStepUpdateType</code></a></div>
</li>
</ul>
<a name="createActivityUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivityUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivityUpdateType.html" title="class in org.mpxj.primavera.schema">ActivityUpdateType</a>&nbsp;createActivityUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivityUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ActivityUpdateType</code></a></div>
</li>
</ul>
<a name="createAlertType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAlertType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/AlertType.html" title="class in org.mpxj.primavera.schema">AlertType</a>&nbsp;createAlertType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/AlertType.html" title="class in org.mpxj.primavera.schema"><code>AlertType</code></a></div>
</li>
</ul>
<a name="createAutovueAttrType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAutovueAttrType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/AutovueAttrType.html" title="class in org.mpxj.primavera.schema">AutovueAttrType</a>&nbsp;createAutovueAttrType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/AutovueAttrType.html" title="class in org.mpxj.primavera.schema"><code>AutovueAttrType</code></a></div>
</li>
</ul>
<a name="createBaselineTypeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBaselineTypeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/BaselineTypeType.html" title="class in org.mpxj.primavera.schema">BaselineTypeType</a>&nbsp;createBaselineTypeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/BaselineTypeType.html" title="class in org.mpxj.primavera.schema"><code>BaselineTypeType</code></a></div>
</li>
</ul>
<a name="createCBSType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCBSType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CBSType.html" title="class in org.mpxj.primavera.schema">CBSType</a>&nbsp;createCBSType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CBSType.html" title="class in org.mpxj.primavera.schema"><code>CBSType</code></a></div>
</li>
</ul>
<a name="createCBSDurationSummaryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCBSDurationSummaryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CBSDurationSummaryType.html" title="class in org.mpxj.primavera.schema">CBSDurationSummaryType</a>&nbsp;createCBSDurationSummaryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CBSDurationSummaryType.html" title="class in org.mpxj.primavera.schema"><code>CBSDurationSummaryType</code></a></div>
</li>
</ul>
<a name="createChangeSetType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createChangeSetType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ChangeSetType.html" title="class in org.mpxj.primavera.schema">ChangeSetType</a>&nbsp;createChangeSetType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ChangeSetType.html" title="class in org.mpxj.primavera.schema"><code>ChangeSetType</code></a></div>
</li>
</ul>
<a name="createDocumentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/DocumentType.html" title="class in org.mpxj.primavera.schema">DocumentType</a>&nbsp;createDocumentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/DocumentType.html" title="class in org.mpxj.primavera.schema"><code>DocumentType</code></a></div>
</li>
</ul>
<a name="createEPSBudgetChangeLogType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSBudgetChangeLogType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema">EPSBudgetChangeLogType</a>&nbsp;createEPSBudgetChangeLogType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><code>EPSBudgetChangeLogType</code></a></div>
</li>
</ul>
<a name="createEPSFundingType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSFundingType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSFundingType.html" title="class in org.mpxj.primavera.schema">EPSFundingType</a>&nbsp;createEPSFundingType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSFundingType.html" title="class in org.mpxj.primavera.schema"><code>EPSFundingType</code></a></div>
</li>
</ul>
<a name="createEPSNoteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSNoteType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSNoteType.html" title="class in org.mpxj.primavera.schema">EPSNoteType</a>&nbsp;createEPSNoteType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSNoteType.html" title="class in org.mpxj.primavera.schema"><code>EPSNoteType</code></a></div>
</li>
</ul>
<a name="createEPSSpendingPlanType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSSpendingPlanType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema">EPSSpendingPlanType</a>&nbsp;createEPSSpendingPlanType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><code>EPSSpendingPlanType</code></a></div>
</li>
</ul>
<a name="createFinancialPeriodTemplateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFinancialPeriodTemplateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/FinancialPeriodTemplateType.html" title="class in org.mpxj.primavera.schema">FinancialPeriodTemplateType</a>&nbsp;createFinancialPeriodTemplateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/FinancialPeriodTemplateType.html" title="class in org.mpxj.primavera.schema"><code>FinancialPeriodTemplateType</code></a></div>
</li>
</ul>
<a name="createGatewayDeploymentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGatewayDeploymentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/GatewayDeploymentType.html" title="class in org.mpxj.primavera.schema">GatewayDeploymentType</a>&nbsp;createGatewayDeploymentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GatewayDeploymentType.html" title="class in org.mpxj.primavera.schema"><code>GatewayDeploymentType</code></a></div>
</li>
</ul>
<a name="createGlobalPreferencesType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGlobalPreferencesType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema">GlobalPreferencesType</a>&nbsp;createGlobalPreferencesType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema"><code>GlobalPreferencesType</code></a></div>
</li>
</ul>
<a name="createGlobalProfileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGlobalProfileType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema">GlobalProfileType</a>&nbsp;createGlobalProfileType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema"><code>GlobalProfileType</code></a></div>
</li>
</ul>
<a name="createGlobalReplaceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGlobalReplaceType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/GlobalReplaceType.html" title="class in org.mpxj.primavera.schema">GlobalReplaceType</a>&nbsp;createGlobalReplaceType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalReplaceType.html" title="class in org.mpxj.primavera.schema"><code>GlobalReplaceType</code></a></div>
</li>
</ul>
<a name="createImportOptionsTemplateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createImportOptionsTemplateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ImportOptionsTemplateType.html" title="class in org.mpxj.primavera.schema">ImportOptionsTemplateType</a>&nbsp;createImportOptionsTemplateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ImportOptionsTemplateType.html" title="class in org.mpxj.primavera.schema"><code>ImportOptionsTemplateType</code></a></div>
</li>
</ul>
<a name="createIssueHistoryType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIssueHistoryType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/IssueHistoryType.html" title="class in org.mpxj.primavera.schema">IssueHistoryType</a>&nbsp;createIssueHistoryType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/IssueHistoryType.html" title="class in org.mpxj.primavera.schema"><code>IssueHistoryType</code></a></div>
</li>
</ul>
<a name="createJobServiceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createJobServiceType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/JobServiceType.html" title="class in org.mpxj.primavera.schema">JobServiceType</a>&nbsp;createJobServiceType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/JobServiceType.html" title="class in org.mpxj.primavera.schema"><code>JobServiceType</code></a></div>
</li>
</ul>
<a name="createLeanTaskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLeanTaskType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/LeanTaskType.html" title="class in org.mpxj.primavera.schema">LeanTaskType</a>&nbsp;createLeanTaskType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/LeanTaskType.html" title="class in org.mpxj.primavera.schema"><code>LeanTaskType</code></a></div>
</li>
</ul>
<a name="createMSPTemplateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMSPTemplateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/MSPTemplateType.html" title="class in org.mpxj.primavera.schema">MSPTemplateType</a>&nbsp;createMSPTemplateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/MSPTemplateType.html" title="class in org.mpxj.primavera.schema"><code>MSPTemplateType</code></a></div>
</li>
</ul>
<a name="createOverheadCodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createOverheadCodeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/OverheadCodeType.html" title="class in org.mpxj.primavera.schema">OverheadCodeType</a>&nbsp;createOverheadCodeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/OverheadCodeType.html" title="class in org.mpxj.primavera.schema"><code>OverheadCodeType</code></a></div>
</li>
</ul>
<a name="createPAuditXType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPAuditXType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/PAuditXType.html" title="class in org.mpxj.primavera.schema">PAuditXType</a>&nbsp;createPAuditXType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/PAuditXType.html" title="class in org.mpxj.primavera.schema"><code>PAuditXType</code></a></div>
</li>
</ul>
<a name="createProfileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProfileType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProfileType.html" title="class in org.mpxj.primavera.schema">ProfileType</a>&nbsp;createProfileType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProfileType.html" title="class in org.mpxj.primavera.schema"><code>ProfileType</code></a></div>
</li>
</ul>
<a name="createProjectType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectType.html" title="class in org.mpxj.primavera.schema">ProjectType</a>&nbsp;createProjectType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectType.html" title="class in org.mpxj.primavera.schema"><code>ProjectType</code></a></div>
</li>
</ul>
<a name="createBaselineProjectType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBaselineProjectType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/BaselineProjectType.html" title="class in org.mpxj.primavera.schema">BaselineProjectType</a>&nbsp;createBaselineProjectType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/BaselineProjectType.html" title="class in org.mpxj.primavera.schema"><code>BaselineProjectType</code></a></div>
</li>
</ul>
<a name="createProjectBudgetChangeLogType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectBudgetChangeLogType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema">ProjectBudgetChangeLogType</a>&nbsp;createProjectBudgetChangeLogType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><code>ProjectBudgetChangeLogType</code></a></div>
</li>
</ul>
<a name="createProjectCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ProjectCodeAssignmentType</a>&nbsp;createProjectCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectCodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createProjectDeploymentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectDeploymentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectDeploymentType.html" title="class in org.mpxj.primavera.schema">ProjectDeploymentType</a>&nbsp;createProjectDeploymentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectDeploymentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectDeploymentType</code></a></div>
</li>
</ul>
<a name="createProjectDocumentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectDocumentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectDocumentType.html" title="class in org.mpxj.primavera.schema">ProjectDocumentType</a>&nbsp;createProjectDocumentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectDocumentType.html" title="class in org.mpxj.primavera.schema"><code>ProjectDocumentType</code></a></div>
</li>
</ul>
<a name="createProjectFundingType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectFundingType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectFundingType.html" title="class in org.mpxj.primavera.schema">ProjectFundingType</a>&nbsp;createProjectFundingType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectFundingType.html" title="class in org.mpxj.primavera.schema"><code>ProjectFundingType</code></a></div>
</li>
</ul>
<a name="createProjectIssueType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectIssueType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectIssueType.html" title="class in org.mpxj.primavera.schema">ProjectIssueType</a>&nbsp;createProjectIssueType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectIssueType.html" title="class in org.mpxj.primavera.schema"><code>ProjectIssueType</code></a></div>
</li>
</ul>
<a name="createProjectNoteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectNoteType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectNoteType.html" title="class in org.mpxj.primavera.schema">ProjectNoteType</a>&nbsp;createProjectNoteType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectNoteType.html" title="class in org.mpxj.primavera.schema"><code>ProjectNoteType</code></a></div>
</li>
</ul>
<a name="createProjectPortfolioType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectPortfolioType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema">ProjectPortfolioType</a>&nbsp;createProjectPortfolioType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema"><code>ProjectPortfolioType</code></a></div>
</li>
</ul>
<a name="createProjectProfileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectProfileType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema">ProjectProfileType</a>&nbsp;createProjectProfileType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema"><code>ProjectProfileType</code></a></div>
</li>
</ul>
<a name="createProjectResourceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourceType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceType.html" title="class in org.mpxj.primavera.schema">ProjectResourceType</a>&nbsp;createProjectResourceType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceType</code></a></div>
</li>
</ul>
<a name="createProjectResourceQuantityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourceQuantityType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema">ProjectResourceQuantityType</a>&nbsp;createProjectResourceQuantityType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceQuantityType</code></a></div>
</li>
</ul>
<a name="createProjectSpendingPlanType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectSpendingPlanType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema">ProjectSpendingPlanType</a>&nbsp;createProjectSpendingPlanType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><code>ProjectSpendingPlanType</code></a></div>
</li>
</ul>
<a name="createProjectThresholdType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectThresholdType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectThresholdType.html" title="class in org.mpxj.primavera.schema">ProjectThresholdType</a>&nbsp;createProjectThresholdType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectThresholdType.html" title="class in org.mpxj.primavera.schema"><code>ProjectThresholdType</code></a></div>
</li>
</ul>
<a name="createRelationshipType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRelationshipType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RelationshipType.html" title="class in org.mpxj.primavera.schema">RelationshipType</a>&nbsp;createRelationshipType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RelationshipType.html" title="class in org.mpxj.primavera.schema"><code>RelationshipType</code></a></div>
</li>
</ul>
<a name="createResourceAccessType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAccessType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAccessType.html" title="class in org.mpxj.primavera.schema">ResourceAccessType</a>&nbsp;createResourceAccessType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAccessType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAccessType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentType</a>&nbsp;createResourceAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCodeAssignmentType</a>&nbsp;createResourceAssignmentCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentCreateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentCreateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCreateType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentCreateType</a>&nbsp;createResourceAssignmentCreateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCreateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentCreateType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentPeriodActualType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentPeriodActualType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentPeriodActualType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentPeriodActualType</a>&nbsp;createResourceAssignmentPeriodActualType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentPeriodActualType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentPeriodActualType</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentUpdateType</a>&nbsp;createResourceAssignmentUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentUpdateType</code></a></div>
</li>
</ul>
<a name="createResourceCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">ResourceCodeAssignmentType</a>&nbsp;createResourceCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createResourceHourType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceHourType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceHourType.html" title="class in org.mpxj.primavera.schema">ResourceHourType</a>&nbsp;createResourceHourType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceHourType.html" title="class in org.mpxj.primavera.schema"><code>ResourceHourType</code></a></div>
</li>
</ul>
<a name="createResourceLocationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceLocationType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceLocationType.html" title="class in org.mpxj.primavera.schema">ResourceLocationType</a>&nbsp;createResourceLocationType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceLocationType.html" title="class in org.mpxj.primavera.schema"><code>ResourceLocationType</code></a></div>
</li>
</ul>
<a name="createResourceTeamType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceTeamType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceTeamType.html" title="class in org.mpxj.primavera.schema">ResourceTeamType</a>&nbsp;createResourceTeamType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceTeamType.html" title="class in org.mpxj.primavera.schema"><code>ResourceTeamType</code></a></div>
</li>
</ul>
<a name="createRiskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskType.html" title="class in org.mpxj.primavera.schema">RiskType</a>&nbsp;createRiskType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskType.html" title="class in org.mpxj.primavera.schema"><code>RiskType</code></a></div>
</li>
</ul>
<a name="createRiskImpactType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskImpactType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskImpactType.html" title="class in org.mpxj.primavera.schema">RiskImpactType</a>&nbsp;createRiskImpactType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskImpactType.html" title="class in org.mpxj.primavera.schema"><code>RiskImpactType</code></a></div>
</li>
</ul>
<a name="createRiskResponseActionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskResponseActionType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskResponseActionType.html" title="class in org.mpxj.primavera.schema">RiskResponseActionType</a>&nbsp;createRiskResponseActionType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponseActionType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponseActionType</code></a></div>
</li>
</ul>
<a name="createRiskResponseActionImpactType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskResponseActionImpactType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskResponseActionImpactType.html" title="class in org.mpxj.primavera.schema">RiskResponseActionImpactType</a>&nbsp;createRiskResponseActionImpactType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponseActionImpactType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponseActionImpactType</code></a></div>
</li>
</ul>
<a name="createRiskResponsePlanType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRiskResponsePlanType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RiskResponsePlanType.html" title="class in org.mpxj.primavera.schema">RiskResponsePlanType</a>&nbsp;createRiskResponsePlanType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RiskResponsePlanType.html" title="class in org.mpxj.primavera.schema"><code>RiskResponsePlanType</code></a></div>
</li>
</ul>
<a name="createRoleCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleCodeAssignmentType.html" title="class in org.mpxj.primavera.schema">RoleCodeAssignmentType</a>&nbsp;createRoleCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>RoleCodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createRoleTeamType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoleTeamType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/RoleTeamType.html" title="class in org.mpxj.primavera.schema">RoleTeamType</a>&nbsp;createRoleTeamType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/RoleTeamType.html" title="class in org.mpxj.primavera.schema"><code>RoleTeamType</code></a></div>
</li>
</ul>
<a name="createScheduleCheckOptionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createScheduleCheckOptionType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ScheduleCheckOptionType.html" title="class in org.mpxj.primavera.schema">ScheduleCheckOptionType</a>&nbsp;createScheduleCheckOptionType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ScheduleCheckOptionType.html" title="class in org.mpxj.primavera.schema"><code>ScheduleCheckOptionType</code></a></div>
</li>
</ul>
<a name="createScheduleOptionsType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createScheduleOptionsType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ScheduleOptionsType.html" title="class in org.mpxj.primavera.schema">ScheduleOptionsType</a>&nbsp;createScheduleOptionsType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ScheduleOptionsType.html" title="class in org.mpxj.primavera.schema"><code>ScheduleOptionsType</code></a></div>
</li>
</ul>
<a name="createStepUserDefinedValueUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createStepUserDefinedValueUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/StepUserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema">StepUserDefinedValueUpdateType</a>&nbsp;createStepUserDefinedValueUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/StepUserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><code>StepUserDefinedValueUpdateType</code></a></div>
</li>
</ul>
<a name="createTimesheetType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimesheetType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/TimesheetType.html" title="class in org.mpxj.primavera.schema">TimesheetType</a>&nbsp;createTimesheetType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetType</code></a></div>
</li>
</ul>
<a name="createTimesheetAuditType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimesheetAuditType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/TimesheetAuditType.html" title="class in org.mpxj.primavera.schema">TimesheetAuditType</a>&nbsp;createTimesheetAuditType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetAuditType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetAuditType</code></a></div>
</li>
</ul>
<a name="createTimesheetDelegateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimesheetDelegateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/TimesheetDelegateType.html" title="class in org.mpxj.primavera.schema">TimesheetDelegateType</a>&nbsp;createTimesheetDelegateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetDelegateType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetDelegateType</code></a></div>
</li>
</ul>
<a name="createTimesheetPeriodType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimesheetPeriodType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/TimesheetPeriodType.html" title="class in org.mpxj.primavera.schema">TimesheetPeriodType</a>&nbsp;createTimesheetPeriodType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/TimesheetPeriodType.html" title="class in org.mpxj.primavera.schema"><code>TimesheetPeriodType</code></a></div>
</li>
</ul>
<a name="createUDFValueType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUDFValueType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UDFValueType.html" title="class in org.mpxj.primavera.schema">UDFValueType</a>&nbsp;createUDFValueType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFValueType.html" title="class in org.mpxj.primavera.schema"><code>UDFValueType</code></a></div>
</li>
</ul>
<a name="createUpdateBaselineOptionType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUpdateBaselineOptionType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UpdateBaselineOptionType.html" title="class in org.mpxj.primavera.schema">UpdateBaselineOptionType</a>&nbsp;createUpdateBaselineOptionType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UpdateBaselineOptionType.html" title="class in org.mpxj.primavera.schema"><code>UpdateBaselineOptionType</code></a></div>
</li>
</ul>
<a name="createUserConsentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserConsentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserConsentType.html" title="class in org.mpxj.primavera.schema">UserConsentType</a>&nbsp;createUserConsentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserConsentType.html" title="class in org.mpxj.primavera.schema"><code>UserConsentType</code></a></div>
</li>
</ul>
<a name="createUserDefinedValueUpdateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserDefinedValueUpdateType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema">UserDefinedValueUpdateType</a>&nbsp;createUserDefinedValueUpdateType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><code>UserDefinedValueUpdateType</code></a></div>
</li>
</ul>
<a name="createUserFieldTitleType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserFieldTitleType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserFieldTitleType.html" title="class in org.mpxj.primavera.schema">UserFieldTitleType</a>&nbsp;createUserFieldTitleType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserFieldTitleType.html" title="class in org.mpxj.primavera.schema"><code>UserFieldTitleType</code></a></div>
</li>
</ul>
<a name="createUserInterfaceViewType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserInterfaceViewType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserInterfaceViewType.html" title="class in org.mpxj.primavera.schema">UserInterfaceViewType</a>&nbsp;createUserInterfaceViewType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserInterfaceViewType.html" title="class in org.mpxj.primavera.schema"><code>UserInterfaceViewType</code></a></div>
</li>
</ul>
<a name="createUserLicenseType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserLicenseType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserLicenseType.html" title="class in org.mpxj.primavera.schema">UserLicenseType</a>&nbsp;createUserLicenseType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserLicenseType.html" title="class in org.mpxj.primavera.schema"><code>UserLicenseType</code></a></div>
</li>
</ul>
<a name="createUserOBSType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserOBSType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserOBSType.html" title="class in org.mpxj.primavera.schema">UserOBSType</a>&nbsp;createUserOBSType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserOBSType.html" title="class in org.mpxj.primavera.schema"><code>UserOBSType</code></a></div>
</li>
</ul>
<a name="createWBSType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWBSType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/WBSType.html" title="class in org.mpxj.primavera.schema">WBSType</a>&nbsp;createWBSType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSType.html" title="class in org.mpxj.primavera.schema"><code>WBSType</code></a></div>
</li>
</ul>
<a name="createWBSMilestoneType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWBSMilestoneType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/WBSMilestoneType.html" title="class in org.mpxj.primavera.schema">WBSMilestoneType</a>&nbsp;createWBSMilestoneType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WBSMilestoneType.html" title="class in org.mpxj.primavera.schema"><code>WBSMilestoneType</code></a></div>
</li>
</ul>
<a name="createWbsReviewersType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWbsReviewersType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/WbsReviewersType.html" title="class in org.mpxj.primavera.schema">WbsReviewersType</a>&nbsp;createWbsReviewersType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WbsReviewersType.html" title="class in org.mpxj.primavera.schema"><code>WbsReviewersType</code></a></div>
</li>
</ul>
<a name="createCodeAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCodeAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a>&nbsp;createCodeAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>CodeAssignmentType</code></a></div>
</li>
</ul>
<a name="createUDFAssignmentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUDFAssignmentType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a>&nbsp;createUDFAssignmentType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>UDFAssignmentType</code></a></div>
</li>
</ul>
<a name="createGlobalPrivilegesType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createGlobalPrivilegesType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html" title="class in org.mpxj.primavera.schema">GlobalPrivilegesType</a>&nbsp;createGlobalPrivilegesType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html" title="class in org.mpxj.primavera.schema"><code>GlobalPrivilegesType</code></a></div>
</li>
</ul>
<a name="createProjectPrivilegesType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectPrivilegesType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html" title="class in org.mpxj.primavera.schema">ProjectPrivilegesType</a>&nbsp;createProjectPrivilegesType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html" title="class in org.mpxj.primavera.schema"><code>ProjectPrivilegesType</code></a></div>
</li>
</ul>
<a name="createPortfolioTeamMemberType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPortfolioTeamMemberType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/PortfolioTeamMemberType.html" title="class in org.mpxj.primavera.schema">PortfolioTeamMemberType</a>&nbsp;createPortfolioTeamMemberType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/PortfolioTeamMemberType.html" title="class in org.mpxj.primavera.schema"><code>PortfolioTeamMemberType</code></a></div>
</li>
</ul>
<a name="createResourceCurveValuesType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceCurveValuesType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceCurveValuesType.html" title="class in org.mpxj.primavera.schema">ResourceCurveValuesType</a>&nbsp;createResourceCurveValuesType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceCurveValuesType.html" title="class in org.mpxj.primavera.schema"><code>ResourceCurveValuesType</code></a></div>
</li>
</ul>
<a name="createWorkTimeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWorkTimeType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/WorkTimeType.html" title="class in org.mpxj.primavera.schema">WorkTimeType</a>&nbsp;createWorkTimeType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/WorkTimeType.html" title="class in org.mpxj.primavera.schema"><code>WorkTimeType</code></a></div>
</li>
</ul>
<a name="createProjectRoleSpreadTypePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectRoleSpreadTypePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectRoleSpreadType.Period</a>&nbsp;createProjectRoleSpreadTypePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ProjectRoleSpreadType.Period</code></a></div>
</li>
</ul>
<a name="createProjectResourceSpreadTypePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourceSpreadTypePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a>&nbsp;createProjectResourceSpreadTypePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceSpreadType.Period</code></a></div>
</li>
</ul>
<a name="createActivitySpreadTypePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActivitySpreadTypePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html" title="class in org.mpxj.primavera.schema">ActivitySpreadType.Period</a>&nbsp;createActivitySpreadTypePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ActivitySpreadType.Period</code></a></div>
</li>
</ul>
<a name="createResourceAssignmentSpreadTypePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceAssignmentSpreadTypePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType.Period</a>&nbsp;createResourceAssignmentSpreadTypePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType.Period</code></a></div>
</li>
</ul>
<a name="createEPSProjectWBSSpreadTypePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createEPSProjectWBSSpreadTypePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" title="class in org.mpxj.primavera.schema">EPSProjectWBSSpreadType.Period</a>&nbsp;createEPSProjectWBSSpreadTypePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>EPSProjectWBSSpreadType.Period</code></a></div>
</li>
</ul>
<a name="createResourceRequestTypeResourceRequestCriterion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createResourceRequestTypeResourceRequestCriterion</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.ResourceRequestCriterion.html" title="class in org.mpxj.primavera.schema">ResourceRequestType.ResourceRequestCriterion</a>&nbsp;createResourceRequestTypeResourceRequestCriterion()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.ResourceRequestCriterion.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType.ResourceRequestCriterion</code></a></div>
</li>
</ul>
<a name="createUserTypeResourceRequests--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserTypeResourceRequests</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/UserType.ResourceRequests.html" title="class in org.mpxj.primavera.schema">UserType.ResourceRequests</a>&nbsp;createUserTypeResourceRequests()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/UserType.ResourceRequests.html" title="class in org.mpxj.primavera.schema"><code>UserType.ResourceRequests</code></a></div>
</li>
</ul>
<a name="createCalendarTypeHolidayOrExceptionsHolidayOrException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalendarTypeHolidayOrExceptionsHolidayOrException</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.HolidayOrException.html" title="class in org.mpxj.primavera.schema">CalendarType.HolidayOrExceptions.HolidayOrException</a>&nbsp;createCalendarTypeHolidayOrExceptionsHolidayOrException()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.HolidayOrException.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.HolidayOrExceptions.HolidayOrException</code></a></div>
</li>
</ul>
<a name="createCalendarTypeStandardWorkWeekStandardWorkHours--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalendarTypeStandardWorkWeekStandardWorkHours</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.StandardWorkHours.html" title="class in org.mpxj.primavera.schema">CalendarType.StandardWorkWeek.StandardWorkHours</a>&nbsp;createCalendarTypeStandardWorkWeekStandardWorkHours()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.StandardWorkHours.html" title="class in org.mpxj.primavera.schema"><code>CalendarType.StandardWorkWeek.StandardWorkHours</code></a></div>
</li>
</ul>
<a name="createProjectListTypeProjectBaselineProject--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createProjectListTypeProjectBaselineProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.BaselineProject.html" title="class in org.mpxj.primavera.schema">ProjectListType.Project.BaselineProject</a>&nbsp;createProjectListTypeProjectBaselineProject()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.BaselineProject.html" title="class in org.mpxj.primavera.schema"><code>ProjectListType.Project.BaselineProject</code></a></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
