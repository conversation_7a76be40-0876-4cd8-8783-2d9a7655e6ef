<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ActivitySpreadType.Period (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ActivitySpreadType.Period (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10,"i265":10,"i266":10,"i267":10,"i268":10,"i269":10,"i270":10,"i271":10,"i272":10,"i273":10,"i274":10,"i275":10,"i276":10,"i277":10,"i278":10,"i279":10,"i280":10,"i281":10,"i282":10,"i283":10,"i284":10,"i285":10,"i286":10,"i287":10,"i288":10,"i289":10,"i290":10,"i291":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitySpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ActivitySpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="ActivitySpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ActivitySpreadType.Period" class="title">Class ActivitySpreadType.Period</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ActivitySpreadType.Period</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema">ActivitySpreadType</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ActivitySpreadType.Period</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Baseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaseline1PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselineActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualCost">actualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualExpenseCost">actualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualLaborCost">actualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualLaborUnits">actualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualMaterialCost">actualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualNonLaborCost">actualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualNonLaborUnits">actualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#actualTotalCost">actualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionExpenseCost">atCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionLaborCost">atCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionLaborUnits">atCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionMaterialCost">atCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionNonLaborCost">atCompletionNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionNonLaborUnits">atCompletionNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#atCompletionTotalCost">atCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualExpenseCost">baseline1ActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualLaborCost">baseline1ActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualLaborUnits">baseline1ActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualMaterialCost">baseline1ActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualNonLaborCost">baseline1ActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualNonLaborUnits">baseline1ActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1ActualTotalCost">baseline1ActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedExpenseCost">baseline1PlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedLaborCost">baseline1PlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedLaborUnits">baseline1PlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedMaterialCost">baseline1PlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedNonLaborCost">baseline1PlannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedNonLaborUnits">baseline1PlannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baseline1PlannedTotalCost">baseline1PlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualExpenseCost">baselineActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualLaborCost">baselineActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualLaborUnits">baselineActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualMaterialCost">baselineActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualNonLaborCost">baselineActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualNonLaborUnits">baselineActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselineActualTotalCost">baselineActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedExpenseCost">baselinePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedLaborCost">baselinePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedLaborUnits">baselinePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedMaterialCost">baselinePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedNonLaborCost">baselinePlannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedNonLaborUnits">baselinePlannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#baselinePlannedTotalCost">baselinePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualCost">cumulativeActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualExpenseCost">cumulativeActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualLaborCost">cumulativeActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualLaborUnits">cumulativeActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualMaterialCost">cumulativeActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualNonLaborCost">cumulativeActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualNonLaborUnits">cumulativeActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeActualTotalCost">cumulativeActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionExpenseCost">cumulativeAtCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionLaborCost">cumulativeAtCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionLaborUnits">cumulativeAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionMaterialCost">cumulativeAtCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionNonLaborCost">cumulativeAtCompletionNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionNonLaborUnits">cumulativeAtCompletionNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeAtCompletionTotalCost">cumulativeAtCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualExpenseCost">cumulativeBaseline1ActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualLaborCost">cumulativeBaseline1ActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualLaborUnits">cumulativeBaseline1ActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualMaterialCost">cumulativeBaseline1ActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualNonLaborCost">cumulativeBaseline1ActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualNonLaborUnits">cumulativeBaseline1ActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1ActualTotalCost">cumulativeBaseline1ActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedExpenseCost">cumulativeBaseline1PlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedLaborCost">cumulativeBaseline1PlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedLaborUnits">cumulativeBaseline1PlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedMaterialCost">cumulativeBaseline1PlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedNonLaborCost">cumulativeBaseline1PlannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedNonLaborUnits">cumulativeBaseline1PlannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaseline1PlannedTotalCost">cumulativeBaseline1PlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualExpenseCost">cumulativeBaselineActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualLaborCost">cumulativeBaselineActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualLaborUnits">cumulativeBaselineActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualMaterialCost">cumulativeBaselineActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualNonLaborCost">cumulativeBaselineActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualNonLaborUnits">cumulativeBaselineActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselineActualTotalCost">cumulativeBaselineActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedExpenseCost">cumulativeBaselinePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedLaborCost">cumulativeBaselinePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedLaborUnits">cumulativeBaselinePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedMaterialCost">cumulativeBaselinePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedNonLaborCost">cumulativeBaselinePlannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedNonLaborUnits">cumulativeBaselinePlannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeBaselinePlannedTotalCost">cumulativeBaselinePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEarnedValueCost">cumulativeEarnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEarnedValueLaborUnits">cumulativeEarnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEstimateAtCompletionCost">cumulativeEstimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEstimateAtCompletionLaborUnits">cumulativeEstimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEstimateToCompleteCost">cumulativeEstimateToCompleteCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeEstimateToCompleteLaborUnits">cumulativeEstimateToCompleteLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedExpenseCost">cumulativePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedLaborCost">cumulativePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedLaborUnits">cumulativePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedMaterialCost">cumulativePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedNonLaborCost">cumulativePlannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedNonLaborUnits">cumulativePlannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedTotalCost">cumulativePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedValueCost">cumulativePlannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativePlannedValueLaborUnits">cumulativePlannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingExpenseCost">cumulativeRemainingExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLaborCost">cumulativeRemainingLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLaborUnits">cumulativeRemainingLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateExpenseCost">cumulativeRemainingLateExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateLaborCost">cumulativeRemainingLateLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateLaborUnits">cumulativeRemainingLateLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateMaterialCost">cumulativeRemainingLateMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateNonLaborCost">cumulativeRemainingLateNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateNonLaborUnits">cumulativeRemainingLateNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingLateTotalCost">cumulativeRemainingLateTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingMaterialCost">cumulativeRemainingMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingNonLaborCost">cumulativeRemainingNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingNonLaborUnits">cumulativeRemainingNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#cumulativeRemainingTotalCost">cumulativeRemainingTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#earnedValueCost">earnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#earnedValueLaborUnits">earnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#endDate">endDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#estimateAtCompletionCost">estimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#estimateAtCompletionLaborUnits">estimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#estimateToCompleteCost">estimateToCompleteCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#estimateToCompleteLaborUnits">estimateToCompleteLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedExpenseCost">plannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedLaborCost">plannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedLaborUnits">plannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedMaterialCost">plannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedNonLaborCost">plannedNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedNonLaborUnits">plannedNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedTotalCost">plannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedValueCost">plannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#plannedValueLaborUnits">plannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingExpenseCost">remainingExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLaborCost">remainingLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLaborUnits">remainingLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateExpenseCost">remainingLateExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateLaborCost">remainingLateLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateLaborUnits">remainingLateLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateMaterialCost">remainingLateMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateNonLaborCost">remainingLateNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateNonLaborUnits">remainingLateNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingLateTotalCost">remainingLateTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingMaterialCost">remainingMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingNonLaborCost">remainingNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingNonLaborUnits">remainingNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#remainingTotalCost">remainingTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#Period--">Period</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Gets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualExpenseCost--">getActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the actualExpenseCost property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualLaborCost--">getActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the actualLaborCost property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualLaborUnits--">getActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the actualLaborUnits property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualMaterialCost--">getActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the actualMaterialCost property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualNonLaborCost--">getActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the actualNonLaborCost property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualNonLaborUnits--">getActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the actualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getActualTotalCost--">getActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the actualTotalCost property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionExpenseCost--">getAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionLaborCost--">getAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionLaborUnits--">getAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionMaterialCost--">getAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionNonLaborCost--">getAtCompletionNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionNonLaborUnits--">getAtCompletionNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getAtCompletionTotalCost--">getAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualExpenseCost--">getBaseline1ActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualExpenseCost property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualLaborCost--">getBaseline1ActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualLaborCost property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualLaborUnits--">getBaseline1ActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualLaborUnits property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualMaterialCost--">getBaseline1ActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualMaterialCost property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualNonLaborCost--">getBaseline1ActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualNonLaborUnits--">getBaseline1ActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1ActualTotalCost--">getBaseline1ActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the baseline1ActualTotalCost property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedExpenseCost--">getBaseline1PlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedLaborCost--">getBaseline1PlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedLaborCost property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedLaborUnits--">getBaseline1PlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedMaterialCost--">getBaseline1PlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedNonLaborCost--">getBaseline1PlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedNonLaborUnits--">getBaseline1PlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaseline1PlannedTotalCost--">getBaseline1PlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the baseline1PlannedTotalCost property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualExpenseCost--">getBaselineActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the baselineActualExpenseCost property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualLaborCost--">getBaselineActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the baselineActualLaborCost property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualLaborUnits--">getBaselineActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselineActualLaborUnits property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualMaterialCost--">getBaselineActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the baselineActualMaterialCost property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualNonLaborCost--">getBaselineActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the baselineActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualNonLaborUnits--">getBaselineActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselineActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselineActualTotalCost--">getBaselineActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the baselineActualTotalCost property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedExpenseCost--">getBaselinePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedLaborCost--">getBaselinePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedLaborUnits--">getBaselinePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedMaterialCost--">getBaselinePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedNonLaborCost--">getBaselinePlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedNonLaborUnits--">getBaselinePlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getBaselinePlannedTotalCost--">getBaselinePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualCost--">getCumulativeActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualExpenseCost--">getCumulativeActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualExpenseCost property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualLaborCost--">getCumulativeActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualLaborCost property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualLaborUnits--">getCumulativeActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualLaborUnits property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualMaterialCost--">getCumulativeActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualMaterialCost property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualNonLaborCost--">getCumulativeActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualNonLaborUnits--">getCumulativeActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeActualTotalCost--">getCumulativeActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualTotalCost property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionExpenseCost--">getCumulativeAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionLaborCost--">getCumulativeAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionLaborUnits--">getCumulativeAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionMaterialCost--">getCumulativeAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionNonLaborCost--">getCumulativeAtCompletionNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionNonLaborUnits--">getCumulativeAtCompletionNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeAtCompletionTotalCost--">getCumulativeAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualExpenseCost--">getCumulativeBaseline1ActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualExpenseCost property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualLaborCost--">getCumulativeBaseline1ActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualLaborCost property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualLaborUnits--">getCumulativeBaseline1ActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualLaborUnits property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualMaterialCost--">getCumulativeBaseline1ActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualMaterialCost property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualNonLaborCost--">getCumulativeBaseline1ActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualNonLaborUnits--">getCumulativeBaseline1ActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1ActualTotalCost--">getCumulativeBaseline1ActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1ActualTotalCost property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedExpenseCost--">getCumulativeBaseline1PlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedLaborCost--">getCumulativeBaseline1PlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedLaborCost property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedLaborUnits--">getCumulativeBaseline1PlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedMaterialCost--">getCumulativeBaseline1PlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedNonLaborCost--">getCumulativeBaseline1PlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedNonLaborUnits--">getCumulativeBaseline1PlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaseline1PlannedTotalCost--">getCumulativeBaseline1PlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaseline1PlannedTotalCost property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualExpenseCost--">getCumulativeBaselineActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualExpenseCost property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualLaborCost--">getCumulativeBaselineActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualLaborCost property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualLaborUnits--">getCumulativeBaselineActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualLaborUnits property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualMaterialCost--">getCumulativeBaselineActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualMaterialCost property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualNonLaborCost--">getCumulativeBaselineActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualNonLaborUnits--">getCumulativeBaselineActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselineActualTotalCost--">getCumulativeBaselineActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselineActualTotalCost property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedExpenseCost--">getCumulativeBaselinePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedLaborCost--">getCumulativeBaselinePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedLaborUnits--">getCumulativeBaselinePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedMaterialCost--">getCumulativeBaselinePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedNonLaborCost--">getCumulativeBaselinePlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedNonLaborUnits--">getCumulativeBaselinePlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeBaselinePlannedTotalCost--">getCumulativeBaselinePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEarnedValueCost--">getCumulativeEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEarnedValueCost property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEarnedValueLaborUnits--">getCumulativeEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEstimateAtCompletionCost--">getCumulativeEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEstimateAtCompletionLaborUnits--">getCumulativeEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEstimateToCompleteCost--">getCumulativeEstimateToCompleteCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeEstimateToCompleteLaborUnits--">getCumulativeEstimateToCompleteLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedExpenseCost--">getCumulativePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedLaborCost--">getCumulativePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedLaborUnits--">getCumulativePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedMaterialCost--">getCumulativePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedNonLaborCost--">getCumulativePlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedNonLaborUnits--">getCumulativePlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedTotalCost--">getCumulativePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedValueCost--">getCumulativePlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedValueCost property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativePlannedValueLaborUnits--">getCumulativePlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingExpenseCost--">getCumulativeRemainingExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingExpenseCost property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLaborCost--">getCumulativeRemainingLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLaborCost property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLaborUnits--">getCumulativeRemainingLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLaborUnits property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateExpenseCost--">getCumulativeRemainingLateExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateLaborCost--">getCumulativeRemainingLateLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateLaborUnits--">getCumulativeRemainingLateLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateMaterialCost--">getCumulativeRemainingLateMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateNonLaborCost--">getCumulativeRemainingLateNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateNonLaborCost property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateNonLaborUnits--">getCumulativeRemainingLateNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateNonLaborUnits property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingLateTotalCost--">getCumulativeRemainingLateTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingMaterialCost--">getCumulativeRemainingMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingMaterialCost property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingNonLaborCost--">getCumulativeRemainingNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingNonLaborCost property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingNonLaborUnits--">getCumulativeRemainingNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingNonLaborUnits property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getCumulativeRemainingTotalCost--">getCumulativeRemainingTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingTotalCost property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEarnedValueCost--">getEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the earnedValueCost property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEarnedValueLaborUnits--">getEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the earnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEndDate--">getEndDate</a></span>()</code>
<div class="block">Gets the value of the endDate property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEstimateAtCompletionCost--">getEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the estimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEstimateAtCompletionLaborUnits--">getEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the estimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEstimateToCompleteCost--">getEstimateToCompleteCost</a></span>()</code>
<div class="block">Gets the value of the estimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getEstimateToCompleteLaborUnits--">getEstimateToCompleteLaborUnits</a></span>()</code>
<div class="block">Gets the value of the estimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedExpenseCost--">getPlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the plannedExpenseCost property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedLaborCost--">getPlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the plannedLaborCost property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedLaborUnits--">getPlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedLaborUnits property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedMaterialCost--">getPlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the plannedMaterialCost property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedNonLaborCost--">getPlannedNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the plannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedNonLaborUnits--">getPlannedNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedTotalCost--">getPlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the plannedTotalCost property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedValueCost--">getPlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the plannedValueCost property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getPlannedValueLaborUnits--">getPlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingExpenseCost--">getRemainingExpenseCost</a></span>()</code>
<div class="block">Gets the value of the remainingExpenseCost property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLaborCost--">getRemainingLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLaborCost property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLaborUnits--">getRemainingLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLaborUnits property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateExpenseCost--">getRemainingLateExpenseCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateLaborCost--">getRemainingLateLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateLaborUnits--">getRemainingLateLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateMaterialCost--">getRemainingLateMaterialCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateNonLaborCost--">getRemainingLateNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateNonLaborCost property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateNonLaborUnits--">getRemainingLateNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLateNonLaborUnits property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingLateTotalCost--">getRemainingLateTotalCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingMaterialCost--">getRemainingMaterialCost</a></span>()</code>
<div class="block">Gets the value of the remainingMaterialCost property.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingNonLaborCost--">getRemainingNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingNonLaborCost property.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingNonLaborUnits--">getRemainingNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingNonLaborUnits property.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getRemainingTotalCost--">getRemainingTotalCost</a></span>()</code>
<div class="block">Gets the value of the remainingTotalCost property.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualCost-java.lang.Double-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualExpenseCost-java.lang.Double-">setActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualExpenseCost property.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualLaborCost-java.lang.Double-">setActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualLaborCost property.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualLaborUnits-java.lang.Double-">setActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualLaborUnits property.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualMaterialCost-java.lang.Double-">setActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualMaterialCost property.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualNonLaborCost-java.lang.Double-">setActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualNonLaborCost property.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualNonLaborUnits-java.lang.Double-">setActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setActualTotalCost-java.lang.Double-">setActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualTotalCost property.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionExpenseCost-java.lang.Double-">setAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionLaborCost-java.lang.Double-">setAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionLaborUnits-java.lang.Double-">setAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionMaterialCost-java.lang.Double-">setAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionNonLaborCost-java.lang.Double-">setAtCompletionNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionNonLaborUnits-java.lang.Double-">setAtCompletionNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setAtCompletionTotalCost-java.lang.Double-">setAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualExpenseCost-java.lang.Double-">setBaseline1ActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualExpenseCost property.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualLaborCost-java.lang.Double-">setBaseline1ActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualLaborCost property.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualLaborUnits-java.lang.Double-">setBaseline1ActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualLaborUnits property.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualMaterialCost-java.lang.Double-">setBaseline1ActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualMaterialCost property.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualNonLaborCost-java.lang.Double-">setBaseline1ActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualNonLaborUnits-java.lang.Double-">setBaseline1ActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1ActualTotalCost-java.lang.Double-">setBaseline1ActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1ActualTotalCost property.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedExpenseCost-java.lang.Double-">setBaseline1PlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedLaborCost-java.lang.Double-">setBaseline1PlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedLaborCost property.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedLaborUnits-java.lang.Double-">setBaseline1PlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedMaterialCost-java.lang.Double-">setBaseline1PlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedNonLaborCost-java.lang.Double-">setBaseline1PlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedNonLaborUnits-java.lang.Double-">setBaseline1PlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaseline1PlannedTotalCost-java.lang.Double-">setBaseline1PlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseline1PlannedTotalCost property.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualExpenseCost-java.lang.Double-">setBaselineActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualExpenseCost property.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualLaborCost-java.lang.Double-">setBaselineActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualLaborCost property.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualLaborUnits-java.lang.Double-">setBaselineActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualLaborUnits property.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualMaterialCost-java.lang.Double-">setBaselineActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualMaterialCost property.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualNonLaborCost-java.lang.Double-">setBaselineActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualNonLaborUnits-java.lang.Double-">setBaselineActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselineActualTotalCost-java.lang.Double-">setBaselineActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineActualTotalCost property.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedExpenseCost-java.lang.Double-">setBaselinePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedLaborCost-java.lang.Double-">setBaselinePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedLaborUnits-java.lang.Double-">setBaselinePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedMaterialCost-java.lang.Double-">setBaselinePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedNonLaborCost-java.lang.Double-">setBaselinePlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedNonLaborUnits-java.lang.Double-">setBaselinePlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setBaselinePlannedTotalCost-java.lang.Double-">setBaselinePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualCost-java.lang.Double-">setCumulativeActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualExpenseCost-java.lang.Double-">setCumulativeActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualExpenseCost property.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualLaborCost-java.lang.Double-">setCumulativeActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualLaborCost property.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualLaborUnits-java.lang.Double-">setCumulativeActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualLaborUnits property.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualMaterialCost-java.lang.Double-">setCumulativeActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualMaterialCost property.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualNonLaborCost-java.lang.Double-">setCumulativeActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualNonLaborUnits-java.lang.Double-">setCumulativeActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeActualTotalCost-java.lang.Double-">setCumulativeActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualTotalCost property.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionExpenseCost-java.lang.Double-">setCumulativeAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionLaborCost-java.lang.Double-">setCumulativeAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionLaborUnits-java.lang.Double-">setCumulativeAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionMaterialCost-java.lang.Double-">setCumulativeAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionNonLaborCost-java.lang.Double-">setCumulativeAtCompletionNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionNonLaborUnits-java.lang.Double-">setCumulativeAtCompletionNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeAtCompletionTotalCost-java.lang.Double-">setCumulativeAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualExpenseCost-java.lang.Double-">setCumulativeBaseline1ActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualExpenseCost property.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualLaborCost-java.lang.Double-">setCumulativeBaseline1ActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualLaborCost property.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualLaborUnits-java.lang.Double-">setCumulativeBaseline1ActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualLaborUnits property.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualMaterialCost-java.lang.Double-">setCumulativeBaseline1ActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualMaterialCost property.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualNonLaborCost-java.lang.Double-">setCumulativeBaseline1ActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualNonLaborUnits-java.lang.Double-">setCumulativeBaseline1ActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1ActualTotalCost-java.lang.Double-">setCumulativeBaseline1ActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1ActualTotalCost property.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedExpenseCost-java.lang.Double-">setCumulativeBaseline1PlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedLaborCost-java.lang.Double-">setCumulativeBaseline1PlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedLaborCost property.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedLaborUnits-java.lang.Double-">setCumulativeBaseline1PlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedMaterialCost-java.lang.Double-">setCumulativeBaseline1PlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedNonLaborCost-java.lang.Double-">setCumulativeBaseline1PlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedNonLaborUnits-java.lang.Double-">setCumulativeBaseline1PlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaseline1PlannedTotalCost-java.lang.Double-">setCumulativeBaseline1PlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaseline1PlannedTotalCost property.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualExpenseCost-java.lang.Double-">setCumulativeBaselineActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualExpenseCost property.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualLaborCost-java.lang.Double-">setCumulativeBaselineActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualLaborCost property.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualLaborUnits-java.lang.Double-">setCumulativeBaselineActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualLaborUnits property.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualMaterialCost-java.lang.Double-">setCumulativeBaselineActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualMaterialCost property.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualNonLaborCost-java.lang.Double-">setCumulativeBaselineActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualNonLaborUnits-java.lang.Double-">setCumulativeBaselineActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselineActualTotalCost-java.lang.Double-">setCumulativeBaselineActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselineActualTotalCost property.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedExpenseCost-java.lang.Double-">setCumulativeBaselinePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedLaborCost-java.lang.Double-">setCumulativeBaselinePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedLaborUnits-java.lang.Double-">setCumulativeBaselinePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedMaterialCost-java.lang.Double-">setCumulativeBaselinePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedNonLaborCost-java.lang.Double-">setCumulativeBaselinePlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedNonLaborUnits-java.lang.Double-">setCumulativeBaselinePlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeBaselinePlannedTotalCost-java.lang.Double-">setCumulativeBaselinePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEarnedValueCost-java.lang.Double-">setCumulativeEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEarnedValueCost property.</div>
</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEarnedValueLaborUnits-java.lang.Double-">setCumulativeEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEstimateAtCompletionCost-java.lang.Double-">setCumulativeEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEstimateAtCompletionLaborUnits-java.lang.Double-">setCumulativeEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEstimateToCompleteCost-java.lang.Double-">setCumulativeEstimateToCompleteCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeEstimateToCompleteLaborUnits-java.lang.Double-">setCumulativeEstimateToCompleteLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedExpenseCost-java.lang.Double-">setCumulativePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedLaborCost-java.lang.Double-">setCumulativePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedLaborUnits-java.lang.Double-">setCumulativePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedMaterialCost-java.lang.Double-">setCumulativePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedNonLaborCost-java.lang.Double-">setCumulativePlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedNonLaborUnits-java.lang.Double-">setCumulativePlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedTotalCost-java.lang.Double-">setCumulativePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedValueCost-java.lang.Double-">setCumulativePlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedValueCost property.</div>
</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativePlannedValueLaborUnits-java.lang.Double-">setCumulativePlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingExpenseCost-java.lang.Double-">setCumulativeRemainingExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingExpenseCost property.</div>
</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLaborCost-java.lang.Double-">setCumulativeRemainingLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLaborCost property.</div>
</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLaborUnits-java.lang.Double-">setCumulativeRemainingLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLaborUnits property.</div>
</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateExpenseCost-java.lang.Double-">setCumulativeRemainingLateExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateLaborCost-java.lang.Double-">setCumulativeRemainingLateLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateLaborUnits-java.lang.Double-">setCumulativeRemainingLateLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateMaterialCost-java.lang.Double-">setCumulativeRemainingLateMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateNonLaborCost-java.lang.Double-">setCumulativeRemainingLateNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateNonLaborCost property.</div>
</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateNonLaborUnits-java.lang.Double-">setCumulativeRemainingLateNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateNonLaborUnits property.</div>
</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingLateTotalCost-java.lang.Double-">setCumulativeRemainingLateTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingMaterialCost-java.lang.Double-">setCumulativeRemainingMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingMaterialCost property.</div>
</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingNonLaborCost-java.lang.Double-">setCumulativeRemainingNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingNonLaborCost property.</div>
</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingNonLaborUnits-java.lang.Double-">setCumulativeRemainingNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingNonLaborUnits property.</div>
</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setCumulativeRemainingTotalCost-java.lang.Double-">setCumulativeRemainingTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingTotalCost property.</div>
</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEarnedValueCost-java.lang.Double-">setEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the earnedValueCost property.</div>
</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEarnedValueLaborUnits-java.lang.Double-">setEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the earnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEndDate-java.time.LocalDateTime-">setEndDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the endDate property.</div>
</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEstimateAtCompletionCost-java.lang.Double-">setEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i265" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEstimateAtCompletionLaborUnits-java.lang.Double-">setEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i266" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEstimateToCompleteCost-java.lang.Double-">setEstimateToCompleteCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i267" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setEstimateToCompleteLaborUnits-java.lang.Double-">setEstimateToCompleteLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i268" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedExpenseCost-java.lang.Double-">setPlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedExpenseCost property.</div>
</td>
</tr>
<tr id="i269" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedLaborCost-java.lang.Double-">setPlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedLaborCost property.</div>
</td>
</tr>
<tr id="i270" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedLaborUnits-java.lang.Double-">setPlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedLaborUnits property.</div>
</td>
</tr>
<tr id="i271" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedMaterialCost-java.lang.Double-">setPlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedMaterialCost property.</div>
</td>
</tr>
<tr id="i272" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedNonLaborCost-java.lang.Double-">setPlannedNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedNonLaborCost property.</div>
</td>
</tr>
<tr id="i273" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedNonLaborUnits-java.lang.Double-">setPlannedNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedNonLaborUnits property.</div>
</td>
</tr>
<tr id="i274" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedTotalCost-java.lang.Double-">setPlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedTotalCost property.</div>
</td>
</tr>
<tr id="i275" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedValueCost-java.lang.Double-">setPlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedValueCost property.</div>
</td>
</tr>
<tr id="i276" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setPlannedValueLaborUnits-java.lang.Double-">setPlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i277" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingExpenseCost-java.lang.Double-">setRemainingExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingExpenseCost property.</div>
</td>
</tr>
<tr id="i278" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLaborCost-java.lang.Double-">setRemainingLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLaborCost property.</div>
</td>
</tr>
<tr id="i279" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLaborUnits-java.lang.Double-">setRemainingLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLaborUnits property.</div>
</td>
</tr>
<tr id="i280" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateExpenseCost-java.lang.Double-">setRemainingLateExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i281" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateLaborCost-java.lang.Double-">setRemainingLateLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i282" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateLaborUnits-java.lang.Double-">setRemainingLateLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i283" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateMaterialCost-java.lang.Double-">setRemainingLateMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i284" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateNonLaborCost-java.lang.Double-">setRemainingLateNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateNonLaborCost property.</div>
</td>
</tr>
<tr id="i285" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateNonLaborUnits-java.lang.Double-">setRemainingLateNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateNonLaborUnits property.</div>
</td>
</tr>
<tr id="i286" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingLateTotalCost-java.lang.Double-">setRemainingLateTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i287" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingMaterialCost-java.lang.Double-">setRemainingMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingMaterialCost property.</div>
</td>
</tr>
<tr id="i288" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingNonLaborCost-java.lang.Double-">setRemainingNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingNonLaborCost property.</div>
</td>
</tr>
<tr id="i289" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingNonLaborUnits-java.lang.Double-">setRemainingNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingNonLaborUnits property.</div>
</td>
</tr>
<tr id="i290" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setRemainingTotalCost-java.lang.Double-">setRemainingTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingTotalCost property.</div>
</td>
</tr>
<tr id="i291" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="endDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> endDate</pre>
</li>
</ul>
<a name="actualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualLaborUnits</pre>
</li>
</ul>
<a name="cumulativeActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualLaborUnits</pre>
</li>
</ul>
<a name="actualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualNonLaborUnits</pre>
</li>
</ul>
<a name="atCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativeAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="atCompletionNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeAtCompletionNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionNonLaborUnits</pre>
</li>
</ul>
<a name="baseline1ActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualLaborUnits</pre>
</li>
</ul>
<a name="baseline1ActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualNonLaborUnits</pre>
</li>
</ul>
<a name="baseline1PlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedLaborUnits</pre>
</li>
</ul>
<a name="baseline1PlannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedNonLaborUnits</pre>
</li>
</ul>
<a name="baselineActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselineActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualLaborUnits</pre>
</li>
</ul>
<a name="baselineActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselineActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualNonLaborUnits</pre>
</li>
</ul>
<a name="baselinePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedLaborUnits</pre>
</li>
</ul>
<a name="baselinePlannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedNonLaborUnits</pre>
</li>
</ul>
<a name="earnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> earnedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEarnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEarnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEarnedValueLaborUnits</pre>
</li>
</ul>
<a name="estimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEstimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="estimateToCompleteLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompleteLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompleteLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEstimateToCompleteLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateToCompleteLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateToCompleteLaborUnits</pre>
</li>
</ul>
<a name="plannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedLaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedLaborUnits</pre>
</li>
</ul>
<a name="plannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedNonLaborUnits</pre>
</li>
</ul>
<a name="plannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedValueLaborUnits</pre>
</li>
</ul>
<a name="remainingLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLaborUnits</pre>
</li>
</ul>
<a name="remainingLateLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLateLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateLaborUnits</pre>
</li>
</ul>
<a name="remainingLateNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLateNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateNonLaborUnits</pre>
</li>
</ul>
<a name="remainingNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingNonLaborUnits</pre>
</li>
</ul>
<a name="actualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualCost</pre>
</li>
</ul>
<a name="cumulativeActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualCost</pre>
</li>
</ul>
<a name="actualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualExpenseCost</pre>
</li>
</ul>
<a name="cumulativeActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualExpenseCost</pre>
</li>
</ul>
<a name="actualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualLaborCost</pre>
</li>
</ul>
<a name="cumulativeActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualLaborCost</pre>
</li>
</ul>
<a name="actualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualMaterialCost</pre>
</li>
</ul>
<a name="cumulativeActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualMaterialCost</pre>
</li>
</ul>
<a name="actualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualNonLaborCost</pre>
</li>
</ul>
<a name="actualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualTotalCost</pre>
</li>
</ul>
<a name="cumulativeActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualTotalCost</pre>
</li>
</ul>
<a name="atCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionExpenseCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionExpenseCost</pre>
</li>
</ul>
<a name="atCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionLaborCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionLaborCost</pre>
</li>
</ul>
<a name="atCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionMaterialCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionMaterialCost</pre>
</li>
</ul>
<a name="atCompletionNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionNonLaborCost</pre>
</li>
</ul>
<a name="atCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionTotalCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionTotalCost</pre>
</li>
</ul>
<a name="baseline1ActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualExpenseCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualExpenseCost</pre>
</li>
</ul>
<a name="baseline1ActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualLaborCost</pre>
</li>
</ul>
<a name="baseline1ActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualMaterialCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualMaterialCost</pre>
</li>
</ul>
<a name="baseline1ActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualNonLaborCost</pre>
</li>
</ul>
<a name="baseline1ActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1ActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1ActualTotalCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1ActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1ActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1ActualTotalCost</pre>
</li>
</ul>
<a name="baseline1PlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedExpenseCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedExpenseCost</pre>
</li>
</ul>
<a name="baseline1PlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedLaborCost</pre>
</li>
</ul>
<a name="baseline1PlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedMaterialCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedMaterialCost</pre>
</li>
</ul>
<a name="baseline1PlannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedNonLaborCost</pre>
</li>
</ul>
<a name="baseline1PlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline1PlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baseline1PlannedTotalCost</pre>
</li>
</ul>
<a name="cumulativeBaseline1PlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaseline1PlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaseline1PlannedTotalCost</pre>
</li>
</ul>
<a name="baselineActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualExpenseCost</pre>
</li>
</ul>
<a name="cumulativeBaselineActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualExpenseCost</pre>
</li>
</ul>
<a name="baselineActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselineActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualLaborCost</pre>
</li>
</ul>
<a name="baselineActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualMaterialCost</pre>
</li>
</ul>
<a name="cumulativeBaselineActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualMaterialCost</pre>
</li>
</ul>
<a name="baselineActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselineActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualNonLaborCost</pre>
</li>
</ul>
<a name="baselineActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineActualTotalCost</pre>
</li>
</ul>
<a name="cumulativeBaselineActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselineActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselineActualTotalCost</pre>
</li>
</ul>
<a name="baselinePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedExpenseCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedExpenseCost</pre>
</li>
</ul>
<a name="baselinePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedLaborCost</pre>
</li>
</ul>
<a name="baselinePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedMaterialCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedMaterialCost</pre>
</li>
</ul>
<a name="baselinePlannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedNonLaborCost</pre>
</li>
</ul>
<a name="baselinePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedTotalCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedTotalCost</pre>
</li>
</ul>
<a name="earnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> earnedValueCost</pre>
</li>
</ul>
<a name="cumulativeEarnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEarnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEarnedValueCost</pre>
</li>
</ul>
<a name="estimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativeEstimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateAtCompletionCost</pre>
</li>
</ul>
<a name="estimateToCompleteCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompleteCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompleteCost</pre>
</li>
</ul>
<a name="cumulativeEstimateToCompleteCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateToCompleteCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateToCompleteCost</pre>
</li>
</ul>
<a name="plannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedExpenseCost</pre>
</li>
</ul>
<a name="cumulativePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedExpenseCost</pre>
</li>
</ul>
<a name="plannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedLaborCost</pre>
</li>
</ul>
<a name="cumulativePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedLaborCost</pre>
</li>
</ul>
<a name="plannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedMaterialCost</pre>
</li>
</ul>
<a name="cumulativePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedMaterialCost</pre>
</li>
</ul>
<a name="plannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedNonLaborCost</pre>
</li>
</ul>
<a name="cumulativePlannedNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedNonLaborCost</pre>
</li>
</ul>
<a name="plannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedTotalCost</pre>
</li>
</ul>
<a name="cumulativePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedTotalCost</pre>
</li>
</ul>
<a name="plannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedValueCost</pre>
</li>
</ul>
<a name="cumulativePlannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedValueCost</pre>
</li>
</ul>
<a name="remainingExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingExpenseCost</pre>
</li>
</ul>
<a name="cumulativeRemainingExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingExpenseCost</pre>
</li>
</ul>
<a name="remainingLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLaborCost</pre>
</li>
</ul>
<a name="remainingLateExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateExpenseCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateExpenseCost</pre>
</li>
</ul>
<a name="remainingLateLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateLaborCost</pre>
</li>
</ul>
<a name="remainingLateMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateMaterialCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateMaterialCost</pre>
</li>
</ul>
<a name="remainingLateNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateNonLaborCost</pre>
</li>
</ul>
<a name="remainingLateTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateTotalCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateTotalCost</pre>
</li>
</ul>
<a name="remainingMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingMaterialCost</pre>
</li>
</ul>
<a name="cumulativeRemainingMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingMaterialCost</pre>
</li>
</ul>
<a name="remainingNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingNonLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingNonLaborCost</pre>
</li>
</ul>
<a name="remainingTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingTotalCost</pre>
</li>
</ul>
<a name="cumulativeRemainingTotalCost">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cumulativeRemainingTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingTotalCost</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Period--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Period</h4>
<pre>public&nbsp;Period()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEndDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEndDate()</pre>
<div class="block">Gets the value of the endDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEndDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndDate</h4>
<pre>public&nbsp;void&nbsp;setEndDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the endDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualLaborUnits()</pre>
<div class="block">Gets the value of the actualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualNonLaborUnits()</pre>
<div class="block">Gets the value of the actualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the atCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionNonLaborUnits()</pre>
<div class="block">Gets the value of the atCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualLaborUnits()</pre>
<div class="block">Gets the value of the baseline1ActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualNonLaborUnits()</pre>
<div class="block">Gets the value of the baseline1ActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedLaborUnits()</pre>
<div class="block">Gets the value of the baseline1PlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the baseline1PlannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualLaborUnits()</pre>
<div class="block">Gets the value of the baselineActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualNonLaborUnits()</pre>
<div class="block">Gets the value of the baselineActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedLaborUnits()</pre>
<div class="block">Gets the value of the baselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the baselinePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the earnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the estimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompleteLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompleteLaborUnits()</pre>
<div class="block">Gets the value of the estimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompleteLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompleteLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateToCompleteLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateToCompleteLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateToCompleteLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateToCompleteLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedLaborUnits()</pre>
<div class="block">Gets the value of the plannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the plannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the plannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLaborUnits()</pre>
<div class="block">Gets the value of the remainingLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateLaborUnits()</pre>
<div class="block">Gets the value of the remainingLateLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateNonLaborUnits()</pre>
<div class="block">Gets the value of the remainingLateNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingNonLaborUnits()</pre>
<div class="block">Gets the value of the remainingNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualCost()</pre>
<div class="block">Gets the value of the actualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualCost()</pre>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualExpenseCost()</pre>
<div class="block">Gets the value of the actualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualLaborCost()</pre>
<div class="block">Gets the value of the actualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualLaborCost()</pre>
<div class="block">Gets the value of the cumulativeActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualMaterialCost()</pre>
<div class="block">Gets the value of the actualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualNonLaborCost()</pre>
<div class="block">Gets the value of the actualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualTotalCost()</pre>
<div class="block">Gets the value of the actualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualTotalCost()</pre>
<div class="block">Gets the value of the cumulativeActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the atCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the atCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the atCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionNonLaborCost()</pre>
<div class="block">Gets the value of the atCompletionNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the atCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualExpenseCost()</pre>
<div class="block">Gets the value of the baseline1ActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualLaborCost()</pre>
<div class="block">Gets the value of the baseline1ActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualMaterialCost()</pre>
<div class="block">Gets the value of the baseline1ActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualNonLaborCost()</pre>
<div class="block">Gets the value of the baseline1ActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1ActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1ActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1ActualTotalCost()</pre>
<div class="block">Gets the value of the baseline1ActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1ActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1ActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1ActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1ActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1ActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1ActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1ActualTotalCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1ActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1ActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1ActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1ActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1ActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedExpenseCost()</pre>
<div class="block">Gets the value of the baseline1PlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedLaborCost()</pre>
<div class="block">Gets the value of the baseline1PlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedMaterialCost()</pre>
<div class="block">Gets the value of the baseline1PlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedNonLaborCost()</pre>
<div class="block">Gets the value of the baseline1PlannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseline1PlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline1PlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaseline1PlannedTotalCost()</pre>
<div class="block">Gets the value of the baseline1PlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseline1PlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseline1PlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setBaseline1PlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseline1PlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaseline1PlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaseline1PlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaseline1PlannedTotalCost()</pre>
<div class="block">Gets the value of the cumulativeBaseline1PlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaseline1PlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaseline1PlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaseline1PlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaseline1PlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualExpenseCost()</pre>
<div class="block">Gets the value of the baselineActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualLaborCost()</pre>
<div class="block">Gets the value of the baselineActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualMaterialCost()</pre>
<div class="block">Gets the value of the baselineActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualNonLaborCost()</pre>
<div class="block">Gets the value of the baselineActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineActualTotalCost()</pre>
<div class="block">Gets the value of the baselineActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselineActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselineActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselineActualTotalCost()</pre>
<div class="block">Gets the value of the cumulativeBaselineActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselineActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselineActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselineActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselineActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedExpenseCost()</pre>
<div class="block">Gets the value of the baselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedLaborCost()</pre>
<div class="block">Gets the value of the baselinePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedMaterialCost()</pre>
<div class="block">Gets the value of the baselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedNonLaborCost()</pre>
<div class="block">Gets the value of the baselinePlannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedTotalCost()</pre>
<div class="block">Gets the value of the baselinePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedTotalCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEarnedValueCost()</pre>
<div class="block">Gets the value of the earnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEarnedValueCost()</pre>
<div class="block">Gets the value of the cumulativeEarnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEarnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the estimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompleteCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompleteCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompleteCost()</pre>
<div class="block">Gets the value of the estimateToCompleteCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompleteCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompleteCost</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompleteCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompleteCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateToCompleteCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateToCompleteCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateToCompleteCost()</pre>
<div class="block">Gets the value of the cumulativeEstimateToCompleteCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateToCompleteCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateToCompleteCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateToCompleteCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateToCompleteCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedExpenseCost()</pre>
<div class="block">Gets the value of the plannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedExpenseCost()</pre>
<div class="block">Gets the value of the cumulativePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedLaborCost()</pre>
<div class="block">Gets the value of the plannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedLaborCost()</pre>
<div class="block">Gets the value of the cumulativePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedMaterialCost()</pre>
<div class="block">Gets the value of the plannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedMaterialCost()</pre>
<div class="block">Gets the value of the cumulativePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedNonLaborCost()</pre>
<div class="block">Gets the value of the plannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativePlannedNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedTotalCost()</pre>
<div class="block">Gets the value of the plannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedTotalCost()</pre>
<div class="block">Gets the value of the cumulativePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedValueCost()</pre>
<div class="block">Gets the value of the plannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedValueCost()</pre>
<div class="block">Gets the value of the cumulativePlannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingExpenseCost()</pre>
<div class="block">Gets the value of the remainingExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLaborCost()</pre>
<div class="block">Gets the value of the remainingLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateExpenseCost()</pre>
<div class="block">Gets the value of the remainingLateExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateLaborCost()</pre>
<div class="block">Gets the value of the remainingLateLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateMaterialCost()</pre>
<div class="block">Gets the value of the remainingLateMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateNonLaborCost()</pre>
<div class="block">Gets the value of the remainingLateNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateTotalCost()</pre>
<div class="block">Gets the value of the remainingLateTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateTotalCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateTotalCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingMaterialCost()</pre>
<div class="block">Gets the value of the remainingMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingNonLaborCost()</pre>
<div class="block">Gets the value of the remainingNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingTotalCost()</pre>
<div class="block">Gets the value of the remainingTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingTotalCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingTotalCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setCumulativeRemainingTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ActivitySpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ActivitySpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="ActivitySpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
