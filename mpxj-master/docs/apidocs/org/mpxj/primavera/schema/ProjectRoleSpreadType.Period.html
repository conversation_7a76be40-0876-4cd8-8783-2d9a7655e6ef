<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectRoleSpreadType.Period (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectRoleSpreadType.Period (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectRoleSpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="ProjectRoleSpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ProjectRoleSpreadType.Period" class="title">Class ProjectRoleSpreadType.Period</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ProjectRoleSpreadType.Period</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema">ProjectRoleSpreadType</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ProjectRoleSpreadType.Period</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Limit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeLimit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualCost">actualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualOvertimeCost">actualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualOvertimeUnits">actualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualRegularCost">actualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualRegularUnits">actualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#actualUnits">actualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#atCompletionCost">atCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#atCompletionUnits">atCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualCost">cumulativeActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualOvertimeCost">cumulativeActualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualOvertimeUnits">cumulativeActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualRegularCost">cumulativeActualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualRegularUnits">cumulativeActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeActualUnits">cumulativeActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeAtCompletionCost">cumulativeAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeAtCompletionUnits">cumulativeAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeLimit">cumulativeLimit</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePeriodActualCost">cumulativePeriodActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePeriodActualUnits">cumulativePeriodActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePeriodAtCompletionCost">cumulativePeriodAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePeriodAtCompletionUnits">cumulativePeriodAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePlannedCost">cumulativePlannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativePlannedUnits">cumulativePlannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeRemainingCost">cumulativeRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeRemainingLateCost">cumulativeRemainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeRemainingLateUnits">cumulativeRemainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeRemainingUnits">cumulativeRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualCost">cumulativeStaffedActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualOvertimeCost">cumulativeStaffedActualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualOvertimeUnits">cumulativeStaffedActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualRegularCost">cumulativeStaffedActualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualRegularUnits">cumulativeStaffedActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedActualUnits">cumulativeStaffedActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedAtCompletionCost">cumulativeStaffedAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedAtCompletionUnits">cumulativeStaffedAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedPlannedCost">cumulativeStaffedPlannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedPlannedUnits">cumulativeStaffedPlannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedRemainingCost">cumulativeStaffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedRemainingLateCost">cumulativeStaffedRemainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedRemainingLateUnits">cumulativeStaffedRemainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeStaffedRemainingUnits">cumulativeStaffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualCost">cumulativeUnstaffedActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualOvertimeCost">cumulativeUnstaffedActualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualOvertimeUnits">cumulativeUnstaffedActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualRegularCost">cumulativeUnstaffedActualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualRegularUnits">cumulativeUnstaffedActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedActualUnits">cumulativeUnstaffedActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedAtCompletionCost">cumulativeUnstaffedAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedAtCompletionUnits">cumulativeUnstaffedAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedPlannedCost">cumulativeUnstaffedPlannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedPlannedUnits">cumulativeUnstaffedPlannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedRemainingCost">cumulativeUnstaffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedRemainingLateCost">cumulativeUnstaffedRemainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedRemainingLateUnits">cumulativeUnstaffedRemainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#cumulativeUnstaffedRemainingUnits">cumulativeUnstaffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#endDate">endDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#limit">limit</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#periodActualCost">periodActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#periodActualUnits">periodActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#periodAtCompletionCost">periodAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#periodAtCompletionUnits">periodAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#plannedCost">plannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#plannedUnits">plannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#remainingCost">remainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#remainingLateCost">remainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#remainingLateUnits">remainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#remainingUnits">remainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualCost">staffedActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualOvertimeCost">staffedActualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualOvertimeUnits">staffedActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualRegularCost">staffedActualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualRegularUnits">staffedActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedActualUnits">staffedActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedAtCompletionCost">staffedAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedAtCompletionUnits">staffedAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedPlannedCost">staffedPlannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedPlannedUnits">staffedPlannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedRemainingCost">staffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedRemainingLateCost">staffedRemainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedRemainingLateUnits">staffedRemainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#staffedRemainingUnits">staffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualCost">unstaffedActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualOvertimeCost">unstaffedActualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualOvertimeUnits">unstaffedActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualRegularCost">unstaffedActualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualRegularUnits">unstaffedActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedActualUnits">unstaffedActualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedAtCompletionCost">unstaffedAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedAtCompletionUnits">unstaffedAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedPlannedCost">unstaffedPlannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedPlannedUnits">unstaffedPlannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedRemainingCost">unstaffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedRemainingLateCost">unstaffedRemainingLateCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedRemainingLateUnits">unstaffedRemainingLateUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#unstaffedRemainingUnits">unstaffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#Period--">Period</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Gets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualOvertimeUnits--">getActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualRegularCost--">getActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the actualRegularCost property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualRegularUnits--">getActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the actualRegularUnits property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getActualUnits--">getActualUnits</a></span>()</code>
<div class="block">Gets the value of the actualUnits property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getAtCompletionCost--">getAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionCost property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getAtCompletionUnits--">getAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionUnits property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualCost--">getCumulativeActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualOvertimeCost--">getCumulativeActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualOvertimeUnits--">getCumulativeActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualRegularCost--">getCumulativeActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualRegularCost property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualRegularUnits--">getCumulativeActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualRegularUnits property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeActualUnits--">getCumulativeActualUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualUnits property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeAtCompletionCost--">getCumulativeAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionCost property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeAtCompletionUnits--">getCumulativeAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeLimit--">getCumulativeLimit</a></span>()</code>
<div class="block">Gets the value of the cumulativeLimit property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePeriodActualCost--">getCumulativePeriodActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualCost property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePeriodActualUnits--">getCumulativePeriodActualUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualUnits property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePeriodAtCompletionCost--">getCumulativePeriodAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionCost property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePeriodAtCompletionUnits--">getCumulativePeriodAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePlannedCost--">getCumulativePlannedCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedCost property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativePlannedUnits--">getCumulativePlannedUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedUnits property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeRemainingCost--">getCumulativeRemainingCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingCost property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeRemainingLateCost--">getCumulativeRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateCost property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeRemainingLateUnits--">getCumulativeRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeRemainingUnits--">getCumulativeRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingUnits property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualCost--">getCumulativeStaffedActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualCost property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualOvertimeCost--">getCumulativeStaffedActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualOvertimeUnits--">getCumulativeStaffedActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualRegularCost--">getCumulativeStaffedActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualRegularUnits--">getCumulativeStaffedActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedActualUnits--">getCumulativeStaffedActualUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedActualUnits property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedAtCompletionCost--">getCumulativeStaffedAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedAtCompletionUnits--">getCumulativeStaffedAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedPlannedCost--">getCumulativeStaffedPlannedCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedPlannedUnits--">getCumulativeStaffedPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedRemainingCost--">getCumulativeStaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedRemainingLateCost--">getCumulativeStaffedRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedRemainingLateUnits--">getCumulativeStaffedRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeStaffedRemainingUnits--">getCumulativeStaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeStaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualCost--">getCumulativeUnstaffedActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualCost property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualOvertimeCost--">getCumulativeUnstaffedActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualOvertimeUnits--">getCumulativeUnstaffedActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualRegularCost--">getCumulativeUnstaffedActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualRegularUnits--">getCumulativeUnstaffedActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedActualUnits--">getCumulativeUnstaffedActualUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedActualUnits property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedAtCompletionCost--">getCumulativeUnstaffedAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedAtCompletionUnits--">getCumulativeUnstaffedAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedPlannedCost--">getCumulativeUnstaffedPlannedCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedPlannedUnits--">getCumulativeUnstaffedPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedRemainingCost--">getCumulativeUnstaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedRemainingLateCost--">getCumulativeUnstaffedRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedRemainingLateUnits--">getCumulativeUnstaffedRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getCumulativeUnstaffedRemainingUnits--">getCumulativeUnstaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getEndDate--">getEndDate</a></span>()</code>
<div class="block">Gets the value of the endDate property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getLimit--">getLimit</a></span>()</code>
<div class="block">Gets the value of the limit property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPeriodActualCost--">getPeriodActualCost</a></span>()</code>
<div class="block">Gets the value of the periodActualCost property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPeriodActualUnits--">getPeriodActualUnits</a></span>()</code>
<div class="block">Gets the value of the periodActualUnits property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPeriodAtCompletionCost--">getPeriodAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionCost property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPeriodAtCompletionUnits--">getPeriodAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPlannedCost--">getPlannedCost</a></span>()</code>
<div class="block">Gets the value of the plannedCost property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getPlannedUnits--">getPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the plannedUnits property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Gets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getRemainingLateCost--">getRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateCost property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getRemainingLateUnits--">getRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLateUnits property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getRemainingUnits--">getRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the remainingUnits property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualCost--">getStaffedActualCost</a></span>()</code>
<div class="block">Gets the value of the staffedActualCost property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualOvertimeCost--">getStaffedActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the staffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualOvertimeUnits--">getStaffedActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the staffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualRegularCost--">getStaffedActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the staffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualRegularUnits--">getStaffedActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the staffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedActualUnits--">getStaffedActualUnits</a></span>()</code>
<div class="block">Gets the value of the staffedActualUnits property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedAtCompletionCost--">getStaffedAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the staffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedAtCompletionUnits--">getStaffedAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the staffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedPlannedCost--">getStaffedPlannedCost</a></span>()</code>
<div class="block">Gets the value of the staffedPlannedCost property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedPlannedUnits--">getStaffedPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the staffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedRemainingCost--">getStaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingCost property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedRemainingLateCost--">getStaffedRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedRemainingLateUnits--">getStaffedRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStaffedRemainingUnits--">getStaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualCost--">getUnstaffedActualCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualCost property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualOvertimeCost--">getUnstaffedActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualOvertimeUnits--">getUnstaffedActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualRegularCost--">getUnstaffedActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualRegularUnits--">getUnstaffedActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedActualUnits--">getUnstaffedActualUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedActualUnits property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedAtCompletionCost--">getUnstaffedAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedAtCompletionUnits--">getUnstaffedAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedPlannedCost--">getUnstaffedPlannedCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedPlannedUnits--">getUnstaffedPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedRemainingCost--">getUnstaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedRemainingLateCost--">getUnstaffedRemainingLateCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedRemainingLateUnits--">getUnstaffedRemainingLateUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#getUnstaffedRemainingUnits--">getUnstaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualCost-java.lang.Double-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualOvertimeCost-java.lang.Double-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualOvertimeUnits-java.lang.Double-">setActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualRegularCost-java.lang.Double-">setActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualRegularCost property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualRegularUnits-java.lang.Double-">setActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualRegularUnits property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setActualUnits-java.lang.Double-">setActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualUnits property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setAtCompletionCost-java.lang.Double-">setAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionCost property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setAtCompletionUnits-java.lang.Double-">setAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionUnits property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualCost-java.lang.Double-">setCumulativeActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualOvertimeCost-java.lang.Double-">setCumulativeActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualOvertimeUnits-java.lang.Double-">setCumulativeActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualRegularCost-java.lang.Double-">setCumulativeActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualRegularCost property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualRegularUnits-java.lang.Double-">setCumulativeActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualRegularUnits property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeActualUnits-java.lang.Double-">setCumulativeActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualUnits property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeAtCompletionCost-java.lang.Double-">setCumulativeAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionCost property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeAtCompletionUnits-java.lang.Double-">setCumulativeAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeLimit-java.lang.Double-">setCumulativeLimit</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeLimit property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePeriodActualCost-java.lang.Double-">setCumulativePeriodActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualCost property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePeriodActualUnits-java.lang.Double-">setCumulativePeriodActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualUnits property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePeriodAtCompletionCost-java.lang.Double-">setCumulativePeriodAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionCost property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePeriodAtCompletionUnits-java.lang.Double-">setCumulativePeriodAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePlannedCost-java.lang.Double-">setCumulativePlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedCost property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativePlannedUnits-java.lang.Double-">setCumulativePlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedUnits property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeRemainingCost-java.lang.Double-">setCumulativeRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingCost property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeRemainingLateCost-java.lang.Double-">setCumulativeRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateCost property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeRemainingLateUnits-java.lang.Double-">setCumulativeRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeRemainingUnits-java.lang.Double-">setCumulativeRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingUnits property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualCost-java.lang.Double-">setCumulativeStaffedActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualCost property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualOvertimeCost-java.lang.Double-">setCumulativeStaffedActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualOvertimeUnits-java.lang.Double-">setCumulativeStaffedActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualRegularCost-java.lang.Double-">setCumulativeStaffedActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualRegularUnits-java.lang.Double-">setCumulativeStaffedActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedActualUnits-java.lang.Double-">setCumulativeStaffedActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedActualUnits property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedAtCompletionCost-java.lang.Double-">setCumulativeStaffedAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedAtCompletionUnits-java.lang.Double-">setCumulativeStaffedAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedPlannedCost-java.lang.Double-">setCumulativeStaffedPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedPlannedUnits-java.lang.Double-">setCumulativeStaffedPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedRemainingCost-java.lang.Double-">setCumulativeStaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedRemainingLateCost-java.lang.Double-">setCumulativeStaffedRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedRemainingLateUnits-java.lang.Double-">setCumulativeStaffedRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeStaffedRemainingUnits-java.lang.Double-">setCumulativeStaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeStaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualCost-java.lang.Double-">setCumulativeUnstaffedActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualCost property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualOvertimeCost-java.lang.Double-">setCumulativeUnstaffedActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualOvertimeUnits-java.lang.Double-">setCumulativeUnstaffedActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualRegularCost-java.lang.Double-">setCumulativeUnstaffedActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualRegularUnits-java.lang.Double-">setCumulativeUnstaffedActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedActualUnits-java.lang.Double-">setCumulativeUnstaffedActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedActualUnits property.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedAtCompletionCost-java.lang.Double-">setCumulativeUnstaffedAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedAtCompletionUnits-java.lang.Double-">setCumulativeUnstaffedAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedPlannedCost-java.lang.Double-">setCumulativeUnstaffedPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedPlannedUnits-java.lang.Double-">setCumulativeUnstaffedPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedRemainingCost-java.lang.Double-">setCumulativeUnstaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedRemainingLateCost-java.lang.Double-">setCumulativeUnstaffedRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedRemainingLateUnits-java.lang.Double-">setCumulativeUnstaffedRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setCumulativeUnstaffedRemainingUnits-java.lang.Double-">setCumulativeUnstaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setEndDate-java.time.LocalDateTime-">setEndDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the endDate property.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setLimit-java.lang.Double-">setLimit</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the limit property.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPeriodActualCost-java.lang.Double-">setPeriodActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualCost property.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPeriodActualUnits-java.lang.Double-">setPeriodActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualUnits property.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPeriodAtCompletionCost-java.lang.Double-">setPeriodAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionCost property.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPeriodAtCompletionUnits-java.lang.Double-">setPeriodAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPlannedCost-java.lang.Double-">setPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedCost property.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setPlannedUnits-java.lang.Double-">setPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedUnits property.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setRemainingCost-java.lang.Double-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setRemainingLateCost-java.lang.Double-">setRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateCost property.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setRemainingLateUnits-java.lang.Double-">setRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateUnits property.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setRemainingUnits-java.lang.Double-">setRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingUnits property.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualCost-java.lang.Double-">setStaffedActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualCost property.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualOvertimeCost-java.lang.Double-">setStaffedActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualOvertimeUnits-java.lang.Double-">setStaffedActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualRegularCost-java.lang.Double-">setStaffedActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualRegularUnits-java.lang.Double-">setStaffedActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedActualUnits-java.lang.Double-">setStaffedActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedActualUnits property.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedAtCompletionCost-java.lang.Double-">setStaffedAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedAtCompletionUnits-java.lang.Double-">setStaffedAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedPlannedCost-java.lang.Double-">setStaffedPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedPlannedCost property.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedPlannedUnits-java.lang.Double-">setStaffedPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedRemainingCost-java.lang.Double-">setStaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingCost property.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedRemainingLateCost-java.lang.Double-">setStaffedRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedRemainingLateUnits-java.lang.Double-">setStaffedRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStaffedRemainingUnits-java.lang.Double-">setStaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualCost-java.lang.Double-">setUnstaffedActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualCost property.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualOvertimeCost-java.lang.Double-">setUnstaffedActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualOvertimeCost property.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualOvertimeUnits-java.lang.Double-">setUnstaffedActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualRegularCost-java.lang.Double-">setUnstaffedActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualRegularCost property.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualRegularUnits-java.lang.Double-">setUnstaffedActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualRegularUnits property.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedActualUnits-java.lang.Double-">setUnstaffedActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedActualUnits property.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedAtCompletionCost-java.lang.Double-">setUnstaffedAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedAtCompletionCost property.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedAtCompletionUnits-java.lang.Double-">setUnstaffedAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedPlannedCost-java.lang.Double-">setUnstaffedPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedPlannedCost property.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedPlannedUnits-java.lang.Double-">setUnstaffedPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedPlannedUnits property.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedRemainingCost-java.lang.Double-">setUnstaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedRemainingLateCost-java.lang.Double-">setUnstaffedRemainingLateCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingLateCost property.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedRemainingLateUnits-java.lang.Double-">setUnstaffedRemainingLateUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingLateUnits property.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html#setUnstaffedRemainingUnits-java.lang.Double-">setUnstaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingUnits property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="endDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> endDate</pre>
</li>
</ul>
<a name="actualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualOvertimeUnits</pre>
</li>
</ul>
<a name="cumulativeActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualOvertimeUnits</pre>
</li>
</ul>
<a name="actualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualRegularUnits</pre>
</li>
</ul>
<a name="cumulativeActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualRegularUnits</pre>
</li>
</ul>
<a name="actualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualUnits</pre>
</li>
</ul>
<a name="cumulativeActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualUnits</pre>
</li>
</ul>
<a name="atCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionUnits</pre>
</li>
</ul>
<a name="cumulativeAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionUnits</pre>
</li>
</ul>
<a name="limit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>limit</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> limit</pre>
</li>
</ul>
<a name="cumulativeLimit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeLimit</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeLimit</pre>
</li>
</ul>
<a name="periodActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualUnits</pre>
</li>
</ul>
<a name="cumulativePeriodActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualUnits</pre>
</li>
</ul>
<a name="periodAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionUnits</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionUnits</pre>
</li>
</ul>
<a name="plannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedUnits</pre>
</li>
</ul>
<a name="cumulativePlannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedUnits</pre>
</li>
</ul>
<a name="remainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateUnits</pre>
</li>
</ul>
<a name="remainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingUnits</pre>
</li>
</ul>
<a name="staffedActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualOvertimeUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualOvertimeUnits</pre>
</li>
</ul>
<a name="staffedActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualRegularUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualRegularUnits</pre>
</li>
</ul>
<a name="staffedActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualUnits</pre>
</li>
</ul>
<a name="staffedAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedAtCompletionUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedAtCompletionUnits</pre>
</li>
</ul>
<a name="staffedPlannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedPlannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedPlannedUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedPlannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedPlannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedPlannedUnits</pre>
</li>
</ul>
<a name="staffedRemainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingLateUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedRemainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedRemainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedRemainingLateUnits</pre>
</li>
</ul>
<a name="staffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingUnits</pre>
</li>
</ul>
<a name="cumulativeStaffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedRemainingUnits</pre>
</li>
</ul>
<a name="unstaffedActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualOvertimeUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualOvertimeUnits</pre>
</li>
</ul>
<a name="unstaffedActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualRegularUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualRegularUnits</pre>
</li>
</ul>
<a name="unstaffedActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualUnits</pre>
</li>
</ul>
<a name="unstaffedAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedAtCompletionUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedAtCompletionUnits</pre>
</li>
</ul>
<a name="unstaffedPlannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedPlannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedPlannedUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedPlannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedPlannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedPlannedUnits</pre>
</li>
</ul>
<a name="unstaffedRemainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingLateUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedRemainingLateUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedRemainingLateUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedRemainingLateUnits</pre>
</li>
</ul>
<a name="unstaffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingUnits</pre>
</li>
</ul>
<a name="cumulativeUnstaffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedRemainingUnits</pre>
</li>
</ul>
<a name="actualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualCost</pre>
</li>
</ul>
<a name="cumulativeActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualCost</pre>
</li>
</ul>
<a name="actualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualOvertimeCost</pre>
</li>
</ul>
<a name="cumulativeActualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualOvertimeCost</pre>
</li>
</ul>
<a name="actualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualRegularCost</pre>
</li>
</ul>
<a name="cumulativeActualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualRegularCost</pre>
</li>
</ul>
<a name="atCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionCost</pre>
</li>
</ul>
<a name="periodActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualCost</pre>
</li>
</ul>
<a name="periodAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionCost</pre>
</li>
</ul>
<a name="plannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedCost</pre>
</li>
</ul>
<a name="cumulativePlannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedCost</pre>
</li>
</ul>
<a name="remainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingCost</pre>
</li>
</ul>
<a name="cumulativeRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingCost</pre>
</li>
</ul>
<a name="remainingLateCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateCost</pre>
</li>
</ul>
<a name="staffedActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualCost</pre>
</li>
</ul>
<a name="cumulativeStaffedActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualCost</pre>
</li>
</ul>
<a name="staffedActualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualOvertimeCost</pre>
</li>
</ul>
<a name="cumulativeStaffedActualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualOvertimeCost</pre>
</li>
</ul>
<a name="staffedActualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedActualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedActualRegularCost</pre>
</li>
</ul>
<a name="cumulativeStaffedActualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedActualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedActualRegularCost</pre>
</li>
</ul>
<a name="staffedAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativeStaffedAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedAtCompletionCost</pre>
</li>
</ul>
<a name="staffedPlannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedPlannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedPlannedCost</pre>
</li>
</ul>
<a name="cumulativeStaffedPlannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedPlannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedPlannedCost</pre>
</li>
</ul>
<a name="staffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingCost</pre>
</li>
</ul>
<a name="cumulativeStaffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedRemainingCost</pre>
</li>
</ul>
<a name="staffedRemainingLateCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingLateCost</pre>
</li>
</ul>
<a name="cumulativeStaffedRemainingLateCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeStaffedRemainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeStaffedRemainingLateCost</pre>
</li>
</ul>
<a name="unstaffedActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualCost</pre>
</li>
</ul>
<a name="unstaffedActualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualOvertimeCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualOvertimeCost</pre>
</li>
</ul>
<a name="unstaffedActualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedActualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedActualRegularCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedActualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedActualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedActualRegularCost</pre>
</li>
</ul>
<a name="unstaffedAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedAtCompletionCost</pre>
</li>
</ul>
<a name="unstaffedPlannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedPlannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedPlannedCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedPlannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedPlannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedPlannedCost</pre>
</li>
</ul>
<a name="unstaffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeUnstaffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedRemainingCost</pre>
</li>
</ul>
<a name="unstaffedRemainingLateCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingLateCost</pre>
</li>
</ul>
<a name="cumulativeUnstaffedRemainingLateCost">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cumulativeUnstaffedRemainingLateCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeUnstaffedRemainingLateCost</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Period--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Period</h4>
<pre>public&nbsp;Period()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEndDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEndDate()</pre>
<div class="block">Gets the value of the endDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEndDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndDate</h4>
<pre>public&nbsp;void&nbsp;setEndDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the endDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualOvertimeUnits()</pre>
<div class="block">Gets the value of the actualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualOvertimeUnits()</pre>
<div class="block">Gets the value of the cumulativeActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualRegularUnits()</pre>
<div class="block">Gets the value of the actualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualRegularUnits()</pre>
<div class="block">Gets the value of the cumulativeActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualUnits()</pre>
<div class="block">Gets the value of the actualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualUnits</h4>
<pre>public&nbsp;void&nbsp;setActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualUnits()</pre>
<div class="block">Gets the value of the cumulativeActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionUnits()</pre>
<div class="block">Gets the value of the atCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionUnits()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLimit</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getLimit()</pre>
<div class="block">Gets the value of the limit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLimit-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLimit</h4>
<pre>public&nbsp;void&nbsp;setLimit(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the limit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeLimit</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeLimit()</pre>
<div class="block">Gets the value of the cumulativeLimit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeLimit-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeLimit</h4>
<pre>public&nbsp;void&nbsp;setCumulativeLimit(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeLimit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualUnits()</pre>
<div class="block">Gets the value of the periodActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionUnits()</pre>
<div class="block">Gets the value of the periodAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedUnits()</pre>
<div class="block">Gets the value of the plannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateUnits()</pre>
<div class="block">Gets the value of the remainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingUnits()</pre>
<div class="block">Gets the value of the remainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualOvertimeUnits()</pre>
<div class="block">Gets the value of the staffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualOvertimeUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualRegularUnits()</pre>
<div class="block">Gets the value of the staffedActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualRegularUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualUnits()</pre>
<div class="block">Gets the value of the staffedActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedAtCompletionUnits()</pre>
<div class="block">Gets the value of the staffedAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedAtCompletionUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedPlannedUnits()</pre>
<div class="block">Gets the value of the staffedPlannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedPlannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedPlannedUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedPlannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedPlannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingLateUnits()</pre>
<div class="block">Gets the value of the staffedRemainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedRemainingLateUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingUnits()</pre>
<div class="block">Gets the value of the staffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedRemainingUnits()</pre>
<div class="block">Gets the value of the cumulativeStaffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualOvertimeUnits()</pre>
<div class="block">Gets the value of the unstaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualOvertimeUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualRegularUnits()</pre>
<div class="block">Gets the value of the unstaffedActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualRegularUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualUnits()</pre>
<div class="block">Gets the value of the unstaffedActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedAtCompletionUnits()</pre>
<div class="block">Gets the value of the unstaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedAtCompletionUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedPlannedUnits()</pre>
<div class="block">Gets the value of the unstaffedPlannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedPlannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedPlannedUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedPlannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedPlannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingLateUnits()</pre>
<div class="block">Gets the value of the unstaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedRemainingLateUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedRemainingLateUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedRemainingLateUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedRemainingLateUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedRemainingLateUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedRemainingLateUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingLateUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingUnits()</pre>
<div class="block">Gets the value of the unstaffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedRemainingUnits()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualCost()</pre>
<div class="block">Gets the value of the actualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualCost()</pre>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualOvertimeCost()</pre>
<div class="block">Gets the value of the cumulativeActualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualRegularCost()</pre>
<div class="block">Gets the value of the actualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualRegularCost()</pre>
<div class="block">Gets the value of the cumulativeActualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionCost()</pre>
<div class="block">Gets the value of the atCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualCost()</pre>
<div class="block">Gets the value of the periodActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionCost()</pre>
<div class="block">Gets the value of the periodAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedCost()</pre>
<div class="block">Gets the value of the plannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedCost()</pre>
<div class="block">Gets the value of the cumulativePlannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingCost()</pre>
<div class="block">Gets the value of the remainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateCost()</pre>
<div class="block">Gets the value of the remainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualCost()</pre>
<div class="block">Gets the value of the staffedActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualOvertimeCost()</pre>
<div class="block">Gets the value of the staffedActualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualOvertimeCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedActualRegularCost()</pre>
<div class="block">Gets the value of the staffedActualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedActualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedActualRegularCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedActualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedActualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedAtCompletionCost()</pre>
<div class="block">Gets the value of the staffedAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedPlannedCost()</pre>
<div class="block">Gets the value of the staffedPlannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedPlannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedPlannedCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedPlannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedPlannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingCost()</pre>
<div class="block">Gets the value of the staffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedRemainingCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingLateCost()</pre>
<div class="block">Gets the value of the staffedRemainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeStaffedRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeStaffedRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeStaffedRemainingLateCost()</pre>
<div class="block">Gets the value of the cumulativeStaffedRemainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeStaffedRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeStaffedRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeStaffedRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeStaffedRemainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualCost()</pre>
<div class="block">Gets the value of the unstaffedActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualOvertimeCost()</pre>
<div class="block">Gets the value of the unstaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualOvertimeCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedActualRegularCost()</pre>
<div class="block">Gets the value of the unstaffedActualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedActualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedActualRegularCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedActualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedActualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedAtCompletionCost()</pre>
<div class="block">Gets the value of the unstaffedAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedPlannedCost()</pre>
<div class="block">Gets the value of the unstaffedPlannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedPlannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedPlannedCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedPlannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedPlannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingCost()</pre>
<div class="block">Gets the value of the unstaffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedRemainingCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeUnstaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingLateCost()</pre>
<div class="block">Gets the value of the unstaffedRemainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeUnstaffedRemainingLateCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeUnstaffedRemainingLateCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeUnstaffedRemainingLateCost()</pre>
<div class="block">Gets the value of the cumulativeUnstaffedRemainingLateCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeUnstaffedRemainingLateCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setCumulativeUnstaffedRemainingLateCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeUnstaffedRemainingLateCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeUnstaffedRemainingLateCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectRoleSpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="ProjectRoleSpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
