<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.mpxj.MpxjEnum (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.mpxj.MpxjEnum (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/MpxjEnum.html" target="_top">Frames</a></li>
<li><a href="MpxjEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.mpxj.MpxjEnum" class="title">Uses of Interface<br>org.mpxj.MpxjEnum</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subinterfaces, and an explanation">
<caption><span>Subinterfaces of <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span></code>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that implement <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></span></code>
<div class="block">This class is used to represent an accrue type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a></span></code>
<div class="block">Instances of this type represent Assignment fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a></span></code>
<div class="block">Enumeration representing booking types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ConstraintField.html" title="enum in org.mpxj">ConstraintField</a></span></code>
<div class="block">Instances of this type represent constraint fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a></span></code>
<div class="block">This class is used to represent a constraint type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a></span></code>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldValueDataType.html" title="enum in org.mpxj">CustomFieldValueDataType</a></span></code>
<div class="block">Enumeration used  by custom field value items to represent their data type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a></span></code>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a></span></code>
<div class="block">Instances of this class represent enumerated date order values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/DayType.html" title="enum in org.mpxj">DayType</a></span></code>
<div class="block">This class is used to represent the day type used by the project calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a></span></code>
<div class="block">Instances of this class represent enumerated earned value method values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a></span></code>
<div class="block">Instances of this class represent enumerated file version values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a></span></code>
<div class="block">Instances of this class represent enumerated date format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectField.html" title="enum in org.mpxj">ProjectField</a></span></code>
<div class="block">Instances of this type represent project properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a></span></code>
<div class="block">Instances of this class represent enumerated time format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RecurrenceType.html" title="enum in org.mpxj">RecurrenceType</a></span></code>
<div class="block">Represents the recurrence type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RelationType.html" title="enum in org.mpxj">RelationType</a></span></code>
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceField.html" title="enum in org.mpxj">ResourceField</a></span></code>
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceRequestType.html" title="enum in org.mpxj">ResourceRequestType</a></span></code>
<div class="block">Instances of this class represent enumerated resource request type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></span></code>
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a></span></code>
<div class="block">Instances of this class represent enumerated schedule from values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a></span></code>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a></span></code>
<div class="block">Represents task mode values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TestOperator.html" title="enum in org.mpxj">TestOperator</a></span></code>
<div class="block">This class represents the set of operators used to perform a test
 between two or more operands.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></span></code>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UserDefinedField.html" title="class in org.mpxj">UserDefinedField</a></span></code>
<div class="block">Represents a user defined field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ViewType.html" title="enum in org.mpxj">ViewType</a></span></code>
<div class="block">This class represents the enumeration of the valid types of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a></span></code>
<div class="block">Instances of this class represent enumerated work group values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a> in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> that implement <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/BackgroundPattern.html" title="enum in org.mpxj.mpp">BackgroundPattern</a></span></code>
<div class="block">Represents the pattern used to fill a group.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp">ChartPattern</a></span></code>
<div class="block">Represents the pattern used to fill the middle section of a Gantt bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ColorType.html" title="enum in org.mpxj.mpp">ColorType</a></span></code>
<div class="block">This enum represents the colors used by Microsoft Project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></span></code>
<div class="block">Enumeration representing the formats which may be shown on a Gantt chart timescale.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp">GanttBarMiddleShape</a></span></code>
<div class="block">Represents the shape type used to draw the middle section of a Gantt bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a></span></code>
<div class="block">Represents the criteria used to define when a Gantt bar is displayed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStartEndShape.html" title="enum in org.mpxj.mpp">GanttBarStartEndShape</a></span></code>
<div class="block">Represents the shape at the start end end of a Gantt bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStartEndType.html" title="enum in org.mpxj.mpp">GanttBarStartEndType</a></span></code>
<div class="block">Represents the style of the start and end sections of a Gantt bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp">Interval</a></span></code>
<div class="block">This class represents daily, weekly or monthly time intervals.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a></span></code>
<div class="block">This class represents the grid line styles used by Microsoft Project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp">LinkStyle</a></span></code>
<div class="block">Class representing how links are drawn on a Gantt chart.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp">NonWorkingTimeStyle</a></span></code>
<div class="block">Class representing how non-working time is shown on a Gantt chart.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp">ProgressLineDay</a></span></code>
<div class="block">Instances of this class represent enumerated day values used as to
 define when progress lines are drawn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/TimescaleAlignment.html" title="enum in org.mpxj.mpp">TimescaleAlignment</a></span></code>
<div class="block">Class representing the label alignment on a Gantt chart timescale.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/TimescaleFormat.html" title="enum in org.mpxj.mpp">TimescaleFormat</a></span></code>
<div class="block">Enumeration representing the formats which may be shown on a Gantt chart timescale.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/TimescaleUnits.html" title="enum in org.mpxj.mpp">TimescaleUnits</a></span></code>
<div class="block">Class representing the units which may be shown on a Gantt chart timescale.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/MpxjEnum.html" target="_top">Frames</a></li>
<li><a href="MpxjEnum.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
