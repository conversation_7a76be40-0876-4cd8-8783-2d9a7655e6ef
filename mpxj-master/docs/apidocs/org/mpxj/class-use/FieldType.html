<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.mpxj.FieldType (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.mpxj.FieldType (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/FieldType.html" target="_top">Frames</a></li>
<li><a href="FieldType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.mpxj.FieldType" class="title">Uses of Interface<br>org.mpxj.FieldType</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.listener">org.mpxj.listener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera">org.mpxj.primavera</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that implement <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/AssignmentField.html" title="enum in org.mpxj">AssignmentField</a></span></code>
<div class="block">Instances of this type represent Assignment fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ConstraintField.html" title="enum in org.mpxj">ConstraintField</a></span></code>
<div class="block">Instances of this type represent constraint fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectField.html" title="enum in org.mpxj">ProjectField</a></span></code>
<div class="block">Instances of this type represent project properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceField.html" title="enum in org.mpxj">ResourceField</a></span></code>
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a></span></code>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UserDefinedField.html" title="class in org.mpxj">UserDefinedField</a></span></code>
<div class="block">Represents a user defined field.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GroupClause.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/GroupClause.html#getField--">getField</a></span>()</code>
<div class="block">Retrieve the grouping field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">Column.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Column.html#getFieldType--">getFieldType</a></span>()</code>
<div class="block">Retrieves the type data displayed in the column.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">CustomField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomField.html#getFieldType--">getFieldType</a></span>()</code>
<div class="block">Retrieve the field type represented by this instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GraphicalIndicator.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/GraphicalIndicator.html#getFieldType--">getFieldType</a></span>()</code>
<div class="block">Retrieves the field type to which this indicator applies.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">CustomFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldContainer.html#getFieldTypeByAlias-org.mpxj.FieldTypeClass-java.lang.String-">getFieldTypeByAlias</a></span>(<a href="../../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a>&nbsp;typeClass,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve a field type from a particular entity using its alias.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignmentContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentContainer.html#getFieldTypeByAlias-java.lang.String-">getFieldTypeByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the type of a field by its alias.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">ResourceContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceContainer.html#getFieldTypeByAlias-java.lang.String-">getFieldTypeByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the type of a field by its alias.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaskContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskContainer.html#getFieldTypeByAlias-java.lang.String-">getFieldTypeByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the type of a field by its alias.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GenericCriteria.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/GenericCriteria.html#getLeftValue--">getLeftValue</a></span>()</code>
<div class="block">Retrieves the LHS of the expression.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">DataLink.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/DataLink.html#getSourceField--">getSourceField</a></span>()</code>
<div class="block">Retrieve the source field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">DataLink.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/DataLink.html#getTargetField--">getTargetField</a></span>()</code>
<div class="block">Retrieve the target field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">AssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AssignmentField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">ConstraintField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ConstraintField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldType.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/FieldType.html#getUnitsType--">getUnitsType</a></span>()</code>
<div class="block">Retrieve the associated units field, if any.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">ResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">TaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/UserDefinedField.html#getUnitsType--">getUnitsType</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return types with arguments of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">A convenience method used to retrieve a set of FieldType instances representing
 all populated fields in the project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectProperties.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectProperties.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of populated fields for this project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignmentContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentContainer.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of populated fields for this project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ResourceContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceContainer.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of populated fields for this project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">TaskContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskContainer.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of populated fields for this project.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/CustomField.html" title="class in org.mpxj">CustomField</a></code></td>
<td class="colLast"><span class="typeNameLabel">CustomFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldContainer.html#add-org.mpxj.FieldType-">add</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Add a new custom field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractFieldContainer.html#get-org.mpxj.FieldType-">get</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/CustomField.html" title="class in org.mpxj">CustomField</a></code></td>
<td class="colLast"><span class="typeNameLabel">CustomFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldContainer.html#get-org.mpxj.FieldType-">get</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Retrieve configuration details for a given field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/FieldContainer.html#get-org.mpxj.FieldType-">get</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Retrieve a field value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">Resource.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Resource.html#getAlwaysCalculatedField-org.mpxj.FieldType-">getAlwaysCalculatedField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractFieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/FieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Retrieve a field value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/function/Function.html?is-external=true" title="class or interface in java.util.function">Function</a>&lt;<a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Resource.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Resource.html#getCalculationMethod-org.mpxj.FieldType-">getCalculationMethod</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/CustomField.html" title="class in org.mpxj">CustomField</a></code></td>
<td class="colLast"><span class="typeNameLabel">CustomFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldContainer.html#getOrCreate-org.mpxj.FieldType-">getOrCreate</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Retrieve configuration details for a given field,
 create a new CustomField entry if one does not exist.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">Resource.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Resource.html#handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">handleFieldChange</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</code>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignment.html#handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">handleFieldChange</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</code>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">CustomFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomFieldContainer.html#remove-org.mpxj.FieldType-">remove</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Remove a custom field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractFieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractFieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FieldContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/FieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Set a field value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GroupClause.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/GroupClause.html#setField-org.mpxj.FieldType-">setField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Set the grouping field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Column.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Column.html#setFieldType-org.mpxj.FieldType-">setFieldType</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</code>
<div class="block">Sets the type data displayed in the column.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GenericCriteria.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/GenericCriteria.html#setLeftValue-org.mpxj.FieldType-">setLeftValue</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Sets the LHS of the expression.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DataLink.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/DataLink.html#setSourceField-org.mpxj.FieldType-">setSourceField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;sourceField)</code>
<div class="block">Set the source field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DataLink.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/DataLink.html#setTargetField-org.mpxj.FieldType-">setTargetField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;targetField)</code>
<div class="block">Set the target field.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CustomField.html#CustomField-org.mpxj.FieldType-org.mpxj.CustomFieldContainer-">CustomField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
           <a href="../../../org/mpxj/CustomFieldContainer.html" title="class in org.mpxj">CustomFieldContainer</a>&nbsp;parent)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with type parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/PopulatedFields.html" title="class in org.mpxj.common">PopulatedFields</a>&lt;E extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;E&gt; &amp; <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,T extends <a href="../../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>&gt;</span></code>
<div class="block">Given a collection of objects containing fields, return a set representing
 all of the fields which have a non-null value in any of the objects.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with type parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FieldLists.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldLists.html#CUSTOM_FIELDS">CUSTOM_FIELDS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FieldLists.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldLists.html#CUSTOM_FIELDS_SET">CUSTOM_FIELDS_SET</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> that return <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPConstraintField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPConstraintField.html#getInstance-int-">getInstance</a></span>(int&nbsp;value)</code>
<div class="block">Retrieve an instance of the ConstraintField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;fieldID)</code>
<div class="block">Retrieve a FieldType instance based on an ID value from
 an MPP9, MPP12 or MPP14 file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPAssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPAssignmentField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the AssignmentField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPProjectField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the ProjectField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPResourceField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the ResourceField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPTaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPTaskField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the TaskField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;fieldID,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve a FieldType instance based on an ID value from
 an MPP9, MPP12 or MPP14 file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPAssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPAssignmentField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the AssignmentField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPProjectField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the ProjectField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPResourceField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the ResourceField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPTaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPTaskField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the TaskField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#mapTextFields-org.mpxj.FieldType-">mapTextFields</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">In some circumstances MS Project refers to the text version of a field (e.g.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> that return types with arguments of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PopulatedFields.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/PopulatedFields.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of fields populated across the collection of objects.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#getFieldID-org.mpxj.FieldType-">getFieldID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</code>
<div class="block">Retrieve an MPP9/MPP12 field ID based on an MPXJ FieldType instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">MPPAssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPAssignmentField.html#getID-org.mpxj.FieldType-">getID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Retrieve the ID of a field, as used by MS Project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">MPPConstraintField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPConstraintField.html#getID-org.mpxj.FieldType-">getID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Retrieve the ID of a field, as used by MS Project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">MPPProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPProjectField.html#getID-org.mpxj.FieldType-">getID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Retrieve the ID of a field, as used by MS Project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">MPPResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPResourceField.html#getID-org.mpxj.FieldType-">getID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Retrieve the ID of a field, as used by MS Project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><span class="typeNameLabel">MPPTaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPTaskField.html#getID-org.mpxj.FieldType-">getID</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;value)</code>
<div class="block">Retrieve the ID of a field, as used by MS Project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#mapTextFields-org.mpxj.FieldType-">mapTextFields</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">In some circumstances MS Project refers to the text version of a field (e.g.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#valueIsNotDefault-org.mpxj.FieldType-java.lang.Object-">valueIsNotDefault</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Determines if this value is the default value for the given field type.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.listener">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/listener/package-summary.html">org.mpxj.listener</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/listener/package-summary.html">org.mpxj.listener</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FieldListener.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/FieldListener.html#fieldChange-org.mpxj.FieldContainer-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">fieldChange</a></span>(<a href="../../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>&nbsp;container,
           <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</code>
<div class="block">Called when a field value is changed.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with type parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">GanttChartView.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_autoFiltersByType">m_autoFiltersByType</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> that return <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#generateMapping-org.mpxj.FieldType-">generateMapping</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;source)</code>
<div class="block">Generate a mapping for a source field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getBottomText--">getBottomText</a></span>()</code>
<div class="block">Retrieve the text appearing at the bottom of the bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">TableFontStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/TableFontStyle.html#getFieldType--">getFieldType</a></span>()</code>
<div class="block">Retrieve the field type of the column to which this style applies.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected abstract <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">CriteriaReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/CriteriaReader.html#getFieldType-byte:A-">getFieldType</a></span>(byte[]&nbsp;block)</code>
<div class="block">Retrieves a field type value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FilterCriteriaReader12.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/FilterCriteriaReader12.html#getFieldType-byte:A-">getFieldType</a></span>(byte[]&nbsp;block)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FilterCriteriaReader14.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/FilterCriteriaReader14.html#getFieldType-byte:A-">getFieldType</a></span>(byte[]&nbsp;block)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FilterCriteriaReader9.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/FilterCriteriaReader9.html#getFieldType-byte:A-">getFieldType</a></span>(byte[]&nbsp;block)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyle.html#getFromField--">getFromField</a></span>()</code>
<div class="block">Retrieve the field used to determine the start date of this bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getInsideText--">getInsideText</a></span>()</code>
<div class="block">Retrieve the text appearing inside the Gantt bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getLeftText--">getLeftText</a></span>()</code>
<div class="block">Retrieve the text appearing to the left of the bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getRightText--">getRightText</a></span>()</code>
<div class="block">Retrieve the text appearing to the right of the bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#getSource-org.mpxj.FieldType-">getSource</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;target)</code>
<div class="block">Given a target field, determine which field is being used as its source.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#getTarget-org.mpxj.FieldType-">getTarget</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;source)</code>
<div class="block">Given a source field, return the target field it should be mapped to.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyle.html#getToField--">getToField</a></span>()</code>
<div class="block">Retrieve the field used to determine the end date of this bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getTopText--">getTopText</a></span>()</code>
<div class="block">Retrieve the text which appears above the bar.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#generateMapping-org.mpxj.FieldType-">generateMapping</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;source)</code>
<div class="block">Generate a mapping for a source field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttChartView.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getAutoFilterByType-org.mpxj.FieldType-">getAutoFilterByType</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</code>
<div class="block">Retrieves the auto filter definition associated with an
 individual column.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#getSource-org.mpxj.FieldType-">getSource</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;target)</code>
<div class="block">Given a target field, determine which field is being used as its source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserDefinedFieldMap.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#getTarget-org.mpxj.FieldType-">getTarget</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;source)</code>
<div class="block">Given a source field, return the target field it should be mapped to.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setBottomText-org.mpxj.FieldType-">setBottomText</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the text appearing at the bottom of the bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyle.html#setFromField-org.mpxj.FieldType-">setFromField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Set the field used to determine the start date of this bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setInsideText-org.mpxj.FieldType-">setInsideText</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the text appearing inside the Gantt bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setLeftText-org.mpxj.FieldType-">setLeftText</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the text appearing to the left of the bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setRightText-org.mpxj.FieldType-">setRightText</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the text appearing to the right of the bar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyle.html#setToField-org.mpxj.FieldType-">setToField</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the field used to determine the end date of this bar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarCommonStyle.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setTopText-org.mpxj.FieldType-">setTopText</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Sets the top text.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with type arguments of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/GenericCriteria.html" title="class in org.mpxj">GenericCriteria</a></code></td>
<td class="colLast"><span class="typeNameLabel">CriteriaReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/CriteriaReader.html#process-org.mpxj.ProjectFile-byte:A-int-int-java.util.List-java.util.List-boolean:A-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       byte[]&nbsp;data,
       int&nbsp;dataOffset,
       int&nbsp;entryOffset,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/GenericCriteriaPrompt.html" title="class in org.mpxj">GenericCriteriaPrompt</a>&gt;&nbsp;prompts,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;&nbsp;fields,
       boolean[]&nbsp;criteriaType)</code>
<div class="block">Main entry point to read criteria data.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MppBitFlag.html#MppBitFlag-org.mpxj.FieldType-int-int-java.lang.Object-java.lang.Object-">MppBitFlag</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type,
          int&nbsp;offset,
          int&nbsp;mask,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;zeroValue,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;nonZeroValue)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/TableFontStyle.html#TableFontStyle-int-org.mpxj.FieldType-org.mpxj.mpp.FontBase-boolean-boolean-boolean-boolean-java.awt.Color-java.awt.Color-org.mpxj.mpp.BackgroundPattern-boolean-boolean-boolean-boolean-boolean-boolean-boolean-boolean-">TableFontStyle</a></span>(int&nbsp;rowUniqueID,
              <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;fieldType,
              <a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&nbsp;fontBase,
              boolean&nbsp;italic,
              boolean&nbsp;bold,
              boolean&nbsp;underline,
              boolean&nbsp;strikethrough,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;backgroundColor,
              <a href="../../../org/mpxj/mpp/BackgroundPattern.html" title="enum in org.mpxj.mpp">BackgroundPattern</a>&nbsp;backgroundPattern,
              boolean&nbsp;italicChanged,
              boolean&nbsp;boldChanged,
              boolean&nbsp;underlineChanged,
              boolean&nbsp;strikethroughChanged,
              boolean&nbsp;colorChanged,
              boolean&nbsp;fontChanged,
              boolean&nbsp;backgroundColorChanged,
              boolean&nbsp;backgroundPatternChanged)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with type arguments of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#UserDefinedFieldMap-org.mpxj.ProjectFile-java.util.List-">UserDefinedFieldMap</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;&nbsp;targetFieldList)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> with parameters of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#parseCustomField-org.mpxj.ProjectFile-org.mpxj.FieldContainer-java.lang.String-org.mpxj.FieldType-org.mpxj.TimeUnit-">parseCustomField</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                <a href="../../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>&nbsp;mpx,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;mpxFieldID,
                <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;durationFormat)</code>
<div class="block">Parse a custom field value.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a> in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a> that return types with arguments of type <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#getActivityFieldMap--">getActivityFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#getActivityFieldMap--">getActivityFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#getAssignmentFieldMap--">getAssignmentFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#getAssignmentFieldMap--">getAssignmentFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#getResourceFieldMap--">getResourceFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#getResourceFieldMap--">getResourceFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#getRoleFieldMap--">getRoleFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#getRoleFieldMap--">getRoleFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#getWbsFieldMap--">getWbsFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#getWbsFieldMap--">getWbsFieldMap</a></span>()</code>
<div class="block">Customise the data retrieved by this reader by modifying the contents of this map.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/FieldType.html" target="_top">Frames</a></li>
<li><a href="FieldType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
