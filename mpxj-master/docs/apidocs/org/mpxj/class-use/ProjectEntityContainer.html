<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.mpxj.ProjectEntityContainer (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.mpxj.ProjectEntityContainer (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectEntityContainer.html" target="_top">Frames</a></li>
<li><a href="ProjectEntityContainer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.mpxj.ProjectEntityContainer" class="title">Uses of Class<br>org.mpxj.ProjectEntityContainer</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ActivityCodeContainer.html" title="class in org.mpxj">ActivityCodeContainer</a></span></code>
<div class="block">Container for activity code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CostAccountContainer.html" title="class in org.mpxj">CostAccountContainer</a></span></code>
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CurrencyContainer.html" title="class in org.mpxj">CurrencyContainer</a></span></code>
<div class="block">Represents the currencies available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ExpenseCategoryContainer.html" title="class in org.mpxj">ExpenseCategoryContainer</a></span></code>
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/LocationContainer.html" title="class in org.mpxj">LocationContainer</a></span></code>
<div class="block">Represents the locations available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/NotesTopicContainer.html" title="class in org.mpxj">NotesTopicContainer</a></span></code>
<div class="block">Represents the notes topics available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarContainer.html" title="class in org.mpxj">ProjectCalendarContainer</a></span></code>
<div class="block">Manages the collection of calendars belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCodeContainer.html" title="class in org.mpxj">ProjectCodeContainer</a></span></code>
<div class="block">Container for project code definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectEntityWithIDContainer.html" title="class in org.mpxj">ProjectEntityWithIDContainer</a>&lt;T extends <a href="../../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a> &amp; <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;&gt;</span></code>
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RelationContainer.html" title="class in org.mpxj">RelationContainer</a></span></code>
<div class="block">Represents Relation instances from the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentCodeContainer.html" title="class in org.mpxj">ResourceAssignmentCodeContainer</a></span></code>
<div class="block">Container for assignment code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentContainer.html" title="class in org.mpxj">ResourceAssignmentContainer</a></span></code>
<div class="block">Manages the collection of resource assignments belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceCodeContainer.html" title="class in org.mpxj">ResourceCodeContainer</a></span></code>
<div class="block">Container for resource code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceContainer.html" title="class in org.mpxj">ResourceContainer</a></span></code>
<div class="block">Manages the collection of resources belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RoleCodeContainer.html" title="class in org.mpxj">RoleCodeContainer</a></span></code>
<div class="block">Container for role code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ShiftContainer.html" title="class in org.mpxj">ShiftContainer</a></span></code>
<div class="block">Represents the shifts available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ShiftPeriodContainer.html" title="class in org.mpxj">ShiftPeriodContainer</a></span></code>
<div class="block">Represents the shift periods available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskContainer.html" title="class in org.mpxj">TaskContainer</a></span></code>
<div class="block">Manages the collection of tasks belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UnitOfMeasureContainer.html" title="class in org.mpxj">UnitOfMeasureContainer</a></span></code>
<div class="block">Represents units of measure available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContourContainer.html" title="class in org.mpxj">WorkContourContainer</a></span></code>
<div class="block">Represents the work contours (resource curves) used by the current project.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a> in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with parameters of type <a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MicrosoftProjectUniqueIDMapper.html#MicrosoftProjectUniqueIDMapper-org.mpxj.ProjectEntityContainer-">MicrosoftProjectUniqueIDMapper</a></span>(<a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">ProjectEntityContainer</a>&lt;? extends <a href="../../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a>&gt;&nbsp;container)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectEntityContainer.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectEntityContainer.html" target="_top">Frames</a></li>
<li><a href="ProjectEntityContainer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
