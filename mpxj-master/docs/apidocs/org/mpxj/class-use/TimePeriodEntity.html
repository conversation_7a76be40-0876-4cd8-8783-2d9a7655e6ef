<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.mpxj.TimePeriodEntity (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.mpxj.TimePeriodEntity (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/TimePeriodEntity.html" target="_top">Frames</a></li>
<li><a href="TimePeriodEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.mpxj.TimePeriodEntity" class="title">Uses of Interface<br>org.mpxj.TimePeriodEntity</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that implement <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a></span></code>
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a> in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with parameters of type <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractTimephasedWorkNormaliser.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/AbstractTimephasedWorkNormaliser.html#mergeSameWork-org.mpxj.ProjectCalendar-org.mpxj.TimePeriodEntity-java.util.List-">mergeSameWork</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
             <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a>&nbsp;parent,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;list)</code>
<div class="block">Merges individual days together into time spans where the
 same work is undertaken each day.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TimephasedNormaliser.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/TimephasedNormaliser.html#normalise-org.mpxj.ProjectCalendar-org.mpxj.TimePeriodEntity-java.util.List-">normalise</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
         <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a>&nbsp;parent,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/common/TimephasedNormaliser.html" title="type parameter in TimephasedNormaliser">T</a>&gt;&nbsp;list)</code>
<div class="block">This method converts the internal representation of timephased
 data used by MS Project into a standardised
 format to make it easy to work with.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a> in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MPPTimephasedBaselineCostNormaliser.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPTimephasedBaselineCostNormaliser.html#normalise-org.mpxj.ProjectCalendar-org.mpxj.TimePeriodEntity-java.util.List-">normalise</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
         <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a>&nbsp;parent,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/TimephasedCost.html" title="class in org.mpxj">TimephasedCost</a>&gt;&nbsp;list)</code>
<div class="block">This method converts the internal representation of timephased
 data used by MS Project into a standardised
 format to make it easy to work with.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MPPAbstractTimephasedWorkNormaliser.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPAbstractTimephasedWorkNormaliser.html#normalise-org.mpxj.ProjectCalendar-org.mpxj.TimePeriodEntity-java.util.List-">normalise</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
         <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a>&nbsp;parent,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;list)</code>
<div class="block">This method converts the internal representation of timephased
 data used by MS Project into a standardised
 format to make it easy to work with.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a> in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> with parameters of type <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MSPDITimephasedWorkNormaliser.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/MSPDITimephasedWorkNormaliser.html#normalise-org.mpxj.ProjectCalendar-org.mpxj.TimePeriodEntity-java.util.List-">normalise</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
         <a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">TimePeriodEntity</a>&nbsp;parent,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/TimephasedWork.html" title="class in org.mpxj">TimephasedWork</a>&gt;&nbsp;list)</code>
<div class="block">This method converts the internal representation of timephased
 data used by MS Project into a standardised
 format to make it easy to work with.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/TimePeriodEntity.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/TimePeriodEntity.html" target="_top">Frames</a></li>
<li><a href="TimePeriodEntity.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
