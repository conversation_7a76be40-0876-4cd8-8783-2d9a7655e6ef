<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.mpxj.ProjectFile (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.mpxj.ProjectFile (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectFile.html" target="_top">Frames</a></li>
<li><a href="ProjectFile.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.mpxj.ProjectFile" class="title">Uses of Class<br>org.mpxj.ProjectFile</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.asta">org.mpxj.asta</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw">org.mpxj.conceptdraw</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.cpm">org.mpxj.cpm</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.edrawproject">org.mpxj.edrawproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.explorer">org.mpxj.explorer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.fasttrack">org.mpxj.fasttrack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.ganttdesigner">org.mpxj.ganttdesigner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.ganttproject">org.mpxj.ganttproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.json">org.mpxj.json</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.merlin">org.mpxj.merlin</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpd">org.mpxj.mpd</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpx">org.mpxj.mpx</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.openplan">org.mpxj.openplan</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.phoenix">org.mpxj.phoenix</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.planner">org.mpxj.planner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera">org.mpxj.primavera</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.p3">org.mpxj.primavera.p3</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera.suretrak">org.mpxj.primavera.suretrak</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.projectcommander">org.mpxj.projectcommander</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.projectlibre">org.mpxj.projectlibre</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.reader">org.mpxj.reader</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.sage">org.mpxj.sage</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.sdef">org.mpxj.sdef</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.synchro">org.mpxj.synchro</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.turboproject">org.mpxj.turboproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.writer">org.mpxj.writer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">Task.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Task.html#expandSubproject--">expandSubproject</a></span>()</code>
<div class="block">If this task represents an external project (subproject), calling this method
 will attempt to read the subproject file, the link the tasks from
 the subproject file as children of this current task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#getBaseline--">getBaseline</a></span>()</code>
<div class="block">Retrieve the default baseline project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#getBaseline-int-">getBaseline</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve baselineN from Baseline, Baseline1, Baseline2 ...</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#getParentFile--">getParentFile</a></span>()</code>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectProperties.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectProperties.html#getResourcePoolObject--">getResourcePoolObject</a></span>()</code>
<div class="block">Retrieve a ProjectFile instance representing the resource pool for this project
 Returns null if this project does not have a resource pool or the file cannot be read.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">Task.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Task.html#getSubprojectObject--">getSubprojectObject</a></span>()</code>
<div class="block">If this task is an external project task or an external predecessor task,
 attempt to load the project to which it refers.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#getBaselines--">getBaselines</a></span>()</code>
<div class="block">Retrieve the baselines linked to this project.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractBaselineStrategy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractBaselineStrategy.html#clearBaseline-org.mpxj.ProjectFile-int-">clearBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
             int&nbsp;index)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BaselineStrategy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/BaselineStrategy.html#clearBaseline-org.mpxj.ProjectFile-int-">clearBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
             int&nbsp;index)</code>
<div class="block">Clear the requested baseline for the supplied project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractBaselineStrategy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractBaselineStrategy.html#populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">populateBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
                <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline,
                int&nbsp;index)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BaselineStrategy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/BaselineStrategy.html#populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">populateBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
                <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline,
                int&nbsp;index)</code>
<div class="block">Use the supplied baseline project to set the baselineN cost, duration, finish,
 fixed cost accrual, fixed cost, start and work attributes for the tasks
 in the supplied project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#setBaseline-org.mpxj.ProjectFile-">setBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline)</code>
<div class="block">Store the supplied project as the default baseline, and use it to set the
 baseline cost, duration, finish, fixed cost accrual, fixed cost, start and
 work attributes for the tasks in the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectFile.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html#setBaseline-org.mpxj.ProjectFile-int-">setBaseline</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline,
           int&nbsp;index)</code>
<div class="block">Store the supplied project as baselineN, and use it to set the
 baselineN cost, duration, finish, fixed cost accrual, fixed cost, start and
 work attributes for the tasks in the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Task.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/Task.html#setSubprojectObject-org.mpxj.ProjectFile-">setSubprojectObject</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Where we have already read a project, this method is used to
 attach it to the task.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/AbstractFieldContainer.html#AbstractFieldContainer-org.mpxj.ProjectFile-">AbstractFieldContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/Column.html#Column-org.mpxj.ProjectFile-">Column</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#ProjectCalendar-org.mpxj.ProjectFile-">ProjectCalendar</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</code>
<div class="block">Default constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#ProjectCalendar-org.mpxj.ProjectFile-boolean-">ProjectCalendar</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
               boolean&nbsp;temporaryCalendar)</code>
<div class="block">Internal constructor to allow the temporary calendar flag to be set.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarContainer.html#ProjectCalendarContainer-org.mpxj.ProjectFile-">ProjectCalendarContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectEntityWithIDContainer.html#ProjectEntityWithIDContainer-org.mpxj.ProjectFile-">ProjectEntityWithIDContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RelationContainer.html#RelationContainer-org.mpxj.ProjectFile-">RelationContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignment.html#ResourceAssignment-org.mpxj.ProjectFile-org.mpxj.Task-">ResourceAssignment</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                  <a href="../../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentContainer.html#ResourceAssignmentContainer-org.mpxj.ProjectFile-">ResourceAssignmentContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceContainer.html#ResourceContainer-org.mpxj.ProjectFile-">ResourceContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/StructuredNotes.html#StructuredNotes-org.mpxj.ProjectFile-java.lang.Integer-org.mpxj.NotesTopic-org.mpxj.Notes-">StructuredNotes</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID,
               <a href="../../../org/mpxj/NotesTopic.html" title="class in org.mpxj">NotesTopic</a>&nbsp;topic,
               <a href="../../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TaskContainer.html#TaskContainer-org.mpxj.ProjectFile-">TaskContainer</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/TemporaryCalendar.html#TemporaryCalendar-org.mpxj.ProjectFile-">TemporaryCalendar</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ViewState.html#ViewState-org.mpxj.ProjectFile-java.lang.String-java.util.List-int-">ViewState</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;viewName,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;uniqueIdList,
         int&nbsp;filterID)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.asta">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AstaSqliteReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaSqliteReader.html#read--">read</a></span>()</code>
<div class="block">Read a project from the current data source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AstaSqliteReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaSqliteReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AstaFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AstaTextFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaTextFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;fieldID)</code>
<div class="block">Retrieve a FieldType instance based on an ID value from
 an MPP9, MPP12 or MPP14 file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPAssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPAssignmentField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the AssignmentField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPProjectField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the ProjectField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPResourceField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the ResourceField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPTaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPTaskField.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value)</code>
<div class="block">Retrieve an instance of the TaskField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">FieldTypeHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/FieldTypeHelper.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;fieldID,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve a FieldType instance based on an ID value from
 an MPP9, MPP12 or MPP14 file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPAssignmentField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPAssignmentField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the AssignmentField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPProjectField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPProjectField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the ProjectField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPResourceField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPResourceField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the ResourceField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPTaskField.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/MPPTaskField.html#getInstance-org.mpxj.ProjectFile-int-org.mpxj.DataType-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
           int&nbsp;value,
           <a href="../../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;customFieldDataType)</code>
<div class="block">Retrieve an instance of the TaskField class based on the data read from an
 MS Project file.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/PopulatedFields.html#PopulatedFields-org.mpxj.ProjectFile-java.lang.Class-java.util.Collection-java.util.Collection-">PopulatedFields</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Class.html?is-external=true" title="class or interface in java.lang">Class</a>&lt;<a href="../../../org/mpxj/common/PopulatedFields.html" title="type parameter in PopulatedFields">E</a>&gt;&nbsp;fieldEnumType,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="../../../org/mpxj/UserDefinedField.html" title="class in org.mpxj">UserDefinedField</a>&gt;&nbsp;userDefinedFields,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="../../../org/mpxj/common/PopulatedFields.html" title="type parameter in PopulatedFields">T</a>&gt;&nbsp;collection)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ConceptDrawProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/conceptdraw/ConceptDrawProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.cpm">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/cpm/package-summary.html">org.mpxj.cpm</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/cpm/package-summary.html">org.mpxj.cpm</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MicrosoftScheduler.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/cpm/MicrosoftScheduler.html#schedule-org.mpxj.ProjectFile-java.time.LocalDateTime-">schedule</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraScheduler.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/cpm/PrimaveraScheduler.html#schedule-org.mpxj.ProjectFile-java.time.LocalDateTime-">schedule</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Scheduler.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/cpm/Scheduler.html#schedule-org.mpxj.ProjectFile-java.time.LocalDateTime-">schedule</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate)</code>
<div class="block">Calling this method schedules the supplied project using CPM, with the tasks
 in the project starting from the supplied start date.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.edrawproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">EdrawProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/edrawproject/EdrawProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.explorer">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/explorer/package-summary.html">org.mpxj.explorer</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/explorer/package-summary.html">org.mpxj.explorer</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectTreeController.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/explorer/ProjectTreeController.html#loadFile-java.io.File-org.mpxj.ProjectFile-">loadFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file,
        <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Command to load a file.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/explorer/package-summary.html">org.mpxj.explorer</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/explorer/ProjectFilePanel.html#ProjectFilePanel-java.io.File-org.mpxj.ProjectFile-org.mpxj.explorer.WriteOptions-">ProjectFilePanel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file,
                <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
                org.mpxj.explorer.WriteOptions&nbsp;writeOptions)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.fasttrack">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/fasttrack/package-summary.html">org.mpxj.fasttrack</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/fasttrack/package-summary.html">org.mpxj.fasttrack</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">FastTrackReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/fasttrack/FastTrackReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttdesigner">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttDesignerReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ganttdesigner/GanttDesignerReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ganttproject/GanttProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.json">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/json/package-summary.html">org.mpxj.json</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/json/package-summary.html">org.mpxj.json</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">JsonWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/json/JsonWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.merlin">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/merlin/package-summary.html">org.mpxj.merlin</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/merlin/package-summary.html">org.mpxj.merlin</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MerlinReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/merlin/MerlinReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpd">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPDDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDDatabaseReader.html#read--">read</a></span>()</code>
<div class="block">Read project data from a database.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPDDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDDatabaseReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPDFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDFileReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">MPDDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDDatabaseReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">MPDFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDFileReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPDUtility.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpd/MPDUtility.html#getAdjustedDuration-org.mpxj.ProjectFile-int-org.mpxj.TimeUnit-">getAdjustedDuration</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                   int&nbsp;duration,
                   <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;timeUnit)</code>
<div class="block">Given a duration and the time units for the duration extracted from an MPP
 file, this method creates a new Duration to represent the given
 duration.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> declared as <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractView.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/AbstractView.html#m_file">m_file</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">CriteriaReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/CriteriaReader.html#m_file">m_file</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPPReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPReader.html#read-org.apache.poi.poifs.filesystem.POIFSFileSystem-">read</a></span>(org.apache.poi.poifs.filesystem.POIFSFileSystem&nbsp;fs)</code>
<div class="block">Alternative entry point allowing an MPP file to be read from
 a user-supplied POI file stream.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">MPPUtility.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/MPPUtility.html#convertRateFromHours-org.mpxj.ProjectFile-org.mpxj.Resource-org.mpxj.ResourceField-org.mpxj.ResourceField-">convertRateFromHours</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                    <a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource,
                    <a href="../../../org/mpxj/ResourceField.html" title="enum in org.mpxj">ResourceField</a>&nbsp;rateField,
                    <a href="../../../org/mpxj/ResourceField.html" title="enum in org.mpxj">ResourceField</a>&nbsp;unitsField)</code>
<div class="block">Convert from the internal representation of a rate as an amount per hour to the
 format presented to the user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttChartView.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getColumnFontStyle-org.mpxj.ProjectFile-byte:A-int-java.util.Map-">getColumnFontStyle</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                  byte[]&nbsp;data,
                  int&nbsp;offset,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>
<div class="block">Retrieve column font details from a block of property data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">GanttChartView14.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView14.html#getColumnFontStyle-org.mpxj.ProjectFile-byte:A-int-java.util.Map-">getColumnFontStyle</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                  byte[]&nbsp;data,
                  int&nbsp;offset,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContourHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/WorkContourHelper.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
           int&nbsp;type)</code>
<div class="block">Retrieve a WorkContour instance based on its Microsoft Project ID value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ConstraintFactory.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ConstraintFactory.html#process-org.apache.poi.poifs.filesystem.DirectoryEntry-org.mpxj.ProjectFile-org.mpxj.mpp.DocumentInputStreamFactory-">process</a></span>(org.apache.poi.poifs.filesystem.DirectoryEntry&nbsp;projectDir,
       <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.DocumentInputStreamFactory&nbsp;inputStreamFactory)</code>
<div class="block">Main entry point when called to process constraint data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/GenericCriteria.html" title="class in org.mpxj">GenericCriteria</a></code></td>
<td class="colLast"><span class="typeNameLabel">CriteriaReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/CriteriaReader.html#process-org.mpxj.ProjectFile-byte:A-int-int-java.util.List-java.util.List-boolean:A-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       byte[]&nbsp;data,
       int&nbsp;dataOffset,
       int&nbsp;entryOffset,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/GenericCriteriaPrompt.html" title="class in org.mpxj">GenericCriteriaPrompt</a>&gt;&nbsp;prompts,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;&nbsp;fields,
       boolean[]&nbsp;criteriaType)</code>
<div class="block">Main entry point to read criteria data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignmentFactory.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ResourceAssignmentFactory.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.FieldMap-org.mpxj.mpp.FieldMap-boolean-org.mpxj.mpp.VarMeta-org.mpxj.mpp.Var2Data-org.mpxj.mpp.FixedMeta-org.mpxj.mpp.FixedData-org.mpxj.mpp.FixedData-int-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.FieldMap&nbsp;fieldMap,
       org.mpxj.mpp.FieldMap&nbsp;enterpriseCustomFieldMap,
       boolean&nbsp;useRawTimephasedData,
       org.mpxj.mpp.VarMeta&nbsp;assnVarMeta,
       org.mpxj.mpp.Var2Data&nbsp;assnVarData,
       org.mpxj.mpp.FixedMeta&nbsp;assnFixedMeta,
       org.mpxj.mpp.FixedData&nbsp;assnFixedData,
       org.mpxj.mpp.FixedData&nbsp;assnFixedData2,
       int&nbsp;count)</code>
<div class="block">Main entry point when called to process assignment data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FilterReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/FilterReader.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.FixedData-org.mpxj.mpp.Var2Data-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.FixedData&nbsp;fixedData,
       org.mpxj.mpp.Var2Data&nbsp;varData)</code>
<div class="block">Entry point for processing filter definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GroupReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GroupReader.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.FixedData-org.mpxj.mpp.Var2Data-java.util.Map-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.FixedData&nbsp;fixedData,
       org.mpxj.mpp.Var2Data&nbsp;varData,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>
<div class="block">Entry point for processing group definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GroupReader14.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GroupReader14.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.FixedData-org.mpxj.mpp.Var2Data-java.util.Map-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.FixedData&nbsp;fixedData,
       org.mpxj.mpp.Var2Data&nbsp;varData,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>
<div class="block">Entry point for processing group definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">GraphicalIndicatorReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GraphicalIndicatorReader.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">The main entry point for processing graphical indicator definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectPropertiesReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ProjectPropertiesReader.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.Props-org.apache.poi.poifs.filesystem.DirectoryEntry-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.Props&nbsp;props,
       org.apache.poi.poifs.filesystem.DirectoryEntry&nbsp;rootDir)</code>
<div class="block">The main entry point for processing project properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ViewStateReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/ViewStateReader.html#process-org.mpxj.ProjectFile-org.mpxj.mpp.Var2Data-byte:A-">process</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
       org.mpxj.mpp.Var2Data&nbsp;varData,
       byte[]&nbsp;fixedData)</code>
<div class="block">Entry point for processing saved view state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactory.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html#processDefaultStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processDefaultStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                    org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">Reads the default set of Gantt bar styles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactory14.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactory14.html#processDefaultStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processDefaultStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                    org.mpxj.mpp.Props&nbsp;props)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactoryCommon.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactoryCommon.html#processDefaultStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processDefaultStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                    org.mpxj.mpp.Props&nbsp;props)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactory.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html#processExceptionStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processExceptionStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                      org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">Reads the set of exception bar styles from MPP files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactory14.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactory14.html#processExceptionStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processExceptionStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                      org.mpxj.mpp.Props&nbsp;props)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">GanttBarStyleFactoryCommon.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleFactoryCommon.html#processExceptionStyles-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">processExceptionStyles</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                      org.mpxj.mpp.Props&nbsp;props)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/AbstractMppView.html#AbstractMppView-org.mpxj.ProjectFile-">AbstractMppView</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/AbstractView.html#AbstractView-org.mpxj.ProjectFile-">AbstractView</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/CustomFieldValueReader9.html#CustomFieldValueReader9-org.apache.poi.poifs.filesystem.DirectoryEntry-org.mpxj.ProjectFile-org.mpxj.mpp.Props-">CustomFieldValueReader9</a></span>(org.apache.poi.poifs.filesystem.DirectoryEntry&nbsp;projectDir,
                       <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                       org.mpxj.mpp.Props&nbsp;projectProps)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GenericView.html#GenericView-org.mpxj.ProjectFile-byte:A-org.mpxj.mpp.Var2Data-">GenericView</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent,
           byte[]&nbsp;data,
           org.mpxj.mpp.Var2Data&nbsp;varData)</code>
<div class="block">Extract the view data from the view data block.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GenericView12.html#GenericView12-org.mpxj.ProjectFile-byte:A-org.mpxj.mpp.Var2Data-">GenericView12</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent,
             byte[]&nbsp;data,
             org.mpxj.mpp.Var2Data&nbsp;varData)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GenericView14.html#GenericView14-org.mpxj.ProjectFile-byte:A-org.mpxj.mpp.Var2Data-">GenericView14</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent,
             byte[]&nbsp;data,
             org.mpxj.mpp.Var2Data&nbsp;varData)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GenericView9.html#GenericView9-org.mpxj.ProjectFile-byte:A-org.mpxj.mpp.Var2Data-">GenericView9</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent,
            byte[]&nbsp;data,
            org.mpxj.mpp.Var2Data&nbsp;varData)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html#UserDefinedFieldMap-org.mpxj.ProjectFile-java.util.List-">UserDefinedFieldMap</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;&nbsp;targetFieldList)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/View8.html#View8-org.mpxj.ProjectFile-byte:A-">View8</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;parent,
     byte[]&nbsp;data)</code>
<div class="block">Extract the view data from the view data block.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpx">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MPXReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpx/MPXReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MPXWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpx/MPXWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;out)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpx/MPXJFormats.html#MPXJFormats-java.util.Locale-java.lang.String-org.mpxj.ProjectFile-">MPXJFormats</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;nullText,
           <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">MSPDIReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/MSPDIReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#parseCustomField-org.mpxj.ProjectFile-org.mpxj.FieldContainer-java.lang.String-org.mpxj.FieldType-org.mpxj.TimeUnit-">parseCustomField</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                <a href="../../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>&nbsp;mpx,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
                <a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;mpxFieldID,
                <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;durationFormat)</code>
<div class="block">Parse a custom field value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#parseDuration-org.mpxj.ProjectFile-org.mpxj.TimeUnit-java.lang.String-">parseDuration</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
             <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;defaultUnits,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Parse a duration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#setContext-org.mpxj.ProjectFile-boolean-">setContext</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
          boolean&nbsp;ignoreErrors)</code>
<div class="block">This method is called to set the parent file to provide context for
 parse and print operations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MSPDIWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/MSPDIWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.openplan">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">OpenPlanReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/openplan/OpenPlanReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">OpenPlanReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/openplan/OpenPlanReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">OpenPlanReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/openplan/OpenPlanReader.html#read-org.apache.poi.poifs.filesystem.POIFSFileSystem-">read</a></span>(org.apache.poi.poifs.filesystem.POIFSFileSystem&nbsp;fs)</code>
<div class="block">Read a single project from the BK3 file represented by the POIFSFileSystem instance.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">OpenPlanReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/openplan/OpenPlanReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">OpenPlanReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/openplan/OpenPlanReader.html#readAll-org.apache.poi.poifs.filesystem.POIFSFileSystem-">readAll</a></span>(org.apache.poi.poifs.filesystem.POIFSFileSystem&nbsp;fs)</code>
<div class="block">Read all projects from the BK3 file represented by the POIFSFileSystem instance.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.phoenix">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PhoenixReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/phoenix/PhoenixReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.planner">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlannerReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/planner/PlannerReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlannerWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/planner/PlannerWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#read--">read</a></span>()</code>
<div class="block">Read a project from the current data source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseFileReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraPMFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraPMFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#readAll--">readAll</a></span>()</code>
<div class="block">Convenience method which allows all projects in the database to
 be read in a single operation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseFileReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraPMFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraPMFileReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>
<div class="block">This is a convenience method which allows all projects in a
 PMXML file to be read in a single pass.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>
<div class="block">This is a convenience method which allows all projects in an
 XER file to be read in a single pass.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraPMFileWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraPMFileWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PrimaveraXERFileWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/PrimaveraXERFileWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;outputStream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.p3">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">P3DatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/p3/P3DatabaseReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">P3PRXFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/p3/P3PRXFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">P3DatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/p3/P3DatabaseReader.html#setProjectNameAndRead-java.io.File-">setProjectNameAndRead</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>
<div class="block">Convenience method which locates the first P3 database in a directory
 and opens it.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">P3DatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/p3/P3DatabaseReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.suretrak">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SureTrakDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/suretrak/SureTrakDatabaseReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SureTrakSTXFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/suretrak/SureTrakSTXFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SureTrakDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/suretrak/SureTrakDatabaseReader.html#setProjectNameAndRead-java.io.File-">setProjectNameAndRead</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>
<div class="block">Convenience method which locates the first SureTrak database in a directory
 and opens it.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">SureTrakDatabaseReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/primavera/suretrak/SureTrakDatabaseReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;directory)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectcommander">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCommanderReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/projectcommander/ProjectCommanderReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectlibre">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectLibreReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/projectlibre/ProjectLibreReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.reader">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.ProjectReaderProxy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html#read--">read</a></span>()</code>
<div class="block">Read a single <code>ProjectFile</code> instance from the supplied file or stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectStreamReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Read a single schedule from a file where a File instance is supplied.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Read a single schedule from a file where the contents of the project file
 are supplied via an input stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectStreamReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Read a single schedule from a file where the file name is supplied.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.ProjectReaderProxy.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html#readAll--">readAll</a></span>()</code>
<div class="block">Read a list of <code>ProjectFile</code> instances from the supplied file or stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Default implementation of readAll.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectStreamReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Read all schedules from a file where a File instance is supplied.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectStreamReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Default implementation of readAll.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Read all schedules from a file where the contents of the project file
 are supplied via an input stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectFileReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectStreamReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Default implementation of readAll to support file
 formats which do not contain multiple schedules.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Read all schedules from a file where the file name is supplied.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/AbstractProjectReader.html#addListenersToProject-org.mpxj.ProjectFile-">addListenersToProject</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project)</code>
<div class="block">Common method to add listeners to a project.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sage">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SageReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/sage/SageReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;is)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sdef">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SDEFReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/sdef/SDEFReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">SDEFWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/sdef/SDEFWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;out)</code>
<div class="block">Write a project file in SDEF format to an output stream.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.synchro">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">SynchroReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/synchro/SynchroReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.turboproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a> that return <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><span class="typeNameLabel">TurboProjectReader.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/turboproject/TurboProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.writer">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a> in <a href="../../../org/mpxj/writer/package-summary.html">org.mpxj.writer</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/writer/package-summary.html">org.mpxj.writer</a> with parameters of type <a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/AbstractProjectWriter.html#write-org.mpxj.ProjectFile-java.io.File-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/ProjectWriter.html#write-org.mpxj.ProjectFile-java.io.File-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Create a project file using the supplied File instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/UniversalProjectWriter.html#write-org.mpxj.ProjectFile-java.io.File-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/ProjectWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;outputStream)</code>
<div class="block">Write a project file's content to the supplied OutputStream instance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/UniversalProjectWriter.html#write-org.mpxj.ProjectFile-java.io.OutputStream-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;outputStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/AbstractProjectWriter.html#write-org.mpxj.ProjectFile-java.lang.String-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/ProjectWriter.html#write-org.mpxj.ProjectFile-java.lang.String-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Create a project file using the supplied file name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectWriter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/writer/UniversalProjectWriter.html#write-org.mpxj.ProjectFile-java.lang.String-">write</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectFile.html" target="_top">Frames</a></li>
<li><a href="ProjectFile.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
