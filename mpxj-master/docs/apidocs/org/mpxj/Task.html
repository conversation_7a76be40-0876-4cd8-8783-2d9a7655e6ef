<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Task (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Task (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10,"i265":10,"i266":10,"i267":10,"i268":10,"i269":10,"i270":10,"i271":10,"i272":10,"i273":10,"i274":10,"i275":10,"i276":10,"i277":10,"i278":10,"i279":10,"i280":10,"i281":10,"i282":10,"i283":10,"i284":10,"i285":10,"i286":10,"i287":10,"i288":10,"i289":10,"i290":10,"i291":10,"i292":10,"i293":10,"i294":10,"i295":10,"i296":10,"i297":10,"i298":10,"i299":10,"i300":10,"i301":10,"i302":10,"i303":10,"i304":10,"i305":10,"i306":10,"i307":10,"i308":10,"i309":10,"i310":10,"i311":10,"i312":10,"i313":10,"i314":10,"i315":10,"i316":10,"i317":10,"i318":10,"i319":10,"i320":10,"i321":10,"i322":10,"i323":10,"i324":10,"i325":10,"i326":10,"i327":10,"i328":10,"i329":10,"i330":10,"i331":10,"i332":10,"i333":10,"i334":10,"i335":10,"i336":10,"i337":10,"i338":10,"i339":10,"i340":10,"i341":10,"i342":10,"i343":10,"i344":10,"i345":10,"i346":10,"i347":10,"i348":10,"i349":10,"i350":10,"i351":10,"i352":10,"i353":10,"i354":10,"i355":10,"i356":10,"i357":10,"i358":10,"i359":10,"i360":10,"i361":10,"i362":10,"i363":10,"i364":10,"i365":10,"i366":10,"i367":10,"i368":10,"i369":10,"i370":10,"i371":10,"i372":10,"i373":10,"i374":10,"i375":10,"i376":10,"i377":10,"i378":10,"i379":10,"i380":10,"i381":10,"i382":10,"i383":10,"i384":10,"i385":10,"i386":10,"i387":10,"i388":10,"i389":10,"i390":10,"i391":10,"i392":10,"i393":10,"i394":10,"i395":10,"i396":10,"i397":10,"i398":10,"i399":10,"i400":10,"i401":10,"i402":10,"i403":10,"i404":10,"i405":10,"i406":10,"i407":10,"i408":10,"i409":10,"i410":10,"i411":10,"i412":10,"i413":10,"i414":10,"i415":10,"i416":10,"i417":10,"i418":10,"i419":10,"i420":10,"i421":10,"i422":10,"i423":10,"i424":10,"i425":10,"i426":10,"i427":10,"i428":10,"i429":10,"i430":10,"i431":10,"i432":10,"i433":10,"i434":10,"i435":10,"i436":10,"i437":10,"i438":10,"i439":10,"i440":10,"i441":10,"i442":10,"i443":10,"i444":10,"i445":10,"i446":10,"i447":10,"i448":10,"i449":10,"i450":10,"i451":10,"i452":10,"i453":10,"i454":10,"i455":10,"i456":10,"i457":10,"i458":10,"i459":10,"i460":10,"i461":10,"i462":10,"i463":10,"i464":10,"i465":10,"i466":10,"i467":10,"i468":10,"i469":10,"i470":10,"i471":10,"i472":10,"i473":10,"i474":10,"i475":10,"i476":10,"i477":10,"i478":10,"i479":10,"i480":10,"i481":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TableContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TaskContainer.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Task.html" target="_top">Frames</a></li>
<li><a href="Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class Task" class="title">Class Task</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">org.mpxj.AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.Task</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;, <a href="../../org/mpxj/ChildTaskContainer.html" title="interface in org.mpxj">ChildTaskContainer</a>, <a href="../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>, <a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a>, <a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a>, <a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Task</span>
extends <a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;, <a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a>, <a href="../../org/mpxj/ChildTaskContainer.html" title="interface in org.mpxj">ChildTaskContainer</a></pre>
<div class="block">This class represents a task record from a project file.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addActivityCodeValue-org.mpxj.ActivityCodeValue-">addActivityCodeValue</a></span>(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</code>
<div class="block">Assign an activity code value to this task.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addChildTask-org.mpxj.Task-">addChildTask</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child)</code>
<div class="block">This method is used to associate a child task with the current
 task instance.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addChildTask-org.mpxj.Task-int-">addChildTask</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child,
            int&nbsp;childOutlineLevel)</code>
<div class="block">This method is used to associate a child task with the current
 task instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addChildTaskBefore-org.mpxj.Task-org.mpxj.Task-">addChildTaskBefore</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child,
                  <a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;previousSibling)</code>
<div class="block">Inserts a child task prior to a given sibling task.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addPredecessor-org.mpxj.Relation.Builder-">addPredecessor</a></span>(<a href="../../org/mpxj/Relation.Builder.html" title="class in org.mpxj">Relation.Builder</a>&nbsp;builder)</code>
<div class="block">This method allows a predecessor relationship to be added to this
 task instance.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/RecurringTask.html" title="class in org.mpxj">RecurringTask</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addRecurringTask--">addRecurringTask</a></span>()</code>
<div class="block">This method allows recurring task details to be added to the
 current task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addResourceAssignment-org.mpxj.Resource-">addResourceAssignment</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">This method allows a resource assignment to be added to the
 current task.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addResourceAssignment-org.mpxj.ResourceAssignment-">addResourceAssignment</a></span>(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</code>
<div class="block">Add a resource assignment which has been populated elsewhere.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#addTask--">addTask</a></span>()</code>
<div class="block">This method allows nested tasks to be added, with the WBS being
 completed automatically.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#clearChildTasks--">clearChildTasks</a></span>()</code>
<div class="block">This method allows the list of child tasks to be cleared in preparation
 for the hierarchical task structure to be built.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#compareTo-org.mpxj.Task-">compareTo</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;o)</code>
<div class="block">This method implements the only method in the Comparable interface.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#expandSubproject--">expandSubproject</a></span>()</code>
<div class="block">If this task represents an external project (subproject), calling this method
 will attempt to read the subproject file, the link the tasks from
 the subproject file as children of this current task.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#generateOutlineNumber-org.mpxj.Task-">generateOutlineNumber</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;parent)</code>
<div class="block">This method is used to automatically generate a value
 for the Outline Number field of this task.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#generateWBS-org.mpxj.Task-">generateWBS</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;parent)</code>
<div class="block">This method is used to automatically generate a value
 for the WBS field of this task.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActive--">getActive</a></span>()</code>
<div class="block">Retrieves the active flag.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ActivityCode.html" title="class in org.mpxj">ActivityCode</a>,<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActivityCodeValues--">getActivityCodeValues</a></span>()</code>
<div class="block">Retrieve the activity code values associated with this task.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActivityID--">getActivityID</a></span>()</code>
<div class="block">Retrieve the activity ID.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActivityPercentComplete--">getActivityPercentComplete</a></span>()</code>
<div class="block">This accessor method returns the percent complete value for this task
 as defined by the Percent Complete Type attribute.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj">ActivityStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActivityStatus--">getActivityStatus</a></span>()</code>
<div class="block">Retrieve the activity status.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj">ActivityType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActivityType--">getActivityType</a></span>()</code>
<div class="block">Retrieve the activity type.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">The Actual Cost field shows costs incurred for work already performed
 by all resources on a task, along with any other recorded costs associated
 with the task.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualDuration--">getActualDuration</a></span>()</code>
<div class="block">The Actual Duration field shows the span of actual working time for a
 task so far, based on the scheduled duration and current remaining work
 or completion percentage.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualFinish--">getActualFinish</a></span>()</code>
<div class="block">The Actual Finish field shows the date and time that a task actually
 finished.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Retrieves the actual overtime cost for this task.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualOvertimeWork--">getActualOvertimeWork</a></span>()</code>
<div class="block">Retrieves the actual overtime work value.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualOvertimeWorkProtected--">getActualOvertimeWorkProtected</a></span>()</code>
<div class="block">Retrieves the actual overtime work protected value.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualStart--">getActualStart</a></span>()</code>
<div class="block">The Actual Start field shows the date and time that a task actually began.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualWork--">getActualWork</a></span>()</code>
<div class="block">The Actual Work field shows the amount of work that has already been done
 by the resources assigned to a task.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualWorkLabor--">getActualWorkLabor</a></span>()</code>
<div class="block">Retrieve the labor component of the task's Actual Work.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualWorkNonlabor--">getActualWorkNonlabor</a></span>()</code>
<div class="block">Retrieve the nonlabor component of the task's Actual Work.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getActualWorkProtected--">getActualWorkProtected</a></span>()</code>
<div class="block">Retrieves the actual work protected value.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getACWP--">getACWP</a></span>()</code>
<div class="block">Retrieve the ACWP value.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBarName--">getBarName</a></span>()</code>
<div class="block">Retrieve the name of the Asta Powerproject bar to which this task belongs.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineBudgetCost--">getBaselineBudgetCost</a></span>()</code>
<div class="block">Retrieve the baseline budget cost.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineBudgetCost-int-">getBaselineBudgetCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline budget cost.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineBudgetWork--">getBaselineBudgetWork</a></span>()</code>
<div class="block">Retrieve the baseline budget work.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineBudgetWork-int-">getBaselineBudgetWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline budget work.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">The Baseline Cost field shows the total planned cost for a task.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineCost-int-">getBaselineCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineDuration--">getBaselineDuration</a></span>()</code>
<div class="block">The Baseline Duration field shows the original span of time planned
 to complete a task.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineDuration-int-">getBaselineDuration</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineDurationText--">getBaselineDurationText</a></span>()</code>
<div class="block">Retrieves the text value for the baseline duration.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineDurationText-int-">getBaselineDurationText</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieves the baseline duration text value.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedDuration--">getBaselineEstimatedDuration</a></span>()</code>
<div class="block">Retrieve the baseline estimated duration.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedDuration-int-">getBaselineEstimatedDuration</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedFinish--">getBaselineEstimatedFinish</a></span>()</code>
<div class="block">Retrieve the baseline estimated finish.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedFinish-int-">getBaselineEstimatedFinish</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedStart--">getBaselineEstimatedStart</a></span>()</code>
<div class="block">Retrieve the baseline estimated start.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineEstimatedStart-int-">getBaselineEstimatedStart</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFinish--">getBaselineFinish</a></span>()</code>
<div class="block">The Baseline Finish field shows the planned completion date for a task
 at the time you saved a baseline.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFinish-int-">getBaselineFinish</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFinishText--">getBaselineFinishText</a></span>()</code>
<div class="block">Retrieves the baseline finish text value.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFinishText-int-">getBaselineFinishText</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieves the baseline finish text value.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFixedCost--">getBaselineFixedCost</a></span>()</code>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFixedCost-int-">getBaselineFixedCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFixedCostAccrual--">getBaselineFixedCostAccrual</a></span>()</code>
<div class="block">Retrieves the baseline fixed cost accrual.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineFixedCostAccrual-int-">getBaselineFixedCostAccrual</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineStart--">getBaselineStart</a></span>()</code>
<div class="block">The Baseline Start field shows the planned beginning date for a task at
 the time you saved a baseline.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineStart-int-">getBaselineStart</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineStartText--">getBaselineStartText</a></span>()</code>
<div class="block">Retrieves the baseline start text value.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineStartText-int-">getBaselineStartText</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieves the baseline start text value.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineTask--">getBaselineTask</a></span>()</code>
<div class="block">If the parent ProjectFile has one or more baseline ProjectFile instances,
 this method will allow you to retrieve the baseline task associated
 with this current task.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineTask-int-">getBaselineTask</a></span>(int&nbsp;index)</code>
<div class="block">If the parent ProjectFile has one or more baseline ProjectFile instances,
 this method will allow you to retrieve the baseline task associated
 with this current task.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineWork--">getBaselineWork</a></span>()</code>
<div class="block">The Baseline Work field shows the originally planned amount of work to be
 performed by all resources assigned to a task.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBaselineWork-int-">getBaselineWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBCWP--">getBCWP</a></span>()</code>
<div class="block">The BCWP (budgeted cost of work performed) field contains
 the cumulative value of the assignment's timephased percent complete
 multiplied by the assignment's timephased baseline cost.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBCWS--">getBCWS</a></span>()</code>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBidItem--">getBidItem</a></span>()</code>
<div class="block">Set the bid item field.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBoardStatusID--">getBoardStatusID</a></span>()</code>
<div class="block">Retrieve the Board Status ID.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBudgetCost--">getBudgetCost</a></span>()</code>
<div class="block">Retrieve the budget cost.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getBudgetWork--">getBudgetWork</a></span>()</code>
<div class="block">Retrieve the budget work.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCalendar--">getCalendar</a></span>()</code>
<div class="block">Retrieves the calendar associated with this task.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCalendarUniqueID--">getCalendarUniqueID</a></span>()</code>
<div class="block">Retrieve the calendar unique ID.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCanonicalActivityID--">getCanonicalActivityID</a></span>()</code>
<div class="block">Retrieve a "canonical" version of the Activity ID.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCategoryOfWork--">getCategoryOfWork</a></span>()</code>
<div class="block">Retrieve the category of work field.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getChildTasks--">getChildTasks</a></span>()</code>
<div class="block">This method retrieves a list of child tasks relative to the
 current task, as defined by the outline level.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCompleteThrough--">getCompleteThrough</a></span>()</code>
<div class="block">Retrieve the "complete through" date.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getConfirmed--">getConfirmed</a></span>()</code>
<div class="block">The Confirmed field indicates whether all resources assigned to a task
 have accepted or rejected the task assignment in response to a TeamAssign
 message regarding their assignments.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getConstraintDate--">getConstraintDate</a></span>()</code>
<div class="block">The Constraint Date field shows the specific date associated with certain
 constraint types, such as Must Start On, Must Finish On,
 Start No Earlier Than,
 Start No Later Than, Finish No Earlier Than, and Finish No Later Than.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getConstraintType--">getConstraintType</a></span>()</code>
<div class="block">The Constraint Type field provides choices for the type of constraint you
 can apply for scheduling a task.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getContact--">getContact</a></span>()</code>
<div class="block">The Contact field contains the name of an individual
 responsible for a task.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCost--">getCost</a></span>()</code>
<div class="block">The Cost field shows the total scheduled, or projected, cost for a task,
 based on costs already incurred for work performed by all resources assigned
 to the task, in addition to the costs planned for the remaining work for the
 assignment.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCost-int-">getCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a cost value.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCostVariance--">getCostVariance</a></span>()</code>
<div class="block">The Cost Variance field shows the difference between the baseline cost
 and total cost for a task.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCreateDate--">getCreateDate</a></span>()</code>
<div class="block">The Created field contains the date and time when a task was added
 to the project.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCritical--">getCritical</a></span>()</code>
<div class="block">The Critical field indicates whether a task has any room in the schedule
 to slip, or if a task is on the critical path.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getCV--">getCV</a></span>()</code>
<div class="block">The CV (earned value cost variance) field shows the difference between
 how much it should have cost to achieve the current level of completion
 on the task, and how much it has actually cost to achieve the current
 level of completion up to the status date or today's date.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDate-int-">getDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a date value.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDeadline--">getDeadline</a></span>()</code>
<div class="block">This method retrieves the deadline for this task.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDepartment--">getDepartment</a></span>()</code>
<div class="block">Retrieve the department field.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDuration--">getDuration</a></span>()</code>
<div class="block">The Duration field is the total span of active working time for a task.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDuration-int-">getDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a duration value.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDurationText--">getDurationText</a></span>()</code>
<div class="block">Retrieves the duration text of a manually scheduled task.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getDurationVariance--">getDurationVariance</a></span>()</code>
<div class="block">The Duration Variance field contains the difference between the
 baseline duration of a task and the total duration (current estimate)
 of a task.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEarlyFinish--">getEarlyFinish</a></span>()</code>
<div class="block">The Early Finish field contains the earliest date that a task could
 possibly finish, based on early finish dates of predecessor and
 successor tasks, other constraints, and any leveling delay.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEarlyStart--">getEarlyStart</a></span>()</code>
<div class="block">The Early Start field contains the earliest date that a task could
 possibly begin, based on the early start dates of predecessor and
 successor tasks, and other constraints.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEarnedValueMethod--">getEarnedValueMethod</a></span>()</code>
<div class="block">Retrieves the earned value method.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEffectiveCalendar--">getEffectiveCalendar</a></span>()</code>
<div class="block">Retrieve the effective calendar for this task.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEffortDriven--">getEffortDriven</a></span>()</code>
<div class="block">Retrieves the effort driven flag.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseCost-int-">getEnterpriseCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseDate-int-">getEnterpriseDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseDuration-int-">getEnterpriseDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseFlag-int-">getEnterpriseFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseNumber-int-">getEnterpriseNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEnterpriseText-int-">getEnterpriseText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getEstimated--">getEstimated</a></span>()</code>
<div class="block">This method retrieves a flag indicating whether the duration of the
 task has only been estimated.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExistingResourceAssignment-org.mpxj.Resource-">getExistingResourceAssignment</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">Retrieves an existing resource assignment if one is present,
 to prevent duplicate resource assignments being added.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExpanded--">getExpanded</a></span>()</code>
<div class="block">Retrieve a flag indicating if the task is shown as expanded
 in MS Project.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExpectedFinish--">getExpectedFinish</a></span>()</code>
<div class="block">Retrieve the expected finish date.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ExpenseItem.html" title="class in org.mpxj">ExpenseItem</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExpenseItems--">getExpenseItems</a></span>()</code>
<div class="block">Retrieve expense items for this task.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExternalEarlyStart--">getExternalEarlyStart</a></span>()</code>
<div class="block">Retrieve the external early start date.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExternalLateFinish--">getExternalLateFinish</a></span>()</code>
<div class="block">Retrieve the external late finish date.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExternalProject--">getExternalProject</a></span>()</code>
<div class="block">Retrieves the external project flag.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getExternalTask--">getExternalTask</a></span>()</code>
<div class="block">Retrieves the external task flag.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFeatureOfWork--">getFeatureOfWork</a></span>()</code>
<div class="block">Retrieve the feature of work field.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFieldByAlias-java.lang.String-">getFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the value of a field using its alias.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFinish--">getFinish</a></span>()</code>
<div class="block">The Finish field shows the date and time that a task is scheduled to
 be completed.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFinish-int-">getFinish</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a finish value.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFinishSlack--">getFinishSlack</a></span>()</code>
<div class="block">Retrieve the finish slack.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFinishText--">getFinishText</a></span>()</code>
<div class="block">Retrieves the finish text of a manually scheduled task.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFinishVariance--">getFinishVariance</a></span>()</code>
<div class="block">Calculate the finish variance.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFixedCost--">getFixedCost</a></span>()</code>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFixedCostAccrual--">getFixedCostAccrual</a></span>()</code>
<div class="block">Retrieves the fixed cost accrual flag value.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFlag-int-">getFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a flag value.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFloatPath--">getFloatPath</a></span>()</code>
<div class="block">Retrieve the float path number.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFloatPathOrder--">getFloatPathOrder</a></span>()</code>
<div class="block">Retrieve the float path order.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getFreeSlack--">getFreeSlack</a></span>()</code>
<div class="block">The Free Slack field contains the amount of time that a task can be
 delayed without delaying any successor tasks.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Retrieve the task GUID.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHammockCode--">getHammockCode</a></span>()</code>
<div class="block">Retrieve the hammock code field.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHideBar--">getHideBar</a></span>()</code>
<div class="block">The Hide Bar field indicates whether the Gantt bars and Calendar bars
 for a task are hidden.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHyperlink--">getHyperlink</a></span>()</code>
<div class="block">Retrieves the task hyperlink attribute.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHyperlinkAddress--">getHyperlinkAddress</a></span>()</code>
<div class="block">Retrieves the task hyperlink address attribute.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHyperlinkScreenTip--">getHyperlinkScreenTip</a></span>()</code>
<div class="block">Retrieves the task hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getHyperlinkSubAddress--">getHyperlinkSubAddress</a></span>()</code>
<div class="block">Retrieves the task hyperlink sub-address attribute.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getID--">getID</a></span>()</code>
<div class="block">The ID field contains the identifier number that Microsoft Project
 automatically assigns to each task as you add it to the project.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getIgnoreResourceCalendar--">getIgnoreResourceCalendar</a></span>()</code>
<div class="block">Retrieves the ignore resource calendar flag.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLateFinish--">getLateFinish</a></span>()</code>
<div class="block">The Late Finish field contains the latest date that a task can finish
 without delaying the finish of the project.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLateStart--">getLateStart</a></span>()</code>
<div class="block">The Late Start field contains the latest date that a task can start
 without delaying the finish of the project.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLevelAssignments--">getLevelAssignments</a></span>()</code>
<div class="block">Retrieves the level assignments flag.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLevelingCanSplit--">getLevelingCanSplit</a></span>()</code>
<div class="block">Retrieves the leveling can split flag.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLevelingDelay--">getLevelingDelay</a></span>()</code>
<div class="block">Delay , in MPX files as eg '0ed'.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLevelingDelayFormat--">getLevelingDelayFormat</a></span>()</code>
<div class="block">Retrieve the leveling delay format.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLinkedFields--">getLinkedFields</a></span>()</code>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project file,
 or from another program.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLocation--">getLocation</a></span>()</code>
<div class="block">Retrieves the location.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLocationUniqueID--">getLocationUniqueID</a></span>()</code>
<div class="block">Retrieves the location unique ID.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getLongestPath--">getLongestPath</a></span>()</code>
<div class="block">Retrieve the longest path.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getMail--">getMail</a></span>()</code>
<div class="block">Retrieve the mail field.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getManager--">getManager</a></span>()</code>
<div class="block">Retrieve the manager field.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getManualDuration--">getManualDuration</a></span>()</code>
<div class="block">Read the manual duration attribute.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getMarked--">getMarked</a></span>()</code>
<div class="block">The Marked field indicates whether a task is marked for further action or
 identification of some kind.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getMethodologyGUID--">getMethodologyGUID</a></span>()</code>
<div class="block">Retrieve the methodology GUID for this task.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getMilestone--">getMilestone</a></span>()</code>
<div class="block">The Milestone field indicates whether a task is a milestone.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getModOrClaimNumber--">getModOrClaimNumber</a></span>()</code>
<div class="block">Retrieve the mod or claim number field.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getName--">getName</a></span>()</code>
<div class="block">Retrieves the task name.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Retrieve the plain text representation of the task notes.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getNotesObject--">getNotesObject</a></span>()</code>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getNull--">getNull</a></span>()</code>
<div class="block">Retrieves the flag indicating if this is a null task.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getNumber-int-">getNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a number value.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getObjects--">getObjects</a></span>()</code>
<div class="block">The Objects field contains the number of objects attached to a task.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOutlineCode-int-">getOutlineCode</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an outline code value.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOutlineLevel--">getOutlineLevel</a></span>()</code>
<div class="block">The Outline Level field contains the number that indicates the level
 of the task in the project outline hierarchy.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOutlineNumber--">getOutlineNumber</a></span>()</code>
<div class="block">The Outline Number field contains the number of the task in the structure
 of an outline.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOverAllocated--">getOverAllocated</a></span>()</code>
<div class="block">Retrieve the over allocated flag.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOverallPercentComplete--">getOverallPercentComplete</a></span>()</code>
<div class="block">Retrieve the overall percent complete field.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOvertimeCost--">getOvertimeCost</a></span>()</code>
<div class="block">Retrieves the overtime cost.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getOvertimeWork--">getOvertimeWork</a></span>()</code>
<div class="block">Retrieves the overtime work attribute.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getParentFile--">getParentFile</a></span>()</code>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getParentTask--">getParentTask</a></span>()</code>
<div class="block">This method retrieves a reference to the parent of this task, as
 defined by the outline level.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getParentTaskUniqueID--">getParentTaskUniqueID</a></span>()</code>
<div class="block">Retrieve the unique ID of the parent task.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPercentageComplete--">getPercentageComplete</a></span>()</code>
<div class="block">The % Complete field contains the current status of a task,
 expressed as the percentage of the task's duration that has been completed.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPercentageWorkComplete--">getPercentageWorkComplete</a></span>()</code>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the percentage of the task's work that has been completed.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj">PercentCompleteType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPercentCompleteType--">getPercentCompleteType</a></span>()</code>
<div class="block">Retrieve the percent complete type.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPhaseOfWork--">getPhaseOfWork</a></span>()</code>
<div class="block">Retrieve the phase of work field.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPhysicalPercentComplete--">getPhysicalPercentComplete</a></span>()</code>
<div class="block">Retrieves the physical percent complete value.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedCost--">getPlannedCost</a></span>()</code>
<div class="block">Retrieve the planned cost field.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedDuration--">getPlannedDuration</a></span>()</code>
<div class="block">Retrieve the planned duration field.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedFinish--">getPlannedFinish</a></span>()</code>
<div class="block">Retrieve the planned finish field.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedStart--">getPlannedStart</a></span>()</code>
<div class="block">Retrieve the planned start field.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedWork--">getPlannedWork</a></span>()</code>
<div class="block">Retrieve the planned work field.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedWorkLabor--">getPlannedWorkLabor</a></span>()</code>
<div class="block">Retrieve the labor component of the task's Planned Work.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPlannedWorkNonlabor--">getPlannedWorkNonlabor</a></span>()</code>
<div class="block">Retrieve the nonlabor component of the task's Planned Work.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPredecessors--">getPredecessors</a></span>()</code>
<div class="block">Retrieves the list of predecessors for this task.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPreleveledFinish--">getPreleveledFinish</a></span>()</code>
<div class="block">Retrieves the preleveled finish attribute.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPreleveledStart--">getPreleveledStart</a></span>()</code>
<div class="block">Retrieves the preleveled start attribute.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPrimaryResource--">getPrimaryResource</a></span>()</code>
<div class="block">Retrieve the primary resource for this task.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPrimaryResourceUniqueID--">getPrimaryResourceUniqueID</a></span>()</code>
<div class="block">Retrieve the primary resource unique ID.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getPriority--">getPriority</a></span>()</code>
<div class="block">The Priority field provides choices for the level of importance
 assigned to a task, which in turn indicates how readily a task can be
 delayed or split during resource leveling.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getProject--">getProject</a></span>()</code>
<div class="block">The Project field shows the name of the project from which a task
 originated.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRecurring--">getRecurring</a></span>()</code>
<div class="block">Retrieve the recurring flag.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/RecurringTask.html" title="class in org.mpxj">RecurringTask</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRecurringTask--">getRecurringTask</a></span>()</code>
<div class="block">This method retrieves the recurring task record.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRegularWork--">getRegularWork</a></span>()</code>
<div class="block">Retrieve the amount of regular work.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">The Remaining Cost field shows the remaining scheduled expense of a
 task that will be incurred in completing the remaining scheduled work
 by all resources assigned to the task.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingDuration--">getRemainingDuration</a></span>()</code>
<div class="block">The Remaining Duration field shows the amount of time required
 to complete the unfinished portion of a task.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingEarlyFinish--">getRemainingEarlyFinish</a></span>()</code>
<div class="block">The date the resource is scheduled to finish the remaining work for the activity.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingEarlyStart--">getRemainingEarlyStart</a></span>()</code>
<div class="block">The date the resource is scheduled to start the remaining work for the activity.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingLateFinish--">getRemainingLateFinish</a></span>()</code>
<div class="block">Retrieve the remaining late finish value.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingLateStart--">getRemainingLateStart</a></span>()</code>
<div class="block">Retrieve the remaining late start value.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingOvertimeCost--">getRemainingOvertimeCost</a></span>()</code>
<div class="block">Retrieves the remaining overtime cost.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingOvertimeWork--">getRemainingOvertimeWork</a></span>()</code>
<div class="block">Retrieves the remaining overtime work attribute.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingWork--">getRemainingWork</a></span>()</code>
<div class="block">The Remaining Work field shows the amount of time, or person-hours,
 still required by all assigned resources to complete a task.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingWorkLabor--">getRemainingWorkLabor</a></span>()</code>
<div class="block">Retrieve the labor component of the task's Remaining Work.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRemainingWorkNonlabor--">getRemainingWorkNonlabor</a></span>()</code>
<div class="block">Retrieve the nonlabor component of the task's Remaining Work.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResourceAssignments--">getResourceAssignments</a></span>()</code>
<div class="block">This method allows the list of resource assignments for this
 task to be retrieved.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResourceGroup--">getResourceGroup</a></span>()</code>
<div class="block">The Resource Group field contains the list of resource groups to which
 the resources assigned to a task belong.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResourceInitials--">getResourceInitials</a></span>()</code>
<div class="block">The Resource Initials field lists the abbreviations for the names of
 resources assigned to a task.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResourceNames--">getResourceNames</a></span>()</code>
<div class="block">The Resource Names field lists the names of all resources assigned
 to a task.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResponsePending--">getResponsePending</a></span>()</code>
<div class="block">Retrieve the response pending flag.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResponsibilityCode--">getResponsibilityCode</a></span>()</code>
<div class="block">Retrieve the responsibility code field.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResume--">getResume</a></span>()</code>
<div class="block">The Resume field shows the date that the remaining portion of a task
 is scheduled to resume after you enter a new value for the % Complete
 field.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getResumeValid--">getResumeValid</a></span>()</code>
<div class="block">Retrieve the resume valid flag.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getRollup--">getRollup</a></span>()</code>
<div class="block">For subtasks, the Rollup field indicates whether information on the
 subtask Gantt bars
 will be rolled up to the summary task bar.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getScheduledDuration--">getScheduledDuration</a></span>()</code>
<div class="block">Retrieve the scheduled duration.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getScheduledFinish--">getScheduledFinish</a></span>()</code>
<div class="block">Retrieve the scheduled finish.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getScheduledStart--">getScheduledStart</a></span>()</code>
<div class="block">Retrieve the scheduled start.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSecondaryConstraintDate--">getSecondaryConstraintDate</a></span>()</code>
<div class="block">Retrieve the secondary constraint date.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSecondaryConstraintType--">getSecondaryConstraintType</a></span>()</code>
<div class="block">Retrieve the secondary constraint type.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSection--">getSection</a></span>()</code>
<div class="block">Retrieve the section field.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSequenceNumber--">getSequenceNumber</a></span>()</code>
<div class="block">Retrieve this task's sequence number.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getShowDurationText--">getShowDurationText</a></span>()</code>
<div class="block">Returns true for manually scheduled tasks if the Duration Text attribute should be
 displayed to the user rather than the Duration attribute.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getShowFinishText--">getShowFinishText</a></span>()</code>
<div class="block">Returns true for manually scheduled tasks if the Finish Text attribute should be
 displayed to the user rather than the Finish attribute.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getShowStartText--">getShowStartText</a></span>()</code>
<div class="block">Returns true for manually scheduled tasks if the Start Text attribute should be
 displayed to the user rather than the Start attribute.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/LocalDateTimeRange.html" title="class in org.mpxj">LocalDateTimeRange</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSplits--">getSplits</a></span>()</code>
<div class="block">This method retrieves a list of task splits.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSprintID--">getSprintID</a></span>()</code>
<div class="block">Retrieve the Sprint ID.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStart--">getStart</a></span>()</code>
<div class="block">The Start field shows the date and time that a task is scheduled to begin.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStart-int-">getStart</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a start value.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStartSlack--">getStartSlack</a></span>()</code>
<div class="block">Retrieve the start slack.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStartText--">getStartText</a></span>()</code>
<div class="block">Retrieve the start text for a manually scheduled task.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStartVariance--">getStartVariance</a></span>()</code>
<div class="block">Calculate the start variance.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Step.html" title="class in org.mpxj">Step</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSteps--">getSteps</a></span>()</code>
<div class="block">Retrieve steps for this task.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStop--">getStop</a></span>()</code>
<div class="block">The Stop field shows the date that represents the end of the actual
 portion of a task.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getStoredMaterial--">getStoredMaterial</a></span>()</code>
<div class="block">Retrieve the stored material value for this task.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectFile--">getSubprojectFile</a></span>()</code>
<div class="block">Contains the file name and path of the external project linked
 to this task.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectGUID--">getSubprojectGUID</a></span>()</code>
<div class="block">Retrieve the GUID of the linked subproject file.</div>
</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectObject--">getSubprojectObject</a></span>()</code>
<div class="block">If this task is an external project task or an external predecessor task,
 attempt to load the project to which it refers.</div>
</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectReadOnly--">getSubprojectReadOnly</a></span>()</code>
<div class="block">Retrieve the subproject read only flag.</div>
</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectTaskID--">getSubprojectTaskID</a></span>()</code>
<div class="block">Where a task in an MPP file represents a task from a subproject,
 this value will be non-zero.</div>
</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectTasksUniqueIDOffset--">getSubprojectTasksUniqueIDOffset</a></span>()</code>
<div class="block">Retrieves the offset added to unique task IDs from sub projects
 to generate the task ID shown in the master project.</div>
</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSubprojectTaskUniqueID--">getSubprojectTaskUniqueID</a></span>()</code>
<div class="block">Where a task in an MPP file represents a task from a subproject,
 this value will be non-zero.</div>
</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSuccessors--">getSuccessors</a></span>()</code>
<div class="block">Retrieves the list of successors for this task.</div>
</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSummary--">getSummary</a></span>()</code>
<div class="block">The Summary field indicates whether a task is a summary task.</div>
</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSummaryProgress--">getSummaryProgress</a></span>()</code>
<div class="block">Retrieve the summary progress date.</div>
</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSuspendDate--">getSuspendDate</a></span>()</code>
<div class="block">Retrieve the suspend date field.</div>
</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getSV--">getSV</a></span>()</code>
<div class="block">The SV (earned value schedule variance) field shows the difference in
 cost terms between the current progress and the baseline plan of the
 task up to the status date or today's date.</div>
</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getTaskMode--">getTaskMode</a></span>()</code>
<div class="block">Retrieves the task mode.</div>
</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getText-int-">getText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a text value.</div>
</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getTotalSlack--">getTotalSlack</a></span>()</code>
<div class="block">The Total Slack field contains the amount of time a task can be
 delayed without delaying the project's finish date.</div>
</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getType--">getType</a></span>()</code>
<div class="block">This method retrieves the task type.</div>
</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getUniqueID--">getUniqueID</a></span>()</code>
<div class="block">The Unique ID field contains the number that Microsoft Project
 automatically designates whenever a new task is created.</div>
</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getUpdateNeeded--">getUpdateNeeded</a></span>()</code>
<div class="block">The Update Needed field indicates whether a TeamUpdate message
 should be sent to the assigned resources because of changes to the
 start date, finish date, or resource reassignments of the task.</div>
</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getWBS--">getWBS</a></span>()</code>
<div class="block">The work breakdown structure code.</div>
</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getWork--">getWork</a></span>()</code>
<div class="block">The Work field shows the total amount of work scheduled to be performed
 on a task by all assigned resources.</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getWorkAreaCode--">getWorkAreaCode</a></span>()</code>
<div class="block">Retrieve the work area code field.</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getWorkersPerDay--">getWorkersPerDay</a></span>()</code>
<div class="block">Retrieve the workers per day field.</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#getWorkVariance--">getWorkVariance</a></span>()</code>
<div class="block">The Work Variance field contains the difference between a task's
 baseline work and the currently scheduled work.</div>
</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#hasChildTasks--">hasChildTasks</a></span>()</code>
<div class="block">Used to determine if a task has child tasks.</div>
</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#isPredecessor-org.mpxj.Task-">isPredecessor</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">Utility method used to determine if the supplied task
 is a predecessor of the current task.</div>
</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#isSuccessor-org.mpxj.Task-">isSuccessor</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">Utility method used to determine if the supplied task
 is a successor of the current task.</div>
</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#remove--">remove</a></span>()</code>
<div class="block">Removes this task from the project.</div>
</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#removeChildTask-org.mpxj.Task-">removeChildTask</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child)</code>
<div class="block">Removes a child task.</div>
</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#removePredecessor-org.mpxj.Task-org.mpxj.RelationType-org.mpxj.Duration-">removePredecessor</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;targetTask,
                 <a href="../../org/mpxj/RelationType.html" title="enum in org.mpxj">RelationType</a>&nbsp;type,
                 <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;lag)</code>
<div class="block">This method allows a predecessor relationship to be removed from this
 task instance.</div>
</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActive-boolean-">setActive</a></span>(boolean&nbsp;active)</code>
<div class="block">Sets the active flag.</div>
</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActivityID-java.lang.String-">setActivityID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the activity ID.</div>
</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActivityStatus-org.mpxj.ActivityStatus-">setActivityStatus</a></span>(<a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj">ActivityStatus</a>&nbsp;value)</code>
<div class="block">Set the activity status.</div>
</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActivityType-org.mpxj.ActivityType-">setActivityType</a></span>(<a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj">ActivityType</a>&nbsp;value)</code>
<div class="block">Set the activity type.</div>
</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualCost-java.lang.Number-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Actual Cost field shows costs incurred for work already performed
 by all resources
 on a task, along with any other recorded costs associated with the task.</div>
</td>
</tr>
<tr id="i265" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualDuration-org.mpxj.Duration-">setActualDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Actual Duration field shows the span of actual working time for a
 task so far,
 based on the scheduled duration and current remaining work or
 completion percentage.</div>
</td>
</tr>
<tr id="i266" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualFinish-java.time.LocalDateTime-">setActualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Actual Finish field shows the date and time that a task actually
 finished.</div>
</td>
</tr>
<tr id="i267" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualOvertimeCost-java.lang.Number-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the actual overtime cost for this task.</div>
</td>
</tr>
<tr id="i268" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualOvertimeWork-org.mpxj.Duration-">setActualOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the actual overtime work value.</div>
</td>
</tr>
<tr id="i269" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualOvertimeWorkProtected-org.mpxj.Duration-">setActualOvertimeWorkProtected</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualOvertimeWorkProtected)</code>
<div class="block">Sets the actual overtime work protected value.</div>
</td>
</tr>
<tr id="i270" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualStart-java.time.LocalDateTime-">setActualStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Actual Start field shows the date and time that a task actually began.</div>
</td>
</tr>
<tr id="i271" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualWork-org.mpxj.Duration-">setActualWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Actual Work field shows the amount of work that has already been
 done by the
 resources assigned to a task.</div>
</td>
</tr>
<tr id="i272" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualWorkLabor-org.mpxj.Duration-">setActualWorkLabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the labor component of the task's Actual Work.</div>
</td>
</tr>
<tr id="i273" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualWorkNonlabor-org.mpxj.Duration-">setActualWorkNonlabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the nonlabor component of the task's Actual Work.</div>
</td>
</tr>
<tr id="i274" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setActualWorkProtected-org.mpxj.Duration-">setActualWorkProtected</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualWorkProtected)</code>
<div class="block">Sets the actual work protected value.</div>
</td>
</tr>
<tr id="i275" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setACWP-java.lang.Number-">setACWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</code>
<div class="block">Set the ACWP value.</div>
</td>
</tr>
<tr id="i276" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBarName-java.lang.String-">setBarName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the name of the Asta Powerproject bar to which this task belongs.</div>
</td>
</tr>
<tr id="i277" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineBudgetCost-int-java.lang.Number-">setBaselineBudgetCost</a></span>(int&nbsp;baselineNumber,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline budget cost.</div>
</td>
</tr>
<tr id="i278" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineBudgetCost-java.lang.Number-">setBaselineBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the baseline budget cost.</div>
</td>
</tr>
<tr id="i279" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineBudgetWork-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the baseline budget work.</div>
</td>
</tr>
<tr id="i280" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineBudgetWork-int-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(int&nbsp;baselineNumber,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline budget work.</div>
</td>
</tr>
<tr id="i281" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineCost-int-java.lang.Number-">setBaselineCost</a></span>(int&nbsp;baselineNumber,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i282" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineCost-java.lang.Number-">setBaselineCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Baseline Cost field shows the total planned cost for a task.</div>
</td>
</tr>
<tr id="i283" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineDuration-org.mpxj.Duration-">setBaselineDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Baseline Duration field shows the original span of time planned to
 complete a task.</div>
</td>
</tr>
<tr id="i284" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineDuration-int-org.mpxj.Duration-">setBaselineDuration</a></span>(int&nbsp;baselineNumber,
                   <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i285" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineDurationText-int-java.lang.String-">setBaselineDurationText</a></span>(int&nbsp;baselineNumber,
                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline duration text value.</div>
</td>
</tr>
<tr id="i286" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineDurationText-java.lang.String-">setBaselineDurationText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline duration text value.</div>
</td>
</tr>
<tr id="i287" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedDuration-org.mpxj.Duration-">setBaselineEstimatedDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Set the baseline estimated duration.</div>
</td>
</tr>
<tr id="i288" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedDuration-int-org.mpxj.Duration-">setBaselineEstimatedDuration</a></span>(int&nbsp;baselineNumber,
                            <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i289" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedFinish-int-java.time.LocalDateTime-">setBaselineEstimatedFinish</a></span>(int&nbsp;baselineNumber,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i290" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedFinish-java.time.LocalDateTime-">setBaselineEstimatedFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the baseline estimated finish.</div>
</td>
</tr>
<tr id="i291" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedStart-int-java.time.LocalDateTime-">setBaselineEstimatedStart</a></span>(int&nbsp;baselineNumber,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i292" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineEstimatedStart-java.time.LocalDateTime-">setBaselineEstimatedStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the baseline estimated start.</div>
</td>
</tr>
<tr id="i293" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFinish-int-java.time.LocalDateTime-">setBaselineFinish</a></span>(int&nbsp;baselineNumber,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i294" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFinish-java.time.LocalDateTime-">setBaselineFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Baseline Finish field shows the planned completion date for a
 task at the time
 you saved a baseline.</div>
</td>
</tr>
<tr id="i295" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFinishText-int-java.lang.String-">setBaselineFinishText</a></span>(int&nbsp;baselineNumber,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline finish text value.</div>
</td>
</tr>
<tr id="i296" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFinishText-java.lang.String-">setBaselineFinishText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline finish text value.</div>
</td>
</tr>
<tr id="i297" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFixedCost-int-java.lang.Number-">setBaselineFixedCost</a></span>(int&nbsp;baselineNumber,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i298" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFixedCost-java.lang.Number-">setBaselineFixedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
</td>
</tr>
<tr id="i299" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFixedCostAccrual-org.mpxj.AccrueType-">setBaselineFixedCostAccrual</a></span>(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</code>
<div class="block">Sets the baseline fixed cost accrual.</div>
</td>
</tr>
<tr id="i300" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineFixedCostAccrual-int-org.mpxj.AccrueType-">setBaselineFixedCostAccrual</a></span>(int&nbsp;baselineNumber,
                           <a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i301" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineStart-int-java.time.LocalDateTime-">setBaselineStart</a></span>(int&nbsp;baselineNumber,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i302" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineStart-java.time.LocalDateTime-">setBaselineStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Baseline Start field shows the planned beginning date for a task at
 the time
 you saved a baseline.</div>
</td>
</tr>
<tr id="i303" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineStartText-int-java.lang.String-">setBaselineStartText</a></span>(int&nbsp;baselineNumber,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline start text value.</div>
</td>
</tr>
<tr id="i304" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineStartText-java.lang.String-">setBaselineStartText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the baseline start text value.</div>
</td>
</tr>
<tr id="i305" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineWork-org.mpxj.Duration-">setBaselineWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Baseline Work field shows the originally planned amount of work to
 be performed
 by all resources assigned to a task.</div>
</td>
</tr>
<tr id="i306" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBaselineWork-int-org.mpxj.Duration-">setBaselineWork</a></span>(int&nbsp;baselineNumber,
               <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i307" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBCWP-java.lang.Number-">setBCWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The BCWP (budgeted cost of work performed) field contains the
 cumulative value
 of the assignment's timephased percent complete multiplied by
 the assignments
 timephased baseline cost.</div>
</td>
</tr>
<tr id="i308" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBCWS-java.lang.Number-">setBCWS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
</td>
</tr>
<tr id="i309" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBidItem-java.lang.String-">setBidItem</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Retrieve the bid item field.</div>
</td>
</tr>
<tr id="i310" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBoardStatusID-java.lang.Integer-">setBoardStatusID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the Board Status ID.</div>
</td>
</tr>
<tr id="i311" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBudgetCost-java.lang.Number-">setBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the budget cost.</div>
</td>
</tr>
<tr id="i312" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setBudgetWork-org.mpxj.Duration-">setBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the budget work.</div>
</td>
</tr>
<tr id="i313" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCalendar-org.mpxj.ProjectCalendar-">setCalendar</a></span>(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">Sets the calendar associated with this task.</div>
</td>
</tr>
<tr id="i314" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCalendarUniqueID-java.lang.Integer-">setCalendarUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the calendar unique ID.</div>
</td>
</tr>
<tr id="i315" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCategoryOfWork-java.lang.String-">setCategoryOfWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the category of work field.</div>
</td>
</tr>
<tr id="i316" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCompleteThrough-java.time.LocalDateTime-">setCompleteThrough</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the "complete through" date.</div>
</td>
</tr>
<tr id="i317" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setConfirmed-boolean-">setConfirmed</a></span>(boolean&nbsp;val)</code>
<div class="block">The Confirmed field indicates whether all resources assigned to a task have
 accepted or rejected the task assignment in response to a TeamAssign message
 regarding their assignments.</div>
</td>
</tr>
<tr id="i318" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setConstraintDate-java.time.LocalDateTime-">setConstraintDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Constraint Date field shows the specific date associated with certain
 constraint types,
  such as Must Start On, Must Finish On, Start No Earlier Than,
  Start No Later Than,
  Finish No Earlier Than, and Finish No Later Than.</div>
</td>
</tr>
<tr id="i319" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setConstraintType-org.mpxj.ConstraintType-">setConstraintType</a></span>(<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;type)</code>
<div class="block">Private method for dealing with string parameters from File.</div>
</td>
</tr>
<tr id="i320" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setContact-java.lang.String-">setContact</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Contact field contains the name of an individual
 responsible for a task.</div>
</td>
</tr>
<tr id="i321" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCost-int-java.lang.Number-">setCost</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a cost value.</div>
</td>
</tr>
<tr id="i322" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCost-java.lang.Number-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Cost field shows the total scheduled, or projected, cost for a task,
 based on costs already incurred for work performed by all resources assigned
 to the task, in addition to the costs planned for the remaining work for the
 assignment.</div>
</td>
</tr>
<tr id="i323" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCostVariance-java.lang.Number-">setCostVariance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Cost Variance field shows the difference between the
 baseline cost and total cost for a task.</div>
</td>
</tr>
<tr id="i324" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCreateDate-java.time.LocalDateTime-">setCreateDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Created field contains the date and time when a task was
 added to the project.</div>
</td>
</tr>
<tr id="i325" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCritical-boolean-">setCritical</a></span>(boolean&nbsp;val)</code>
<div class="block">The Critical field indicates whether a task has any room in the
 schedule to slip,
 or if a task is on the critical path.</div>
</td>
</tr>
<tr id="i326" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setCV-java.lang.Number-">setCV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The CV (earned value cost variance) field shows the difference
 between how much it should have cost to achieve the current level of
 completion on the task, and how much it has actually cost to achieve the
 current level of completion up to the status date or today's date.</div>
</td>
</tr>
<tr id="i327" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDate-int-java.time.LocalDateTime-">setDate</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a date value.</div>
</td>
</tr>
<tr id="i328" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDeadline-java.time.LocalDateTime-">setDeadline</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;deadline)</code>
<div class="block">This method sets the deadline for this task.</div>
</td>
</tr>
<tr id="i329" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDepartment-java.lang.String-">setDepartment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the department field.</div>
</td>
</tr>
<tr id="i330" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDuration-org.mpxj.Duration-">setDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Duration field is the total span of active working time for a task.</div>
</td>
</tr>
<tr id="i331" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDuration-int-org.mpxj.Duration-">setDuration</a></span>(int&nbsp;index,
           <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a duration value.</div>
</td>
</tr>
<tr id="i332" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDurationText-java.lang.String-">setDurationText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Set the duration text used for a manually scheduled task.</div>
</td>
</tr>
<tr id="i333" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setDurationVariance-org.mpxj.Duration-">setDurationVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">The Duration Variance field contains the difference between the
 baseline duration of a task and the forecast or actual duration
 of the task.</div>
</td>
</tr>
<tr id="i334" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEarlyFinish-java.time.LocalDateTime-">setEarlyFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The Early Finish field contains the earliest date that a task
 could possibly finish, based on early finish dates of predecessor
 and successor tasks, other constraints, and any leveling delay.</div>
</td>
</tr>
<tr id="i335" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEarlyStart-java.time.LocalDateTime-">setEarlyStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The Early Start field contains the earliest date that a task could
 possibly begin, based on the early start dates of predecessor and
 successor tasks, and other constraints.</div>
</td>
</tr>
<tr id="i336" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEarnedValueMethod-org.mpxj.EarnedValueMethod-">setEarnedValueMethod</a></span>(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;earnedValueMethod)</code>
<div class="block">Sets the earned value method.</div>
</td>
</tr>
<tr id="i337" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEffortDriven-boolean-">setEffortDriven</a></span>(boolean&nbsp;flag)</code>
<div class="block">Sets the effort driven flag.</div>
</td>
</tr>
<tr id="i338" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseCost-int-java.lang.Number-">setEnterpriseCost</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i339" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseDate-int-java.time.LocalDateTime-">setEnterpriseDate</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i340" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseDuration-int-org.mpxj.Duration-">setEnterpriseDuration</a></span>(int&nbsp;index,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i341" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseFlag-int-boolean-">setEnterpriseFlag</a></span>(int&nbsp;index,
                 boolean&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i342" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseNumber-int-java.lang.Number-">setEnterpriseNumber</a></span>(int&nbsp;index,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i343" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEnterpriseText-int-java.lang.String-">setEnterpriseText</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i344" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setEstimated-boolean-">setEstimated</a></span>(boolean&nbsp;estimated)</code>
<div class="block">This method retrieves a flag indicating whether the duration of the
 task has only been estimated.</div>
</td>
</tr>
<tr id="i345" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExpanded-boolean-">setExpanded</a></span>(boolean&nbsp;expanded)</code>
<div class="block">Set a flag indicating if the task is shown as expanded
 in MS Project.</div>
</td>
</tr>
<tr id="i346" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExpectedFinish-java.time.LocalDateTime-">setExpectedFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the expected finish date.</div>
</td>
</tr>
<tr id="i347" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExpenseItems-java.util.List-">setExpenseItems</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ExpenseItem.html" title="class in org.mpxj">ExpenseItem</a>&gt;&nbsp;items)</code>
<div class="block">Set the expense items for this task.</div>
</td>
</tr>
<tr id="i348" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExternalEarlyStart-java.time.LocalDateTime-">setExternalEarlyStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the external early start date.</div>
</td>
</tr>
<tr id="i349" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExternalLateFinish-java.time.LocalDateTime-">setExternalLateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the external late finish date.</div>
</td>
</tr>
<tr id="i350" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setExternalTask-boolean-">setExternalTask</a></span>(boolean&nbsp;externalTask)</code>
<div class="block">Sets the external task flag.</div>
</td>
</tr>
<tr id="i351" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFeatureOfWork-java.lang.String-">setFeatureOfWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the feature of work field.</div>
</td>
</tr>
<tr id="i352" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFieldByAlias-java.lang.String-java.lang.Object-">setFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Set the value of a field using its alias.</div>
</td>
</tr>
<tr id="i353" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFinish-int-java.time.LocalDateTime-">setFinish</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a finish value.</div>
</td>
</tr>
<tr id="i354" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFinish-java.time.LocalDateTime-">setFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The Finish field shows the date and time that a task is scheduled to be
 completed.</div>
</td>
</tr>
<tr id="i355" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFinishSlack-org.mpxj.Duration-">setFinishSlack</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Set the finish slack.</div>
</td>
</tr>
<tr id="i356" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFinishText-java.lang.String-">setFinishText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Set the finish text used for a manually scheduled task.</div>
</td>
</tr>
<tr id="i357" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFinishVariance-org.mpxj.Duration-">setFinishVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">The Finish Variance field contains the amount of time that represents the
 difference between a task's baseline finish date and its forecast
 or actual finish date.</div>
</td>
</tr>
<tr id="i358" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFixedCost-java.lang.Number-">setFixedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
</td>
</tr>
<tr id="i359" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFixedCostAccrual-org.mpxj.AccrueType-">setFixedCostAccrual</a></span>(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</code>
<div class="block">Sets the fixed cost accrual flag value.</div>
</td>
</tr>
<tr id="i360" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFlag-int-boolean-">setFlag</a></span>(int&nbsp;index,
       boolean&nbsp;value)</code>
<div class="block">Set a flag value.</div>
</td>
</tr>
<tr id="i361" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFloatPath-java.lang.Integer-">setFloatPath</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the float path number.</div>
</td>
</tr>
<tr id="i362" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFloatPathOrder-java.lang.Integer-">setFloatPathOrder</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the float path order.</div>
</td>
</tr>
<tr id="i363" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setFreeSlack-org.mpxj.Duration-">setFreeSlack</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">The Free Slack field contains the amount of time that a task can be
 delayed without delaying any successor tasks.</div>
</td>
</tr>
<tr id="i364" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Set the task GUID.</div>
</td>
</tr>
<tr id="i365" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHammockCode-boolean-">setHammockCode</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the hammock code field.</div>
</td>
</tr>
<tr id="i366" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHideBar-boolean-">setHideBar</a></span>(boolean&nbsp;flag)</code>
<div class="block">The Hide Bar flag indicates whether the Gantt bars and Calendar bars
 for a task are hidden when this project's data is displayed in MS Project.</div>
</td>
</tr>
<tr id="i367" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHyperlink-java.lang.String-">setHyperlink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink attribute.</div>
</td>
</tr>
<tr id="i368" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHyperlinkAddress-java.lang.String-">setHyperlinkAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink address attribute.</div>
</td>
</tr>
<tr id="i369" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHyperlinkScreenTip-java.lang.String-">setHyperlinkScreenTip</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i370" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setHyperlinkSubAddress-java.lang.String-">setHyperlinkSubAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the task hyperlink sub address attribute.</div>
</td>
</tr>
<tr id="i371" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setID-java.lang.Integer-">setID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">The ID field contains the identifier number that Microsoft Project
 automatically assigns to each task as you add it to the project.</div>
</td>
</tr>
<tr id="i372" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setIgnoreResourceCalendar-boolean-">setIgnoreResourceCalendar</a></span>(boolean&nbsp;ignoreResourceCalendar)</code>
<div class="block">Sets the ignore resource calendar flag.</div>
</td>
</tr>
<tr id="i373" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLateFinish-java.time.LocalDateTime-">setLateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The Late Finish field contains the latest date that a task can finish
 without delaying the finish of the project.</div>
</td>
</tr>
<tr id="i374" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLateStart-java.time.LocalDateTime-">setLateStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The Late Start field contains the latest date that a task can start
 without delaying the finish of the project.</div>
</td>
</tr>
<tr id="i375" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLevelAssignments-boolean-">setLevelAssignments</a></span>(boolean&nbsp;flag)</code>
<div class="block">Sets the level assignments flag.</div>
</td>
</tr>
<tr id="i376" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLevelingCanSplit-boolean-">setLevelingCanSplit</a></span>(boolean&nbsp;flag)</code>
<div class="block">Sets the leveling can split flag.</div>
</td>
</tr>
<tr id="i377" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLevelingDelay-org.mpxj.Duration-">setLevelingDelay</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">Set amount of delay as elapsed real time.</div>
</td>
</tr>
<tr id="i378" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLevelingDelayFormat-org.mpxj.TimeUnit-">setLevelingDelayFormat</a></span>(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;levelingDelayFormat)</code>
<div class="block">Set the leveling delay format.</div>
</td>
</tr>
<tr id="i379" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLinkedFields-boolean-">setLinkedFields</a></span>(boolean&nbsp;flag)</code>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project
 file, or from another program.</div>
</td>
</tr>
<tr id="i380" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLocation-org.mpxj.Location-">setLocation</a></span>(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</code>
<div class="block">Sets the location.</div>
</td>
</tr>
<tr id="i381" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLocationUniqueID-java.lang.Integer-">setLocationUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the location unique ID.</div>
</td>
</tr>
<tr id="i382" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setLongestPath-boolean-">setLongestPath</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the longest path.</div>
</td>
</tr>
<tr id="i383" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setMail-java.lang.String-">setMail</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the mail field.</div>
</td>
</tr>
<tr id="i384" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setManager-java.lang.String-">setManager</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the manager field.</div>
</td>
</tr>
<tr id="i385" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setManualDuration-org.mpxj.Duration-">setManualDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</code>
<div class="block">Set the manual duration attribute.</div>
</td>
</tr>
<tr id="i386" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setMarked-boolean-">setMarked</a></span>(boolean&nbsp;flag)</code>
<div class="block">This is a user defined field used to mark a task for some form of
 additional action.</div>
</td>
</tr>
<tr id="i387" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setMethodologyGUID-java.util.UUID-">setMethodologyGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Set the methodology GUID for this task.</div>
</td>
</tr>
<tr id="i388" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setMilestone-boolean-">setMilestone</a></span>(boolean&nbsp;flag)</code>
<div class="block">The Milestone field indicates whether a task is a milestone.</div>
</td>
</tr>
<tr id="i389" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setModOrClaimNumber-java.lang.String-">setModOrClaimNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Retrieve the mod or claim number field.</div>
</td>
</tr>
<tr id="i390" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">The Name field contains the name of a task.</div>
</td>
</tr>
<tr id="i391" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</code>
<div class="block">This method is used to add notes to the current task.</div>
</td>
</tr>
<tr id="i392" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setNotesObject-org.mpxj.Notes-">setNotesObject</a></span>(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</code>
<div class="block">Set the Notes instance representing the task notes.</div>
</td>
</tr>
<tr id="i393" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setNull-boolean-">setNull</a></span>(boolean&nbsp;isNull)</code>
<div class="block">Sets the flag indicating if this is a null task.</div>
</td>
</tr>
<tr id="i394" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setNumber-int-java.lang.Number-">setNumber</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a number value.</div>
</td>
</tr>
<tr id="i395" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setObjects-java.lang.Integer-">setObjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">The Objects field contains the number of objects attached to a task.</div>
</td>
</tr>
<tr id="i396" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOutlineCode-int-java.lang.String-">setOutlineCode</a></span>(int&nbsp;index,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set an outline code value.</div>
</td>
</tr>
<tr id="i397" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOutlineLevel-java.lang.Integer-">setOutlineLevel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">The Outline Level field contains the number that indicates the level of
 the task in the project outline hierarchy.</div>
</td>
</tr>
<tr id="i398" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOutlineNumber-java.lang.String-">setOutlineNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Outline Number field contains the number of the task in the structure
 of an outline.</div>
</td>
</tr>
<tr id="i399" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOverAllocated-boolean-">setOverAllocated</a></span>(boolean&nbsp;overAllocated)</code>
<div class="block">Set the over allocated flag.</div>
</td>
</tr>
<tr id="i400" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOverallPercentComplete-java.lang.Number-">setOverallPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the overall percent complete field.</div>
</td>
</tr>
<tr id="i401" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOvertimeCost-java.lang.Number-">setOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;number)</code>
<div class="block">Sets the overtime cost value.</div>
</td>
</tr>
<tr id="i402" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setOvertimeWork-org.mpxj.Duration-">setOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the overtime work attribute.</div>
</td>
</tr>
<tr id="i403" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPercentageComplete-java.lang.Number-">setPercentageComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The % Complete field contains the current status of a task, expressed
 as the percentage of the
 task's duration that has been completed.</div>
</td>
</tr>
<tr id="i404" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPercentageWorkComplete-java.lang.Number-">setPercentageWorkComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the
 percentage of the task's work that has been completed.</div>
</td>
</tr>
<tr id="i405" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPercentCompleteType-org.mpxj.PercentCompleteType-">setPercentCompleteType</a></span>(<a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj">PercentCompleteType</a>&nbsp;value)</code>
<div class="block">Set the percent complete type.</div>
</td>
</tr>
<tr id="i406" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPhaseOfWork-java.lang.String-">setPhaseOfWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the phase of work field.</div>
</td>
</tr>
<tr id="i407" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPhysicalPercentComplete-java.lang.Number-">setPhysicalPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;physicalPercentComplete)</code>
<div class="block">Sets the physical percent complete value.</div>
</td>
</tr>
<tr id="i408" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedCost-java.lang.Number-">setPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the planned cost field.</div>
</td>
</tr>
<tr id="i409" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedDuration-org.mpxj.Duration-">setPlannedDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the planned duration field.</div>
</td>
</tr>
<tr id="i410" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedFinish-java.time.LocalDateTime-">setPlannedFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the planned finish field.</div>
</td>
</tr>
<tr id="i411" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedStart-java.time.LocalDateTime-">setPlannedStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the planned start field.</div>
</td>
</tr>
<tr id="i412" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedWork-org.mpxj.Duration-">setPlannedWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the planned work field.</div>
</td>
</tr>
<tr id="i413" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedWorkLabor-org.mpxj.Duration-">setPlannedWorkLabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the labor component of the task's Planned Work.</div>
</td>
</tr>
<tr id="i414" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPlannedWorkNonlabor-org.mpxj.Duration-">setPlannedWorkNonlabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the nonlabor component of the task's Planned Work.</div>
</td>
</tr>
<tr id="i415" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPreleveledFinish-java.time.LocalDateTime-">setPreleveledFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Sets the preleveled finish attribute.</div>
</td>
</tr>
<tr id="i416" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPreleveledStart-java.time.LocalDateTime-">setPreleveledStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Sets the preleveled start attribute.</div>
</td>
</tr>
<tr id="i417" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPrimaryResource-org.mpxj.Resource-">setPrimaryResource</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">Set the primary resource for this task.</div>
</td>
</tr>
<tr id="i418" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPrimaryResourceUniqueID-java.lang.Integer-">setPrimaryResourceUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the primary resource unique ID.</div>
</td>
</tr>
<tr id="i419" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setPriority-org.mpxj.Priority-">setPriority</a></span>(<a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;priority)</code>
<div class="block">The Priority field provides choices for the level of importance
 assigned to a task, which in turn indicates how readily a task can be
 delayed or split during resource leveling.</div>
</td>
</tr>
<tr id="i420" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setProject-java.lang.String-">setProject</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Project field shows the name of the project from which a
 task originated.</div>
</td>
</tr>
<tr id="i421" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRecurring-boolean-">setRecurring</a></span>(boolean&nbsp;recurring)</code>
<div class="block">Set the recurring flag.</div>
</td>
</tr>
<tr id="i422" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRegularWork-org.mpxj.Duration-">setRegularWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;regularWork)</code>
<div class="block">Set the amount of regular work.</div>
</td>
</tr>
<tr id="i423" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingCost-java.lang.Number-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The Remaining Cost field shows the remaining scheduled expense of a task that
 will be incurred in completing the remaining scheduled work by all resources
 assigned to the task.</div>
</td>
</tr>
<tr id="i424" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingDuration-org.mpxj.Duration-">setRemainingDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Remaining Duration field shows the amount of time required to complete
 the unfinished portion of a task.</div>
</td>
</tr>
<tr id="i425" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingEarlyFinish-java.time.LocalDateTime-">setRemainingEarlyFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The date the resource is scheduled to finish the remaining work for the activity.</div>
</td>
</tr>
<tr id="i426" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingEarlyStart-java.time.LocalDateTime-">setRemainingEarlyStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">The date the resource is scheduled to begin the remaining work for the activity.</div>
</td>
</tr>
<tr id="i427" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingLateFinish-java.time.LocalDateTime-">setRemainingLateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining late finish value.</div>
</td>
</tr>
<tr id="i428" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingLateStart-java.time.LocalDateTime-">setRemainingLateStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the remaining late start value.</div>
</td>
</tr>
<tr id="i429" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingOvertimeCost-java.lang.Number-">setRemainingOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the remaining overtime cost value.</div>
</td>
</tr>
<tr id="i430" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingOvertimeWork-org.mpxj.Duration-">setRemainingOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the remaining overtime work attribute.</div>
</td>
</tr>
<tr id="i431" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingWork-org.mpxj.Duration-">setRemainingWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Remaining Work field shows the amount of time, or person-hours,
 still required by all assigned resources to complete a task.</div>
</td>
</tr>
<tr id="i432" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingWorkLabor-org.mpxj.Duration-">setRemainingWorkLabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the labor component of the task's Remaining Work.</div>
</td>
</tr>
<tr id="i433" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRemainingWorkNonlabor-org.mpxj.Duration-">setRemainingWorkNonlabor</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the nonlabor component of the task's Remaining Work.</div>
</td>
</tr>
<tr id="i434" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResourceGroup-java.lang.String-">setResourceGroup</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Resource Group field contains the list of resource groups to which the
 resources assigned to a task belong.</div>
</td>
</tr>
<tr id="i435" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResourceInitials-java.lang.String-">setResourceInitials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Resource Initials field lists the abbreviations for the names of
 resources assigned to a task.</div>
</td>
</tr>
<tr id="i436" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResourceNames-java.lang.String-">setResourceNames</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Resource Names field lists the names of all resources
 assigned to a task.</div>
</td>
</tr>
<tr id="i437" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResponsePending-boolean-">setResponsePending</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the response pending flag.</div>
</td>
</tr>
<tr id="i438" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResponsibilityCode-java.lang.String-">setResponsibilityCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the responsibility code field.</div>
</td>
</tr>
<tr id="i439" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResume-java.time.LocalDateTime-">setResume</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Resume field shows the date that the remaining portion of a task is
 scheduled to resume after you enter a new value for the % Complete field.</div>
</td>
</tr>
<tr id="i440" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setResumeValid-boolean-">setResumeValid</a></span>(boolean&nbsp;resumeValid)</code>
<div class="block">Set the resume valid flag.</div>
</td>
</tr>
<tr id="i441" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setRollup-boolean-">setRollup</a></span>(boolean&nbsp;val)</code>
<div class="block">For subtasks, the Rollup field indicates whether information on the subtask
 Gantt bars will be rolled up to the summary task bar.</div>
</td>
</tr>
<tr id="i442" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setScheduledDuration-org.mpxj.Duration-">setScheduledDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the scheduled duration.</div>
</td>
</tr>
<tr id="i443" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setScheduledFinish-java.time.LocalDateTime-">setScheduledFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the scheduled finish.</div>
</td>
</tr>
<tr id="i444" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setScheduledStart-java.time.LocalDateTime-">setScheduledStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the scheduled start.</div>
</td>
</tr>
<tr id="i445" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSecondaryConstraintDate-java.time.LocalDateTime-">setSecondaryConstraintDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Set the secondary constraint date.</div>
</td>
</tr>
<tr id="i446" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSecondaryConstraintType-org.mpxj.ConstraintType-">setSecondaryConstraintType</a></span>(<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;type)</code>
<div class="block">Set the secondary constraint type.</div>
</td>
</tr>
<tr id="i447" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSection-java.lang.String-">setSection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the section field.</div>
</td>
</tr>
<tr id="i448" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSequenceNumber-java.lang.Integer-">setSequenceNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;sequenceNumber)</code>
<div class="block">Set this task's sequence number.</div>
</td>
</tr>
<tr id="i449" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSplits-java.util.List-">setSplits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/LocalDateTimeRange.html" title="class in org.mpxj">LocalDateTimeRange</a>&gt;&nbsp;splits)</code>
<div class="block">Internal method used to set the list of splits.</div>
</td>
</tr>
<tr id="i450" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSprintID-java.lang.Integer-">setSprintID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the sprint ID.</div>
</td>
</tr>
<tr id="i451" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStart-int-java.time.LocalDateTime-">setStart</a></span>(int&nbsp;index,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a start value.</div>
</td>
</tr>
<tr id="i452" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStart-java.time.LocalDateTime-">setStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Start field shows the date and time that a task is scheduled to begin.</div>
</td>
</tr>
<tr id="i453" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStartSlack-org.mpxj.Duration-">setStartSlack</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Set the start slack.</div>
</td>
</tr>
<tr id="i454" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStartText-java.lang.String-">setStartText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Set the start text used for a manually scheduled task.</div>
</td>
</tr>
<tr id="i455" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStartVariance-org.mpxj.Duration-">setStartVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Start Variance field contains the amount of time that represents the
 difference between a task's baseline start date and its currently
 scheduled start date.</div>
</td>
</tr>
<tr id="i456" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSteps-java.util.List-">setSteps</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Step.html" title="class in org.mpxj">Step</a>&gt;&nbsp;steps)</code>
<div class="block">Set the steps for this task.</div>
</td>
</tr>
<tr id="i457" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStop-java.time.LocalDateTime-">setStop</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</code>
<div class="block">The Stop field shows the date that represents the end of the actual
 portion of a task.</div>
</td>
</tr>
<tr id="i458" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setStoredMaterial-java.lang.Number-">setStoredMaterial</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the stored material value for this task.</div>
</td>
</tr>
<tr id="i459" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectFile-java.lang.String-">setSubprojectFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The Subproject File field contains the external
 project's path and file name.</div>
</td>
</tr>
<tr id="i460" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectGUID-java.util.UUID-">setSubprojectGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</code>
<div class="block">Set the GUID of the linked subproject file.</div>
</td>
</tr>
<tr id="i461" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectObject-org.mpxj.ProjectFile-">setSubprojectObject</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</code>
<div class="block">Where we have already read a project, this method is used to
 attach it to the task.</div>
</td>
</tr>
<tr id="i462" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectReadOnly-boolean-">setSubprojectReadOnly</a></span>(boolean&nbsp;subprojectReadOnly)</code>
<div class="block">Set the subproject read only flag.</div>
</td>
</tr>
<tr id="i463" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectTaskID-java.lang.Integer-">setSubprojectTaskID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectTaskID)</code>
<div class="block">Sets the sub project task ID.</div>
</td>
</tr>
<tr id="i464" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectTasksUniqueIDOffset-java.lang.Integer-">setSubprojectTasksUniqueIDOffset</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;offset)</code>
<div class="block">Sets the offset added to unique task IDs from sub projects
 to generate the task ID shown in the master project.</div>
</td>
</tr>
<tr id="i465" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSubprojectTaskUniqueID-java.lang.Integer-">setSubprojectTaskUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectUniqueTaskID)</code>
<div class="block">Sets the sub project unique task ID.</div>
</td>
</tr>
<tr id="i466" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSummary-boolean-">setSummary</a></span>(boolean&nbsp;val)</code>
<div class="block">The Summary field indicates whether a task is a summary task.</div>
</td>
</tr>
<tr id="i467" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSummaryProgress-java.time.LocalDateTime-">setSummaryProgress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the summary progress date.</div>
</td>
</tr>
<tr id="i468" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSuspendDate-java.time.LocalDateTime-">setSuspendDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the suspend date field.</div>
</td>
</tr>
<tr id="i469" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setSV-java.lang.Number-">setSV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">The SV (earned value schedule variance) field shows the difference
 in cost terms between the current progress and the baseline plan
 of the task up to the status date or today's date.</div>
</td>
</tr>
<tr id="i470" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setTaskMode-org.mpxj.TaskMode-">setTaskMode</a></span>(<a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a>&nbsp;mode)</code>
<div class="block">Sets the task mode.</div>
</td>
</tr>
<tr id="i471" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setText-int-java.lang.String-">setText</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set a text value.</div>
</td>
</tr>
<tr id="i472" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setTotalSlack-org.mpxj.Duration-">setTotalSlack</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Total Slack field contains the amount of time a task can be delayed
 without delaying the project's finish date.</div>
</td>
</tr>
<tr id="i473" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setType-org.mpxj.TaskType-">setType</a></span>(<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;type)</code>
<div class="block">This method sets the task type.</div>
</td>
</tr>
<tr id="i474" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setUniqueID-java.lang.Integer-">setUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">The Unique ID field contains the number that Microsoft Project
 automatically designates whenever a new task is created.</div>
</td>
</tr>
<tr id="i475" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setUpdateNeeded-boolean-">setUpdateNeeded</a></span>(boolean&nbsp;val)</code>
<div class="block">The Update Needed field indicates whether a TeamUpdate message should
 be sent to the assigned resources because of changes to the start date,
 finish date, or resource reassignments of the task.</div>
</td>
</tr>
<tr id="i476" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setWBS-java.lang.String-">setWBS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">The work breakdown structure code.</div>
</td>
</tr>
<tr id="i477" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setWork-org.mpxj.Duration-">setWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Work field shows the total amount of work scheduled to be performed
 on a task by all assigned resources.</div>
</td>
</tr>
<tr id="i478" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setWorkAreaCode-java.lang.String-">setWorkAreaCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the work area code field.</div>
</td>
</tr>
<tr id="i479" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setWorkersPerDay-java.lang.Integer-">setWorkersPerDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the workers per day field.</div>
</td>
</tr>
<tr id="i480" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#setWorkVariance-org.mpxj.Duration-">setWorkVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">The Work Variance field contains the difference between a task's baseline
 work and the currently scheduled work.</div>
</td>
</tr>
<tr id="i481" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Task.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.AbstractFieldContainer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a></h3>
<code><a href="../../org/mpxj/AbstractFieldContainer.html#addFieldListener-org.mpxj.listener.FieldListener-">addFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#disableEvents--">disableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#enableEvents--">enableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#get-org.mpxj.FieldType-">get</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#removeFieldListener-org.mpxj.listener.FieldListener-">removeFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="generateWBS-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateWBS</h4>
<pre>public&nbsp;void&nbsp;generateWBS(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;parent)</pre>
<div class="block">This method is used to automatically generate a value
 for the WBS field of this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>parent</code> - Parent Task</dd>
</dl>
</li>
</ul>
<a name="generateOutlineNumber-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateOutlineNumber</h4>
<pre>public&nbsp;void&nbsp;generateOutlineNumber(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;parent)</pre>
<div class="block">This method is used to automatically generate a value
 for the Outline Number field of this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>parent</code> - Parent Task</dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</pre>
<div class="block">This method is used to add notes to the current task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - notes to be added</dd>
</dl>
</li>
</ul>
<a name="setNotesObject-org.mpxj.Notes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotesObject</h4>
<pre>public&nbsp;void&nbsp;setNotesObject(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</pre>
<div class="block">Set the Notes instance representing the task notes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - Notes instance</dd>
</dl>
</li>
</ul>
<a name="addTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;addTask()</pre>
<div class="block">This method allows nested tasks to be added, with the WBS being
 completed automatically.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ChildTaskContainer.html#addTask--">addTask</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ChildTaskContainer.html" title="interface in org.mpxj">ChildTaskContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new task</dd>
</dl>
</li>
</ul>
<a name="addChildTask-org.mpxj.Task-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChildTask</h4>
<pre>public&nbsp;void&nbsp;addChildTask(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child,
                         int&nbsp;childOutlineLevel)</pre>
<div class="block">This method is used to associate a child task with the current
 task instance. It has package access, and has been designed to
 allow the hierarchical outline structure of tasks in an MPX
 file to be constructed as the file is read in.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - Child task.</dd>
<dd><code>childOutlineLevel</code> - Outline level of the child task.</dd>
</dl>
</li>
</ul>
<a name="addChildTask-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChildTask</h4>
<pre>public&nbsp;void&nbsp;addChildTask(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child)</pre>
<div class="block">This method is used to associate a child task with the current
 task instance. It has been designed to
 allow the hierarchical outline structure of tasks in an MPX
 file to be updated once all of the task data has been read.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - child task</dd>
</dl>
</li>
</ul>
<a name="addChildTaskBefore-org.mpxj.Task-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChildTaskBefore</h4>
<pre>public&nbsp;void&nbsp;addChildTaskBefore(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child,
                               <a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;previousSibling)</pre>
<div class="block">Inserts a child task prior to a given sibling task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - new child task</dd>
<dd><code>previousSibling</code> - sibling task</dd>
</dl>
</li>
</ul>
<a name="removeChildTask-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeChildTask</h4>
<pre>public&nbsp;void&nbsp;removeChildTask(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;child)</pre>
<div class="block">Removes a child task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - child task instance</dd>
</dl>
</li>
</ul>
<a name="clearChildTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearChildTasks</h4>
<pre>public&nbsp;void&nbsp;clearChildTasks()</pre>
<div class="block">This method allows the list of child tasks to be cleared in preparation
 for the hierarchical task structure to be built.</div>
</li>
</ul>
<a name="addRecurringTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRecurringTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/RecurringTask.html" title="class in org.mpxj">RecurringTask</a>&nbsp;addRecurringTask()</pre>
<div class="block">This method allows recurring task details to be added to the
 current task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>RecurringTask object</dd>
</dl>
</li>
</ul>
<a name="getRecurringTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecurringTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/RecurringTask.html" title="class in org.mpxj">RecurringTask</a>&nbsp;getRecurringTask()</pre>
<div class="block">This method retrieves the recurring task record. If the current
 task is not a recurring task, then this method will return null.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Recurring task record.</dd>
</dl>
</li>
</ul>
<a name="getActivityCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityCodeValues</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ActivityCode.html" title="class in org.mpxj">ActivityCode</a>,<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&gt;&nbsp;getActivityCodeValues()</pre>
<div class="block">Retrieve the activity code values associated with this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>map of activity code values</dd>
</dl>
</li>
</ul>
<a name="addActivityCodeValue-org.mpxj.ActivityCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addActivityCodeValue</h4>
<pre>public&nbsp;void&nbsp;addActivityCodeValue(<a href="../../org/mpxj/ActivityCodeValue.html" title="class in org.mpxj">ActivityCodeValue</a>&nbsp;value)</pre>
<div class="block">Assign an activity code value to this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity code value</dd>
</dl>
</li>
</ul>
<a name="addResourceAssignment-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceAssignment</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;addResourceAssignment(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">This method allows a resource assignment to be added to the
 current task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - the resource to assign</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ResourceAssignment object</dd>
</dl>
</li>
</ul>
<a name="addResourceAssignment-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceAssignment</h4>
<pre>public&nbsp;void&nbsp;addResourceAssignment(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</pre>
<div class="block">Add a resource assignment which has been populated elsewhere.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>assignment</code> - resource assignment</dd>
</dl>
</li>
</ul>
<a name="getExistingResourceAssignment-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExistingResourceAssignment</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;getExistingResourceAssignment(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">Retrieves an existing resource assignment if one is present,
 to prevent duplicate resource assignments being added.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource to test for</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>existing resource assignment</dd>
</dl>
</li>
</ul>
<a name="getResourceAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceAssignments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;&nbsp;getResourceAssignments()</pre>
<div class="block">This method allows the list of resource assignments for this
 task to be retrieved.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of resource assignments</dd>
</dl>
</li>
</ul>
<a name="addPredecessor-org.mpxj.Relation.Builder-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPredecessor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;addPredecessor(<a href="../../org/mpxj/Relation.Builder.html" title="class in org.mpxj">Relation.Builder</a>&nbsp;builder)</pre>
<div class="block">This method allows a predecessor relationship to be added to this
 task instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>builder</code> - Relation.Builder instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Relation instance</dd>
</dl>
</li>
</ul>
<a name="removePredecessor-org.mpxj.Task-org.mpxj.RelationType-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePredecessor</h4>
<pre>public&nbsp;boolean&nbsp;removePredecessor(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;targetTask,
                                 <a href="../../org/mpxj/RelationType.html" title="enum in org.mpxj">RelationType</a>&nbsp;type,
                                 <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;lag)</pre>
<div class="block">This method allows a predecessor relationship to be removed from this
 task instance.  It will only delete relationships that exactly match the
 given targetTask, type and lag time.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>targetTask</code> - the predecessor task</dd>
<dd><code>type</code> - relation type</dd>
<dd><code>lag</code> - relation lag</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>returns true if the relation is found and removed</dd>
</dl>
</li>
</ul>
<a name="setPercentageComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentageComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentageComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The % Complete field contains the current status of a task, expressed
 as the percentage of the
 task's duration that has been completed. You can enter percent complete,
 or you can have
 Microsoft Project calculate it for you based on actual duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setPercentageWorkComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentageWorkComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentageWorkComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the
 percentage of the task's work that has been completed. You can enter
 percent work
 complete, or you can have Microsoft Project calculate it for you
 based on actual
 work on the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Actual Cost field shows costs incurred for work already performed
 by all resources
 on a task, along with any other recorded costs associated with the task.
 You can enter
 all the actual costs or have Microsoft Project calculate them for you.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setActualDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualDuration</h4>
<pre>public&nbsp;void&nbsp;setActualDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Actual Duration field shows the span of actual working time for a
 task so far,
 based on the scheduled duration and current remaining work or
 completion percentage.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setActualFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualFinish</h4>
<pre>public&nbsp;void&nbsp;setActualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Actual Finish field shows the date and time that a task actually
 finished.
 Microsoft Project sets the Actual Finish field to the scheduled finish
 date if
 the completion percentage is 100. This field contains "NA" until you
 enter actual
 information or set the completion percentage to 100.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setActualStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualStart</h4>
<pre>public&nbsp;void&nbsp;setActualStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Actual Start field shows the date and time that a task actually began.
 When a task is first created, the Actual Start field contains "NA." Once you
 enter the first actual work or a completion percentage for a task, Microsoft
 Project sets the actual start date to the scheduled start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setActualWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWork</h4>
<pre>public&nbsp;void&nbsp;setActualWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Actual Work field shows the amount of work that has already been
 done by the
 resources assigned to a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Baseline Cost field shows the total planned cost for a task.
 Baseline cost is also referred to as budget at completion (BAC).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the amount to be set</dd>
</dl>
</li>
</ul>
<a name="setBaselineDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDuration</h4>
<pre>public&nbsp;void&nbsp;setBaselineDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Baseline Duration field shows the original span of time planned to
 complete a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - duration</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Baseline Finish field shows the planned completion date for a
 task at the time
 you saved a baseline. Information in this field becomes available
 when you set a
 baseline for a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - Date to be set</dd>
</dl>
</li>
</ul>
<a name="setBaselineStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Baseline Start field shows the planned beginning date for a task at
 the time
 you saved a baseline. Information in this field becomes available when you
 set a baseline.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - Date to be set</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Baseline Work field shows the originally planned amount of work to
 be performed
 by all resources assigned to a task. This field shows the planned
 person-hours
 scheduled for a task. Information in the Baseline Work field
 becomes available
 when you set a baseline for the project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the duration to be set.</dd>
</dl>
</li>
</ul>
<a name="setBCWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWP</h4>
<pre>public&nbsp;void&nbsp;setBCWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The BCWP (budgeted cost of work performed) field contains the
 cumulative value
 of the assignment's timephased percent complete multiplied by
 the assignments
 timephased baseline cost. BCWP is calculated up to the status
 date or today's
 date. This information is also known as earned value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the amount to be set</dd>
</dl>
</li>
</ul>
<a name="setBCWS-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWS</h4>
<pre>public&nbsp;void&nbsp;setBCWS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - the amount to set</dd>
</dl>
</li>
</ul>
<a name="setConfirmed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConfirmed</h4>
<pre>public&nbsp;void&nbsp;setConfirmed(boolean&nbsp;val)</pre>
<div class="block">The Confirmed field indicates whether all resources assigned to a task have
 accepted or rejected the task assignment in response to a TeamAssign message
 regarding their assignments.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="setConstraintDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConstraintDate</h4>
<pre>public&nbsp;void&nbsp;setConstraintDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Constraint Date field shows the specific date associated with certain
 constraint types,
  such as Must Start On, Must Finish On, Start No Earlier Than,
  Start No Later Than,
  Finish No Earlier Than, and Finish No Later Than.
  SEE class constants</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - Date to be set</dd>
</dl>
</li>
</ul>
<a name="setSecondaryConstraintDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSecondaryConstraintDate</h4>
<pre>public&nbsp;void&nbsp;setSecondaryConstraintDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the secondary constraint date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - secondary constraint date</dd>
</dl>
</li>
</ul>
<a name="setConstraintType-org.mpxj.ConstraintType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConstraintType</h4>
<pre>public&nbsp;void&nbsp;setConstraintType(<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;type)</pre>
<div class="block">Private method for dealing with string parameters from File.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - string constraint type</dd>
</dl>
</li>
</ul>
<a name="setSecondaryConstraintType-org.mpxj.ConstraintType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSecondaryConstraintType</h4>
<pre>public&nbsp;void&nbsp;setSecondaryConstraintType(<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;type)</pre>
<div class="block">Set the secondary constraint type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - secondary constraint type</dd>
</dl>
</li>
</ul>
<a name="setContact-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContact</h4>
<pre>public&nbsp;void&nbsp;setContact(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Contact field contains the name of an individual
 responsible for a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Cost field shows the total scheduled, or projected, cost for a task,
 based on costs already incurred for work performed by all resources assigned
 to the task, in addition to the costs planned for the remaining work for the
 assignment. This can also be referred to as estimate at completion (EAC).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - amount</dd>
</dl>
</li>
</ul>
<a name="setCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dd><code>value</code> - cost value</dd>
</dl>
</li>
</ul>
<a name="getCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost(int&nbsp;index)</pre>
<div class="block">Retrieve a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost value</dd>
</dl>
</li>
</ul>
<a name="setCostVariance-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostVariance</h4>
<pre>public&nbsp;void&nbsp;setCostVariance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Cost Variance field shows the difference between the
 baseline cost and total cost for a task. The total cost is the
 current estimate of costs based on actual costs and remaining costs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - amount</dd>
</dl>
</li>
</ul>
<a name="setCreateDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreateDate</h4>
<pre>public&nbsp;void&nbsp;setCreateDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Created field contains the date and time when a task was
 added to the project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - date</dd>
</dl>
</li>
</ul>
<a name="setCritical-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCritical</h4>
<pre>public&nbsp;void&nbsp;setCritical(boolean&nbsp;val)</pre>
<div class="block">The Critical field indicates whether a task has any room in the
 schedule to slip,
 or if a task is on the critical path. The Critical field contains
 Yes if the task
 is critical and No if the task is not critical.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - whether task is critical or not</dd>
</dl>
</li>
</ul>
<a name="setCV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCV</h4>
<pre>public&nbsp;void&nbsp;setCV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The CV (earned value cost variance) field shows the difference
 between how much it should have cost to achieve the current level of
 completion on the task, and how much it has actually cost to achieve the
 current level of completion up to the status date or today's date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value to set</dd>
</dl>
</li>
</ul>
<a name="setLevelingDelay-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelingDelay</h4>
<pre>public&nbsp;void&nbsp;setLevelingDelay(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">Set amount of delay as elapsed real time.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - elapsed time</dd>
</dl>
</li>
</ul>
<a name="setDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;void&nbsp;setDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Duration field is the total span of active working time for a task.
 This is generally the amount of time from the start to the finish of a task.
 The default for new tasks is 1 day (1d).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - duration</dd>
</dl>
</li>
</ul>
<a name="setDurationText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationText</h4>
<pre>public&nbsp;void&nbsp;setDurationText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Set the duration text used for a manually scheduled task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - text</dd>
</dl>
</li>
</ul>
<a name="setManualDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManualDuration</h4>
<pre>public&nbsp;void&nbsp;setManualDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;dur)</pre>
<div class="block">Set the manual duration attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dur</code> - manual duration</dd>
</dl>
</li>
</ul>
<a name="getManualDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManualDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getManualDuration()</pre>
<div class="block">Read the manual duration attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>manual duration</dd>
</dl>
</li>
</ul>
<a name="setDurationVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationVariance</h4>
<pre>public&nbsp;void&nbsp;setDurationVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">The Duration Variance field contains the difference between the
 baseline duration of a task and the forecast or actual duration
 of the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="setEarlyFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarlyFinish</h4>
<pre>public&nbsp;void&nbsp;setEarlyFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The Early Finish field contains the earliest date that a task
 could possibly finish, based on early finish dates of predecessor
 and successor tasks, other constraints, and any leveling delay.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - Date value</dd>
</dl>
</li>
</ul>
<a name="setRemainingEarlyFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingEarlyFinish</h4>
<pre>public&nbsp;void&nbsp;setRemainingEarlyFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The date the resource is scheduled to finish the remaining work for the activity.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - Date value</dd>
</dl>
</li>
</ul>
<a name="setEarlyStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarlyStart</h4>
<pre>public&nbsp;void&nbsp;setEarlyStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The Early Start field contains the earliest date that a task could
 possibly begin, based on the early start dates of predecessor and
 successor tasks, and other constraints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - Date value</dd>
</dl>
</li>
</ul>
<a name="setRemainingEarlyStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingEarlyStart</h4>
<pre>public&nbsp;void&nbsp;setRemainingEarlyStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The date the resource is scheduled to begin the remaining work for the activity.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - Date value</dd>
</dl>
</li>
</ul>
<a name="setFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The Finish field shows the date and time that a task is scheduled to be
 completed. MS project allows a finish date to be entered, and will
 calculate the duration, or a duration can be supplied and MS Project
 will calculate the finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - Date value</dd>
</dl>
</li>
</ul>
<a name="setFinishText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishText</h4>
<pre>public&nbsp;void&nbsp;setFinishText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Set the finish text used for a manually scheduled task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - text</dd>
</dl>
</li>
</ul>
<a name="setFinishVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishVariance</h4>
<pre>public&nbsp;void&nbsp;setFinishVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">The Finish Variance field contains the amount of time that represents the
 difference between a task's baseline finish date and its forecast
 or actual finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="setFixedCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFixedCost</h4>
<pre>public&nbsp;void&nbsp;setFixedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - amount</dd>
</dl>
</li>
</ul>
<a name="setFreeSlack-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFreeSlack</h4>
<pre>public&nbsp;void&nbsp;setFreeSlack(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">The Free Slack field contains the amount of time that a task can be
 delayed without delaying any successor tasks. If the task has no
 successors, free slack is the amount of time that a task can be delayed
 without delaying the entire project's finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="setHideBar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHideBar</h4>
<pre>public&nbsp;void&nbsp;setHideBar(boolean&nbsp;flag)</pre>
<div class="block">The Hide Bar flag indicates whether the Gantt bars and Calendar bars
 for a task are hidden when this project's data is displayed in MS Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="setID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>public&nbsp;void&nbsp;setID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">The ID field contains the identifier number that Microsoft Project
 automatically assigns to each task as you add it to the project.
 The ID indicates the position of a task with respect to the other tasks.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithID.html#setID-java.lang.Integer-">setID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - ID</dd>
</dl>
</li>
</ul>
<a name="setLateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLateFinish</h4>
<pre>public&nbsp;void&nbsp;setLateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The Late Finish field contains the latest date that a task can finish
 without delaying the finish of the project. This date is based on the
 task's late start date, as well as the late start and late finish dates
 of predecessor and successor tasks, and other constraints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - date value</dd>
</dl>
</li>
</ul>
<a name="setRemainingLateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateFinish</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining late finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining late finish</dd>
</dl>
</li>
</ul>
<a name="setLateStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLateStart</h4>
<pre>public&nbsp;void&nbsp;setLateStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">The Late Start field contains the latest date that a task can start
 without delaying the finish of the project. This date is based on the
 task's start date, as well as the late start and late finish dates of
 predecessor and successor tasks, and other constraints.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - date value</dd>
</dl>
</li>
</ul>
<a name="setRemainingLateStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateStart</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the remaining late start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - remaining late start</dd>
</dl>
</li>
</ul>
<a name="setLinkedFields-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkedFields</h4>
<pre>public&nbsp;void&nbsp;setLinkedFields(boolean&nbsp;flag)</pre>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project
 file, or from another program.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="setMarked-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarked</h4>
<pre>public&nbsp;void&nbsp;setMarked(boolean&nbsp;flag)</pre>
<div class="block">This is a user defined field used to mark a task for some form of
 additional action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="setMilestone-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMilestone</h4>
<pre>public&nbsp;void&nbsp;setMilestone(boolean&nbsp;flag)</pre>
<div class="block">The Milestone field indicates whether a task is a milestone.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">The Name field contains the name of a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - task name</dd>
</dl>
</li>
</ul>
<a name="setObjects-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObjects</h4>
<pre>public&nbsp;void&nbsp;setObjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">The Objects field contains the number of objects attached to a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - integer value</dd>
</dl>
</li>
</ul>
<a name="setOutlineLevel-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineLevel</h4>
<pre>public&nbsp;void&nbsp;setOutlineLevel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">The Outline Level field contains the number that indicates the level of
 the task in the project outline hierarchy.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - int</dd>
</dl>
</li>
</ul>
<a name="setOutlineNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineNumber</h4>
<pre>public&nbsp;void&nbsp;setOutlineNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Outline Number field contains the number of the task in the structure
 of an outline. This number indicates the task's position within the
 hierarchical structure of the project outline. The outline number is
 similar to a WBS (work breakdown structure) number, except that the
 outline number is automatically entered by Microsoft Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - text</dd>
</dl>
</li>
</ul>
<a name="setPriority-org.mpxj.Priority-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>public&nbsp;void&nbsp;setPriority(<a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;priority)</pre>
<div class="block">The Priority field provides choices for the level of importance
 assigned to a task, which in turn indicates how readily a task can be
 delayed or split during resource leveling.
 The default priority is Medium. Those tasks with a priority
 of Do Not Level are never delayed or split when Microsoft Project levels
 tasks that have overallocated resources assigned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>priority</code> - the priority value</dd>
</dl>
</li>
</ul>
<a name="setProject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProject</h4>
<pre>public&nbsp;void&nbsp;setProject(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Project field shows the name of the project from which a
 task originated.
 This can be the name of the active project file. If there are
 other projects
 inserted into the active project file, the name of the
 inserted project appears
 in this field for the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - text</dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Remaining Cost field shows the remaining scheduled expense of a task that
 will be incurred in completing the remaining scheduled work by all resources
 assigned to the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - currency amount</dd>
</dl>
</li>
</ul>
<a name="setRemainingDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingDuration</h4>
<pre>public&nbsp;void&nbsp;setRemainingDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Remaining Duration field shows the amount of time required to complete
 the unfinished portion of a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration.</dd>
</dl>
</li>
</ul>
<a name="setRemainingWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Remaining Work field shows the amount of time, or person-hours,
 still required by all assigned resources to complete a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="setResourceGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceGroup</h4>
<pre>public&nbsp;void&nbsp;setResourceGroup(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Resource Group field contains the list of resource groups to which the
 resources assigned to a task belong.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - String list</dd>
</dl>
</li>
</ul>
<a name="setResourceInitials-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceInitials</h4>
<pre>public&nbsp;void&nbsp;setResourceInitials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Resource Initials field lists the abbreviations for the names of
 resources assigned to a task. These initials can serve as substitutes
 for the names.

 Note that MS Project 98 does not normally populate this field when
 it generates an MPX file, and will therefore not expect to see values
 in this field when it reads an MPX file. Supplying values for this
 field will cause MS Project 98, 2000, and 2002 to create new resources
 and ignore any other resource assignments that have been defined
 in the MPX file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - String containing a comma separated list of initials</dd>
</dl>
</li>
</ul>
<a name="setResourceNames-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceNames</h4>
<pre>public&nbsp;void&nbsp;setResourceNames(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Resource Names field lists the names of all resources
 assigned to a task.

 Note that MS Project 98 does not normally populate this field when
 it generates an MPX file, and will therefore not expect to see values
 in this field when it reads an MPX file. Supplying values for this
 field when writing an MPX file will cause MS Project 98, 2000, and 2002
 to create new resources and ignore any other resource assignments
 that have been defined in the MPX file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - String containing a comma separated list of names</dd>
</dl>
</li>
</ul>
<a name="setResume-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResume</h4>
<pre>public&nbsp;void&nbsp;setResume(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Resume field shows the date that the remaining portion of a task is
 scheduled to resume after you enter a new value for the % Complete field.
 The Resume field is also recalculated when the remaining portion of a task
 is moved to a new date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - Date</dd>
</dl>
</li>
</ul>
<a name="setRollup-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRollup</h4>
<pre>public&nbsp;void&nbsp;setRollup(boolean&nbsp;val)</pre>
<div class="block">For subtasks, the Rollup field indicates whether information on the subtask
 Gantt bars will be rolled up to the summary task bar. For summary tasks, the
 Rollup field indicates whether the summary task bar displays rolled up bars.
 You must have the Rollup field for summary tasks set to Yes for any subtasks
 to roll up to them.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - boolean</dd>
</dl>
</li>
</ul>
<a name="setStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Start field shows the date and time that a task is scheduled to begin.
 You can enter the start date you want, to indicate the date when the task
 should begin. Or, you can have Microsoft Project calculate the start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - Date</dd>
</dl>
</li>
</ul>
<a name="setStartText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartText</h4>
<pre>public&nbsp;void&nbsp;setStartText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Set the start text used for a manually scheduled task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - text</dd>
</dl>
</li>
</ul>
<a name="setStartVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartVariance</h4>
<pre>public&nbsp;void&nbsp;setStartVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Start Variance field contains the amount of time that represents the
 difference between a task's baseline start date and its currently
 scheduled start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="setStop-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStop</h4>
<pre>public&nbsp;void&nbsp;setStop(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;val)</pre>
<div class="block">The Stop field shows the date that represents the end of the actual
 portion of a task. Typically, Microsoft Project calculates the stop date.
 However, you can edit this date as well.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - Date</dd>
</dl>
</li>
</ul>
<a name="setSubprojectFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectFile</h4>
<pre>public&nbsp;void&nbsp;setSubprojectFile(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The Subproject File field contains the external
 project's path and file name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - String</dd>
</dl>
</li>
</ul>
<a name="setSubprojectGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectGUID</h4>
<pre>public&nbsp;void&nbsp;setSubprojectGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</pre>
<div class="block">Set the GUID of the linked subproject file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>guid</code> - subproject GUID</dd>
</dl>
</li>
</ul>
<a name="getSubprojectGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getSubprojectGUID()</pre>
<div class="block">Retrieve the GUID of the linked subproject file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>subproject GUID</dd>
</dl>
</li>
</ul>
<a name="setSummary-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSummary</h4>
<pre>public&nbsp;void&nbsp;setSummary(boolean&nbsp;val)</pre>
<div class="block">The Summary field indicates whether a task is a summary task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - boolean</dd>
</dl>
</li>
</ul>
<a name="setSV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSV</h4>
<pre>public&nbsp;void&nbsp;setSV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The SV (earned value schedule variance) field shows the difference
 in cost terms between the current progress and the baseline plan
 of the task up to the status date or today's date. You can use SV
 to check costs to determine whether tasks are on schedule.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - currency amount</dd>
</dl>
</li>
</ul>
<a name="setTotalSlack-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotalSlack</h4>
<pre>public&nbsp;void&nbsp;setTotalSlack(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Total Slack field contains the amount of time a task can be delayed
 without delaying the project's finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="setUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUniqueID</h4>
<pre>public&nbsp;void&nbsp;setUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">The Unique ID field contains the number that Microsoft Project
 automatically designates whenever a new task is created.
 This number indicates the sequence in which the task was created,
 regardless of placement in the schedule.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html#setUniqueID-java.lang.Integer-">setUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - unique ID</dd>
</dl>
</li>
</ul>
<a name="setUpdateNeeded-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdateNeeded</h4>
<pre>public&nbsp;void&nbsp;setUpdateNeeded(boolean&nbsp;val)</pre>
<div class="block">The Update Needed field indicates whether a TeamUpdate message should
 be sent to the assigned resources because of changes to the start date,
 finish date, or resource reassignments of the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - boolean</dd>
</dl>
</li>
</ul>
<a name="setWBS-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWBS</h4>
<pre>public&nbsp;void&nbsp;setWBS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">The work breakdown structure code. The WBS field contains an alphanumeric
 code you can use to represent the task's position within the hierarchical
 structure of the project. This field is similar to the outline number,
 except that you can edit it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - String</dd>
</dl>
</li>
</ul>
<a name="setWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Work field shows the total amount of work scheduled to be performed
 on a task by all assigned resources. This field shows the total work,
 or person-hours, for a task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="setWorkVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkVariance</h4>
<pre>public&nbsp;void&nbsp;setWorkVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">The Work Variance field contains the difference between a task's baseline
 work and the currently scheduled work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - duration</dd>
</dl>
</li>
</ul>
<a name="getPercentageComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentageComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentageComplete()</pre>
<div class="block">The % Complete field contains the current status of a task,
 expressed as the percentage of the task's duration that has been completed.
 You can enter percent complete, or you can have Microsoft Project calculate
 it for you based on actual duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percentage as float</dd>
</dl>
</li>
</ul>
<a name="getPercentageWorkComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentageWorkComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentageWorkComplete()</pre>
<div class="block">The % Work Complete field contains the current status of a task,
 expressed as the percentage of the task's work that has been completed.
 You can enter percent work complete, or you can have Microsoft Project
 calculate it for you based on actual work on the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percentage as float</dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualCost()</pre>
<div class="block">The Actual Cost field shows costs incurred for work already performed
 by all resources on a task, along with any other recorded costs associated
 with the task. You can enter all the actual costs or have Microsoft Project
 calculate them for you.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="getActualDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualDuration()</pre>
<div class="block">The Actual Duration field shows the span of actual working time for a
 task so far, based on the scheduled duration and current remaining work
 or completion percentage.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration string</dd>
</dl>
</li>
</ul>
<a name="getActualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualFinish()</pre>
<div class="block">The Actual Finish field shows the date and time that a task actually
 finished. Microsoft Project sets the Actual Finish field to the scheduled
 finish date if the completion percentage is 100. This field contains "NA"
 until you enter actual information or set the completion percentage to 100.
 If "NA" is entered as value, arbitrary year zero Date is used. Date(0);</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getActualStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualStart()</pre>
<div class="block">The Actual Start field shows the date and time that a task actually began.
 When a task is first created, the Actual Start field contains "NA." Once
 you enter the first actual work or a completion percentage for a task,
 Microsoft Project sets the actual start date to the scheduled start date.
 If "NA" is entered as value, arbitrary year zero Date is used. Date(0);</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWork()</pre>
<div class="block">The Actual Work field shows the amount of work that has already been done
 by the resources assigned to a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration string</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost()</pre>
<div class="block">The Baseline Cost field shows the total planned cost for a task.
 Baseline cost is also referred to as budget at completion (BAC).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="getBaselineDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineDuration()</pre>
<div class="block">The Baseline Duration field shows the original span of time planned
 to complete a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>- duration string</dd>
</dl>
</li>
</ul>
<a name="getBaselineDurationText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDurationText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineDurationText()</pre>
<div class="block">Retrieves the text value for the baseline duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline duration text</dd>
</dl>
</li>
</ul>
<a name="setBaselineDurationText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDurationText</h4>
<pre>public&nbsp;void&nbsp;setBaselineDurationText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline duration text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline duration text</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineFinish()</pre>
<div class="block">The Baseline Finish field shows the planned completion date for a task
 at the time you saved a baseline. Information in this field becomes
 available when you set a baseline for a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinishText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinishText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineFinishText()</pre>
<div class="block">Retrieves the baseline finish text value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline finish text</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinishText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinishText</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinishText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline finish text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline finish text</dd>
</dl>
</li>
</ul>
<a name="getBaselineStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineStart()</pre>
<div class="block">The Baseline Start field shows the planned beginning date for a task at
 the time you saved a baseline. Information in this field becomes available
 when you set a baseline.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getBaselineStartText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStartText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineStartText()</pre>
<div class="block">Retrieves the baseline start text value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline start value</dd>
</dl>
</li>
</ul>
<a name="setBaselineStartText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStartText</h4>
<pre>public&nbsp;void&nbsp;setBaselineStartText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline start text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline start text</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork()</pre>
<div class="block">The Baseline Work field shows the originally planned amount of work to be
 performed by all resources assigned to a task. This field shows the planned
 person-hours scheduled for a task. Information in the Baseline Work field
 becomes available when you set a baseline for the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getBCWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWP()</pre>
<div class="block">The BCWP (budgeted cost of work performed) field contains
 the cumulative value of the assignment's timephased percent complete
 multiplied by the assignment's timephased baseline cost.
 BCWP is calculated up to the status date or today's date.
 This information is also known as earned value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="getBCWS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWS()</pre>
<div class="block">The BCWS (budgeted cost of work scheduled) field contains the cumulative
 timephased baseline costs up to the status date or today's date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount as float</dd>
</dl>
</li>
</ul>
<a name="getConfirmed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConfirmed</h4>
<pre>public&nbsp;boolean&nbsp;getConfirmed()</pre>
<div class="block">The Confirmed field indicates whether all resources assigned to a task
 have accepted or rejected the task assignment in response to a TeamAssign
 message regarding their assignments.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getConstraintDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConstraintDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getConstraintDate()</pre>
<div class="block">The Constraint Date field shows the specific date associated with certain
 constraint types, such as Must Start On, Must Finish On,
 Start No Earlier Than,
 Start No Later Than, Finish No Earlier Than, and Finish No Later Than.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getSecondaryConstraintDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSecondaryConstraintDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getSecondaryConstraintDate()</pre>
<div class="block">Retrieve the secondary constraint date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>secondary constraint date</dd>
</dl>
</li>
</ul>
<a name="getConstraintType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConstraintType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;getConstraintType()</pre>
<div class="block">The Constraint Type field provides choices for the type of constraint you
 can apply for scheduling a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>constraint type</dd>
</dl>
</li>
</ul>
<a name="getSecondaryConstraintType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSecondaryConstraintType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ConstraintType.html" title="enum in org.mpxj">ConstraintType</a>&nbsp;getSecondaryConstraintType()</pre>
<div class="block">Retrieve the secondary constraint type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>secondary constraint type</dd>
</dl>
</li>
</ul>
<a name="getContact--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContact</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getContact()</pre>
<div class="block">The Contact field contains the name of an individual
 responsible for a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String</dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost()</pre>
<div class="block">The Cost field shows the total scheduled, or projected, cost for a task,
 based on costs already incurred for work performed by all resources assigned
 to the task, in addition to the costs planned for the remaining work for the
 assignment. This can also be referred to as estimate at completion (EAC).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost amount</dd>
</dl>
</li>
</ul>
<a name="getCostVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostVariance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCostVariance()</pre>
<div class="block">The Cost Variance field shows the difference between the baseline cost
 and total cost for a task. The total cost is the current estimate of costs
 based on actual costs and remaining costs.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>amount</dd>
</dl>
</li>
</ul>
<a name="getCreateDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreateDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreateDate()</pre>
<div class="block">The Created field contains the date and time when a task was added
 to the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getCritical--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCritical</h4>
<pre>public&nbsp;boolean&nbsp;getCritical()</pre>
<div class="block">The Critical field indicates whether a task has any room in the schedule
 to slip, or if a task is on the critical path. The Critical field contains
 Yes if the task is critical and No if the task is not critical.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getCV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCV()</pre>
<div class="block">The CV (earned value cost variance) field shows the difference between
 how much it should have cost to achieve the current level of completion
 on the task, and how much it has actually cost to achieve the current
 level of completion up to the status date or today's date.
 How Calculated   CV is the difference between BCWP
 (budgeted cost of work performed) and ACWP
 (actual cost of work performed). Microsoft Project calculates
 the CV as follows: CV = BCWP - ACWP</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sum of earned value cost variance</dd>
</dl>
</li>
</ul>
<a name="getLevelingDelay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelingDelay</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getLevelingDelay()</pre>
<div class="block">Delay , in MPX files as eg '0ed'. Use duration</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDuration()</pre>
<div class="block">The Duration field is the total span of active working time for a task.
 This is generally the amount of time from the start to the finish of a task.
 The default for new tasks is 1 day (1d).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getDurationText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDurationText()</pre>
<div class="block">Retrieves the duration text of a manually scheduled task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration text</dd>
</dl>
</li>
</ul>
<a name="setDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;void&nbsp;setDuration(int&nbsp;index,
                        <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dd><code>value</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDuration(int&nbsp;index)</pre>
<div class="block">Retrieve a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration value</dd>
</dl>
</li>
</ul>
<a name="getDurationVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDurationVariance()</pre>
<div class="block">The Duration Variance field contains the difference between the
 baseline duration of a task and the total duration (current estimate)
 of a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getEarlyFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarlyFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEarlyFinish()</pre>
<div class="block">The Early Finish field contains the earliest date that a task could
 possibly finish, based on early finish dates of predecessor and
 successor tasks, other constraints, and any leveling delay.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getRemainingEarlyFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingEarlyFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingEarlyFinish()</pre>
<div class="block">The date the resource is scheduled to finish the remaining work for the activity.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getEarlyStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarlyStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEarlyStart()</pre>
<div class="block">The Early Start field contains the earliest date that a task could
 possibly begin, based on the early start dates of predecessor and
 successor tasks, and other constraints.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getRemainingEarlyStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingEarlyStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingEarlyStart()</pre>
<div class="block">The date the resource is scheduled to start the remaining work for the activity.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish()</pre>
<div class="block">The Finish field shows the date and time that a task is scheduled to
 be completed. You can enter the finish date you want, to indicate the
 date when the task should be completed. Or, you can have Microsoft
 Project calculate the finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getFinishText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFinishText()</pre>
<div class="block">Retrieves the finish text of a manually scheduled task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish text</dd>
</dl>
</li>
</ul>
<a name="setFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dd><code>value</code> - finish value</dd>
</dl>
</li>
</ul>
<a name="getFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish(int&nbsp;index)</pre>
<div class="block">Retrieve a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish value</dd>
</dl>
</li>
</ul>
<a name="getFinishVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getFinishVariance()</pre>
<div class="block">Calculate the finish variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish variance</dd>
</dl>
</li>
</ul>
<a name="getFixedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFixedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getFixedCost()</pre>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount</dd>
</dl>
</li>
</ul>
<a name="setFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFlag</h4>
<pre>public&nbsp;void&nbsp;setFlag(int&nbsp;index,
                    boolean&nbsp;value)</pre>
<div class="block">Set a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dd><code>value</code> - flag value</dd>
</dl>
</li>
</ul>
<a name="getFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlag</h4>
<pre>public&nbsp;boolean&nbsp;getFlag(int&nbsp;index)</pre>
<div class="block">Retrieve a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>flag value</dd>
</dl>
</li>
</ul>
<a name="getFreeSlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFreeSlack</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getFreeSlack()</pre>
<div class="block">The Free Slack field contains the amount of time that a task can be
 delayed without delaying any successor tasks. If the task has no
 successors, free slack is the amount of time that a task can be
 delayed without delaying the entire project's finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getHideBar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideBar</h4>
<pre>public&nbsp;boolean&nbsp;getHideBar()</pre>
<div class="block">The Hide Bar field indicates whether the Gantt bars and Calendar bars
 for a task are hidden. Click Yes in the Hide Bar field to hide the
 bar for the task. Click No in the Hide Bar field to show the bar
 for the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getID()</pre>
<div class="block">The ID field contains the identifier number that Microsoft Project
 automatically assigns to each task as you add it to the project.
 The ID indicates the position of a task with respect to the other tasks.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithID.html#getID--">getID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the task ID</dd>
</dl>
</li>
</ul>
<a name="getLateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLateFinish()</pre>
<div class="block">The Late Finish field contains the latest date that a task can finish
 without delaying the finish of the project. This date is based on the
 task's late start date, as well as the late start and late finish
 dates of predecessor and successor
 tasks, and other constraints.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getRemainingLateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateFinish()</pre>
<div class="block">Retrieve the remaining late finish value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining late finish</dd>
</dl>
</li>
</ul>
<a name="getLateStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLateStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLateStart()</pre>
<div class="block">The Late Start field contains the latest date that a task can start
 without delaying the finish of the project. This date is based on
 the task's start date, as well as the late start and late finish
 dates of predecessor and successor tasks, and other constraints.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getRemainingLateStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateStart()</pre>
<div class="block">Retrieve the remaining late start value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining late start</dd>
</dl>
</li>
</ul>
<a name="getLinkedFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkedFields</h4>
<pre>public&nbsp;boolean&nbsp;getLinkedFields()</pre>
<div class="block">The Linked Fields field indicates whether there are OLE links to the task,
 either from elsewhere in the active project, another Microsoft Project file,
 or from another program.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getMarked--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarked</h4>
<pre>public&nbsp;boolean&nbsp;getMarked()</pre>
<div class="block">The Marked field indicates whether a task is marked for further action or
 identification of some kind. To mark a task, click Yes in the Marked field.
 If you don't want a task marked, click No.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true for marked</dd>
</dl>
</li>
</ul>
<a name="getMilestone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMilestone</h4>
<pre>public&nbsp;boolean&nbsp;getMilestone()</pre>
<div class="block">The Milestone field indicates whether a task is a milestone.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Retrieves the task name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task name</dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Retrieve the plain text representation of the task notes.
 Use the getNotesObject method to retrieve an object which
 contains both the plain text notes and, if relevant,
 the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>notes</dd>
</dl>
</li>
</ul>
<a name="getNotesObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;getNotesObject()</pre>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Notes instance</dd>
</dl>
</li>
</ul>
<a name="setNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumber</h4>
<pre>public&nbsp;void&nbsp;setNumber(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dd><code>value</code> - number value</dd>
</dl>
</li>
</ul>
<a name="getNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getNumber(int&nbsp;index)</pre>
<div class="block">Retrieve a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number value</dd>
</dl>
</li>
</ul>
<a name="getObjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getObjects()</pre>
<div class="block">The Objects field contains the number of objects attached to a task.
 Microsoft Project counts the number of objects linked or embedded to a task.
 However, objects in the Notes box in the Resource Form are not included
 in this count.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int</dd>
</dl>
</li>
</ul>
<a name="getOutlineLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineLevel</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getOutlineLevel()</pre>
<div class="block">The Outline Level field contains the number that indicates the level
 of the task in the project outline hierarchy.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int</dd>
</dl>
</li>
</ul>
<a name="getOutlineNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOutlineNumber()</pre>
<div class="block">The Outline Number field contains the number of the task in the structure
 of an outline. This number indicates the task's position within the
 hierarchical structure of the project outline. The outline number is
 similar to a WBS (work breakdown structure) number,
 except that the outline number is automatically entered by
 Microsoft Project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String</dd>
</dl>
</li>
</ul>
<a name="getPredecessors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPredecessors</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&gt;&nbsp;getPredecessors()</pre>
<div class="block">Retrieves the list of predecessors for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of predecessor Relation instances</dd>
</dl>
</li>
</ul>
<a name="getSuccessors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuccessors</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&gt;&nbsp;getSuccessors()</pre>
<div class="block">Retrieves the list of successors for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of successor Relation instances</dd>
</dl>
</li>
</ul>
<a name="getPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriority</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;getPriority()</pre>
<div class="block">The Priority field provides choices for the level of importance
 assigned to a task, which in turn indicates how readily a task can be
 delayed or split during resource leveling.
 The default priority is Medium. Those tasks with a priority
 of Do Not Level are never delayed or split when Microsoft Project levels
 tasks that have overallocated resources assigned.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>priority class instance</dd>
</dl>
</li>
</ul>
<a name="getProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProject</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProject()</pre>
<div class="block">The Project field shows the name of the project from which a task
 originated.
 This can be the name of the active project file. If there are other
 projects inserted
 into the active project file, the name of the inserted project appears
 in this field
 for the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>name of originating project</dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingCost()</pre>
<div class="block">The Remaining Cost field shows the remaining scheduled expense of a
 task that will be incurred in completing the remaining scheduled work
 by all resources assigned to the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining cost</dd>
</dl>
</li>
</ul>
<a name="getRemainingDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingDuration()</pre>
<div class="block">The Remaining Duration field shows the amount of time required
 to complete the unfinished portion of a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration</dd>
</dl>
</li>
</ul>
<a name="getRemainingWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingWork()</pre>
<div class="block">The Remaining Work field shows the amount of time, or person-hours,
 still required by all assigned resources to complete a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the amount of time still required to complete a task</dd>
</dl>
</li>
</ul>
<a name="getResourceGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceGroup</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceGroup()</pre>
<div class="block">The Resource Group field contains the list of resource groups to which
 the resources assigned to a task belong.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>single string list of groups</dd>
</dl>
</li>
</ul>
<a name="getResourceInitials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceInitials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceInitials()</pre>
<div class="block">The Resource Initials field lists the abbreviations for the names of
 resources assigned to a task. These initials can serve as substitutes
 for the names.

 Note that MS Project 98 does not export values for this field when
 writing an MPX file, and the field is not currently populated by MPXJ
 when reading an MPP file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String containing a comma separated list of initials</dd>
</dl>
</li>
</ul>
<a name="getResourceNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceNames</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceNames()</pre>
<div class="block">The Resource Names field lists the names of all resources assigned
 to a task.

 Note that MS Project 98 does not export values for this field when
 writing an MPX file, and the field is not currently populated by MPXJ
 when reading an MPP file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String containing a comma separated list of names</dd>
</dl>
</li>
</ul>
<a name="getResume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResume</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getResume()</pre>
<div class="block">The Resume field shows the date that the remaining portion of a task
 is scheduled to resume after you enter a new value for the % Complete
 field. The Resume field is also recalculated when the remaining portion
 of a task is moved to a new date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getRollup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRollup</h4>
<pre>public&nbsp;boolean&nbsp;getRollup()</pre>
<div class="block">For subtasks, the Rollup field indicates whether information on the
 subtask Gantt bars
 will be rolled up to the summary task bar. For summary tasks, the
 Rollup field indicates
 whether the summary task bar displays rolled up bars. You must
 have the Rollup field for
 summary tasks set to Yes for any subtasks to roll up to them.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="getStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart()</pre>
<div class="block">The Start field shows the date and time that a task is scheduled to begin.
 You can enter the start date you want, to indicate the date when the task
 should begin. Or, you can have Microsoft Project calculate the start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getStartText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getStartText()</pre>
<div class="block">Retrieve the start text for a manually scheduled task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start text</dd>
</dl>
</li>
</ul>
<a name="setStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(int&nbsp;index,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dd><code>value</code> - start value</dd>
</dl>
</li>
</ul>
<a name="getStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart(int&nbsp;index)</pre>
<div class="block">Retrieve a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start value</dd>
</dl>
</li>
</ul>
<a name="getStartVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getStartVariance()</pre>
<div class="block">Calculate the start variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start variance</dd>
</dl>
</li>
</ul>
<a name="getStop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStop</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStop()</pre>
<div class="block">The Stop field shows the date that represents the end of the actual
 portion of a task. Typically, Microsoft Project calculates the stop date.
 However, you can edit this date as well.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date</dd>
</dl>
</li>
</ul>
<a name="getSubprojectFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectFile</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSubprojectFile()</pre>
<div class="block">Contains the file name and path of the external project linked
 to this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>subproject file path</dd>
</dl>
</li>
</ul>
<a name="getSummary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummary</h4>
<pre>public&nbsp;boolean&nbsp;getSummary()</pre>
<div class="block">The Summary field indicates whether a task is a summary task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, true-is summary task</dd>
</dl>
</li>
</ul>
<a name="getSV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getSV()</pre>
<div class="block">The SV (earned value schedule variance) field shows the difference in
 cost terms between the current progress and the baseline plan of the
 task up to the status date or today's date. You can use SV to
 check costs to determine whether tasks are on schedule.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>-earned value schedule variance</dd>
</dl>
</li>
</ul>
<a name="setText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dd><code>value</code> - text value</dd>
</dl>
</li>
</ul>
<a name="getText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getText(int&nbsp;index)</pre>
<div class="block">Retrieve a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>text value</dd>
</dl>
</li>
</ul>
<a name="setOutlineCode-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineCode</h4>
<pre>public&nbsp;void&nbsp;setOutlineCode(int&nbsp;index,
                           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set an outline code value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - outline code index (1-10)</dd>
<dd><code>value</code> - outline code value</dd>
</dl>
</li>
</ul>
<a name="getOutlineCode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOutlineCode(int&nbsp;index)</pre>
<div class="block">Retrieve an outline code value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - outline code index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>outline code value</dd>
</dl>
</li>
</ul>
<a name="getTotalSlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalSlack</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getTotalSlack()</pre>
<div class="block">The Total Slack field contains the amount of time a task can be
 delayed without delaying the project's finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representing duration</dd>
</dl>
</li>
</ul>
<a name="getUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUniqueID()</pre>
<div class="block">The Unique ID field contains the number that Microsoft Project
 automatically designates whenever a new task is created. This number
 indicates the sequence in which the task was
 created, regardless of placement in the schedule.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html#getUniqueID--">getUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String</dd>
</dl>
</li>
</ul>
<a name="getUpdateNeeded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdateNeeded</h4>
<pre>public&nbsp;boolean&nbsp;getUpdateNeeded()</pre>
<div class="block">The Update Needed field indicates whether a TeamUpdate message
 should be sent to the assigned resources because of changes to the
 start date, finish date, or resource reassignments of the task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if needed.</dd>
</dl>
</li>
</ul>
<a name="getWBS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWBS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWBS()</pre>
<div class="block">The work breakdown structure code. The WBS field contains an
 alphanumeric code you can use to represent the task's position within
 the hierarchical structure of the project. This field is similar to
 the outline number, except that you can edit it.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string</dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWork()</pre>
<div class="block">The Work field shows the total amount of work scheduled to be performed
 on a task by all assigned resources. This field shows the total work,
 or person-hours, for a task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration representing duration .</dd>
</dl>
</li>
</ul>
<a name="getWorkVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWorkVariance()</pre>
<div class="block">The Work Variance field contains the difference between a task's
 baseline work and the currently scheduled work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Duration representing duration.</dd>
</dl>
</li>
</ul>
<a name="getParentTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;getParentTask()</pre>
<div class="block">This method retrieves a reference to the parent of this task, as
 defined by the outline level. If this task is at the top level,
 this method will return null.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>parent task</dd>
</dl>
</li>
</ul>
<a name="getParentTaskUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentTaskUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getParentTaskUniqueID()</pre>
<div class="block">Retrieve the unique ID of the parent task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>parent task unique ID, or null if there is no parent class</dd>
</dl>
</li>
</ul>
<a name="getChildTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChildTasks</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;&nbsp;getChildTasks()</pre>
<div class="block">This method retrieves a list of child tasks relative to the
 current task, as defined by the outline level. If there
 are no child tasks, this method will return an empty list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ChildTaskContainer.html#getChildTasks--">getChildTasks</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ChildTaskContainer.html" title="interface in org.mpxj">ChildTaskContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>child tasks</dd>
</dl>
</li>
</ul>
<a name="compareTo-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareTo</h4>
<pre>public&nbsp;int&nbsp;compareTo(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;o)</pre>
<div class="block">This method implements the only method in the Comparable interface.
 This allows Tasks to be compared and sorted based on their ID value.
 Note that if the MPX/MPP file has been generated by MSP, the ID value
 will always be in the correct sequence. The Unique ID value will not
 necessarily be in the correct sequence as task insertions and deletions
 will change the order.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - object to compare this instance with</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>result of comparison</dd>
</dl>
</li>
</ul>
<a name="getEstimated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimated</h4>
<pre>public&nbsp;boolean&nbsp;getEstimated()</pre>
<div class="block">This method retrieves a flag indicating whether the duration of the
 task has only been estimated.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean</dd>
</dl>
</li>
</ul>
<a name="setEstimated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimated</h4>
<pre>public&nbsp;void&nbsp;setEstimated(boolean&nbsp;estimated)</pre>
<div class="block">This method retrieves a flag indicating whether the duration of the
 task has only been estimated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>estimated</code> - Boolean flag</dd>
</dl>
</li>
</ul>
<a name="getDeadline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeadline</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDeadline()</pre>
<div class="block">This method retrieves the deadline for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Task deadline</dd>
</dl>
</li>
</ul>
<a name="setDeadline-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDeadline</h4>
<pre>public&nbsp;void&nbsp;setDeadline(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;deadline)</pre>
<div class="block">This method sets the deadline for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>deadline</code> - deadline date</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;getType()</pre>
<div class="block">This method retrieves the task type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int representing the task type</dd>
</dl>
</li>
</ul>
<a name="setType-org.mpxj.TaskType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;type)</pre>
<div class="block">This method sets the task type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - task type</dd>
</dl>
</li>
</ul>
<a name="getNull--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNull</h4>
<pre>public&nbsp;boolean&nbsp;getNull()</pre>
<div class="block">Retrieves the flag indicating if this is a null task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setNull-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNull</h4>
<pre>public&nbsp;void&nbsp;setNull(boolean&nbsp;isNull)</pre>
<div class="block">Sets the flag indicating if this is a null task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isNull</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getResumeValid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResumeValid</h4>
<pre>public&nbsp;boolean&nbsp;getResumeValid()</pre>
<div class="block">Retrieve the resume valid flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resume valid flag</dd>
</dl>
</li>
</ul>
<a name="setResumeValid-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResumeValid</h4>
<pre>public&nbsp;void&nbsp;setResumeValid(boolean&nbsp;resumeValid)</pre>
<div class="block">Set the resume valid flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resumeValid</code> - resume valid flag</dd>
</dl>
</li>
</ul>
<a name="getRecurring--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecurring</h4>
<pre>public&nbsp;boolean&nbsp;getRecurring()</pre>
<div class="block">Retrieve the recurring flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>recurring flag</dd>
</dl>
</li>
</ul>
<a name="setRecurring-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRecurring</h4>
<pre>public&nbsp;void&nbsp;setRecurring(boolean&nbsp;recurring)</pre>
<div class="block">Set the recurring flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>recurring</code> - recurring flag</dd>
</dl>
</li>
</ul>
<a name="getOverAllocated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverAllocated</h4>
<pre>public&nbsp;boolean&nbsp;getOverAllocated()</pre>
<div class="block">Retrieve the over allocated flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>over allocated flag</dd>
</dl>
</li>
</ul>
<a name="setOverAllocated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverAllocated</h4>
<pre>public&nbsp;void&nbsp;setOverAllocated(boolean&nbsp;overAllocated)</pre>
<div class="block">Set the over allocated flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overAllocated</code> - over allocated flag</dd>
</dl>
</li>
</ul>
<a name="getSubprojectTaskUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectTaskUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSubprojectTaskUniqueID()</pre>
<div class="block">Where a task in an MPP file represents a task from a subproject,
 this value will be non-zero. The value itself is the unique ID
 value shown in the parent project. To retrieve the value of the
 task unique ID in the child project, remove the top two bytes:

 taskID = (subprojectUniqueID &amp; 0xFFFF)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sub project unique task ID</dd>
</dl>
</li>
</ul>
<a name="setSubprojectTaskUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectTaskUniqueID</h4>
<pre>public&nbsp;void&nbsp;setSubprojectTaskUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectUniqueTaskID)</pre>
<div class="block">Sets the sub project unique task ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subprojectUniqueTaskID</code> - subproject unique task ID</dd>
</dl>
</li>
</ul>
<a name="getSubprojectTaskID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectTaskID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSubprojectTaskID()</pre>
<div class="block">Where a task in an MPP file represents a task from a subproject,
 this value will be non-zero. The value itself is the ID
 value shown in the parent project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sub project task ID</dd>
</dl>
</li>
</ul>
<a name="setSubprojectTaskID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectTaskID</h4>
<pre>public&nbsp;void&nbsp;setSubprojectTaskID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectTaskID)</pre>
<div class="block">Sets the sub project task ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subprojectTaskID</code> - subproject task ID</dd>
</dl>
</li>
</ul>
<a name="setSubprojectTasksUniqueIDOffset-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectTasksUniqueIDOffset</h4>
<pre>public&nbsp;void&nbsp;setSubprojectTasksUniqueIDOffset(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;offset)</pre>
<div class="block">Sets the offset added to unique task IDs from sub projects
 to generate the task ID shown in the master project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>offset</code> - unique ID offset</dd>
</dl>
</li>
</ul>
<a name="getSubprojectTasksUniqueIDOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectTasksUniqueIDOffset</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSubprojectTasksUniqueIDOffset()</pre>
<div class="block">Retrieves the offset added to unique task IDs from sub projects
 to generate the task ID shown in the master project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unique ID offset</dd>
</dl>
</li>
</ul>
<a name="getSubprojectReadOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectReadOnly</h4>
<pre>public&nbsp;boolean&nbsp;getSubprojectReadOnly()</pre>
<div class="block">Retrieve the subproject read only flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>subproject read only flag</dd>
</dl>
</li>
</ul>
<a name="setSubprojectReadOnly-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectReadOnly</h4>
<pre>public&nbsp;void&nbsp;setSubprojectReadOnly(boolean&nbsp;subprojectReadOnly)</pre>
<div class="block">Set the subproject read only flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subprojectReadOnly</code> - subproject read only flag</dd>
</dl>
</li>
</ul>
<a name="getExternalTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalTask</h4>
<pre>public&nbsp;boolean&nbsp;getExternalTask()</pre>
<div class="block">Retrieves the external task flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>external task flag</dd>
</dl>
</li>
</ul>
<a name="setExternalTask-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExternalTask</h4>
<pre>public&nbsp;void&nbsp;setExternalTask(boolean&nbsp;externalTask)</pre>
<div class="block">Sets the external task flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>externalTask</code> - external task flag</dd>
</dl>
</li>
</ul>
<a name="getExternalProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalProject</h4>
<pre>public&nbsp;boolean&nbsp;getExternalProject()</pre>
<div class="block">Retrieves the external project flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this task represents an external project</dd>
</dl>
</li>
</ul>
<a name="getACWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getACWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getACWP()</pre>
<div class="block">Retrieve the ACWP value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ACWP value</dd>
</dl>
</li>
</ul>
<a name="setACWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setACWP</h4>
<pre>public&nbsp;void&nbsp;setACWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</pre>
<div class="block">Set the ACWP value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>acwp</code> - ACWP value</dd>
</dl>
</li>
</ul>
<a name="getLevelingDelayFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelingDelayFormat</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;getLevelingDelayFormat()</pre>
<div class="block">Retrieve the leveling delay format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>leveling delay  format</dd>
</dl>
</li>
</ul>
<a name="setLevelingDelayFormat-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelingDelayFormat</h4>
<pre>public&nbsp;void&nbsp;setLevelingDelayFormat(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;levelingDelayFormat)</pre>
<div class="block">Set the leveling delay format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>levelingDelayFormat</code> - leveling delay format</dd>
</dl>
</li>
</ul>
<a name="getIgnoreResourceCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIgnoreResourceCalendar</h4>
<pre>public&nbsp;boolean&nbsp;getIgnoreResourceCalendar()</pre>
<div class="block">Retrieves the ignore resource calendar flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ignore resource calendar flag</dd>
</dl>
</li>
</ul>
<a name="setIgnoreResourceCalendar-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIgnoreResourceCalendar</h4>
<pre>public&nbsp;void&nbsp;setIgnoreResourceCalendar(boolean&nbsp;ignoreResourceCalendar)</pre>
<div class="block">Sets the ignore resource calendar flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ignoreResourceCalendar</code> - ignore resource calendar flag</dd>
</dl>
</li>
</ul>
<a name="getPhysicalPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhysicalPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPhysicalPercentComplete()</pre>
<div class="block">Retrieves the physical percent complete value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>physical percent complete value</dd>
</dl>
</li>
</ul>
<a name="setPhysicalPercentComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhysicalPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setPhysicalPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;physicalPercentComplete)</pre>
<div class="block">Sets the physical percent complete value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>physicalPercentComplete</code> - physical percent complete value</dd>
</dl>
</li>
</ul>
<a name="getEarnedValueMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueMethod</h4>
<pre>public&nbsp;<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;getEarnedValueMethod()</pre>
<div class="block">Retrieves the earned value method.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>earned value method</dd>
</dl>
</li>
</ul>
<a name="setEarnedValueMethod-org.mpxj.EarnedValueMethod-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueMethod</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueMethod(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;earnedValueMethod)</pre>
<div class="block">Sets the earned value method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>earnedValueMethod</code> - earned value method</dd>
</dl>
</li>
</ul>
<a name="getActualWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWorkProtected</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWorkProtected()</pre>
<div class="block">Retrieves the actual work protected value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual work protected value</dd>
</dl>
</li>
</ul>
<a name="setActualWorkProtected-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualWorkProtected(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualWorkProtected)</pre>
<div class="block">Sets the actual work protected value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualWorkProtected</code> - actual work protected value</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWorkProtected</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualOvertimeWorkProtected()</pre>
<div class="block">Retrieves the actual overtime work protected value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime work protected value</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWorkProtected-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWorkProtected(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualOvertimeWorkProtected)</pre>
<div class="block">Sets the actual overtime work protected value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualOvertimeWorkProtected</code> - actual overtime work protected value</dd>
</dl>
</li>
</ul>
<a name="getRegularWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegularWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRegularWork()</pre>
<div class="block">Retrieve the amount of regular work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>amount of regular work</dd>
</dl>
</li>
</ul>
<a name="setRegularWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegularWork</h4>
<pre>public&nbsp;void&nbsp;setRegularWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;regularWork)</pre>
<div class="block">Set the amount of regular work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>regularWork</code> - amount of regular work</dd>
</dl>
</li>
</ul>
<a name="setEffortDriven-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEffortDriven</h4>
<pre>public&nbsp;void&nbsp;setEffortDriven(boolean&nbsp;flag)</pre>
<div class="block">Sets the effort driven flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - value</dd>
</dl>
</li>
</ul>
<a name="getEffortDriven--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffortDriven</h4>
<pre>public&nbsp;boolean&nbsp;getEffortDriven()</pre>
<div class="block">Retrieves the effort driven flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Flag value</dd>
</dl>
</li>
</ul>
<a name="setDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dd><code>value</code> - date value</dd>
</dl>
</li>
</ul>
<a name="getDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDate(int&nbsp;index)</pre>
<div class="block">Retrieve a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>date value</dd>
</dl>
</li>
</ul>
<a name="getOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getOvertimeCost()</pre>
<div class="block">Retrieves the overtime cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Cost value</dd>
</dl>
</li>
</ul>
<a name="setOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;number)</pre>
<div class="block">Sets the overtime cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>number</code> - Cost value</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Retrieves the actual overtime cost for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime cost</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the actual overtime cost for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - actual overtime cost</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualOvertimeWork()</pre>
<div class="block">Retrieves the actual overtime work value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime work value</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the actual overtime work value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - actual overtime work value</dd>
</dl>
</li>
</ul>
<a name="getFixedCostAccrual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFixedCostAccrual</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getFixedCostAccrual()</pre>
<div class="block">Retrieves the fixed cost accrual flag value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>fixed cost accrual flag</dd>
</dl>
</li>
</ul>
<a name="setFixedCostAccrual-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFixedCostAccrual</h4>
<pre>public&nbsp;void&nbsp;setFixedCostAccrual(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</pre>
<div class="block">Sets the fixed cost accrual flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - fixed cost accrual type</dd>
</dl>
</li>
</ul>
<a name="getHyperlink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlink()</pre>
<div class="block">Retrieves the task hyperlink attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkAddress()</pre>
<div class="block">Retrieves the task hyperlink address attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink address attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkSubAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkSubAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkSubAddress()</pre>
<div class="block">Retrieves the task hyperlink sub-address attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink sub address attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkScreenTip--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkScreenTip</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkScreenTip()</pre>
<div class="block">Retrieves the task hyperlink screen tip attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlink-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlink</h4>
<pre>public&nbsp;void&nbsp;setHyperlink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink address attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink address attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkSubAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkSubAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkSubAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink sub address attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink sub address attribute</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkScreenTip-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkScreenTip</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkScreenTip(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the task hyperlink screen tip attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="getLevelAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelAssignments</h4>
<pre>public&nbsp;boolean&nbsp;getLevelAssignments()</pre>
<div class="block">Retrieves the level assignments flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>level assignments flag</dd>
</dl>
</li>
</ul>
<a name="setLevelAssignments-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelAssignments</h4>
<pre>public&nbsp;void&nbsp;setLevelAssignments(boolean&nbsp;flag)</pre>
<div class="block">Sets the level assignments flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - level assignments flag</dd>
</dl>
</li>
</ul>
<a name="getLevelingCanSplit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelingCanSplit</h4>
<pre>public&nbsp;boolean&nbsp;getLevelingCanSplit()</pre>
<div class="block">Retrieves the leveling can split flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>leveling can split flag</dd>
</dl>
</li>
</ul>
<a name="setLevelingCanSplit-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelingCanSplit</h4>
<pre>public&nbsp;void&nbsp;setLevelingCanSplit(boolean&nbsp;flag)</pre>
<div class="block">Sets the leveling can split flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - leveling can split flag</dd>
</dl>
</li>
</ul>
<a name="getOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getOvertimeWork()</pre>
<div class="block">Retrieves the overtime work attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overtime work value</dd>
</dl>
</li>
</ul>
<a name="setOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the overtime work attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - overtime work value</dd>
</dl>
</li>
</ul>
<a name="getPreleveledStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreleveledStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPreleveledStart()</pre>
<div class="block">Retrieves the preleveled start attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>preleveled start</dd>
</dl>
</li>
</ul>
<a name="getPreleveledFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreleveledFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPreleveledFinish()</pre>
<div class="block">Retrieves the preleveled finish attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>preleveled finish</dd>
</dl>
</li>
</ul>
<a name="setPreleveledStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreleveledStart</h4>
<pre>public&nbsp;void&nbsp;setPreleveledStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Sets the preleveled start attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - preleveled start attribute</dd>
</dl>
</li>
</ul>
<a name="setPreleveledFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreleveledFinish</h4>
<pre>public&nbsp;void&nbsp;setPreleveledFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Sets the preleveled finish attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - preleveled finish attribute</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingOvertimeWork()</pre>
<div class="block">Retrieves the remaining overtime work attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining overtime work</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the remaining overtime work attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - remaining overtime work</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingOvertimeCost()</pre>
<div class="block">Retrieves the remaining overtime cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining overtime cost value</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the remaining overtime cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - overtime cost value</dd>
</dl>
</li>
</ul>
<a name="getCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;getCalendar()</pre>
<div class="block">Retrieves the calendar associated with this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectCalendar instance</dd>
</dl>
</li>
</ul>
<a name="setCalendarUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarUniqueID</h4>
<pre>public&nbsp;void&nbsp;setCalendarUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the calendar unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="getCalendarUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCalendarUniqueID()</pre>
<div class="block">Retrieve the calendar unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="setCalendar-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendar</h4>
<pre>public&nbsp;void&nbsp;setCalendar(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block">Sets the calendar associated with this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar instance</dd>
</dl>
</li>
</ul>
<a name="getExpanded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpanded</h4>
<pre>public&nbsp;boolean&nbsp;getExpanded()</pre>
<div class="block">Retrieve a flag indicating if the task is shown as expanded
 in MS Project. If this flag is set to true, any sub tasks
 for this current task will be visible. If this is false,
 any sub tasks will be hidden.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setExpanded-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExpanded</h4>
<pre>public&nbsp;void&nbsp;setExpanded(boolean&nbsp;expanded)</pre>
<div class="block">Set a flag indicating if the task is shown as expanded
 in MS Project. If this flag is set to true, any sub tasks
 for this current task will be visible. If this is false,
 any sub tasks will be hidden.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expanded</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="setStartSlack-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartSlack</h4>
<pre>public&nbsp;void&nbsp;setStartSlack(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Set the start slack.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - start slack</dd>
</dl>
</li>
</ul>
<a name="setFinishSlack-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishSlack</h4>
<pre>public&nbsp;void&nbsp;setFinishSlack(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Set the finish slack.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - finish slack</dd>
</dl>
</li>
</ul>
<a name="getStartSlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartSlack</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getStartSlack()</pre>
<div class="block">Retrieve the start slack.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start slack</dd>
</dl>
</li>
</ul>
<a name="getFinishSlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishSlack</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getFinishSlack()</pre>
<div class="block">Retrieve the finish slack.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish slack</dd>
</dl>
</li>
</ul>
<a name="getFieldByAlias-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldByAlias</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</pre>
<div class="block">Retrieve the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setFieldByAlias-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldByAlias</h4>
<pre>public&nbsp;void&nbsp;setFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Set the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getSplits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSplits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/LocalDateTimeRange.html" title="class in org.mpxj">LocalDateTimeRange</a>&gt;&nbsp;getSplits()</pre>
<div class="block">This method retrieves a list of task splits. Each split is represented
 by a DateRange instance. The list will always follow the pattern
 task range, split range, task range and so on.

 Note that this method will return null if the task is not split.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of split times</dd>
</dl>
</li>
</ul>
<a name="setSplits-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplits</h4>
<pre>public&nbsp;void&nbsp;setSplits(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/LocalDateTimeRange.html" title="class in org.mpxj">LocalDateTimeRange</a>&gt;&nbsp;splits)</pre>
<div class="block">Internal method used to set the list of splits.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>splits</code> - list of split times</dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()</pre>
<div class="block">Removes this task from the project.</div>
</li>
</ul>
<a name="expandSubproject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>expandSubproject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;expandSubproject()</pre>
<div class="block">If this task represents an external project (subproject), calling this method
 will attempt to read the subproject file, the link the tasks from
 the subproject file as children of this current task.
 <p/>
 Calling this method on a task which does not represent an external project
 will have no effect.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a ProjectFile instance for the subproject, or null if no project was loaded</dd>
</dl>
</li>
</ul>
<a name="getSubprojectObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getSubprojectObject()</pre>
<div class="block">If this task is an external project task or an external predecessor task,
 attempt to load the project to which it refers. We will try the full path for the project
 and either the process working directory, or the directory the caller supplied to
 ProjectFile.setSubprojectWorkingDirectory.
 <p/>
 Note that the ProjectFile instance is cached once it has been read, so multiple calls
 to this method don't incur the cost of finding and re-reading the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance or null if the project could not be located or read</dd>
</dl>
</li>
</ul>
<a name="setSubprojectObject-org.mpxj.ProjectFile-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectObject</h4>
<pre>public&nbsp;void&nbsp;setSubprojectObject(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;projectFile)</pre>
<div class="block">Where we have already read a project, this method is used to
 attach it to the task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectFile</code> - ProjectFile instance</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseCost(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseCost</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseCost(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEnterpriseDate(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDate</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDate(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getEnterpriseDuration(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDuration</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDuration(int&nbsp;index,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseFlag</h4>
<pre>public&nbsp;boolean&nbsp;getEnterpriseFlag(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseFlag</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseFlag(int&nbsp;index,
                              boolean&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseNumber(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseNumber</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseNumber(int&nbsp;index,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEnterpriseText(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseText</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseText(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(int&nbsp;baselineNumber,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDuration</h4>
<pre>public&nbsp;void&nbsp;setBaselineDuration(int&nbsp;baselineNumber,
                                <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinish(int&nbsp;baselineNumber,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineStart(int&nbsp;baselineNumber,
                             <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(int&nbsp;baselineNumber,
                            <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineDuration(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineDurationText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDurationText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineDurationText(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieves the baseline duration text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline duration text value</dd>
</dl>
</li>
</ul>
<a name="setBaselineDurationText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDurationText</h4>
<pre>public&nbsp;void&nbsp;setBaselineDurationText(int&nbsp;baselineNumber,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline duration text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline duration text value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineFinish(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinishText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinishText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineFinishText(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieves the baseline finish text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline finish text value</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinishText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinishText</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinishText(int&nbsp;baselineNumber,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline finish text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline finish text value</dd>
</dl>
</li>
</ul>
<a name="getBaselineStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineStart(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineStartText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStartText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineStartText(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieves the baseline start text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline start text value</dd>
</dl>
</li>
</ul>
<a name="setBaselineStartText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStartText</h4>
<pre>public&nbsp;void&nbsp;setBaselineStartText(int&nbsp;baselineNumber,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the baseline start text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline start text value</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getCompleteThrough--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompleteThrough</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCompleteThrough()</pre>
<div class="block">Retrieve the "complete through" date. This is the date at which
 the percent complete progress line on a task finishes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>complete through date</dd>
</dl>
</li>
</ul>
<a name="setCompleteThrough-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompleteThrough</h4>
<pre>public&nbsp;void&nbsp;setCompleteThrough(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the "complete through" date. This is the date at which
 the percent complete progress line on a task finishes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - complete through date</dd>
</dl>
</li>
</ul>
<a name="getSummaryProgress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummaryProgress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getSummaryProgress()</pre>
<div class="block">Retrieve the summary progress date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>summary progress date</dd>
</dl>
</li>
</ul>
<a name="setSummaryProgress-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSummaryProgress</h4>
<pre>public&nbsp;void&nbsp;setSummaryProgress(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the summary progress date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - summary progress date</dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Retrieve the task GUID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task GUID</dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Set the task GUID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - task GUID</dd>
</dl>
</li>
</ul>
<a name="getTaskMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskMode</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a>&nbsp;getTaskMode()</pre>
<div class="block">Retrieves the task mode.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task mode</dd>
</dl>
</li>
</ul>
<a name="setTaskMode-org.mpxj.TaskMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskMode</h4>
<pre>public&nbsp;void&nbsp;setTaskMode(<a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj">TaskMode</a>&nbsp;mode)</pre>
<div class="block">Sets the task mode.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mode</code> - task mode</dd>
</dl>
</li>
</ul>
<a name="getActive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActive</h4>
<pre>public&nbsp;boolean&nbsp;getActive()</pre>
<div class="block">Retrieves the active flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>active flag value</dd>
</dl>
</li>
</ul>
<a name="setActive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActive</h4>
<pre>public&nbsp;void&nbsp;setActive(boolean&nbsp;active)</pre>
<div class="block">Sets the active flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>active</code> - active flag value</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineEstimatedDuration()</pre>
<div class="block">Retrieve the baseline estimated duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline estimated duration</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedDuration</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Set the baseline estimated duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - baseline estimated duration</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedDuration</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedDuration(int&nbsp;baselineNumber,
                                         <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineEstimatedDuration(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineEstimatedStart()</pre>
<div class="block">Retrieve the baseline estimated start.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline estimated start</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the baseline estimated start.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - baseline estimated start</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineEstimatedStart(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedStart(int&nbsp;baselineNumber,
                                      <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineEstimatedFinish()</pre>
<div class="block">Retrieve the baseline estimated finish.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline estimated finish</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Set the baseline estimated finish.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - baseline estimated finish</dd>
</dl>
</li>
</ul>
<a name="getBaselineEstimatedFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineEstimatedFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineEstimatedFinish(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineEstimatedFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineEstimatedFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineEstimatedFinish(int&nbsp;baselineNumber,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineFixedCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFixedCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineFixedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - amount</dd>
</dl>
</li>
</ul>
<a name="getBaselineFixedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFixedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineFixedCost()</pre>
<div class="block">The Fixed Cost field shows any task expense that is not associated
 with a resource cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency amount</dd>
</dl>
</li>
</ul>
<a name="setBaselineFixedCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFixedCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineFixedCost(int&nbsp;baselineNumber,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFixedCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFixedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineFixedCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFixedCostAccrual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFixedCostAccrual</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getBaselineFixedCostAccrual()</pre>
<div class="block">Retrieves the baseline fixed cost accrual.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>fixed cost accrual flag</dd>
</dl>
</li>
</ul>
<a name="setBaselineFixedCostAccrual-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFixedCostAccrual</h4>
<pre>public&nbsp;void&nbsp;setBaselineFixedCostAccrual(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</pre>
<div class="block">Sets the baseline fixed cost accrual.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - fixed cost accrual type</dd>
</dl>
</li>
</ul>
<a name="setBaselineFixedCostAccrual-int-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFixedCostAccrual</h4>
<pre>public&nbsp;void&nbsp;setBaselineFixedCostAccrual(int&nbsp;baselineNumber,
                                        <a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFixedCostAccrual-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFixedCostAccrual</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getBaselineFixedCostAccrual(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getExpenseItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpenseItems</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ExpenseItem.html" title="class in org.mpxj">ExpenseItem</a>&gt;&nbsp;getExpenseItems()</pre>
<div class="block">Retrieve expense items for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of expense items</dd>
</dl>
</li>
</ul>
<a name="setExpenseItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExpenseItems</h4>
<pre>public&nbsp;void&nbsp;setExpenseItems(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ExpenseItem.html" title="class in org.mpxj">ExpenseItem</a>&gt;&nbsp;items)</pre>
<div class="block">Set the expense items for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - list of expense items</dd>
</dl>
</li>
</ul>
<a name="setStoredMaterial-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStoredMaterial</h4>
<pre>public&nbsp;void&nbsp;setStoredMaterial(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the stored material value for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - stored material value</dd>
</dl>
</li>
</ul>
<a name="getStoredMaterial--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStoredMaterial</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getStoredMaterial()</pre>
<div class="block">Retrieve the stored material value for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>stored material value</dd>
</dl>
</li>
</ul>
<a name="setFeatureOfWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFeatureOfWork</h4>
<pre>public&nbsp;void&nbsp;setFeatureOfWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the feature of work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - feature of work value</dd>
</dl>
</li>
</ul>
<a name="getFeatureOfWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFeatureOfWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFeatureOfWork()</pre>
<div class="block">Retrieve the feature of work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>feature of work value</dd>
</dl>
</li>
</ul>
<a name="setCategoryOfWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategoryOfWork</h4>
<pre>public&nbsp;void&nbsp;setCategoryOfWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the category of work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - category of work value</dd>
</dl>
</li>
</ul>
<a name="getCategoryOfWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategoryOfWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCategoryOfWork()</pre>
<div class="block">Retrieve the category of work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>category of work value</dd>
</dl>
</li>
</ul>
<a name="setPhaseOfWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhaseOfWork</h4>
<pre>public&nbsp;void&nbsp;setPhaseOfWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the phase of work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - phase of work value</dd>
</dl>
</li>
</ul>
<a name="getPhaseOfWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhaseOfWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPhaseOfWork()</pre>
<div class="block">Retrieve the phase of work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>phase of work value</dd>
</dl>
</li>
</ul>
<a name="setBidItem-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBidItem</h4>
<pre>public&nbsp;void&nbsp;setBidItem(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Retrieve the bid item field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - bid item value</dd>
</dl>
</li>
</ul>
<a name="getBidItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBidItem</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBidItem()</pre>
<div class="block">Set the bid item field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bid item value</dd>
</dl>
</li>
</ul>
<a name="setModOrClaimNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModOrClaimNumber</h4>
<pre>public&nbsp;void&nbsp;setModOrClaimNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Retrieve the mod or claim number field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - mod or claim number value</dd>
</dl>
</li>
</ul>
<a name="getModOrClaimNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModOrClaimNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getModOrClaimNumber()</pre>
<div class="block">Retrieve the mod or claim number field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>mod or claim number value</dd>
</dl>
</li>
</ul>
<a name="setWorkAreaCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkAreaCode</h4>
<pre>public&nbsp;void&nbsp;setWorkAreaCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the work area code field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work area code value</dd>
</dl>
</li>
</ul>
<a name="getWorkAreaCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkAreaCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWorkAreaCode()</pre>
<div class="block">Retrieve the work area code field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work area code value</dd>
</dl>
</li>
</ul>
<a name="setResponsibilityCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResponsibilityCode</h4>
<pre>public&nbsp;void&nbsp;setResponsibilityCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the responsibility code field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - responsibility code value</dd>
</dl>
</li>
</ul>
<a name="getResponsibilityCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponsibilityCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResponsibilityCode()</pre>
<div class="block">Retrieve the responsibility code field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>responsibility code value</dd>
</dl>
</li>
</ul>
<a name="setWorkersPerDay-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkersPerDay</h4>
<pre>public&nbsp;void&nbsp;setWorkersPerDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the workers per day field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - workers per day value</dd>
</dl>
</li>
</ul>
<a name="getWorkersPerDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkersPerDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getWorkersPerDay()</pre>
<div class="block">Retrieve the workers per day field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>workers per day value</dd>
</dl>
</li>
</ul>
<a name="setHammockCode-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHammockCode</h4>
<pre>public&nbsp;void&nbsp;setHammockCode(boolean&nbsp;value)</pre>
<div class="block">Set the hammock code field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - hammock code value</dd>
</dl>
</li>
</ul>
<a name="getHammockCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHammockCode</h4>
<pre>public&nbsp;boolean&nbsp;getHammockCode()</pre>
<div class="block">Retrieve the hammock code field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hammock code value</dd>
</dl>
</li>
</ul>
<a name="setMail-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMail</h4>
<pre>public&nbsp;void&nbsp;setMail(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the mail field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - mail value</dd>
</dl>
</li>
</ul>
<a name="getMail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMail</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMail()</pre>
<div class="block">Retrieve the mail field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>mail value</dd>
</dl>
</li>
</ul>
<a name="setSection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSection</h4>
<pre>public&nbsp;void&nbsp;setSection(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the section field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - section value</dd>
</dl>
</li>
</ul>
<a name="getSection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSection</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSection()</pre>
<div class="block">Retrieve the section field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>section value</dd>
</dl>
</li>
</ul>
<a name="setManager-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManager</h4>
<pre>public&nbsp;void&nbsp;setManager(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the manager field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - manager value</dd>
</dl>
</li>
</ul>
<a name="getManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManager</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getManager()</pre>
<div class="block">Retrieve the manager field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>manager value</dd>
</dl>
</li>
</ul>
<a name="setDepartment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepartment</h4>
<pre>public&nbsp;void&nbsp;setDepartment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the department field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - department value</dd>
</dl>
</li>
</ul>
<a name="getDepartment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepartment</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDepartment()</pre>
<div class="block">Retrieve the department field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>department value</dd>
</dl>
</li>
</ul>
<a name="setOverallPercentComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverallPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setOverallPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the overall percent complete field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - overall percent complete value</dd>
</dl>
</li>
</ul>
<a name="getOverallPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverallPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getOverallPercentComplete()</pre>
<div class="block">Retrieve the overall percent complete field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overall percent complete value</dd>
</dl>
</li>
</ul>
<a name="setPlannedFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedFinish</h4>
<pre>public&nbsp;void&nbsp;setPlannedFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the planned finish field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned finish value</dd>
</dl>
</li>
</ul>
<a name="getPlannedFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedFinish()</pre>
<div class="block">Retrieve the planned finish field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned finish value</dd>
</dl>
</li>
</ul>
<a name="setPlannedStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedStart</h4>
<pre>public&nbsp;void&nbsp;setPlannedStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the planned start field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned start value</dd>
</dl>
</li>
</ul>
<a name="getPlannedStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedStart()</pre>
<div class="block">Retrieve the planned start field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned start value</dd>
</dl>
</li>
</ul>
<a name="setPlannedDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedDuration</h4>
<pre>public&nbsp;void&nbsp;setPlannedDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the planned duration field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned duration value</dd>
</dl>
</li>
</ul>
<a name="getPlannedDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPlannedDuration()</pre>
<div class="block">Retrieve the planned duration field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned duration value</dd>
</dl>
</li>
</ul>
<a name="setPlannedWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedWork</h4>
<pre>public&nbsp;void&nbsp;setPlannedWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the planned work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned work value</dd>
</dl>
</li>
</ul>
<a name="getPlannedWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPlannedWork()</pre>
<div class="block">Retrieve the planned work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned work value</dd>
</dl>
</li>
</ul>
<a name="setPlannedCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the planned cost field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - planned cost value</dd>
</dl>
</li>
</ul>
<a name="getPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPlannedCost()</pre>
<div class="block">Retrieve the planned cost field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned cost value</dd>
</dl>
</li>
</ul>
<a name="setSuspendDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSuspendDate</h4>
<pre>public&nbsp;void&nbsp;setSuspendDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the suspend date field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - suspend date value</dd>
</dl>
</li>
</ul>
<a name="getSuspendDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSuspendDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getSuspendDate()</pre>
<div class="block">Retrieve the suspend date field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>suspend date value</dd>
</dl>
</li>
</ul>
<a name="setPrimaryResourceUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrimaryResourceUniqueID</h4>
<pre>public&nbsp;void&nbsp;setPrimaryResourceUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the primary resource unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - primary resource unique ID</dd>
</dl>
</li>
</ul>
<a name="getPrimaryResourceUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrimaryResourceUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPrimaryResourceUniqueID()</pre>
<div class="block">Retrieve the primary resource unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>primary resource unique ID</dd>
</dl>
</li>
</ul>
<a name="getPrimaryResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrimaryResource</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;getPrimaryResource()</pre>
<div class="block">Retrieve the primary resource for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>primary resource</dd>
</dl>
</li>
</ul>
<a name="setPrimaryResource-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrimaryResource</h4>
<pre>public&nbsp;void&nbsp;setPrimaryResource(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">Set the primary resource for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource</dd>
</dl>
</li>
</ul>
<a name="setActivityID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityID</h4>
<pre>public&nbsp;void&nbsp;setActivityID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the activity ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity ID value</dd>
</dl>
</li>
</ul>
<a name="getCanonicalActivityID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCanonicalActivityID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCanonicalActivityID()</pre>
<div class="block">Retrieve a "canonical" version of the Activity ID.
 This method handles the case where the Activity ID for
 a WBS entry will be prefixed with the Project ID.
 This method replaces the Project ID with the text "PROJECT",
 which allows WBS entries to be matched by Activity ID
 across projects.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>canonical Activity ID value</dd>
</dl>
</li>
</ul>
<a name="getActivityID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityID()</pre>
<div class="block">Retrieve the activity ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity ID value</dd>
</dl>
</li>
</ul>
<a name="setPercentCompleteType-org.mpxj.PercentCompleteType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentCompleteType</h4>
<pre>public&nbsp;void&nbsp;setPercentCompleteType(<a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj">PercentCompleteType</a>&nbsp;value)</pre>
<div class="block">Set the percent complete type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - percent complete type</dd>
</dl>
</li>
</ul>
<a name="getPercentCompleteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentCompleteType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj">PercentCompleteType</a>&nbsp;getPercentCompleteType()</pre>
<div class="block">Retrieve the percent complete type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percent complete type</dd>
</dl>
</li>
</ul>
<a name="getActivityStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityStatus</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj">ActivityStatus</a>&nbsp;getActivityStatus()</pre>
<div class="block">Retrieve the activity status.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity status</dd>
</dl>
</li>
</ul>
<a name="setActivityStatus-org.mpxj.ActivityStatus-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityStatus</h4>
<pre>public&nbsp;void&nbsp;setActivityStatus(<a href="../../org/mpxj/ActivityStatus.html" title="enum in org.mpxj">ActivityStatus</a>&nbsp;value)</pre>
<div class="block">Set the activity status.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity status</dd>
</dl>
</li>
</ul>
<a name="getActivityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj">ActivityType</a>&nbsp;getActivityType()</pre>
<div class="block">Retrieve the activity type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity type</dd>
</dl>
</li>
</ul>
<a name="setActivityType-org.mpxj.ActivityType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityType</h4>
<pre>public&nbsp;void&nbsp;setActivityType(<a href="../../org/mpxj/ActivityType.html" title="enum in org.mpxj">ActivityType</a>&nbsp;value)</pre>
<div class="block">Set the activity type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity type</dd>
</dl>
</li>
</ul>
<a name="getLongestPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLongestPath</h4>
<pre>public&nbsp;boolean&nbsp;getLongestPath()</pre>
<div class="block">Retrieve the longest path.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if part of the longest path</dd>
</dl>
</li>
</ul>
<a name="setLongestPath-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLongestPath</h4>
<pre>public&nbsp;void&nbsp;setLongestPath(boolean&nbsp;value)</pre>
<div class="block">Set the longest path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - true if part of the longest path</dd>
</dl>
</li>
</ul>
<a name="getExternalEarlyStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalEarlyStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getExternalEarlyStart()</pre>
<div class="block">Retrieve the external early start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>external early start date</dd>
</dl>
</li>
</ul>
<a name="setExternalEarlyStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExternalEarlyStart</h4>
<pre>public&nbsp;void&nbsp;setExternalEarlyStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the external early start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - external early start date</dd>
</dl>
</li>
</ul>
<a name="getExternalLateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalLateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getExternalLateFinish()</pre>
<div class="block">Retrieve the external late finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>external late finish date</dd>
</dl>
</li>
</ul>
<a name="setExternalLateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExternalLateFinish</h4>
<pre>public&nbsp;void&nbsp;setExternalLateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the external late finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - external late finish date</dd>
</dl>
</li>
</ul>
<a name="getSprintID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSprintID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSprintID()</pre>
<div class="block">Retrieve the Sprint ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sprint ID</dd>
</dl>
</li>
</ul>
<a name="setSprintID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSprintID</h4>
<pre>public&nbsp;void&nbsp;setSprintID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the sprint ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - sprint ID</dd>
</dl>
</li>
</ul>
<a name="getBoardStatusID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoardStatusID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBoardStatusID()</pre>
<div class="block">Retrieve the Board Status ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>board status ID</dd>
</dl>
</li>
</ul>
<a name="setBoardStatusID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBoardStatusID</h4>
<pre>public&nbsp;void&nbsp;setBoardStatusID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the Board Status ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - board status ID</dd>
</dl>
</li>
</ul>
<a name="getResponsePending--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponsePending</h4>
<pre>public&nbsp;boolean&nbsp;getResponsePending()</pre>
<div class="block">Retrieve the response pending flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>response pending flag value</dd>
</dl>
</li>
</ul>
<a name="setResponsePending-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResponsePending</h4>
<pre>public&nbsp;void&nbsp;setResponsePending(boolean&nbsp;value)</pre>
<div class="block">Set the response pending flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - response pending flag value</dd>
</dl>
</li>
</ul>
<a name="getScheduledStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScheduledStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getScheduledStart()</pre>
<div class="block">Retrieve the scheduled start.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>scheduled start value</dd>
</dl>
</li>
</ul>
<a name="setScheduledStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduledStart</h4>
<pre>public&nbsp;void&nbsp;setScheduledStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the scheduled start.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - scheduled start value</dd>
</dl>
</li>
</ul>
<a name="getScheduledFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScheduledFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getScheduledFinish()</pre>
<div class="block">Retrieve the scheduled finish.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>scheduled finish value</dd>
</dl>
</li>
</ul>
<a name="setScheduledFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduledFinish</h4>
<pre>public&nbsp;void&nbsp;setScheduledFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the scheduled finish.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - scheduled finish value</dd>
</dl>
</li>
</ul>
<a name="getScheduledDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScheduledDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getScheduledDuration()</pre>
<div class="block">Retrieve the scheduled duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>scheduled duration value</dd>
</dl>
</li>
</ul>
<a name="setScheduledDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduledDuration</h4>
<pre>public&nbsp;void&nbsp;setScheduledDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the scheduled duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - scheduled duration value</dd>
</dl>
</li>
</ul>
<a name="getBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBudgetCost()</pre>
<div class="block">Retrieve the budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget cost value</dd>
</dl>
</li>
</ul>
<a name="setBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBudgetWork()</pre>
<div class="block">Retrieve the budget work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget work value</dd>
</dl>
</li>
</ul>
<a name="setBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - budget work value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost()</pre>
<div class="block">Retrieve the baseline budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork()</pre>
<div class="block">Retrieve the baseline budget work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget cost</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(int&nbsp;baselineNumber,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(int&nbsp;baselineNumber,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="setSequenceNumber-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSequenceNumber</h4>
<pre>public&nbsp;void&nbsp;setSequenceNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;sequenceNumber)</pre>
<div class="block">Set this task's sequence number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sequenceNumber</code> - task sequence number</dd>
</dl>
</li>
</ul>
<a name="getSequenceNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSequenceNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSequenceNumber()</pre>
<div class="block">Retrieve this task's sequence number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task sequence number</dd>
</dl>
</li>
</ul>
<a name="getSteps--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSteps</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Step.html" title="class in org.mpxj">Step</a>&gt;&nbsp;getSteps()</pre>
<div class="block">Retrieve steps for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of steps</dd>
</dl>
</li>
</ul>
<a name="setSteps-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSteps</h4>
<pre>public&nbsp;void&nbsp;setSteps(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Step.html" title="class in org.mpxj">Step</a>&gt;&nbsp;steps)</pre>
<div class="block">Set the steps for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>steps</code> - list of steps</dd>
</dl>
</li>
</ul>
<a name="getLocationUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLocationUniqueID()</pre>
<div class="block">Retrieves the location unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location unique ID</dd>
</dl>
</li>
</ul>
<a name="setLocationUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationUniqueID</h4>
<pre>public&nbsp;void&nbsp;setLocationUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the location unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - location unique ID</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;getLocation()</pre>
<div class="block">Retrieves the location.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location.</dd>
</dl>
</li>
</ul>
<a name="setLocation-org.mpxj.Location-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocation</h4>
<pre>public&nbsp;void&nbsp;setLocation(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</pre>
<div class="block">Sets the location.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - location</dd>
</dl>
</li>
</ul>
<a name="getBarName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBarName()</pre>
<div class="block">Retrieve the name of the Asta Powerproject bar to which this task belongs.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bar name</dd>
</dl>
</li>
</ul>
<a name="setBarName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarName</h4>
<pre>public&nbsp;void&nbsp;setBarName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the name of the Asta Powerproject bar to which this task belongs.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - bar name</dd>
</dl>
</li>
</ul>
<a name="getExpectedFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpectedFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getExpectedFinish()</pre>
<div class="block">Retrieve the expected finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>expected finish date</dd>
</dl>
</li>
</ul>
<a name="setExpectedFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExpectedFinish</h4>
<pre>public&nbsp;void&nbsp;setExpectedFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the expected finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - expected finish date</dd>
</dl>
</li>
</ul>
<a name="setActualWorkLabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWorkLabor</h4>
<pre>public&nbsp;void&nbsp;setActualWorkLabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the labor component of the task's Actual Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getActualWorkLabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWorkLabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWorkLabor()</pre>
<div class="block">Retrieve the labor component of the task's Actual Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="setActualWorkNonlabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWorkNonlabor</h4>
<pre>public&nbsp;void&nbsp;setActualWorkNonlabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the nonlabor component of the task's Actual Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getActualWorkNonlabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWorkNonlabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWorkNonlabor()</pre>
<div class="block">Retrieve the nonlabor component of the task's Actual Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="setPlannedWorkLabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedWorkLabor</h4>
<pre>public&nbsp;void&nbsp;setPlannedWorkLabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the labor component of the task's Planned Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getPlannedWorkLabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedWorkLabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPlannedWorkLabor()</pre>
<div class="block">Retrieve the labor component of the task's Planned Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="setPlannedWorkNonlabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedWorkNonlabor</h4>
<pre>public&nbsp;void&nbsp;setPlannedWorkNonlabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the nonlabor component of the task's Planned Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getPlannedWorkNonlabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedWorkNonlabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPlannedWorkNonlabor()</pre>
<div class="block">Retrieve the nonlabor component of the task's Planned Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="setRemainingWorkLabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWorkLabor</h4>
<pre>public&nbsp;void&nbsp;setRemainingWorkLabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the labor component of the task's Remaining Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getRemainingWorkLabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWorkLabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingWorkLabor()</pre>
<div class="block">Retrieve the labor component of the task's Remaining Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="setRemainingWorkNonlabor-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWorkNonlabor</h4>
<pre>public&nbsp;void&nbsp;setRemainingWorkNonlabor(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the nonlabor component of the task's Remaining Work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - work value</dd>
</dl>
</li>
</ul>
<a name="getRemainingWorkNonlabor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWorkNonlabor</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingWorkNonlabor()</pre>
<div class="block">Retrieve the nonlabor component of the task's Remaining Work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work value</dd>
</dl>
</li>
</ul>
<a name="getShowStartText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowStartText</h4>
<pre>public&nbsp;boolean&nbsp;getShowStartText()</pre>
<div class="block">Returns true for manually scheduled tasks if the Start Text attribute should be
 displayed to the user rather than the Start attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if Start Text should be displayed</dd>
</dl>
</li>
</ul>
<a name="getShowFinishText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowFinishText</h4>
<pre>public&nbsp;boolean&nbsp;getShowFinishText()</pre>
<div class="block">Returns true for manually scheduled tasks if the Finish Text attribute should be
 displayed to the user rather than the Finish attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if Finish Text should be displayed</dd>
</dl>
</li>
</ul>
<a name="getShowDurationText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowDurationText</h4>
<pre>public&nbsp;boolean&nbsp;getShowDurationText()</pre>
<div class="block">Returns true for manually scheduled tasks if the Duration Text attribute should be
 displayed to the user rather than the Duration attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if Duration Text should be displayed</dd>
</dl>
</li>
</ul>
<a name="getActivityPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActivityPercentComplete()</pre>
<div class="block">This accessor method returns the percent complete value for this task
 as defined by the Percent Complete Type attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity percent complete</dd>
</dl>
</li>
</ul>
<a name="getMethodologyGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMethodologyGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getMethodologyGUID()</pre>
<div class="block">Retrieve the methodology GUID for this task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>methodology GUID</dd>
</dl>
</li>
</ul>
<a name="setMethodologyGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMethodologyGUID</h4>
<pre>public&nbsp;void&nbsp;setMethodologyGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Set the methodology GUID for this task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - methodology GUID</dd>
</dl>
</li>
</ul>
<a name="getFloatPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloatPath</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFloatPath()</pre>
<div class="block">Retrieve the float path number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>float path number</dd>
</dl>
</li>
</ul>
<a name="setFloatPath-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloatPath</h4>
<pre>public&nbsp;void&nbsp;setFloatPath(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the float path number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - float path number</dd>
</dl>
</li>
</ul>
<a name="getFloatPathOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloatPathOrder</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFloatPathOrder()</pre>
<div class="block">Retrieve the float path order.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>float path order</dd>
</dl>
</li>
</ul>
<a name="setFloatPathOrder-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloatPathOrder</h4>
<pre>public&nbsp;void&nbsp;setFloatPathOrder(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the float path order.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - float path order</dd>
</dl>
</li>
</ul>
<a name="getEffectiveCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEffectiveCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;getEffectiveCalendar()</pre>
<div class="block">Retrieve the effective calendar for this task. If the task does not have
 a specific calendar associated with it, fall back to using the default calendar
 for the project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectCalendar instance</dd>
</dl>
</li>
</ul>
<a name="getBaselineTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;getBaselineTask()</pre>
<div class="block">If the parent ProjectFile has one or more baseline ProjectFile instances,
 this method will allow you to retrieve the baseline task associated
 with this current task. If no baseline task is present this method will return null.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline task or null</dd>
</dl>
</li>
</ul>
<a name="getBaselineTask-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineTask</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;getBaselineTask(int&nbsp;index)</pre>
<div class="block">If the parent ProjectFile has one or more baseline ProjectFile instances,
 this method will allow you to retrieve the baseline task associated
 with this current task. If no baseline task is present this method will return null.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - baseline index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline task or null</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="isPredecessor-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPredecessor</h4>
<pre>public&nbsp;boolean&nbsp;isPredecessor(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">Utility method used to determine if the supplied task
 is a predecessor of the current task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - potential predecessor task</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Boolean flag</dd>
</dl>
</li>
</ul>
<a name="isSuccessor-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSuccessor</h4>
<pre>public&nbsp;boolean&nbsp;isSuccessor(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">Utility method used to determine if the supplied task
 is a successor of the current task.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - potential successor task</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Boolean flag</dd>
</dl>
</li>
</ul>
<a name="hasChildTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasChildTasks</h4>
<pre>public&nbsp;boolean&nbsp;hasChildTasks()</pre>
<div class="block">Used to determine if a task has child tasks.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if the task has child tasks</dd>
</dl>
</li>
</ul>
<a name="getParentFile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentFile</h4>
<pre>public final&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getParentFile()</pre>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>reference to this the parent ProjectFile instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TableContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TaskContainer.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Task.html" target="_top">Frames</a></li>
<li><a href="Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
