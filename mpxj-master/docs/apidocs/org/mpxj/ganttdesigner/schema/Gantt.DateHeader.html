<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Gantt.DateHeader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Gantt.DateHeader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gantt.DateHeader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" target="_top">Frames</a></li>
<li><a href="Gantt.DateHeader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.ganttdesigner.schema</div>
<h2 title="Class Gantt.DateHeader" class="title">Class Gantt.DateHeader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.ganttdesigner.schema.Gantt.DateHeader</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html" title="class in org.mpxj.ganttdesigner.schema">Gantt</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Gantt.DateHeader</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="Tier" maxOccurs="unbounded"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Tick" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Reference"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                 &lt;attribute name="Day0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Week0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Month0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Year0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
       &lt;attribute name="Tiers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="TierHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="DayWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="DarkGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="LightGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="ColorBackground" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="ColorGridlines" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="AltLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="Lightness" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Tier</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#altLight">altLight</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#colorBackground">colorBackground</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#colorGridlines">colorGridlines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#darkGrid">darkGrid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#dayWidth">dayWidth</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#lightGrid">lightGrid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#lightness">lightness</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#reference">reference</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Tier</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#tier">tier</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#tierHeight">tierHeight</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#tiers">tiers</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#DateHeader--">DateHeader</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getAltLight--">getAltLight</a></span>()</code>
<div class="block">Gets the value of the altLight property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getColorBackground--">getColorBackground</a></span>()</code>
<div class="block">Gets the value of the colorBackground property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getColorGridlines--">getColorGridlines</a></span>()</code>
<div class="block">Gets the value of the colorGridlines property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getDarkGrid--">getDarkGrid</a></span>()</code>
<div class="block">Gets the value of the darkGrid property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getDayWidth--">getDayWidth</a></span>()</code>
<div class="block">Gets the value of the dayWidth property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getLightGrid--">getLightGrid</a></span>()</code>
<div class="block">Gets the value of the lightGrid property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getLightness--">getLightness</a></span>()</code>
<div class="block">Gets the value of the lightness property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getReference--">getReference</a></span>()</code>
<div class="block">Gets the value of the reference property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Tier</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getTier--">getTier</a></span>()</code>
<div class="block">Gets the value of the tier property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getTierHeight--">getTierHeight</a></span>()</code>
<div class="block">Gets the value of the tierHeight property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#getTiers--">getTiers</a></span>()</code>
<div class="block">Gets the value of the tiers property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setAltLight-java.lang.Integer-">setAltLight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the altLight property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setColorBackground-java.lang.Integer-">setColorBackground</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the colorBackground property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setColorGridlines-java.lang.Integer-">setColorGridlines</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the colorGridlines property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setDarkGrid-java.lang.Integer-">setDarkGrid</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the darkGrid property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setDayWidth-java.lang.Integer-">setDayWidth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the dayWidth property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setLightGrid-java.lang.Integer-">setLightGrid</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the lightGrid property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setLightness-java.lang.Double-">setLightness</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the lightness property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setReference-org.mpxj.ganttdesigner.schema.Gantt.DateHeader.Reference-">setReference</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a>&nbsp;value)</code>
<div class="block">Sets the value of the reference property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setTierHeight-java.lang.Integer-">setTierHeight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the tierHeight property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html#setTiers-java.lang.Integer-">setTiers</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the tiers property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="tier">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tier</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Tier</a>&gt; tier</pre>
</li>
</ul>
<a name="reference">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reference</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a> reference</pre>
</li>
</ul>
<a name="tiers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tiers</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> tiers</pre>
</li>
</ul>
<a name="tierHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tierHeight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> tierHeight</pre>
</li>
</ul>
<a name="dayWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dayWidth</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> dayWidth</pre>
</li>
</ul>
<a name="darkGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>darkGrid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> darkGrid</pre>
</li>
</ul>
<a name="lightGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lightGrid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> lightGrid</pre>
</li>
</ul>
<a name="colorBackground">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorBackground</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> colorBackground</pre>
</li>
</ul>
<a name="colorGridlines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>colorGridlines</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> colorGridlines</pre>
</li>
</ul>
<a name="altLight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>altLight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> altLight</pre>
</li>
</ul>
<a name="lightness">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>lightness</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> lightness</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DateHeader--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DateHeader</h4>
<pre>public&nbsp;DateHeader()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTier</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Tier</a>&gt;&nbsp;getTier()</pre>
<div class="block">Gets the value of the tier property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the tier property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getTier().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.DateHeader.Tier</code></a></div>
</li>
</ul>
<a name="getReference--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReference</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a>&nbsp;getReference()</pre>
<div class="block">Gets the value of the reference property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.DateHeader.Reference</code></a></dd>
</dl>
</li>
</ul>
<a name="setReference-org.mpxj.ganttdesigner.schema.Gantt.DateHeader.Reference-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReference</h4>
<pre>public&nbsp;void&nbsp;setReference(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader.Reference</a>&nbsp;value)</pre>
<div class="block">Sets the value of the reference property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.DateHeader.Reference</code></a></dd>
</dl>
</li>
</ul>
<a name="getTiers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTiers</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getTiers()</pre>
<div class="block">Gets the value of the tiers property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setTiers-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTiers</h4>
<pre>public&nbsp;void&nbsp;setTiers(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the tiers property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getTierHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTierHeight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getTierHeight()</pre>
<div class="block">Gets the value of the tierHeight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setTierHeight-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTierHeight</h4>
<pre>public&nbsp;void&nbsp;setTierHeight(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the tierHeight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getDayWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayWidth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDayWidth()</pre>
<div class="block">Gets the value of the dayWidth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setDayWidth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayWidth</h4>
<pre>public&nbsp;void&nbsp;setDayWidth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dayWidth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getDarkGrid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDarkGrid</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDarkGrid()</pre>
<div class="block">Gets the value of the darkGrid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setDarkGrid-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDarkGrid</h4>
<pre>public&nbsp;void&nbsp;setDarkGrid(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the darkGrid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getLightGrid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightGrid</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLightGrid()</pre>
<div class="block">Gets the value of the lightGrid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setLightGrid-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightGrid</h4>
<pre>public&nbsp;void&nbsp;setLightGrid(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lightGrid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getColorBackground--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColorBackground</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getColorBackground()</pre>
<div class="block">Gets the value of the colorBackground property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setColorBackground-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColorBackground</h4>
<pre>public&nbsp;void&nbsp;setColorBackground(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the colorBackground property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getColorGridlines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColorGridlines</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getColorGridlines()</pre>
<div class="block">Gets the value of the colorGridlines property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setColorGridlines-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColorGridlines</h4>
<pre>public&nbsp;void&nbsp;setColorGridlines(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the colorGridlines property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getAltLight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAltLight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getAltLight()</pre>
<div class="block">Gets the value of the altLight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setAltLight-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAltLight</h4>
<pre>public&nbsp;void&nbsp;setAltLight(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the altLight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getLightness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightness</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getLightness()</pre>
<div class="block">Gets the value of the lightness property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang"><code>Double</code></a></dd>
</dl>
</li>
</ul>
<a name="setLightness-java.lang.Double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setLightness</h4>
<pre>public&nbsp;void&nbsp;setLightness(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lightness property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang"><code>Double</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gantt.DateHeader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" target="_top">Frames</a></li>
<li><a href="Gantt.DateHeader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
