<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Gantt (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Gantt (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gantt.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Adapter5.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/Gantt.html" target="_top">Frames</a></li>
<li><a href="Gantt.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.ganttdesigner.schema</div>
<h2 title="Class Gantt" class="title">Class Gantt</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.ganttdesigner.schema.Gantt</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Gantt</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="Display"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Height" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Split" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="File"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Saved" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                 &lt;attribute name="Created" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                 &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Physical" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Globalization"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Culture"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="UICulture"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Cal" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Parent" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="Currency"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="LCID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="ISO" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DN" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Currency" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
                 &lt;attribute name="RegionInfo" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="FirstDay"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="LastDay"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Padding"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Left" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Top" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Right" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Bottom" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Tasks"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Task" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="S" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                           &lt;attribute name="B" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="BC" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="D" type="{}ganttDesignerDuration" /&gt;
                           &lt;attribute name="H" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="U" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="VA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="In" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="C" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="PC" type="{}ganttDesignerPercent" /&gt;
                           &lt;attribute name="DL" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                           &lt;attribute name="P" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="L" type="{}ganttDesignerDuration" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Remarks" type="{}ganttDesignerRemark"/&gt;
         &lt;element name="Remarks1" type="{}ganttDesignerRemark"/&gt;
         &lt;element name="Remarks2" type="{}ganttDesignerRemark"/&gt;
         &lt;element name="Remarks3" type="{}ganttDesignerRemark"/&gt;
         &lt;element name="Remarks4" type="{}ganttDesignerRemark"/&gt;
         &lt;element name="TextStyles"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Font" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
                 &lt;attribute name="Flag" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Deadline" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="BarStyles"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Color2" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Color3" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Columns"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Header" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="W" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="A" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="D" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DA" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DNW" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Calendar"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="WorkDays" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="WeekStart" type="{}ganttDesignerDay" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="DateHeader"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Tier" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Tick" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="Reference"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="value" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                           &lt;attribute name="Day0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Week0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Month0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Year0" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
                 &lt;attribute name="Tiers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="TierHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="DayWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="DarkGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="LightGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="ColorBackground" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="ColorGridlines" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="AltLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Lightness" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Holidays"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Holiday" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Date" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Headers"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="HeaderLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="HeadersFonts"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Font" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Footers"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="FooterLineSpace" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="FootersFonts"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Font" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="Style" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Print"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="leftColumnsOnPage1Only" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                 &lt;attribute name="fitToPage" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfPages" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fitHorizontal" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="NumberSequence" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="PrintToImageFile"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Copy"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="allRows" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="toTask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumnsCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="numberOfLeftColumns" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="showTaskNumbers" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="dateOnRightCompDisp" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="fromDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                 &lt;attribute name="toDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ChartColor"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="ChartBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="BorderBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="LeftPanelBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="LeftPanelGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="RightPanelHGrid" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="RightPanelVGridDark" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="RightPanelVGridLight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="HolidayShade" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="SelectedRowBack" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="SelectedRowFore" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#barStyles">barStyles</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#calendar">calendar</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#chartColor">chartColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#columns">columns</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#copy">copy</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#dateHeader">dateHeader</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#display">display</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#file">file</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#firstDay">firstDay</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#footers">footers</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#footersFonts">footersFonts</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#globalization">globalization</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#headers">headers</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#headersFonts">headersFonts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#holidays">holidays</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#lastDay">lastDay</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#padding">padding</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#print">print</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#printToImageFile">printToImageFile</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#remarks">remarks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#remarks1">remarks1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#remarks2">remarks2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#remarks3">remarks3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#remarks4">remarks4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#tasks">tasks</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#textStyles">textStyles</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#version">version</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#Gantt--">Gantt</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getBarStyles--">getBarStyles</a></span>()</code>
<div class="block">Gets the value of the barStyles property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getCalendar--">getCalendar</a></span>()</code>
<div class="block">Gets the value of the calendar property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getChartColor--">getChartColor</a></span>()</code>
<div class="block">Gets the value of the chartColor property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getColumns--">getColumns</a></span>()</code>
<div class="block">Gets the value of the columns property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getCopy--">getCopy</a></span>()</code>
<div class="block">Gets the value of the copy property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getDateHeader--">getDateHeader</a></span>()</code>
<div class="block">Gets the value of the dateHeader property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getDisplay--">getDisplay</a></span>()</code>
<div class="block">Gets the value of the display property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getFile--">getFile</a></span>()</code>
<div class="block">Gets the value of the file property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getFirstDay--">getFirstDay</a></span>()</code>
<div class="block">Gets the value of the firstDay property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getFooters--">getFooters</a></span>()</code>
<div class="block">Gets the value of the footers property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getFootersFonts--">getFootersFonts</a></span>()</code>
<div class="block">Gets the value of the footersFonts property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getGlobalization--">getGlobalization</a></span>()</code>
<div class="block">Gets the value of the globalization property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getHeaders--">getHeaders</a></span>()</code>
<div class="block">Gets the value of the headers property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getHeadersFonts--">getHeadersFonts</a></span>()</code>
<div class="block">Gets the value of the headersFonts property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getHolidays--">getHolidays</a></span>()</code>
<div class="block">Gets the value of the holidays property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getLastDay--">getLastDay</a></span>()</code>
<div class="block">Gets the value of the lastDay property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getPadding--">getPadding</a></span>()</code>
<div class="block">Gets the value of the padding property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getPrint--">getPrint</a></span>()</code>
<div class="block">Gets the value of the print property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getPrintToImageFile--">getPrintToImageFile</a></span>()</code>
<div class="block">Gets the value of the printToImageFile property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getRemarks--">getRemarks</a></span>()</code>
<div class="block">Gets the value of the remarks property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getRemarks1--">getRemarks1</a></span>()</code>
<div class="block">Gets the value of the remarks1 property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getRemarks2--">getRemarks2</a></span>()</code>
<div class="block">Gets the value of the remarks2 property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getRemarks3--">getRemarks3</a></span>()</code>
<div class="block">Gets the value of the remarks3 property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getRemarks4--">getRemarks4</a></span>()</code>
<div class="block">Gets the value of the remarks4 property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getTasks--">getTasks</a></span>()</code>
<div class="block">Gets the value of the tasks property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getTextStyles--">getTextStyles</a></span>()</code>
<div class="block">Gets the value of the textStyles property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Gets the value of the version property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setBarStyles-org.mpxj.ganttdesigner.schema.Gantt.BarStyles-">setBarStyles</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a>&nbsp;value)</code>
<div class="block">Sets the value of the barStyles property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setCalendar-org.mpxj.ganttdesigner.schema.Gantt.Calendar-">setCalendar</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendar property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setChartColor-org.mpxj.ganttdesigner.schema.Gantt.ChartColor-">setChartColor</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a>&nbsp;value)</code>
<div class="block">Sets the value of the chartColor property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setColumns-org.mpxj.ganttdesigner.schema.Gantt.Columns-">setColumns</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a>&nbsp;value)</code>
<div class="block">Sets the value of the columns property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setCopy-org.mpxj.ganttdesigner.schema.Gantt.Copy-">setCopy</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a>&nbsp;value)</code>
<div class="block">Sets the value of the copy property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setDateHeader-org.mpxj.ganttdesigner.schema.Gantt.DateHeader-">setDateHeader</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateHeader property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setDisplay-org.mpxj.ganttdesigner.schema.Gantt.Display-">setDisplay</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a>&nbsp;value)</code>
<div class="block">Sets the value of the display property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setFile-org.mpxj.ganttdesigner.schema.Gantt.File-">setFile</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a>&nbsp;value)</code>
<div class="block">Sets the value of the file property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setFirstDay-org.mpxj.ganttdesigner.schema.Gantt.FirstDay-">setFirstDay</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a>&nbsp;value)</code>
<div class="block">Sets the value of the firstDay property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setFooters-org.mpxj.ganttdesigner.schema.Gantt.Footers-">setFooters</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a>&nbsp;value)</code>
<div class="block">Sets the value of the footers property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setFootersFonts-org.mpxj.ganttdesigner.schema.Gantt.FootersFonts-">setFootersFonts</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a>&nbsp;value)</code>
<div class="block">Sets the value of the footersFonts property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setGlobalization-org.mpxj.ganttdesigner.schema.Gantt.Globalization-">setGlobalization</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a>&nbsp;value)</code>
<div class="block">Sets the value of the globalization property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setHeaders-org.mpxj.ganttdesigner.schema.Gantt.Headers-">setHeaders</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a>&nbsp;value)</code>
<div class="block">Sets the value of the headers property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setHeadersFonts-org.mpxj.ganttdesigner.schema.Gantt.HeadersFonts-">setHeadersFonts</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a>&nbsp;value)</code>
<div class="block">Sets the value of the headersFonts property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setHolidays-org.mpxj.ganttdesigner.schema.Gantt.Holidays-">setHolidays</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a>&nbsp;value)</code>
<div class="block">Sets the value of the holidays property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setLastDay-org.mpxj.ganttdesigner.schema.Gantt.LastDay-">setLastDay</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastDay property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setPadding-org.mpxj.ganttdesigner.schema.Gantt.Padding-">setPadding</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a>&nbsp;value)</code>
<div class="block">Sets the value of the padding property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setPrint-org.mpxj.ganttdesigner.schema.Gantt.Print-">setPrint</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a>&nbsp;value)</code>
<div class="block">Sets the value of the print property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setPrintToImageFile-org.mpxj.ganttdesigner.schema.Gantt.PrintToImageFile-">setPrintToImageFile</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a>&nbsp;value)</code>
<div class="block">Sets the value of the printToImageFile property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setRemarks-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">setRemarks</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</code>
<div class="block">Sets the value of the remarks property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setRemarks1-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">setRemarks1</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</code>
<div class="block">Sets the value of the remarks1 property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setRemarks2-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">setRemarks2</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</code>
<div class="block">Sets the value of the remarks2 property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setRemarks3-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">setRemarks3</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</code>
<div class="block">Sets the value of the remarks3 property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setRemarks4-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">setRemarks4</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</code>
<div class="block">Sets the value of the remarks4 property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setTasks-org.mpxj.ganttdesigner.schema.Gantt.Tasks-">setTasks</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a>&nbsp;value)</code>
<div class="block">Sets the value of the tasks property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setTextStyles-org.mpxj.ganttdesigner.schema.Gantt.TextStyles-">setTextStyles</a></span>(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a>&nbsp;value)</code>
<div class="block">Sets the value of the textStyles property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html#setVersion-java.lang.String-">setVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the version property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="display">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>display</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a> display</pre>
</li>
</ul>
<a name="file">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>file</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a> file</pre>
</li>
</ul>
<a name="globalization">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>globalization</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a> globalization</pre>
</li>
</ul>
<a name="firstDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstDay</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a> firstDay</pre>
</li>
</ul>
<a name="lastDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastDay</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a> lastDay</pre>
</li>
</ul>
<a name="padding">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>padding</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a> padding</pre>
</li>
</ul>
<a name="tasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tasks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a> tasks</pre>
</li>
</ul>
<a name="remarks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remarks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a> remarks</pre>
</li>
</ul>
<a name="remarks1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remarks1</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a> remarks1</pre>
</li>
</ul>
<a name="remarks2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remarks2</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a> remarks2</pre>
</li>
</ul>
<a name="remarks3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remarks3</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a> remarks3</pre>
</li>
</ul>
<a name="remarks4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remarks4</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a> remarks4</pre>
</li>
</ul>
<a name="textStyles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>textStyles</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a> textStyles</pre>
</li>
</ul>
<a name="barStyles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>barStyles</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a> barStyles</pre>
</li>
</ul>
<a name="columns">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>columns</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a> columns</pre>
</li>
</ul>
<a name="calendar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendar</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a> calendar</pre>
</li>
</ul>
<a name="dateHeader">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateHeader</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a> dateHeader</pre>
</li>
</ul>
<a name="holidays">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>holidays</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a> holidays</pre>
</li>
</ul>
<a name="headers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headers</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a> headers</pre>
</li>
</ul>
<a name="headersFonts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headersFonts</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a> headersFonts</pre>
</li>
</ul>
<a name="footers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footers</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a> footers</pre>
</li>
</ul>
<a name="footersFonts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footersFonts</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a> footersFonts</pre>
</li>
</ul>
<a name="print">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a> print</pre>
</li>
</ul>
<a name="printToImageFile">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printToImageFile</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a> printToImageFile</pre>
</li>
</ul>
<a name="copy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copy</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a> copy</pre>
</li>
</ul>
<a name="chartColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>chartColor</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a> chartColor</pre>
</li>
</ul>
<a name="version">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>version</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> version</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Gantt--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Gantt</h4>
<pre>public&nbsp;Gantt()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDisplay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a>&nbsp;getDisplay()</pre>
<div class="block">Gets the value of the display property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Display</code></a></dd>
</dl>
</li>
</ul>
<a name="setDisplay-org.mpxj.ganttdesigner.schema.Gantt.Display-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisplay</h4>
<pre>public&nbsp;void&nbsp;setDisplay(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Display</a>&nbsp;value)</pre>
<div class="block">Sets the value of the display property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Display</code></a></dd>
</dl>
</li>
</ul>
<a name="getFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFile</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a>&nbsp;getFile()</pre>
<div class="block">Gets the value of the file property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.File</code></a></dd>
</dl>
</li>
</ul>
<a name="setFile-org.mpxj.ganttdesigner.schema.Gantt.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFile</h4>
<pre>public&nbsp;void&nbsp;setFile(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.File</a>&nbsp;value)</pre>
<div class="block">Sets the value of the file property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.File</code></a></dd>
</dl>
</li>
</ul>
<a name="getGlobalization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGlobalization</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a>&nbsp;getGlobalization()</pre>
<div class="block">Gets the value of the globalization property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Globalization</code></a></dd>
</dl>
</li>
</ul>
<a name="setGlobalization-org.mpxj.ganttdesigner.schema.Gantt.Globalization-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGlobalization</h4>
<pre>public&nbsp;void&nbsp;setGlobalization(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Globalization</a>&nbsp;value)</pre>
<div class="block">Sets the value of the globalization property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Globalization</code></a></dd>
</dl>
</li>
</ul>
<a name="getFirstDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a>&nbsp;getFirstDay()</pre>
<div class="block">Gets the value of the firstDay property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.FirstDay</code></a></dd>
</dl>
</li>
</ul>
<a name="setFirstDay-org.mpxj.ganttdesigner.schema.Gantt.FirstDay-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstDay</h4>
<pre>public&nbsp;void&nbsp;setFirstDay(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FirstDay</a>&nbsp;value)</pre>
<div class="block">Sets the value of the firstDay property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.FirstDay</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a>&nbsp;getLastDay()</pre>
<div class="block">Gets the value of the lastDay property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.LastDay</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastDay-org.mpxj.ganttdesigner.schema.Gantt.LastDay-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastDay</h4>
<pre>public&nbsp;void&nbsp;setLastDay(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.LastDay</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastDay property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.LastDay</code></a></dd>
</dl>
</li>
</ul>
<a name="getPadding--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPadding</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a>&nbsp;getPadding()</pre>
<div class="block">Gets the value of the padding property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Padding</code></a></dd>
</dl>
</li>
</ul>
<a name="setPadding-org.mpxj.ganttdesigner.schema.Gantt.Padding-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPadding</h4>
<pre>public&nbsp;void&nbsp;setPadding(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Padding</a>&nbsp;value)</pre>
<div class="block">Sets the value of the padding property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Padding</code></a></dd>
</dl>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a>&nbsp;getTasks()</pre>
<div class="block">Gets the value of the tasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="setTasks-org.mpxj.ganttdesigner.schema.Gantt.Tasks-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTasks</h4>
<pre>public&nbsp;void&nbsp;setTasks(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Tasks</a>&nbsp;value)</pre>
<div class="block">Sets the value of the tasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemarks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemarks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;getRemarks()</pre>
<div class="block">Gets the value of the remarks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemarks-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemarks</h4>
<pre>public&nbsp;void&nbsp;setRemarks(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remarks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemarks1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemarks1</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;getRemarks1()</pre>
<div class="block">Gets the value of the remarks1 property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemarks1-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemarks1</h4>
<pre>public&nbsp;void&nbsp;setRemarks1(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remarks1 property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemarks2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemarks2</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;getRemarks2()</pre>
<div class="block">Gets the value of the remarks2 property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemarks2-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemarks2</h4>
<pre>public&nbsp;void&nbsp;setRemarks2(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remarks2 property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemarks3--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemarks3</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;getRemarks3()</pre>
<div class="block">Gets the value of the remarks3 property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemarks3-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemarks3</h4>
<pre>public&nbsp;void&nbsp;setRemarks3(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remarks3 property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemarks4--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemarks4</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;getRemarks4()</pre>
<div class="block">Gets the value of the remarks4 property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemarks4-org.mpxj.ganttdesigner.schema.GanttDesignerRemark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemarks4</h4>
<pre>public&nbsp;void&nbsp;setRemarks4(<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema">GanttDesignerRemark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remarks4 property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><code>GanttDesignerRemark</code></a></dd>
</dl>
</li>
</ul>
<a name="getTextStyles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextStyles</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a>&nbsp;getTextStyles()</pre>
<div class="block">Gets the value of the textStyles property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.TextStyles</code></a></dd>
</dl>
</li>
</ul>
<a name="setTextStyles-org.mpxj.ganttdesigner.schema.Gantt.TextStyles-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTextStyles</h4>
<pre>public&nbsp;void&nbsp;setTextStyles(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.TextStyles</a>&nbsp;value)</pre>
<div class="block">Sets the value of the textStyles property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.TextStyles</code></a></dd>
</dl>
</li>
</ul>
<a name="getBarStyles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarStyles</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a>&nbsp;getBarStyles()</pre>
<div class="block">Gets the value of the barStyles property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.BarStyles</code></a></dd>
</dl>
</li>
</ul>
<a name="setBarStyles-org.mpxj.ganttdesigner.schema.Gantt.BarStyles-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarStyles</h4>
<pre>public&nbsp;void&nbsp;setBarStyles(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.BarStyles</a>&nbsp;value)</pre>
<div class="block">Sets the value of the barStyles property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.BarStyles</code></a></dd>
</dl>
</li>
</ul>
<a name="getColumns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColumns</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a>&nbsp;getColumns()</pre>
<div class="block">Gets the value of the columns property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Columns</code></a></dd>
</dl>
</li>
</ul>
<a name="setColumns-org.mpxj.ganttdesigner.schema.Gantt.Columns-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColumns</h4>
<pre>public&nbsp;void&nbsp;setColumns(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Columns</a>&nbsp;value)</pre>
<div class="block">Sets the value of the columns property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Columns</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendar</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a>&nbsp;getCalendar()</pre>
<div class="block">Gets the value of the calendar property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Calendar</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendar-org.mpxj.ganttdesigner.schema.Gantt.Calendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendar</h4>
<pre>public&nbsp;void&nbsp;setCalendar(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Calendar</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendar property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Calendar</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateHeader</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a>&nbsp;getDateHeader()</pre>
<div class="block">Gets the value of the dateHeader property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.DateHeader</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateHeader-org.mpxj.ganttdesigner.schema.Gantt.DateHeader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateHeader</h4>
<pre>public&nbsp;void&nbsp;setDateHeader(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.DateHeader</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateHeader property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.DateHeader</code></a></dd>
</dl>
</li>
</ul>
<a name="getHolidays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHolidays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a>&nbsp;getHolidays()</pre>
<div class="block">Gets the value of the holidays property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Holidays</code></a></dd>
</dl>
</li>
</ul>
<a name="setHolidays-org.mpxj.ganttdesigner.schema.Gantt.Holidays-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHolidays</h4>
<pre>public&nbsp;void&nbsp;setHolidays(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Holidays</a>&nbsp;value)</pre>
<div class="block">Sets the value of the holidays property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Holidays</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaders</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a>&nbsp;getHeaders()</pre>
<div class="block">Gets the value of the headers property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Headers</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaders-org.mpxj.ganttdesigner.schema.Gantt.Headers-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeaders</h4>
<pre>public&nbsp;void&nbsp;setHeaders(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Headers</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headers property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Headers</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeadersFonts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadersFonts</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a>&nbsp;getHeadersFonts()</pre>
<div class="block">Gets the value of the headersFonts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.HeadersFonts</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeadersFonts-org.mpxj.ganttdesigner.schema.Gantt.HeadersFonts-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeadersFonts</h4>
<pre>public&nbsp;void&nbsp;setHeadersFonts(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.HeadersFonts</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headersFonts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.HeadersFonts</code></a></dd>
</dl>
</li>
</ul>
<a name="getFooters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooters</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a>&nbsp;getFooters()</pre>
<div class="block">Gets the value of the footers property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Footers</code></a></dd>
</dl>
</li>
</ul>
<a name="setFooters-org.mpxj.ganttdesigner.schema.Gantt.Footers-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooters</h4>
<pre>public&nbsp;void&nbsp;setFooters(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Footers</a>&nbsp;value)</pre>
<div class="block">Sets the value of the footers property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Footers</code></a></dd>
</dl>
</li>
</ul>
<a name="getFootersFonts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFootersFonts</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a>&nbsp;getFootersFonts()</pre>
<div class="block">Gets the value of the footersFonts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.FootersFonts</code></a></dd>
</dl>
</li>
</ul>
<a name="setFootersFonts-org.mpxj.ganttdesigner.schema.Gantt.FootersFonts-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFootersFonts</h4>
<pre>public&nbsp;void&nbsp;setFootersFonts(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.FootersFonts</a>&nbsp;value)</pre>
<div class="block">Sets the value of the footersFonts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.FootersFonts</code></a></dd>
</dl>
</li>
</ul>
<a name="getPrint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrint</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a>&nbsp;getPrint()</pre>
<div class="block">Gets the value of the print property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Print</code></a></dd>
</dl>
</li>
</ul>
<a name="setPrint-org.mpxj.ganttdesigner.schema.Gantt.Print-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrint</h4>
<pre>public&nbsp;void&nbsp;setPrint(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Print</a>&nbsp;value)</pre>
<div class="block">Sets the value of the print property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Print</code></a></dd>
</dl>
</li>
</ul>
<a name="getPrintToImageFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintToImageFile</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a>&nbsp;getPrintToImageFile()</pre>
<div class="block">Gets the value of the printToImageFile property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.PrintToImageFile</code></a></dd>
</dl>
</li>
</ul>
<a name="setPrintToImageFile-org.mpxj.ganttdesigner.schema.Gantt.PrintToImageFile-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintToImageFile</h4>
<pre>public&nbsp;void&nbsp;setPrintToImageFile(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.PrintToImageFile</a>&nbsp;value)</pre>
<div class="block">Sets the value of the printToImageFile property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.PrintToImageFile</code></a></dd>
</dl>
</li>
</ul>
<a name="getCopy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCopy</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a>&nbsp;getCopy()</pre>
<div class="block">Gets the value of the copy property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Copy</code></a></dd>
</dl>
</li>
</ul>
<a name="setCopy-org.mpxj.ganttdesigner.schema.Gantt.Copy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCopy</h4>
<pre>public&nbsp;void&nbsp;setCopy(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.Copy</a>&nbsp;value)</pre>
<div class="block">Sets the value of the copy property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.Copy</code></a></dd>
</dl>
</li>
</ul>
<a name="getChartColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChartColor</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a>&nbsp;getChartColor()</pre>
<div class="block">Gets the value of the chartColor property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.ChartColor</code></a></dd>
</dl>
</li>
</ul>
<a name="setChartColor-org.mpxj.ganttdesigner.schema.Gantt.ChartColor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChartColor</h4>
<pre>public&nbsp;void&nbsp;setChartColor(<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema">Gantt.ChartColor</a>&nbsp;value)</pre>
<div class="block">Sets the value of the chartColor property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema"><code>Gantt.ChartColor</code></a></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getVersion()</pre>
<div class="block">Gets the value of the version property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVersion</h4>
<pre>public&nbsp;void&nbsp;setVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the version property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Gantt.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Adapter5.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/Gantt.html" target="_top">Frames</a></li>
<li><a href="Gantt.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
