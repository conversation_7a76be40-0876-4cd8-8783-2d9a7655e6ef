<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GanttChartView (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GanttChartView (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":6,"i97":6,"i98":6,"i99":6,"i100":6,"i101":6,"i102":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttChartView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarStyleFactoryCommon.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttChartView12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttChartView.html" target="_top">Frames</a></li>
<li><a href="GanttChartView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mpp</div>
<h2 title="Class GanttChartView" class="title">Class GanttChartView</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/mpp/AbstractView.html" title="class in org.mpxj.mpp">org.mpxj.mpp.AbstractView</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/mpp/AbstractMppView.html" title="class in org.mpxj.mpp">org.mpxj.mpp.AbstractMppView</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/mpp/GenericView.html" title="class in org.mpxj.mpp">org.mpxj.mpp.GenericView</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mpp.GanttChartView</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/mpxj/View.html" title="interface in org.mpxj">View</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/mpxj/mpp/GanttChartView12.html" title="class in org.mpxj.mpp">GanttChartView12</a>, <a href="../../../org/mpxj/mpp/GanttChartView14.html" title="class in org.mpxj.mpp">GanttChartView14</a>, <a href="../../../org/mpxj/mpp/GanttChartView9.html" title="class in org.mpxj.mpp">GanttChartView9</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">GanttChartView</span>
extends <a href="../../../org/mpxj/mpp/GenericView.html" title="class in org.mpxj.mpp">GenericView</a></pre>
<div class="block">This class represents the set of properties used to define the appearance
 of a Gantt chart view in MS Project.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_alwaysRollupGanttBars">m_alwaysRollupGanttBars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_autoFilters">m_autoFilters</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_autoFiltersByType">m_autoFiltersByType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barDateFormat">m_barDateFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barRowsGridLines">m_barRowsGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barStyleExceptions">m_barStyleExceptions</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barStyles">m_barStyles</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barStylesMap">m_barStylesMap</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barTextBottomFontStyle">m_barTextBottomFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barTextInsideFontStyle">m_barTextInsideFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barTextLeftFontStyle">m_barTextLeftFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barTextRightFontStyle">m_barTextRightFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_barTextTopFontStyle">m_barTextTopFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_bottomTierColumnGridLines">m_bottomTierColumnGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_bottomTimescaleFontStyle">m_bottomTimescaleFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_criticalTasksFontStyle">m_criticalTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_currentDateGridLines">m_currentDateGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_externalTasksFontStyle">m_externalTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_ganttBarHeight">m_ganttBarHeight</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_ganttRowsGridLines">m_ganttRowsGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_hideRollupBarsWhenSummaryExpanded">m_hideRollupBarsWhenSummaryExpanded</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_highlightedTasksFontStyle">m_highlightedTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp">LinkStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_linkStyle">m_linkStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_markedTasksFontStyle">m_markedTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_middleTierColumnGridLines">m_middleTierColumnGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_middleTimescaleFontStyle">m_middleTimescaleFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_milestoneTasksFontStyle">m_milestoneTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_nonCriticalTasksFontStyle">m_nonCriticalTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_nonWorkingColor">m_nonWorkingColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_nonWorkingDaysCalendarName">m_nonWorkingDaysCalendarName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp">ChartPattern</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_nonWorkingPattern">m_nonWorkingPattern</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp">NonWorkingTimeStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_nonWorkingStyle">m_nonWorkingStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_pageBreakGridLines">m_pageBreakGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesActualPlan">m_progressLinesActualPlan</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesAtCurrentDate">m_progressLinesAtCurrentDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesAtRecurringIntervals">m_progressLinesAtRecurringIntervals</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesBeginAtDate">m_progressLinesBeginAtDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesBeginAtProjectStart">m_progressLinesBeginAtProjectStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesCurrentLineColor">m_progressLinesCurrentLineColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesCurrentLineStyle">m_progressLinesCurrentLineStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesCurrentProgressPointColor">m_progressLinesCurrentProgressPointColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesCurrentProgressPointShape">m_progressLinesCurrentProgressPointShape</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesDateFormat">m_progressLinesDateFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesDisplaySelected">m_progressLinesDisplaySelected</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesDisplaySelectedDates">m_progressLinesDisplaySelectedDates</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesDisplayType">m_progressLinesDisplayType</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesEnabled">m_progressLinesEnabled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesFontStyle">m_progressLinesFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp">Interval</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesInterval">m_progressLinesInterval</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalDailyDayNumber">m_progressLinesIntervalDailyDayNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalDailyWorkday">m_progressLinesIntervalDailyWorkday</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyDay">m_progressLinesIntervalMonthlyDay</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyDayDayNumber">m_progressLinesIntervalMonthlyDayDayNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyDayMonthNumber">m_progressLinesIntervalMonthlyDayMonthNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyFirstLast">m_progressLinesIntervalMonthlyFirstLast</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp">ProgressLineDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyFirstLastDay">m_progressLinesIntervalMonthlyFirstLastDay</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalMonthlyFirstLastMonthNumber">m_progressLinesIntervalMonthlyFirstLastMonthNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalWeekleyWeekNumber">m_progressLinesIntervalWeekleyWeekNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesIntervalWeeklyDay">m_progressLinesIntervalWeeklyDay</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesOtherLineColor">m_progressLinesOtherLineColor</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesOtherLineStyle">m_progressLinesOtherLineStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesOtherProgressPointColor">m_progressLinesOtherProgressPointColor</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesOtherProgressPointShape">m_progressLinesOtherProgressPointShape</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_progressLinesShowDate">m_progressLinesShowDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_projectFinishGridLines">m_projectFinishGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_projectStartGridLines">m_projectStartGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_projectSummaryTasksFontStyle">m_projectSummaryTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_roundBarsToWholeDays">m_roundBarsToWholeDays</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_rowAndColumnFontStyle">m_rowAndColumnFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_sheetColumnsGridLines">m_sheetColumnsGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_sheetRowsGridLines">m_sheetRowsGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_showBarSplits">m_showBarSplits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_showDrawings">m_showDrawings</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_statusDateGridLines">m_statusDateGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_summaryTasksFontStyle">m_summaryTasksFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_tableFontStyles">m_tableFontStyles</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleBottomTier">m_timescaleBottomTier</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleMiddleTier">m_timescaleMiddleTier</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleScaleSeparator">m_timescaleScaleSeparator</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleShowTiers">m_timescaleShowTiers</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleSize">m_timescaleSize</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_timescaleTopTier">m_timescaleTopTier</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_titleHorizontalGridLines">m_titleHorizontalGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_titleVerticalGridLines">m_titleVerticalGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_topTierColumnGridLines">m_topTierColumnGridLines</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#m_topTimescaleFontStyle">m_topTimescaleFontStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#TIMESCALE_PROPERTIES">TIMESCALE_PROPERTIES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#VIEW_PROPERTIES">VIEW_PROPERTIES</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.mpxj.mpp.AbstractView">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.mpxj.mpp.<a href="../../../org/mpxj/mpp/AbstractView.html" title="class in org.mpxj.mpp">AbstractView</a></h3>
<code><a href="../../../org/mpxj/mpp/AbstractView.html#m_file">m_file</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#m_id">m_id</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#m_name">m_name</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#m_tableName">m_tableName</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#m_tables">m_tables</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#m_type">m_type</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getAlwaysRollupGanttBars--">getAlwaysRollupGanttBars</a></span>()</code>
<div class="block">Retrieve the always rollup Gantt bars flag.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getAutoFilterByType-org.mpxj.FieldType-">getAutoFilterByType</a></span>(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</code>
<div class="block">Retrieves the auto filter definition associated with an
 individual column.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getAutoFilters--">getAutoFilters</a></span>()</code>
<div class="block">Retrieves a list of all auto filters associated with this view.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarDateFormat--">getBarDateFormat</a></span>()</code>
<div class="block">Retrieve the bar date format.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarRowsGridLines--">getBarRowsGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarStyleExceptions--">getBarStyleExceptions</a></span>()</code>
<div class="block">Retrieve an array representing bar styles which have been defined
 by the user for a specific task.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarStyles--">getBarStyles</a></span>()</code>
<div class="block">Retrieve an array of bar styles which are applied to all Gantt
 chart bars, unless an exception has been defined.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarTextBottomFontStyle--">getBarTextBottomFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarTextInsideFontStyle--">getBarTextInsideFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarTextLeftFontStyle--">getBarTextLeftFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarTextRightFontStyle--">getBarTextRightFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBarTextTopFontStyle--">getBarTextTopFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBottomTierColumnGridLines--">getBottomTierColumnGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getBottomTimescaleFontStyle--">getBottomTimescaleFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getColumnFontStyle-org.mpxj.ProjectFile-byte:A-int-java.util.Map-">getColumnFontStyle</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                  byte[]&nbsp;data,
                  int&nbsp;offset,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>
<div class="block">Retrieve column font details from a block of property data.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getCriticalTasksFontStyle--">getCriticalTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getCurrentDateGridLines--">getCurrentDateGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getDefaultFilter--">getDefaultFilter</a></span>()</code>
<div class="block">Convenience method used to retrieve the default filter instance
 associated with this view.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getDefaultFilterName--">getDefaultFilterName</a></span>()</code>
<div class="block">Retrieve the name of the filter applied to this view.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getExternalTasksFontStyle--">getExternalTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getFontStyle-byte:A-int-java.util.Map-">getFontStyle</a></span>(byte[]&nbsp;data,
            int&nbsp;offset,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</code>
<div class="block">Retrieve font details from a block of property data.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getGanttBarHeight--">getGanttBarHeight</a></span>()</code>
<div class="block">Retrieve the height of the Gantt bars in this view.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getGanttBarStyleByID-java.lang.Integer-">getGanttBarStyleByID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Retrieve a list of Gantt Bar Styles for this view which match the supplied  ID.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getGanttRowsGridLines--">getGanttRowsGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getGroupName--">getGroupName</a></span>()</code>
<div class="block">Retrieve the name of the grouping applied to this view.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getHideRollupBarsWhenSummaryExpanded--">getHideRollupBarsWhenSummaryExpanded</a></span>()</code>
<div class="block">Retrieve the hide rollup bars when summary expanded.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getHighlightedTasksFontStyle--">getHighlightedTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getHighlightFilter--">getHighlightFilter</a></span>()</code>
<div class="block">Retrieve the highlight filter flag.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp">LinkStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getLinkStyle--">getLinkStyle</a></span>()</code>
<div class="block">Retrieve the bar link style.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getMarkedTasksFontStyle--">getMarkedTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getMiddleTierColumnGridLines--">getMiddleTierColumnGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getMiddleTimescaleFontStyle--">getMiddleTimescaleFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getMilestoneTasksFontStyle--">getMilestoneTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getNonCriticalTasksFontStyle--">getNonCriticalTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getNonWorkingColor--">getNonWorkingColor</a></span>()</code>
<div class="block">Retrieve the non-working time color.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getNonWorkingDaysCalendarName--">getNonWorkingDaysCalendarName</a></span>()</code>
<div class="block">Retrieve the name of the calendar used to define non-working days for
 this view..</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp">ChartPattern</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getNonWorkingPattern--">getNonWorkingPattern</a></span>()</code>
<div class="block">Retrieve the non-working time pattern.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp">NonWorkingTimeStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getNonWorkingStyle--">getNonWorkingStyle</a></span>()</code>
<div class="block">Retrieve the style used to draw non-working time.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getPageBreakGridLines--">getPageBreakGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesActualPlan--">getProgressLinesActualPlan</a></span>()</code>
<div class="block">Retrieve the progress lines actual plan flag.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesAtCurrentDate--">getProgressLinesAtCurrentDate</a></span>()</code>
<div class="block">Retrieve the progress lines at current date flag.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesAtRecurringIntervals--">getProgressLinesAtRecurringIntervals</a></span>()</code>
<div class="block">Retrieve the progress lines at recurring intervals flag.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesBeginAtDate--">getProgressLinesBeginAtDate</a></span>()</code>
<div class="block">Retrieve the progress lines begin at date.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesBeginAtProjectStart--">getProgressLinesBeginAtProjectStart</a></span>()</code>
<div class="block">Retrieve the progress lines begin at project start flag.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesCurrentLineColor--">getProgressLinesCurrentLineColor</a></span>()</code>
<div class="block">Retrieve the progress lines current line color.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesCurrentLineStyle--">getProgressLinesCurrentLineStyle</a></span>()</code>
<div class="block">Retrieve the progress lines current line style.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesCurrentProgressPointColor--">getProgressLinesCurrentProgressPointColor</a></span>()</code>
<div class="block">Retrieve the current progress point color.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesCurrentProgressPointShape--">getProgressLinesCurrentProgressPointShape</a></span>()</code>
<div class="block">Retrieve the current progress point shape.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesDateFormat--">getProgressLinesDateFormat</a></span>()</code>
<div class="block">Retrieve the progress line date format.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesDisplaySelected--">getProgressLinesDisplaySelected</a></span>()</code>
<div class="block">Retrieves the flag indicating if selected dates have been supplied
 for progress line display.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesDisplaySelectedDates--">getProgressLinesDisplaySelectedDates</a></span>()</code>
<div class="block">Retrieves an array of selected dates for progress line display,
 or returns null if no dates have been supplied.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesDisplayType--">getProgressLinesDisplayType</a></span>()</code>
<div class="block">Retrieves the progress lines display type.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesEnabled--">getProgressLinesEnabled</a></span>()</code>
<div class="block">Retrieves the progress lines enabled flag.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesFontStyle--">getProgressLinesFontStyle</a></span>()</code>
<div class="block">Retrieves the progress lines font style.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp">Interval</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesInterval--">getProgressLinesInterval</a></span>()</code>
<div class="block">Retrieves the progress line interval.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalDailyDayNumber--">getProgressLinesIntervalDailyDayNumber</a></span>()</code>
<div class="block">Retrieve the progress lines daily day number.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyDay--">getProgressLinesIntervalMonthlyDay</a></span>()</code>
<div class="block">Retrieves the progress lines monthly day of month.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyDayDayNumber--">getProgressLinesIntervalMonthlyDayDayNumber</a></span>()</code>
<div class="block">Retrieves the progress lines monthly day number.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyDayMonthNumber--">getProgressLinesIntervalMonthlyDayMonthNumber</a></span>()</code>
<div class="block">Retrieves the progress line month number for the monthly day type.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyFirstLast--">getProgressLinesIntervalMonthlyFirstLast</a></span>()</code>
<div class="block">Retrieves the progress lines monthly first flag.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp">ProgressLineDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyFirstLastDay--">getProgressLinesIntervalMonthlyFirstLastDay</a></span>()</code>
<div class="block">Retrieves the progress lines monthly day.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalMonthlyFirstLastMonthNumber--">getProgressLinesIntervalMonthlyFirstLastMonthNumber</a></span>()</code>
<div class="block">Retrieves the progress lines month number for the monthly first last type.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalWeekleyWeekNumber--">getProgressLinesIntervalWeekleyWeekNumber</a></span>()</code>
<div class="block">Retrieves the progress lines weekly week number.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>boolean[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesIntervalWeeklyDay--">getProgressLinesIntervalWeeklyDay</a></span>()</code>
<div class="block">Retrieves the progress lines weekly day.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesOtherLineColor--">getProgressLinesOtherLineColor</a></span>()</code>
<div class="block">Retrieves the progress lines other line color.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesOtherLineStyle--">getProgressLinesOtherLineStyle</a></span>()</code>
<div class="block">Retrieves the progress lines other line style.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesOtherProgressPointColor--">getProgressLinesOtherProgressPointColor</a></span>()</code>
<div class="block">Retrieves the progress lines other progress point color.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesOtherProgressPointShape--">getProgressLinesOtherProgressPointShape</a></span>()</code>
<div class="block">Retrieves the progress lines other progress point shape.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProgressLinesShowDate--">getProgressLinesShowDate</a></span>()</code>
<div class="block">Retrieves the progress lines show date flag.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProjectFinishGridLines--">getProjectFinishGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProjectStartGridLines--">getProjectStartGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getProjectSummaryTasksFontStyle--">getProjectSummaryTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getRoundBarsToWholeDays--">getRoundBarsToWholeDays</a></span>()</code>
<div class="block">Retrieve the round bars to whole days flag.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getRowAndColumnFontStyle--">getRowAndColumnFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getSheetColumnsGridLines--">getSheetColumnsGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getSheetRowsGridLines--">getSheetRowsGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getShowBarSplits--">getShowBarSplits</a></span>()</code>
<div class="block">Retrieve the show bar splits flag.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getShowDrawings--">getShowDrawings</a></span>()</code>
<div class="block">Retrieve the show drawings flag.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getShowInMenu--">getShowInMenu</a></span>()</code>
<div class="block">Retrieve the show in menu flag.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getStatusDateGridLines--">getStatusDateGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getSummaryTasksFontStyle--">getSummaryTasksFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTableFontStyles--">getTableFontStyles</a></span>()</code>
<div class="block">Retrieve any column font styles which the user has defined.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTableWidth--">getTableWidth</a></span>()</code>
<div class="block">Retrieve the width of the table part of the view.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleBottomTier--">getTimescaleBottomTier</a></span>()</code>
<div class="block">Retrieves a timescale tier.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleMiddleTier--">getTimescaleMiddleTier</a></span>()</code>
<div class="block">Retrieves a timescale tier.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleScaleSeparator--">getTimescaleScaleSeparator</a></span>()</code>
<div class="block">Retrieve a flag indicating if a separator is shown between the
 major and minor scales.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleShowTiers--">getTimescaleShowTiers</a></span>()</code>
<div class="block">Retrieve the number of timescale tiers to display.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleSize--">getTimescaleSize</a></span>()</code>
<div class="block">Retrieve the timescale size value.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTimescaleTopTier--">getTimescaleTopTier</a></span>()</code>
<div class="block">Retrieves a timescale tier.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTitleHorizontalGridLines--">getTitleHorizontalGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTitleVerticalGridLines--">getTitleVerticalGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTopTierColumnGridLines--">getTopTierColumnGridLines</a></span>()</code>
<div class="block">Retrieve a grid lines definition.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#getTopTimescaleFontStyle--">getTopTimescaleFontStyle</a></span>()</code>
<div class="block">Retrieve a FontStyle instance.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#isProgressLinesIntervalDailyWorkday--">isProgressLinesIntervalDailyWorkday</a></span>()</code>
<div class="block">Retrieve the progress lines daily workday flag.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#mapGanttBarHeight-int-">mapGanttBarHeight</a></span>(int&nbsp;height)</code>
<div class="block">This method maps the encoded height of a Gantt bar to
 the height in pixels.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#populateBarStyles-org.mpxj.mpp.GanttBarStyle:A-">populateBarStyles</a></span>(<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]&nbsp;barStyles)</code>
<div class="block">Set the array of Gantt Bar Styles, and populate the ID map for these styles.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processAutoFilters-byte:A-">processAutoFilters</a></span>(byte[]&nbsp;data)</code>
<div class="block">Extract autofilter definitions.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processDefaultBarStyles-org.mpxj.mpp.Props-">processDefaultBarStyles</a></span>(org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">Extract the Gantt bar styles.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processExceptionBarStyles-org.mpxj.mpp.Props-">processExceptionBarStyles</a></span>(org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">Extract the exception Gantt bar styles.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processProgressLines-java.util.Map-byte:A-">processProgressLines</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                    byte[]&nbsp;data)</code>
<div class="block">Extract progress line properties.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processTableFontStyles-java.util.Map-byte:A-">processTableFontStyles</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                      byte[]&nbsp;data)</code>
<div class="block">Extract table font styles.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#processViewProperties-java.util.Map-org.mpxj.mpp.Props-">processViewProperties</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                     org.mpxj.mpp.Props&nbsp;props)</code>
<div class="block">Extract view properties.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttChartView.html#toString--">toString</a></span>()</code>
<div class="block">Generate a string representation of this instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.mpp.GenericView">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GenericView.html" title="class in org.mpxj.mpp">GenericView</a></h3>
<code><a href="../../../org/mpxj/mpp/GenericView.html#getPropertiesID--">getPropertiesID</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.mpp.AbstractView">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.mpp.<a href="../../../org/mpxj/mpp/AbstractView.html" title="class in org.mpxj.mpp">AbstractView</a></h3>
<code><a href="../../../org/mpxj/mpp/AbstractView.html#getID--">getID</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#getName--">getName</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#getTable--">getTable</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#getTableName--">getTableName</a>, <a href="../../../org/mpxj/mpp/AbstractView.html#getType--">getType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="m_sheetRowsGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_sheetRowsGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_sheetRowsGridLines</pre>
</li>
</ul>
<a name="m_sheetColumnsGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_sheetColumnsGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_sheetColumnsGridLines</pre>
</li>
</ul>
<a name="m_titleVerticalGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_titleVerticalGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_titleVerticalGridLines</pre>
</li>
</ul>
<a name="m_titleHorizontalGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_titleHorizontalGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_titleHorizontalGridLines</pre>
</li>
</ul>
<a name="m_middleTierColumnGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_middleTierColumnGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_middleTierColumnGridLines</pre>
</li>
</ul>
<a name="m_bottomTierColumnGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_bottomTierColumnGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_bottomTierColumnGridLines</pre>
</li>
</ul>
<a name="m_ganttRowsGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_ganttRowsGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_ganttRowsGridLines</pre>
</li>
</ul>
<a name="m_barRowsGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barRowsGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_barRowsGridLines</pre>
</li>
</ul>
<a name="m_currentDateGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_currentDateGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_currentDateGridLines</pre>
</li>
</ul>
<a name="m_pageBreakGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_pageBreakGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_pageBreakGridLines</pre>
</li>
</ul>
<a name="m_projectStartGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_projectStartGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_projectStartGridLines</pre>
</li>
</ul>
<a name="m_projectFinishGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_projectFinishGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_projectFinishGridLines</pre>
</li>
</ul>
<a name="m_statusDateGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_statusDateGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_statusDateGridLines</pre>
</li>
</ul>
<a name="m_topTierColumnGridLines">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_topTierColumnGridLines</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a> m_topTierColumnGridLines</pre>
</li>
</ul>
<a name="m_ganttBarHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_ganttBarHeight</h4>
<pre>protected&nbsp;int m_ganttBarHeight</pre>
</li>
</ul>
<a name="m_timescaleTopTier">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleTopTier</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a> m_timescaleTopTier</pre>
</li>
</ul>
<a name="m_timescaleMiddleTier">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleMiddleTier</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a> m_timescaleMiddleTier</pre>
</li>
</ul>
<a name="m_timescaleBottomTier">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleBottomTier</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a> m_timescaleBottomTier</pre>
</li>
</ul>
<a name="m_timescaleScaleSeparator">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleScaleSeparator</h4>
<pre>protected&nbsp;boolean m_timescaleScaleSeparator</pre>
</li>
</ul>
<a name="m_timescaleSize">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleSize</h4>
<pre>protected&nbsp;int m_timescaleSize</pre>
</li>
</ul>
<a name="m_timescaleShowTiers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_timescaleShowTiers</h4>
<pre>protected&nbsp;int m_timescaleShowTiers</pre>
</li>
</ul>
<a name="m_nonWorkingDaysCalendarName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_nonWorkingDaysCalendarName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> m_nonWorkingDaysCalendarName</pre>
</li>
</ul>
<a name="m_nonWorkingColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_nonWorkingColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a> m_nonWorkingColor</pre>
</li>
</ul>
<a name="m_nonWorkingPattern">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_nonWorkingPattern</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp">ChartPattern</a> m_nonWorkingPattern</pre>
</li>
</ul>
<a name="m_nonWorkingStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_nonWorkingStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp">NonWorkingTimeStyle</a> m_nonWorkingStyle</pre>
</li>
</ul>
<a name="m_showDrawings">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_showDrawings</h4>
<pre>protected&nbsp;boolean m_showDrawings</pre>
</li>
</ul>
<a name="m_roundBarsToWholeDays">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_roundBarsToWholeDays</h4>
<pre>protected&nbsp;boolean m_roundBarsToWholeDays</pre>
</li>
</ul>
<a name="m_showBarSplits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_showBarSplits</h4>
<pre>protected&nbsp;boolean m_showBarSplits</pre>
</li>
</ul>
<a name="m_alwaysRollupGanttBars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_alwaysRollupGanttBars</h4>
<pre>protected&nbsp;boolean m_alwaysRollupGanttBars</pre>
</li>
</ul>
<a name="m_hideRollupBarsWhenSummaryExpanded">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_hideRollupBarsWhenSummaryExpanded</h4>
<pre>protected&nbsp;boolean m_hideRollupBarsWhenSummaryExpanded</pre>
</li>
</ul>
<a name="m_barDateFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barDateFormat</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> m_barDateFormat</pre>
</li>
</ul>
<a name="m_linkStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_linkStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp">LinkStyle</a> m_linkStyle</pre>
</li>
</ul>
<a name="m_barStyles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barStyles</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[] m_barStyles</pre>
</li>
</ul>
<a name="m_barStylesMap">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barStylesMap</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>&gt;&gt; m_barStylesMap</pre>
</li>
</ul>
<a name="m_barStyleExceptions">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barStyleExceptions</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[] m_barStyleExceptions</pre>
</li>
</ul>
<a name="m_highlightedTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_highlightedTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_highlightedTasksFontStyle</pre>
</li>
</ul>
<a name="m_rowAndColumnFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_rowAndColumnFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_rowAndColumnFontStyle</pre>
</li>
</ul>
<a name="m_nonCriticalTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_nonCriticalTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_nonCriticalTasksFontStyle</pre>
</li>
</ul>
<a name="m_criticalTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_criticalTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_criticalTasksFontStyle</pre>
</li>
</ul>
<a name="m_summaryTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_summaryTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_summaryTasksFontStyle</pre>
</li>
</ul>
<a name="m_milestoneTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_milestoneTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_milestoneTasksFontStyle</pre>
</li>
</ul>
<a name="m_topTimescaleFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_topTimescaleFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_topTimescaleFontStyle</pre>
</li>
</ul>
<a name="m_middleTimescaleFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_middleTimescaleFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_middleTimescaleFontStyle</pre>
</li>
</ul>
<a name="m_bottomTimescaleFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_bottomTimescaleFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_bottomTimescaleFontStyle</pre>
</li>
</ul>
<a name="m_barTextLeftFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barTextLeftFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_barTextLeftFontStyle</pre>
</li>
</ul>
<a name="m_barTextRightFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barTextRightFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_barTextRightFontStyle</pre>
</li>
</ul>
<a name="m_barTextTopFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barTextTopFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_barTextTopFontStyle</pre>
</li>
</ul>
<a name="m_barTextBottomFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barTextBottomFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_barTextBottomFontStyle</pre>
</li>
</ul>
<a name="m_barTextInsideFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_barTextInsideFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_barTextInsideFontStyle</pre>
</li>
</ul>
<a name="m_markedTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_markedTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_markedTasksFontStyle</pre>
</li>
</ul>
<a name="m_projectSummaryTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_projectSummaryTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_projectSummaryTasksFontStyle</pre>
</li>
</ul>
<a name="m_externalTasksFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_externalTasksFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_externalTasksFontStyle</pre>
</li>
</ul>
<a name="m_tableFontStyles">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_tableFontStyles</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a>[] m_tableFontStyles</pre>
</li>
</ul>
<a name="m_progressLinesEnabled">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesEnabled</h4>
<pre>protected&nbsp;boolean m_progressLinesEnabled</pre>
</li>
</ul>
<a name="m_progressLinesAtCurrentDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesAtCurrentDate</h4>
<pre>protected&nbsp;boolean m_progressLinesAtCurrentDate</pre>
</li>
</ul>
<a name="m_progressLinesAtRecurringIntervals">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesAtRecurringIntervals</h4>
<pre>protected&nbsp;boolean m_progressLinesAtRecurringIntervals</pre>
</li>
</ul>
<a name="m_progressLinesInterval">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesInterval</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp">Interval</a> m_progressLinesInterval</pre>
</li>
</ul>
<a name="m_progressLinesIntervalDailyDayNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalDailyDayNumber</h4>
<pre>protected&nbsp;int m_progressLinesIntervalDailyDayNumber</pre>
</li>
</ul>
<a name="m_progressLinesIntervalDailyWorkday">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalDailyWorkday</h4>
<pre>protected&nbsp;boolean m_progressLinesIntervalDailyWorkday</pre>
</li>
</ul>
<a name="m_progressLinesIntervalWeeklyDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalWeeklyDay</h4>
<pre>protected final&nbsp;boolean[] m_progressLinesIntervalWeeklyDay</pre>
</li>
</ul>
<a name="m_progressLinesIntervalWeekleyWeekNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalWeekleyWeekNumber</h4>
<pre>protected&nbsp;int m_progressLinesIntervalWeekleyWeekNumber</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyDay</h4>
<pre>protected&nbsp;boolean m_progressLinesIntervalMonthlyDay</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyDayDayNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyDayDayNumber</h4>
<pre>protected&nbsp;int m_progressLinesIntervalMonthlyDayDayNumber</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyDayMonthNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyDayMonthNumber</h4>
<pre>protected&nbsp;int m_progressLinesIntervalMonthlyDayMonthNumber</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyFirstLastDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyFirstLastDay</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp">ProgressLineDay</a> m_progressLinesIntervalMonthlyFirstLastDay</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyFirstLast">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyFirstLast</h4>
<pre>protected&nbsp;boolean m_progressLinesIntervalMonthlyFirstLast</pre>
</li>
</ul>
<a name="m_progressLinesIntervalMonthlyFirstLastMonthNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesIntervalMonthlyFirstLastMonthNumber</h4>
<pre>protected&nbsp;int m_progressLinesIntervalMonthlyFirstLastMonthNumber</pre>
</li>
</ul>
<a name="m_progressLinesBeginAtProjectStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesBeginAtProjectStart</h4>
<pre>protected&nbsp;boolean m_progressLinesBeginAtProjectStart</pre>
</li>
</ul>
<a name="m_progressLinesBeginAtDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesBeginAtDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> m_progressLinesBeginAtDate</pre>
</li>
</ul>
<a name="m_progressLinesDisplaySelected">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesDisplaySelected</h4>
<pre>protected&nbsp;boolean m_progressLinesDisplaySelected</pre>
</li>
</ul>
<a name="m_progressLinesDisplaySelectedDates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesDisplaySelectedDates</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>[] m_progressLinesDisplaySelectedDates</pre>
</li>
</ul>
<a name="m_progressLinesActualPlan">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesActualPlan</h4>
<pre>protected&nbsp;boolean m_progressLinesActualPlan</pre>
</li>
</ul>
<a name="m_progressLinesDisplayType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesDisplayType</h4>
<pre>protected&nbsp;int m_progressLinesDisplayType</pre>
</li>
</ul>
<a name="m_progressLinesShowDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesShowDate</h4>
<pre>protected&nbsp;boolean m_progressLinesShowDate</pre>
</li>
</ul>
<a name="m_progressLinesDateFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesDateFormat</h4>
<pre>protected&nbsp;int m_progressLinesDateFormat</pre>
</li>
</ul>
<a name="m_progressLinesFontStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a> m_progressLinesFontStyle</pre>
</li>
</ul>
<a name="m_progressLinesCurrentLineColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesCurrentLineColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a> m_progressLinesCurrentLineColor</pre>
</li>
</ul>
<a name="m_progressLinesCurrentLineStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesCurrentLineStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a> m_progressLinesCurrentLineStyle</pre>
</li>
</ul>
<a name="m_progressLinesCurrentProgressPointColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesCurrentProgressPointColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a> m_progressLinesCurrentProgressPointColor</pre>
</li>
</ul>
<a name="m_progressLinesCurrentProgressPointShape">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesCurrentProgressPointShape</h4>
<pre>protected&nbsp;int m_progressLinesCurrentProgressPointShape</pre>
</li>
</ul>
<a name="m_progressLinesOtherLineColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesOtherLineColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a> m_progressLinesOtherLineColor</pre>
</li>
</ul>
<a name="m_progressLinesOtherLineStyle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesOtherLineStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a> m_progressLinesOtherLineStyle</pre>
</li>
</ul>
<a name="m_progressLinesOtherProgressPointColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesOtherProgressPointColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a> m_progressLinesOtherProgressPointColor</pre>
</li>
</ul>
<a name="m_progressLinesOtherProgressPointShape">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_progressLinesOtherProgressPointShape</h4>
<pre>protected&nbsp;int m_progressLinesOtherProgressPointShape</pre>
</li>
</ul>
<a name="m_autoFilters">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_autoFilters</h4>
<pre>protected final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt; m_autoFilters</pre>
</li>
</ul>
<a name="m_autoFiltersByType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>m_autoFiltersByType</h4>
<pre>protected final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>,<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt; m_autoFiltersByType</pre>
</li>
</ul>
<a name="VIEW_PROPERTIES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIEW_PROPERTIES</h4>
<pre>protected static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> VIEW_PROPERTIES</pre>
</li>
</ul>
<a name="TIMESCALE_PROPERTIES">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TIMESCALE_PROPERTIES</h4>
<pre>protected static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> TIMESCALE_PROPERTIES</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="processDefaultBarStyles-org.mpxj.mpp.Props-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processDefaultBarStyles</h4>
<pre>protected abstract&nbsp;void&nbsp;processDefaultBarStyles(org.mpxj.mpp.Props&nbsp;props)</pre>
<div class="block">Extract the Gantt bar styles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>props</code> - props structure containing the style definitions</dd>
</dl>
</li>
</ul>
<a name="processExceptionBarStyles-org.mpxj.mpp.Props-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processExceptionBarStyles</h4>
<pre>protected abstract&nbsp;void&nbsp;processExceptionBarStyles(org.mpxj.mpp.Props&nbsp;props)</pre>
<div class="block">Extract the exception Gantt bar styles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>props</code> - props structure containing the style definitions</dd>
</dl>
</li>
</ul>
<a name="processAutoFilters-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processAutoFilters</h4>
<pre>protected abstract&nbsp;void&nbsp;processAutoFilters(byte[]&nbsp;data)</pre>
<div class="block">Extract autofilter definitions.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - autofilters data block</dd>
</dl>
</li>
</ul>
<a name="processViewProperties-java.util.Map-org.mpxj.mpp.Props-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processViewProperties</h4>
<pre>protected abstract&nbsp;void&nbsp;processViewProperties(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                                              org.mpxj.mpp.Props&nbsp;props)</pre>
<div class="block">Extract view properties.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fontBases</code> - font defintions</dd>
<dd><code>props</code> - Gantt chart view props</dd>
</dl>
</li>
</ul>
<a name="processTableFontStyles-java.util.Map-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processTableFontStyles</h4>
<pre>protected abstract&nbsp;void&nbsp;processTableFontStyles(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                                               byte[]&nbsp;data)</pre>
<div class="block">Extract table font styles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fontBases</code> - font bases</dd>
<dd><code>data</code> - column data</dd>
</dl>
</li>
</ul>
<a name="processProgressLines-java.util.Map-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>processProgressLines</h4>
<pre>protected abstract&nbsp;void&nbsp;processProgressLines(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases,
                                             byte[]&nbsp;data)</pre>
<div class="block">Extract progress line properties.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fontBases</code> - font bases</dd>
<dd><code>data</code> - column data</dd>
</dl>
</li>
</ul>
<a name="getSheetColumnsGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSheetColumnsGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getSheetColumnsGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getSheetRowsGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSheetRowsGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getSheetRowsGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getStatusDateGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusDateGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getStatusDateGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getTitleHorizontalGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitleHorizontalGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getTitleHorizontalGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getTitleVerticalGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitleVerticalGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getTitleVerticalGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getBarRowsGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarRowsGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getBarRowsGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getCurrentDateGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentDateGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getCurrentDateGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getGanttRowsGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttRowsGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getGanttRowsGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getTopTierColumnGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopTierColumnGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getTopTierColumnGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getMiddleTierColumnGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMiddleTierColumnGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getMiddleTierColumnGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getBottomTierColumnGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBottomTierColumnGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getBottomTierColumnGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getNonWorkingDaysCalendarName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNonWorkingDaysCalendarName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNonWorkingDaysCalendarName()</pre>
<div class="block">Retrieve the name of the calendar used to define non-working days for
 this view..</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calendar name</dd>
</dl>
</li>
</ul>
<a name="getPageBreakGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPageBreakGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getPageBreakGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getProjectFinishGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectFinishGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getProjectFinishGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getProjectStartGridLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectStartGridLines</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp">GridLines</a>&nbsp;getProjectStartGridLines()</pre>
<div class="block">Retrieve a grid lines definition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>grid lines definition</dd>
</dl>
</li>
</ul>
<a name="getGanttBarHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttBarHeight</h4>
<pre>public&nbsp;int&nbsp;getGanttBarHeight()</pre>
<div class="block">Retrieve the height of the Gantt bars in this view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Gantt bar height</dd>
</dl>
</li>
</ul>
<a name="getTimescaleScaleSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleScaleSeparator</h4>
<pre>public&nbsp;boolean&nbsp;getTimescaleScaleSeparator()</pre>
<div class="block">Retrieve a flag indicating if a separator is shown between the
 major and minor scales.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getTimescaleTopTier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleTopTier</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a>&nbsp;getTimescaleTopTier()</pre>
<div class="block">Retrieves a timescale tier.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timescale tier</dd>
</dl>
</li>
</ul>
<a name="getTimescaleMiddleTier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleMiddleTier</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a>&nbsp;getTimescaleMiddleTier()</pre>
<div class="block">Retrieves a timescale tier.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timescale tier</dd>
</dl>
</li>
</ul>
<a name="getTimescaleBottomTier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleBottomTier</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp">TimescaleTier</a>&nbsp;getTimescaleBottomTier()</pre>
<div class="block">Retrieves a timescale tier.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timescale tier</dd>
</dl>
</li>
</ul>
<a name="getTimescaleSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleSize</h4>
<pre>public&nbsp;int&nbsp;getTimescaleSize()</pre>
<div class="block">Retrieve the timescale size value. This is a percentage value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>timescale size value</dd>
</dl>
</li>
</ul>
<a name="getTimescaleShowTiers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleShowTiers</h4>
<pre>public&nbsp;int&nbsp;getTimescaleShowTiers()</pre>
<div class="block">Retrieve the number of timescale tiers to display.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number of timescale tiers to show</dd>
</dl>
</li>
</ul>
<a name="getNonWorkingColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNonWorkingColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;getNonWorkingColor()</pre>
<div class="block">Retrieve the non-working time color.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>non-working time color</dd>
</dl>
</li>
</ul>
<a name="getNonWorkingPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNonWorkingPattern</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp">ChartPattern</a>&nbsp;getNonWorkingPattern()</pre>
<div class="block">Retrieve the non-working time pattern. This is an integer between
 0 and 10 inclusive which represents the fixed set of patterns
 supported by MS Project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>non-working time pattern</dd>
</dl>
</li>
</ul>
<a name="getNonWorkingStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNonWorkingStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp">NonWorkingTimeStyle</a>&nbsp;getNonWorkingStyle()</pre>
<div class="block">Retrieve the style used to draw non-working time.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>non working time style</dd>
</dl>
</li>
</ul>
<a name="getAlwaysRollupGanttBars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlwaysRollupGanttBars</h4>
<pre>public&nbsp;boolean&nbsp;getAlwaysRollupGanttBars()</pre>
<div class="block">Retrieve the always rollup Gantt bars flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>always rollup Gantt bars flag</dd>
</dl>
</li>
</ul>
<a name="getBarDateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarDateFormat</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&nbsp;getBarDateFormat()</pre>
<div class="block">Retrieve the bar date format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bar date format</dd>
</dl>
</li>
</ul>
<a name="getHideRollupBarsWhenSummaryExpanded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideRollupBarsWhenSummaryExpanded</h4>
<pre>public&nbsp;boolean&nbsp;getHideRollupBarsWhenSummaryExpanded()</pre>
<div class="block">Retrieve the hide rollup bars when summary expanded.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hide rollup bars when summary expanded</dd>
</dl>
</li>
</ul>
<a name="getLinkStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp">LinkStyle</a>&nbsp;getLinkStyle()</pre>
<div class="block">Retrieve the bar link style.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bar link style</dd>
</dl>
</li>
</ul>
<a name="getRoundBarsToWholeDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoundBarsToWholeDays</h4>
<pre>public&nbsp;boolean&nbsp;getRoundBarsToWholeDays()</pre>
<div class="block">Retrieve the round bars to whole days flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>round bars to whole days flag</dd>
</dl>
</li>
</ul>
<a name="getShowBarSplits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowBarSplits</h4>
<pre>public&nbsp;boolean&nbsp;getShowBarSplits()</pre>
<div class="block">Retrieve the show bar splits flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>show bar splits flag</dd>
</dl>
</li>
</ul>
<a name="getShowDrawings--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowDrawings</h4>
<pre>public&nbsp;boolean&nbsp;getShowDrawings()</pre>
<div class="block">Retrieve the show drawings flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>show drawings flag</dd>
</dl>
</li>
</ul>
<a name="getBarStyleExceptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarStyleExceptions</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp">GanttBarStyleException</a>[]&nbsp;getBarStyleExceptions()</pre>
<div class="block">Retrieve an array representing bar styles which have been defined
 by the user for a specific task.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>array of bar style exceptions</dd>
</dl>
</li>
</ul>
<a name="getGanttBarStyleByID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttBarStyleByID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>&gt;&nbsp;getGanttBarStyleByID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Retrieve a list of Gantt Bar Styles for this view which match the supplied  ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - Gantt Bar Style ID</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>GanttBarStyle or null</dd>
</dl>
</li>
</ul>
<a name="getBarStyles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarStyles</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]&nbsp;getBarStyles()</pre>
<div class="block">Retrieve an array of bar styles which are applied to all Gantt
 chart bars, unless an exception has been defined.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>array of bar styles</dd>
</dl>
</li>
</ul>
<a name="getTableWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableWidth</h4>
<pre>public&nbsp;int&nbsp;getTableWidth()</pre>
<div class="block">Retrieve the width of the table part of the view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>table width</dd>
</dl>
</li>
</ul>
<a name="getDefaultFilterName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFilterName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDefaultFilterName()</pre>
<div class="block">Retrieve the name of the filter applied to this view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>filter name</dd>
</dl>
</li>
</ul>
<a name="getDefaultFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFilter</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&nbsp;getDefaultFilter()</pre>
<div class="block">Convenience method used to retrieve the default filter instance
 associated with this view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>filter instance, null if no filter associated with view</dd>
</dl>
</li>
</ul>
<a name="getGroupName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroupName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getGroupName()</pre>
<div class="block">Retrieve the name of the grouping applied to this view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>group name</dd>
</dl>
</li>
</ul>
<a name="getHighlightFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHighlightFilter</h4>
<pre>public&nbsp;boolean&nbsp;getHighlightFilter()</pre>
<div class="block">Retrieve the highlight filter flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>highlight filter flag</dd>
</dl>
</li>
</ul>
<a name="getShowInMenu--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowInMenu</h4>
<pre>public&nbsp;boolean&nbsp;getShowInMenu()</pre>
<div class="block">Retrieve the show in menu flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>show in menu flag</dd>
</dl>
</li>
</ul>
<a name="getBarTextBottomFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextBottomFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBarTextBottomFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getBarTextInsideFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextInsideFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBarTextInsideFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getBarTextLeftFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextLeftFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBarTextLeftFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getBarTextRightFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextRightFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBarTextRightFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getBarTextTopFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextTopFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBarTextTopFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getCriticalTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCriticalTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getCriticalTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getExternalTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExternalTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getExternalTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getHighlightedTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHighlightedTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getHighlightedTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getTopTimescaleFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopTimescaleFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getTopTimescaleFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getMiddleTimescaleFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMiddleTimescaleFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getMiddleTimescaleFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getMarkedTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarkedTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getMarkedTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getMilestoneTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMilestoneTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getMilestoneTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getBottomTimescaleFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBottomTimescaleFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getBottomTimescaleFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getNonCriticalTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNonCriticalTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getNonCriticalTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getProjectSummaryTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectSummaryTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getProjectSummaryTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getRowAndColumnFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRowAndColumnFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getRowAndColumnFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getSummaryTasksFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummaryTasksFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getSummaryTasksFontStyle()</pre>
<div class="block">Retrieve a FontStyle instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getTableFontStyles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTableFontStyles</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a>[]&nbsp;getTableFontStyles()</pre>
<div class="block">Retrieve any column font styles which the user has defined.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>column font styles array</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesActualPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesActualPlan</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesActualPlan()</pre>
<div class="block">Retrieve the progress lines actual plan flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesAtCurrentDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesAtCurrentDate</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesAtCurrentDate()</pre>
<div class="block">Retrieve the progress lines at current date flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesAtRecurringIntervals--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesAtRecurringIntervals</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesAtRecurringIntervals()</pre>
<div class="block">Retrieve the progress lines at recurring intervals flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesBeginAtDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesBeginAtDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getProgressLinesBeginAtDate()</pre>
<div class="block">Retrieve the progress lines begin at date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines begin at date</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesBeginAtProjectStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesBeginAtProjectStart</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesBeginAtProjectStart()</pre>
<div class="block">Retrieve the progress lines begin at project start flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesCurrentLineColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesCurrentLineColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;getProgressLinesCurrentLineColor()</pre>
<div class="block">Retrieve the progress lines current line color.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current line color</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesCurrentLineStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesCurrentLineStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a>&nbsp;getProgressLinesCurrentLineStyle()</pre>
<div class="block">Retrieve the progress lines current line style.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current line style</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesCurrentProgressPointColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesCurrentProgressPointColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;getProgressLinesCurrentProgressPointColor()</pre>
<div class="block">Retrieve the current progress point color.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current progress point color</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesCurrentProgressPointShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesCurrentProgressPointShape</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesCurrentProgressPointShape()</pre>
<div class="block">Retrieve the current progress point shape.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current progress point shape</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalDailyDayNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalDailyDayNumber</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesIntervalDailyDayNumber()</pre>
<div class="block">Retrieve the progress lines daily day number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines daily day number</dd>
</dl>
</li>
</ul>
<a name="isProgressLinesIntervalDailyWorkday--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProgressLinesIntervalDailyWorkday</h4>
<pre>public&nbsp;boolean&nbsp;isProgressLinesIntervalDailyWorkday()</pre>
<div class="block">Retrieve the progress lines daily workday flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>daily workday flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesDateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesDateFormat</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesDateFormat()</pre>
<div class="block">Retrieve the progress line date format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress line date format.</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesDisplaySelected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesDisplaySelected</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesDisplaySelected()</pre>
<div class="block">Retrieves the flag indicating if selected dates have been supplied
 for progress line display.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesDisplaySelectedDates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesDisplaySelectedDates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>[]&nbsp;getProgressLinesDisplaySelectedDates()</pre>
<div class="block">Retrieves an array of selected dates for progress line display,
 or returns null if no dates have been supplied.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>array of selected dates</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesDisplayType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesDisplayType</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesDisplayType()</pre>
<div class="block">Retrieves the progress lines display type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines display type</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesEnabled()</pre>
<div class="block">Retrieves the progress lines enabled flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesFontStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesFontStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getProgressLinesFontStyle()</pre>
<div class="block">Retrieves the progress lines font style.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines font style</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesInterval--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesInterval</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp">Interval</a>&nbsp;getProgressLinesInterval()</pre>
<div class="block">Retrieves the progress line interval.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress line interval</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyFirstLastDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyFirstLastDay</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp">ProgressLineDay</a>&nbsp;getProgressLinesIntervalMonthlyFirstLastDay()</pre>
<div class="block">Retrieves the progress lines monthly day.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines monthly day</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyFirstLastMonthNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyFirstLastMonthNumber</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesIntervalMonthlyFirstLastMonthNumber()</pre>
<div class="block">Retrieves the progress lines month number for the monthly first last type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>month number</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyDayDayNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyDayDayNumber</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesIntervalMonthlyDayDayNumber()</pre>
<div class="block">Retrieves the progress lines monthly day number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines monthly day number</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyDay</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesIntervalMonthlyDay()</pre>
<div class="block">Retrieves the progress lines monthly day of month.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines monthly day of month</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyDayMonthNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyDayMonthNumber</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesIntervalMonthlyDayMonthNumber()</pre>
<div class="block">Retrieves the progress line month number for the monthly day type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>month number</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalMonthlyFirstLast--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalMonthlyFirstLast</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesIntervalMonthlyFirstLast()</pre>
<div class="block">Retrieves the progress lines monthly first flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines monthly first flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesOtherLineColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesOtherLineColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;getProgressLinesOtherLineColor()</pre>
<div class="block">Retrieves the progress lines other line color.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines other line color</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesOtherLineStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesOtherLineStyle</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp">LineStyle</a>&nbsp;getProgressLinesOtherLineStyle()</pre>
<div class="block">Retrieves the progress lines other line style.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines other line style</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesOtherProgressPointColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesOtherProgressPointColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;getProgressLinesOtherProgressPointColor()</pre>
<div class="block">Retrieves the progress lines other progress point color.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines other progress point color</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesOtherProgressPointShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesOtherProgressPointShape</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesOtherProgressPointShape()</pre>
<div class="block">Retrieves the progress lines other progress point shape.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines other progress point shape</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesShowDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesShowDate</h4>
<pre>public&nbsp;boolean&nbsp;getProgressLinesShowDate()</pre>
<div class="block">Retrieves the progress lines show date flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines show date flag</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalWeekleyWeekNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalWeekleyWeekNumber</h4>
<pre>public&nbsp;int&nbsp;getProgressLinesIntervalWeekleyWeekNumber()</pre>
<div class="block">Retrieves the progress lines weekly week number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines weekly week number</dd>
</dl>
</li>
</ul>
<a name="getProgressLinesIntervalWeeklyDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressLinesIntervalWeeklyDay</h4>
<pre>public&nbsp;boolean[]&nbsp;getProgressLinesIntervalWeeklyDay()</pre>
<div class="block">Retrieves the progress lines weekly day.
 Note that this is designed to be used with the constants defined
 by the Day class, for example use Day.MONDAY.getValue() as the
 index into this array for the Monday flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>progress lines weekly day</dd>
</dl>
</li>
</ul>
<a name="mapGanttBarHeight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mapGanttBarHeight</h4>
<pre>protected&nbsp;int&nbsp;mapGanttBarHeight(int&nbsp;height)</pre>
<div class="block">This method maps the encoded height of a Gantt bar to
 the height in pixels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>height</code> - encoded height</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>height in pixels</dd>
</dl>
</li>
</ul>
<a name="getFontStyle-byte:A-int-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp">FontStyle</a>&nbsp;getFontStyle(byte[]&nbsp;data,
                                 int&nbsp;offset,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</pre>
<div class="block">Retrieve font details from a block of property data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - property data</dd>
<dd><code>offset</code> - offset into property data</dd>
<dd><code>fontBases</code> - map of font bases</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>FontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getColumnFontStyle-org.mpxj.ProjectFile-byte:A-int-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColumnFontStyle</h4>
<pre>protected&nbsp;<a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp">TableFontStyle</a>&nbsp;getColumnFontStyle(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
                                            byte[]&nbsp;data,
                                            int&nbsp;offset,
                                            <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp">FontBase</a>&gt;&nbsp;fontBases)</pre>
<div class="block">Retrieve column font details from a block of property data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - parent file</dd>
<dd><code>data</code> - property data</dd>
<dd><code>offset</code> - offset into property data</dd>
<dd><code>fontBases</code> - map of font bases</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ColumnFontStyle instance</dd>
</dl>
</li>
</ul>
<a name="getAutoFilters--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoFilters</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&gt;&nbsp;getAutoFilters()</pre>
<div class="block">Retrieves a list of all auto filters associated with this view.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of filter instances</dd>
</dl>
</li>
</ul>
<a name="getAutoFilterByType-org.mpxj.FieldType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoFilterByType</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/Filter.html" title="class in org.mpxj">Filter</a>&nbsp;getAutoFilterByType(<a href="../../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</pre>
<div class="block">Retrieves the auto filter definition associated with an
 individual column. Returns null if there is no filter defined for
 the supplied column type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - field type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>filter instance</dd>
</dl>
</li>
</ul>
<a name="populateBarStyles-org.mpxj.mpp.GanttBarStyle:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>populateBarStyles</h4>
<pre>protected&nbsp;void&nbsp;populateBarStyles(<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp">GanttBarStyle</a>[]&nbsp;barStyles)</pre>
<div class="block">Set the array of Gantt Bar Styles, and populate the ID map for these styles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>barStyles</code> - Gantt Bar Styles array</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Generate a string representation of this instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/mpxj/mpp/AbstractView.html#toString--">toString</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/mpxj/mpp/AbstractView.html" title="class in org.mpxj.mpp">AbstractView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representation of this instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttChartView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarStyleFactoryCommon.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttChartView12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttChartView.html" target="_top">Frames</a></li>
<li><a href="GanttChartView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
