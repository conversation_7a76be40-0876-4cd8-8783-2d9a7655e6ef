<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GanttBarShowForTasks (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GanttBarShowForTasks (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarShowForTasks.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarStartEndShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarShowForTasks.html" target="_top">Frames</a></li>
<li><a href="GanttBarShowForTasks.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mpp</div>
<h2 title="Enum GanttBarShowForTasks" class="title">Enum GanttBarShowForTasks</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.mpp.GanttBarShowForTasks</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&gt;, <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">GanttBarShowForTasks</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&gt;
implements <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></pre>
<div class="block">Represents the criteria used to define when a Gantt bar is displayed.
 Note that the value attribute has been chosen to allow the normal and
 negated types to be determined. value &amp; 0x64 will be zero for normal types,
 and non-zero for negative types. value &amp; 0x63 will convert a negative type
 to a normal type, the type can then be retrieved using the getInstance
 method.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#ACTIVE">ACTIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#CRITICAL">CRITICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#DELIVERABLE">DELIVERABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#DEPENDENCY">DEPENDENCY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#EXTERNAL">EXTERNAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FINISHED">FINISHED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FINISHEDEARLY">FINISHEDEARLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FINISHEDLATE">FINISHEDLATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FINISHEDONTIME">FINISHEDONTIME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG1">FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG10">FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG11">FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG12">FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG13">FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG14">FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG15">FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG16">FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG17">FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG18">FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG19">FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG2">FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG20">FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG3">FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG4">FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG5">FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG6">FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG7">FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG8">FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#FLAG9">FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#GROUPBYSUMMARY">GROUPBYSUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#INPROGRESS">INPROGRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#LATE">LATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#MANUALLYSCHEDULED">MANUALLYSCHEDULED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#MARKED">MARKED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#MILESTONE">MILESTONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NONCRITICAL">NONCRITICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NORMAL">NORMAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_ACTIVE">NOT_ACTIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_CRITICAL">NOT_CRITICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_DELIVERABLE">NOT_DELIVERABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_DEPENDENCY">NOT_DEPENDENCY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_EXTERNAL">NOT_EXTERNAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FINISHEDEARLY">NOT_FINISHEDEARLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FINISHEDLATE">NOT_FINISHEDLATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FINISHEDONTIME">NOT_FINISHEDONTIME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG1">NOT_FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG10">NOT_FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG11">NOT_FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG12">NOT_FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG13">NOT_FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG14">NOT_FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG15">NOT_FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG16">NOT_FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG17">NOT_FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG18">NOT_FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG19">NOT_FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG2">NOT_FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG20">NOT_FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG3">NOT_FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG4">NOT_FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG5">NOT_FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG6">NOT_FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG7">NOT_FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG8">NOT_FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_FLAG9">NOT_FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_GROUPBYSUMMARY">NOT_GROUPBYSUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_INPROGRESS">NOT_INPROGRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_LATE">NOT_LATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_MANUALLYSCHEDULED">NOT_MANUALLYSCHEDULED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_MARKED">NOT_MARKED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_MILESTONE">NOT_MILESTONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_NORMAL">NOT_NORMAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_PLACEHOLDER">NOT_PLACEHOLDER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_PLACEHOLDERDURATION">NOT_PLACEHOLDERDURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_PLACEHOLDERFINISH">NOT_PLACEHOLDERFINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_PLACEHOLDERSTART">NOT_PLACEHOLDERSTART</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_PROJECTSUMMARY">NOT_PROJECTSUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_ROLLEDUP">NOT_ROLLEDUP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_SPLIT">NOT_SPLIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_STARTEDEARLY">NOT_STARTEDEARLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_STARTEDLATE">NOT_STARTEDLATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_STARTEDONTIME">NOT_STARTEDONTIME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_SUMMARY">NOT_SUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOT_WARNING">NOT_WARNING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOTFINISHED">NOTFINISHED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#NOTSTARTED">NOTSTARTED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#PLACEHOLDER">PLACEHOLDER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#PLACEHOLDERDURATION">PLACEHOLDERDURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#PLACEHOLDERFINISH">PLACEHOLDERFINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#PLACEHOLDERSTART">PLACEHOLDERSTART</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#PROJECTSUMMARY">PROJECTSUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#ROLLEDUP">ROLLEDUP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#SPLIT">SPLIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#STARTEDEARLY">STARTEDEARLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#STARTEDLATE">STARTEDLATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#STARTEDONTIME">STARTEDONTIME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#SUMMARY">SUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#WARNING">WARNING</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#getInstance-int-">getInstance</a></span>(int&nbsp;type)</code>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#getInstance-java.lang.Number-">getInstance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;type)</code>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#getName--">getName</a></span>()</code>
<div class="block">Retrieve the line style name.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#getValue--">getValue</a></span>()</code>
<div class="block">Accessor method used to retrieve the numeric representation of the enum.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#toString--">toString</a></span>()</code>
<div class="block">Retrieve the String representation of this line style.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NORMAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NORMAL</pre>
</li>
</ul>
<a name="MILESTONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILESTONE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> MILESTONE</pre>
</li>
</ul>
<a name="SUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> SUMMARY</pre>
</li>
</ul>
<a name="CRITICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CRITICAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> CRITICAL</pre>
</li>
</ul>
<a name="NONCRITICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NONCRITICAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NONCRITICAL</pre>
</li>
</ul>
<a name="MARKED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MARKED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> MARKED</pre>
</li>
</ul>
<a name="FINISHED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISHED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FINISHED</pre>
</li>
</ul>
<a name="INPROGRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INPROGRESS</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> INPROGRESS</pre>
</li>
</ul>
<a name="NOTFINISHED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOTFINISHED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOTFINISHED</pre>
</li>
</ul>
<a name="NOTSTARTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOTSTARTED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOTSTARTED</pre>
</li>
</ul>
<a name="STARTEDLATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STARTEDLATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> STARTEDLATE</pre>
</li>
</ul>
<a name="FINISHEDLATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISHEDLATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FINISHEDLATE</pre>
</li>
</ul>
<a name="STARTEDEARLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STARTEDEARLY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> STARTEDEARLY</pre>
</li>
</ul>
<a name="FINISHEDEARLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISHEDEARLY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FINISHEDEARLY</pre>
</li>
</ul>
<a name="STARTEDONTIME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STARTEDONTIME</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> STARTEDONTIME</pre>
</li>
</ul>
<a name="FINISHEDONTIME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISHEDONTIME</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FINISHEDONTIME</pre>
</li>
</ul>
<a name="FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG1</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG1</pre>
</li>
</ul>
<a name="FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG2</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG2</pre>
</li>
</ul>
<a name="FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG3</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG3</pre>
</li>
</ul>
<a name="FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG4</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG4</pre>
</li>
</ul>
<a name="FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG5</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG5</pre>
</li>
</ul>
<a name="FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG6</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG6</pre>
</li>
</ul>
<a name="FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG7</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG7</pre>
</li>
</ul>
<a name="FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG8</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG8</pre>
</li>
</ul>
<a name="FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG9</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG9</pre>
</li>
</ul>
<a name="FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG10</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG10</pre>
</li>
</ul>
<a name="ROLLEDUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROLLEDUP</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> ROLLEDUP</pre>
</li>
</ul>
<a name="PROJECTSUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROJECTSUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> PROJECTSUMMARY</pre>
</li>
</ul>
<a name="SPLIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPLIT</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> SPLIT</pre>
</li>
</ul>
<a name="EXTERNAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTERNAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> EXTERNAL</pre>
</li>
</ul>
<a name="FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG11</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG11</pre>
</li>
</ul>
<a name="FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG12</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG12</pre>
</li>
</ul>
<a name="FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG13</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG13</pre>
</li>
</ul>
<a name="FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG14</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG14</pre>
</li>
</ul>
<a name="FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG15</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG15</pre>
</li>
</ul>
<a name="FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG16</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG16</pre>
</li>
</ul>
<a name="FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG17</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG17</pre>
</li>
</ul>
<a name="FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG18</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG18</pre>
</li>
</ul>
<a name="FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG19</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG19</pre>
</li>
</ul>
<a name="FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG20</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> FLAG20</pre>
</li>
</ul>
<a name="GROUPBYSUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GROUPBYSUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> GROUPBYSUMMARY</pre>
</li>
</ul>
<a name="DELIVERABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> DELIVERABLE</pre>
</li>
</ul>
<a name="DEPENDENCY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEPENDENCY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> DEPENDENCY</pre>
</li>
</ul>
<a name="ACTIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> ACTIVE</pre>
</li>
</ul>
<a name="MANUALLYSCHEDULED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUALLYSCHEDULED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> MANUALLYSCHEDULED</pre>
</li>
</ul>
<a name="WARNING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WARNING</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> WARNING</pre>
</li>
</ul>
<a name="PLACEHOLDERSTART">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLACEHOLDERSTART</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> PLACEHOLDERSTART</pre>
</li>
</ul>
<a name="PLACEHOLDERFINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLACEHOLDERFINISH</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> PLACEHOLDERFINISH</pre>
</li>
</ul>
<a name="PLACEHOLDERDURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLACEHOLDERDURATION</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> PLACEHOLDERDURATION</pre>
</li>
</ul>
<a name="PLACEHOLDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLACEHOLDER</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> PLACEHOLDER</pre>
</li>
</ul>
<a name="LATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> LATE</pre>
</li>
</ul>
<a name="NOT_NORMAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_NORMAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_NORMAL</pre>
</li>
</ul>
<a name="NOT_MILESTONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_MILESTONE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_MILESTONE</pre>
</li>
</ul>
<a name="NOT_SUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_SUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_SUMMARY</pre>
</li>
</ul>
<a name="NOT_CRITICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_CRITICAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_CRITICAL</pre>
</li>
</ul>
<a name="NOT_MARKED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_MARKED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_MARKED</pre>
</li>
</ul>
<a name="NOT_INPROGRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_INPROGRESS</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_INPROGRESS</pre>
</li>
</ul>
<a name="NOT_STARTEDLATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_STARTEDLATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_STARTEDLATE</pre>
</li>
</ul>
<a name="NOT_FINISHEDLATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FINISHEDLATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FINISHEDLATE</pre>
</li>
</ul>
<a name="NOT_STARTEDEARLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_STARTEDEARLY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_STARTEDEARLY</pre>
</li>
</ul>
<a name="NOT_FINISHEDEARLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FINISHEDEARLY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FINISHEDEARLY</pre>
</li>
</ul>
<a name="NOT_STARTEDONTIME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_STARTEDONTIME</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_STARTEDONTIME</pre>
</li>
</ul>
<a name="NOT_FINISHEDONTIME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FINISHEDONTIME</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FINISHEDONTIME</pre>
</li>
</ul>
<a name="NOT_FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG1</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG1</pre>
</li>
</ul>
<a name="NOT_FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG2</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG2</pre>
</li>
</ul>
<a name="NOT_FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG3</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG3</pre>
</li>
</ul>
<a name="NOT_FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG4</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG4</pre>
</li>
</ul>
<a name="NOT_FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG5</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG5</pre>
</li>
</ul>
<a name="NOT_FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG6</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG6</pre>
</li>
</ul>
<a name="NOT_FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG7</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG7</pre>
</li>
</ul>
<a name="NOT_FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG8</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG8</pre>
</li>
</ul>
<a name="NOT_FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG9</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG9</pre>
</li>
</ul>
<a name="NOT_FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG10</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG10</pre>
</li>
</ul>
<a name="NOT_ROLLEDUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_ROLLEDUP</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_ROLLEDUP</pre>
</li>
</ul>
<a name="NOT_PROJECTSUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_PROJECTSUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_PROJECTSUMMARY</pre>
</li>
</ul>
<a name="NOT_SPLIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_SPLIT</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_SPLIT</pre>
</li>
</ul>
<a name="NOT_EXTERNAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_EXTERNAL</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_EXTERNAL</pre>
</li>
</ul>
<a name="NOT_FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG11</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG11</pre>
</li>
</ul>
<a name="NOT_FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG12</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG12</pre>
</li>
</ul>
<a name="NOT_FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG13</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG13</pre>
</li>
</ul>
<a name="NOT_FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG14</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG14</pre>
</li>
</ul>
<a name="NOT_FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG15</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG15</pre>
</li>
</ul>
<a name="NOT_FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG16</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG16</pre>
</li>
</ul>
<a name="NOT_FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG17</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG17</pre>
</li>
</ul>
<a name="NOT_FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG18</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG18</pre>
</li>
</ul>
<a name="NOT_FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG19</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG19</pre>
</li>
</ul>
<a name="NOT_FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_FLAG20</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_FLAG20</pre>
</li>
</ul>
<a name="NOT_GROUPBYSUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_GROUPBYSUMMARY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_GROUPBYSUMMARY</pre>
</li>
</ul>
<a name="NOT_DELIVERABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_DELIVERABLE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_DELIVERABLE</pre>
</li>
</ul>
<a name="NOT_DEPENDENCY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_DEPENDENCY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_DEPENDENCY</pre>
</li>
</ul>
<a name="NOT_ACTIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_ACTIVE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_ACTIVE</pre>
</li>
</ul>
<a name="NOT_MANUALLYSCHEDULED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_MANUALLYSCHEDULED</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_MANUALLYSCHEDULED</pre>
</li>
</ul>
<a name="NOT_WARNING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_WARNING</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_WARNING</pre>
</li>
</ul>
<a name="NOT_PLACEHOLDERSTART">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_PLACEHOLDERSTART</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_PLACEHOLDERSTART</pre>
</li>
</ul>
<a name="NOT_PLACEHOLDERFINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_PLACEHOLDERFINISH</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_PLACEHOLDERFINISH</pre>
</li>
</ul>
<a name="NOT_PLACEHOLDERDURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_PLACEHOLDERDURATION</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_PLACEHOLDERDURATION</pre>
</li>
</ul>
<a name="NOT_PLACEHOLDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOT_PLACEHOLDER</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_PLACEHOLDER</pre>
</li>
</ul>
<a name="NOT_LATE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NOT_LATE</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a> NOT_LATE</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (GanttBarShowForTasks c : GanttBarShowForTasks.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&nbsp;valueOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getInstance-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&nbsp;getInstance(int&nbsp;type)</pre>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - int type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum instance</dd>
</dl>
</li>
</ul>
<a name="getInstance-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&nbsp;getInstance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;type)</pre>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - int type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum instance</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block">Accessor method used to retrieve the numeric representation of the enum.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/MpxjEnum.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int representation of the enum</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Retrieve the line style name. Currently this is not localised.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>style name</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Retrieve the String representation of this line style.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp">GanttBarShowForTasks</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>String representation of this line style</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarShowForTasks.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarStartEndShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarShowForTasks.html" target="_top">Frames</a></li>
<li><a href="GanttBarShowForTasks.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
