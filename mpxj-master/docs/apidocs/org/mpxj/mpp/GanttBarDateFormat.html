<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GanttBarDateFormat (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GanttBarDateFormat (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":10,"i3":10,"i4":10,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarDateFormat.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarDateFormat.html" target="_top">Frames</a></li>
<li><a href="GanttBarDateFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mpp</div>
<h2 title="Enum GanttBarDateFormat" class="title">Enum GanttBarDateFormat</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.mpp.GanttBarDateFormat</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&gt;, <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">GanttBarDateFormat</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&gt;
implements <a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></pre>
<div class="block">Enumeration representing the formats which may be shown on a Gantt chart timescale.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD">DD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMM">DD_MMM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMM_HHMM">DD_MMM_HHMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMM_YY">DD_MMM_YY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMMM">DD_MMMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMMM_YYYY">DD_MMMM_YYYY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DD_MMMM_YYYY_HHMM">DD_MMMM_YYYY_HHMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DD">DDD_DD</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DD_MMM">DDD_DD_MMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DD_MMM_YY">DDD_DD_MMM_YY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DDMM">DDD_DDMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DDMMYY">DDD_DDMMYY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_DDMMYY_HHMM">DDD_DDMMYY_HHMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDD_HHMM">DDD_HHMM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDMM">DDMM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDMMYY">DDMMYY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDMMYY_MMSS">DDMMYY_MMSS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DDMMYYYY">DDMMYYYY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#DEFAULT">DEFAULT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#HHMM">HHMM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#MWW">MWW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#MWWYY_HHMM">MWWYY_HHMM</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#getInstance-int-">getInstance</a></span>(int&nbsp;type)</code>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#getInstance-java.lang.Number-">getInstance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;type)</code>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#getName--">getName</a></span>()</code>
<div class="block">Retrieve the name of this alignment.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#getValue--">getValue</a></span>()</code>
<div class="block">Accessor method used to retrieve the numeric representation of the enum.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#toString--">toString</a></span>()</code>
<div class="block">Generate a string representation of this instance.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarDateFormat.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="DEFAULT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DEFAULT</pre>
</li>
</ul>
<a name="DDMMYY_MMSS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDMMYY_MMSS</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDMMYY_MMSS</pre>
</li>
</ul>
<a name="DDMMYY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDMMYY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDMMYY</pre>
</li>
</ul>
<a name="DDMMYYYY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDMMYYYY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDMMYYYY</pre>
</li>
</ul>
<a name="DD_MMMM_YYYY_HHMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMMM_YYYY_HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMMM_YYYY_HHMM</pre>
</li>
</ul>
<a name="DD_MMMM_YYYY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMMM_YYYY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMMM_YYYY</pre>
</li>
</ul>
<a name="DD_MMM_HHMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMM_HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMM_HHMM</pre>
</li>
</ul>
<a name="DD_MMM_YY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMM_YY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMM_YY</pre>
</li>
</ul>
<a name="DD_MMMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMMM</pre>
</li>
</ul>
<a name="DD_MMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD_MMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD_MMM</pre>
</li>
</ul>
<a name="DDD_DDMMYY_HHMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DDMMYY_HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DDMMYY_HHMM</pre>
</li>
</ul>
<a name="DDD_DDMMYY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DDMMYY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DDMMYY</pre>
</li>
</ul>
<a name="DDD_DD_MMM_YY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DD_MMM_YY</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DD_MMM_YY</pre>
</li>
</ul>
<a name="DDD_HHMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_HHMM</pre>
</li>
</ul>
<a name="DDD_DD_MMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DD_MMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DD_MMM</pre>
</li>
</ul>
<a name="DDD_DDMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DDMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DDMM</pre>
</li>
</ul>
<a name="DDD_DD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDD_DD</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDD_DD</pre>
</li>
</ul>
<a name="DDMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DDMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DDMM</pre>
</li>
</ul>
<a name="DD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DD</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> DD</pre>
</li>
</ul>
<a name="HHMM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> HHMM</pre>
</li>
</ul>
<a name="MWW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MWW</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> MWW</pre>
</li>
</ul>
<a name="MWWYY_HHMM">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MWWYY_HHMM</h4>
<pre>public static final&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a> MWWYY_HHMM</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (GanttBarDateFormat c : GanttBarDateFormat.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&nbsp;valueOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getInstance-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&nbsp;getInstance(int&nbsp;type)</pre>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - int type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum instance</dd>
</dl>
</li>
</ul>
<a name="getInstance-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&nbsp;getInstance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;type)</pre>
<div class="block">Retrieve an instance of the enum based on its int value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - int type</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum instance</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block">Accessor method used to retrieve the numeric representation of the enum.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/MpxjEnum.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int representation of the enum</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Retrieve the name of this alignment. Note that this is not
 localised.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>name of this alignment type</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Generate a string representation of this instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp">GanttBarDateFormat</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representation of this instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarDateFormat.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarDateFormat.html" target="_top">Frames</a></li>
<li><a href="GanttBarDateFormat.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
