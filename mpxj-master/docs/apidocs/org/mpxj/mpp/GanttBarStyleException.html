<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>GanttBarStyleException (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="GanttBarStyleException (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarStyleException.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html" title="interface in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarStyleException.html" target="_top">Frames</a></li>
<li><a href="GanttBarStyleException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mpp</div>
<h2 title="Class GanttBarStyleException" class="title">Class GanttBarStyleException</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp">org.mpxj.mpp.GanttBarCommonStyle</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mpp.GanttBarStyleException</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">GanttBarStyleException</span>
extends <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp">GanttBarCommonStyle</a></pre>
<div class="block">This class represents the default style for a Gantt chart bar.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#GanttBarStyleException--">GanttBarStyleException</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#getGanttBarStyleID--">getGanttBarStyleID</a></span>()</code>
<div class="block">Set the ID of the Gantt Bar Style to which this exception is applied.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#getTaskUniqueID--">getTaskUniqueID</a></span>()</code>
<div class="block">Retrieve the unique task ID for the task to which this style
 exception applies.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#setBarStyleIndex-int-">setBarStyleIndex</a></span>(int&nbsp;index)</code>
<div class="block">Sets the bar style index.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#setGanttBarStyleID-java.lang.Integer-">setGanttBarStyleID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the ID of the Gantt Bar Style to which this exception is applied.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#setTaskUniqueID-int-">setTaskUniqueID</a></span>(int&nbsp;id)</code>
<div class="block">Sets the task unique ID.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/GanttBarStyleException.html#toString--">toString</a></span>()</code>
<div class="block">Generate a string representation of this instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.mpp.GanttBarCommonStyle">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp">GanttBarCommonStyle</a></h3>
<code><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getBottomText--">getBottomText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getEndColor--">getEndColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getEndShape--">getEndShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getEndType--">getEndType</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getInsideText--">getInsideText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getLeftText--">getLeftText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getMiddleColor--">getMiddleColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getMiddlePattern--">getMiddlePattern</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getMiddleShape--">getMiddleShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getRightText--">getRightText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getStartColor--">getStartColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getStartShape--">getStartShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getStartType--">getStartType</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#getTopText--">getTopText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setBottomText-org.mpxj.FieldType-">setBottomText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setEndColor-java.awt.Color-">setEndColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setEndShape-org.mpxj.mpp.GanttBarStartEndShape-">setEndShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setEndType-org.mpxj.mpp.GanttBarStartEndType-">setEndType</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setInsideText-org.mpxj.FieldType-">setInsideText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setLeftText-org.mpxj.FieldType-">setLeftText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setMiddleColor-java.awt.Color-">setMiddleColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setMiddlePattern-org.mpxj.mpp.ChartPattern-">setMiddlePattern</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setMiddleShape-org.mpxj.mpp.GanttBarMiddleShape-">setMiddleShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setRightText-org.mpxj.FieldType-">setRightText</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setStartColor-java.awt.Color-">setStartColor</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setStartShape-org.mpxj.mpp.GanttBarStartEndShape-">setStartShape</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setStartType-org.mpxj.mpp.GanttBarStartEndType-">setStartType</a>, <a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#setTopText-org.mpxj.FieldType-">setTopText</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GanttBarStyleException--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GanttBarStyleException</h4>
<pre>public&nbsp;GanttBarStyleException()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTaskUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskUniqueID</h4>
<pre>public&nbsp;int&nbsp;getTaskUniqueID()</pre>
<div class="block">Retrieve the unique task ID for the task to which this style
 exception applies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>task ID</dd>
</dl>
</li>
</ul>
<a name="setTaskUniqueID-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskUniqueID</h4>
<pre>public&nbsp;void&nbsp;setTaskUniqueID(int&nbsp;id)</pre>
<div class="block">Sets the task unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - task unique ID</dd>
</dl>
</li>
</ul>
<a name="getGanttBarStyleID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttBarStyleID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getGanttBarStyleID()</pre>
<div class="block">Set the ID of the Gantt Bar Style to which this exception is applied.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bar style ID</dd>
</dl>
</li>
</ul>
<a name="setGanttBarStyleID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGanttBarStyleID</h4>
<pre>public&nbsp;void&nbsp;setGanttBarStyleID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the ID of the Gantt Bar Style to which this exception is applied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - bar style ID</dd>
</dl>
</li>
</ul>
<a name="setBarStyleIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarStyleIndex</h4>
<pre>public&nbsp;void&nbsp;setBarStyleIndex(int&nbsp;index)</pre>
<div class="block">Sets the bar style index.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - bar style index</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Generate a string representation of this instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html#toString--">toString</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp">GanttBarCommonStyle</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representation of this instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/GanttBarStyleException.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html" title="interface in org.mpxj.mpp"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/GanttBarStyleException.html" target="_top">Frames</a></li>
<li><a href="GanttBarStyleException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
