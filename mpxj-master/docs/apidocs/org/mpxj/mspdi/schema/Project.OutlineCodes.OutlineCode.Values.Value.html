<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Project.OutlineCodes.OutlineCode.Values.Value (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Project.OutlineCodes.OutlineCode.Values.Value (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.OutlineCodes.OutlineCode.Values.Value.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" target="_top">Frames</a></li>
<li><a href="Project.OutlineCodes.OutlineCode.Values.Value.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mspdi.schema</div>
<h2 title="Class Project.OutlineCodes.OutlineCode.Values.Value" class="title">Class Project.OutlineCodes.OutlineCode.Values.Value</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mspdi.schema.Project.OutlineCodes.OutlineCode.Values.Value</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Values</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Project.OutlineCodes.OutlineCode.Values.Value</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;all&gt;
         &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="FieldGUID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="Type"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="6"/&gt;
               &lt;enumeration value="9"/&gt;
               &lt;enumeration value="15"/&gt;
               &lt;enumeration value="17"/&gt;
               &lt;enumeration value="21"/&gt;
               &lt;enumeration value="27"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="IsCollapsed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="ParentValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
       &lt;/all&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#description">description</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#fieldGUID">fieldGUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#isCollapsed">isCollapsed</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#parentValueID">parentValueID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#type">type</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#value">value</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#valueID">valueID</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#Value--">Value</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Gets the value of the description property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getFieldGUID--">getFieldGUID</a></span>()</code>
<div class="block">Gets the value of the fieldGUID property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getParentValueID--">getParentValueID</a></span>()</code>
<div class="block">Gets the value of the parentValueID property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getType--">getType</a></span>()</code>
<div class="block">Gets the value of the type property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getValue--">getValue</a></span>()</code>
<div class="block">Gets the value of the value property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#getValueID--">getValueID</a></span>()</code>
<div class="block">Gets the value of the valueID property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#isIsCollapsed--">isIsCollapsed</a></span>()</code>
<div class="block">Gets the value of the isCollapsed property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setDescription-java.lang.String-">setDescription</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the description property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setFieldGUID-java.util.UUID-">setFieldGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Sets the value of the fieldGUID property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setIsCollapsed-java.lang.Boolean-">setIsCollapsed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isCollapsed property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setParentValueID-java.math.BigInteger-">setParentValueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the parentValueID property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setType-java.math.BigInteger-">setType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the type property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setValue-java.lang.String-">setValue</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the value property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html#setValueID-java.math.BigInteger-">setValueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the valueID property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="valueID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> valueID</pre>
</li>
</ul>
<a name="fieldGUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fieldGUID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a> fieldGUID</pre>
</li>
</ul>
<a name="type">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> type</pre>
</li>
</ul>
<a name="isCollapsed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCollapsed</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isCollapsed</pre>
</li>
</ul>
<a name="parentValueID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parentValueID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> parentValueID</pre>
</li>
</ul>
<a name="value">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>value</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> value</pre>
</li>
</ul>
<a name="description">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>description</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> description</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Value--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Value</h4>
<pre>public&nbsp;Value()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getValueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getValueID()</pre>
<div class="block">Gets the value of the valueID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setValueID-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueID</h4>
<pre>public&nbsp;void&nbsp;setValueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the valueID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getFieldGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getFieldGUID()</pre>
<div class="block">Gets the value of the fieldGUID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFieldGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldGUID</h4>
<pre>public&nbsp;void&nbsp;setFieldGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Sets the value of the fieldGUID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getType()</pre>
<div class="block">Gets the value of the type property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setType-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the type property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsCollapsed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsCollapsed</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsCollapsed()</pre>
<div class="block">Gets the value of the isCollapsed property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsCollapsed-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsCollapsed</h4>
<pre>public&nbsp;void&nbsp;setIsCollapsed(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isCollapsed property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getParentValueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentValueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getParentValueID()</pre>
<div class="block">Gets the value of the parentValueID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setParentValueID-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParentValueID</h4>
<pre>public&nbsp;void&nbsp;setParentValueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the parentValueID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getValue()</pre>
<div class="block">Gets the value of the value property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setValue-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValue</h4>
<pre>public&nbsp;void&nbsp;setValue(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the value property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDescription()</pre>
<div class="block">Gets the value of the description property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDescription</h4>
<pre>public&nbsp;void&nbsp;setDescription(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the description property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.OutlineCodes.OutlineCode.Values.Value.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" target="_top">Frames</a></li>
<li><a href="Project.OutlineCodes.OutlineCode.Values.Value.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
