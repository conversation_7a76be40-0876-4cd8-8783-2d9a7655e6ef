<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Project (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Project (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.html" target="_top">Frames</a></li>
<li><a href="Project.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mspdi.schema</div>
<h2 title="Class Project" class="title">Class Project</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mspdi.schema.Project</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Project</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for Project complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType name="Project"&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="SaveVersion" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
         &lt;element name="UID" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="16"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Name" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="255"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="Title" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Subject" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Category" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Company" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Manager" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Author" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="Revision" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="LastSaved" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ScheduleFromStart" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="FYStartDate" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="6"/&gt;
               &lt;enumeration value="7"/&gt;
               &lt;enumeration value="8"/&gt;
               &lt;enumeration value="9"/&gt;
               &lt;enumeration value="10"/&gt;
               &lt;enumeration value="11"/&gt;
               &lt;enumeration value="12"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CriticalSlackLimit" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="CurrencyDigits" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="CurrencySymbol" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="20"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CurrencyCode"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="3"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CurrencySymbolPosition" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="DefaultStartTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
         &lt;element name="DefaultFinishTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
         &lt;element name="MinutesPerDay" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="MinutesPerWeek" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="DaysPerMonth" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="DefaultTaskType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="DefaultFixedCostAccrual" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="DefaultStandardRate" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="DefaultOvertimeRate" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="DurationFormat" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="6"/&gt;
               &lt;enumeration value="7"/&gt;
               &lt;enumeration value="8"/&gt;
               &lt;enumeration value="9"/&gt;
               &lt;enumeration value="10"/&gt;
               &lt;enumeration value="11"/&gt;
               &lt;enumeration value="12"/&gt;
               &lt;enumeration value="19"/&gt;
               &lt;enumeration value="20"/&gt;
               &lt;enumeration value="21"/&gt;
               &lt;enumeration value="35"/&gt;
               &lt;enumeration value="36"/&gt;
               &lt;enumeration value="37"/&gt;
               &lt;enumeration value="38"/&gt;
               &lt;enumeration value="39"/&gt;
               &lt;enumeration value="40"/&gt;
               &lt;enumeration value="41"/&gt;
               &lt;enumeration value="42"/&gt;
               &lt;enumeration value="43"/&gt;
               &lt;enumeration value="44"/&gt;
               &lt;enumeration value="51"/&gt;
               &lt;enumeration value="52"/&gt;
               &lt;enumeration value="53"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="WorkFormat" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="EditableActualCosts" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="HonorConstraints" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="EarnedValueMethod" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="InsertedProjectsLikeSummary" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="MultipleCriticalPaths" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="NewTasksEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="NewTasksEstimated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="SplitsInProgressTasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="SpreadActualCost" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="SpreadPercentComplete" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="TaskUpdatesResource" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="FiscalYearStart" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="WeekStartDay" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="6"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="MoveCompletedEndsBack" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="MoveRemainingStartsBack" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="MoveRemainingStartsForward" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="MoveCompletedEndsForward" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="BaselineForEarnedValue" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="6"/&gt;
               &lt;enumeration value="7"/&gt;
               &lt;enumeration value="8"/&gt;
               &lt;enumeration value="9"/&gt;
               &lt;enumeration value="10"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="AutoAddNewResourcesAndTasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="StatusDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="CurrentDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="MicrosoftProjectServerURL" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="Autolink" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="NewTaskStartDate" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="NewTasksAreManual" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="DefaultTaskEVMethod" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ProjectExternallyEdited" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="ExtendedCreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ActualsInSync" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="RemoveFileProperties" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="AdminProject" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="BaselineCalendar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="UpdateManuallyScheduledTasksWhenEditingLinks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="OutlineCodes" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="OutlineCode" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Guid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="FieldName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Alias" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="PhoneticAlias" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Values" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence maxOccurs="unbounded" minOccurs="0"&gt;
                                       &lt;element name="Value"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;all&gt;
                                                 &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="FieldGUID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                                 &lt;element name="Type"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                       &lt;enumeration value="9"/&gt;
                                                       &lt;enumeration value="15"/&gt;
                                                       &lt;enumeration value="17"/&gt;
                                                       &lt;enumeration value="21"/&gt;
                                                       &lt;enumeration value="27"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="IsCollapsed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                                 &lt;element name="ParentValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                                 &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                               &lt;/all&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Enterprise" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="EnterpriseOutlineCodeAlias" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="ResourceSubstitutionEnabled" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="LeafOnly" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="AllLevelsRequired" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="OnlyTableValuesAllowed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="ShowIndent" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Masks" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Mask" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="Level" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="Type" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Length" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="Separator" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="WBSMasks" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="VerifyUniqueCodes" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="GenerateCodes" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="Prefix" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="50"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="WBSMask" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Level" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="Type"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Length" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="Separator" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ExtendedAttributes" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="FieldName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="CFType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Guid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ElemType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="20"/&gt;
                                   &lt;enumeration value="21"/&gt;
                                   &lt;enumeration value="22"/&gt;
                                   &lt;enumeration value="23"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="MaxMultiValues" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="UserDef" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Alias" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="50"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="SecondaryPID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="AutoRollDown" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="DefaultGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Ltuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="SecondaryGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="PhoneticAlias" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="50"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="RollupType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="CalculationType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Formula" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="RestrictValues" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="ValuelistSortOrder" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="AppendNewValues" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Default" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ValueList" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Value" maxOccurs="unbounded"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;all&gt;
                                                 &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                                                 &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                                 &lt;element name="Description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                                 &lt;element name="Phonetic" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                               &lt;/all&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Calendars" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Calendar" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Name" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="IsBaseCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="IsBaselineCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="BaseCalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="WeekDays" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="DayType"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                                 &lt;element name="TimePeriod" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                           &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="WorkingTimes" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;choice&gt;
                                                             &lt;element name="WorkingTime" maxOccurs="5" minOccurs="0"&gt;
                                                               &lt;complexType&gt;
                                                                 &lt;complexContent&gt;
                                                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                     &lt;sequence&gt;
                                                                       &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                       &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                     &lt;/sequence&gt;
                                                                   &lt;/restriction&gt;
                                                                 &lt;/complexContent&gt;
                                                               &lt;/complexType&gt;
                                                             &lt;/element&gt;
                                                           &lt;/choice&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Exceptions" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Exception" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                                 &lt;element name="TimePeriod" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                           &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="Name" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                                       &lt;maxLength value="512"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Type" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                       &lt;enumeration value="8"/&gt;
                                                       &lt;enumeration value="9"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Period" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="DaysOfWeek" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="MonthItem" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                       &lt;enumeration value="8"/&gt;
                                                       &lt;enumeration value="9"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="MonthPosition" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Month" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                       &lt;enumeration value="8"/&gt;
                                                       &lt;enumeration value="9"/&gt;
                                                       &lt;enumeration value="10"/&gt;
                                                       &lt;enumeration value="11"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="MonthDay" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                                 &lt;element name="WorkingTimes" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;choice&gt;
                                                             &lt;element name="WorkingTime" maxOccurs="5" minOccurs="0"&gt;
                                                               &lt;complexType&gt;
                                                                 &lt;complexContent&gt;
                                                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                     &lt;sequence&gt;
                                                                       &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                       &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                     &lt;/sequence&gt;
                                                                   &lt;/restriction&gt;
                                                                 &lt;/complexContent&gt;
                                                               &lt;/complexType&gt;
                                                             &lt;/element&gt;
                                                           &lt;/choice&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="WorkWeeks" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="WorkWeek" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="TimePeriod" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                           &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Name" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                                       &lt;maxLength value="512"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="WeekDays" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;sequence&gt;
                                                                     &lt;element name="DayType"&gt;
                                                                       &lt;simpleType&gt;
                                                                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                                           &lt;enumeration value="0"/&gt;
                                                                           &lt;enumeration value="1"/&gt;
                                                                           &lt;enumeration value="2"/&gt;
                                                                           &lt;enumeration value="3"/&gt;
                                                                           &lt;enumeration value="4"/&gt;
                                                                           &lt;enumeration value="5"/&gt;
                                                                           &lt;enumeration value="6"/&gt;
                                                                           &lt;enumeration value="7"/&gt;
                                                                         &lt;/restriction&gt;
                                                                       &lt;/simpleType&gt;
                                                                     &lt;/element&gt;
                                                                     &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                                                     &lt;element name="WorkingTimes" minOccurs="0"&gt;
                                                                       &lt;complexType&gt;
                                                                         &lt;complexContent&gt;
                                                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                             &lt;sequence&gt;
                                                                               &lt;choice&gt;
                                                                                 &lt;element name="WorkingTime" maxOccurs="5" minOccurs="0"&gt;
                                                                                   &lt;complexType&gt;
                                                                                     &lt;complexContent&gt;
                                                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                                         &lt;sequence&gt;
                                                                                           &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                                           &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time" minOccurs="0"/&gt;
                                                                                         &lt;/sequence&gt;
                                                                                       &lt;/restriction&gt;
                                                                                     &lt;/complexContent&gt;
                                                                                   &lt;/complexType&gt;
                                                                                 &lt;/element&gt;
                                                                               &lt;/choice&gt;
                                                                             &lt;/sequence&gt;
                                                                           &lt;/restriction&gt;
                                                                         &lt;/complexContent&gt;
                                                                       &lt;/complexType&gt;
                                                                     &lt;/element&gt;
                                                                   &lt;/sequence&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Tasks" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Task" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Name" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Active" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Type" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="IsNull" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Contact" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="WBS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="WBSLevel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="OutlineNumber" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="OutlineLevel" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Priority" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Duration" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="DurationFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                   &lt;enumeration value="9"/&gt;
                                   &lt;enumeration value="10"/&gt;
                                   &lt;enumeration value="11"/&gt;
                                   &lt;enumeration value="12"/&gt;
                                   &lt;enumeration value="19"/&gt;
                                   &lt;enumeration value="20"/&gt;
                                   &lt;enumeration value="21"/&gt;
                                   &lt;enumeration value="35"/&gt;
                                   &lt;enumeration value="36"/&gt;
                                   &lt;enumeration value="37"/&gt;
                                   &lt;enumeration value="38"/&gt;
                                   &lt;enumeration value="39"/&gt;
                                   &lt;enumeration value="40"/&gt;
                                   &lt;enumeration value="41"/&gt;
                                   &lt;enumeration value="42"/&gt;
                                   &lt;enumeration value="43"/&gt;
                                   &lt;enumeration value="44"/&gt;
                                   &lt;enumeration value="51"/&gt;
                                   &lt;enumeration value="52"/&gt;
                                   &lt;enumeration value="53"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="Stop" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Resume" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ResumeValid" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="EffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Recurring" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="OverAllocated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Estimated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Summary" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="DisplayAsSummary" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Critical" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="IsSubproject" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="IsSubprojectReadOnly" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="SubprojectName" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="ExternalTask" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="ExternalTaskProject" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="EarlyStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="EarlyFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="LateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="LateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="StartVariance" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="FinishVariance" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="WorkVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="FreeSlack" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="TotalSlack" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="StartSlack" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="FinishSlack" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="FixedCost" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="FixedCostAccrual" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="PercentComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="PercentWorkComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RegularWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingDuration" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ACWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="CV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="ConstraintType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="CalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="ConstraintDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Deadline" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="LevelAssignments" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="LevelingCanSplit" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="LevelingDelay" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="LevelingDelayFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                   &lt;enumeration value="9"/&gt;
                                   &lt;enumeration value="10"/&gt;
                                   &lt;enumeration value="11"/&gt;
                                   &lt;enumeration value="12"/&gt;
                                   &lt;enumeration value="19"/&gt;
                                   &lt;enumeration value="20"/&gt;
                                   &lt;enumeration value="21"/&gt;
                                   &lt;enumeration value="35"/&gt;
                                   &lt;enumeration value="36"/&gt;
                                   &lt;enumeration value="37"/&gt;
                                   &lt;enumeration value="38"/&gt;
                                   &lt;enumeration value="39"/&gt;
                                   &lt;enumeration value="40"/&gt;
                                   &lt;enumeration value="41"/&gt;
                                   &lt;enumeration value="42"/&gt;
                                   &lt;enumeration value="43"/&gt;
                                   &lt;enumeration value="44"/&gt;
                                   &lt;enumeration value="51"/&gt;
                                   &lt;enumeration value="52"/&gt;
                                   &lt;enumeration value="53"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="PreLeveledStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="PreLeveledFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Hyperlink" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkSubAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="IgnoreResourceCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="HideBar" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Rollup" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="PhysicalPercentComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="EarnedValueMethod" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="PredecessorLink" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                       &lt;element name="Type" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="0"/&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                       &lt;element name="CrossProjectName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                       &lt;element name="LagFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="6"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                             &lt;enumeration value="8"/&gt;
                                             &lt;enumeration value="9"/&gt;
                                             &lt;enumeration value="10"/&gt;
                                             &lt;enumeration value="11"/&gt;
                                             &lt;enumeration value="12"/&gt;
                                             &lt;enumeration value="19"/&gt;
                                             &lt;enumeration value="20"/&gt;
                                             &lt;enumeration value="35"/&gt;
                                             &lt;enumeration value="36"/&gt;
                                             &lt;enumeration value="37"/&gt;
                                             &lt;enumeration value="38"/&gt;
                                             &lt;enumeration value="39"/&gt;
                                             &lt;enumeration value="40"/&gt;
                                             &lt;enumeration value="41"/&gt;
                                             &lt;enumeration value="42"/&gt;
                                             &lt;enumeration value="43"/&gt;
                                             &lt;enumeration value="44"/&gt;
                                             &lt;enumeration value="51"/&gt;
                                             &lt;enumeration value="52"/&gt;
                                             &lt;enumeration value="53"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="ActualWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="DurationFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="6"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                             &lt;enumeration value="8"/&gt;
                                             &lt;enumeration value="9"/&gt;
                                             &lt;enumeration value="10"/&gt;
                                             &lt;enumeration value="11"/&gt;
                                             &lt;enumeration value="12"/&gt;
                                             &lt;enumeration value="19"/&gt;
                                             &lt;enumeration value="20"/&gt;
                                             &lt;enumeration value="21"/&gt;
                                             &lt;enumeration value="35"/&gt;
                                             &lt;enumeration value="36"/&gt;
                                             &lt;enumeration value="37"/&gt;
                                             &lt;enumeration value="38"/&gt;
                                             &lt;enumeration value="39"/&gt;
                                             &lt;enumeration value="40"/&gt;
                                             &lt;enumeration value="41"/&gt;
                                             &lt;enumeration value="42"/&gt;
                                             &lt;enumeration value="43"/&gt;
                                             &lt;enumeration value="44"/&gt;
                                             &lt;enumeration value="51"/&gt;
                                             &lt;enumeration value="52"/&gt;
                                             &lt;enumeration value="53"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Baseline" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                       &lt;element name="Interim" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                       &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                       &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                       &lt;element name="Duration" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                                       &lt;element name="DurationFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="6"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                             &lt;enumeration value="8"/&gt;
                                             &lt;enumeration value="9"/&gt;
                                             &lt;enumeration value="10"/&gt;
                                             &lt;enumeration value="11"/&gt;
                                             &lt;enumeration value="12"/&gt;
                                             &lt;enumeration value="19"/&gt;
                                             &lt;enumeration value="20"/&gt;
                                             &lt;enumeration value="21"/&gt;
                                             &lt;enumeration value="35"/&gt;
                                             &lt;enumeration value="36"/&gt;
                                             &lt;enumeration value="37"/&gt;
                                             &lt;enumeration value="38"/&gt;
                                             &lt;enumeration value="39"/&gt;
                                             &lt;enumeration value="40"/&gt;
                                             &lt;enumeration value="41"/&gt;
                                             &lt;enumeration value="42"/&gt;
                                             &lt;enumeration value="43"/&gt;
                                             &lt;enumeration value="44"/&gt;
                                             &lt;enumeration value="51"/&gt;
                                             &lt;enumeration value="52"/&gt;
                                             &lt;enumeration value="53"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="EstimatedDuration" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                       &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                                       &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                       &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                       &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                       &lt;element name="FixedCost" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="OutlineCode" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                       &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="IsPublished" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="StatusManager" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="CommitmentStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="CommitmentFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="CommitmentType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="FinishText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="DurationText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ManualDuration" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                             &lt;element name="Project" type="{http://schemas.microsoft.com/project}Project" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Resources" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Resource" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Name" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Type" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="IsNull" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Initials" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Phonetics" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="NTAccount" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="MaterialLabel" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Code" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Group" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="WorkGroup" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="EmailAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Hyperlink" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkSubAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="MaxUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="PeakUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="OverAllocated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="CanLevel" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="AccrueAt" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RegularWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="OvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="PercentWorkComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="StandardRateFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="WorkVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="CostVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="SV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="CV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="ACWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="CalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="IsGeneric" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="IsInactive" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="IsEnterprise" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="BookingType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="ActualWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActiveDirectoryGUID" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="16"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="DurationFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="6"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                             &lt;enumeration value="8"/&gt;
                                             &lt;enumeration value="9"/&gt;
                                             &lt;enumeration value="10"/&gt;
                                             &lt;enumeration value="11"/&gt;
                                             &lt;enumeration value="12"/&gt;
                                             &lt;enumeration value="19"/&gt;
                                             &lt;enumeration value="20"/&gt;
                                             &lt;enumeration value="21"/&gt;
                                             &lt;enumeration value="35"/&gt;
                                             &lt;enumeration value="36"/&gt;
                                             &lt;enumeration value="37"/&gt;
                                             &lt;enumeration value="38"/&gt;
                                             &lt;enumeration value="39"/&gt;
                                             &lt;enumeration value="40"/&gt;
                                             &lt;enumeration value="41"/&gt;
                                             &lt;enumeration value="42"/&gt;
                                             &lt;enumeration value="43"/&gt;
                                             &lt;enumeration value="44"/&gt;
                                             &lt;enumeration value="51"/&gt;
                                             &lt;enumeration value="52"/&gt;
                                             &lt;enumeration value="53"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Baseline" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                                       &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                                       &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                       &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                       &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="OutlineCode" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                                       &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="CostCenter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="IsCostResource" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="AssnOwner" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="AssnOwnerGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="IsBudget" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="AvailabilityPeriods" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="AvailabilityPeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                 &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                                 &lt;element name="AvailableUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Rates" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Rate" maxOccurs="25" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="RatesFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                                 &lt;element name="RatesTo" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                                 &lt;element name="RateTable" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                                 &lt;element name="StandardRateFormat" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                                 &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="7"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Assignments" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Assignment" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="TaskUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="ResourceUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="PercentWorkComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="ActualWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ACWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="Confirmed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="CostRateTable" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="CostVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="CV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="Delay" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="FinishVariance" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Hyperlink" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="HyperlinkSubAddress" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;maxLength value="512"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="WorkVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="HasFixedRateUnits" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="FixedMaterial" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="LevelingDelay" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="LevelingDelayFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                   &lt;enumeration value="9"/&gt;
                                   &lt;enumeration value="10"/&gt;
                                   &lt;enumeration value="11"/&gt;
                                   &lt;enumeration value="12"/&gt;
                                   &lt;enumeration value="19"/&gt;
                                   &lt;enumeration value="20"/&gt;
                                   &lt;enumeration value="21"/&gt;
                                   &lt;enumeration value="35"/&gt;
                                   &lt;enumeration value="36"/&gt;
                                   &lt;enumeration value="37"/&gt;
                                   &lt;enumeration value="38"/&gt;
                                   &lt;enumeration value="39"/&gt;
                                   &lt;enumeration value="40"/&gt;
                                   &lt;enumeration value="41"/&gt;
                                   &lt;enumeration value="42"/&gt;
                                   &lt;enumeration value="43"/&gt;
                                   &lt;enumeration value="44"/&gt;
                                   &lt;enumeration value="51"/&gt;
                                   &lt;enumeration value="52"/&gt;
                                   &lt;enumeration value="53"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="LinkedFields" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Overallocated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="PeakUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="RateScale" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="RegularWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="RemainingOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="RemainingWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ResponsePending" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Stop" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="Resume" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="StartVariance" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="Summary" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="SV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="Units" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="UpdateNeeded" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                             &lt;element name="VAC" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="WorkContour" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BookingType" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="ActualWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ActualOvertimeWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="AssnOwner" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="AssnOwnerGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="BudgetCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="BudgetWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="DurationFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="6"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                             &lt;enumeration value="8"/&gt;
                                             &lt;enumeration value="9"/&gt;
                                             &lt;enumeration value="10"/&gt;
                                             &lt;enumeration value="11"/&gt;
                                             &lt;enumeration value="12"/&gt;
                                             &lt;enumeration value="19"/&gt;
                                             &lt;enumeration value="20"/&gt;
                                             &lt;enumeration value="21"/&gt;
                                             &lt;enumeration value="35"/&gt;
                                             &lt;enumeration value="36"/&gt;
                                             &lt;enumeration value="37"/&gt;
                                             &lt;enumeration value="38"/&gt;
                                             &lt;enumeration value="39"/&gt;
                                             &lt;enumeration value="40"/&gt;
                                             &lt;enumeration value="41"/&gt;
                                             &lt;enumeration value="42"/&gt;
                                             &lt;enumeration value="43"/&gt;
                                             &lt;enumeration value="44"/&gt;
                                             &lt;enumeration value="51"/&gt;
                                             &lt;enumeration value="52"/&gt;
                                             &lt;enumeration value="53"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Baseline" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                       &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                                       &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                                       &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                       &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="f404000" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404001" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404002" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404003" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404004" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404005" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404006" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404007" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404008" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404009" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40400f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404010" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404011" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404012" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404013" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404014" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404015" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404016" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404017" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404018" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404019" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40401f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404020" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404021" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404022" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404023" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404024" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404025" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404026" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404027" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404028" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404029" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40402f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404030" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404031" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404032" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404033" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404034" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404035" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404036" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404037" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404038" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404039" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40403f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404040" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404041" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404042" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404043" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404044" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404045" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404046" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404047" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404048" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404049" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40404f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404050" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404051" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404052" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404053" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404054" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404055" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404056" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404057" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404058" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404059" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40405f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404060" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404061" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404062" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404063" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404064" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404065" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404066" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404067" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404068" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404069" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40406f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404070" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404071" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404072" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404073" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404074" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404075" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404076" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404077" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404078" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404079" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40407f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404080" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404081" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404082" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404083" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404084" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404085" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404086" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404087" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404088" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404089" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40408f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404090" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404091" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404092" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404093" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404094" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404095" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404096" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404097" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404098" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f404099" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409a" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409b" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409c" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409d" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409e" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f40409f" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a0" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a1" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a2" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a3" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a4" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a5" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a6" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a7" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a8" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040a9" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040aa" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040ab" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040ac" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040ad" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040ae" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040af" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b0" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b1" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b2" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b3" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b4" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b5" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b6" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b7" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b8" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040b9" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040ba" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040bb" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040bc" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040bd" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040be" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040bf" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c0" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c1" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c2" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c3" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c4" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c5" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c6" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c7" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="f4040c8" type="{http://www.w3.org/2001/XMLSchema}anyType" minOccurs="0"/&gt;
                             &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#actualsInSync">actualsInSync</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#adminProject">adminProject</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#assignments">assignments</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#author">author</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#autoAddNewResourcesAndTasks">autoAddNewResourcesAndTasks</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#autolink">autolink</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#baselineCalendar">baselineCalendar</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#baselineForEarnedValue">baselineForEarnedValue</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#calendars">calendars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#calendarUID">calendarUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#category">category</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#company">company</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#creationDate">creationDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#criticalSlackLimit">criticalSlackLimit</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#currencyCode">currencyCode</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#currencyDigits">currencyDigits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#currencySymbol">currencySymbol</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#currencySymbolPosition">currencySymbolPosition</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#currentDate">currentDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#daysPerMonth">daysPerMonth</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultFinishTime">defaultFinishTime</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultFixedCostAccrual">defaultFixedCostAccrual</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultOvertimeRate">defaultOvertimeRate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultStandardRate">defaultStandardRate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultStartTime">defaultStartTime</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultTaskEVMethod">defaultTaskEVMethod</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#defaultTaskType">defaultTaskType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#durationFormat">durationFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#earnedValueMethod">earnedValueMethod</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#editableActualCosts">editableActualCosts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#extendedAttributes">extendedAttributes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#extendedCreationDate">extendedCreationDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#finishDate">finishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#fiscalYearStart">fiscalYearStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#fyStartDate">fyStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#guid">guid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#honorConstraints">honorConstraints</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#insertedProjectsLikeSummary">insertedProjectsLikeSummary</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled">keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#lastSaved">lastSaved</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#manager">manager</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#microsoftProjectServerURL">microsoftProjectServerURL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#minutesPerDay">minutesPerDay</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#minutesPerWeek">minutesPerWeek</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#moveCompletedEndsBack">moveCompletedEndsBack</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#moveCompletedEndsForward">moveCompletedEndsForward</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#moveRemainingStartsBack">moveRemainingStartsBack</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#moveRemainingStartsForward">moveRemainingStartsForward</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#multipleCriticalPaths">multipleCriticalPaths</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#newTasksAreManual">newTasksAreManual</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#newTasksEffortDriven">newTasksEffortDriven</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#newTasksEstimated">newTasksEstimated</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#newTaskStartDate">newTaskStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#outlineCodes">outlineCodes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#projectExternallyEdited">projectExternallyEdited</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#removeFileProperties">removeFileProperties</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#resources">resources</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#revision">revision</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#saveVersion">saveVersion</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#scheduleFromStart">scheduleFromStart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#splitsInProgressTasks">splitsInProgressTasks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#spreadActualCost">spreadActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#spreadPercentComplete">spreadPercentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#statusDate">statusDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#subject">subject</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#tasks">tasks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#taskUpdatesResource">taskUpdatesResource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#title">title</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#uid">uid</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#updateManuallyScheduledTasksWhenEditingLinks">updateManuallyScheduledTasksWhenEditingLinks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#wbsMasks">wbsMasks</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#weekStartDay">weekStartDay</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#workFormat">workFormat</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#Project--">Project</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getAssignments--">getAssignments</a></span>()</code>
<div class="block">Gets the value of the assignments property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getAuthor--">getAuthor</a></span>()</code>
<div class="block">Gets the value of the author property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getBaselineCalendar--">getBaselineCalendar</a></span>()</code>
<div class="block">Gets the value of the baselineCalendar property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getBaselineForEarnedValue--">getBaselineForEarnedValue</a></span>()</code>
<div class="block">Gets the value of the baselineForEarnedValue property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCalendars--">getCalendars</a></span>()</code>
<div class="block">Gets the value of the calendars property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCalendarUID--">getCalendarUID</a></span>()</code>
<div class="block">Gets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCategory--">getCategory</a></span>()</code>
<div class="block">Gets the value of the category property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCompany--">getCompany</a></span>()</code>
<div class="block">Gets the value of the company property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Gets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCriticalSlackLimit--">getCriticalSlackLimit</a></span>()</code>
<div class="block">Gets the value of the criticalSlackLimit property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCurrencyCode--">getCurrencyCode</a></span>()</code>
<div class="block">Gets the value of the currencyCode property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCurrencyDigits--">getCurrencyDigits</a></span>()</code>
<div class="block">Gets the value of the currencyDigits property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCurrencySymbol--">getCurrencySymbol</a></span>()</code>
<div class="block">Gets the value of the currencySymbol property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCurrencySymbolPosition--">getCurrencySymbolPosition</a></span>()</code>
<div class="block">Gets the value of the currencySymbolPosition property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getCurrentDate--">getCurrentDate</a></span>()</code>
<div class="block">Gets the value of the currentDate property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDaysPerMonth--">getDaysPerMonth</a></span>()</code>
<div class="block">Gets the value of the daysPerMonth property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultFinishTime--">getDefaultFinishTime</a></span>()</code>
<div class="block">Gets the value of the defaultFinishTime property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultFixedCostAccrual--">getDefaultFixedCostAccrual</a></span>()</code>
<div class="block">Gets the value of the defaultFixedCostAccrual property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultOvertimeRate--">getDefaultOvertimeRate</a></span>()</code>
<div class="block">Gets the value of the defaultOvertimeRate property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultStandardRate--">getDefaultStandardRate</a></span>()</code>
<div class="block">Gets the value of the defaultStandardRate property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultStartTime--">getDefaultStartTime</a></span>()</code>
<div class="block">Gets the value of the defaultStartTime property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultTaskEVMethod--">getDefaultTaskEVMethod</a></span>()</code>
<div class="block">Gets the value of the defaultTaskEVMethod property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDefaultTaskType--">getDefaultTaskType</a></span>()</code>
<div class="block">Gets the value of the defaultTaskType property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getDurationFormat--">getDurationFormat</a></span>()</code>
<div class="block">Gets the value of the durationFormat property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getEarnedValueMethod--">getEarnedValueMethod</a></span>()</code>
<div class="block">Gets the value of the earnedValueMethod property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getExtendedAttributes--">getExtendedAttributes</a></span>()</code>
<div class="block">Gets the value of the extendedAttributes property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getExtendedCreationDate--">getExtendedCreationDate</a></span>()</code>
<div class="block">Gets the value of the extendedCreationDate property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getFinishDate--">getFinishDate</a></span>()</code>
<div class="block">Gets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getFYStartDate--">getFYStartDate</a></span>()</code>
<div class="block">Gets the value of the fyStartDate property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Gets the value of the guid property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getLastSaved--">getLastSaved</a></span>()</code>
<div class="block">Gets the value of the lastSaved property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getManager--">getManager</a></span>()</code>
<div class="block">Gets the value of the manager property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getMinutesPerDay--">getMinutesPerDay</a></span>()</code>
<div class="block">Gets the value of the minutesPerDay property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getMinutesPerWeek--">getMinutesPerWeek</a></span>()</code>
<div class="block">Gets the value of the minutesPerWeek property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getNewTaskStartDate--">getNewTaskStartDate</a></span>()</code>
<div class="block">Gets the value of the newTaskStartDate property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getOutlineCodes--">getOutlineCodes</a></span>()</code>
<div class="block">Gets the value of the outlineCodes property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getResources--">getResources</a></span>()</code>
<div class="block">Gets the value of the resources property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getRevision--">getRevision</a></span>()</code>
<div class="block">Gets the value of the revision property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getSaveVersion--">getSaveVersion</a></span>()</code>
<div class="block">Gets the value of the saveVersion property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getStatusDate--">getStatusDate</a></span>()</code>
<div class="block">Gets the value of the statusDate property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getSubject--">getSubject</a></span>()</code>
<div class="block">Gets the value of the subject property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getTasks--">getTasks</a></span>()</code>
<div class="block">Gets the value of the tasks property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getTitle--">getTitle</a></span>()</code>
<div class="block">Gets the value of the title property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getUID--">getUID</a></span>()</code>
<div class="block">Gets the value of the uid property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getWBSMasks--">getWBSMasks</a></span>()</code>
<div class="block">Gets the value of the wbsMasks property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getWeekStartDay--">getWeekStartDay</a></span>()</code>
<div class="block">Gets the value of the weekStartDay property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#getWorkFormat--">getWorkFormat</a></span>()</code>
<div class="block">Gets the value of the workFormat property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isActualsInSync--">isActualsInSync</a></span>()</code>
<div class="block">Gets the value of the actualsInSync property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isAdminProject--">isAdminProject</a></span>()</code>
<div class="block">Gets the value of the adminProject property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isAutoAddNewResourcesAndTasks--">isAutoAddNewResourcesAndTasks</a></span>()</code>
<div class="block">Gets the value of the autoAddNewResourcesAndTasks property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isAutolink--">isAutolink</a></span>()</code>
<div class="block">Gets the value of the autolink property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isEditableActualCosts--">isEditableActualCosts</a></span>()</code>
<div class="block">Gets the value of the editableActualCosts property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isFiscalYearStart--">isFiscalYearStart</a></span>()</code>
<div class="block">Gets the value of the fiscalYearStart property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isHonorConstraints--">isHonorConstraints</a></span>()</code>
<div class="block">Gets the value of the honorConstraints property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isInsertedProjectsLikeSummary--">isInsertedProjectsLikeSummary</a></span>()</code>
<div class="block">Gets the value of the insertedProjectsLikeSummary property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled--">isKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</a></span>()</code>
<div class="block">Gets the value of the keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMicrosoftProjectServerURL--">isMicrosoftProjectServerURL</a></span>()</code>
<div class="block">Gets the value of the microsoftProjectServerURL property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMoveCompletedEndsBack--">isMoveCompletedEndsBack</a></span>()</code>
<div class="block">Gets the value of the moveCompletedEndsBack property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMoveCompletedEndsForward--">isMoveCompletedEndsForward</a></span>()</code>
<div class="block">Gets the value of the moveCompletedEndsForward property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMoveRemainingStartsBack--">isMoveRemainingStartsBack</a></span>()</code>
<div class="block">Gets the value of the moveRemainingStartsBack property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMoveRemainingStartsForward--">isMoveRemainingStartsForward</a></span>()</code>
<div class="block">Gets the value of the moveRemainingStartsForward property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isMultipleCriticalPaths--">isMultipleCriticalPaths</a></span>()</code>
<div class="block">Gets the value of the multipleCriticalPaths property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isNewTasksAreManual--">isNewTasksAreManual</a></span>()</code>
<div class="block">Gets the value of the newTasksAreManual property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isNewTasksEffortDriven--">isNewTasksEffortDriven</a></span>()</code>
<div class="block">Gets the value of the newTasksEffortDriven property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isNewTasksEstimated--">isNewTasksEstimated</a></span>()</code>
<div class="block">Gets the value of the newTasksEstimated property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isProjectExternallyEdited--">isProjectExternallyEdited</a></span>()</code>
<div class="block">Gets the value of the projectExternallyEdited property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isRemoveFileProperties--">isRemoveFileProperties</a></span>()</code>
<div class="block">Gets the value of the removeFileProperties property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isScheduleFromStart--">isScheduleFromStart</a></span>()</code>
<div class="block">Gets the value of the scheduleFromStart property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isSplitsInProgressTasks--">isSplitsInProgressTasks</a></span>()</code>
<div class="block">Gets the value of the splitsInProgressTasks property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isSpreadActualCost--">isSpreadActualCost</a></span>()</code>
<div class="block">Gets the value of the spreadActualCost property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isSpreadPercentComplete--">isSpreadPercentComplete</a></span>()</code>
<div class="block">Gets the value of the spreadPercentComplete property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isTaskUpdatesResource--">isTaskUpdatesResource</a></span>()</code>
<div class="block">Gets the value of the taskUpdatesResource property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#isUpdateManuallyScheduledTasksWhenEditingLinks--">isUpdateManuallyScheduledTasksWhenEditingLinks</a></span>()</code>
<div class="block">Gets the value of the updateManuallyScheduledTasksWhenEditingLinks property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setActualsInSync-java.lang.Boolean-">setActualsInSync</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualsInSync property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setAdminProject-java.lang.Boolean-">setAdminProject</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the adminProject property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setAssignments-org.mpxj.mspdi.schema.Project.Assignments-">setAssignments</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a>&nbsp;value)</code>
<div class="block">Sets the value of the assignments property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setAuthor-java.lang.String-">setAuthor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the author property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setAutoAddNewResourcesAndTasks-java.lang.Boolean-">setAutoAddNewResourcesAndTasks</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the autoAddNewResourcesAndTasks property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setAutolink-java.lang.Boolean-">setAutolink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the autolink property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setBaselineCalendar-java.lang.String-">setBaselineCalendar</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineCalendar property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setBaselineForEarnedValue-java.math.BigInteger-">setBaselineForEarnedValue</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineForEarnedValue property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCalendars-org.mpxj.mspdi.schema.Project.Calendars-">setCalendars</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendars property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCalendarUID-java.math.BigInteger-">setCalendarUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCategory-java.lang.String-">setCategory</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the category property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCompany-java.lang.String-">setCompany</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the company property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCreationDate-java.time.LocalDateTime-">setCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCriticalSlackLimit-java.math.BigInteger-">setCriticalSlackLimit</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the criticalSlackLimit property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCurrencyCode-java.lang.String-">setCurrencyCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the currencyCode property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCurrencyDigits-java.math.BigInteger-">setCurrencyDigits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the currencyDigits property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCurrencySymbol-java.lang.String-">setCurrencySymbol</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the currencySymbol property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCurrencySymbolPosition-org.mpxj.CurrencySymbolPosition-">setCurrencySymbolPosition</a></span>(<a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;value)</code>
<div class="block">Sets the value of the currencySymbolPosition property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setCurrentDate-java.time.LocalDateTime-">setCurrentDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the currentDate property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDaysPerMonth-java.math.BigInteger-">setDaysPerMonth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the daysPerMonth property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultFinishTime-java.time.LocalTime-">setDefaultFinishTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultFinishTime property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultFixedCostAccrual-org.mpxj.AccrueType-">setDefaultFixedCostAccrual</a></span>(<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultFixedCostAccrual property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultOvertimeRate-java.math.BigDecimal-">setDefaultOvertimeRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultOvertimeRate property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultStandardRate-java.math.BigDecimal-">setDefaultStandardRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultStandardRate property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultStartTime-java.time.LocalTime-">setDefaultStartTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultStartTime property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultTaskEVMethod-java.math.BigInteger-">setDefaultTaskEVMethod</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultTaskEVMethod property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDefaultTaskType-org.mpxj.TaskType-">setDefaultTaskType</a></span>(<a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultTaskType property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setDurationFormat-java.math.BigInteger-">setDurationFormat</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the durationFormat property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setEarnedValueMethod-java.math.BigInteger-">setEarnedValueMethod</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the earnedValueMethod property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setEditableActualCosts-java.lang.Boolean-">setEditableActualCosts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the editableActualCosts property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setExtendedAttributes-org.mpxj.mspdi.schema.Project.ExtendedAttributes-">setExtendedAttributes</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a>&nbsp;value)</code>
<div class="block">Sets the value of the extendedAttributes property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setExtendedCreationDate-java.time.LocalDateTime-">setExtendedCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the extendedCreationDate property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setFinishDate-java.time.LocalDateTime-">setFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setFiscalYearStart-java.lang.Boolean-">setFiscalYearStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the fiscalYearStart property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setFYStartDate-java.math.BigInteger-">setFYStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the fyStartDate property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Sets the value of the guid property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setHonorConstraints-java.lang.Boolean-">setHonorConstraints</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the honorConstraints property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setInsertedProjectsLikeSummary-java.lang.Boolean-">setInsertedProjectsLikeSummary</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the insertedProjectsLikeSummary property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled-java.lang.Boolean-">setKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setLastSaved-java.time.LocalDateTime-">setLastSaved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastSaved property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setManager-java.lang.String-">setManager</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the manager property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMicrosoftProjectServerURL-java.lang.Boolean-">setMicrosoftProjectServerURL</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the microsoftProjectServerURL property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMinutesPerDay-java.math.BigInteger-">setMinutesPerDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the minutesPerDay property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMinutesPerWeek-java.math.BigInteger-">setMinutesPerWeek</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the minutesPerWeek property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMoveCompletedEndsBack-java.lang.Boolean-">setMoveCompletedEndsBack</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the moveCompletedEndsBack property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMoveCompletedEndsForward-java.lang.Boolean-">setMoveCompletedEndsForward</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the moveCompletedEndsForward property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMoveRemainingStartsBack-java.lang.Boolean-">setMoveRemainingStartsBack</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the moveRemainingStartsBack property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMoveRemainingStartsForward-java.lang.Boolean-">setMoveRemainingStartsForward</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the moveRemainingStartsForward property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setMultipleCriticalPaths-java.lang.Boolean-">setMultipleCriticalPaths</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the multipleCriticalPaths property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setNewTasksAreManual-java.lang.Boolean-">setNewTasksAreManual</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the newTasksAreManual property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setNewTasksEffortDriven-java.lang.Boolean-">setNewTasksEffortDriven</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the newTasksEffortDriven property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setNewTasksEstimated-java.lang.Boolean-">setNewTasksEstimated</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the newTasksEstimated property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setNewTaskStartDate-java.math.BigInteger-">setNewTaskStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the newTaskStartDate property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setOutlineCodes-org.mpxj.mspdi.schema.Project.OutlineCodes-">setOutlineCodes</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a>&nbsp;value)</code>
<div class="block">Sets the value of the outlineCodes property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setProjectExternallyEdited-java.lang.Boolean-">setProjectExternallyEdited</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectExternallyEdited property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setRemoveFileProperties-java.lang.Boolean-">setRemoveFileProperties</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the removeFileProperties property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setResources-org.mpxj.mspdi.schema.Project.Resources-">setResources</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a>&nbsp;value)</code>
<div class="block">Sets the value of the resources property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setRevision-java.math.BigInteger-">setRevision</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the revision property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setSaveVersion-java.math.BigInteger-">setSaveVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the saveVersion property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setScheduleFromStart-java.lang.Boolean-">setScheduleFromStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the scheduleFromStart property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setSplitsInProgressTasks-java.lang.Boolean-">setSplitsInProgressTasks</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the splitsInProgressTasks property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setSpreadActualCost-java.lang.Boolean-">setSpreadActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the spreadActualCost property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setSpreadPercentComplete-java.lang.Boolean-">setSpreadPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the spreadPercentComplete property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setStatusDate-java.time.LocalDateTime-">setStatusDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the statusDate property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setSubject-java.lang.String-">setSubject</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the subject property.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setTasks-org.mpxj.mspdi.schema.Project.Tasks-">setTasks</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a>&nbsp;value)</code>
<div class="block">Sets the value of the tasks property.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setTaskUpdatesResource-java.lang.Boolean-">setTaskUpdatesResource</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the taskUpdatesResource property.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setTitle-java.lang.String-">setTitle</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the title property.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setUID-java.lang.String-">setUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the uid property.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setUpdateManuallyScheduledTasksWhenEditingLinks-java.lang.Boolean-">setUpdateManuallyScheduledTasksWhenEditingLinks</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the updateManuallyScheduledTasksWhenEditingLinks property.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setWBSMasks-org.mpxj.mspdi.schema.Project.WBSMasks-">setWBSMasks</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a>&nbsp;value)</code>
<div class="block">Sets the value of the wbsMasks property.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setWeekStartDay-java.math.BigInteger-">setWeekStartDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the weekStartDay property.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.html#setWorkFormat-java.math.BigInteger-">setWorkFormat</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the workFormat property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="saveVersion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveVersion</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> saveVersion</pre>
</li>
</ul>
<a name="uid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> uid</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="guid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>guid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a> guid</pre>
</li>
</ul>
<a name="title">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>title</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> title</pre>
</li>
</ul>
<a name="subject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subject</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> subject</pre>
</li>
</ul>
<a name="category">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>category</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> category</pre>
</li>
</ul>
<a name="company">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>company</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> company</pre>
</li>
</ul>
<a name="manager">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manager</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> manager</pre>
</li>
</ul>
<a name="author">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>author</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> author</pre>
</li>
</ul>
<a name="creationDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> creationDate</pre>
</li>
</ul>
<a name="revision">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>revision</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> revision</pre>
</li>
</ul>
<a name="lastSaved">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastSaved</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> lastSaved</pre>
</li>
</ul>
<a name="scheduleFromStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scheduleFromStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> scheduleFromStart</pre>
</li>
</ul>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="finishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> finishDate</pre>
</li>
</ul>
<a name="fyStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fyStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> fyStartDate</pre>
</li>
</ul>
<a name="criticalSlackLimit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>criticalSlackLimit</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> criticalSlackLimit</pre>
</li>
</ul>
<a name="currencyDigits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currencyDigits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> currencyDigits</pre>
</li>
</ul>
<a name="currencySymbol">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currencySymbol</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> currencySymbol</pre>
</li>
</ul>
<a name="currencyCode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currencyCode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> currencyCode</pre>
</li>
</ul>
<a name="currencySymbolPosition">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currencySymbolPosition</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a> currencySymbolPosition</pre>
</li>
</ul>
<a name="calendarUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarUID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> calendarUID</pre>
</li>
</ul>
<a name="defaultStartTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultStartTime</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a> defaultStartTime</pre>
</li>
</ul>
<a name="defaultFinishTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultFinishTime</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a> defaultFinishTime</pre>
</li>
</ul>
<a name="minutesPerDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minutesPerDay</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> minutesPerDay</pre>
</li>
</ul>
<a name="minutesPerWeek">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minutesPerWeek</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> minutesPerWeek</pre>
</li>
</ul>
<a name="daysPerMonth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>daysPerMonth</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> daysPerMonth</pre>
</li>
</ul>
<a name="defaultTaskType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultTaskType</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a> defaultTaskType</pre>
</li>
</ul>
<a name="defaultFixedCostAccrual">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultFixedCostAccrual</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a> defaultFixedCostAccrual</pre>
</li>
</ul>
<a name="defaultStandardRate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultStandardRate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> defaultStandardRate</pre>
</li>
</ul>
<a name="defaultOvertimeRate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultOvertimeRate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> defaultOvertimeRate</pre>
</li>
</ul>
<a name="durationFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>durationFormat</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> durationFormat</pre>
</li>
</ul>
<a name="workFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workFormat</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> workFormat</pre>
</li>
</ul>
<a name="editableActualCosts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editableActualCosts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> editableActualCosts</pre>
</li>
</ul>
<a name="honorConstraints">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>honorConstraints</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> honorConstraints</pre>
</li>
</ul>
<a name="earnedValueMethod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earnedValueMethod</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> earnedValueMethod</pre>
</li>
</ul>
<a name="insertedProjectsLikeSummary">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>insertedProjectsLikeSummary</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> insertedProjectsLikeSummary</pre>
</li>
</ul>
<a name="multipleCriticalPaths">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>multipleCriticalPaths</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> multipleCriticalPaths</pre>
</li>
</ul>
<a name="newTasksEffortDriven">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTasksEffortDriven</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> newTasksEffortDriven</pre>
</li>
</ul>
<a name="newTasksEstimated">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTasksEstimated</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> newTasksEstimated</pre>
</li>
</ul>
<a name="splitsInProgressTasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>splitsInProgressTasks</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> splitsInProgressTasks</pre>
</li>
</ul>
<a name="spreadActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>spreadActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> spreadActualCost</pre>
</li>
</ul>
<a name="spreadPercentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>spreadPercentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> spreadPercentComplete</pre>
</li>
</ul>
<a name="taskUpdatesResource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskUpdatesResource</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> taskUpdatesResource</pre>
</li>
</ul>
<a name="fiscalYearStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fiscalYearStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> fiscalYearStart</pre>
</li>
</ul>
<a name="weekStartDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>weekStartDay</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> weekStartDay</pre>
</li>
</ul>
<a name="moveCompletedEndsBack">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCompletedEndsBack</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> moveCompletedEndsBack</pre>
</li>
</ul>
<a name="moveRemainingStartsBack">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveRemainingStartsBack</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> moveRemainingStartsBack</pre>
</li>
</ul>
<a name="moveRemainingStartsForward">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveRemainingStartsForward</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> moveRemainingStartsForward</pre>
</li>
</ul>
<a name="moveCompletedEndsForward">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCompletedEndsForward</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> moveCompletedEndsForward</pre>
</li>
</ul>
<a name="baselineForEarnedValue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineForEarnedValue</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> baselineForEarnedValue</pre>
</li>
</ul>
<a name="autoAddNewResourcesAndTasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>autoAddNewResourcesAndTasks</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> autoAddNewResourcesAndTasks</pre>
</li>
</ul>
<a name="statusDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>statusDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> statusDate</pre>
</li>
</ul>
<a name="currentDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>currentDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> currentDate</pre>
</li>
</ul>
<a name="microsoftProjectServerURL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>microsoftProjectServerURL</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> microsoftProjectServerURL</pre>
</li>
</ul>
<a name="autolink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>autolink</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> autolink</pre>
</li>
</ul>
<a name="newTaskStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTaskStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> newTaskStartDate</pre>
</li>
</ul>
<a name="newTasksAreManual">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newTasksAreManual</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> newTasksAreManual</pre>
</li>
</ul>
<a name="defaultTaskEVMethod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultTaskEVMethod</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> defaultTaskEVMethod</pre>
</li>
</ul>
<a name="projectExternallyEdited">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectExternallyEdited</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> projectExternallyEdited</pre>
</li>
</ul>
<a name="extendedCreationDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendedCreationDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> extendedCreationDate</pre>
</li>
</ul>
<a name="actualsInSync">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualsInSync</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> actualsInSync</pre>
</li>
</ul>
<a name="removeFileProperties">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeFileProperties</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> removeFileProperties</pre>
</li>
</ul>
<a name="adminProject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>adminProject</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> adminProject</pre>
</li>
</ul>
<a name="baselineCalendar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineCalendar</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> baselineCalendar</pre>
</li>
</ul>
<a name="updateManuallyScheduledTasksWhenEditingLinks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateManuallyScheduledTasksWhenEditingLinks</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> updateManuallyScheduledTasksWhenEditingLinks</pre>
</li>
</ul>
<a name="keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</pre>
</li>
</ul>
<a name="outlineCodes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outlineCodes</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a> outlineCodes</pre>
</li>
</ul>
<a name="wbsMasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wbsMasks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a> wbsMasks</pre>
</li>
</ul>
<a name="extendedAttributes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendedAttributes</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a> extendedAttributes</pre>
</li>
</ul>
<a name="calendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendars</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a> calendars</pre>
</li>
</ul>
<a name="tasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tasks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a> tasks</pre>
</li>
</ul>
<a name="resources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a> resources</pre>
</li>
</ul>
<a name="assignments">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>assignments</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a> assignments</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Project--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Project</h4>
<pre>public&nbsp;Project()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSaveVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSaveVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getSaveVersion()</pre>
<div class="block">Gets the value of the saveVersion property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setSaveVersion-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSaveVersion</h4>
<pre>public&nbsp;void&nbsp;setSaveVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the saveVersion property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getUID()</pre>
<div class="block">Gets the value of the uid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUID</h4>
<pre>public&nbsp;void&nbsp;setUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the uid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Gets the value of the guid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Sets the value of the guid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTitle()</pre>
<div class="block">Gets the value of the title property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTitle</h4>
<pre>public&nbsp;void&nbsp;setTitle(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the title property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getSubject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubject</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSubject()</pre>
<div class="block">Gets the value of the subject property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSubject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubject</h4>
<pre>public&nbsp;void&nbsp;setSubject(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the subject property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCategory()</pre>
<div class="block">Gets the value of the category property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>public&nbsp;void&nbsp;setCategory(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the category property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCompany--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompany</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCompany()</pre>
<div class="block">Gets the value of the company property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCompany-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompany</h4>
<pre>public&nbsp;void&nbsp;setCompany(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the company property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManager</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getManager()</pre>
<div class="block">Gets the value of the manager property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setManager-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManager</h4>
<pre>public&nbsp;void&nbsp;setManager(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the manager property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAuthor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAuthor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAuthor()</pre>
<div class="block">Gets the value of the author property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAuthor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuthor</h4>
<pre>public&nbsp;void&nbsp;setAuthor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the author property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreationDate()</pre>
<div class="block">Gets the value of the creationDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the creationDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRevision--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRevision</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getRevision()</pre>
<div class="block">Gets the value of the revision property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setRevision-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRevision</h4>
<pre>public&nbsp;void&nbsp;setRevision(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the revision property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastSaved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSaved</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLastSaved()</pre>
<div class="block">Gets the value of the lastSaved property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastSaved-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastSaved</h4>
<pre>public&nbsp;void&nbsp;setLastSaved(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastSaved property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isScheduleFromStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isScheduleFromStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isScheduleFromStart()</pre>
<div class="block">Gets the value of the scheduleFromStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setScheduleFromStart-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduleFromStart</h4>
<pre>public&nbsp;void&nbsp;setScheduleFromStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the scheduleFromStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinishDate()</pre>
<div class="block">Gets the value of the finishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishDate</h4>
<pre>public&nbsp;void&nbsp;setFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the finishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFYStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFYStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getFYStartDate()</pre>
<div class="block">Gets the value of the fyStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setFYStartDate-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFYStartDate</h4>
<pre>public&nbsp;void&nbsp;setFYStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the fyStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getCriticalSlackLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCriticalSlackLimit</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getCriticalSlackLimit()</pre>
<div class="block">Gets the value of the criticalSlackLimit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setCriticalSlackLimit-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCriticalSlackLimit</h4>
<pre>public&nbsp;void&nbsp;setCriticalSlackLimit(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the criticalSlackLimit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getCurrencyDigits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencyDigits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getCurrencyDigits()</pre>
<div class="block">Gets the value of the currencyDigits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setCurrencyDigits-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencyDigits</h4>
<pre>public&nbsp;void&nbsp;setCurrencyDigits(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the currencyDigits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getCurrencySymbol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencySymbol</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrencySymbol()</pre>
<div class="block">Gets the value of the currencySymbol property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCurrencySymbol-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencySymbol</h4>
<pre>public&nbsp;void&nbsp;setCurrencySymbol(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the currencySymbol property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCurrencyCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencyCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrencyCode()</pre>
<div class="block">Gets the value of the currencyCode property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCurrencyCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencyCode</h4>
<pre>public&nbsp;void&nbsp;setCurrencyCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the currencyCode property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCurrencySymbolPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencySymbolPosition</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;getCurrencySymbolPosition()</pre>
<div class="block">Gets the value of the currencySymbolPosition property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCurrencySymbolPosition-org.mpxj.CurrencySymbolPosition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencySymbolPosition</h4>
<pre>public&nbsp;void&nbsp;setCurrencySymbolPosition(<a href="../../../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;value)</pre>
<div class="block">Sets the value of the currencySymbolPosition property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getCalendarUID()</pre>
<div class="block">Gets the value of the calendarUID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarUID-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarUID</h4>
<pre>public&nbsp;void&nbsp;setCalendarUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarUID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultStartTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;getDefaultStartTime()</pre>
<div class="block">Gets the value of the defaultStartTime property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultStartTime-java.time.LocalTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultStartTime</h4>
<pre>public&nbsp;void&nbsp;setDefaultStartTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultStartTime property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultFinishTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFinishTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;getDefaultFinishTime()</pre>
<div class="block">Gets the value of the defaultFinishTime property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultFinishTime-java.time.LocalTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFinishTime</h4>
<pre>public&nbsp;void&nbsp;setDefaultFinishTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultFinishTime property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMinutesPerDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getMinutesPerDay()</pre>
<div class="block">Gets the value of the minutesPerDay property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setMinutesPerDay-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerDay</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the minutesPerDay property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getMinutesPerWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerWeek</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getMinutesPerWeek()</pre>
<div class="block">Gets the value of the minutesPerWeek property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setMinutesPerWeek-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerWeek</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerWeek(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the minutesPerWeek property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getDaysPerMonth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDaysPerMonth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getDaysPerMonth()</pre>
<div class="block">Gets the value of the daysPerMonth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setDaysPerMonth-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDaysPerMonth</h4>
<pre>public&nbsp;void&nbsp;setDaysPerMonth(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the daysPerMonth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultTaskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTaskType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;getDefaultTaskType()</pre>
<div class="block">Gets the value of the defaultTaskType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultTaskType-org.mpxj.TaskType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultTaskType</h4>
<pre>public&nbsp;void&nbsp;setDefaultTaskType(<a href="../../../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultTaskType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultFixedCostAccrual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFixedCostAccrual</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getDefaultFixedCostAccrual()</pre>
<div class="block">Gets the value of the defaultFixedCostAccrual property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultFixedCostAccrual-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFixedCostAccrual</h4>
<pre>public&nbsp;void&nbsp;setDefaultFixedCostAccrual(<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultFixedCostAccrual property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultStandardRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultStandardRate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getDefaultStandardRate()</pre>
<div class="block">Gets the value of the defaultStandardRate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultStandardRate-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultStandardRate</h4>
<pre>public&nbsp;void&nbsp;setDefaultStandardRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultStandardRate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultOvertimeRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultOvertimeRate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getDefaultOvertimeRate()</pre>
<div class="block">Gets the value of the defaultOvertimeRate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultOvertimeRate-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultOvertimeRate</h4>
<pre>public&nbsp;void&nbsp;setDefaultOvertimeRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultOvertimeRate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDurationFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationFormat</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getDurationFormat()</pre>
<div class="block">Gets the value of the durationFormat property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setDurationFormat-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationFormat</h4>
<pre>public&nbsp;void&nbsp;setDurationFormat(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the durationFormat property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getWorkFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkFormat</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getWorkFormat()</pre>
<div class="block">Gets the value of the workFormat property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setWorkFormat-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkFormat</h4>
<pre>public&nbsp;void&nbsp;setWorkFormat(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the workFormat property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isEditableActualCosts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditableActualCosts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEditableActualCosts()</pre>
<div class="block">Gets the value of the editableActualCosts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEditableActualCosts-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditableActualCosts</h4>
<pre>public&nbsp;void&nbsp;setEditableActualCosts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the editableActualCosts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isHonorConstraints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHonorConstraints</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isHonorConstraints()</pre>
<div class="block">Gets the value of the honorConstraints property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHonorConstraints-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHonorConstraints</h4>
<pre>public&nbsp;void&nbsp;setHonorConstraints(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the honorConstraints property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarnedValueMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueMethod</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getEarnedValueMethod()</pre>
<div class="block">Gets the value of the earnedValueMethod property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarnedValueMethod-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueMethod</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueMethod(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earnedValueMethod property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isInsertedProjectsLikeSummary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInsertedProjectsLikeSummary</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isInsertedProjectsLikeSummary()</pre>
<div class="block">Gets the value of the insertedProjectsLikeSummary property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setInsertedProjectsLikeSummary-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInsertedProjectsLikeSummary</h4>
<pre>public&nbsp;void&nbsp;setInsertedProjectsLikeSummary(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the insertedProjectsLikeSummary property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMultipleCriticalPaths--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMultipleCriticalPaths</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMultipleCriticalPaths()</pre>
<div class="block">Gets the value of the multipleCriticalPaths property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMultipleCriticalPaths-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMultipleCriticalPaths</h4>
<pre>public&nbsp;void&nbsp;setMultipleCriticalPaths(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the multipleCriticalPaths property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isNewTasksEffortDriven--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNewTasksEffortDriven</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isNewTasksEffortDriven()</pre>
<div class="block">Gets the value of the newTasksEffortDriven property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNewTasksEffortDriven-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksEffortDriven</h4>
<pre>public&nbsp;void&nbsp;setNewTasksEffortDriven(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the newTasksEffortDriven property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isNewTasksEstimated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNewTasksEstimated</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isNewTasksEstimated()</pre>
<div class="block">Gets the value of the newTasksEstimated property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNewTasksEstimated-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksEstimated</h4>
<pre>public&nbsp;void&nbsp;setNewTasksEstimated(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the newTasksEstimated property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isSplitsInProgressTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSplitsInProgressTasks</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isSplitsInProgressTasks()</pre>
<div class="block">Gets the value of the splitsInProgressTasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSplitsInProgressTasks-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplitsInProgressTasks</h4>
<pre>public&nbsp;void&nbsp;setSplitsInProgressTasks(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the splitsInProgressTasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isSpreadActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSpreadActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isSpreadActualCost()</pre>
<div class="block">Gets the value of the spreadActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSpreadActualCost-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpreadActualCost</h4>
<pre>public&nbsp;void&nbsp;setSpreadActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the spreadActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isSpreadPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSpreadPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isSpreadPercentComplete()</pre>
<div class="block">Gets the value of the spreadPercentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSpreadPercentComplete-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpreadPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setSpreadPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the spreadPercentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isTaskUpdatesResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTaskUpdatesResource</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isTaskUpdatesResource()</pre>
<div class="block">Gets the value of the taskUpdatesResource property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setTaskUpdatesResource-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskUpdatesResource</h4>
<pre>public&nbsp;void&nbsp;setTaskUpdatesResource(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the taskUpdatesResource property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isFiscalYearStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFiscalYearStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isFiscalYearStart()</pre>
<div class="block">Gets the value of the fiscalYearStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFiscalYearStart-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFiscalYearStart</h4>
<pre>public&nbsp;void&nbsp;setFiscalYearStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the fiscalYearStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWeekStartDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeekStartDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getWeekStartDay()</pre>
<div class="block">Gets the value of the weekStartDay property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setWeekStartDay-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWeekStartDay</h4>
<pre>public&nbsp;void&nbsp;setWeekStartDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the weekStartDay property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isMoveCompletedEndsBack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMoveCompletedEndsBack</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMoveCompletedEndsBack()</pre>
<div class="block">Gets the value of the moveCompletedEndsBack property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMoveCompletedEndsBack-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveCompletedEndsBack</h4>
<pre>public&nbsp;void&nbsp;setMoveCompletedEndsBack(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the moveCompletedEndsBack property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMoveRemainingStartsBack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMoveRemainingStartsBack</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMoveRemainingStartsBack()</pre>
<div class="block">Gets the value of the moveRemainingStartsBack property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMoveRemainingStartsBack-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveRemainingStartsBack</h4>
<pre>public&nbsp;void&nbsp;setMoveRemainingStartsBack(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the moveRemainingStartsBack property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMoveRemainingStartsForward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMoveRemainingStartsForward</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMoveRemainingStartsForward()</pre>
<div class="block">Gets the value of the moveRemainingStartsForward property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMoveRemainingStartsForward-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveRemainingStartsForward</h4>
<pre>public&nbsp;void&nbsp;setMoveRemainingStartsForward(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the moveRemainingStartsForward property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMoveCompletedEndsForward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMoveCompletedEndsForward</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMoveCompletedEndsForward()</pre>
<div class="block">Gets the value of the moveCompletedEndsForward property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMoveCompletedEndsForward-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveCompletedEndsForward</h4>
<pre>public&nbsp;void&nbsp;setMoveCompletedEndsForward(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the moveCompletedEndsForward property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineForEarnedValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineForEarnedValue</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getBaselineForEarnedValue()</pre>
<div class="block">Gets the value of the baselineForEarnedValue property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineForEarnedValue-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineForEarnedValue</h4>
<pre>public&nbsp;void&nbsp;setBaselineForEarnedValue(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineForEarnedValue property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isAutoAddNewResourcesAndTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAutoAddNewResourcesAndTasks</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAutoAddNewResourcesAndTasks()</pre>
<div class="block">Gets the value of the autoAddNewResourcesAndTasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAutoAddNewResourcesAndTasks-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoAddNewResourcesAndTasks</h4>
<pre>public&nbsp;void&nbsp;setAutoAddNewResourcesAndTasks(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the autoAddNewResourcesAndTasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStatusDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStatusDate()</pre>
<div class="block">Gets the value of the statusDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStatusDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatusDate</h4>
<pre>public&nbsp;void&nbsp;setStatusDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the statusDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCurrentDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCurrentDate()</pre>
<div class="block">Gets the value of the currentDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCurrentDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrentDate</h4>
<pre>public&nbsp;void&nbsp;setCurrentDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the currentDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isMicrosoftProjectServerURL--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMicrosoftProjectServerURL</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMicrosoftProjectServerURL()</pre>
<div class="block">Gets the value of the microsoftProjectServerURL property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMicrosoftProjectServerURL-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMicrosoftProjectServerURL</h4>
<pre>public&nbsp;void&nbsp;setMicrosoftProjectServerURL(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the microsoftProjectServerURL property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAutolink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAutolink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAutolink()</pre>
<div class="block">Gets the value of the autolink property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAutolink-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutolink</h4>
<pre>public&nbsp;void&nbsp;setAutolink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the autolink property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getNewTaskStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTaskStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getNewTaskStartDate()</pre>
<div class="block">Gets the value of the newTaskStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setNewTaskStartDate-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTaskStartDate</h4>
<pre>public&nbsp;void&nbsp;setNewTaskStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the newTaskStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isNewTasksAreManual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNewTasksAreManual</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isNewTasksAreManual()</pre>
<div class="block">Gets the value of the newTasksAreManual property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNewTasksAreManual-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksAreManual</h4>
<pre>public&nbsp;void&nbsp;setNewTasksAreManual(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the newTasksAreManual property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultTaskEVMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTaskEVMethod</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getDefaultTaskEVMethod()</pre>
<div class="block">Gets the value of the defaultTaskEVMethod property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultTaskEVMethod-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultTaskEVMethod</h4>
<pre>public&nbsp;void&nbsp;setDefaultTaskEVMethod(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultTaskEVMethod property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="isProjectExternallyEdited--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProjectExternallyEdited</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isProjectExternallyEdited()</pre>
<div class="block">Gets the value of the projectExternallyEdited property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectExternallyEdited-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectExternallyEdited</h4>
<pre>public&nbsp;void&nbsp;setProjectExternallyEdited(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectExternallyEdited property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getExtendedCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtendedCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getExtendedCreationDate()</pre>
<div class="block">Gets the value of the extendedCreationDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setExtendedCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtendedCreationDate</h4>
<pre>public&nbsp;void&nbsp;setExtendedCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the extendedCreationDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isActualsInSync--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActualsInSync</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isActualsInSync()</pre>
<div class="block">Gets the value of the actualsInSync property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualsInSync-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualsInSync</h4>
<pre>public&nbsp;void&nbsp;setActualsInSync(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualsInSync property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isRemoveFileProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRemoveFileProperties</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isRemoveFileProperties()</pre>
<div class="block">Gets the value of the removeFileProperties property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemoveFileProperties-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoveFileProperties</h4>
<pre>public&nbsp;void&nbsp;setRemoveFileProperties(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the removeFileProperties property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdminProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdminProject</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAdminProject()</pre>
<div class="block">Gets the value of the adminProject property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdminProject-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdminProject</h4>
<pre>public&nbsp;void&nbsp;setAdminProject(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the adminProject property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCalendar</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineCalendar()</pre>
<div class="block">Gets the value of the baselineCalendar property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineCalendar-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCalendar</h4>
<pre>public&nbsp;void&nbsp;setBaselineCalendar(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineCalendar property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isUpdateManuallyScheduledTasksWhenEditingLinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUpdateManuallyScheduledTasksWhenEditingLinks</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isUpdateManuallyScheduledTasksWhenEditingLinks()</pre>
<div class="block">Gets the value of the updateManuallyScheduledTasksWhenEditingLinks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUpdateManuallyScheduledTasksWhenEditingLinks-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdateManuallyScheduledTasksWhenEditingLinks</h4>
<pre>public&nbsp;void&nbsp;setUpdateManuallyScheduledTasksWhenEditingLinks(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the updateManuallyScheduledTasksWhenEditingLinks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled()</pre>
<div class="block">Gets the value of the keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled</h4>
<pre>public&nbsp;void&nbsp;setKeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the keepTaskOnNearestWorkingTimeWhenMadeAutoScheduled property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getOutlineCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineCodes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a>&nbsp;getOutlineCodes()</pre>
<div class="block">Gets the value of the outlineCodes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes</code></a></dd>
</dl>
</li>
</ul>
<a name="setOutlineCodes-org.mpxj.mspdi.schema.Project.OutlineCodes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineCodes</h4>
<pre>public&nbsp;void&nbsp;setOutlineCodes(<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a>&nbsp;value)</pre>
<div class="block">Sets the value of the outlineCodes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes</code></a></dd>
</dl>
</li>
</ul>
<a name="getWBSMasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWBSMasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a>&nbsp;getWBSMasks()</pre>
<div class="block">Gets the value of the wbsMasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks</code></a></dd>
</dl>
</li>
</ul>
<a name="setWBSMasks-org.mpxj.mspdi.schema.Project.WBSMasks-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWBSMasks</h4>
<pre>public&nbsp;void&nbsp;setWBSMasks(<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a>&nbsp;value)</pre>
<div class="block">Sets the value of the wbsMasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks</code></a></dd>
</dl>
</li>
</ul>
<a name="getExtendedAttributes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtendedAttributes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a>&nbsp;getExtendedAttributes()</pre>
<div class="block">Gets the value of the extendedAttributes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes</code></a></dd>
</dl>
</li>
</ul>
<a name="setExtendedAttributes-org.mpxj.mspdi.schema.Project.ExtendedAttributes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtendedAttributes</h4>
<pre>public&nbsp;void&nbsp;setExtendedAttributes(<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a>&nbsp;value)</pre>
<div class="block">Sets the value of the extendedAttributes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a>&nbsp;getCalendars()</pre>
<div class="block">Gets the value of the calendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendars-org.mpxj.mspdi.schema.Project.Calendars-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendars</h4>
<pre>public&nbsp;void&nbsp;setCalendars(<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a>&nbsp;getTasks()</pre>
<div class="block">Gets the value of the tasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="setTasks-org.mpxj.mspdi.schema.Project.Tasks-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTasks</h4>
<pre>public&nbsp;void&nbsp;setTasks(<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a>&nbsp;value)</pre>
<div class="block">Sets the value of the tasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="getResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResources</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a>&nbsp;getResources()</pre>
<div class="block">Gets the value of the resources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="setResources-org.mpxj.mspdi.schema.Project.Resources-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResources</h4>
<pre>public&nbsp;void&nbsp;setResources(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="getAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssignments</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a>&nbsp;getAssignments()</pre>
<div class="block">Gets the value of the assignments property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssignments-org.mpxj.mspdi.schema.Project.Assignments-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAssignments</h4>
<pre>public&nbsp;void&nbsp;setAssignments(<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assignments property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.html" target="_top">Frames</a></li>
<li><a href="Project.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
