<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Project.Resources.Resource (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Project.Resources.Resource (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.Resources.Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.Resources.Resource.html" target="_top">Frames</a></li>
<li><a href="Project.Resources.Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mspdi.schema</div>
<h2 title="Class Project.Resources.Resource" class="title">Class Project.Resources.Resource</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mspdi.schema.Project.Resources.Resource</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Project.Resources.Resource</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
         &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="Name" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Type" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="IsNull" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="Initials" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Phonetics" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="NTAccount" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="MaterialLabel" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Code" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Group" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="WorkGroup" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="EmailAddress" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Hyperlink" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="HyperlinkAddress" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="HyperlinkSubAddress" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="512"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="MaxUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="PeakUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="OverAllocated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="CanLevel" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="AccrueAt" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="RegularWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="OvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="ActualWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="RemainingWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="RemainingOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="PercentWorkComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="StandardRateFormat" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="7"/&gt;
               &lt;enumeration value="8"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="1"/&gt;
               &lt;enumeration value="2"/&gt;
               &lt;enumeration value="3"/&gt;
               &lt;enumeration value="4"/&gt;
               &lt;enumeration value="5"/&gt;
               &lt;enumeration value="7"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="RemainingOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
         &lt;element name="WorkVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="CostVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="SV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="CV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="ACWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="CalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
         &lt;element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
         &lt;element name="IsGeneric" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsInactive" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsEnterprise" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="BookingType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ActualWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
         &lt;element name="ActiveDirectoryGUID" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="16"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="DurationFormat" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="3"/&gt;
                         &lt;enumeration value="4"/&gt;
                         &lt;enumeration value="5"/&gt;
                         &lt;enumeration value="6"/&gt;
                         &lt;enumeration value="7"/&gt;
                         &lt;enumeration value="8"/&gt;
                         &lt;enumeration value="9"/&gt;
                         &lt;enumeration value="10"/&gt;
                         &lt;enumeration value="11"/&gt;
                         &lt;enumeration value="12"/&gt;
                         &lt;enumeration value="19"/&gt;
                         &lt;enumeration value="20"/&gt;
                         &lt;enumeration value="21"/&gt;
                         &lt;enumeration value="35"/&gt;
                         &lt;enumeration value="36"/&gt;
                         &lt;enumeration value="37"/&gt;
                         &lt;enumeration value="38"/&gt;
                         &lt;enumeration value="39"/&gt;
                         &lt;enumeration value="40"/&gt;
                         &lt;enumeration value="41"/&gt;
                         &lt;enumeration value="42"/&gt;
                         &lt;enumeration value="43"/&gt;
                         &lt;enumeration value="44"/&gt;
                         &lt;enumeration value="51"/&gt;
                         &lt;enumeration value="52"/&gt;
                         &lt;enumeration value="53"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Baseline" maxOccurs="unbounded" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                   &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="OutlineCode" maxOccurs="unbounded" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                   &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="CostCenter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="IsCostResource" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="AssnOwner" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="AssnOwnerGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="IsBudget" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="AvailabilityPeriods" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="AvailabilityPeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                             &lt;element name="AvailableUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Rates" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Rate" maxOccurs="25" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="RatesFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                             &lt;element name="RatesTo" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                             &lt;element name="RateTable" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="StandardRateFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                             &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#accrueAt">accrueAt</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#activeDirectoryGUID">activeDirectoryGUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualCost">actualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualOvertimeCost">actualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualOvertimeWork">actualOvertimeWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualOvertimeWorkProtected">actualOvertimeWorkProtected</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualWork">actualWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#actualWorkProtected">actualWorkProtected</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#acwp">acwp</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#assnOwner">assnOwner</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#assnOwnerGuid">assnOwnerGuid</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#availabilityPeriods">availabilityPeriods</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#availableFrom">availableFrom</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#availableTo">availableTo</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#baseline">baseline</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#bcwp">bcwp</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#bcws">bcws</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#bookingType">bookingType</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#calendarUID">calendarUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#canLevel">canLevel</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#code">code</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#cost">cost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#costCenter">costCenter</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#costPerUse">costPerUse</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#costVariance">costVariance</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#creationDate">creationDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#cv">cv</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#emailAddress">emailAddress</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#extendedAttribute">extendedAttribute</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#finish">finish</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#group">group</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#guid">guid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#hyperlink">hyperlink</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#hyperlinkAddress">hyperlinkAddress</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#hyperlinkSubAddress">hyperlinkSubAddress</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#id">id</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#initials">initials</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isBudget">isBudget</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isCostResource">isCostResource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isEnterprise">isEnterprise</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isGeneric">isGeneric</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isInactive">isInactive</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isNull">isNull</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#materialLabel">materialLabel</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#maxUnits">maxUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#notes">notes</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#ntAccount">ntAccount</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#outlineCode">outlineCode</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#overAllocated">overAllocated</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#overtimeCost">overtimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#overtimeRate">overtimeRate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#overtimeRateFormat">overtimeRateFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#overtimeWork">overtimeWork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#peakUnits">peakUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#percentWorkComplete">percentWorkComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#phonetics">phonetics</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#rates">rates</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#regularWork">regularWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#remainingCost">remainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#remainingOvertimeCost">remainingOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#remainingOvertimeWork">remainingOvertimeWork</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#remainingWork">remainingWork</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#standardRate">standardRate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#standardRateFormat">standardRateFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#start">start</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#sv">sv</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#timephasedData">timephasedData</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#type">type</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#uid">uid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#work">work</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#workGroup">workGroup</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#workVariance">workVariance</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#Resource--">Resource</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAccrueAt--">getAccrueAt</a></span>()</code>
<div class="block">Gets the value of the accrueAt property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActiveDirectoryGUID--">getActiveDirectoryGUID</a></span>()</code>
<div class="block">Gets the value of the activeDirectoryGUID property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Gets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualOvertimeWork--">getActualOvertimeWork</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeWork property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualOvertimeWorkProtected--">getActualOvertimeWorkProtected</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeWorkProtected property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualWork--">getActualWork</a></span>()</code>
<div class="block">Gets the value of the actualWork property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getActualWorkProtected--">getActualWorkProtected</a></span>()</code>
<div class="block">Gets the value of the actualWorkProtected property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getACWP--">getACWP</a></span>()</code>
<div class="block">Gets the value of the acwp property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAssnOwner--">getAssnOwner</a></span>()</code>
<div class="block">Gets the value of the assnOwner property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAssnOwnerGuid--">getAssnOwnerGuid</a></span>()</code>
<div class="block">Gets the value of the assnOwnerGuid property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAvailabilityPeriods--">getAvailabilityPeriods</a></span>()</code>
<div class="block">Gets the value of the availabilityPeriods property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAvailableFrom--">getAvailableFrom</a></span>()</code>
<div class="block">Gets the value of the availableFrom property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getAvailableTo--">getAvailableTo</a></span>()</code>
<div class="block">Gets the value of the availableTo property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getBaseline--">getBaseline</a></span>()</code>
<div class="block">Gets the value of the baseline property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getBCWP--">getBCWP</a></span>()</code>
<div class="block">Gets the value of the bcwp property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getBCWS--">getBCWS</a></span>()</code>
<div class="block">Gets the value of the bcws property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getBookingType--">getBookingType</a></span>()</code>
<div class="block">Gets the value of the bookingType property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCalendarUID--">getCalendarUID</a></span>()</code>
<div class="block">Gets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCode--">getCode</a></span>()</code>
<div class="block">Gets the value of the code property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCost--">getCost</a></span>()</code>
<div class="block">Gets the value of the cost property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCostCenter--">getCostCenter</a></span>()</code>
<div class="block">Gets the value of the costCenter property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCostPerUse--">getCostPerUse</a></span>()</code>
<div class="block">Gets the value of the costPerUse property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCostVariance--">getCostVariance</a></span>()</code>
<div class="block">Gets the value of the costVariance property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Gets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getCV--">getCV</a></span>()</code>
<div class="block">Gets the value of the cv property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getEmailAddress--">getEmailAddress</a></span>()</code>
<div class="block">Gets the value of the emailAddress property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getExtendedAttribute--">getExtendedAttribute</a></span>()</code>
<div class="block">Gets the value of the extendedAttribute property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getFinish--">getFinish</a></span>()</code>
<div class="block">Gets the value of the finish property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getGroup--">getGroup</a></span>()</code>
<div class="block">Gets the value of the group property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Gets the value of the guid property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getHyperlink--">getHyperlink</a></span>()</code>
<div class="block">Gets the value of the hyperlink property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getHyperlinkAddress--">getHyperlinkAddress</a></span>()</code>
<div class="block">Gets the value of the hyperlinkAddress property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getHyperlinkSubAddress--">getHyperlinkSubAddress</a></span>()</code>
<div class="block">Gets the value of the hyperlinkSubAddress property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getID--">getID</a></span>()</code>
<div class="block">Gets the value of the id property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getInitials--">getInitials</a></span>()</code>
<div class="block">Gets the value of the initials property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getMaterialLabel--">getMaterialLabel</a></span>()</code>
<div class="block">Gets the value of the materialLabel property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getMaxUnits--">getMaxUnits</a></span>()</code>
<div class="block">Gets the value of the maxUnits property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Gets the value of the notes property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getNTAccount--">getNTAccount</a></span>()</code>
<div class="block">Gets the value of the ntAccount property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getOutlineCode--">getOutlineCode</a></span>()</code>
<div class="block">Gets the value of the outlineCode property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getOvertimeCost--">getOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the overtimeCost property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getOvertimeRate--">getOvertimeRate</a></span>()</code>
<div class="block">Gets the value of the overtimeRate property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getOvertimeRateFormat--">getOvertimeRateFormat</a></span>()</code>
<div class="block">Gets the value of the overtimeRateFormat property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getOvertimeWork--">getOvertimeWork</a></span>()</code>
<div class="block">Gets the value of the overtimeWork property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getPeakUnits--">getPeakUnits</a></span>()</code>
<div class="block">Gets the value of the peakUnits property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getPercentWorkComplete--">getPercentWorkComplete</a></span>()</code>
<div class="block">Gets the value of the percentWorkComplete property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getPhonetics--">getPhonetics</a></span>()</code>
<div class="block">Gets the value of the phonetics property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRates--">getRates</a></span>()</code>
<div class="block">Gets the value of the rates property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRegularWork--">getRegularWork</a></span>()</code>
<div class="block">Gets the value of the regularWork property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Gets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRemainingOvertimeCost--">getRemainingOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the remainingOvertimeCost property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRemainingOvertimeWork--">getRemainingOvertimeWork</a></span>()</code>
<div class="block">Gets the value of the remainingOvertimeWork property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getRemainingWork--">getRemainingWork</a></span>()</code>
<div class="block">Gets the value of the remainingWork property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getStandardRate--">getStandardRate</a></span>()</code>
<div class="block">Gets the value of the standardRate property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getStandardRateFormat--">getStandardRateFormat</a></span>()</code>
<div class="block">Gets the value of the standardRateFormat property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getStart--">getStart</a></span>()</code>
<div class="block">Gets the value of the start property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getSV--">getSV</a></span>()</code>
<div class="block">Gets the value of the sv property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getTimephasedData--">getTimephasedData</a></span>()</code>
<div class="block">Gets the value of the timephasedData property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getType--">getType</a></span>()</code>
<div class="block">Gets the value of the type property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getUID--">getUID</a></span>()</code>
<div class="block">Gets the value of the uid property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getWork--">getWork</a></span>()</code>
<div class="block">Gets the value of the work property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getWorkGroup--">getWorkGroup</a></span>()</code>
<div class="block">Gets the value of the workGroup property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#getWorkVariance--">getWorkVariance</a></span>()</code>
<div class="block">Gets the value of the workVariance property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isCanLevel--">isCanLevel</a></span>()</code>
<div class="block">Gets the value of the canLevel property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsBudget--">isIsBudget</a></span>()</code>
<div class="block">Gets the value of the isBudget property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsCostResource--">isIsCostResource</a></span>()</code>
<div class="block">Gets the value of the isCostResource property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsEnterprise--">isIsEnterprise</a></span>()</code>
<div class="block">Gets the value of the isEnterprise property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsGeneric--">isIsGeneric</a></span>()</code>
<div class="block">Gets the value of the isGeneric property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsInactive--">isIsInactive</a></span>()</code>
<div class="block">Gets the value of the isInactive property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isIsNull--">isIsNull</a></span>()</code>
<div class="block">Gets the value of the isNull property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#isOverAllocated--">isOverAllocated</a></span>()</code>
<div class="block">Gets the value of the overAllocated property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAccrueAt-org.mpxj.AccrueType-">setAccrueAt</a></span>(<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</code>
<div class="block">Sets the value of the accrueAt property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActiveDirectoryGUID-java.lang.String-">setActiveDirectoryGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the activeDirectoryGUID property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualCost-java.math.BigDecimal-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualOvertimeCost-java.math.BigDecimal-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualOvertimeWork-java.lang.String-">setActualOvertimeWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeWork property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualOvertimeWorkProtected-java.lang.String-">setActualOvertimeWorkProtected</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeWorkProtected property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualWork-java.lang.String-">setActualWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualWork property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setActualWorkProtected-java.lang.String-">setActualWorkProtected</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualWorkProtected property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setACWP-java.math.BigDecimal-">setACWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the acwp property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAssnOwner-java.lang.String-">setAssnOwner</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the assnOwner property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAssnOwnerGuid-java.lang.String-">setAssnOwnerGuid</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the assnOwnerGuid property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAvailabilityPeriods-org.mpxj.mspdi.schema.Project.Resources.Resource.AvailabilityPeriods-">setAvailabilityPeriods</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a>&nbsp;value)</code>
<div class="block">Sets the value of the availabilityPeriods property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAvailableFrom-java.time.LocalDateTime-">setAvailableFrom</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the availableFrom property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setAvailableTo-java.time.LocalDateTime-">setAvailableTo</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the availableTo property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setBCWP-java.math.BigDecimal-">setBCWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the bcwp property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setBCWS-java.math.BigDecimal-">setBCWS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the bcws property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setBookingType-org.mpxj.BookingType-">setBookingType</a></span>(<a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;value)</code>
<div class="block">Sets the value of the bookingType property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCalendarUID-java.math.BigInteger-">setCalendarUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCanLevel-java.lang.Boolean-">setCanLevel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the canLevel property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCode-java.lang.String-">setCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the code property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCost-java.math.BigDecimal-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the cost property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCostCenter-java.lang.String-">setCostCenter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the costCenter property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCostPerUse-java.math.BigDecimal-">setCostPerUse</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the costPerUse property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCostVariance-java.math.BigDecimal-">setCostVariance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the costVariance property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCreationDate-java.time.LocalDateTime-">setCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setCV-java.math.BigDecimal-">setCV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the cv property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setEmailAddress-java.lang.String-">setEmailAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the emailAddress property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setFinish-java.time.LocalDateTime-">setFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the finish property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setGroup-java.lang.String-">setGroup</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the group property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Sets the value of the guid property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setHyperlink-java.lang.String-">setHyperlink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the hyperlink property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setHyperlinkAddress-java.lang.String-">setHyperlinkAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the hyperlinkAddress property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setHyperlinkSubAddress-java.lang.String-">setHyperlinkSubAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the hyperlinkSubAddress property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setID-java.math.BigInteger-">setID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the id property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setInitials-java.lang.String-">setInitials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the initials property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsBudget-java.lang.Boolean-">setIsBudget</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isBudget property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsCostResource-java.lang.Boolean-">setIsCostResource</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isCostResource property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsEnterprise-java.lang.Boolean-">setIsEnterprise</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isEnterprise property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsGeneric-java.lang.Boolean-">setIsGeneric</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isGeneric property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsInactive-java.lang.Boolean-">setIsInactive</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isInactive property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setIsNull-java.lang.Boolean-">setIsNull</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isNull property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setMaterialLabel-java.lang.String-">setMaterialLabel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the materialLabel property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setMaxUnits-java.math.BigDecimal-">setMaxUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the maxUnits property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the notes property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setNTAccount-java.lang.String-">setNTAccount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the ntAccount property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setOverAllocated-java.lang.Boolean-">setOverAllocated</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the overAllocated property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setOvertimeCost-java.math.BigDecimal-">setOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the overtimeCost property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setOvertimeRate-java.math.BigDecimal-">setOvertimeRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the overtimeRate property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setOvertimeRateFormat-java.math.BigInteger-">setOvertimeRateFormat</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the overtimeRateFormat property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setOvertimeWork-java.lang.String-">setOvertimeWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the overtimeWork property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setPeakUnits-java.math.BigDecimal-">setPeakUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the peakUnits property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setPercentWorkComplete-java.lang.Number-">setPercentWorkComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Sets the value of the percentWorkComplete property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setPhonetics-java.lang.String-">setPhonetics</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the phonetics property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRates-org.mpxj.mspdi.schema.Project.Resources.Resource.Rates-">setRates</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a>&nbsp;value)</code>
<div class="block">Sets the value of the rates property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRegularWork-java.lang.String-">setRegularWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the regularWork property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRemainingCost-java.math.BigDecimal-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRemainingOvertimeCost-java.math.BigDecimal-">setRemainingOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingOvertimeCost property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRemainingOvertimeWork-java.lang.String-">setRemainingOvertimeWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingOvertimeWork property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setRemainingWork-java.lang.String-">setRemainingWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingWork property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setStandardRate-java.math.BigDecimal-">setStandardRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the standardRate property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setStandardRateFormat-java.math.BigInteger-">setStandardRateFormat</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</code>
<div class="block">Sets the value of the standardRateFormat property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setStart-java.time.LocalDateTime-">setStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the start property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setSV-java.math.BigDecimal-">setSV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the sv property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setType-org.mpxj.ResourceType-">setType</a></span>(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</code>
<div class="block">Sets the value of the type property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setUID-java.lang.Integer-">setUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the uid property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setWork-java.lang.String-">setWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the work property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setWorkGroup-org.mpxj.WorkGroup-">setWorkGroup</a></span>(<a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;value)</code>
<div class="block">Sets the value of the workGroup property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html#setWorkVariance-java.math.BigDecimal-">setWorkVariance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the workVariance property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="uid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>uid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> uid</pre>
</li>
</ul>
<a name="guid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>guid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a> guid</pre>
</li>
</ul>
<a name="id">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> id</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="type">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a> type</pre>
</li>
</ul>
<a name="isNull">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNull</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isNull</pre>
</li>
</ul>
<a name="initials">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>initials</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> initials</pre>
</li>
</ul>
<a name="phonetics">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>phonetics</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> phonetics</pre>
</li>
</ul>
<a name="ntAccount">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ntAccount</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> ntAccount</pre>
</li>
</ul>
<a name="materialLabel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>materialLabel</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> materialLabel</pre>
</li>
</ul>
<a name="code">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>code</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> code</pre>
</li>
</ul>
<a name="group">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>group</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> group</pre>
</li>
</ul>
<a name="workGroup">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workGroup</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a> workGroup</pre>
</li>
</ul>
<a name="emailAddress">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emailAddress</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> emailAddress</pre>
</li>
</ul>
<a name="hyperlink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hyperlink</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> hyperlink</pre>
</li>
</ul>
<a name="hyperlinkAddress">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hyperlinkAddress</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> hyperlinkAddress</pre>
</li>
</ul>
<a name="hyperlinkSubAddress">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hyperlinkSubAddress</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> hyperlinkSubAddress</pre>
</li>
</ul>
<a name="maxUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maxUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> maxUnits</pre>
</li>
</ul>
<a name="peakUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>peakUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> peakUnits</pre>
</li>
</ul>
<a name="overAllocated">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overAllocated</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> overAllocated</pre>
</li>
</ul>
<a name="availableFrom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>availableFrom</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> availableFrom</pre>
</li>
</ul>
<a name="availableTo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>availableTo</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> availableTo</pre>
</li>
</ul>
<a name="start">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>start</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> start</pre>
</li>
</ul>
<a name="finish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> finish</pre>
</li>
</ul>
<a name="canLevel">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canLevel</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> canLevel</pre>
</li>
</ul>
<a name="accrueAt">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>accrueAt</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a> accrueAt</pre>
</li>
</ul>
<a name="work">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>work</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> work</pre>
</li>
</ul>
<a name="regularWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>regularWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> regularWork</pre>
</li>
</ul>
<a name="overtimeWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overtimeWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> overtimeWork</pre>
</li>
</ul>
<a name="actualWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> actualWork</pre>
</li>
</ul>
<a name="remainingWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> remainingWork</pre>
</li>
</ul>
<a name="actualOvertimeWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> actualOvertimeWork</pre>
</li>
</ul>
<a name="remainingOvertimeWork">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingOvertimeWork</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> remainingOvertimeWork</pre>
</li>
</ul>
<a name="percentWorkComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>percentWorkComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a> percentWorkComplete</pre>
</li>
</ul>
<a name="standardRate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>standardRate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> standardRate</pre>
</li>
</ul>
<a name="standardRateFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>standardRateFormat</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> standardRateFormat</pre>
</li>
</ul>
<a name="cost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> cost</pre>
</li>
</ul>
<a name="overtimeRate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overtimeRate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> overtimeRate</pre>
</li>
</ul>
<a name="overtimeRateFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overtimeRateFormat</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> overtimeRateFormat</pre>
</li>
</ul>
<a name="overtimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overtimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> overtimeCost</pre>
</li>
</ul>
<a name="costPerUse">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costPerUse</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> costPerUse</pre>
</li>
</ul>
<a name="actualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> actualCost</pre>
</li>
</ul>
<a name="actualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> actualOvertimeCost</pre>
</li>
</ul>
<a name="remainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> remainingCost</pre>
</li>
</ul>
<a name="remainingOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> remainingOvertimeCost</pre>
</li>
</ul>
<a name="workVariance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workVariance</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> workVariance</pre>
</li>
</ul>
<a name="costVariance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costVariance</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> costVariance</pre>
</li>
</ul>
<a name="sv">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sv</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> sv</pre>
</li>
</ul>
<a name="cv">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cv</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> cv</pre>
</li>
</ul>
<a name="acwp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>acwp</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> acwp</pre>
</li>
</ul>
<a name="calendarUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarUID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a> calendarUID</pre>
</li>
</ul>
<a name="notes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>notes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> notes</pre>
</li>
</ul>
<a name="bcws">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bcws</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> bcws</pre>
</li>
</ul>
<a name="bcwp">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bcwp</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> bcwp</pre>
</li>
</ul>
<a name="isGeneric">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGeneric</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isGeneric</pre>
</li>
</ul>
<a name="isInactive">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isInactive</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isInactive</pre>
</li>
</ul>
<a name="isEnterprise">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnterprise</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isEnterprise</pre>
</li>
</ul>
<a name="bookingType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bookingType</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a> bookingType</pre>
</li>
</ul>
<a name="actualWorkProtected">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualWorkProtected</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> actualWorkProtected</pre>
</li>
</ul>
<a name="actualOvertimeWorkProtected">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeWorkProtected</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> actualOvertimeWorkProtected</pre>
</li>
</ul>
<a name="activeDirectoryGUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activeDirectoryGUID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> activeDirectoryGUID</pre>
</li>
</ul>
<a name="creationDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> creationDate</pre>
</li>
</ul>
<a name="extendedAttribute">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>extendedAttribute</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a>&gt; extendedAttribute</pre>
</li>
</ul>
<a name="baseline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseline</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a>&gt; baseline</pre>
</li>
</ul>
<a name="outlineCode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outlineCode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a>&gt; outlineCode</pre>
</li>
</ul>
<a name="costCenter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costCenter</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> costCenter</pre>
</li>
</ul>
<a name="isCostResource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCostResource</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isCostResource</pre>
</li>
</ul>
<a name="assnOwner">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assnOwner</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> assnOwner</pre>
</li>
</ul>
<a name="assnOwnerGuid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assnOwnerGuid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> assnOwnerGuid</pre>
</li>
</ul>
<a name="isBudget">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBudget</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isBudget</pre>
</li>
</ul>
<a name="availabilityPeriods">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>availabilityPeriods</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a> availabilityPeriods</pre>
</li>
</ul>
<a name="rates">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rates</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a> rates</pre>
</li>
</ul>
<a name="timephasedData">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>timephasedData</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a>&gt; timephasedData</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Resource--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Resource</h4>
<pre>public&nbsp;Resource()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUID()</pre>
<div class="block">Gets the value of the uid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUID</h4>
<pre>public&nbsp;void&nbsp;setUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the uid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Gets the value of the guid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Sets the value of the guid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getID()</pre>
<div class="block">Gets the value of the id property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setID-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>public&nbsp;void&nbsp;setID(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the id property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;getType()</pre>
<div class="block">Gets the value of the type property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setType-org.mpxj.ResourceType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the type property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsNull--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsNull</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsNull()</pre>
<div class="block">Gets the value of the isNull property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsNull-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsNull</h4>
<pre>public&nbsp;void&nbsp;setIsNull(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isNull property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getInitials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInitials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getInitials()</pre>
<div class="block">Gets the value of the initials property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setInitials-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInitials</h4>
<pre>public&nbsp;void&nbsp;setInitials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the initials property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPhonetics--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhonetics</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPhonetics()</pre>
<div class="block">Gets the value of the phonetics property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPhonetics-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhonetics</h4>
<pre>public&nbsp;void&nbsp;setPhonetics(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the phonetics property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getNTAccount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNTAccount</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNTAccount()</pre>
<div class="block">Gets the value of the ntAccount property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNTAccount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNTAccount</h4>
<pre>public&nbsp;void&nbsp;setNTAccount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the ntAccount property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMaterialLabel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterialLabel</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMaterialLabel()</pre>
<div class="block">Gets the value of the materialLabel property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaterialLabel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaterialLabel</h4>
<pre>public&nbsp;void&nbsp;setMaterialLabel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the materialLabel property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCode()</pre>
<div class="block">Gets the value of the code property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCode</h4>
<pre>public&nbsp;void&nbsp;setCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the code property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroup</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getGroup()</pre>
<div class="block">Gets the value of the group property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroup</h4>
<pre>public&nbsp;void&nbsp;setGroup(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the group property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWorkGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkGroup</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;getWorkGroup()</pre>
<div class="block">Gets the value of the workGroup property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWorkGroup-org.mpxj.WorkGroup-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkGroup</h4>
<pre>public&nbsp;void&nbsp;setWorkGroup(<a href="../../../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;value)</pre>
<div class="block">Sets the value of the workGroup property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEmailAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmailAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEmailAddress()</pre>
<div class="block">Gets the value of the emailAddress property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEmailAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmailAddress</h4>
<pre>public&nbsp;void&nbsp;setEmailAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the emailAddress property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHyperlink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlink()</pre>
<div class="block">Gets the value of the hyperlink property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHyperlink-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlink</h4>
<pre>public&nbsp;void&nbsp;setHyperlink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hyperlink property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkAddress()</pre>
<div class="block">Gets the value of the hyperlinkAddress property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHyperlinkAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hyperlinkAddress property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHyperlinkSubAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkSubAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkSubAddress()</pre>
<div class="block">Gets the value of the hyperlinkSubAddress property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHyperlinkSubAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkSubAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkSubAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hyperlinkSubAddress property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMaxUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMaxUnits()</pre>
<div class="block">Gets the value of the maxUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaxUnits-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxUnits</h4>
<pre>public&nbsp;void&nbsp;setMaxUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the maxUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeakUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeakUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getPeakUnits()</pre>
<div class="block">Gets the value of the peakUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeakUnits-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeakUnits</h4>
<pre>public&nbsp;void&nbsp;setPeakUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the peakUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isOverAllocated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOverAllocated</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isOverAllocated()</pre>
<div class="block">Gets the value of the overAllocated property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOverAllocated-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverAllocated</h4>
<pre>public&nbsp;void&nbsp;setOverAllocated(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overAllocated property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAvailableFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableFrom</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getAvailableFrom()</pre>
<div class="block">Gets the value of the availableFrom property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAvailableFrom-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAvailableFrom</h4>
<pre>public&nbsp;void&nbsp;setAvailableFrom(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the availableFrom property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAvailableTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableTo</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getAvailableTo()</pre>
<div class="block">Gets the value of the availableTo property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAvailableTo-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAvailableTo</h4>
<pre>public&nbsp;void&nbsp;setAvailableTo(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the availableTo property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart()</pre>
<div class="block">Gets the value of the start property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the start property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish()</pre>
<div class="block">Gets the value of the finish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the finish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isCanLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCanLevel</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isCanLevel()</pre>
<div class="block">Gets the value of the canLevel property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCanLevel-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCanLevel</h4>
<pre>public&nbsp;void&nbsp;setCanLevel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the canLevel property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAccrueAt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccrueAt</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getAccrueAt()</pre>
<div class="block">Gets the value of the accrueAt property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAccrueAt-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAccrueAt</h4>
<pre>public&nbsp;void&nbsp;setAccrueAt(<a href="../../../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the accrueAt property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWork()</pre>
<div class="block">Gets the value of the work property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the work property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRegularWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegularWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRegularWork()</pre>
<div class="block">Gets the value of the regularWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRegularWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegularWork</h4>
<pre>public&nbsp;void&nbsp;setRegularWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the regularWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOvertimeWork()</pre>
<div class="block">Gets the value of the overtimeWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOvertimeWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setOvertimeWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overtimeWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActualWork()</pre>
<div class="block">Gets the value of the actualWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWork</h4>
<pre>public&nbsp;void&nbsp;setActualWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRemainingWork()</pre>
<div class="block">Gets the value of the remainingWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActualOvertimeWork()</pre>
<div class="block">Gets the value of the actualOvertimeWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRemainingOvertimeWork()</pre>
<div class="block">Gets the value of the remainingOvertimeWork property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeWork-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingOvertimeWork property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPercentWorkComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentWorkComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentWorkComplete()</pre>
<div class="block">Gets the value of the percentWorkComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPercentWorkComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentWorkComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentWorkComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Sets the value of the percentWorkComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStandardRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStandardRate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getStandardRate()</pre>
<div class="block">Gets the value of the standardRate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setStandardRate-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStandardRate</h4>
<pre>public&nbsp;void&nbsp;setStandardRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the standardRate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getStandardRateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStandardRateFormat</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getStandardRateFormat()</pre>
<div class="block">Gets the value of the standardRateFormat property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setStandardRateFormat-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStandardRateFormat</h4>
<pre>public&nbsp;void&nbsp;setStandardRateFormat(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the standardRateFormat property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getCost()</pre>
<div class="block">Gets the value of the cost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getOvertimeRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeRate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getOvertimeRate()</pre>
<div class="block">Gets the value of the overtimeRate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setOvertimeRate-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeRate</h4>
<pre>public&nbsp;void&nbsp;setOvertimeRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overtimeRate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getOvertimeRateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeRateFormat</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getOvertimeRateFormat()</pre>
<div class="block">Gets the value of the overtimeRateFormat property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setOvertimeRateFormat-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeRateFormat</h4>
<pre>public&nbsp;void&nbsp;setOvertimeRateFormat(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overtimeRateFormat property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getOvertimeCost()</pre>
<div class="block">Gets the value of the overtimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setOvertimeCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overtimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostPerUse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostPerUse</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getCostPerUse()</pre>
<div class="block">Gets the value of the costPerUse property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostPerUse-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostPerUse</h4>
<pre>public&nbsp;void&nbsp;setCostPerUse(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costPerUse property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getActualCost()</pre>
<div class="block">Gets the value of the actualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getRemainingCost()</pre>
<div class="block">Gets the value of the remainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getRemainingOvertimeCost()</pre>
<div class="block">Gets the value of the remainingOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeCost-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getWorkVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkVariance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getWorkVariance()</pre>
<div class="block">Gets the value of the workVariance property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWorkVariance-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkVariance</h4>
<pre>public&nbsp;void&nbsp;setWorkVariance(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the workVariance property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostVariance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getCostVariance()</pre>
<div class="block">Gets the value of the costVariance property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostVariance-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostVariance</h4>
<pre>public&nbsp;void&nbsp;setCostVariance(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costVariance property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getSV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getSV()</pre>
<div class="block">Gets the value of the sv property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSV-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSV</h4>
<pre>public&nbsp;void&nbsp;setSV(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the sv property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getCV()</pre>
<div class="block">Gets the value of the cv property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCV-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCV</h4>
<pre>public&nbsp;void&nbsp;setCV(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cv property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getACWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getACWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getACWP()</pre>
<div class="block">Gets the value of the acwp property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setACWP-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setACWP</h4>
<pre>public&nbsp;void&nbsp;setACWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the acwp property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;getCalendarUID()</pre>
<div class="block">Gets the value of the calendarUID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarUID-java.math.BigInteger-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarUID</h4>
<pre>public&nbsp;void&nbsp;setCalendarUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math">BigInteger</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarUID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigInteger.html?is-external=true" title="class or interface in java.math"><code>BigInteger</code></a></dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Gets the value of the notes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the notes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBCWS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getBCWS()</pre>
<div class="block">Gets the value of the bcws property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBCWS-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWS</h4>
<pre>public&nbsp;void&nbsp;setBCWS(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the bcws property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBCWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getBCWP()</pre>
<div class="block">Gets the value of the bcwp property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBCWP-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWP</h4>
<pre>public&nbsp;void&nbsp;setBCWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the bcwp property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsGeneric--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsGeneric</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsGeneric()</pre>
<div class="block">Gets the value of the isGeneric property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsGeneric-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsGeneric</h4>
<pre>public&nbsp;void&nbsp;setIsGeneric(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isGeneric property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsInactive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsInactive</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsInactive()</pre>
<div class="block">Gets the value of the isInactive property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsInactive-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsInactive</h4>
<pre>public&nbsp;void&nbsp;setIsInactive(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isInactive property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsEnterprise--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsEnterprise</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsEnterprise()</pre>
<div class="block">Gets the value of the isEnterprise property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsEnterprise-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsEnterprise</h4>
<pre>public&nbsp;void&nbsp;setIsEnterprise(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isEnterprise property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBookingType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBookingType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;getBookingType()</pre>
<div class="block">Gets the value of the bookingType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBookingType-org.mpxj.BookingType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBookingType</h4>
<pre>public&nbsp;void&nbsp;setBookingType(<a href="../../../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the bookingType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWorkProtected</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActualWorkProtected()</pre>
<div class="block">Gets the value of the actualWorkProtected property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualWorkProtected-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualWorkProtected(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualWorkProtected property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWorkProtected</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActualOvertimeWorkProtected()</pre>
<div class="block">Gets the value of the actualOvertimeWorkProtected property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWorkProtected-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWorkProtected(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeWorkProtected property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActiveDirectoryGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActiveDirectoryGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActiveDirectoryGUID()</pre>
<div class="block">Gets the value of the activeDirectoryGUID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActiveDirectoryGUID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActiveDirectoryGUID</h4>
<pre>public&nbsp;void&nbsp;setActiveDirectoryGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activeDirectoryGUID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreationDate()</pre>
<div class="block">Gets the value of the creationDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the creationDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getExtendedAttribute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtendedAttribute</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a>&gt;&nbsp;getExtendedAttribute()</pre>
<div class="block">Gets the value of the extendedAttribute property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the extendedAttribute property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getExtendedAttribute().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.ExtendedAttribute</code></a></div>
</li>
</ul>
<a name="getBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseline</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a>&gt;&nbsp;getBaseline()</pre>
<div class="block">Gets the value of the baseline property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the baseline property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getBaseline().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Baseline</code></a></div>
</li>
</ul>
<a name="getOutlineCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a>&gt;&nbsp;getOutlineCode()</pre>
<div class="block">Gets the value of the outlineCode property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the outlineCode property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getOutlineCode().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.OutlineCode</code></a></div>
</li>
</ul>
<a name="getCostCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostCenter</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCostCenter()</pre>
<div class="block">Gets the value of the costCenter property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostCenter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostCenter</h4>
<pre>public&nbsp;void&nbsp;setCostCenter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costCenter property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsCostResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsCostResource</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsCostResource()</pre>
<div class="block">Gets the value of the isCostResource property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsCostResource-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsCostResource</h4>
<pre>public&nbsp;void&nbsp;setIsCostResource(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isCostResource property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAssnOwner--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssnOwner</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAssnOwner()</pre>
<div class="block">Gets the value of the assnOwner property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssnOwner-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssnOwner</h4>
<pre>public&nbsp;void&nbsp;setAssnOwner(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assnOwner property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAssnOwnerGuid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssnOwnerGuid</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAssnOwnerGuid()</pre>
<div class="block">Gets the value of the assnOwnerGuid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssnOwnerGuid-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssnOwnerGuid</h4>
<pre>public&nbsp;void&nbsp;setAssnOwnerGuid(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assnOwnerGuid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsBudget--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsBudget</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsBudget()</pre>
<div class="block">Gets the value of the isBudget property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsBudget-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsBudget</h4>
<pre>public&nbsp;void&nbsp;setIsBudget(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isBudget property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAvailabilityPeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailabilityPeriods</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a>&nbsp;getAvailabilityPeriods()</pre>
<div class="block">Gets the value of the availabilityPeriods property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods</code></a></dd>
</dl>
</li>
</ul>
<a name="setAvailabilityPeriods-org.mpxj.mspdi.schema.Project.Resources.Resource.AvailabilityPeriods-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAvailabilityPeriods</h4>
<pre>public&nbsp;void&nbsp;setAvailabilityPeriods(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a>&nbsp;value)</pre>
<div class="block">Sets the value of the availabilityPeriods property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods</code></a></dd>
</dl>
</li>
</ul>
<a name="getRates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRates</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a>&nbsp;getRates()</pre>
<div class="block">Gets the value of the rates property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates</code></a></dd>
</dl>
</li>
</ul>
<a name="setRates-org.mpxj.mspdi.schema.Project.Resources.Resource.Rates-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRates</h4>
<pre>public&nbsp;void&nbsp;setRates(<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rates property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates</code></a></dd>
</dl>
</li>
</ul>
<a name="getTimephasedData--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTimephasedData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a>&gt;&nbsp;getTimephasedData()</pre>
<div class="block">Gets the value of the timephasedData property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the timephasedData property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getTimephasedData().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema"><code>TimephasedDataType</code></a></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.Resources.Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.Resources.Resource.html" target="_top">Frames</a></li>
<li><a href="Project.Resources.Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
