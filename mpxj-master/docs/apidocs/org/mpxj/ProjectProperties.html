<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectProperties (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectProperties (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10,"i265":10,"i266":10,"i267":10,"i268":10,"i269":10,"i270":10,"i271":10,"i272":10,"i273":10,"i274":10,"i275":10,"i276":10,"i277":10,"i278":10,"i279":10,"i280":10,"i281":10,"i282":10,"i283":10,"i284":10,"i285":10,"i286":10,"i287":10,"i288":10,"i289":10,"i290":10,"i291":10,"i292":10,"i293":10,"i294":10,"i295":10,"i296":10,"i297":10,"i298":10,"i299":10,"i300":10,"i301":10,"i302":10,"i303":10,"i304":10,"i305":10,"i306":10,"i307":10,"i308":10,"i309":10,"i310":10,"i311":10,"i312":10,"i313":10,"i314":10,"i315":10,"i316":10,"i317":10,"i318":10,"i319":10,"i320":10,"i321":10,"i322":10,"i323":10,"i324":10,"i325":10,"i326":10,"i327":10,"i328":10,"i329":10,"i330":10,"i331":10,"i332":10,"i333":10,"i334":10,"i335":10,"i336":10,"i337":10,"i338":10,"i339":10,"i340":10,"i341":10,"i342":10,"i343":10,"i344":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectProperties.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ProjectFileSharedData.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ProjectProperties.html" target="_top">Frames</a></li>
<li><a href="ProjectProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class ProjectProperties" class="title">Class ProjectProperties</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">org.mpxj.AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/ProjectProperties.html" title="class in org.mpxj">ProjectProperties</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.ProjectProperties</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>, <a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ProjectProperties</span>
extends <a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/ProjectProperties.html" title="class in org.mpxj">ProjectProperties</a>&gt;
implements <a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></pre>
<div class="block">This class represents a collection of properties relevant to the whole project.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#addProjectCodeValue-org.mpxj.ProjectCodeValue-">addProjectCodeValue</a></span>(<a href="../../org/mpxj/ProjectCodeValue.html" title="class in org.mpxj">ProjectCodeValue</a>&nbsp;value)</code>
<div class="block">Assign a project code value to this project.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActivityIdIncrement--">getActivityIdIncrement</a></span>()</code>
<div class="block">Retrieve the increment used when creating Activity ID values.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActivityIdIncrementBasedOnSelectedActivity--">getActivityIdIncrementBasedOnSelectedActivity</a></span>()</code>
<div class="block">Retrieve the "increment activity ID based on selected activity" flag.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActivityIdPrefix--">getActivityIdPrefix</a></span>()</code>
<div class="block">Retrieve the prefix used when creating an Activity ID.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActivityIdSuffix--">getActivityIdSuffix</a></span>()</code>
<div class="block">Retrieve the suffix used when creating an Activity ID.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Retrieves the actual project cost.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualDuration--">getActualDuration</a></span>()</code>
<div class="block">Retrieves the actual project duration.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualFinish--">getActualFinish</a></span>()</code>
<div class="block">Retrieves the actual project finish date.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualsInSync--">getActualsInSync</a></span>()</code>
<div class="block">Retrieve the actuals in sync flag.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualStart--">getActualStart</a></span>()</code>
<div class="block">Retrieves the actual project start date.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getActualWork--">getActualWork</a></span>()</code>
<div class="block">Retrieves the actual project work duration.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAdminProject--">getAdminProject</a></span>()</code>
<div class="block">Retrieve the admin project flag.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAMText--">getAMText</a></span>()</code>
<div class="block">Gets the AM text.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getApplicationVersion--">getApplicationVersion</a></span>()</code>
<div class="block">Retrieves the version of the application used to create this project.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAuthor--">getAuthor</a></span>()</code>
<div class="block">Retrieves the project author text.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAutoAddNewResourcesAndTasks--">getAutoAddNewResourcesAndTasks</a></span>()</code>
<div class="block">Retrieve the auto add new resources and tasks flag.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAutoFilter--">getAutoFilter</a></span>()</code>
<div class="block">Retrieve a flag indicating if auto filter is enabled.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getAutolink--">getAutolink</a></span>()</code>
<div class="block">Retrieves the autolink flag.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBarTextDateFormat--">getBarTextDateFormat</a></span>()</code>
<div class="block">Gets Bar Text Date Format.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineCalendarName--">getBaselineCalendarName</a></span>()</code>
<div class="block">Set the baseline calendar name.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">Retrieves the baseline project cost.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineDate--">getBaselineDate</a></span>()</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineDate-int-">getBaselineDate</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineDuration--">getBaselineDuration</a></span>()</code>
<div class="block">Retrieves the baseline duration value.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineFinish--">getBaselineFinish</a></span>()</code>
<div class="block">Retrieves the baseline project finish date.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineForEarnedValue--">getBaselineForEarnedValue</a></span>()</code>
<div class="block">Retrieve the number of the baseline to use for earned value
 calculations.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineProjectUniqueID--">getBaselineProjectUniqueID</a></span>()</code>
<div class="block">Retrieve the baseline project unique ID for this project.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineStart--">getBaselineStart</a></span>()</code>
<div class="block">Retrieves the baseline project start date.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineTypeName--">getBaselineTypeName</a></span>()</code>
<div class="block">Retrieve the name of the baseline type associated with this project.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineTypeUniqueID--">getBaselineTypeUniqueID</a></span>()</code>
<div class="block">Retrieve the unique ID of the baseline type associated with this project.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getBaselineWork--">getBaselineWork</a></span>()</code>
<div class="block">Retrieves the baseline project work duration.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCalculateFloatBasedOnFinishDateOfEachProject--">getCalculateFloatBasedOnFinishDateOfEachProject</a></span>()</code>
<div class="block">Set the calculate float based on finish date of each project flag.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCalculateMultipleFloatPaths--">getCalculateMultipleFloatPaths</a></span>()</code>
<div class="block">Get the calculate multiple float paths flag.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCalculateMultipleFloatPathsUsingTotalFloat--">getCalculateMultipleFloatPathsUsingTotalFloat</a></span>()</code>
<div class="block">Retrieve the calculate multiple float paths using total float flag.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCategory--">getCategory</a></span>()</code>
<div class="block">Retrieves the category text.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getComments--">getComments</a></span>()</code>
<div class="block">Returns any comments.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCompany--">getCompany</a></span>()</code>
<div class="block">Retrieves the company name.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getComputeStartToStartLagFromEarlyStart--">getComputeStartToStartLagFromEarlyStart</a></span>()</code>
<div class="block">Retrieve the compute start to start lag from early start flag.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getConsiderAssignmentsInOtherProjects--">getConsiderAssignmentsInOtherProjects</a></span>()</code>
<div class="block">Retrieve the consider assignments in other projects when leveling flag.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan--">getConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan</a></span>()</code>
<div class="block">Retrieve the priority of assignment in other projects to consider when leveling.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getContentStatus--">getContentStatus</a></span>()</code>
<div class="block">Retrieve the content status property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getContentType--">getContentType</a></span>()</code>
<div class="block">Retrieve the content type property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCost--">getCost</a></span>()</code>
<div class="block">Retrieves the project cost.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Retrieve the project creation date.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/CriticalActivityType.html" title="enum in org.mpxj">CriticalActivityType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCriticalActivityType--">getCriticalActivityType</a></span>()</code>
<div class="block">Retrieve the critical activity type for this project.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCriticalSlackLimit--">getCriticalSlackLimit</a></span>()</code>
<div class="block">Retrieve the critical slack limit.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCurrencyCode--">getCurrencyCode</a></span>()</code>
<div class="block">Retrieve the currency code for this project.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCurrencyDigits--">getCurrencyDigits</a></span>()</code>
<div class="block">Gets no of currency digits.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCurrencySymbol--">getCurrencySymbol</a></span>()</code>
<div class="block">Retrieves the currency symbol.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCurrentDate--">getCurrentDate</a></span>()</code>
<div class="block">Retrieves the current date.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getCustomProperties--">getCustomProperties</a></span>()</code>
<div class="block">Retrieve a map of custom document properties.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDataDateAndPlannedStartSetToProjectForecastStart--">getDataDateAndPlannedStartSetToProjectForecastStart</a></span>()</code>
<div class="block">Retrieve the data date and planned start set to project forecast start flag.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDateFormat--">getDateFormat</a></span>()</code>
<div class="block">Gets the set Date Format.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDateOrder--">getDateOrder</a></span>()</code>
<div class="block">Gets constant representing set Date order eg DMY, MDY.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDateSeparator--">getDateSeparator</a></span>()</code>
<div class="block">Gets the date separator.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDaysPerMonth--">getDaysPerMonth</a></span>()</code>
<div class="block">Retrieve the number of days per month.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDecimalSeparator--">getDecimalSeparator</a></span>()</code>
<div class="block">Gets the decimal separator.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultCalendar--">getDefaultCalendar</a></span>()</code>
<div class="block">Retrieve the default calendar for this project.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultCalendarUniqueID--">getDefaultCalendarUniqueID</a></span>()</code>
<div class="block">Retrieve the default calendar unique ID for this project.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultDurationIsFixed--">getDefaultDurationIsFixed</a></span>()</code>
<div class="block">Retrieves a flag indicating if the default duration type is fixed.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultDurationUnits--">getDefaultDurationUnits</a></span>()</code>
<div class="block">Gets Default Duration units.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultEndTime--">getDefaultEndTime</a></span>()</code>
<div class="block">Retrieves the default end time.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultFixedCostAccrual--">getDefaultFixedCostAccrual</a></span>()</code>
<div class="block">Retrieve the default fixed cost accrual type.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultOvertimeRate--">getDefaultOvertimeRate</a></span>()</code>
<div class="block">Get overtime rate.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultStandardRate--">getDefaultStandardRate</a></span>()</code>
<div class="block">Retrieves the default standard rate.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultStartTime--">getDefaultStartTime</a></span>()</code>
<div class="block">Retrieve the default start time, specified using the Java Date type.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultTaskEarnedValueMethod--">getDefaultTaskEarnedValueMethod</a></span>()</code>
<div class="block">Retrieves the default task earned value method.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultTaskType--">getDefaultTaskType</a></span>()</code>
<div class="block">Retrieve the default task type.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDefaultWorkUnits--">getDefaultWorkUnits</a></span>()</code>
<div class="block">Default work units.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDisplayMultipleFloatPathsEndingWithActivityUniqueID--">getDisplayMultipleFloatPathsEndingWithActivityUniqueID</a></span>()</code>
<div class="block">Retrieve the display multiple float paths ending with activity unique ID value.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDocumentVersion--">getDocumentVersion</a></span>()</code>
<div class="block">Retrieve the document version property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getDuration--">getDuration</a></span>()</code>
<div class="block">Retrieves the project duration.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getEarnedValueMethod--">getEarnedValueMethod</a></span>()</code>
<div class="block">Retrieve the earned value method.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getEditableActualCosts--">getEditableActualCosts</a></span>()</code>
<div class="block">Retrieve the editable actual costs flag.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getEditingTime--">getEditingTime</a></span>()</code>
<div class="block">Retrieve the editing time property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getEnablePublication--">getEnablePublication</a></span>()</code>
<div class="block">Retrieve the enable publication flag.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getEnableSummarization--">getEnableSummarization</a></span>()</code>
<div class="block">Retrieve the enable summarization flag.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getExportFlag--">getExportFlag</a></span>()</code>
<div class="block">Retrieves the export flag used to specify if the project was chosen to export from P6.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getExtendedCreationDate--">getExtendedCreationDate</a></span>()</code>
<div class="block">Retrieve the extended creation date.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFileApplication--">getFileApplication</a></span>()</code>
<div class="block">Retrieves the vendor of the file used to populate this ProjectFile instance.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFileType--">getFileType</a></span>()</code>
<div class="block">Retrieves the type of file used to populate this ProjectFile instance.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFinishDate--">getFinishDate</a></span>()</code>
<div class="block">Retrieves the project finish date.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFinishVariance--">getFinishVariance</a></span>()</code>
<div class="block">Retrieves the project finish variance duration.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFiscalYearStart--">getFiscalYearStart</a></span>()</code>
<div class="block">Retrieve the fiscal year start flag.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFiscalYearStartMonth--">getFiscalYearStartMonth</a></span>()</code>
<div class="block">Retrieves the fiscal year start month (January=1, December=12).</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getFullApplicationName--">getFullApplicationName</a></span>()</code>
<div class="block">Retrieves the name of the application used to create this project data.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Retrieve the GUID for this project.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getHonorConstraints--">getHonorConstraints</a></span>()</code>
<div class="block">Retrieves the honor constraints flag.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getHyperlinkBase--">getHyperlinkBase</a></span>()</code>
<div class="block">Gets the hyperlink base for this Project.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getIgnoreRelationshipsToAndFromOtherProjects--">getIgnoreRelationshipsToAndFromOtherProjects</a></span>()</code>
<div class="block">Retrieve the ignore relationships to and from other projects flag.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getInsertedProjectsLikeSummary--">getInsertedProjectsLikeSummary</a></span>()</code>
<div class="block">Retrieves the inserted projects like summary flag.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getKeywords--">getKeywords</a></span>()</code>
<div class="block">Retrieves the project keyword text.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLanguage--">getLanguage</a></span>()</code>
<div class="block">Retrieve the language property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLastAuthor--">getLastAuthor</a></span>()</code>
<div class="block">Retrieve the project user property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLastBaselineUpdateDate--">getLastBaselineUpdateDate</a></span>()</code>
<div class="block">Retrieve the last baseline update date.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLastPrinted--">getLastPrinted</a></span>()</code>
<div class="block">Retrieve the last printed property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLastSaved--">getLastSaved</a></span>()</code>
<div class="block">Retrieve the last saved date.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLevelAllResources--">getLevelAllResources</a></span>()</code>
<div class="block">Retrieve the level all resources flag.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLevelingPriorities--">getLevelingPriorities</a></span>()</code>
<div class="block">Retrieve the leveling priorities expression.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLevelResourcesOnlyWithinActivityTotalFloat--">getLevelResourcesOnlyWithinActivityTotalFloat</a></span>()</code>
<div class="block">Retrieve the level resources only within activity total float flag.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLimitNumberOfFloatPathsToCalculate--">getLimitNumberOfFloatPathsToCalculate</a></span>()</code>
<div class="block">Retrieve the limit number of paths to calculate flag.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLocation--">getLocation</a></span>()</code>
<div class="block">Retrieves the location.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getLocationUniqueID--">getLocationUniqueID</a></span>()</code>
<div class="block">Retrieves the location unique ID.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMakeOpenEndedActivitiesCritical--">getMakeOpenEndedActivitiesCritical</a></span>()</code>
<div class="block">Retrieve the mark open-ended activities as critical flag.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getManager--">getManager</a></span>()</code>
<div class="block">Retrieves the manager name.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMaximumNumberOfFloatPathsToCalculate--">getMaximumNumberOfFloatPathsToCalculate</a></span>()</code>
<div class="block">Retrieve the maximum number of float paths to calculate.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMaxPercentToOverallocateResources--">getMaxPercentToOverallocateResources</a></span>()</code>
<div class="block">Retrieve the maximum percentage to overallocate resources.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMicrosoftProjectServerURL--">getMicrosoftProjectServerURL</a></span>()</code>
<div class="block">Retrieves the Microsoft Project Server URL flag.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMinutesPerDay--">getMinutesPerDay</a></span>()</code>
<div class="block">Retrieve the number of minutes per day.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMinutesPerMonth--">getMinutesPerMonth</a></span>()</code>
<div class="block">Retrieve the default number of minutes per month.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMinutesPerWeek--">getMinutesPerWeek</a></span>()</code>
<div class="block">Retrieve the number of minutes per week.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMinutesPerYear--">getMinutesPerYear</a></span>()</code>
<div class="block">Retrieve the default number of minutes per year.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMoveCompletedEndsBack--">getMoveCompletedEndsBack</a></span>()</code>
<div class="block">Retrieve the move completed ends back flag.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMoveCompletedEndsForward--">getMoveCompletedEndsForward</a></span>()</code>
<div class="block">Retrieve the move completed ends forward flag.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMoveRemainingStartsBack--">getMoveRemainingStartsBack</a></span>()</code>
<div class="block">Retrieves the move remaining starts back flag.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMoveRemainingStartsForward--">getMoveRemainingStartsForward</a></span>()</code>
<div class="block">Retrieve the move remaining starts forward flag.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMppFileType--">getMppFileType</a></span>()</code>
<div class="block">This method retrieves a value representing the type of MPP file
 that has been read.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/CodePage.html" title="enum in org.mpxj">CodePage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMpxCodePage--">getMpxCodePage</a></span>()</code>
<div class="block">Retrieves the codepage.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMpxDelimiter--">getMpxDelimiter</a></span>()</code>
<div class="block">Retrieves the delimiter character, "," by default.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMpxFileVersion--">getMpxFileVersion</a></span>()</code>
<div class="block">Version of the MPX file.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMpxProgramName--">getMpxProgramName</a></span>()</code>
<div class="block">Program name file created by.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMultipleCriticalPaths--">getMultipleCriticalPaths</a></span>()</code>
<div class="block">Retrieve the multiple critical paths flag.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getMustFinishBy--">getMustFinishBy</a></span>()</code>
<div class="block">Retrieves the must finish by date for this project.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getName--">getName</a></span>()</code>
<div class="block">Retrieves the project name.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNewTasksAreManual--">getNewTasksAreManual</a></span>()</code>
<div class="block">Retrieve the flag indicating if new tasks task mode should default to
 manual (true) or automatic (false).</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNewTasksEffortDriven--">getNewTasksEffortDriven</a></span>()</code>
<div class="block">Retrieve the new tasks effort driven flag.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNewTasksEstimated--">getNewTasksEstimated</a></span>()</code>
<div class="block">Retrieve the new tasks estimated flag.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNewTaskStartIsProjectStart--">getNewTaskStartIsProjectStart</a></span>()</code>
<div class="block">Retrieve the flag indicating if new tasks should default to the
 project start date (true) or the current date (false).</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Retrieve the project notes.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getNotesObject--">getNotesObject</a></span>()</code>
<div class="block">Retrieve the project notes object.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getParentFile--">getParentFile</a></span>()</code>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPercentageComplete--">getPercentageComplete</a></span>()</code>
<div class="block">Retrieves the project percentage complete.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPlannedStart--">getPlannedStart</a></span>()</code>
<div class="block">Retrieves the planned start by date for this project.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPMText--">getPMText</a></span>()</code>
<div class="block">Gets the PM text.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPopulatedFields--">getPopulatedFields</a></span>()</code>
<div class="block">Retrieve the set of populated fields for this project.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPresentationFormat--">getPresentationFormat</a></span>()</code>
<div class="block">Retrieve the format property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPreserveMinimumFloatWhenLeveling--">getPreserveMinimumFloatWhenLeveling</a></span>()</code>
<div class="block">Retrieve the preserve minimum float when leveling value.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getPreserveScheduledEarlyAndLateDates--">getPreserveScheduledEarlyAndLateDates</a></span>()</code>
<div class="block">Retrieve the preserve scheduled early and late dates flag.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ProjectCode.html" title="class in org.mpxj">ProjectCode</a>,<a href="../../org/mpxj/ProjectCodeValue.html" title="class in org.mpxj">ProjectCodeValue</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectCodeValues--">getProjectCodeValues</a></span>()</code>
<div class="block">Retrieve the project code values associated with this project.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectExternallyEdited--">getProjectExternallyEdited</a></span>()</code>
<div class="block">Retrieve the externally edited flag.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectFilePath--">getProjectFilePath</a></span>()</code>
<div class="block">Gets the project file path.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectID--">getProjectID</a></span>()</code>
<div class="block">Retrieve the project ID for this project.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectIsBaseline--">getProjectIsBaseline</a></span>()</code>
<div class="block">Returns true if this ProjectFile instance represents a baseline.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectTitle--">getProjectTitle</a></span>()</code>
<div class="block">Gets the project title.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getProjectWebsiteUrl--">getProjectWebsiteUrl</a></span>()</code>
<div class="block">Retrieve the project website URL.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/RelationshipLagCalendar.html" title="enum in org.mpxj">RelationshipLagCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getRelationshipLagCalendar--">getRelationshipLagCalendar</a></span>()</code>
<div class="block">Retrieve the relationship lag calendar.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getRemoveFileProperties--">getRemoveFileProperties</a></span>()</code>
<div class="block">Retrieve the remove file properties flag.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getResourcePoolFile--">getResourcePoolFile</a></span>()</code>
<div class="block">Retrieve the resource pool file associated with this project.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getResourcePoolObject--">getResourcePoolObject</a></span>()</code>
<div class="block">Retrieve a ProjectFile instance representing the resource pool for this project
 Returns null if this project does not have a resource pool or the file cannot be read.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getRevision--">getRevision</a></span>()</code>
<div class="block">Retrieve the project revision number.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getScheduledFinish--">getScheduledFinish</a></span>()</code>
<div class="block">Retrieves the scheduled finish by date for this project.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getScheduleFrom--">getScheduleFrom</a></span>()</code>
<div class="block">Retrieves an enumerated value indicating if tasks in this project are
 scheduled from a start or a finish date.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/SchedulingProgressedActivities.html" title="enum in org.mpxj">SchedulingProgressedActivities</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSchedulingProgressedActivities--">getSchedulingProgressedActivities</a></span>()</code>
<div class="block">Retrieve the method used when scheduling progressed activities.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getShortApplicationName--">getShortApplicationName</a></span>()</code>
<div class="block">Retrieve the application property.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getShowProjectSummaryTask--">getShowProjectSummaryTask</a></span>()</code>
<div class="block">Retrieves the "show project summary task" flag.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSplitInProgressTasks--">getSplitInProgressTasks</a></span>()</code>
<div class="block">Flag representing whether to split in-progress tasks.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSpreadActualCost--">getSpreadActualCost</a></span>()</code>
<div class="block">Retrieve the spread actual cost flag.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSpreadPercentComplete--">getSpreadPercentComplete</a></span>()</code>
<div class="block">Retrieves the spread percent complete flag.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Retrieves the project start date.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getStartVariance--">getStartVariance</a></span>()</code>
<div class="block">Retrieves the start variance duration.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getStatusDate--">getStatusDate</a></span>()</code>
<div class="block">Retrieve the status date.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSubject--">getSubject</a></span>()</code>
<div class="block">Returns the project subject text.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getSymbolPosition--">getSymbolPosition</a></span>()</code>
<div class="block">Retrieves a constant representing the position of the currency symbol.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getTemplate--">getTemplate</a></span>()</code>
<div class="block">Retrieve the template property.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getThousandsSeparator--">getThousandsSeparator</a></span>()</code>
<div class="block">Gets the thousands separator.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getTimeFormat--">getTimeFormat</a></span>()</code>
<div class="block">Gets constant representing the Time Format.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>char</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getTimeSeparator--">getTimeSeparator</a></span>()</code>
<div class="block">Gets the time separator.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/TotalSlackCalculationType.html" title="enum in org.mpxj">TotalSlackCalculationType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getTotalSlackCalculationType--">getTotalSlackCalculationType</a></span>()</code>
<div class="block">Retrieve the total slack calculation type.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getUniqueID--">getUniqueID</a></span>()</code>
<div class="block">Retrieve the unique ID for this project.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getUpdatingTaskStatusUpdatesResourceStatus--">getUpdatingTaskStatusUpdatesResourceStatus</a></span>()</code>
<div class="block">Flags whether updating Task status also updates resource status.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getUseExpectedFinishDates--">getUseExpectedFinishDates</a></span>()</code>
<div class="block">Retrieve the use expected finish dates flag.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getWbsCodeSeparator--">getWbsCodeSeparator</a></span>()</code>
<div class="block">Retrieve the WBS Code separator character.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/DayOfWeek.html?is-external=true" title="class or interface in java.time">DayOfWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getWeekStartDay--">getWeekStartDay</a></span>()</code>
<div class="block">Retrieve the week start day.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getWork--">getWork</a></span>()</code>
<div class="block">Retrieves the project work duration.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#getWork2--">getWork2</a></span>()</code>
<div class="block">Retrieves the project's "Work 2" attribute.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActivityIdIncrement-java.lang.Integer-">setActivityIdIncrement</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the increment used when creating Activity ID values.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActivityIdIncrementBasedOnSelectedActivity-boolean-">setActivityIdIncrementBasedOnSelectedActivity</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the "increment activity ID based on selected activity" flag.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActivityIdPrefix-java.lang.String-">setActivityIdPrefix</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the prefix used when creating an Activity ID.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActivityIdSuffix-java.lang.Integer-">setActivityIdSuffix</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the suffix used when creating an Activity ID.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualCost-java.lang.Number-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</code>
<div class="block">Sets the actual project cost.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualDuration-org.mpxj.Duration-">setActualDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualDuration)</code>
<div class="block">Sets the actual project duration.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualFinish-java.time.LocalDateTime-">setActualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;actualFinishDate)</code>
<div class="block">Sets the actual project finish date.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualsInSync-boolean-">setActualsInSync</a></span>(boolean&nbsp;actualsInSync)</code>
<div class="block">Set the actuals in sync flag.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualStart-java.time.LocalDateTime-">setActualStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;actualStartDate)</code>
<div class="block">Sets the actual project start date.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setActualWork-org.mpxj.Duration-">setActualWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualWork)</code>
<div class="block">Sets the actual project work duration.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAdminProject-boolean-">setAdminProject</a></span>(boolean&nbsp;adminProject)</code>
<div class="block">Set the admin project flag.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAMText-java.lang.String-">setAMText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;amText)</code>
<div class="block">Sets the AM text.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setApplicationVersion-java.lang.Integer-">setApplicationVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;version)</code>
<div class="block">Sets the version of the application used to create this project.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAuthor-java.lang.String-">setAuthor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;author)</code>
<div class="block">Sets the project author text.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAutoAddNewResourcesAndTasks-boolean-">setAutoAddNewResourcesAndTasks</a></span>(boolean&nbsp;autoAddNewResourcesAndTasks)</code>
<div class="block">Set the auto add new resources and tasks flag.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAutoFilter-boolean-">setAutoFilter</a></span>(boolean&nbsp;autoFilter)</code>
<div class="block">Sets a flag indicating if auto filter is enabled.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setAutolink-boolean-">setAutolink</a></span>(boolean&nbsp;autolink)</code>
<div class="block">Sets the autolink flag.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBarTextDateFormat-org.mpxj.ProjectDateFormat-">setBarTextDateFormat</a></span>(<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;dateFormat)</code>
<div class="block">Sets Bar Text Date Format.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineCalendarName-java.lang.String-">setBaselineCalendarName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Retrieve the baseline calendar name.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineCost-java.lang.Number-">setBaselineCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;baselineCost)</code>
<div class="block">Sets the baseline project cost.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineDate-int-java.time.LocalDateTime-">setBaselineDate</a></span>(int&nbsp;baselineNumber,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineDate-java.time.LocalDateTime-">setBaselineDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineDuration-org.mpxj.Duration-">setBaselineDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;baselineDuration)</code>
<div class="block">Sets the baseline project duration value.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineFinish-java.time.LocalDateTime-">setBaselineFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;baselineFinishDate)</code>
<div class="block">Sets the baseline project finish date.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineForEarnedValue-java.lang.Integer-">setBaselineForEarnedValue</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;baselineForEarnedValue)</code>
<div class="block">Set the number of the baseline to use for earned value
 calculations.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineProjectUniqueID-java.lang.Integer-">setBaselineProjectUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Set the baseline project unique ID for this project.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineStart-java.time.LocalDateTime-">setBaselineStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;baselineStartDate)</code>
<div class="block">Sets the baseline project start date.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineTypeName-java.lang.String-">setBaselineTypeName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the name of the baseline type associated with this project.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineTypeUniqueID-java.lang.Integer-">setBaselineTypeUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the unique ID of the baseline type associated with this project.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setBaselineWork-org.mpxj.Duration-">setBaselineWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;baselineWork)</code>
<div class="block">Set the baseline project work duration.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCalculateFloatBasedOnFinishDateOfEachProject-boolean-">setCalculateFloatBasedOnFinishDateOfEachProject</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the calculate float based on finish date of each project flag.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCalculateMultipleFloatPaths-boolean-">setCalculateMultipleFloatPaths</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the calculate multiple float paths flag.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCalculateMultipleFloatPathsUsingTotalFloat-boolean-">setCalculateMultipleFloatPathsUsingTotalFloat</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the calculate multiple float paths using total float flag.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCategory-java.lang.String-">setCategory</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</code>
<div class="block">Sets the category text.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setComments-java.lang.String-">setComments</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;comments)</code>
<div class="block">Set comment text.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCompany-java.lang.String-">setCompany</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;company)</code>
<div class="block">Sets the company name.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setComputeStartToStartLagFromEarlyStart-boolean-">setComputeStartToStartLagFromEarlyStart</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the compute start to start lag from early start flag.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setConsiderAssignmentsInOtherProjects-boolean-">setConsiderAssignmentsInOtherProjects</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the consider assignments in other projects when leveling flag.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan-java.lang.Integer-">setConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the priority of assignment in other projects to consider when leveling.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setContentStatus-java.lang.String-">setContentStatus</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contentStatus)</code>
<div class="block">Set the content status property.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setContentType-java.lang.String-">setContentType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contentType)</code>
<div class="block">Set the content type property.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCost-java.lang.Number-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the project cost.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCreationDate-java.time.LocalDateTime-">setCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</code>
<div class="block">Set the project creation date.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCriticalActivityType-org.mpxj.CriticalActivityType-">setCriticalActivityType</a></span>(<a href="../../org/mpxj/CriticalActivityType.html" title="enum in org.mpxj">CriticalActivityType</a>&nbsp;value)</code>
<div class="block">Set the critical activity type for this project.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCriticalSlackLimit-org.mpxj.Duration-">setCriticalSlackLimit</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;criticalSlackLimit)</code>
<div class="block">Set the critical slack limit.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCurrencyCode-java.lang.String-">setCurrencyCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;currencyCode)</code>
<div class="block">Set the currency code for this project.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCurrencyDigits-java.lang.Integer-">setCurrencyDigits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;currDigs)</code>
<div class="block">Sets no of currency digits.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCurrencySymbol-java.lang.String-">setCurrencySymbol</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;symbol)</code>
<div class="block">Sets currency symbol.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCurrentDate-java.time.LocalDateTime-">setCurrentDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;currentDate)</code>
<div class="block">Sets the current date.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setCustomProperties-java.util.Map-">setCustomProperties</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;customProperties)</code>
<div class="block">Sets a map of custom document properties.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDataDateAndPlannedStartSetToProjectForecastStart-boolean-">setDataDateAndPlannedStartSetToProjectForecastStart</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the data date and planned start set to project forecast start flag.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDateFormat-org.mpxj.ProjectDateFormat-">setDateFormat</a></span>(<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;dateFormat)</code>
<div class="block">Sets the set Date Format.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDateOrder-org.mpxj.DateOrder-">setDateOrder</a></span>(<a href="../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a>&nbsp;dateOrder)</code>
<div class="block">Sets constant representing set Date order eg DMY, MDY.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDateSeparator-char-">setDateSeparator</a></span>(char&nbsp;dateSeparator)</code>
<div class="block">Sets the date separator.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDaysPerMonth-java.lang.Integer-">setDaysPerMonth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;daysPerMonth)</code>
<div class="block">Set the number of days per month.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDecimalSeparator-char-">setDecimalSeparator</a></span>(char&nbsp;decSep)</code>
<div class="block">Sets the decimal separator.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultCalendar-org.mpxj.ProjectCalendar-">setDefaultCalendar</a></span>(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">Set the default calendar for this project.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultCalendarUniqueID-java.lang.Integer-">setDefaultCalendarUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the default calendar unique ID for this project.</div>
</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultDurationIsFixed-boolean-">setDefaultDurationIsFixed</a></span>(boolean&nbsp;fixed)</code>
<div class="block">Sets a flag indicating if the default duration type is fixed.</div>
</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultDurationUnits-org.mpxj.TimeUnit-">setDefaultDurationUnits</a></span>(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;units)</code>
<div class="block">Default duration units.</div>
</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultEndTime-java.time.LocalTime-">setDefaultEndTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;date)</code>
<div class="block">Sets the default end time.</div>
</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultFixedCostAccrual-org.mpxj.AccrueType-">setDefaultFixedCostAccrual</a></span>(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;defaultFixedCostAccrual)</code>
<div class="block">Sets the default fixed cost accrual type.</div>
</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultOvertimeRate-org.mpxj.Rate-">setDefaultOvertimeRate</a></span>(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</code>
<div class="block">Set default overtime rate.</div>
</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultStandardRate-org.mpxj.Rate-">setDefaultStandardRate</a></span>(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</code>
<div class="block">Sets the default standard rate.</div>
</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultStartTime-java.time.LocalTime-">setDefaultStartTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;defaultStartTime)</code>
<div class="block">Set the default start time, specified using the Java Date type.</div>
</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultTaskEarnedValueMethod-org.mpxj.EarnedValueMethod-">setDefaultTaskEarnedValueMethod</a></span>(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;defaultTaskEarnedValueMethod)</code>
<div class="block">Sets the default task earned value method.</div>
</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultTaskType-org.mpxj.TaskType-">setDefaultTaskType</a></span>(<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;defaultTaskType)</code>
<div class="block">Set the default task type.</div>
</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDefaultWorkUnits-org.mpxj.TimeUnit-">setDefaultWorkUnits</a></span>(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;units)</code>
<div class="block">Default work units.</div>
</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDisplayMultipleFloatPathsEndingWithActivityUniqueID-java.lang.Integer-">setDisplayMultipleFloatPathsEndingWithActivityUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the display multiple float paths ending with activity unique ID value.</div>
</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDocumentVersion-java.lang.String-">setDocumentVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;documentVersion)</code>
<div class="block">Set the document version property.</div>
</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setDuration-org.mpxj.Duration-">setDuration</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Sets the project duration.</div>
</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setEarnedValueMethod-org.mpxj.EarnedValueMethod-">setEarnedValueMethod</a></span>(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;earnedValueMethod)</code>
<div class="block">Set the earned value method.</div>
</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setEditableActualCosts-boolean-">setEditableActualCosts</a></span>(boolean&nbsp;editableActualCosts)</code>
<div class="block">Set the editable actual costs flag.</div>
</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setEditingTime-java.lang.Integer-">setEditingTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;editingTime)</code>
<div class="block">Set the editing time property.</div>
</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setEnablePublication-boolean-">setEnablePublication</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the enable publication flag.</div>
</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setEnableSummarization-boolean-">setEnableSummarization</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the enable summarization flg.</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setExportFlag-boolean-">setExportFlag</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the export flag to populate this ProjectFile instance.</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setExtendedCreationDate-java.time.LocalDateTime-">setExtendedCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</code>
<div class="block">Set the extended creation date.</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFileApplication-java.lang.String-">setFileApplication</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Sets the vendor of file used to populate this ProjectFile instance.</div>
</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFileType-java.lang.String-">setFileType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Sets the type of file used to populate this ProjectFile instance.</div>
</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFinishDate-java.time.LocalDateTime-">setFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finishDate)</code>
<div class="block">Sets the project finish date.</div>
</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFinishVariance-org.mpxj.Duration-">setFinishVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;finishVariance)</code>
<div class="block">Sets the project finish variance duration.</div>
</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFiscalYearStart-boolean-">setFiscalYearStart</a></span>(boolean&nbsp;fiscalYearStart)</code>
<div class="block">Set the fiscal year start flag.</div>
</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFiscalYearStartMonth-java.lang.Integer-">setFiscalYearStartMonth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;fiscalYearStartMonth)</code>
<div class="block">Sets the fiscal year start month (January=1, December=12).</div>
</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setFullApplicationName-java.lang.String-">setFullApplicationName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Sets the name of the application used to create this project data.</div>
</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</code>
<div class="block">Set the GUID for this project.</div>
</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setHonorConstraints-boolean-">setHonorConstraints</a></span>(boolean&nbsp;honorConstraints)</code>
<div class="block">Sets the honor constraints flag.</div>
</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setHyperlinkBase-java.lang.String-">setHyperlinkBase</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkBase)</code>
<div class="block">Sets the hyperlink base for this Project.</div>
</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setIgnoreRelationshipsToAndFromOtherProjects-boolean-">setIgnoreRelationshipsToAndFromOtherProjects</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the ignore relationships to and from other projects flag.</div>
</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setInsertedProjectsLikeSummary-boolean-">setInsertedProjectsLikeSummary</a></span>(boolean&nbsp;insertedProjectsLikeSummary)</code>
<div class="block">Sets the inserted projects like summary flag.</div>
</td>
</tr>
<tr id="i265" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setKeywords-java.lang.String-">setKeywords</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keywords)</code>
<div class="block">Sets the project keyword text.</div>
</td>
</tr>
<tr id="i266" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLanguage-java.lang.String-">setLanguage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language)</code>
<div class="block">Set the language property.</div>
</td>
</tr>
<tr id="i267" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLastAuthor-java.lang.String-">setLastAuthor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectUser)</code>
<div class="block">Set the project user property.</div>
</td>
</tr>
<tr id="i268" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLastBaselineUpdateDate-java.time.LocalDateTime-">setLastBaselineUpdateDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set the last baseline update date.</div>
</td>
</tr>
<tr id="i269" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLastPrinted-java.time.LocalDateTime-">setLastPrinted</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;lastPrinted)</code>
<div class="block">Set the last printed property.</div>
</td>
</tr>
<tr id="i270" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLastSaved-java.time.LocalDateTime-">setLastSaved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;lastSaved)</code>
<div class="block">Set the last saved date.</div>
</td>
</tr>
<tr id="i271" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLevelAllResources-boolean-">setLevelAllResources</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the level all resources flag.</div>
</td>
</tr>
<tr id="i272" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLevelingPriorities-java.lang.String-">setLevelingPriorities</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the leveling priorities expression.</div>
</td>
</tr>
<tr id="i273" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLevelResourcesOnlyWithinActivityTotalFloat-boolean-">setLevelResourcesOnlyWithinActivityTotalFloat</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the level resources only within activity total float flag.</div>
</td>
</tr>
<tr id="i274" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLimitNumberOfFloatPathsToCalculate-boolean-">setLimitNumberOfFloatPathsToCalculate</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the limit number of paths to calculate flag.</div>
</td>
</tr>
<tr id="i275" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLocation-org.mpxj.Location-">setLocation</a></span>(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</code>
<div class="block">Sets the location.</div>
</td>
</tr>
<tr id="i276" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setLocationUniqueID-java.lang.Integer-">setLocationUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the location unique ID.</div>
</td>
</tr>
<tr id="i277" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMakeOpenEndedActivitiesCritical-boolean-">setMakeOpenEndedActivitiesCritical</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the mark open-ended activities as critical flag.</div>
</td>
</tr>
<tr id="i278" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setManager-java.lang.String-">setManager</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;manager)</code>
<div class="block">Sets the manager name.</div>
</td>
</tr>
<tr id="i279" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMaximumNumberOfFloatPathsToCalculate-java.lang.Integer-">setMaximumNumberOfFloatPathsToCalculate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Set the maximum number of float paths to calculate.</div>
</td>
</tr>
<tr id="i280" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMaxPercentToOverallocateResources-java.lang.Number-">setMaxPercentToOverallocateResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the maximum percentage to overallocate resources.</div>
</td>
</tr>
<tr id="i281" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMicrosoftProjectServerURL-boolean-">setMicrosoftProjectServerURL</a></span>(boolean&nbsp;microsoftProjectServerURL)</code>
<div class="block">Sets the Microsoft Project Server URL flag.</div>
</td>
</tr>
<tr id="i282" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMinutesPerDay-java.lang.Integer-">setMinutesPerDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerDay)</code>
<div class="block">Set the number of minutes per day.</div>
</td>
</tr>
<tr id="i283" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMinutesPerMonth-java.lang.Integer-">setMinutesPerMonth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerMonth)</code>
<div class="block">Set the default number of minutes per month.</div>
</td>
</tr>
<tr id="i284" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMinutesPerWeek-java.lang.Integer-">setMinutesPerWeek</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerWeek)</code>
<div class="block">Set the number of minutes per week.</div>
</td>
</tr>
<tr id="i285" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMinutesPerYear-java.lang.Integer-">setMinutesPerYear</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerYear)</code>
<div class="block">Set the default number of minutes per year.</div>
</td>
</tr>
<tr id="i286" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMoveCompletedEndsBack-boolean-">setMoveCompletedEndsBack</a></span>(boolean&nbsp;moveCompletedEndsBack)</code>
<div class="block">Set the move completed ends back flag.</div>
</td>
</tr>
<tr id="i287" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMoveCompletedEndsForward-boolean-">setMoveCompletedEndsForward</a></span>(boolean&nbsp;moveCompletedEndsForward)</code>
<div class="block">Sets the move completed ends forward flag.</div>
</td>
</tr>
<tr id="i288" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMoveRemainingStartsBack-boolean-">setMoveRemainingStartsBack</a></span>(boolean&nbsp;moveRemainingStartsBack)</code>
<div class="block">Sets the move remaining starts back flag.</div>
</td>
</tr>
<tr id="i289" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMoveRemainingStartsForward-boolean-">setMoveRemainingStartsForward</a></span>(boolean&nbsp;moveRemainingStartsForward)</code>
<div class="block">Set the move remaining starts forward flag.</div>
</td>
</tr>
<tr id="i290" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMppFileType-java.lang.Integer-">setMppFileType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;fileType)</code>
<div class="block">Used internally to set the file type.</div>
</td>
</tr>
<tr id="i291" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMpxCodePage-org.mpxj.CodePage-">setMpxCodePage</a></span>(<a href="../../org/mpxj/CodePage.html" title="enum in org.mpxj">CodePage</a>&nbsp;codePage)</code>
<div class="block">Sets the codepage.</div>
</td>
</tr>
<tr id="i292" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMpxDelimiter-char-">setMpxDelimiter</a></span>(char&nbsp;delimiter)</code>
<div class="block">Sets the delimiter character, "," by default.</div>
</td>
</tr>
<tr id="i293" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMpxFileVersion-org.mpxj.FileVersion-">setMpxFileVersion</a></span>(<a href="../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a>&nbsp;version)</code>
<div class="block">Version of the MPX file.</div>
</td>
</tr>
<tr id="i294" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMpxProgramName-java.lang.String-">setMpxProgramName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;programName)</code>
<div class="block">Program name file created by.</div>
</td>
</tr>
<tr id="i295" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMultipleCriticalPaths-boolean-">setMultipleCriticalPaths</a></span>(boolean&nbsp;multipleCriticalPaths)</code>
<div class="block">Set the multiple critical paths flag.</div>
</td>
</tr>
<tr id="i296" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setMustFinishBy-java.time.LocalDateTime-">setMustFinishBy</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Sets the must finish by date for this project.</div>
</td>
</tr>
<tr id="i297" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Sets the project name.</div>
</td>
</tr>
<tr id="i298" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNewTasksAreManual-boolean-">setNewTasksAreManual</a></span>(boolean&nbsp;newTasksAreManual)</code>
<div class="block">Set the flag indicating if new tasks task mode should default to
 manual (true) or automatic (false).</div>
</td>
</tr>
<tr id="i299" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNewTasksEffortDriven-boolean-">setNewTasksEffortDriven</a></span>(boolean&nbsp;newTasksEffortDriven)</code>
<div class="block">Sets the new tasks effort driven flag.</div>
</td>
</tr>
<tr id="i300" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNewTasksEstimated-boolean-">setNewTasksEstimated</a></span>(boolean&nbsp;newTasksEstimated)</code>
<div class="block">Set the new tasks estimated flag.</div>
</td>
</tr>
<tr id="i301" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNewTaskStartIsProjectStart-boolean-">setNewTaskStartIsProjectStart</a></span>(boolean&nbsp;newTaskStartIsProjectStart)</code>
<div class="block">Sets the flag indicating if new tasks should default to the
 project start date (true) or the current date (false).</div>
</td>
</tr>
<tr id="i302" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</code>
<div class="block">Set the project notes.</div>
</td>
</tr>
<tr id="i303" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setNotesObject-org.mpxj.Notes-">setNotesObject</a></span>(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</code>
<div class="block">Set the project notes object.</div>
</td>
</tr>
<tr id="i304" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPercentageComplete-java.lang.Number-">setPercentageComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;percentComplete)</code>
<div class="block">Sets project percentage complete.</div>
</td>
</tr>
<tr id="i305" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPlannedStart-java.time.LocalDateTime-">setPlannedStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Sets the planned start by date for this project.</div>
</td>
</tr>
<tr id="i306" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPMText-java.lang.String-">setPMText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pmText)</code>
<div class="block">Sets the PM text.</div>
</td>
</tr>
<tr id="i307" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPresentationFormat-java.lang.String-">setPresentationFormat</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;format)</code>
<div class="block">Set the format property.</div>
</td>
</tr>
<tr id="i308" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPreserveMinimumFloatWhenLeveling-org.mpxj.Duration-">setPreserveMinimumFloatWhenLeveling</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the preserve minimum float when leveling value.</div>
</td>
</tr>
<tr id="i309" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setPreserveScheduledEarlyAndLateDates-boolean-">setPreserveScheduledEarlyAndLateDates</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the preserve scheduled early and late dates flag.</div>
</td>
</tr>
<tr id="i310" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectExternallyEdited-boolean-">setProjectExternallyEdited</a></span>(boolean&nbsp;projectExternallyEdited)</code>
<div class="block">Set the externally edited flag.</div>
</td>
</tr>
<tr id="i311" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectFilePath-java.lang.String-">setProjectFilePath</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectFilePath)</code>
<div class="block">Sets the project file path.</div>
</td>
</tr>
<tr id="i312" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectID-java.lang.String-">setProjectID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">Set the project ID for this project.</div>
</td>
</tr>
<tr id="i313" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectIsBaseline-boolean-">setProjectIsBaseline</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the flag to indicate if this ProjectFile instance
 represents a baseline.</div>
</td>
</tr>
<tr id="i314" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectTitle-java.lang.String-">setProjectTitle</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectTitle)</code>
<div class="block">Sets the project title.</div>
</td>
</tr>
<tr id="i315" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setProjectWebsiteUrl-java.lang.String-">setProjectWebsiteUrl</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the project website URL.</div>
</td>
</tr>
<tr id="i316" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setRelationshipLagCalendar-org.mpxj.RelationshipLagCalendar-">setRelationshipLagCalendar</a></span>(<a href="../../org/mpxj/RelationshipLagCalendar.html" title="enum in org.mpxj">RelationshipLagCalendar</a>&nbsp;calendar)</code>
<div class="block">Set the relationship lag calendar.</div>
</td>
</tr>
<tr id="i317" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setRemoveFileProperties-boolean-">setRemoveFileProperties</a></span>(boolean&nbsp;removeFileProperties)</code>
<div class="block">Set the remove file properties flag.</div>
</td>
</tr>
<tr id="i318" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setResourcePoolFile-java.lang.String-">setResourcePoolFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;file)</code>
<div class="block">Set the resource pool file associated with this project.</div>
</td>
</tr>
<tr id="i319" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setRevision-java.lang.Integer-">setRevision</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;revision)</code>
<div class="block">Set the project revision number.</div>
</td>
</tr>
<tr id="i320" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setScheduledFinish-java.time.LocalDateTime-">setScheduledFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Sets the scheduled finish by date for this project.</div>
</td>
</tr>
<tr id="i321" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setScheduleFrom-org.mpxj.ScheduleFrom-">setScheduleFrom</a></span>(<a href="../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a>&nbsp;scheduleFrom)</code>
<div class="block">Sets an enumerated value indicating if tasks in this project are
 scheduled from a start or a finish date.</div>
</td>
</tr>
<tr id="i322" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSchedulingProgressedActivities-org.mpxj.SchedulingProgressedActivities-">setSchedulingProgressedActivities</a></span>(<a href="../../org/mpxj/SchedulingProgressedActivities.html" title="enum in org.mpxj">SchedulingProgressedActivities</a>&nbsp;value)</code>
<div class="block">Set the method used when scheduling progressed activities.</div>
</td>
</tr>
<tr id="i323" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setShortApplicationName-java.lang.String-">setShortApplicationName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;application)</code>
<div class="block">Set the application property.</div>
</td>
</tr>
<tr id="i324" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setShowProjectSummaryTask-boolean-">setShowProjectSummaryTask</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the "show project summary task" flag.</div>
</td>
</tr>
<tr id="i325" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSplitInProgressTasks-boolean-">setSplitInProgressTasks</a></span>(boolean&nbsp;flag)</code>
<div class="block">Flag representing whether to split in-progress tasks.</div>
</td>
</tr>
<tr id="i326" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSpreadActualCost-boolean-">setSpreadActualCost</a></span>(boolean&nbsp;spreadActualCost)</code>
<div class="block">Set the spread actual cost flag.</div>
</td>
</tr>
<tr id="i327" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSpreadPercentComplete-boolean-">setSpreadPercentComplete</a></span>(boolean&nbsp;spreadPercentComplete)</code>
<div class="block">Sets the spread percent complete flag.</div>
</td>
</tr>
<tr id="i328" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate)</code>
<div class="block">Sets the project start date.</div>
</td>
</tr>
<tr id="i329" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setStartVariance-org.mpxj.Duration-">setStartVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;startVariance)</code>
<div class="block">Sets the start variance duration.</div>
</td>
</tr>
<tr id="i330" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setStatusDate-java.time.LocalDateTime-">setStatusDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;statusDate)</code>
<div class="block">Set the status date.</div>
</td>
</tr>
<tr id="i331" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSubject-java.lang.String-">setSubject</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject)</code>
<div class="block">Sets the project subject text.</div>
</td>
</tr>
<tr id="i332" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setSymbolPosition-org.mpxj.CurrencySymbolPosition-">setSymbolPosition</a></span>(<a href="../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;value)</code>
<div class="block">Sets the position of the currency symbol.</div>
</td>
</tr>
<tr id="i333" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setTemplate-java.lang.String-">setTemplate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;template)</code>
<div class="block">Set the template property.</div>
</td>
</tr>
<tr id="i334" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setThousandsSeparator-char-">setThousandsSeparator</a></span>(char&nbsp;sep)</code>
<div class="block">Sets the thousands separator.</div>
</td>
</tr>
<tr id="i335" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setTimeFormat-org.mpxj.ProjectTimeFormat-">setTimeFormat</a></span>(<a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a>&nbsp;timeFormat)</code>
<div class="block">Sets constant representing the time format.</div>
</td>
</tr>
<tr id="i336" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setTimeSeparator-char-">setTimeSeparator</a></span>(char&nbsp;timeSeparator)</code>
<div class="block">Sets the time separator.</div>
</td>
</tr>
<tr id="i337" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setTotalSlackCalculationType-org.mpxj.TotalSlackCalculationType-">setTotalSlackCalculationType</a></span>(<a href="../../org/mpxj/TotalSlackCalculationType.html" title="enum in org.mpxj">TotalSlackCalculationType</a>&nbsp;type)</code>
<div class="block">Set the total slack calculation type.</div>
</td>
</tr>
<tr id="i338" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setUniqueID-java.lang.Integer-">setUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Set the unique ID for this project.</div>
</td>
</tr>
<tr id="i339" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setUpdatingTaskStatusUpdatesResourceStatus-boolean-">setUpdatingTaskStatusUpdatesResourceStatus</a></span>(boolean&nbsp;flag)</code>
<div class="block">Flags whether updating Task status also updates resource status.</div>
</td>
</tr>
<tr id="i340" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setUseExpectedFinishDates-boolean-">setUseExpectedFinishDates</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the use expected finish dates flag.</div>
</td>
</tr>
<tr id="i341" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setWbsCodeSeparator-java.lang.String-">setWbsCodeSeparator</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the WBS Code separator character.</div>
</td>
</tr>
<tr id="i342" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setWeekStartDay-java.time.DayOfWeek-">setWeekStartDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/DayOfWeek.html?is-external=true" title="class or interface in java.time">DayOfWeek</a>&nbsp;weekStartDay)</code>
<div class="block">Set the week start day.</div>
</td>
</tr>
<tr id="i343" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setWork-org.mpxj.Duration-">setWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</code>
<div class="block">Sets the project work duration.</div>
</td>
</tr>
<tr id="i344" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectProperties.html#setWork2-java.lang.Number-">setWork2</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;work2)</code>
<div class="block">Sets the project's "Work 2" attribute.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.AbstractFieldContainer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a></h3>
<code><a href="../../org/mpxj/AbstractFieldContainer.html#addFieldListener-org.mpxj.listener.FieldListener-">addFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#disableEvents--">disableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#enableEvents--">enableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#get-org.mpxj.FieldType-">get</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#removeFieldListener-org.mpxj.listener.FieldListener-">removeFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDefaultDurationUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultDurationUnits</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;getDefaultDurationUnits()</pre>
<div class="block">Gets Default Duration units. The constants used to define the
 duration units are defined by the <code>TimeUnit</code> class.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default duration units</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj"><code>TimeUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultDurationUnits-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultDurationUnits</h4>
<pre>public&nbsp;void&nbsp;setDefaultDurationUnits(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;units)</pre>
<div class="block">Default duration units. The constants used to define the
 duration units are defined by the <code>TimeUnit</code> class.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>units</code> - default duration units</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj"><code>TimeUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultDurationIsFixed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultDurationIsFixed</h4>
<pre>public&nbsp;boolean&nbsp;getDefaultDurationIsFixed()</pre>
<div class="block">Retrieves a flag indicating if the default duration type is fixed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setDefaultDurationIsFixed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultDurationIsFixed</h4>
<pre>public&nbsp;void&nbsp;setDefaultDurationIsFixed(boolean&nbsp;fixed)</pre>
<div class="block">Sets a flag indicating if the default duration type is fixed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fixed</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getDefaultWorkUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultWorkUnits</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;getDefaultWorkUnits()</pre>
<div class="block">Default work units. The constants used to define the
 work units are defined by the <code>TimeUnit</code> class.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default work units</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj"><code>TimeUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultWorkUnits-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultWorkUnits</h4>
<pre>public&nbsp;void&nbsp;setDefaultWorkUnits(<a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;units)</pre>
<div class="block">Default work units. The constants used to define the
 work units are defined by the <code>TimeUnit</code> class.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>units</code> - default work units</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../org/mpxj/TimeUnit.html" title="enum in org.mpxj"><code>TimeUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="getDefaultStandardRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultStandardRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getDefaultStandardRate()</pre>
<div class="block">Retrieves the default standard rate.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default standard rate</dd>
</dl>
</li>
</ul>
<a name="setDefaultStandardRate-org.mpxj.Rate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultStandardRate</h4>
<pre>public&nbsp;void&nbsp;setDefaultStandardRate(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</pre>
<div class="block">Sets the default standard rate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rate</code> - default standard rate</dd>
</dl>
</li>
</ul>
<a name="getDefaultOvertimeRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultOvertimeRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getDefaultOvertimeRate()</pre>
<div class="block">Get overtime rate.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>rate</dd>
</dl>
</li>
</ul>
<a name="setDefaultOvertimeRate-org.mpxj.Rate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultOvertimeRate</h4>
<pre>public&nbsp;void&nbsp;setDefaultOvertimeRate(<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</pre>
<div class="block">Set default overtime rate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rate</code> - default overtime rate</dd>
</dl>
</li>
</ul>
<a name="getUpdatingTaskStatusUpdatesResourceStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdatingTaskStatusUpdatesResourceStatus</h4>
<pre>public&nbsp;boolean&nbsp;getUpdatingTaskStatusUpdatesResourceStatus()</pre>
<div class="block">Flags whether updating Task status also updates resource status.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setUpdatingTaskStatusUpdatesResourceStatus-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdatingTaskStatusUpdatesResourceStatus</h4>
<pre>public&nbsp;void&nbsp;setUpdatingTaskStatusUpdatesResourceStatus(boolean&nbsp;flag)</pre>
<div class="block">Flags whether updating Task status also updates resource status.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getSplitInProgressTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSplitInProgressTasks</h4>
<pre>public&nbsp;boolean&nbsp;getSplitInProgressTasks()</pre>
<div class="block">Flag representing whether to split in-progress tasks.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Boolean value</dd>
</dl>
</li>
</ul>
<a name="setSplitInProgressTasks-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplitInProgressTasks</h4>
<pre>public&nbsp;void&nbsp;setSplitInProgressTasks(boolean&nbsp;flag)</pre>
<div class="block">Flag representing whether to split in-progress tasks.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - boolean value</dd>
</dl>
</li>
</ul>
<a name="getDateOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateOrder</h4>
<pre>public&nbsp;<a href="../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a>&nbsp;getDateOrder()</pre>
<div class="block">Gets constant representing set Date order eg DMY, MDY.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>constant value for date order</dd>
</dl>
</li>
</ul>
<a name="setDateOrder-org.mpxj.DateOrder-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateOrder</h4>
<pre>public&nbsp;void&nbsp;setDateOrder(<a href="../../org/mpxj/DateOrder.html" title="enum in org.mpxj">DateOrder</a>&nbsp;dateOrder)</pre>
<div class="block">Sets constant representing set Date order eg DMY, MDY.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dateOrder</code> - date order value</dd>
</dl>
</li>
</ul>
<a name="getTimeFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimeFormat</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a>&nbsp;getTimeFormat()</pre>
<div class="block">Gets constant representing the Time Format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>time format constant</dd>
</dl>
</li>
</ul>
<a name="setTimeFormat-org.mpxj.ProjectTimeFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeFormat</h4>
<pre>public&nbsp;void&nbsp;setTimeFormat(<a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj">ProjectTimeFormat</a>&nbsp;timeFormat)</pre>
<div class="block">Sets constant representing the time format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeFormat</code> - constant value</dd>
</dl>
</li>
</ul>
<a name="getDefaultStartTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultStartTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;getDefaultStartTime()</pre>
<div class="block">Retrieve the default start time, specified using the Java Date type.
 Note that this assumes that the value returned from
 the getTime method starts at zero... i.e. the date part
 of the date/time value has not been set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default start time</dd>
</dl>
</li>
</ul>
<a name="setDefaultStartTime-java.time.LocalTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultStartTime</h4>
<pre>public&nbsp;void&nbsp;setDefaultStartTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;defaultStartTime)</pre>
<div class="block">Set the default start time, specified using the Java Date type.
 Note that this assumes that the value returned from
 the getTime method starts at zero... i.e. the date part
 of the date/time value has not been set.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaultStartTime</code> - default time</dd>
</dl>
</li>
</ul>
<a name="getDateSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateSeparator</h4>
<pre>public&nbsp;char&nbsp;getDateSeparator()</pre>
<div class="block">Gets the date separator.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>date separator as set.</dd>
</dl>
</li>
</ul>
<a name="setDateSeparator-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateSeparator</h4>
<pre>public&nbsp;void&nbsp;setDateSeparator(char&nbsp;dateSeparator)</pre>
<div class="block">Sets the date separator.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dateSeparator</code> - date separator as set.</dd>
</dl>
</li>
</ul>
<a name="getTimeSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimeSeparator</h4>
<pre>public&nbsp;char&nbsp;getTimeSeparator()</pre>
<div class="block">Gets the time separator.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>time separator as set.</dd>
</dl>
</li>
</ul>
<a name="setTimeSeparator-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimeSeparator</h4>
<pre>public&nbsp;void&nbsp;setTimeSeparator(char&nbsp;timeSeparator)</pre>
<div class="block">Sets the time separator.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeSeparator</code> - time separator</dd>
</dl>
</li>
</ul>
<a name="getAMText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAMText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAMText()</pre>
<div class="block">Gets the AM text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>AM Text as set.</dd>
</dl>
</li>
</ul>
<a name="setAMText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAMText</h4>
<pre>public&nbsp;void&nbsp;setAMText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;amText)</pre>
<div class="block">Sets the AM text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>amText</code> - AM Text as set.</dd>
</dl>
</li>
</ul>
<a name="getPMText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPMText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPMText()</pre>
<div class="block">Gets the PM text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>PM Text as set.</dd>
</dl>
</li>
</ul>
<a name="setPMText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPMText</h4>
<pre>public&nbsp;void&nbsp;setPMText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pmText)</pre>
<div class="block">Sets the PM text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>pmText</code> - PM Text as set.</dd>
</dl>
</li>
</ul>
<a name="getDateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateFormat</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;getDateFormat()</pre>
<div class="block">Gets the set Date Format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project date format</dd>
</dl>
</li>
</ul>
<a name="setDateFormat-org.mpxj.ProjectDateFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateFormat</h4>
<pre>public&nbsp;void&nbsp;setDateFormat(<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;dateFormat)</pre>
<div class="block">Sets the set Date Format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dateFormat</code> - int representing Date Format</dd>
</dl>
</li>
</ul>
<a name="getBarTextDateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarTextDateFormat</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;getBarTextDateFormat()</pre>
<div class="block">Gets Bar Text Date Format.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int value</dd>
</dl>
</li>
</ul>
<a name="setBarTextDateFormat-org.mpxj.ProjectDateFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarTextDateFormat</h4>
<pre>public&nbsp;void&nbsp;setBarTextDateFormat(<a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj">ProjectDateFormat</a>&nbsp;dateFormat)</pre>
<div class="block">Sets Bar Text Date Format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dateFormat</code> - value to be set</dd>
</dl>
</li>
</ul>
<a name="getDefaultEndTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultEndTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;getDefaultEndTime()</pre>
<div class="block">Retrieves the default end time.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>End time</dd>
</dl>
</li>
</ul>
<a name="setDefaultEndTime-java.time.LocalTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultEndTime</h4>
<pre>public&nbsp;void&nbsp;setDefaultEndTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalTime.html?is-external=true" title="class or interface in java.time">LocalTime</a>&nbsp;date)</pre>
<div class="block">Sets the default end time.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - End time</dd>
</dl>
</li>
</ul>
<a name="setProjectTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectTitle</h4>
<pre>public&nbsp;void&nbsp;setProjectTitle(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectTitle)</pre>
<div class="block">Sets the project title.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectTitle</code> - project title</dd>
</dl>
</li>
</ul>
<a name="getProjectTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectTitle</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectTitle()</pre>
<div class="block">Gets the project title.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project title</dd>
</dl>
</li>
</ul>
<a name="setCompany-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompany</h4>
<pre>public&nbsp;void&nbsp;setCompany(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;company)</pre>
<div class="block">Sets the company name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>company</code> - company name</dd>
</dl>
</li>
</ul>
<a name="getCompany--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompany</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCompany()</pre>
<div class="block">Retrieves the company name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>company name</dd>
</dl>
</li>
</ul>
<a name="setManager-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManager</h4>
<pre>public&nbsp;void&nbsp;setManager(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;manager)</pre>
<div class="block">Sets the manager name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>manager</code> - manager name</dd>
</dl>
</li>
</ul>
<a name="getManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManager</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getManager()</pre>
<div class="block">Retrieves the manager name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>manager name</dd>
</dl>
</li>
</ul>
<a name="setDefaultCalendarUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultCalendarUniqueID</h4>
<pre>public&nbsp;void&nbsp;setDefaultCalendarUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the default calendar unique ID for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - default calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="getDefaultCalendarUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultCalendarUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDefaultCalendarUniqueID()</pre>
<div class="block">Retrieve the default calendar unique ID for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="setDefaultCalendar-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultCalendar</h4>
<pre>public&nbsp;void&nbsp;setDefaultCalendar(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block">Set the default calendar for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - default calendar</dd>
</dl>
</li>
</ul>
<a name="getDefaultCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;getDefaultCalendar()</pre>
<div class="block">Retrieve the default calendar for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default calendar</dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate)</pre>
<div class="block">Sets the project start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startDate</code> - project start date</dd>
</dl>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Retrieves the project start date. If an explicit start date has not been
 set, we fall back on the earliest start date in the file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project start date</dd>
</dl>
</li>
</ul>
<a name="getFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinishDate()</pre>
<div class="block">Retrieves the project finish date. If an explicit finish date has not been set we
 fall back on the latest task finish date in the file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project finish date</dd>
</dl>
</li>
</ul>
<a name="setFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishDate</h4>
<pre>public&nbsp;void&nbsp;setFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;finishDate)</pre>
<div class="block">Sets the project finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>finishDate</code> - project finish date</dd>
</dl>
</li>
</ul>
<a name="getScheduleFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScheduleFrom</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a>&nbsp;getScheduleFrom()</pre>
<div class="block">Retrieves an enumerated value indicating if tasks in this project are
 scheduled from a start or a finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>schedule from flag</dd>
</dl>
</li>
</ul>
<a name="setScheduleFrom-org.mpxj.ScheduleFrom-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduleFrom</h4>
<pre>public&nbsp;void&nbsp;setScheduleFrom(<a href="../../org/mpxj/ScheduleFrom.html" title="enum in org.mpxj">ScheduleFrom</a>&nbsp;scheduleFrom)</pre>
<div class="block">Sets an enumerated value indicating if tasks in this project are
 scheduled from a start or a finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>scheduleFrom</code> - schedule from value</dd>
</dl>
</li>
</ul>
<a name="getCurrentDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCurrentDate()</pre>
<div class="block">Retrieves the current date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>current date</dd>
</dl>
</li>
</ul>
<a name="setCurrentDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrentDate</h4>
<pre>public&nbsp;void&nbsp;setCurrentDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;currentDate)</pre>
<div class="block">Sets the current date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>currentDate</code> - current date</dd>
</dl>
</li>
</ul>
<a name="getComments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getComments()</pre>
<div class="block">Returns any comments.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>comments</dd>
</dl>
</li>
</ul>
<a name="setComments-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComments</h4>
<pre>public&nbsp;void&nbsp;setComments(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;comments)</pre>
<div class="block">Set comment text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>comments</code> - comment text</dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost()</pre>
<div class="block">Retrieves the project cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project cost</dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the project cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - project cost</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;baselineCost)</pre>
<div class="block">Sets the baseline project cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineCost</code> - baseline project cost</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost()</pre>
<div class="block">Retrieves the baseline project cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project cost</dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</pre>
<div class="block">Sets the actual project cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualCost</code> - actual project cost</dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualCost()</pre>
<div class="block">Retrieves the actual project cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual project cost</dd>
</dl>
</li>
</ul>
<a name="setWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;work)</pre>
<div class="block">Sets the project work duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work</code> - project work duration</dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWork()</pre>
<div class="block">Retrieves the project work duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project work duration</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;baselineWork)</pre>
<div class="block">Set the baseline project work duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineWork</code> - baseline project work duration</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork()</pre>
<div class="block">Retrieves the baseline project work duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project work duration</dd>
</dl>
</li>
</ul>
<a name="setActualWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWork</h4>
<pre>public&nbsp;void&nbsp;setActualWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualWork)</pre>
<div class="block">Sets the actual project work duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualWork</code> - actual project work duration</dd>
</dl>
</li>
</ul>
<a name="getActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWork()</pre>
<div class="block">Retrieves the actual project work duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual project work duration</dd>
</dl>
</li>
</ul>
<a name="getWork2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork2</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getWork2()</pre>
<div class="block">Retrieves the project's "Work 2" attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Work 2 attribute</dd>
</dl>
</li>
</ul>
<a name="setWork2-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork2</h4>
<pre>public&nbsp;void&nbsp;setWork2(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;work2)</pre>
<div class="block">Sets the project's "Work 2" attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>work2</code> - work2 percentage value</dd>
</dl>
</li>
</ul>
<a name="getDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDuration()</pre>
<div class="block">Retrieves the project duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project duration</dd>
</dl>
</li>
</ul>
<a name="setDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;void&nbsp;setDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Sets the project duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - project duration</dd>
</dl>
</li>
</ul>
<a name="getBaselineDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineDuration()</pre>
<div class="block">Retrieves the baseline duration value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project duration value</dd>
</dl>
</li>
</ul>
<a name="setBaselineDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDuration</h4>
<pre>public&nbsp;void&nbsp;setBaselineDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;baselineDuration)</pre>
<div class="block">Sets the baseline project duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineDuration</code> - baseline project duration</dd>
</dl>
</li>
</ul>
<a name="getActualDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualDuration()</pre>
<div class="block">Retrieves the actual project duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual project duration</dd>
</dl>
</li>
</ul>
<a name="setActualDuration-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualDuration</h4>
<pre>public&nbsp;void&nbsp;setActualDuration(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;actualDuration)</pre>
<div class="block">Sets the actual project duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualDuration</code> - actual project duration</dd>
</dl>
</li>
</ul>
<a name="getPercentageComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentageComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentageComplete()</pre>
<div class="block">Retrieves the project percentage complete.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percentage value</dd>
</dl>
</li>
</ul>
<a name="setPercentageComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentageComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentageComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;percentComplete)</pre>
<div class="block">Sets project percentage complete.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>percentComplete</code> - project percent complete</dd>
</dl>
</li>
</ul>
<a name="setBaselineStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStart</h4>
<pre>public&nbsp;void&nbsp;setBaselineStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;baselineStartDate)</pre>
<div class="block">Sets the baseline project start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineStartDate</code> - baseline project start date</dd>
</dl>
</li>
</ul>
<a name="getBaselineStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineStart()</pre>
<div class="block">Retrieves the baseline project start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project start date</dd>
</dl>
</li>
</ul>
<a name="setBaselineFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineFinish</h4>
<pre>public&nbsp;void&nbsp;setBaselineFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;baselineFinishDate)</pre>
<div class="block">Sets the baseline project finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineFinishDate</code> - baseline project finish date</dd>
</dl>
</li>
</ul>
<a name="getBaselineFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineFinish()</pre>
<div class="block">Retrieves the baseline project finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project finish date</dd>
</dl>
</li>
</ul>
<a name="setActualStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualStart</h4>
<pre>public&nbsp;void&nbsp;setActualStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;actualStartDate)</pre>
<div class="block">Sets the actual project start date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualStartDate</code> - actual project start date</dd>
</dl>
</li>
</ul>
<a name="getActualStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualStart()</pre>
<div class="block">Retrieves the actual project start date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual project start date</dd>
</dl>
</li>
</ul>
<a name="setActualFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualFinish</h4>
<pre>public&nbsp;void&nbsp;setActualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;actualFinishDate)</pre>
<div class="block">Sets the actual project finish date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualFinishDate</code> - actual project finish date</dd>
</dl>
</li>
</ul>
<a name="getActualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualFinish()</pre>
<div class="block">Retrieves the actual project finish date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual project finish date</dd>
</dl>
</li>
</ul>
<a name="getStartVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getStartVariance()</pre>
<div class="block">Retrieves the start variance duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start date variance</dd>
</dl>
</li>
</ul>
<a name="setStartVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartVariance</h4>
<pre>public&nbsp;void&nbsp;setStartVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;startVariance)</pre>
<div class="block">Sets the start variance duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startVariance</code> - the start date variance</dd>
</dl>
</li>
</ul>
<a name="getFinishVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getFinishVariance()</pre>
<div class="block">Retrieves the project finish variance duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project finish variance duration</dd>
</dl>
</li>
</ul>
<a name="setFinishVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishVariance</h4>
<pre>public&nbsp;void&nbsp;setFinishVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;finishVariance)</pre>
<div class="block">Sets the project finish variance duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>finishVariance</code> - project finish variance duration</dd>
</dl>
</li>
</ul>
<a name="getSubject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubject</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSubject()</pre>
<div class="block">Returns the project subject text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>subject text</dd>
</dl>
</li>
</ul>
<a name="setSubject-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubject</h4>
<pre>public&nbsp;void&nbsp;setSubject(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject)</pre>
<div class="block">Sets the project subject text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subject</code> - subject text</dd>
</dl>
</li>
</ul>
<a name="getAuthor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAuthor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAuthor()</pre>
<div class="block">Retrieves the project author text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>author text</dd>
</dl>
</li>
</ul>
<a name="setAuthor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuthor</h4>
<pre>public&nbsp;void&nbsp;setAuthor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;author)</pre>
<div class="block">Sets the project author text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>author</code> - project author text</dd>
</dl>
</li>
</ul>
<a name="getKeywords--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeywords</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getKeywords()</pre>
<div class="block">Retrieves the project keyword text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project keyword text</dd>
</dl>
</li>
</ul>
<a name="setKeywords-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeywords</h4>
<pre>public&nbsp;void&nbsp;setKeywords(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;keywords)</pre>
<div class="block">Sets the project keyword text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>keywords</code> - project keyword text</dd>
</dl>
</li>
</ul>
<a name="setCurrencySymbol-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencySymbol</h4>
<pre>public&nbsp;void&nbsp;setCurrencySymbol(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;symbol)</pre>
<div class="block">Sets currency symbol.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>symbol</code> - currency symbol</dd>
</dl>
</li>
</ul>
<a name="getCurrencySymbol--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencySymbol</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrencySymbol()</pre>
<div class="block">Retrieves the currency symbol.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency symbol</dd>
</dl>
</li>
</ul>
<a name="setSymbolPosition-org.mpxj.CurrencySymbolPosition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSymbolPosition</h4>
<pre>public&nbsp;void&nbsp;setSymbolPosition(<a href="../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;value)</pre>
<div class="block">Sets the position of the currency symbol.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - currency symbol position.</dd>
</dl>
</li>
</ul>
<a name="getSymbolPosition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSymbolPosition</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CurrencySymbolPosition.html" title="enum in org.mpxj">CurrencySymbolPosition</a>&nbsp;getSymbolPosition()</pre>
<div class="block">Retrieves a constant representing the position of the currency symbol.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>position</dd>
</dl>
</li>
</ul>
<a name="setCurrencyDigits-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencyDigits</h4>
<pre>public&nbsp;void&nbsp;setCurrencyDigits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;currDigs)</pre>
<div class="block">Sets no of currency digits.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>currDigs</code> - Available values, 0,1,2</dd>
</dl>
</li>
</ul>
<a name="getCurrencyDigits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencyDigits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCurrencyDigits()</pre>
<div class="block">Gets no of currency digits.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Available values, 0,1,2</dd>
</dl>
</li>
</ul>
<a name="setThousandsSeparator-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThousandsSeparator</h4>
<pre>public&nbsp;void&nbsp;setThousandsSeparator(char&nbsp;sep)</pre>
<div class="block">Sets the thousands separator.
 Note that this separator defines the thousands separator for all decimal
 numbers that appear in the MPX file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sep</code> - character</dd>
</dl>
</li>
</ul>
<a name="getThousandsSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThousandsSeparator</h4>
<pre>public&nbsp;char&nbsp;getThousandsSeparator()</pre>
<div class="block">Gets the thousands separator.
 Note that this separator defines the thousands separator for all decimal
 numbers that appear in the MPX file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>character</dd>
</dl>
</li>
</ul>
<a name="setDecimalSeparator-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecimalSeparator</h4>
<pre>public&nbsp;void&nbsp;setDecimalSeparator(char&nbsp;decSep)</pre>
<div class="block">Sets the decimal separator.
 Note that this separator defines the decimal separator for all decimal
 numbers that appear in the MPX file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>decSep</code> - character</dd>
</dl>
</li>
</ul>
<a name="getDecimalSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecimalSeparator</h4>
<pre>public&nbsp;char&nbsp;getDecimalSeparator()</pre>
<div class="block">Gets the decimal separator.
 Note that this separator defines the decimal separator for all decimal
 numbers that appear in the MPX file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>character</dd>
</dl>
</li>
</ul>
<a name="getProjectExternallyEdited--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectExternallyEdited</h4>
<pre>public&nbsp;boolean&nbsp;getProjectExternallyEdited()</pre>
<div class="block">Retrieve the externally edited flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>externally edited flag</dd>
</dl>
</li>
</ul>
<a name="setProjectExternallyEdited-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectExternallyEdited</h4>
<pre>public&nbsp;void&nbsp;setProjectExternallyEdited(boolean&nbsp;projectExternallyEdited)</pre>
<div class="block">Set the externally edited flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectExternallyEdited</code> - externally edited flag</dd>
</dl>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCategory()</pre>
<div class="block">Retrieves the category text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>category text</dd>
</dl>
</li>
</ul>
<a name="setCategory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>public&nbsp;void&nbsp;setCategory(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category)</pre>
<div class="block">Sets the category text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>category</code> - category text</dd>
</dl>
</li>
</ul>
<a name="getDaysPerMonth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDaysPerMonth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDaysPerMonth()</pre>
<div class="block">Retrieve the number of days per month.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html#getDaysPerMonth--">getDaysPerMonth</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>days per month</dd>
</dl>
</li>
</ul>
<a name="setDaysPerMonth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDaysPerMonth</h4>
<pre>public&nbsp;void&nbsp;setDaysPerMonth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;daysPerMonth)</pre>
<div class="block">Set the number of days per month.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>daysPerMonth</code> - days per month</dd>
</dl>
</li>
</ul>
<a name="getMinutesPerDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMinutesPerDay()</pre>
<div class="block">Retrieve the number of minutes per day.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html#getMinutesPerDay--">getMinutesPerDay</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>minutes per day</dd>
</dl>
</li>
</ul>
<a name="setMinutesPerDay-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerDay</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerDay)</pre>
<div class="block">Set the number of minutes per day.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minutesPerDay</code> - minutes per day</dd>
</dl>
</li>
</ul>
<a name="getMinutesPerWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerWeek</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMinutesPerWeek()</pre>
<div class="block">Retrieve the number of minutes per week.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html#getMinutesPerWeek--">getMinutesPerWeek</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>minutes per week</dd>
</dl>
</li>
</ul>
<a name="setMinutesPerWeek-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerWeek</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerWeek(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerWeek)</pre>
<div class="block">Set the number of minutes per week.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minutesPerWeek</code> - minutes per week</dd>
</dl>
</li>
</ul>
<a name="getMinutesPerMonth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerMonth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMinutesPerMonth()</pre>
<div class="block">Retrieve the default number of minutes per month.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html#getMinutesPerMonth--">getMinutesPerMonth</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>minutes per month</dd>
</dl>
</li>
</ul>
<a name="setMinutesPerMonth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerMonth</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerMonth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerMonth)</pre>
<div class="block">Set the default number of minutes per month.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minutesPerMonth</code> - minutes per month</dd>
</dl>
</li>
</ul>
<a name="getMinutesPerYear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerYear</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMinutesPerYear()</pre>
<div class="block">Retrieve the default number of minutes per year.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html#getMinutesPerYear--">getMinutesPerYear</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>minutes per year</dd>
</dl>
</li>
</ul>
<a name="setMinutesPerYear-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerYear</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerYear(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;minutesPerYear)</pre>
<div class="block">Set the default number of minutes per year.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minutesPerYear</code> - minutes per year</dd>
</dl>
</li>
</ul>
<a name="getFiscalYearStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiscalYearStart</h4>
<pre>public&nbsp;boolean&nbsp;getFiscalYearStart()</pre>
<div class="block">Retrieve the fiscal year start flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>fiscal year start flag</dd>
</dl>
</li>
</ul>
<a name="setFiscalYearStart-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFiscalYearStart</h4>
<pre>public&nbsp;void&nbsp;setFiscalYearStart(boolean&nbsp;fiscalYearStart)</pre>
<div class="block">Set the fiscal year start flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fiscalYearStart</code> - fiscal year start</dd>
</dl>
</li>
</ul>
<a name="getDefaultTaskEarnedValueMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTaskEarnedValueMethod</h4>
<pre>public&nbsp;<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;getDefaultTaskEarnedValueMethod()</pre>
<div class="block">Retrieves the default task earned value method.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default task earned value method</dd>
</dl>
</li>
</ul>
<a name="setDefaultTaskEarnedValueMethod-org.mpxj.EarnedValueMethod-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultTaskEarnedValueMethod</h4>
<pre>public&nbsp;void&nbsp;setDefaultTaskEarnedValueMethod(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;defaultTaskEarnedValueMethod)</pre>
<div class="block">Sets the default task earned value method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaultTaskEarnedValueMethod</code> - default task earned value method</dd>
</dl>
</li>
</ul>
<a name="getRemoveFileProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemoveFileProperties</h4>
<pre>public&nbsp;boolean&nbsp;getRemoveFileProperties()</pre>
<div class="block">Retrieve the remove file properties flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remove file properties flag</dd>
</dl>
</li>
</ul>
<a name="setRemoveFileProperties-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemoveFileProperties</h4>
<pre>public&nbsp;void&nbsp;setRemoveFileProperties(boolean&nbsp;removeFileProperties)</pre>
<div class="block">Set the remove file properties flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>removeFileProperties</code> - remove file properties flag</dd>
</dl>
</li>
</ul>
<a name="getMoveCompletedEndsBack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMoveCompletedEndsBack</h4>
<pre>public&nbsp;boolean&nbsp;getMoveCompletedEndsBack()</pre>
<div class="block">Retrieve the move completed ends back flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>move completed ends back flag</dd>
</dl>
</li>
</ul>
<a name="setMoveCompletedEndsBack-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveCompletedEndsBack</h4>
<pre>public&nbsp;void&nbsp;setMoveCompletedEndsBack(boolean&nbsp;moveCompletedEndsBack)</pre>
<div class="block">Set the move completed ends back flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moveCompletedEndsBack</code> - move completed ends back flag</dd>
</dl>
</li>
</ul>
<a name="getNewTasksEstimated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTasksEstimated</h4>
<pre>public&nbsp;boolean&nbsp;getNewTasksEstimated()</pre>
<div class="block">Retrieve the new tasks estimated flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new tasks estimated flag</dd>
</dl>
</li>
</ul>
<a name="setNewTasksEstimated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksEstimated</h4>
<pre>public&nbsp;void&nbsp;setNewTasksEstimated(boolean&nbsp;newTasksEstimated)</pre>
<div class="block">Set the new tasks estimated flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newTasksEstimated</code> - new tasks estimated flag</dd>
</dl>
</li>
</ul>
<a name="getSpreadActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpreadActualCost</h4>
<pre>public&nbsp;boolean&nbsp;getSpreadActualCost()</pre>
<div class="block">Retrieve the spread actual cost flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>spread actual cost flag</dd>
</dl>
</li>
</ul>
<a name="setSpreadActualCost-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpreadActualCost</h4>
<pre>public&nbsp;void&nbsp;setSpreadActualCost(boolean&nbsp;spreadActualCost)</pre>
<div class="block">Set the spread actual cost flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spreadActualCost</code> - spread actual cost flag</dd>
</dl>
</li>
</ul>
<a name="getMultipleCriticalPaths--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMultipleCriticalPaths</h4>
<pre>public&nbsp;boolean&nbsp;getMultipleCriticalPaths()</pre>
<div class="block">Retrieve the multiple critical paths flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>multiple critical paths flag</dd>
</dl>
</li>
</ul>
<a name="setMultipleCriticalPaths-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMultipleCriticalPaths</h4>
<pre>public&nbsp;void&nbsp;setMultipleCriticalPaths(boolean&nbsp;multipleCriticalPaths)</pre>
<div class="block">Set the multiple critical paths flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>multipleCriticalPaths</code> - multiple critical paths flag</dd>
</dl>
</li>
</ul>
<a name="getAutoAddNewResourcesAndTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoAddNewResourcesAndTasks</h4>
<pre>public&nbsp;boolean&nbsp;getAutoAddNewResourcesAndTasks()</pre>
<div class="block">Retrieve the auto add new resources and tasks flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>auto add new resources and tasks flag</dd>
</dl>
</li>
</ul>
<a name="setAutoAddNewResourcesAndTasks-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoAddNewResourcesAndTasks</h4>
<pre>public&nbsp;void&nbsp;setAutoAddNewResourcesAndTasks(boolean&nbsp;autoAddNewResourcesAndTasks)</pre>
<div class="block">Set the auto add new resources and tasks flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>autoAddNewResourcesAndTasks</code> - auto add new resources and tasks flag</dd>
</dl>
</li>
</ul>
<a name="getLastSaved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSaved</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLastSaved()</pre>
<div class="block">Retrieve the last saved date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>last saved date</dd>
</dl>
</li>
</ul>
<a name="setLastSaved-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastSaved</h4>
<pre>public&nbsp;void&nbsp;setLastSaved(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;lastSaved)</pre>
<div class="block">Set the last saved date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lastSaved</code> - last saved date</dd>
</dl>
</li>
</ul>
<a name="getStatusDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStatusDate()</pre>
<div class="block">Retrieve the status date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>status date</dd>
</dl>
</li>
</ul>
<a name="setStatusDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatusDate</h4>
<pre>public&nbsp;void&nbsp;setStatusDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;statusDate)</pre>
<div class="block">Set the status date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>statusDate</code> - status date</dd>
</dl>
</li>
</ul>
<a name="getMoveRemainingStartsBack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMoveRemainingStartsBack</h4>
<pre>public&nbsp;boolean&nbsp;getMoveRemainingStartsBack()</pre>
<div class="block">Retrieves the move remaining starts back flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>move remaining starts back flag</dd>
</dl>
</li>
</ul>
<a name="setMoveRemainingStartsBack-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveRemainingStartsBack</h4>
<pre>public&nbsp;void&nbsp;setMoveRemainingStartsBack(boolean&nbsp;moveRemainingStartsBack)</pre>
<div class="block">Sets the move remaining starts back flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moveRemainingStartsBack</code> - remaining starts back flag</dd>
</dl>
</li>
</ul>
<a name="getAutolink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutolink</h4>
<pre>public&nbsp;boolean&nbsp;getAutolink()</pre>
<div class="block">Retrieves the autolink flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>autolink flag</dd>
</dl>
</li>
</ul>
<a name="setAutolink-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutolink</h4>
<pre>public&nbsp;void&nbsp;setAutolink(boolean&nbsp;autolink)</pre>
<div class="block">Sets the autolink flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>autolink</code> - autolink flag</dd>
</dl>
</li>
</ul>
<a name="getMicrosoftProjectServerURL--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMicrosoftProjectServerURL</h4>
<pre>public&nbsp;boolean&nbsp;getMicrosoftProjectServerURL()</pre>
<div class="block">Retrieves the Microsoft Project Server URL flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Microsoft Project Server URL flag</dd>
</dl>
</li>
</ul>
<a name="setMicrosoftProjectServerURL-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMicrosoftProjectServerURL</h4>
<pre>public&nbsp;void&nbsp;setMicrosoftProjectServerURL(boolean&nbsp;microsoftProjectServerURL)</pre>
<div class="block">Sets the Microsoft Project Server URL flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>microsoftProjectServerURL</code> - Microsoft Project Server URL flag</dd>
</dl>
</li>
</ul>
<a name="getHonorConstraints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHonorConstraints</h4>
<pre>public&nbsp;boolean&nbsp;getHonorConstraints()</pre>
<div class="block">Retrieves the honor constraints flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>honor constraints flag</dd>
</dl>
</li>
</ul>
<a name="setHonorConstraints-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHonorConstraints</h4>
<pre>public&nbsp;void&nbsp;setHonorConstraints(boolean&nbsp;honorConstraints)</pre>
<div class="block">Sets the honor constraints flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>honorConstraints</code> - honor constraints flag</dd>
</dl>
</li>
</ul>
<a name="getAdminProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdminProject</h4>
<pre>public&nbsp;boolean&nbsp;getAdminProject()</pre>
<div class="block">Retrieve the admin project flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>admin project flag</dd>
</dl>
</li>
</ul>
<a name="setAdminProject-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdminProject</h4>
<pre>public&nbsp;void&nbsp;setAdminProject(boolean&nbsp;adminProject)</pre>
<div class="block">Set the admin project flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>adminProject</code> - admin project flag</dd>
</dl>
</li>
</ul>
<a name="getInsertedProjectsLikeSummary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInsertedProjectsLikeSummary</h4>
<pre>public&nbsp;boolean&nbsp;getInsertedProjectsLikeSummary()</pre>
<div class="block">Retrieves the inserted projects like summary flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>inserted projects like summary flag</dd>
</dl>
</li>
</ul>
<a name="setInsertedProjectsLikeSummary-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInsertedProjectsLikeSummary</h4>
<pre>public&nbsp;void&nbsp;setInsertedProjectsLikeSummary(boolean&nbsp;insertedProjectsLikeSummary)</pre>
<div class="block">Sets the inserted projects like summary flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>insertedProjectsLikeSummary</code> - inserted projects like summary flag</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Retrieves the project name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project name</dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Sets the project name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - project name</dd>
</dl>
</li>
</ul>
<a name="getSpreadPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpreadPercentComplete</h4>
<pre>public&nbsp;boolean&nbsp;getSpreadPercentComplete()</pre>
<div class="block">Retrieves the spread percent complete flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>spread percent complete flag</dd>
</dl>
</li>
</ul>
<a name="setSpreadPercentComplete-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpreadPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setSpreadPercentComplete(boolean&nbsp;spreadPercentComplete)</pre>
<div class="block">Sets the spread percent complete flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>spreadPercentComplete</code> - spread percent complete flag</dd>
</dl>
</li>
</ul>
<a name="getMoveCompletedEndsForward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMoveCompletedEndsForward</h4>
<pre>public&nbsp;boolean&nbsp;getMoveCompletedEndsForward()</pre>
<div class="block">Retrieve the move completed ends forward flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>move completed ends forward flag</dd>
</dl>
</li>
</ul>
<a name="setMoveCompletedEndsForward-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveCompletedEndsForward</h4>
<pre>public&nbsp;void&nbsp;setMoveCompletedEndsForward(boolean&nbsp;moveCompletedEndsForward)</pre>
<div class="block">Sets the move completed ends forward flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moveCompletedEndsForward</code> - move completed ends forward flag</dd>
</dl>
</li>
</ul>
<a name="getEditableActualCosts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEditableActualCosts</h4>
<pre>public&nbsp;boolean&nbsp;getEditableActualCosts()</pre>
<div class="block">Retrieve the editable actual costs flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>editable actual costs flag</dd>
</dl>
</li>
</ul>
<a name="setEditableActualCosts-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditableActualCosts</h4>
<pre>public&nbsp;void&nbsp;setEditableActualCosts(boolean&nbsp;editableActualCosts)</pre>
<div class="block">Set the editable actual costs flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>editableActualCosts</code> - editable actual costs flag</dd>
</dl>
</li>
</ul>
<a name="getUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUniqueID()</pre>
<div class="block">Retrieve the unique ID for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unique ID</dd>
</dl>
</li>
</ul>
<a name="setUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUniqueID</h4>
<pre>public&nbsp;void&nbsp;setUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Set the unique ID for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - unique ID</dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Retrieve the GUID for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unique ID</dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;guid)</pre>
<div class="block">Set the GUID for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>guid</code> - GUID</dd>
</dl>
</li>
</ul>
<a name="getRevision--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRevision</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRevision()</pre>
<div class="block">Retrieve the project revision number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>revision number</dd>
</dl>
</li>
</ul>
<a name="getNewTasksEffortDriven--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTasksEffortDriven</h4>
<pre>public&nbsp;boolean&nbsp;getNewTasksEffortDriven()</pre>
<div class="block">Retrieve the new tasks effort driven flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new tasks effort driven flag</dd>
</dl>
</li>
</ul>
<a name="setNewTasksEffortDriven-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksEffortDriven</h4>
<pre>public&nbsp;void&nbsp;setNewTasksEffortDriven(boolean&nbsp;newTasksEffortDriven)</pre>
<div class="block">Sets the new tasks effort driven flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newTasksEffortDriven</code> - new tasks effort driven flag</dd>
</dl>
</li>
</ul>
<a name="setRevision-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRevision</h4>
<pre>public&nbsp;void&nbsp;setRevision(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;revision)</pre>
<div class="block">Set the project revision number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>revision</code> - revision number</dd>
</dl>
</li>
</ul>
<a name="getMoveRemainingStartsForward--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMoveRemainingStartsForward</h4>
<pre>public&nbsp;boolean&nbsp;getMoveRemainingStartsForward()</pre>
<div class="block">Retrieve the move remaining starts forward flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>move remaining starts forward flag</dd>
</dl>
</li>
</ul>
<a name="setMoveRemainingStartsForward-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMoveRemainingStartsForward</h4>
<pre>public&nbsp;void&nbsp;setMoveRemainingStartsForward(boolean&nbsp;moveRemainingStartsForward)</pre>
<div class="block">Set the move remaining starts forward flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>moveRemainingStartsForward</code> - move remaining starts forward flag</dd>
</dl>
</li>
</ul>
<a name="getActualsInSync--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualsInSync</h4>
<pre>public&nbsp;boolean&nbsp;getActualsInSync()</pre>
<div class="block">Retrieve the actuals in sync flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actuals in sync flag</dd>
</dl>
</li>
</ul>
<a name="setActualsInSync-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualsInSync</h4>
<pre>public&nbsp;void&nbsp;setActualsInSync(boolean&nbsp;actualsInSync)</pre>
<div class="block">Set the actuals in sync flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualsInSync</code> - actuals in sync flag</dd>
</dl>
</li>
</ul>
<a name="getDefaultTaskType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTaskType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;getDefaultTaskType()</pre>
<div class="block">Retrieve the default task type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default task type</dd>
</dl>
</li>
</ul>
<a name="setDefaultTaskType-org.mpxj.TaskType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultTaskType</h4>
<pre>public&nbsp;void&nbsp;setDefaultTaskType(<a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj">TaskType</a>&nbsp;defaultTaskType)</pre>
<div class="block">Set the default task type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaultTaskType</code> - default task type</dd>
</dl>
</li>
</ul>
<a name="getEarnedValueMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueMethod</h4>
<pre>public&nbsp;<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;getEarnedValueMethod()</pre>
<div class="block">Retrieve the earned value method.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>earned value method</dd>
</dl>
</li>
</ul>
<a name="setEarnedValueMethod-org.mpxj.EarnedValueMethod-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueMethod</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueMethod(<a href="../../org/mpxj/EarnedValueMethod.html" title="enum in org.mpxj">EarnedValueMethod</a>&nbsp;earnedValueMethod)</pre>
<div class="block">Set the earned value method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>earnedValueMethod</code> - earned value method</dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreationDate()</pre>
<div class="block">Retrieve the project creation date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project creation date</dd>
</dl>
</li>
</ul>
<a name="setCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</pre>
<div class="block">Set the project creation date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>creationDate</code> - project creation date</dd>
</dl>
</li>
</ul>
<a name="getExtendedCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExtendedCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getExtendedCreationDate()</pre>
<div class="block">Retrieve the extended creation date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>extended creation date</dd>
</dl>
</li>
</ul>
<a name="getDefaultFixedCostAccrual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFixedCostAccrual</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getDefaultFixedCostAccrual()</pre>
<div class="block">Retrieve the default fixed cost accrual type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>default fixed cost accrual type</dd>
</dl>
</li>
</ul>
<a name="setDefaultFixedCostAccrual-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFixedCostAccrual</h4>
<pre>public&nbsp;void&nbsp;setDefaultFixedCostAccrual(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;defaultFixedCostAccrual)</pre>
<div class="block">Sets the default fixed cost accrual type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaultFixedCostAccrual</code> - default fixed cost accrual type</dd>
</dl>
</li>
</ul>
<a name="setExtendedCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExtendedCreationDate</h4>
<pre>public&nbsp;void&nbsp;setExtendedCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</pre>
<div class="block">Set the extended creation date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>creationDate</code> - extended creation date</dd>
</dl>
</li>
</ul>
<a name="getCriticalSlackLimit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCriticalSlackLimit</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getCriticalSlackLimit()</pre>
<div class="block">Retrieve the critical slack limit.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>critical slack limit</dd>
</dl>
</li>
</ul>
<a name="setCriticalSlackLimit-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCriticalSlackLimit</h4>
<pre>public&nbsp;void&nbsp;setCriticalSlackLimit(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;criticalSlackLimit)</pre>
<div class="block">Set the critical slack limit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>criticalSlackLimit</code> - critical slack limit</dd>
</dl>
</li>
</ul>
<a name="getBaselineForEarnedValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineForEarnedValue</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaselineForEarnedValue()</pre>
<div class="block">Retrieve the number of the baseline to use for earned value
 calculations.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline for earned value</dd>
</dl>
</li>
</ul>
<a name="setBaselineForEarnedValue-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineForEarnedValue</h4>
<pre>public&nbsp;void&nbsp;setBaselineForEarnedValue(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;baselineForEarnedValue)</pre>
<div class="block">Set the number of the baseline to use for earned value
 calculations.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineForEarnedValue</code> - baseline for earned value</dd>
</dl>
</li>
</ul>
<a name="getFiscalYearStartMonth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFiscalYearStartMonth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFiscalYearStartMonth()</pre>
<div class="block">Retrieves the fiscal year start month (January=1, December=12).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>fiscal year start month</dd>
</dl>
</li>
</ul>
<a name="setFiscalYearStartMonth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFiscalYearStartMonth</h4>
<pre>public&nbsp;void&nbsp;setFiscalYearStartMonth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;fiscalYearStartMonth)</pre>
<div class="block">Sets the fiscal year start month (January=1, December=12).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fiscalYearStartMonth</code> - fiscal year start month</dd>
</dl>
</li>
</ul>
<a name="getNewTaskStartIsProjectStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTaskStartIsProjectStart</h4>
<pre>public&nbsp;boolean&nbsp;getNewTaskStartIsProjectStart()</pre>
<div class="block">Retrieve the flag indicating if new tasks should default to the
 project start date (true) or the current date (false).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new task start is project start</dd>
</dl>
</li>
</ul>
<a name="setNewTaskStartIsProjectStart-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTaskStartIsProjectStart</h4>
<pre>public&nbsp;void&nbsp;setNewTaskStartIsProjectStart(boolean&nbsp;newTaskStartIsProjectStart)</pre>
<div class="block">Sets the flag indicating if new tasks should default to the
 project start date (true) or the current date (false).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newTaskStartIsProjectStart</code> - new task start is project start</dd>
</dl>
</li>
</ul>
<a name="getNewTasksAreManual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewTasksAreManual</h4>
<pre>public&nbsp;boolean&nbsp;getNewTasksAreManual()</pre>
<div class="block">Retrieve the flag indicating if new tasks task mode should default to
 manual (true) or automatic (false).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new task type is manual or auto</dd>
</dl>
</li>
</ul>
<a name="setNewTasksAreManual-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewTasksAreManual</h4>
<pre>public&nbsp;void&nbsp;setNewTasksAreManual(boolean&nbsp;newTasksAreManual)</pre>
<div class="block">Set the flag indicating if new tasks task mode should default to
 manual (true) or automatic (false).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newTasksAreManual</code> - new task type is manual or auto</dd>
</dl>
</li>
</ul>
<a name="getWeekStartDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWeekStartDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/DayOfWeek.html?is-external=true" title="class or interface in java.time">DayOfWeek</a>&nbsp;getWeekStartDay()</pre>
<div class="block">Retrieve the week start day.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>week start day</dd>
</dl>
</li>
</ul>
<a name="setWeekStartDay-java.time.DayOfWeek-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWeekStartDay</h4>
<pre>public&nbsp;void&nbsp;setWeekStartDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/DayOfWeek.html?is-external=true" title="class or interface in java.time">DayOfWeek</a>&nbsp;weekStartDay)</pre>
<div class="block">Set the week start day.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>weekStartDay</code> - week start day</dd>
</dl>
</li>
</ul>
<a name="getCurrencyCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencyCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCurrencyCode()</pre>
<div class="block">Retrieve the currency code for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency code</dd>
</dl>
</li>
</ul>
<a name="setCurrencyCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencyCode</h4>
<pre>public&nbsp;void&nbsp;setCurrencyCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;currencyCode)</pre>
<div class="block">Set the currency code for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>currencyCode</code> - currency code</dd>
</dl>
</li>
</ul>
<a name="setCustomProperties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCustomProperties</h4>
<pre>public&nbsp;void&nbsp;setCustomProperties(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;customProperties)</pre>
<div class="block">Sets a map of custom document properties.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>customProperties</code> - The Document Summary Information Map</dd>
</dl>
</li>
</ul>
<a name="getCustomProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCustomProperties</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getCustomProperties()</pre>
<div class="block">Retrieve a map of custom document properties.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Document Summary Information Map</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkBase-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkBase</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkBase(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkBase)</pre>
<div class="block">Sets the hyperlink base for this Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hyperlinkBase</code> - Hyperlink base</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkBase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkBase</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkBase()</pre>
<div class="block">Gets the hyperlink base for this Project. If any.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Hyperlink base</dd>
</dl>
</li>
</ul>
<a name="getShowProjectSummaryTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowProjectSummaryTask</h4>
<pre>public&nbsp;boolean&nbsp;getShowProjectSummaryTask()</pre>
<div class="block">Retrieves the "show project summary task" flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setShowProjectSummaryTask-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowProjectSummaryTask</h4>
<pre>public&nbsp;void&nbsp;setShowProjectSummaryTask(boolean&nbsp;value)</pre>
<div class="block">Sets the "show project summary task" flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getBaselineDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineDate()</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDate</h4>
<pre>public&nbsp;void&nbsp;setBaselineDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getBaselineDate(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineDate</h4>
<pre>public&nbsp;void&nbsp;setBaselineDate(int&nbsp;baselineNumber,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTemplate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTemplate()</pre>
<div class="block">Retrieve the template property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>template property</dd>
</dl>
</li>
</ul>
<a name="setTemplate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTemplate</h4>
<pre>public&nbsp;void&nbsp;setTemplate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;template)</pre>
<div class="block">Set the template property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>template</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getLastAuthor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastAuthor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLastAuthor()</pre>
<div class="block">Retrieve the project user property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project user property</dd>
</dl>
</li>
</ul>
<a name="setLastAuthor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastAuthor</h4>
<pre>public&nbsp;void&nbsp;setLastAuthor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectUser)</pre>
<div class="block">Set the project user property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectUser</code> - project user property</dd>
</dl>
</li>
</ul>
<a name="getLastPrinted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastPrinted</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLastPrinted()</pre>
<div class="block">Retrieve the last printed property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>last printed property</dd>
</dl>
</li>
</ul>
<a name="setLastPrinted-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastPrinted</h4>
<pre>public&nbsp;void&nbsp;setLastPrinted(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;lastPrinted)</pre>
<div class="block">Set the last printed property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lastPrinted</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getShortApplicationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShortApplicationName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getShortApplicationName()</pre>
<div class="block">Retrieve the application property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setShortApplicationName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShortApplicationName</h4>
<pre>public&nbsp;void&nbsp;setShortApplicationName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;application)</pre>
<div class="block">Set the application property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>application</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getEditingTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEditingTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getEditingTime()</pre>
<div class="block">Retrieve the editing time property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setEditingTime-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditingTime</h4>
<pre>public&nbsp;void&nbsp;setEditingTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;editingTime)</pre>
<div class="block">Set the editing time property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>editingTime</code> - editing time property</dd>
</dl>
</li>
</ul>
<a name="getPresentationFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationFormat</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPresentationFormat()</pre>
<div class="block">Retrieve the format property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setPresentationFormat-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPresentationFormat</h4>
<pre>public&nbsp;void&nbsp;setPresentationFormat(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;format)</pre>
<div class="block">Set the format property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>format</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getContentType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getContentType()</pre>
<div class="block">Retrieve the content type property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>content type property</dd>
</dl>
</li>
</ul>
<a name="setContentType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentType</h4>
<pre>public&nbsp;void&nbsp;setContentType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contentType)</pre>
<div class="block">Set the content type property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contentType</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getContentStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentStatus</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getContentStatus()</pre>
<div class="block">Retrieve the content status property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setContentStatus-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setContentStatus</h4>
<pre>public&nbsp;void&nbsp;setContentStatus(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;contentStatus)</pre>
<div class="block">Set the content status property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contentStatus</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguage</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLanguage()</pre>
<div class="block">Retrieve the language property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLanguage</h4>
<pre>public&nbsp;void&nbsp;setLanguage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;language)</pre>
<div class="block">Set the language property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>language</code> - property value</dd>
</dl>
</li>
</ul>
<a name="getDocumentVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocumentVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDocumentVersion()</pre>
<div class="block">Retrieve the document version property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>property value</dd>
</dl>
</li>
</ul>
<a name="setDocumentVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDocumentVersion</h4>
<pre>public&nbsp;void&nbsp;setDocumentVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;documentVersion)</pre>
<div class="block">Set the document version property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>documentVersion</code> - property value</dd>
</dl>
</li>
</ul>
<a name="setMpxDelimiter-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMpxDelimiter</h4>
<pre>public&nbsp;void&nbsp;setMpxDelimiter(char&nbsp;delimiter)</pre>
<div class="block">Sets the delimiter character, "," by default.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delimiter</code> - delimiter character</dd>
</dl>
</li>
</ul>
<a name="getMpxDelimiter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMpxDelimiter</h4>
<pre>public&nbsp;char&nbsp;getMpxDelimiter()</pre>
<div class="block">Retrieves the delimiter character, "," by default.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>delimiter character</dd>
</dl>
</li>
</ul>
<a name="setMpxProgramName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMpxProgramName</h4>
<pre>public&nbsp;void&nbsp;setMpxProgramName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;programName)</pre>
<div class="block">Program name file created by.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>programName</code> - system name</dd>
</dl>
</li>
</ul>
<a name="getMpxProgramName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMpxProgramName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMpxProgramName()</pre>
<div class="block">Program name file created by.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>program name</dd>
</dl>
</li>
</ul>
<a name="setMpxFileVersion-org.mpxj.FileVersion-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMpxFileVersion</h4>
<pre>public&nbsp;void&nbsp;setMpxFileVersion(<a href="../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a>&nbsp;version)</pre>
<div class="block">Version of the MPX file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>version</code> - MPX file version</dd>
</dl>
</li>
</ul>
<a name="getMpxFileVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMpxFileVersion</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FileVersion.html" title="enum in org.mpxj">FileVersion</a>&nbsp;getMpxFileVersion()</pre>
<div class="block">Version of the MPX file.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>MPX file version</dd>
</dl>
</li>
</ul>
<a name="setMpxCodePage-org.mpxj.CodePage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMpxCodePage</h4>
<pre>public&nbsp;void&nbsp;setMpxCodePage(<a href="../../org/mpxj/CodePage.html" title="enum in org.mpxj">CodePage</a>&nbsp;codePage)</pre>
<div class="block">Sets the codepage.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>codePage</code> - code page type</dd>
</dl>
</li>
</ul>
<a name="getMpxCodePage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMpxCodePage</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CodePage.html" title="enum in org.mpxj">CodePage</a>&nbsp;getMpxCodePage()</pre>
<div class="block">Retrieves the codepage.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>code page type</dd>
</dl>
</li>
</ul>
<a name="setProjectFilePath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectFilePath</h4>
<pre>public&nbsp;void&nbsp;setProjectFilePath(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;projectFilePath)</pre>
<div class="block">Sets the project file path.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectFilePath</code> - project file path</dd>
</dl>
</li>
</ul>
<a name="getProjectFilePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectFilePath</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectFilePath()</pre>
<div class="block">Gets the project file path.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project file path</dd>
</dl>
</li>
</ul>
<a name="getFullApplicationName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFullApplicationName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFullApplicationName()</pre>
<div class="block">Retrieves the name of the application used to create this project data.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>application name</dd>
</dl>
</li>
</ul>
<a name="setFullApplicationName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFullApplicationName</h4>
<pre>public&nbsp;void&nbsp;setFullApplicationName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Sets the name of the application used to create this project data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - application name</dd>
</dl>
</li>
</ul>
<a name="getApplicationVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getApplicationVersion()</pre>
<div class="block">Retrieves the version of the application used to create this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>application name</dd>
</dl>
</li>
</ul>
<a name="setApplicationVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplicationVersion</h4>
<pre>public&nbsp;void&nbsp;setApplicationVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;version)</pre>
<div class="block">Sets the version of the application used to create this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>version</code> - application version</dd>
</dl>
</li>
</ul>
<a name="getMppFileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMppFileType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMppFileType()</pre>
<div class="block">This method retrieves a value representing the type of MPP file
 that has been read. Currently, this method will return the value 8 for
 an MPP8 file (Project 98), 9 for an MPP9 file (Project 2000 and
 Project 2002), 12 for an MPP12 file (Project 2003, Project 2007) and 14 for an
 MPP14 file (Project 2010 and Project 2013).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>integer representing the file type</dd>
</dl>
</li>
</ul>
<a name="setMppFileType-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMppFileType</h4>
<pre>public&nbsp;void&nbsp;setMppFileType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;fileType)</pre>
<div class="block">Used internally to set the file type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileType</code> - file type</dd>
</dl>
</li>
</ul>
<a name="getAutoFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoFilter</h4>
<pre>public&nbsp;boolean&nbsp;getAutoFilter()</pre>
<div class="block">Retrieve a flag indicating if auto filter is enabled.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>auto filter flag</dd>
</dl>
</li>
</ul>
<a name="setAutoFilter-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoFilter</h4>
<pre>public&nbsp;void&nbsp;setAutoFilter(boolean&nbsp;autoFilter)</pre>
<div class="block">Sets a flag indicating if auto filter is enabled.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>autoFilter</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getFileApplication--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileApplication</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFileApplication()</pre>
<div class="block">Retrieves the vendor of the file used to populate this ProjectFile instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>file type</dd>
</dl>
</li>
</ul>
<a name="setFileApplication-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileApplication</h4>
<pre>public&nbsp;void&nbsp;setFileApplication(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</pre>
<div class="block">Sets the vendor of file used to populate this ProjectFile instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - file type</dd>
</dl>
</li>
</ul>
<a name="getFileType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFileType()</pre>
<div class="block">Retrieves the type of file used to populate this ProjectFile instance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>file type</dd>
</dl>
</li>
</ul>
<a name="setFileType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFileType</h4>
<pre>public&nbsp;void&nbsp;setFileType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</pre>
<div class="block">Sets the type of file used to populate this ProjectFile instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - file type</dd>
</dl>
</li>
</ul>
<a name="getExportFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExportFlag</h4>
<pre>public&nbsp;boolean&nbsp;getExportFlag()</pre>
<div class="block">Retrieves the export flag used to specify if the project was chosen to export from P6.
 Projects that have external relationships may be included in an export, even when not
 specifically flagged in the export. This flag differentiates those projects</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>export boolean flag</dd>
</dl>
</li>
</ul>
<a name="setExportFlag-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExportFlag</h4>
<pre>public&nbsp;void&nbsp;setExportFlag(boolean&nbsp;value)</pre>
<div class="block">Sets the export flag to populate this ProjectFile instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getBaselineProjectUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineProjectUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaselineProjectUniqueID()</pre>
<div class="block">Retrieve the baseline project unique ID for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project unique ID</dd>
</dl>
</li>
</ul>
<a name="setBaselineProjectUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineProjectUniqueID</h4>
<pre>public&nbsp;void&nbsp;setBaselineProjectUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Set the baseline project unique ID for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - baseline project unique ID</dd>
</dl>
</li>
</ul>
<a name="getProjectID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectID()</pre>
<div class="block">Retrieve the project ID for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline project unique ID</dd>
</dl>
</li>
</ul>
<a name="setProjectID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectID</h4>
<pre>public&nbsp;void&nbsp;setProjectID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</pre>
<div class="block">Set the project ID for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - project ID</dd>
</dl>
</li>
</ul>
<a name="getCriticalActivityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCriticalActivityType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CriticalActivityType.html" title="enum in org.mpxj">CriticalActivityType</a>&nbsp;getCriticalActivityType()</pre>
<div class="block">Retrieve the critical activity type for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>critical activity type</dd>
</dl>
</li>
</ul>
<a name="setCriticalActivityType-org.mpxj.CriticalActivityType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCriticalActivityType</h4>
<pre>public&nbsp;void&nbsp;setCriticalActivityType(<a href="../../org/mpxj/CriticalActivityType.html" title="enum in org.mpxj">CriticalActivityType</a>&nbsp;value)</pre>
<div class="block">Set the critical activity type for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - critical activity type</dd>
</dl>
</li>
</ul>
<a name="setMustFinishBy-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMustFinishBy</h4>
<pre>public&nbsp;void&nbsp;setMustFinishBy(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Sets the must finish by date for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - must finish by date</dd>
</dl>
</li>
</ul>
<a name="getMustFinishBy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMustFinishBy</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getMustFinishBy()</pre>
<div class="block">Retrieves the must finish by date for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>must finish by date</dd>
</dl>
</li>
</ul>
<a name="setScheduledFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScheduledFinish</h4>
<pre>public&nbsp;void&nbsp;setScheduledFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Sets the scheduled finish by date for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - scheduled finish by date</dd>
</dl>
</li>
</ul>
<a name="getScheduledFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScheduledFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getScheduledFinish()</pre>
<div class="block">Retrieves the scheduled finish by date for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>scheduled finish by date</dd>
</dl>
</li>
</ul>
<a name="setPlannedStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedStart</h4>
<pre>public&nbsp;void&nbsp;setPlannedStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Sets the planned start by date for this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - planned start by date</dd>
</dl>
</li>
</ul>
<a name="getPlannedStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedStart()</pre>
<div class="block">Retrieves the planned start by date for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>planned start by date</dd>
</dl>
</li>
</ul>
<a name="getLocationUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLocationUniqueID()</pre>
<div class="block">Retrieves the location unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location unique ID</dd>
</dl>
</li>
</ul>
<a name="setLocationUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationUniqueID</h4>
<pre>public&nbsp;void&nbsp;setLocationUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the location unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - location unique ID</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;getLocation()</pre>
<div class="block">Retrieves the location.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location.</dd>
</dl>
</li>
</ul>
<a name="setLocation-org.mpxj.Location-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocation</h4>
<pre>public&nbsp;void&nbsp;setLocation(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</pre>
<div class="block">Sets the location.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - location</dd>
</dl>
</li>
</ul>
<a name="getResourcePoolFile--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourcePoolFile</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourcePoolFile()</pre>
<div class="block">Retrieve the resource pool file associated with this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource pool file</dd>
</dl>
</li>
</ul>
<a name="setResourcePoolFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourcePoolFile</h4>
<pre>public&nbsp;void&nbsp;setResourcePoolFile(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;file)</pre>
<div class="block">Set the resource pool file associated with this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - resource pool file</dd>
</dl>
</li>
</ul>
<a name="getResourcePoolObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourcePoolObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getResourcePoolObject()</pre>
<div class="block">Retrieve a ProjectFile instance representing the resource pool for this project
 Returns null if this project does not have a resource pool or the file cannot be read.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance for the resource pool</dd>
</dl>
</li>
</ul>
<a name="setTotalSlackCalculationType-org.mpxj.TotalSlackCalculationType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotalSlackCalculationType</h4>
<pre>public&nbsp;void&nbsp;setTotalSlackCalculationType(<a href="../../org/mpxj/TotalSlackCalculationType.html" title="enum in org.mpxj">TotalSlackCalculationType</a>&nbsp;type)</pre>
<div class="block">Set the total slack calculation type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - total slack calculation type</dd>
</dl>
</li>
</ul>
<a name="getTotalSlackCalculationType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalSlackCalculationType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/TotalSlackCalculationType.html" title="enum in org.mpxj">TotalSlackCalculationType</a>&nbsp;getTotalSlackCalculationType()</pre>
<div class="block">Retrieve the total slack calculation type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>total slack calculation type</dd>
</dl>
</li>
</ul>
<a name="setRelationshipLagCalendar-org.mpxj.RelationshipLagCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRelationshipLagCalendar</h4>
<pre>public&nbsp;void&nbsp;setRelationshipLagCalendar(<a href="../../org/mpxj/RelationshipLagCalendar.html" title="enum in org.mpxj">RelationshipLagCalendar</a>&nbsp;calendar)</pre>
<div class="block">Set the relationship lag calendar.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - relationship lag calendar</dd>
</dl>
</li>
</ul>
<a name="getRelationshipLagCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRelationshipLagCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/RelationshipLagCalendar.html" title="enum in org.mpxj">RelationshipLagCalendar</a>&nbsp;getRelationshipLagCalendar()</pre>
<div class="block">Retrieve the relationship lag calendar.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>relationship lag calendar</dd>
</dl>
</li>
</ul>
<a name="getWbsCodeSeparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWbsCodeSeparator</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWbsCodeSeparator()</pre>
<div class="block">Retrieve the WBS Code separator character.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>WBS Code separator character</dd>
</dl>
</li>
</ul>
<a name="setWbsCodeSeparator-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWbsCodeSeparator</h4>
<pre>public&nbsp;void&nbsp;setWbsCodeSeparator(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the WBS Code separator character.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - WBS Code separator character</dd>
</dl>
</li>
</ul>
<a name="getConsiderAssignmentsInOtherProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConsiderAssignmentsInOtherProjects</h4>
<pre>public&nbsp;boolean&nbsp;getConsiderAssignmentsInOtherProjects()</pre>
<div class="block">Retrieve the consider assignments in other projects when leveling flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>consider assignments in other projects flag</dd>
</dl>
</li>
</ul>
<a name="setConsiderAssignmentsInOtherProjects-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConsiderAssignmentsInOtherProjects</h4>
<pre>public&nbsp;void&nbsp;setConsiderAssignmentsInOtherProjects(boolean&nbsp;value)</pre>
<div class="block">Set the consider assignments in other projects when leveling flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - consider assignments in other projects fla</dd>
</dl>
</li>
</ul>
<a name="getConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan()</pre>
<div class="block">Retrieve the priority of assignment in other projects to consider when leveling.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>assignment priority</dd>
</dl>
</li>
</ul>
<a name="setConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan</h4>
<pre>public&nbsp;void&nbsp;setConsiderAssignmentsInOtherProjectsWithPriorityEqualHigherThan(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the priority of assignment in other projects to consider when leveling.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - assignment priority</dd>
</dl>
</li>
</ul>
<a name="getPreserveScheduledEarlyAndLateDates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreserveScheduledEarlyAndLateDates</h4>
<pre>public&nbsp;boolean&nbsp;getPreserveScheduledEarlyAndLateDates()</pre>
<div class="block">Retrieve the preserve scheduled early and late dates flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>preserve scheduled early and late dates flag</dd>
</dl>
</li>
</ul>
<a name="setPreserveScheduledEarlyAndLateDates-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreserveScheduledEarlyAndLateDates</h4>
<pre>public&nbsp;void&nbsp;setPreserveScheduledEarlyAndLateDates(boolean&nbsp;value)</pre>
<div class="block">Set the preserve scheduled early and late dates flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - preserve scheduled early and late dates flag</dd>
</dl>
</li>
</ul>
<a name="getLevelAllResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelAllResources</h4>
<pre>public&nbsp;boolean&nbsp;getLevelAllResources()</pre>
<div class="block">Retrieve the level all resources flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>level all resources flag</dd>
</dl>
</li>
</ul>
<a name="setLevelAllResources-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelAllResources</h4>
<pre>public&nbsp;void&nbsp;setLevelAllResources(boolean&nbsp;value)</pre>
<div class="block">Set the level all resources flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - level all resources flag</dd>
</dl>
</li>
</ul>
<a name="getLevelResourcesOnlyWithinActivityTotalFloat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelResourcesOnlyWithinActivityTotalFloat</h4>
<pre>public&nbsp;boolean&nbsp;getLevelResourcesOnlyWithinActivityTotalFloat()</pre>
<div class="block">Retrieve the level resources only within activity total float flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>level resources only within activity total float flag</dd>
</dl>
</li>
</ul>
<a name="setLevelResourcesOnlyWithinActivityTotalFloat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelResourcesOnlyWithinActivityTotalFloat</h4>
<pre>public&nbsp;void&nbsp;setLevelResourcesOnlyWithinActivityTotalFloat(boolean&nbsp;value)</pre>
<div class="block">Set the level resources only within activity total float flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - level resources only within activity total float flag</dd>
</dl>
</li>
</ul>
<a name="getPreserveMinimumFloatWhenLeveling--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreserveMinimumFloatWhenLeveling</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getPreserveMinimumFloatWhenLeveling()</pre>
<div class="block">Retrieve the preserve minimum float when leveling value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>float to preserve when leveling</dd>
</dl>
</li>
</ul>
<a name="setPreserveMinimumFloatWhenLeveling-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreserveMinimumFloatWhenLeveling</h4>
<pre>public&nbsp;void&nbsp;setPreserveMinimumFloatWhenLeveling(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the preserve minimum float when leveling value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - float to preserve when leveling</dd>
</dl>
</li>
</ul>
<a name="getMaxPercentToOverallocateResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxPercentToOverallocateResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getMaxPercentToOverallocateResources()</pre>
<div class="block">Retrieve the maximum percentage to overallocate resources.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>maximum percentage to overallocate resources</dd>
</dl>
</li>
</ul>
<a name="setMaxPercentToOverallocateResources-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxPercentToOverallocateResources</h4>
<pre>public&nbsp;void&nbsp;setMaxPercentToOverallocateResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the maximum percentage to overallocate resources.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - maximum percentage to overallocate resources</dd>
</dl>
</li>
</ul>
<a name="getLevelingPriorities--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevelingPriorities</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLevelingPriorities()</pre>
<div class="block">Retrieve the leveling priorities expression.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>leveling priorities expression</dd>
</dl>
</li>
</ul>
<a name="setLevelingPriorities-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelingPriorities</h4>
<pre>public&nbsp;void&nbsp;setLevelingPriorities(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the leveling priorities expression.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - leveling priorities expression</dd>
</dl>
</li>
</ul>
<a name="getDataDateAndPlannedStartSetToProjectForecastStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataDateAndPlannedStartSetToProjectForecastStart</h4>
<pre>public&nbsp;boolean&nbsp;getDataDateAndPlannedStartSetToProjectForecastStart()</pre>
<div class="block">Retrieve the data date and planned start set to project forecast start flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>data date and planned start set to project forecast start flag</dd>
</dl>
</li>
</ul>
<a name="setDataDateAndPlannedStartSetToProjectForecastStart-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDataDateAndPlannedStartSetToProjectForecastStart</h4>
<pre>public&nbsp;void&nbsp;setDataDateAndPlannedStartSetToProjectForecastStart(boolean&nbsp;value)</pre>
<div class="block">Set the data date and planned start set to project forecast start flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - data date and planned start set to project forecast start flag</dd>
</dl>
</li>
</ul>
<a name="getIgnoreRelationshipsToAndFromOtherProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIgnoreRelationshipsToAndFromOtherProjects</h4>
<pre>public&nbsp;boolean&nbsp;getIgnoreRelationshipsToAndFromOtherProjects()</pre>
<div class="block">Retrieve the ignore relationships to and from other projects flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ignore relationships to and from other projects flag</dd>
</dl>
</li>
</ul>
<a name="setIgnoreRelationshipsToAndFromOtherProjects-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIgnoreRelationshipsToAndFromOtherProjects</h4>
<pre>public&nbsp;void&nbsp;setIgnoreRelationshipsToAndFromOtherProjects(boolean&nbsp;value)</pre>
<div class="block">Set the ignore relationships to and from other projects flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - ignore relationships to and from other projects flag</dd>
</dl>
</li>
</ul>
<a name="getMakeOpenEndedActivitiesCritical--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMakeOpenEndedActivitiesCritical</h4>
<pre>public&nbsp;boolean&nbsp;getMakeOpenEndedActivitiesCritical()</pre>
<div class="block">Retrieve the mark open-ended activities as critical flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>mark open-ended activities as critical flag</dd>
</dl>
</li>
</ul>
<a name="setMakeOpenEndedActivitiesCritical-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMakeOpenEndedActivitiesCritical</h4>
<pre>public&nbsp;void&nbsp;setMakeOpenEndedActivitiesCritical(boolean&nbsp;value)</pre>
<div class="block">Set the mark open-ended activities as critical flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - mark open-ended activities as critical flag</dd>
</dl>
</li>
</ul>
<a name="getUseExpectedFinishDates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUseExpectedFinishDates</h4>
<pre>public&nbsp;boolean&nbsp;getUseExpectedFinishDates()</pre>
<div class="block">Retrieve the use expected finish dates flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>use expected finish dates flag</dd>
</dl>
</li>
</ul>
<a name="setUseExpectedFinishDates-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseExpectedFinishDates</h4>
<pre>public&nbsp;void&nbsp;setUseExpectedFinishDates(boolean&nbsp;value)</pre>
<div class="block">Set the use expected finish dates flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - use expected finish dates flag</dd>
</dl>
</li>
</ul>
<a name="getComputeStartToStartLagFromEarlyStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComputeStartToStartLagFromEarlyStart</h4>
<pre>public&nbsp;boolean&nbsp;getComputeStartToStartLagFromEarlyStart()</pre>
<div class="block">Retrieve the compute start to start lag from early start flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start to start lag from early start flag</dd>
</dl>
</li>
</ul>
<a name="setComputeStartToStartLagFromEarlyStart-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComputeStartToStartLagFromEarlyStart</h4>
<pre>public&nbsp;void&nbsp;setComputeStartToStartLagFromEarlyStart(boolean&nbsp;value)</pre>
<div class="block">Set the compute start to start lag from early start flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - compute start to start lag from early start flag</dd>
</dl>
</li>
</ul>
<a name="getCalculateFloatBasedOnFinishDateOfEachProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateFloatBasedOnFinishDateOfEachProject</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateFloatBasedOnFinishDateOfEachProject()</pre>
<div class="block">Set the calculate float based on finish date of each project flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculate float based on finish date of each project flag</dd>
</dl>
</li>
</ul>
<a name="setCalculateFloatBasedOnFinishDateOfEachProject-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateFloatBasedOnFinishDateOfEachProject</h4>
<pre>public&nbsp;void&nbsp;setCalculateFloatBasedOnFinishDateOfEachProject(boolean&nbsp;value)</pre>
<div class="block">Set the calculate float based on finish date of each project flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - calculate float based on finish date of each project flag</dd>
</dl>
</li>
</ul>
<a name="getCalculateMultipleFloatPaths--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateMultipleFloatPaths</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateMultipleFloatPaths()</pre>
<div class="block">Get the calculate multiple float paths flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculate multiple float paths flag</dd>
</dl>
</li>
</ul>
<a name="setCalculateMultipleFloatPaths-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateMultipleFloatPaths</h4>
<pre>public&nbsp;void&nbsp;setCalculateMultipleFloatPaths(boolean&nbsp;value)</pre>
<div class="block">Set the calculate multiple float paths flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - calculate multiple float paths flag</dd>
</dl>
</li>
</ul>
<a name="getCalculateMultipleFloatPathsUsingTotalFloat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateMultipleFloatPathsUsingTotalFloat</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateMultipleFloatPathsUsingTotalFloat()</pre>
<div class="block">Retrieve the calculate multiple float paths using total float flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculate multiple float paths using total float flag</dd>
</dl>
</li>
</ul>
<a name="setCalculateMultipleFloatPathsUsingTotalFloat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateMultipleFloatPathsUsingTotalFloat</h4>
<pre>public&nbsp;void&nbsp;setCalculateMultipleFloatPathsUsingTotalFloat(boolean&nbsp;value)</pre>
<div class="block">Set the calculate multiple float paths using total float flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - calculate multiple float paths using total float flag</dd>
</dl>
</li>
</ul>
<a name="getDisplayMultipleFloatPathsEndingWithActivityUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisplayMultipleFloatPathsEndingWithActivityUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDisplayMultipleFloatPathsEndingWithActivityUniqueID()</pre>
<div class="block">Retrieve the display multiple float paths ending with activity unique ID value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>display multiple float paths ending with activity unique ID value</dd>
</dl>
</li>
</ul>
<a name="setDisplayMultipleFloatPathsEndingWithActivityUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisplayMultipleFloatPathsEndingWithActivityUniqueID</h4>
<pre>public&nbsp;void&nbsp;setDisplayMultipleFloatPathsEndingWithActivityUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the display multiple float paths ending with activity unique ID value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - display multiple float paths ending with activity unique ID value</dd>
</dl>
</li>
</ul>
<a name="getLimitNumberOfFloatPathsToCalculate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLimitNumberOfFloatPathsToCalculate</h4>
<pre>public&nbsp;boolean&nbsp;getLimitNumberOfFloatPathsToCalculate()</pre>
<div class="block">Retrieve the limit number of paths to calculate flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>limit number of paths to calculate flag</dd>
</dl>
</li>
</ul>
<a name="setLimitNumberOfFloatPathsToCalculate-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLimitNumberOfFloatPathsToCalculate</h4>
<pre>public&nbsp;void&nbsp;setLimitNumberOfFloatPathsToCalculate(boolean&nbsp;value)</pre>
<div class="block">Set the limit number of paths to calculate flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - limit number of paths to calculate flag</dd>
</dl>
</li>
</ul>
<a name="getMaximumNumberOfFloatPathsToCalculate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaximumNumberOfFloatPathsToCalculate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMaximumNumberOfFloatPathsToCalculate()</pre>
<div class="block">Retrieve the maximum number of float paths to calculate.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>maximum number of float paths to calculate.</dd>
</dl>
</li>
</ul>
<a name="setSchedulingProgressedActivities-org.mpxj.SchedulingProgressedActivities-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSchedulingProgressedActivities</h4>
<pre>public&nbsp;void&nbsp;setSchedulingProgressedActivities(<a href="../../org/mpxj/SchedulingProgressedActivities.html" title="enum in org.mpxj">SchedulingProgressedActivities</a>&nbsp;value)</pre>
<div class="block">Set the method used when scheduling progressed activities.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - scheduling progressed activities method</dd>
</dl>
</li>
</ul>
<a name="getSchedulingProgressedActivities--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSchedulingProgressedActivities</h4>
<pre>public&nbsp;<a href="../../org/mpxj/SchedulingProgressedActivities.html" title="enum in org.mpxj">SchedulingProgressedActivities</a>&nbsp;getSchedulingProgressedActivities()</pre>
<div class="block">Retrieve the method used when scheduling progressed activities.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>maximum number of float paths to calculate.</dd>
</dl>
</li>
</ul>
<a name="setMaximumNumberOfFloatPathsToCalculate-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaximumNumberOfFloatPathsToCalculate</h4>
<pre>public&nbsp;void&nbsp;setMaximumNumberOfFloatPathsToCalculate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the maximum number of float paths to calculate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - maximum number of float paths to calculate</dd>
</dl>
</li>
</ul>
<a name="getBaselineTypeName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineTypeName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineTypeName()</pre>
<div class="block">Retrieve the name of the baseline type associated with this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline type name</dd>
</dl>
</li>
</ul>
<a name="setBaselineTypeName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineTypeName</h4>
<pre>public&nbsp;void&nbsp;setBaselineTypeName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the name of the baseline type associated with this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline type name</dd>
</dl>
</li>
</ul>
<a name="getBaselineTypeUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineTypeUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaselineTypeUniqueID()</pre>
<div class="block">Retrieve the unique ID of the baseline type associated with this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline type unique ID</dd>
</dl>
</li>
</ul>
<a name="setBaselineTypeUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineTypeUniqueID</h4>
<pre>public&nbsp;void&nbsp;setBaselineTypeUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the unique ID of the baseline type associated with this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline type unique ID</dd>
</dl>
</li>
</ul>
<a name="getLastBaselineUpdateDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastBaselineUpdateDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLastBaselineUpdateDate()</pre>
<div class="block">Retrieve the last baseline update date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>last baseline update date</dd>
</dl>
</li>
</ul>
<a name="setLastBaselineUpdateDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastBaselineUpdateDate</h4>
<pre>public&nbsp;void&nbsp;setLastBaselineUpdateDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set the last baseline update date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - last baseline update date</dd>
</dl>
</li>
</ul>
<a name="getActivityIdPrefix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityIdPrefix</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityIdPrefix()</pre>
<div class="block">Retrieve the prefix used when creating an Activity ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity ID prefix</dd>
</dl>
</li>
</ul>
<a name="setActivityIdPrefix-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityIdPrefix</h4>
<pre>public&nbsp;void&nbsp;setActivityIdPrefix(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the prefix used when creating an Activity ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity ID prefix</dd>
</dl>
</li>
</ul>
<a name="getActivityIdSuffix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityIdSuffix</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getActivityIdSuffix()</pre>
<div class="block">Retrieve the suffix used when creating an Activity ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity ID suffix</dd>
</dl>
</li>
</ul>
<a name="setActivityIdSuffix-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityIdSuffix</h4>
<pre>public&nbsp;void&nbsp;setActivityIdSuffix(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the suffix used when creating an Activity ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity ID suffix</dd>
</dl>
</li>
</ul>
<a name="getActivityIdIncrement--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityIdIncrement</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getActivityIdIncrement()</pre>
<div class="block">Retrieve the increment used when creating Activity ID values.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>activity ID increment</dd>
</dl>
</li>
</ul>
<a name="setActivityIdIncrement-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityIdIncrement</h4>
<pre>public&nbsp;void&nbsp;setActivityIdIncrement(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Set the increment used when creating Activity ID values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - activity ID increment</dd>
</dl>
</li>
</ul>
<a name="getActivityIdIncrementBasedOnSelectedActivity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityIdIncrementBasedOnSelectedActivity</h4>
<pre>public&nbsp;boolean&nbsp;getActivityIdIncrementBasedOnSelectedActivity()</pre>
<div class="block">Retrieve the "increment activity ID based on selected activity" flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>"increment activity ID based on selected activity" flag</dd>
</dl>
</li>
</ul>
<a name="setActivityIdIncrementBasedOnSelectedActivity-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityIdIncrementBasedOnSelectedActivity</h4>
<pre>public&nbsp;void&nbsp;setActivityIdIncrementBasedOnSelectedActivity(boolean&nbsp;value)</pre>
<div class="block">Set the "increment activity ID based on selected activity" flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - "increment activity ID based on selected activity" flag</dd>
</dl>
</li>
</ul>
<a name="getBaselineCalendarName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCalendarName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaselineCalendarName()</pre>
<div class="block">Set the baseline calendar name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline calendar name</dd>
</dl>
</li>
</ul>
<a name="setBaselineCalendarName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCalendarName</h4>
<pre>public&nbsp;void&nbsp;setBaselineCalendarName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Retrieve the baseline calendar name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline calendar name</dd>
</dl>
</li>
</ul>
<a name="getProjectIsBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectIsBaseline</h4>
<pre>public&nbsp;boolean&nbsp;getProjectIsBaseline()</pre>
<div class="block">Returns true if this ProjectFile instance represents a baseline.
 This is useful where readers can return a list of all
 schedules from a data source which may include both projects
 and baselines.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this ProjectFile instance represents a baseline</dd>
</dl>
</li>
</ul>
<a name="setProjectIsBaseline-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectIsBaseline</h4>
<pre>public&nbsp;void&nbsp;setProjectIsBaseline(boolean&nbsp;value)</pre>
<div class="block">Set the flag to indicate if this ProjectFile instance
 represents a baseline.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - true if this ProjectFile instance represents a baseline</dd>
</dl>
</li>
</ul>
<a name="getProjectWebsiteUrl--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectWebsiteUrl</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectWebsiteUrl()</pre>
<div class="block">Retrieve the project website URL.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project website URL</dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Retrieve the project notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project notes</dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</pre>
<div class="block">Set the project notes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - project notes</dd>
</dl>
</li>
</ul>
<a name="getNotesObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;getNotesObject()</pre>
<div class="block">Retrieve the project notes object.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>project notes object</dd>
</dl>
</li>
</ul>
<a name="setNotesObject-org.mpxj.Notes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotesObject</h4>
<pre>public&nbsp;void&nbsp;setNotesObject(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</pre>
<div class="block">Set the project notes object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - project notes object</dd>
</dl>
</li>
</ul>
<a name="setProjectWebsiteUrl-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectWebsiteUrl</h4>
<pre>public&nbsp;void&nbsp;setProjectWebsiteUrl(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the project website URL.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - project website url</dd>
</dl>
</li>
</ul>
<a name="getProjectCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectCodeValues</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ProjectCode.html" title="class in org.mpxj">ProjectCode</a>,<a href="../../org/mpxj/ProjectCodeValue.html" title="class in org.mpxj">ProjectCodeValue</a>&gt;&nbsp;getProjectCodeValues()</pre>
<div class="block">Retrieve the project code values associated with this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>map of project code values</dd>
</dl>
</li>
</ul>
<a name="addProjectCodeValue-org.mpxj.ProjectCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectCodeValue</h4>
<pre>public&nbsp;void&nbsp;addProjectCodeValue(<a href="../../org/mpxj/ProjectCodeValue.html" title="class in org.mpxj">ProjectCodeValue</a>&nbsp;value)</pre>
<div class="block">Assign a project code value to this project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - project code value</dd>
</dl>
</li>
</ul>
<a name="getEnablePublication--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnablePublication</h4>
<pre>public&nbsp;boolean&nbsp;getEnablePublication()</pre>
<div class="block">Retrieve the enable publication flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enable publication flag</dd>
</dl>
</li>
</ul>
<a name="setEnablePublication-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnablePublication</h4>
<pre>public&nbsp;void&nbsp;setEnablePublication(boolean&nbsp;value)</pre>
<div class="block">Set the enable publication flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - enable publication flag</dd>
</dl>
</li>
</ul>
<a name="getEnableSummarization--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnableSummarization</h4>
<pre>public&nbsp;boolean&nbsp;getEnableSummarization()</pre>
<div class="block">Retrieve the enable summarization flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enable summarization flag</dd>
</dl>
</li>
</ul>
<a name="setEnableSummarization-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableSummarization</h4>
<pre>public&nbsp;void&nbsp;setEnableSummarization(boolean&nbsp;value)</pre>
<div class="block">Set the enable summarization flg.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - enable summarization flag</dd>
</dl>
</li>
</ul>
<a name="getPopulatedFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPopulatedFields</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</a>&lt;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&gt;&nbsp;getPopulatedFields()</pre>
<div class="block">Retrieve the set of populated fields for this project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>set of populated fields</dd>
</dl>
</li>
</ul>
<a name="getParentFile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentFile</h4>
<pre>public final&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getParentFile()</pre>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>reference to this the parent ProjectFile instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectProperties.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ProjectFileSharedData.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectTimeFormat.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ProjectProperties.html" target="_top">Frames</a></li>
<li><a href="ProjectProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
