<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LocalDateTimeHelper (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocalDateTimeHelper (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocalDateTimeHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/LocalDateHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/LocalTimeHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/LocalDateTimeHelper.html" target="_top">Frames</a></li>
<li><a href="LocalDateTimeHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.common</div>
<h2 title="Class LocalDateTimeHelper" class="title">Class LocalDateTimeHelper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.common.LocalDateTimeHelper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">LocalDateTimeHelper</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Common code for working with LocalDateTime instances.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#END_DATE_NA">END_DATE_NA</a></span></code>
<div class="block">Date representing NA at the end of a date range: Friday December 31 23:59:00 2049.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#START_DATE_NA">START_DATE_NA</a></span></code>
<div class="block">Date representing NA at the start of a date range: January 01 00:00:00 1984.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#LocalDateTimeHelper--">LocalDateTimeHelper</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#compare-java.time.LocalDateTime-java.time.LocalDateTime-">compare</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</code>
<div class="block">Compare two dates, handling null values.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#compare-java.time.LocalDateTime-java.time.LocalDateTime-java.time.LocalDateTime-">compare</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;endDate,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;targetDate)</code>
<div class="block">This method compares a target date with a date range.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#getDayStartDate-java.time.LocalDateTime-">getDayStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</code>
<div class="block">Returns a new Date instance whose value
 represents the start of the day (i.e.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#getVariance-org.mpxj.ProjectCalendar-java.time.LocalDateTime-java.time.LocalDateTime-org.mpxj.TimeUnit-">getVariance</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date1,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date2,
           <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;format)</code>
<div class="block">This utility method calculates the difference in working
 time between two dates, given the context of a calendar.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#max-java.time.LocalDateTime-java.time.LocalDateTime-">max</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</code>
<div class="block">Returns the later of two dates, handling null values.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#min-java.time.LocalDateTime-java.time.LocalDateTime-">min</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</code>
<div class="block">Returns the earlier of two dates, handling null values.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/LocalDateTimeHelper.html#parseBest-java.time.format.DateTimeFormatter-java.lang.String-">parseBest</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html?is-external=true" title="class or interface in java.time.format">DateTimeFormatter</a>&nbsp;format,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Use the parseBest method of the formatter to retrieve a LocalDateTime instance
 handling the case where the formatter returns a LocalDate instance.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="START_DATE_NA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_DATE_NA</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> START_DATE_NA</pre>
<div class="block">Date representing NA at the start of a date range: January 01 00:00:00 1984.</div>
</li>
</ul>
<a name="END_DATE_NA">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>END_DATE_NA</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> END_DATE_NA</pre>
<div class="block">Date representing NA at the end of a date range: Friday December 31 23:59:00 2049.
 That's actually the value used by older versions of MS Project. The most recent version
 of MS Project uses Friday December 31 23:59:06 2049 (note the six extra seconds).
 The problem with using this value to represent NA at the end of a date range is it
 isn't interpreted correctly by older versions of MS Project. The compromise here is that
 we'll use the value recognised by older versions of MS Project, which will work as expected
 and display NA as the end date. For the current version of MS Project this will display a
 the end date as 2049, rather than NA, but this should still be interpreted correctly.
 TODO: consider making this behaviour configurable.</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="LocalDateTimeHelper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LocalDateTimeHelper</h4>
<pre>public&nbsp;LocalDateTimeHelper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDayStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDayStartDate</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDayStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date)</pre>
<div class="block">Returns a new Date instance whose value
 represents the start of the day (i.e. the time of day is 00:00:00.000)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>date</code> - date to convert</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>day start date</dd>
</dl>
</li>
</ul>
<a name="compare-java.time.LocalDateTime-java.time.LocalDateTime-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compare</h4>
<pre>public static&nbsp;int&nbsp;compare(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;startDate,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;endDate,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;targetDate)</pre>
<div class="block">This method compares a target date with a date range. The method will
 return 0 if the date is within the range, less than zero if the date
 is before the range starts, and greater than zero if the date is after
 the range ends.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>startDate</code> - range start date</dd>
<dd><code>endDate</code> - range end date</dd>
<dd><code>targetDate</code> - target date in milliseconds</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>comparison result</dd>
</dl>
</li>
</ul>
<a name="compare-java.time.LocalDateTime-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compare</h4>
<pre>public static&nbsp;int&nbsp;compare(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</pre>
<div class="block">Compare two dates, handling null values.
 TODO: correct the comparison order to align with Date.compareTo</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>d1</code> - Date instance</dd>
<dd><code>d2</code> - Date instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int comparison result</dd>
</dl>
</li>
</ul>
<a name="min-java.time.LocalDateTime-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>min</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;min(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</pre>
<div class="block">Returns the earlier of two dates, handling null values. A non-null Date
 is always considered to be earlier than a null Date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>d1</code> - Date instance</dd>
<dd><code>d2</code> - Date instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date earliest date</dd>
</dl>
</li>
</ul>
<a name="max-java.time.LocalDateTime-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>max</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;max(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d1,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;d2)</pre>
<div class="block">Returns the later of two dates, handling null values. A non-null Date
 is always considered to be later than a null Date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>d1</code> - Date instance</dd>
<dd><code>d2</code> - Date instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Date latest date</dd>
</dl>
</li>
</ul>
<a name="parseBest-java.time.format.DateTimeFormatter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseBest</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;parseBest(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html?is-external=true" title="class or interface in java.time.format">DateTimeFormatter</a>&nbsp;format,
                                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Use the parseBest method of the formatter to retrieve a LocalDateTime instance
 handling the case where the formatter returns a LocalDate instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>format</code> - DateTimeFormatter instance</dd>
<dd><code>value</code> - value to parse</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>LocalDateTime instance</dd>
</dl>
</li>
</ul>
<a name="getVariance-org.mpxj.ProjectCalendar-java.time.LocalDateTime-java.time.LocalDateTime-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getVariance</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getVariance(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar,
                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date1,
                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;date2,
                                   <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;format)</pre>
<div class="block">This utility method calculates the difference in working
 time between two dates, given the context of a calendar.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar</dd>
<dd><code>date1</code> - first date</dd>
<dd><code>date2</code> - second date</dd>
<dd><code>format</code> - required format for the resulting duration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>difference in working time between the two dates</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocalDateTimeHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/LocalDateHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/LocalTimeHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/LocalDateTimeHelper.html" target="_top">Frames</a></li>
<li><a href="LocalDateTimeHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
