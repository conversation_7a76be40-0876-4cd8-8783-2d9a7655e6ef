<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.mpxj (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.mpxj (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.mpxj" class="title">Uses of Package<br>org.mpxj</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../org/mpxj/package-summary.html">org.mpxj</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.asta">org.mpxj.asta</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw">org.mpxj.conceptdraw</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw.schema">org.mpxj.conceptdraw.schema</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.cpm">org.mpxj.cpm</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.edrawproject">org.mpxj.edrawproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.explorer">org.mpxj.explorer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.fasttrack">org.mpxj.fasttrack</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.ganttdesigner">org.mpxj.ganttdesigner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.ganttdesigner.schema">org.mpxj.ganttdesigner.schema</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.ganttproject">org.mpxj.ganttproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.json">org.mpxj.json</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.listener">org.mpxj.listener</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.merlin">org.mpxj.merlin</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpd">org.mpxj.mpd</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpx">org.mpxj.mpx</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mspdi.schema">org.mpxj.mspdi.schema</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.openplan">org.mpxj.openplan</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.phoenix">org.mpxj.phoenix</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.planner">org.mpxj.planner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera">org.mpxj.primavera</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.common">org.mpxj.primavera.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera.p3">org.mpxj.primavera.p3</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.suretrak">org.mpxj.primavera.suretrak</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.projectcommander">org.mpxj.projectcommander</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.projectlibre">org.mpxj.projectlibre</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.reader">org.mpxj.reader</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.sage">org.mpxj.sage</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.sdef">org.mpxj.sdef</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.synchro">org.mpxj.synchro</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.turboproject">org.mpxj.turboproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.utility">org.mpxj.utility</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.writer">org.mpxj.writer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/package-summary.html">org.mpxj</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AbstractBaselineStrategy.html#org.mpxj">AbstractBaselineStrategy</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AbstractFieldContainer.html#org.mpxj">AbstractFieldContainer</a>
<div class="block">Implementation of common functionality for the FieldContainer interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AccrueType.html#org.mpxj">AccrueType</a>
<div class="block">This class is used to represent an accrue type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCode.html#org.mpxj">ActivityCode</a>
<div class="block">Activity code type definition, contains a list of the valid
 values for this activity code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCode.Builder.html#org.mpxj">ActivityCode.Builder</a>
<div class="block">ActivityCode builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCodeContainer.html#org.mpxj">ActivityCodeContainer</a>
<div class="block">Container for activity code definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCodeScope.html#org.mpxj">ActivityCodeScope</a>
<div class="block">Represents the scope of an activity code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCodeValue.html#org.mpxj">ActivityCodeValue</a>
<div class="block">Represents an individual activity code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityCodeValue.Builder.html#org.mpxj">ActivityCodeValue.Builder</a>
<div class="block">ActivityCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityStatus.html#org.mpxj">ActivityStatus</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ActivityType.html#org.mpxj">ActivityType</a>
<div class="block">P6/PPX Activity type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AssignmentField.html#org.mpxj">AssignmentField</a>
<div class="block">Instances of this type represent Assignment fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Availability.html#org.mpxj">Availability</a>
<div class="block">This class represents a row from a resource's availability table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AvailabilityTable.html#org.mpxj">AvailabilityTable</a>
<div class="block">This class represents a resource's availability table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BaselineStrategy.html#org.mpxj">BaselineStrategy</a>
<div class="block">Classes implementing this interface manage population of baseline attributes
 in one schedule by comparing it to another schedule.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BookingType.html#org.mpxj">BookingType</a>
<div class="block">Enumeration representing booking types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CalendarType.html#org.mpxj">CalendarType</a>
<div class="block">P6 Calendar Types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ChildResourceContainer.html#org.mpxj">ChildResourceContainer</a>
<div class="block">Interface implemented by classes which have child resources.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ChildTaskContainer.html#org.mpxj">ChildTaskContainer</a>
<div class="block">Interface implemented by classes which have child tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Code.html#org.mpxj">Code</a>
<div class="block">Interface implemented by classes representing Primavera P6 codes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CodePage.html#org.mpxj">CodePage</a>
<div class="block">Instances of this class represent enumerated code page values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CodeValue.html#org.mpxj">CodeValue</a>
<div class="block">Implemented by classes which represent a value forming part of a Primavera P6 code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Column.html#org.mpxj">Column</a>
<div class="block">This class represents a column in an MS Project table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ConstraintField.html#org.mpxj">ConstraintField</a>
<div class="block">Instances of this type represent constraint fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ConstraintType.html#org.mpxj">ConstraintType</a>
<div class="block">This class is used to represent a constraint type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CostAccount.html#org.mpxj">CostAccount</a>
<div class="block">Cost account definition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CostAccount.Builder.html#org.mpxj">CostAccount.Builder</a>
<div class="block">CostAccount builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CostAccountContainer.html#org.mpxj">CostAccountContainer</a>
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CostRateTable.html#org.mpxj">CostRateTable</a>
<div class="block">This class represents a resource's cost rate table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CostRateTableEntry.html#org.mpxj">CostRateTableEntry</a>
<div class="block">This class represents a row from a resource's cost rate table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CriticalActivityType.html#org.mpxj">CriticalActivityType</a>
<div class="block">Determines how the critical flag is set for tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Currency.html#org.mpxj">Currency</a>
<div class="block">Represents a currency.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Currency.Builder.html#org.mpxj">Currency.Builder</a>
<div class="block">Currency builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencyContainer.html#org.mpxj">CurrencyContainer</a>
<div class="block">Represents the currencies available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomField.html#org.mpxj">CustomField</a>
<div class="block">Configuration detail for a field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomFieldContainer.html#org.mpxj">CustomFieldContainer</a>
<div class="block">Container holding configuration details for all custom fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomFieldLookupTable.html#org.mpxj">CustomFieldLookupTable</a>
<div class="block">Lookup table definition for a custom field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomFieldValueDataType.html#org.mpxj">CustomFieldValueDataType</a>
<div class="block">Enumeration used  by custom field value items to represent their data type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomFieldValueMask.html#org.mpxj">CustomFieldValueMask</a>
<div class="block">One element of the mask used to define the structured content of a custom field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataLinkContainer.html#org.mpxj">DataLinkContainer</a>
<div class="block">Manages the data link definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataType.html#org.mpxj">DataType</a>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DateOrder.html#org.mpxj">DateOrder</a>
<div class="block">Instances of this class represent enumerated date order values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DayType.html#org.mpxj">DayType</a>
<div class="block">This class is used to represent the day type used by the project calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DefaultBaselineStrategy.html#org.mpxj">DefaultBaselineStrategy</a>
<div class="block">Handles setting baseline fields in one project using values read from another project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EarnedValueMethod.html#org.mpxj">EarnedValueMethod</a>
<div class="block">Instances of this class represent enumerated earned value method values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EPS.html#org.mpxj">EPS</a>
<div class="block">Represents the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EpsNode.html#org.mpxj">EpsNode</a>
<div class="block">Represents a node in the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EpsProjectNode.html#org.mpxj">EpsProjectNode</a>
<div class="block">Represents a project node in the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EventManager.html#org.mpxj">EventManager</a>
<div class="block">Provides subscriptions to events raised when project files are written and read.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ExpenseCategory.html#org.mpxj">ExpenseCategory</a>
<div class="block">Expense category definition.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ExpenseCategory.Builder.html#org.mpxj">ExpenseCategory.Builder</a>
<div class="block">ExpenseCategory builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ExpenseCategoryContainer.html#org.mpxj">ExpenseCategoryContainer</a>
<div class="block">Container for expense categories.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ExpenseItem.html#org.mpxj">ExpenseItem</a>
<div class="block">Expense item definition.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ExpenseItem.Builder.html#org.mpxj">ExpenseItem.Builder</a>
<div class="block">Expense item builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldContainer.html#org.mpxj">FieldContainer</a>
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldTypeClass.html#org.mpxj">FieldTypeClass</a>
<div class="block">Represents the type of entity to which a FieldType instance can belong.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FileVersion.html#org.mpxj">FileVersion</a>
<div class="block">Instances of this class represent enumerated file version values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Filter.html#org.mpxj">Filter</a>
<div class="block">This class represents a filter which may be applied to a
 task or resource view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FilterContainer.html#org.mpxj">FilterContainer</a>
<div class="block">Manages filter definitions belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GenericCriteria.html#org.mpxj">GenericCriteria</a>
<div class="block">This class represents the criteria used as part of an evaluation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GenericCriteriaPrompt.html#org.mpxj">GenericCriteriaPrompt</a>
<div class="block">Represents a prompt to the user as part of filter criteria.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GraphicalIndicator.html#org.mpxj">GraphicalIndicator</a>
<div class="block">This class represents the set of information which defines how
 a Graphical Indicator will be presented for a single column in
 a table within Microsoft Project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GraphicalIndicatorCriteria.html#org.mpxj">GraphicalIndicatorCriteria</a>
<div class="block">This class represents the criteria used to determine if a graphical
 indicator is displayed in place of an attribute value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Group.html#org.mpxj">Group</a>
<div class="block">This class represents the definition of the grouping used
 to organise data in a view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GroupClause.html#org.mpxj">GroupClause</a>
<div class="block">This class represents a clause from a definition of a group.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GroupContainer.html#org.mpxj">GroupContainer</a>
<div class="block">Manages the group definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ListWithCallbacks.html#org.mpxj">ListWithCallbacks</a>
<div class="block">Class implementing a list interface, backed by an ArrayList instance with callbacks
 which can be overridden by subclasses for notification of added and removed items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/LocalDateRange.html#org.mpxj">LocalDateRange</a>
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/LocalDateTimeRange.html#org.mpxj">LocalDateTimeRange</a>
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/LocalTimeRange.html#org.mpxj">LocalTimeRange</a>
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Location.html#org.mpxj">Location</a>
<div class="block">Represents a location, use to tag projects, resources and activities.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Location.Builder.html#org.mpxj">Location.Builder</a>
<div class="block">Location builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/LocationContainer.html#org.mpxj">LocationContainer</a>
<div class="block">Represents the locations available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MpxjEnum.html#org.mpxj">MpxjEnum</a>
<div class="block">This interface defines the common features of enums used by MPXJ.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Notes.html#org.mpxj">Notes</a>
<div class="block">Represents plain text notes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/NotesTopic.html#org.mpxj">NotesTopic</a>
<div class="block">Represents a topic, used by P6 to organise notes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/NotesTopic.Builder.html#org.mpxj">NotesTopic.Builder</a>
<div class="block">NotesTopic builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/NotesTopicContainer.html#org.mpxj">NotesTopicContainer</a>
<div class="block">Represents the notes topics available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/PercentCompleteType.html#org.mpxj">PercentCompleteType</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Priority.html#org.mpxj">Priority</a>
<div class="block">This class is used to represent a priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarContainer.html#org.mpxj">ProjectCalendarContainer</a>
<div class="block">Manages the collection of calendars belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarDays.html#org.mpxj">ProjectCalendarDays</a>
<div class="block">This class represents a basic working week, with no exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarException.html#org.mpxj">ProjectCalendarException</a>
<div class="block">This class represents instances of Calendar Exception records from
 an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarHours.html#org.mpxj">ProjectCalendarHours</a>
<div class="block">This class is used to represent the records in an MPX file that define
 working hours in a calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarWeek.html#org.mpxj">ProjectCalendarWeek</a>
<div class="block">This class represents a basic working week, with no exceptions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCode.html#org.mpxj">ProjectCode</a>
<div class="block">Project code type definition, contains a list of the valid
 values for this project code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCode.Builder.html#org.mpxj">ProjectCode.Builder</a>
<div class="block">ProjectCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCodeContainer.html#org.mpxj">ProjectCodeContainer</a>
<div class="block">Container for project code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCodeValue.html#org.mpxj">ProjectCodeValue</a>
<div class="block">Represents an individual project code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCodeValue.Builder.html#org.mpxj">ProjectCodeValue.Builder</a>
<div class="block">ProjectCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectConfig.html#org.mpxj">ProjectConfig</a>
<div class="block">Container for configuration details used to control the behaviour of the ProjectFile class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectDateFormat.html#org.mpxj">ProjectDateFormat</a>
<div class="block">Instances of this class represent enumerated date format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityContainer.html#org.mpxj">ProjectEntityContainer</a>
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithID.html#org.mpxj">ProjectEntityWithID</a>
<div class="block">Implemented by entities which can be identified by an ID.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithIDContainer.html#org.mpxj">ProjectEntityWithIDContainer</a>
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithMutableUniqueID.html#org.mpxj">ProjectEntityWithMutableUniqueID</a>
<div class="block">Implemented by entities which can be identified by a mutable Unique ID.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithUniqueID.html#org.mpxj">ProjectEntityWithUniqueID</a>
<div class="block">Implemented by entities which can be identified by a Unique ID.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectField.html#org.mpxj">ProjectField</a>
<div class="block">Instances of this type represent project properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFileSharedData.html#org.mpxj">ProjectFileSharedData</a>
<div class="block">Implements a container for common data which can be shared across multiple ProjectFile instances.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectProperties.html#org.mpxj">ProjectProperties</a>
<div class="block">This class represents a collection of properties relevant to the whole project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectTimeFormat.html#org.mpxj">ProjectTimeFormat</a>
<div class="block">Instances of this class represent enumerated time format values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Rate.html#org.mpxj">Rate</a>
<div class="block">This class represents a currency rate per period of time (for example $10/h)
 as found in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RateSource.html#org.mpxj">RateSource</a>
<div class="block">Represents the source of cost rate for a resource assignment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RecurrenceType.html#org.mpxj">RecurrenceType</a>
<div class="block">Represents the recurrence type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RecurringData.html#org.mpxj">RecurringData</a>
<div class="block">This class provides a description of a recurring event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RecurringTask.html#org.mpxj">RecurringTask</a>
<div class="block">This class represents the Recurring Task Record as found in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Relation.html#org.mpxj">Relation</a>
<div class="block">This class represents the relationship between two tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Relation.Builder.html#org.mpxj">Relation.Builder</a>
<div class="block">Relation builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationContainer.html#org.mpxj">RelationContainer</a>
<div class="block">Represents Relation instances from the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationshipLagCalendar.html#org.mpxj">RelationshipLagCalendar</a>
<div class="block">Represents the calendar to be used when making date calculations
 using the relationship lag value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationType.html#org.mpxj">RelationType</a>
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Resource.html#org.mpxj">Resource</a>
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignment.html#org.mpxj">ResourceAssignment</a>
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentCode.html#org.mpxj">ResourceAssignmentCode</a>
<div class="block">ResourceAssignment code type definition, contains a list of the valid
 values for this assignment code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentCode.Builder.html#org.mpxj">ResourceAssignmentCode.Builder</a>
<div class="block">ResourceAssignmentCode builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentCodeContainer.html#org.mpxj">ResourceAssignmentCodeContainer</a>
<div class="block">Container for assignment code definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentCodeValue.html#org.mpxj">ResourceAssignmentCodeValue</a>
<div class="block">Represents an individual assignment code value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentCodeValue.Builder.html#org.mpxj">ResourceAssignmentCodeValue.Builder</a>
<div class="block">ResourceAssignmentCodeValue builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentContainer.html#org.mpxj">ResourceAssignmentContainer</a>
<div class="block">Manages the collection of resource assignments belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignmentWorkgroupFields.html#org.mpxj">ResourceAssignmentWorkgroupFields</a>
<div class="block">This class represents a resource assignment workgroup fields record
 from an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceCode.html#org.mpxj">ResourceCode</a>
<div class="block">Resource code type definition, contains a list of the valid
 values for this resource code.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceCode.Builder.html#org.mpxj">ResourceCode.Builder</a>
<div class="block">ResourceCode builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceCodeContainer.html#org.mpxj">ResourceCodeContainer</a>
<div class="block">Container for resource code definitions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceCodeValue.html#org.mpxj">ResourceCodeValue</a>
<div class="block">Represents an individual resource code value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceCodeValue.Builder.html#org.mpxj">ResourceCodeValue.Builder</a>
<div class="block">ResourceCodeValue builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceContainer.html#org.mpxj">ResourceContainer</a>
<div class="block">Manages the collection of resources belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceField.html#org.mpxj">ResourceField</a>
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceRequestType.html#org.mpxj">ResourceRequestType</a>
<div class="block">Instances of this class represent enumerated resource request type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceType.html#org.mpxj">ResourceType</a>
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RoleCode.html#org.mpxj">RoleCode</a>
<div class="block">Role code type definition, contains a list of the valid
 values for this role code.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RoleCode.Builder.html#org.mpxj">RoleCode.Builder</a>
<div class="block">RoleCode builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RoleCodeContainer.html#org.mpxj">RoleCodeContainer</a>
<div class="block">Container for role code definitions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RoleCodeValue.html#org.mpxj">RoleCodeValue</a>
<div class="block">Represents an individual role code value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RoleCodeValue.Builder.html#org.mpxj">RoleCodeValue.Builder</a>
<div class="block">RoleCodeValue builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ScheduleFrom.html#org.mpxj">ScheduleFrom</a>
<div class="block">Instances of this class represent enumerated schedule from values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/SchedulingProgressedActivities.html#org.mpxj">SchedulingProgressedActivities</a>
<div class="block">Represents the method used to schedule progressed activities.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Shift.html#org.mpxj">Shift</a>
<div class="block">Represents a Resource Shift.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Shift.Builder.html#org.mpxj">Shift.Builder</a>
<div class="block">Shift builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ShiftContainer.html#org.mpxj">ShiftContainer</a>
<div class="block">Represents the shifts available to the current project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ShiftPeriod.html#org.mpxj">ShiftPeriod</a>
<div class="block">Represents a Resource Shift Period.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ShiftPeriod.Builder.html#org.mpxj">ShiftPeriod.Builder</a>
<div class="block">ShiftPeriod builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ShiftPeriodContainer.html#org.mpxj">ShiftPeriodContainer</a>
<div class="block">Represents the shift periods available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/SkillLevel.html#org.mpxj">SkillLevel</a>
<div class="block">Represents the skill level of a resource
 when assigned to a role.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Step.html#org.mpxj">Step</a>
<div class="block">Represents an activity step.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Step.Builder.html#org.mpxj">Step.Builder</a>
<div class="block">Step builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Table.html#org.mpxj">Table</a>
<div class="block">This class represents the definition of a table of data from an MPP file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TableContainer.html#org.mpxj">TableContainer</a>
<div class="block">Manages the table definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskContainer.html#org.mpxj">TaskContainer</a>
<div class="block">Manages the collection of tasks belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskField.html#org.mpxj">TaskField</a>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskMode.html#org.mpxj">TaskMode</a>
<div class="block">Represents task mode values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskType.html#org.mpxj">TaskType</a>
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TestOperator.html#org.mpxj">TestOperator</a>
<div class="block">This class represents the set of operators used to perform a test
 between two or more operands.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimePeriodEntity.html#org.mpxj">TimePeriodEntity</a>
<div class="block">Classes implementing this interface represent a period of time
 between a start LocalDateTime and a finish LocalDateTime.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCost.html#org.mpxj">TimephasedCost</a>
<div class="block">Represents timephased cost.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCostContainer.html#org.mpxj">TimephasedCostContainer</a>
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedItem.html#org.mpxj">TimephasedItem</a>
<div class="block">This class represents an amount, spread over a period of time.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWork.html#org.mpxj">TimephasedWork</a>
<div class="block">Represents timephased work.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWorkContainer.html#org.mpxj">TimephasedWorkContainer</a>
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnitDefaultsContainer.html#org.mpxj">TimeUnitDefaultsContainer</a>
<div class="block">Classes implementing this interface provide access to the defaults used
 when converting duration time units.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TotalSlackCalculationType.html#org.mpxj">TotalSlackCalculationType</a>
<div class="block">Enum representing the different calculation types which can be used to determine total slack.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UniqueIdObjectSequenceProvider.html#org.mpxj">UniqueIdObjectSequenceProvider</a>
<div class="block">Classes implementing this interface provide a method which allows
 unique ID object sequences to be retrieved for the requested class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UnitOfMeasure.html#org.mpxj">UnitOfMeasure</a>
<div class="block">Class representing a unit of measure.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UnitOfMeasure.Builder.html#org.mpxj">UnitOfMeasure.Builder</a>
<div class="block">Unit of measure builder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UnitOfMeasureContainer.html#org.mpxj">UnitOfMeasureContainer</a>
<div class="block">Represents units of measure available to the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UserDefinedField.html#org.mpxj">UserDefinedField</a>
<div class="block">Represents a user defined field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UserDefinedField.Builder.html#org.mpxj">UserDefinedField.Builder</a>
<div class="block">User defined field builder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UserDefinedFieldContainer.html#org.mpxj">UserDefinedFieldContainer</a>
<div class="block">Manages the collection of user defined fields belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ViewContainer.html#org.mpxj">ViewContainer</a>
<div class="block">Manages the sub projects belonging to a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ViewState.html#org.mpxj">ViewState</a>
<div class="block">This class represents the state of a view which has been saved
 as part of a project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ViewType.html#org.mpxj">ViewType</a>
<div class="block">This class represents the enumeration of the valid types of view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkContour.html#org.mpxj">WorkContour</a>
<div class="block">Instances of this class represent enumerated work contour values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkContourContainer.html#org.mpxj">WorkContourContainer</a>
<div class="block">Represents the work contours (resource curves) used by the current project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkGroup.html#org.mpxj">WorkGroup</a>
<div class="block">Instances of this class represent enumerated work group values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.asta">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AbstractBaselineStrategy.html#org.mpxj.asta">AbstractBaselineStrategy</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BaselineStrategy.html#org.mpxj.asta">BaselineStrategy</a>
<div class="block">Classes implementing this interface manage population of baseline attributes
 in one schedule by comparing it to another schedule.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.asta">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.asta">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.asta">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/common/package-summary.html">org.mpxj.common</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AssignmentField.html#org.mpxj.common">AssignmentField</a>
<div class="block">Instances of this type represent Assignment fields.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataType.html#org.mpxj.common">DataType</a>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.common">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldContainer.html#org.mpxj.common">FieldContainer</a>
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj.common">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj.common">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarDays.html#org.mpxj.common">ProjectCalendarDays</a>
<div class="block">This class represents a basic working week, with no exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarException.html#org.mpxj.common">ProjectCalendarException</a>
<div class="block">This class represents instances of Calendar Exception records from
 an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendarHours.html#org.mpxj.common">ProjectCalendarHours</a>
<div class="block">This class is used to represent the records in an MPX file that define
 working hours in a calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityContainer.html#org.mpxj.common">ProjectEntityContainer</a>
<div class="block">Common implementation shared by project entities, providing storage, iteration and lookup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithMutableUniqueID.html#org.mpxj.common">ProjectEntityWithMutableUniqueID</a>
<div class="block">Implemented by entities which can be identified by a mutable Unique ID.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectEntityWithUniqueID.html#org.mpxj.common">ProjectEntityWithUniqueID</a>
<div class="block">Implemented by entities which can be identified by a Unique ID.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectField.html#org.mpxj.common">ProjectField</a>
<div class="block">Instances of this type represent project properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.common">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Rate.html#org.mpxj.common">Rate</a>
<div class="block">This class represents a currency rate per period of time (for example $10/h)
 as found in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Resource.html#org.mpxj.common">Resource</a>
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignment.html#org.mpxj.common">ResourceAssignment</a>
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceField.html#org.mpxj.common">ResourceField</a>
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.common">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskField.html#org.mpxj.common">TaskField</a>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimePeriodEntity.html#org.mpxj.common">TimePeriodEntity</a>
<div class="block">Classes implementing this interface represent a period of time
 between a start LocalDateTime and a finish LocalDateTime.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCost.html#org.mpxj.common">TimephasedCost</a>
<div class="block">Represents timephased cost.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCostContainer.html#org.mpxj.common">TimephasedCostContainer</a>
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWork.html#org.mpxj.common">TimephasedWork</a>
<div class="block">Represents timephased work.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWorkContainer.html#org.mpxj.common">TimephasedWorkContainer</a>
<div class="block">Timephased data container.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.common">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnitDefaultsContainer.html#org.mpxj.common">TimeUnitDefaultsContainer</a>
<div class="block">Classes implementing this interface provide access to the defaults used
 when converting duration time units.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/UserDefinedField.html#org.mpxj.common">UserDefinedField</a>
<div class="block">Represents a user defined field.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj.conceptdraw">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.conceptdraw">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Priority.html#org.mpxj.conceptdraw">Priority</a>
<div class="block">This class is used to represent a priority.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.conceptdraw">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationType.html#org.mpxj.conceptdraw">RelationType</a>
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceType.html#org.mpxj.conceptdraw">ResourceType</a>
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskType.html#org.mpxj.conceptdraw">TaskType</a>
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.conceptdraw">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw.schema">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/conceptdraw/schema/package-summary.html">org.mpxj.conceptdraw.schema</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj.conceptdraw.schema">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Priority.html#org.mpxj.conceptdraw.schema">Priority</a>
<div class="block">This class is used to represent a priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationType.html#org.mpxj.conceptdraw.schema">RelationType</a>
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceType.html#org.mpxj.conceptdraw.schema">ResourceType</a>
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskType.html#org.mpxj.conceptdraw.schema">TaskType</a>
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.conceptdraw.schema">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.cpm">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/cpm/package-summary.html">org.mpxj.cpm</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.cpm">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.cpm">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.edrawproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.edrawproject">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.edrawproject">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.explorer">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/explorer/package-summary.html">org.mpxj.explorer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.explorer">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.fasttrack">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/fasttrack/package-summary.html">org.mpxj.fasttrack</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.fasttrack">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.fasttrack">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttdesigner">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.ganttdesigner">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.ganttdesigner">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.ganttdesigner">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttdesigner.schema">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/ganttdesigner/schema/package-summary.html">org.mpxj.ganttdesigner.schema</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.ganttdesigner.schema">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.ganttproject">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.ganttproject">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.json">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/json/package-summary.html">org.mpxj.json</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.json">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.json">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.listener">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/listener/package-summary.html">org.mpxj.listener</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldContainer.html#org.mpxj.listener">FieldContainer</a>
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj.listener">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj.listener">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Relation.html#org.mpxj.listener">Relation</a>
<div class="block">This class represents the relationship between two tasks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Resource.html#org.mpxj.listener">Resource</a>
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceAssignment.html#org.mpxj.listener">ResourceAssignment</a>
<div class="block">This class represents a resource assignment record from an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.listener">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.merlin">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/merlin/package-summary.html">org.mpxj.merlin</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.merlin">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.merlin">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpd">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/mpd/package-summary.html">org.mpxj.mpd</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj.mpd">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.mpd">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.mpd">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.mpd">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.mpd">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj.mpp">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CustomFieldValueDataType.html#org.mpxj.mpp">CustomFieldValueDataType</a>
<div class="block">Enumeration used  by custom field value items to represent their data type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataType.html#org.mpxj.mpp">DataType</a>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.mpp">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldContainer.html#org.mpxj.mpp">FieldContainer</a>
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj.mpp">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Filter.html#org.mpxj.mpp">Filter</a>
<div class="block">This class represents a filter which may be applied to a
 task or resource view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GenericCriteria.html#org.mpxj.mpp">GenericCriteria</a>
<div class="block">This class represents the criteria used as part of an evaluation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/GenericCriteriaPrompt.html#org.mpxj.mpp">GenericCriteriaPrompt</a>
<div class="block">Represents a prompt to the user as part of filter criteria.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MpxjEnum.html#org.mpxj.mpp">MpxjEnum</a>
<div class="block">This interface defines the common features of enums used by MPXJ.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.mpp">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj.mpp">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.mpp">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectProperties.html#org.mpxj.mpp">ProjectProperties</a>
<div class="block">This class represents a collection of properties relevant to the whole project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Resource.html#org.mpxj.mpp">Resource</a>
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceField.html#org.mpxj.mpp">ResourceField</a>
<div class="block">Instances of this type represent Resource fields.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RtfNotes.html#org.mpxj.mpp">RtfNotes</a>
<div class="block">Represents notes formatted as RTF.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Table.html#org.mpxj.mpp">Table</a>
<div class="block">This class represents the definition of a table of data from an MPP file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TableContainer.html#org.mpxj.mpp">TableContainer</a>
<div class="block">Manages the table definitions belonging to a project.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskType.html#org.mpxj.mpp">TaskType</a>
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimePeriodEntity.html#org.mpxj.mpp">TimePeriodEntity</a>
<div class="block">Classes implementing this interface represent a period of time
 between a start LocalDateTime and a finish LocalDateTime.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCost.html#org.mpxj.mpp">TimephasedCost</a>
<div class="block">Represents timephased cost.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWork.html#org.mpxj.mpp">TimephasedWork</a>
<div class="block">Represents timephased work.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimeUnit.html#org.mpxj.mpp">TimeUnit</a>
<div class="block">This class contains utility functions allowing time unit specifications
 to be parsed and formatted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/View.html#org.mpxj.mpp">View</a>
<div class="block">This interface represents a view of a set of project data that has been
 instantiated within an MS Project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ViewType.html#org.mpxj.mpp">ViewType</a>
<div class="block">This class represents the enumeration of the valid types of view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkContour.html#org.mpxj.mpp">WorkContour</a>
<div class="block">Instances of this class represent enumerated work contour values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpx">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.mpx">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.mpx">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.mpx">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskField.html#org.mpxj.mpx">TaskField</a>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AccrueType.html#org.mpxj.mspdi">AccrueType</a>
<div class="block">This class is used to represent an accrue type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BookingType.html#org.mpxj.mspdi">BookingType</a>
<div class="block">Enumeration representing booking types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ConstraintType.html#org.mpxj.mspdi">ConstraintType</a>
<div class="block">This class is used to represent a constraint type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataType.html#org.mpxj.mspdi">DataType</a>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EarnedValueMethod.html#org.mpxj.mspdi">EarnedValueMethod</a>
<div class="block">Instances of this class represent enumerated earned value method values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldContainer.html#org.mpxj.mspdi">FieldContainer</a>
<div class="block">This interface is implemented by the Task and Resource classes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj.mspdi">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/HasCharset.html#org.mpxj.mspdi">HasCharset</a>
<div class="block">This interface is implemented by reader classes which allow
 a Charset to be specified before reading.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.mspdi">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj.mspdi">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.mspdi">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectProperties.html#org.mpxj.mspdi">ProjectProperties</a>
<div class="block">This class represents a collection of properties relevant to the whole project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Rate.html#org.mpxj.mspdi">Rate</a>
<div class="block">This class represents a currency rate per period of time (for example $10/h)
 as found in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Resource.html#org.mpxj.mspdi">Resource</a>
<div class="block">This class represents a resource used in a project.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimePeriodEntity.html#org.mpxj.mspdi">TimePeriodEntity</a>
<div class="block">Classes implementing this interface represent a period of time
 between a start LocalDateTime and a finish LocalDateTime.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWork.html#org.mpxj.mspdi">TimephasedWork</a>
<div class="block">Represents timephased work.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkContour.html#org.mpxj.mspdi">WorkContour</a>
<div class="block">Instances of this class represent enumerated work contour values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkGroup.html#org.mpxj.mspdi">WorkGroup</a>
<div class="block">Instances of this class represent enumerated work group values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi.schema">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/mspdi/schema/package-summary.html">org.mpxj.mspdi.schema</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AccrueType.html#org.mpxj.mspdi.schema">AccrueType</a>
<div class="block">This class is used to represent an accrue type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BookingType.html#org.mpxj.mspdi.schema">BookingType</a>
<div class="block">Enumeration representing booking types.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/CurrencySymbolPosition.html#org.mpxj.mspdi.schema">CurrencySymbolPosition</a>
<div class="block">Instances of this class represent enumerated currency symbol position values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ResourceType.html#org.mpxj.mspdi.schema">ResourceType</a>
<div class="block">Instances of this class represent enumerated resource type values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskType.html#org.mpxj.mspdi.schema">TaskType</a>
<div class="block">Instances of this enum task type values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkContour.html#org.mpxj.mspdi.schema">WorkContour</a>
<div class="block">Instances of this class represent enumerated work contour values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/WorkGroup.html#org.mpxj.mspdi.schema">WorkGroup</a>
<div class="block">Instances of this class represent enumerated work group values.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.openplan">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.openplan">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.openplan">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.phoenix">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.phoenix">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.phoenix">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.planner">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.planner">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.planner">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/AbstractBaselineStrategy.html#org.mpxj.primavera">AbstractBaselineStrategy</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/BaselineStrategy.html#org.mpxj.primavera">BaselineStrategy</a>
<div class="block">Classes implementing this interface manage population of baseline attributes
 in one schedule by comparing it to another schedule.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/DataType.html#org.mpxj.primavera">DataType</a>
<div class="block">This class represents the data type of an attribute.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/EPS.html#org.mpxj.primavera">EPS</a>
<div class="block">Represents the Enterprise Project Structure from a P6 database.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/FieldType.html#org.mpxj.primavera">FieldType</a>
<div class="block">This interface is implemented by classes which represent a field
 in a Task, Resource or Assignment entity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/HasCharset.html#org.mpxj.primavera">HasCharset</a>
<div class="block">This interface is implemented by reader classes which allow
 a Charset to be specified before reading.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.primavera">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.primavera">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Task.html#org.mpxj.primavera">Task</a>
<div class="block">This class represents a task record from a project file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TaskField.html#org.mpxj.primavera">TaskField</a>
<div class="block">Instances of this type represent Task fields.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.common">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/primavera/common/package-summary.html">org.mpxj.primavera.common</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.primavera.common">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/RelationType.html#org.mpxj.primavera.common">RelationType</a>
<div class="block">This class is used to represent a relation type.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.p3">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.primavera.p3">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.primavera.p3">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.suretrak">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.primavera.suretrak">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.primavera.suretrak">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectcommander">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.projectcommander">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.projectcommander">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectlibre">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.projectlibre">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.projectlibre">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.reader">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.reader">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.reader">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sage">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.sage">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.sage">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sdef">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/HasCharset.html#org.mpxj.sdef">HasCharset</a>
<div class="block">This interface is implemented by reader classes which allow
 a Charset to be specified before reading.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.sdef">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.sdef">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.synchro">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.synchro">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.synchro">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.turboproject">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.turboproject">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.turboproject">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.utility">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/utility/package-summary.html">org.mpxj.utility</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/Duration.html#org.mpxj.utility">Duration</a>
<div class="block">This represents time durations as specified in an MPX file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/LocalDateTimeRange.html#org.mpxj.utility">LocalDateTimeRange</a>
<div class="block">This class represents a period of time.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/MPXJException.html#org.mpxj.utility">MPXJException</a>
<div class="block">Standard exception type thrown by the MPXJ library.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectCalendar.html#org.mpxj.utility">ProjectCalendar</a>
<div class="block">This class represents a Calendar Definition record.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedCost.html#org.mpxj.utility">TimephasedCost</a>
<div class="block">Represents timephased cost.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../org/mpxj/class-use/TimephasedWork.html#org.mpxj.utility">TimephasedWork</a>
<div class="block">Represents timephased work.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.writer">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../org/mpxj/package-summary.html">org.mpxj</a> used by <a href="../../org/mpxj/writer/package-summary.html">org.mpxj.writer</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../org/mpxj/class-use/ProjectFile.html#org.mpxj.writer">ProjectFile</a>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
