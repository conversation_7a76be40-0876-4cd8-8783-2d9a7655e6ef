<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TaskField (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TaskField (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TaskContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/TaskField.html" target="_top">Frames</a></li>
<li><a href="TaskField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Enum TaskField" class="title">Enum TaskField</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.TaskField</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&gt;, <a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>, <a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">TaskField</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&gt;
implements <a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></pre>
<div class="block">Instances of this type represent Task fields.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVE">ACTIVE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVITY_CODE_VALUES">ACTIVITY_CODE_VALUES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVITY_ID">ACTIVITY_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVITY_PERCENT_COMPLETE">ACTIVITY_PERCENT_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVITY_STATUS">ACTIVITY_STATUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTIVITY_TYPE">ACTIVITY_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_COST">ACTUAL_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_DURATION">ACTUAL_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_DURATION_UNITS">ACTUAL_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_FINISH">ACTUAL_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_OVERTIME_COST">ACTUAL_OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_OVERTIME_WORK">ACTUAL_OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_OVERTIME_WORK_PROTECTED">ACTUAL_OVERTIME_WORK_PROTECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_START">ACTUAL_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_WORK">ACTUAL_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_WORK_LABOR">ACTUAL_WORK_LABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_WORK_NONLABOR">ACTUAL_WORK_NONLABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACTUAL_WORK_PROTECTED">ACTUAL_WORK_PROTECTED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ACWP">ACWP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ASSIGNMENT">ASSIGNMENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ASSIGNMENT_DELAY">ASSIGNMENT_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ASSIGNMENT_OWNER">ASSIGNMENT_OWNER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ASSIGNMENT_UNITS">ASSIGNMENT_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BAR_NAME">BAR_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_BUDGET_COST">BASELINE_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_BUDGET_WORK">BASELINE_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_COST">BASELINE_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_DELIVERABLE_FINISH">BASELINE_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_DELIVERABLE_START">BASELINE_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_DURATION">BASELINE_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_DURATION_ESTIMATED">BASELINE_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_DURATION_UNITS">BASELINE_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_ESTIMATED_DURATION">BASELINE_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_ESTIMATED_FINISH">BASELINE_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_ESTIMATED_START">BASELINE_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_FINISH">BASELINE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_FIXED_COST">BASELINE_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_FIXED_COST_ACCRUAL">BASELINE_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_START">BASELINE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE_WORK">BASELINE_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_BUDGET_COST">BASELINE1_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_BUDGET_WORK">BASELINE1_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_COST">BASELINE1_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_DELIVERABLE_FINISH">BASELINE1_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_DELIVERABLE_START">BASELINE1_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_DURATION">BASELINE1_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_DURATION_ESTIMATED">BASELINE1_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_DURATION_UNITS">BASELINE1_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_ESTIMATED_DURATION">BASELINE1_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_ESTIMATED_FINISH">BASELINE1_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_ESTIMATED_START">BASELINE1_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_FINISH">BASELINE1_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_FIXED_COST">BASELINE1_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_FIXED_COST_ACCRUAL">BASELINE1_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_START">BASELINE1_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE1_WORK">BASELINE1_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_BUDGET_COST">BASELINE10_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_BUDGET_WORK">BASELINE10_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_COST">BASELINE10_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_DELIVERABLE_FINISH">BASELINE10_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_DELIVERABLE_START">BASELINE10_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_DURATION">BASELINE10_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_DURATION_ESTIMATED">BASELINE10_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_DURATION_UNITS">BASELINE10_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_ESTIMATED_DURATION">BASELINE10_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_ESTIMATED_FINISH">BASELINE10_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_ESTIMATED_START">BASELINE10_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_FINISH">BASELINE10_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_FIXED_COST">BASELINE10_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_FIXED_COST_ACCRUAL">BASELINE10_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_START">BASELINE10_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE10_WORK">BASELINE10_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_BUDGET_COST">BASELINE2_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_BUDGET_WORK">BASELINE2_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_COST">BASELINE2_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_DELIVERABLE_FINISH">BASELINE2_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_DELIVERABLE_START">BASELINE2_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_DURATION">BASELINE2_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_DURATION_ESTIMATED">BASELINE2_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_DURATION_UNITS">BASELINE2_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_ESTIMATED_DURATION">BASELINE2_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_ESTIMATED_FINISH">BASELINE2_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_ESTIMATED_START">BASELINE2_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_FINISH">BASELINE2_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_FIXED_COST">BASELINE2_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_FIXED_COST_ACCRUAL">BASELINE2_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_START">BASELINE2_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE2_WORK">BASELINE2_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_BUDGET_COST">BASELINE3_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_BUDGET_WORK">BASELINE3_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_COST">BASELINE3_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_DELIVERABLE_FINISH">BASELINE3_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_DELIVERABLE_START">BASELINE3_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_DURATION">BASELINE3_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_DURATION_ESTIMATED">BASELINE3_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_DURATION_UNITS">BASELINE3_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_ESTIMATED_DURATION">BASELINE3_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_ESTIMATED_FINISH">BASELINE3_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_ESTIMATED_START">BASELINE3_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_FINISH">BASELINE3_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_FIXED_COST">BASELINE3_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_FIXED_COST_ACCRUAL">BASELINE3_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_START">BASELINE3_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE3_WORK">BASELINE3_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_BUDGET_COST">BASELINE4_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_BUDGET_WORK">BASELINE4_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_COST">BASELINE4_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_DELIVERABLE_FINISH">BASELINE4_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_DELIVERABLE_START">BASELINE4_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_DURATION">BASELINE4_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_DURATION_ESTIMATED">BASELINE4_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_DURATION_UNITS">BASELINE4_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_ESTIMATED_DURATION">BASELINE4_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_ESTIMATED_FINISH">BASELINE4_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_ESTIMATED_START">BASELINE4_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_FINISH">BASELINE4_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_FIXED_COST">BASELINE4_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_FIXED_COST_ACCRUAL">BASELINE4_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_START">BASELINE4_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE4_WORK">BASELINE4_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_BUDGET_COST">BASELINE5_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_BUDGET_WORK">BASELINE5_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_COST">BASELINE5_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_DELIVERABLE_FINISH">BASELINE5_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_DELIVERABLE_START">BASELINE5_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_DURATION">BASELINE5_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_DURATION_ESTIMATED">BASELINE5_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_DURATION_UNITS">BASELINE5_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_ESTIMATED_DURATION">BASELINE5_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_ESTIMATED_FINISH">BASELINE5_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_ESTIMATED_START">BASELINE5_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_FINISH">BASELINE5_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_FIXED_COST">BASELINE5_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_FIXED_COST_ACCRUAL">BASELINE5_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_START">BASELINE5_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE5_WORK">BASELINE5_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_BUDGET_COST">BASELINE6_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_BUDGET_WORK">BASELINE6_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_COST">BASELINE6_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_DELIVERABLE_FINISH">BASELINE6_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_DELIVERABLE_START">BASELINE6_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_DURATION">BASELINE6_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_DURATION_ESTIMATED">BASELINE6_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_DURATION_UNITS">BASELINE6_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_ESTIMATED_DURATION">BASELINE6_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_ESTIMATED_FINISH">BASELINE6_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_ESTIMATED_START">BASELINE6_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_FINISH">BASELINE6_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_FIXED_COST">BASELINE6_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_FIXED_COST_ACCRUAL">BASELINE6_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_START">BASELINE6_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE6_WORK">BASELINE6_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_BUDGET_COST">BASELINE7_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_BUDGET_WORK">BASELINE7_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_COST">BASELINE7_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_DELIVERABLE_FINISH">BASELINE7_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_DELIVERABLE_START">BASELINE7_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_DURATION">BASELINE7_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_DURATION_ESTIMATED">BASELINE7_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_DURATION_UNITS">BASELINE7_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_ESTIMATED_DURATION">BASELINE7_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_ESTIMATED_FINISH">BASELINE7_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_ESTIMATED_START">BASELINE7_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_FINISH">BASELINE7_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_FIXED_COST">BASELINE7_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_FIXED_COST_ACCRUAL">BASELINE7_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_START">BASELINE7_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE7_WORK">BASELINE7_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_BUDGET_COST">BASELINE8_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_BUDGET_WORK">BASELINE8_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_COST">BASELINE8_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_DELIVERABLE_FINISH">BASELINE8_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_DELIVERABLE_START">BASELINE8_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_DURATION">BASELINE8_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_DURATION_ESTIMATED">BASELINE8_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_DURATION_UNITS">BASELINE8_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_ESTIMATED_DURATION">BASELINE8_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_ESTIMATED_FINISH">BASELINE8_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_ESTIMATED_START">BASELINE8_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_FINISH">BASELINE8_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_FIXED_COST">BASELINE8_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_FIXED_COST_ACCRUAL">BASELINE8_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_START">BASELINE8_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE8_WORK">BASELINE8_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_BUDGET_COST">BASELINE9_BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_BUDGET_WORK">BASELINE9_BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_COST">BASELINE9_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_DELIVERABLE_FINISH">BASELINE9_DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_DELIVERABLE_START">BASELINE9_DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_DURATION">BASELINE9_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_DURATION_ESTIMATED">BASELINE9_DURATION_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_DURATION_UNITS">BASELINE9_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_ESTIMATED_DURATION">BASELINE9_ESTIMATED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_ESTIMATED_FINISH">BASELINE9_ESTIMATED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_ESTIMATED_START">BASELINE9_ESTIMATED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_FINISH">BASELINE9_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_FIXED_COST">BASELINE9_FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_FIXED_COST_ACCRUAL">BASELINE9_FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_START">BASELINE9_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BASELINE9_WORK">BASELINE9_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BCWP">BCWP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BCWS">BCWS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BID_ITEM">BID_ITEM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BOARD_STATUS">BOARD_STATUS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BOARD_STATUS_ID">BOARD_STATUS_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BUDGET_COST">BUDGET_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#BUDGET_WORK">BUDGET_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CALENDAR_UNIQUE_ID">CALENDAR_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CATEGORY_OF_WORK">CATEGORY_OF_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COMPLETE_THROUGH">COMPLETE_THROUGH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CONFIRMED">CONFIRMED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CONSTRAINT_DATE">CONSTRAINT_DATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CONSTRAINT_TYPE">CONSTRAINT_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CONTACT">CONTACT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST">COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST_RATE_TABLE">COST_RATE_TABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST_VARIANCE">COST_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST1">COST1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST10">COST10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST2">COST2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST3">COST3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST4">COST4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST5">COST5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST6">COST6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST7">COST7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST8">COST8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#COST9">COST9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CPI">CPI</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CREATED">CREATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CRITICAL">CRITICAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CV">CV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#CVPERCENT">CVPERCENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE1">DATE1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE10">DATE10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE2">DATE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE3">DATE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE4">DATE4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE5">DATE5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE6">DATE6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE7">DATE7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE8">DATE8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DATE9">DATE9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DEADLINE">DEADLINE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DELIVERABLE_FINISH">DELIVERABLE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DELIVERABLE_GUID">DELIVERABLE_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DELIVERABLE_NAME">DELIVERABLE_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DELIVERABLE_START">DELIVERABLE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DELIVERABLE_TYPE">DELIVERABLE_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DEPARTMENT">DEPARTMENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION">DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION_TEXT">DURATION_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION_UNITS">DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION_VARIANCE">DURATION_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION1">DURATION1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION1_ESTIMATED">DURATION1_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION1_UNITS">DURATION1_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION10">DURATION10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION10_ESTIMATED">DURATION10_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION10_UNITS">DURATION10_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION2">DURATION2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION2_ESTIMATED">DURATION2_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION2_UNITS">DURATION2_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION3">DURATION3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION3_ESTIMATED">DURATION3_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION3_UNITS">DURATION3_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION4">DURATION4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION4_ESTIMATED">DURATION4_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION4_UNITS">DURATION4_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION5">DURATION5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION5_ESTIMATED">DURATION5_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION5_UNITS">DURATION5_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION6">DURATION6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION6_ESTIMATED">DURATION6_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION6_UNITS">DURATION6_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION7">DURATION7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION7_ESTIMATED">DURATION7_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION7_UNITS">DURATION7_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION8">DURATION8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION8_ESTIMATED">DURATION8_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION8_UNITS">DURATION8_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION9">DURATION9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION9_ESTIMATED">DURATION9_ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#DURATION9_UNITS">DURATION9_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EAC">EAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EARLY_FINISH">EARLY_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EARLY_START">EARLY_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EARNED_VALUE_METHOD">EARNED_VALUE_METHOD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EFFORT_DRIVEN">EFFORT_DRIVEN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST1">ENTERPRISE_COST1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST10">ENTERPRISE_COST10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST2">ENTERPRISE_COST2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST3">ENTERPRISE_COST3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST4">ENTERPRISE_COST4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST5">ENTERPRISE_COST5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST6">ENTERPRISE_COST6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST7">ENTERPRISE_COST7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST8">ENTERPRISE_COST8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_COST9">ENTERPRISE_COST9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATA">ENTERPRISE_DATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE1">ENTERPRISE_DATE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE10">ENTERPRISE_DATE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE11">ENTERPRISE_DATE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE12">ENTERPRISE_DATE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE13">ENTERPRISE_DATE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE14">ENTERPRISE_DATE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE15">ENTERPRISE_DATE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE16">ENTERPRISE_DATE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE17">ENTERPRISE_DATE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE18">ENTERPRISE_DATE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE19">ENTERPRISE_DATE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE2">ENTERPRISE_DATE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE20">ENTERPRISE_DATE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE21">ENTERPRISE_DATE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE22">ENTERPRISE_DATE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE23">ENTERPRISE_DATE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE24">ENTERPRISE_DATE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE25">ENTERPRISE_DATE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE26">ENTERPRISE_DATE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE27">ENTERPRISE_DATE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE28">ENTERPRISE_DATE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE29">ENTERPRISE_DATE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE3">ENTERPRISE_DATE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE30">ENTERPRISE_DATE30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE4">ENTERPRISE_DATE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE5">ENTERPRISE_DATE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE6">ENTERPRISE_DATE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE7">ENTERPRISE_DATE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE8">ENTERPRISE_DATE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DATE9">ENTERPRISE_DATE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION1">ENTERPRISE_DURATION1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION1_UNITS">ENTERPRISE_DURATION1_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION10">ENTERPRISE_DURATION10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION10_UNITS">ENTERPRISE_DURATION10_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION2">ENTERPRISE_DURATION2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION2_UNITS">ENTERPRISE_DURATION2_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION3">ENTERPRISE_DURATION3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION3_UNITS">ENTERPRISE_DURATION3_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION4">ENTERPRISE_DURATION4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION4_UNITS">ENTERPRISE_DURATION4_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION5">ENTERPRISE_DURATION5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION5_UNITS">ENTERPRISE_DURATION5_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION6">ENTERPRISE_DURATION6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION6_UNITS">ENTERPRISE_DURATION6_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION7">ENTERPRISE_DURATION7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION7_UNITS">ENTERPRISE_DURATION7_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION8">ENTERPRISE_DURATION8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION8_UNITS">ENTERPRISE_DURATION8_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION9">ENTERPRISE_DURATION9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_DURATION9_UNITS">ENTERPRISE_DURATION9_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG1">ENTERPRISE_FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG10">ENTERPRISE_FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG11">ENTERPRISE_FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG12">ENTERPRISE_FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG13">ENTERPRISE_FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG14">ENTERPRISE_FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG15">ENTERPRISE_FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG16">ENTERPRISE_FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG17">ENTERPRISE_FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG18">ENTERPRISE_FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG19">ENTERPRISE_FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG2">ENTERPRISE_FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG20">ENTERPRISE_FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG3">ENTERPRISE_FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG4">ENTERPRISE_FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG5">ENTERPRISE_FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG6">ENTERPRISE_FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG7">ENTERPRISE_FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG8">ENTERPRISE_FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_FLAG9">ENTERPRISE_FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER1">ENTERPRISE_NUMBER1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER10">ENTERPRISE_NUMBER10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER11">ENTERPRISE_NUMBER11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER12">ENTERPRISE_NUMBER12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER13">ENTERPRISE_NUMBER13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER14">ENTERPRISE_NUMBER14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER15">ENTERPRISE_NUMBER15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER16">ENTERPRISE_NUMBER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER17">ENTERPRISE_NUMBER17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER18">ENTERPRISE_NUMBER18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER19">ENTERPRISE_NUMBER19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER2">ENTERPRISE_NUMBER2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER20">ENTERPRISE_NUMBER20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER21">ENTERPRISE_NUMBER21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER22">ENTERPRISE_NUMBER22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER23">ENTERPRISE_NUMBER23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER24">ENTERPRISE_NUMBER24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER25">ENTERPRISE_NUMBER25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER26">ENTERPRISE_NUMBER26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER27">ENTERPRISE_NUMBER27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER28">ENTERPRISE_NUMBER28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER29">ENTERPRISE_NUMBER29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER3">ENTERPRISE_NUMBER3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER30">ENTERPRISE_NUMBER30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER31">ENTERPRISE_NUMBER31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER32">ENTERPRISE_NUMBER32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER33">ENTERPRISE_NUMBER33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER34">ENTERPRISE_NUMBER34</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER35">ENTERPRISE_NUMBER35</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER36">ENTERPRISE_NUMBER36</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER37">ENTERPRISE_NUMBER37</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER38">ENTERPRISE_NUMBER38</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER39">ENTERPRISE_NUMBER39</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER4">ENTERPRISE_NUMBER4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER40">ENTERPRISE_NUMBER40</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER5">ENTERPRISE_NUMBER5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER6">ENTERPRISE_NUMBER6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER7">ENTERPRISE_NUMBER7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER8">ENTERPRISE_NUMBER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_NUMBER9">ENTERPRISE_NUMBER9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE1">ENTERPRISE_OUTLINE_CODE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE10">ENTERPRISE_OUTLINE_CODE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE11">ENTERPRISE_OUTLINE_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE12">ENTERPRISE_OUTLINE_CODE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE13">ENTERPRISE_OUTLINE_CODE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE14">ENTERPRISE_OUTLINE_CODE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE15">ENTERPRISE_OUTLINE_CODE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE16">ENTERPRISE_OUTLINE_CODE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE17">ENTERPRISE_OUTLINE_CODE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE18">ENTERPRISE_OUTLINE_CODE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE19">ENTERPRISE_OUTLINE_CODE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE2">ENTERPRISE_OUTLINE_CODE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE20">ENTERPRISE_OUTLINE_CODE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE21">ENTERPRISE_OUTLINE_CODE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE22">ENTERPRISE_OUTLINE_CODE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE23">ENTERPRISE_OUTLINE_CODE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE24">ENTERPRISE_OUTLINE_CODE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE25">ENTERPRISE_OUTLINE_CODE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE26">ENTERPRISE_OUTLINE_CODE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE27">ENTERPRISE_OUTLINE_CODE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE28">ENTERPRISE_OUTLINE_CODE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE29">ENTERPRISE_OUTLINE_CODE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE3">ENTERPRISE_OUTLINE_CODE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE30">ENTERPRISE_OUTLINE_CODE30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE4">ENTERPRISE_OUTLINE_CODE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE5">ENTERPRISE_OUTLINE_CODE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE6">ENTERPRISE_OUTLINE_CODE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE7">ENTERPRISE_OUTLINE_CODE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE8">ENTERPRISE_OUTLINE_CODE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_OUTLINE_CODE9">ENTERPRISE_OUTLINE_CODE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST1">ENTERPRISE_PROJECT_COST1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST10">ENTERPRISE_PROJECT_COST10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST2">ENTERPRISE_PROJECT_COST2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST3">ENTERPRISE_PROJECT_COST3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST4">ENTERPRISE_PROJECT_COST4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST5">ENTERPRISE_PROJECT_COST5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST6">ENTERPRISE_PROJECT_COST6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST7">ENTERPRISE_PROJECT_COST7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST8">ENTERPRISE_PROJECT_COST8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_COST9">ENTERPRISE_PROJECT_COST9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE1">ENTERPRISE_PROJECT_DATE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE10">ENTERPRISE_PROJECT_DATE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE11">ENTERPRISE_PROJECT_DATE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE12">ENTERPRISE_PROJECT_DATE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE13">ENTERPRISE_PROJECT_DATE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE14">ENTERPRISE_PROJECT_DATE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE15">ENTERPRISE_PROJECT_DATE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE16">ENTERPRISE_PROJECT_DATE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE17">ENTERPRISE_PROJECT_DATE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE18">ENTERPRISE_PROJECT_DATE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE19">ENTERPRISE_PROJECT_DATE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE2">ENTERPRISE_PROJECT_DATE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE20">ENTERPRISE_PROJECT_DATE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE21">ENTERPRISE_PROJECT_DATE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE22">ENTERPRISE_PROJECT_DATE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE23">ENTERPRISE_PROJECT_DATE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE24">ENTERPRISE_PROJECT_DATE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE25">ENTERPRISE_PROJECT_DATE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE26">ENTERPRISE_PROJECT_DATE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE27">ENTERPRISE_PROJECT_DATE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE28">ENTERPRISE_PROJECT_DATE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE29">ENTERPRISE_PROJECT_DATE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE3">ENTERPRISE_PROJECT_DATE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE30">ENTERPRISE_PROJECT_DATE30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE4">ENTERPRISE_PROJECT_DATE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE5">ENTERPRISE_PROJECT_DATE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE6">ENTERPRISE_PROJECT_DATE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE7">ENTERPRISE_PROJECT_DATE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE8">ENTERPRISE_PROJECT_DATE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DATE9">ENTERPRISE_PROJECT_DATE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION1">ENTERPRISE_PROJECT_DURATION1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION10">ENTERPRISE_PROJECT_DURATION10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION2">ENTERPRISE_PROJECT_DURATION2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION3">ENTERPRISE_PROJECT_DURATION3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION4">ENTERPRISE_PROJECT_DURATION4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION5">ENTERPRISE_PROJECT_DURATION5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION6">ENTERPRISE_PROJECT_DURATION6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION7">ENTERPRISE_PROJECT_DURATION7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION8">ENTERPRISE_PROJECT_DURATION8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_DURATION9">ENTERPRISE_PROJECT_DURATION9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG1">ENTERPRISE_PROJECT_FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG10">ENTERPRISE_PROJECT_FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG11">ENTERPRISE_PROJECT_FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG12">ENTERPRISE_PROJECT_FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG13">ENTERPRISE_PROJECT_FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG14">ENTERPRISE_PROJECT_FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG15">ENTERPRISE_PROJECT_FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG16">ENTERPRISE_PROJECT_FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG17">ENTERPRISE_PROJECT_FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG18">ENTERPRISE_PROJECT_FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG19">ENTERPRISE_PROJECT_FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG2">ENTERPRISE_PROJECT_FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG20">ENTERPRISE_PROJECT_FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG3">ENTERPRISE_PROJECT_FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG4">ENTERPRISE_PROJECT_FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG5">ENTERPRISE_PROJECT_FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG6">ENTERPRISE_PROJECT_FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG7">ENTERPRISE_PROJECT_FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG8">ENTERPRISE_PROJECT_FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_FLAG9">ENTERPRISE_PROJECT_FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER1">ENTERPRISE_PROJECT_NUMBER1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER10">ENTERPRISE_PROJECT_NUMBER10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER11">ENTERPRISE_PROJECT_NUMBER11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER12">ENTERPRISE_PROJECT_NUMBER12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER13">ENTERPRISE_PROJECT_NUMBER13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER14">ENTERPRISE_PROJECT_NUMBER14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER15">ENTERPRISE_PROJECT_NUMBER15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER16">ENTERPRISE_PROJECT_NUMBER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER17">ENTERPRISE_PROJECT_NUMBER17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER18">ENTERPRISE_PROJECT_NUMBER18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER19">ENTERPRISE_PROJECT_NUMBER19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER2">ENTERPRISE_PROJECT_NUMBER2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER20">ENTERPRISE_PROJECT_NUMBER20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER21">ENTERPRISE_PROJECT_NUMBER21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER22">ENTERPRISE_PROJECT_NUMBER22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER23">ENTERPRISE_PROJECT_NUMBER23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER24">ENTERPRISE_PROJECT_NUMBER24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER25">ENTERPRISE_PROJECT_NUMBER25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER26">ENTERPRISE_PROJECT_NUMBER26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER27">ENTERPRISE_PROJECT_NUMBER27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER28">ENTERPRISE_PROJECT_NUMBER28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER29">ENTERPRISE_PROJECT_NUMBER29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER3">ENTERPRISE_PROJECT_NUMBER3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER30">ENTERPRISE_PROJECT_NUMBER30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER31">ENTERPRISE_PROJECT_NUMBER31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER32">ENTERPRISE_PROJECT_NUMBER32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER33">ENTERPRISE_PROJECT_NUMBER33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER34">ENTERPRISE_PROJECT_NUMBER34</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER35">ENTERPRISE_PROJECT_NUMBER35</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER36">ENTERPRISE_PROJECT_NUMBER36</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER37">ENTERPRISE_PROJECT_NUMBER37</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER38">ENTERPRISE_PROJECT_NUMBER38</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER39">ENTERPRISE_PROJECT_NUMBER39</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER4">ENTERPRISE_PROJECT_NUMBER4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER40">ENTERPRISE_PROJECT_NUMBER40</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER5">ENTERPRISE_PROJECT_NUMBER5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER6">ENTERPRISE_PROJECT_NUMBER6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER7">ENTERPRISE_PROJECT_NUMBER7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER8">ENTERPRISE_PROJECT_NUMBER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_NUMBER9">ENTERPRISE_PROJECT_NUMBER9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE1">ENTERPRISE_PROJECT_OUTLINE_CODE1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE10">ENTERPRISE_PROJECT_OUTLINE_CODE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE11">ENTERPRISE_PROJECT_OUTLINE_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE12">ENTERPRISE_PROJECT_OUTLINE_CODE12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE13">ENTERPRISE_PROJECT_OUTLINE_CODE13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE14">ENTERPRISE_PROJECT_OUTLINE_CODE14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE15">ENTERPRISE_PROJECT_OUTLINE_CODE15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE16">ENTERPRISE_PROJECT_OUTLINE_CODE16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE17">ENTERPRISE_PROJECT_OUTLINE_CODE17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE18">ENTERPRISE_PROJECT_OUTLINE_CODE18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE19">ENTERPRISE_PROJECT_OUTLINE_CODE19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE2">ENTERPRISE_PROJECT_OUTLINE_CODE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE20">ENTERPRISE_PROJECT_OUTLINE_CODE20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE21">ENTERPRISE_PROJECT_OUTLINE_CODE21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE22">ENTERPRISE_PROJECT_OUTLINE_CODE22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE23">ENTERPRISE_PROJECT_OUTLINE_CODE23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE24">ENTERPRISE_PROJECT_OUTLINE_CODE24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE25">ENTERPRISE_PROJECT_OUTLINE_CODE25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE26">ENTERPRISE_PROJECT_OUTLINE_CODE26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE27">ENTERPRISE_PROJECT_OUTLINE_CODE27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE28">ENTERPRISE_PROJECT_OUTLINE_CODE28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE29">ENTERPRISE_PROJECT_OUTLINE_CODE29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE3">ENTERPRISE_PROJECT_OUTLINE_CODE3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE30">ENTERPRISE_PROJECT_OUTLINE_CODE30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE4">ENTERPRISE_PROJECT_OUTLINE_CODE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE5">ENTERPRISE_PROJECT_OUTLINE_CODE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE6">ENTERPRISE_PROJECT_OUTLINE_CODE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE7">ENTERPRISE_PROJECT_OUTLINE_CODE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE8">ENTERPRISE_PROJECT_OUTLINE_CODE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_OUTLINE_CODE9">ENTERPRISE_PROJECT_OUTLINE_CODE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT1">ENTERPRISE_PROJECT_TEXT1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT10">ENTERPRISE_PROJECT_TEXT10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT11">ENTERPRISE_PROJECT_TEXT11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT12">ENTERPRISE_PROJECT_TEXT12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT13">ENTERPRISE_PROJECT_TEXT13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT14">ENTERPRISE_PROJECT_TEXT14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT15">ENTERPRISE_PROJECT_TEXT15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT16">ENTERPRISE_PROJECT_TEXT16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT17">ENTERPRISE_PROJECT_TEXT17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT18">ENTERPRISE_PROJECT_TEXT18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT19">ENTERPRISE_PROJECT_TEXT19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT2">ENTERPRISE_PROJECT_TEXT2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT20">ENTERPRISE_PROJECT_TEXT20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT21">ENTERPRISE_PROJECT_TEXT21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT22">ENTERPRISE_PROJECT_TEXT22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT23">ENTERPRISE_PROJECT_TEXT23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT24">ENTERPRISE_PROJECT_TEXT24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT25">ENTERPRISE_PROJECT_TEXT25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT26">ENTERPRISE_PROJECT_TEXT26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT27">ENTERPRISE_PROJECT_TEXT27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT28">ENTERPRISE_PROJECT_TEXT28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT29">ENTERPRISE_PROJECT_TEXT29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT3">ENTERPRISE_PROJECT_TEXT3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT30">ENTERPRISE_PROJECT_TEXT30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT31">ENTERPRISE_PROJECT_TEXT31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT32">ENTERPRISE_PROJECT_TEXT32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT33">ENTERPRISE_PROJECT_TEXT33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT34">ENTERPRISE_PROJECT_TEXT34</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT35">ENTERPRISE_PROJECT_TEXT35</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT36">ENTERPRISE_PROJECT_TEXT36</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT37">ENTERPRISE_PROJECT_TEXT37</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT38">ENTERPRISE_PROJECT_TEXT38</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT39">ENTERPRISE_PROJECT_TEXT39</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT4">ENTERPRISE_PROJECT_TEXT4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT40">ENTERPRISE_PROJECT_TEXT40</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT5">ENTERPRISE_PROJECT_TEXT5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT6">ENTERPRISE_PROJECT_TEXT6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT7">ENTERPRISE_PROJECT_TEXT7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT8">ENTERPRISE_PROJECT_TEXT8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_PROJECT_TEXT9">ENTERPRISE_PROJECT_TEXT9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT1">ENTERPRISE_TEXT1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT10">ENTERPRISE_TEXT10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT11">ENTERPRISE_TEXT11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT12">ENTERPRISE_TEXT12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT13">ENTERPRISE_TEXT13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT14">ENTERPRISE_TEXT14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT15">ENTERPRISE_TEXT15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT16">ENTERPRISE_TEXT16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT17">ENTERPRISE_TEXT17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT18">ENTERPRISE_TEXT18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT19">ENTERPRISE_TEXT19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT2">ENTERPRISE_TEXT2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT20">ENTERPRISE_TEXT20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT21">ENTERPRISE_TEXT21</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT22">ENTERPRISE_TEXT22</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT23">ENTERPRISE_TEXT23</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT24">ENTERPRISE_TEXT24</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT25">ENTERPRISE_TEXT25</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT26">ENTERPRISE_TEXT26</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT27">ENTERPRISE_TEXT27</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT28">ENTERPRISE_TEXT28</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT29">ENTERPRISE_TEXT29</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT3">ENTERPRISE_TEXT3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT30">ENTERPRISE_TEXT30</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT31">ENTERPRISE_TEXT31</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT32">ENTERPRISE_TEXT32</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT33">ENTERPRISE_TEXT33</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT34">ENTERPRISE_TEXT34</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT35">ENTERPRISE_TEXT35</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT36">ENTERPRISE_TEXT36</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT37">ENTERPRISE_TEXT37</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT38">ENTERPRISE_TEXT38</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT39">ENTERPRISE_TEXT39</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT4">ENTERPRISE_TEXT4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT40">ENTERPRISE_TEXT40</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT5">ENTERPRISE_TEXT5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT6">ENTERPRISE_TEXT6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT7">ENTERPRISE_TEXT7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT8">ENTERPRISE_TEXT8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ENTERPRISE_TEXT9">ENTERPRISE_TEXT9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ERROR_MESSAGE">ERROR_MESSAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ESTIMATED">ESTIMATED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXPANDED">EXPANDED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXPECTED_FINISH">EXPECTED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXPENSE_ITEMS">EXPENSE_ITEMS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXTERNAL_EARLY_START">EXTERNAL_EARLY_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXTERNAL_LATE_FINISH">EXTERNAL_LATE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXTERNAL_PROJECT">EXTERNAL_PROJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#EXTERNAL_TASK">EXTERNAL_TASK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FEATURE_OF_WORK">FEATURE_OF_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH">FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH_SLACK">FINISH_SLACK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH_TEXT">FINISH_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH_VARIANCE">FINISH_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH1">FINISH1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH10">FINISH10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH2">FINISH2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH3">FINISH3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH4">FINISH4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH5">FINISH5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH6">FINISH6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH7">FINISH7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH8">FINISH8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FINISH9">FINISH9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FIXED_COST">FIXED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FIXED_COST_ACCRUAL">FIXED_COST_ACCRUAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FIXED_DURATION">FIXED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG1">FLAG1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG10">FLAG10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG11">FLAG11</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG12">FLAG12</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG13">FLAG13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG14">FLAG14</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG15">FLAG15</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG16">FLAG16</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG17">FLAG17</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG18">FLAG18</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG19">FLAG19</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG2">FLAG2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG20">FLAG20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG3">FLAG3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG4">FLAG4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG5">FLAG5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG6">FLAG6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG7">FLAG7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG8">FLAG8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLAG9">FLAG9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLOAT_PATH">FLOAT_PATH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FLOAT_PATH_ORDER">FLOAT_PATH_ORDER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#FREE_SLACK">FREE_SLACK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#GROUP_BY_SUMMARY">GROUP_BY_SUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#GUID">GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HAMMOCK_CODE">HAMMOCK_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HIDE_BAR">HIDE_BAR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK">HYPERLINK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK_ADDRESS">HYPERLINK_ADDRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK_DATA">HYPERLINK_DATA</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK_HREF">HYPERLINK_HREF</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK_SCREEN_TIP">HYPERLINK_SCREEN_TIP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#HYPERLINK_SUBADDRESS">HYPERLINK_SUBADDRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ID">ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#IGNORE_RESOURCE_CALENDAR">IGNORE_RESOURCE_CALENDAR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#IGNORE_WARNINGS">IGNORE_WARNINGS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#INDEX">INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#INDICATORS">INDICATORS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#IS_DURATION_VALID">IS_DURATION_VALID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#IS_FINISH_VALID">IS_FINISH_VALID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#IS_START_VALID">IS_START_VALID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LATE_FINISH">LATE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LATE_START">LATE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LEVEL_ASSIGNMENTS">LEVEL_ASSIGNMENTS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LEVELING_CAN_SPLIT">LEVELING_CAN_SPLIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LEVELING_DELAY">LEVELING_DELAY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LEVELING_DELAY_UNITS">LEVELING_DELAY_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LINKED_FIELDS">LINKED_FIELDS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LOCATION_UNIQUE_ID">LOCATION_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#LONGEST_PATH">LONGEST_PATH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MAIL">MAIL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MANAGER">MANAGER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MANUAL_DURATION">MANUAL_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MANUAL_DURATION_UNITS">MANUAL_DURATION_UNITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MARKED">MARKED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#METHODOLOGY_GUID">METHODOLOGY_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MILESTONE">MILESTONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MOD_OR_CLAIM_NUMBER">MOD_OR_CLAIM_NUMBER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NAME">NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NOTES">NOTES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NULL">NULL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER1">NUMBER1</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER10">NUMBER10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER11">NUMBER11</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER12">NUMBER12</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER13">NUMBER13</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER14">NUMBER14</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER15">NUMBER15</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER16">NUMBER16</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER17">NUMBER17</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER18">NUMBER18</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER19">NUMBER19</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER2">NUMBER2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER20">NUMBER20</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER3">NUMBER3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER4">NUMBER4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER5">NUMBER5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER6">NUMBER6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER7">NUMBER7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER8">NUMBER8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#NUMBER9">NUMBER9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OBJECTS">OBJECTS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE1">OUTLINE_CODE1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE1_INDEX">OUTLINE_CODE1_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE10">OUTLINE_CODE10</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE10_INDEX">OUTLINE_CODE10_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE2">OUTLINE_CODE2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE2_INDEX">OUTLINE_CODE2_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE3">OUTLINE_CODE3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE3_INDEX">OUTLINE_CODE3_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE4">OUTLINE_CODE4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE4_INDEX">OUTLINE_CODE4_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE5">OUTLINE_CODE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE5_INDEX">OUTLINE_CODE5_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE6">OUTLINE_CODE6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE6_INDEX">OUTLINE_CODE6_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE7">OUTLINE_CODE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE7_INDEX">OUTLINE_CODE7_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE8">OUTLINE_CODE8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE8_INDEX">OUTLINE_CODE8_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE9">OUTLINE_CODE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_CODE9_INDEX">OUTLINE_CODE9_INDEX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_LEVEL">OUTLINE_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OUTLINE_NUMBER">OUTLINE_NUMBER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OVERALL_PERCENT_COMPLETE">OVERALL_PERCENT_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OVERALLOCATED">OVERALLOCATED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OVERTIME_COST">OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#OVERTIME_WORK">OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PARENT_TASK_UNIQUE_ID">PARENT_TASK_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PATH_DRIVEN_SUCCESSOR">PATH_DRIVEN_SUCCESSOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PATH_DRIVING_PREDECESSOR">PATH_DRIVING_PREDECESSOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PATH_PREDECESSOR">PATH_PREDECESSOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PATH_SUCCESSOR">PATH_SUCCESSOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PEAK">PEAK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PERCENT_COMPLETE">PERCENT_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PERCENT_COMPLETE_TYPE">PERCENT_COMPLETE_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PERCENT_WORK_COMPLETE">PERCENT_WORK_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PHASE_OF_WORK">PHASE_OF_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PHYSICAL_PERCENT_COMPLETE">PHYSICAL_PERCENT_COMPLETE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLACEHOLDER">PLACEHOLDER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_COST">PLANNED_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_DURATION">PLANNED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_FINISH">PLANNED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_START">PLANNED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_WORK">PLANNED_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_WORK_LABOR">PLANNED_WORK_LABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PLANNED_WORK_NONLABOR">PLANNED_WORK_NONLABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PREDECESSORS">PREDECESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PRELEVELED_FINISH">PRELEVELED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PRELEVELED_START">PRELEVELED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PRIMARY_RESOURCE_UNIQUE_ID">PRIMARY_RESOURCE_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PRIORITY">PRIORITY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PROJECT">PROJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#PUBLISH">PUBLISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RECALC_OUTLINE_CODES">RECALC_OUTLINE_CODES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RECURRING">RECURRING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RECURRING_DATA">RECURRING_DATA</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REGULAR_WORK">REGULAR_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_COST">REMAINING_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_DURATION">REMAINING_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_EARLY_FINISH">REMAINING_EARLY_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_EARLY_START">REMAINING_EARLY_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_LATE_FINISH">REMAINING_LATE_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_LATE_START">REMAINING_LATE_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_OVERTIME_COST">REMAINING_OVERTIME_COST</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_OVERTIME_WORK">REMAINING_OVERTIME_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_WORK">REMAINING_WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_WORK_LABOR">REMAINING_WORK_LABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REMAINING_WORK_NONLABOR">REMAINING_WORK_NONLABOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#REQUEST_DEMAND">REQUEST_DEMAND</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE20">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE21">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE22">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE22</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE23">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE23</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE24">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE25">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE26">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE26</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE27">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE27</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE28">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE28</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_MULTI_VALUE_CODE29">RESOURCE_ENTERPRISE_MULTI_VALUE_CODE29</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE1">RESOURCE_ENTERPRISE_OUTLINE_CODE1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE10">RESOURCE_ENTERPRISE_OUTLINE_CODE10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE11">RESOURCE_ENTERPRISE_OUTLINE_CODE11</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE12">RESOURCE_ENTERPRISE_OUTLINE_CODE12</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE13">RESOURCE_ENTERPRISE_OUTLINE_CODE13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE14">RESOURCE_ENTERPRISE_OUTLINE_CODE14</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE15">RESOURCE_ENTERPRISE_OUTLINE_CODE15</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE16">RESOURCE_ENTERPRISE_OUTLINE_CODE16</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE17">RESOURCE_ENTERPRISE_OUTLINE_CODE17</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE18">RESOURCE_ENTERPRISE_OUTLINE_CODE18</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE19">RESOURCE_ENTERPRISE_OUTLINE_CODE19</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE2">RESOURCE_ENTERPRISE_OUTLINE_CODE2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE20">RESOURCE_ENTERPRISE_OUTLINE_CODE20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE21">RESOURCE_ENTERPRISE_OUTLINE_CODE21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE22">RESOURCE_ENTERPRISE_OUTLINE_CODE22</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE23">RESOURCE_ENTERPRISE_OUTLINE_CODE23</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE24">RESOURCE_ENTERPRISE_OUTLINE_CODE24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE25">RESOURCE_ENTERPRISE_OUTLINE_CODE25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE26">RESOURCE_ENTERPRISE_OUTLINE_CODE26</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE27">RESOURCE_ENTERPRISE_OUTLINE_CODE27</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE28">RESOURCE_ENTERPRISE_OUTLINE_CODE28</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE29">RESOURCE_ENTERPRISE_OUTLINE_CODE29</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE3">RESOURCE_ENTERPRISE_OUTLINE_CODE3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE4">RESOURCE_ENTERPRISE_OUTLINE_CODE4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE5">RESOURCE_ENTERPRISE_OUTLINE_CODE5</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE6">RESOURCE_ENTERPRISE_OUTLINE_CODE6</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE7">RESOURCE_ENTERPRISE_OUTLINE_CODE7</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE8">RESOURCE_ENTERPRISE_OUTLINE_CODE8</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_OUTLINE_CODE9">RESOURCE_ENTERPRISE_OUTLINE_CODE9</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_ENTERPRISE_RBS">RESOURCE_ENTERPRISE_RBS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_GROUP">RESOURCE_GROUP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_INITIALS">RESOURCE_INITIALS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_NAMES">RESOURCE_NAMES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_PHONETICS">RESOURCE_PHONETICS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESOURCE_TYPE">RESOURCE_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESPONSE_PENDING">RESPONSE_PENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESPONSIBILITY_CODE">RESPONSIBILITY_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESUME">RESUME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESUME_NO_EARLIER_THAN">RESUME_NO_EARLIER_THAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#RESUME_VALID">RESUME_VALID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#ROLLUP">ROLLUP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SCHEDULED_DURATION">SCHEDULED_DURATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SCHEDULED_FINISH">SCHEDULED_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SCHEDULED_START">SCHEDULED_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SECONDARY_CONSTRAINT_DATE">SECONDARY_CONSTRAINT_DATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SECONDARY_CONSTRAINT_TYPE">SECONDARY_CONSTRAINT_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SECTION">SECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SEQUENCE_NUMBER">SEQUENCE_NUMBER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SHOW_DURATION_TEXT">SHOW_DURATION_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SHOW_FINISH_TEXT">SHOW_FINISH_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SHOW_ON_BOARD">SHOW_ON_BOARD</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SHOW_START_TEXT">SHOW_START_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPI">SPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPLITS">SPLITS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPRINT">SPRINT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPRINT_FINISH">SPRINT_FINISH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPRINT_ID">SPRINT_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SPRINT_START">SPRINT_START</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START">START</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START_SLACK">START_SLACK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START_TEXT">START_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START_VARIANCE">START_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START1">START1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START10">START10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START2">START2</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START3">START3</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START4">START4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START5">START5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START6">START6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START7">START7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START8">START8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#START9">START9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STATUS">STATUS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STATUS_INDICATOR">STATUS_INDICATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STATUS_MANAGER">STATUS_MANAGER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STEPS">STEPS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STOP">STOP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#STORED_MATERIAL">STORED_MATERIAL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_FILE">SUBPROJECT_FILE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_GUID">SUBPROJECT_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_READ_ONLY">SUBPROJECT_READ_ONLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_TASK_ID">SUBPROJECT_TASK_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_TASK_UNIQUE_ID">SUBPROJECT_TASK_UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUBPROJECT_TASKS_UNIQUEID_OFFSET">SUBPROJECT_TASKS_UNIQUEID_OFFSET</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUCCESSORS">SUCCESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUMMARY">SUMMARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUMMARY_PROGRESS">SUMMARY_PROGRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SUSPEND_DATE">SUSPEND_DATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SV">SV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#SVPERCENT">SVPERCENT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TASK_CALENDAR">TASK_CALENDAR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TASK_CALENDAR_GUID">TASK_CALENDAR_GUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TASK_MODE">TASK_MODE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TASK_SUMMARY_NAME">TASK_SUMMARY_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TCPI">TCPI</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEAMSTATUS_PENDING">TEAMSTATUS_PENDING</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT1">TEXT1</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT10">TEXT10</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT11">TEXT11</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT12">TEXT12</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT13">TEXT13</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT14">TEXT14</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT15">TEXT15</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT16">TEXT16</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT17">TEXT17</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT18">TEXT18</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT19">TEXT19</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT2">TEXT2</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT20">TEXT20</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT21">TEXT21</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT22">TEXT22</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT23">TEXT23</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT24">TEXT24</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT25">TEXT25</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT26">TEXT26</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT27">TEXT27</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT28">TEXT28</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT29">TEXT29</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT3">TEXT3</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT30">TEXT30</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT4">TEXT4</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT5">TEXT5</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT6">TEXT6</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT7">TEXT7</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT8">TEXT8</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TEXT9">TEXT9</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TOTAL_SLACK">TOTAL_SLACK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#TYPE">TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#UNAVAILABLE">UNAVAILABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#UNIQUE_ID">UNIQUE_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#UNIQUE_ID_PREDECESSORS">UNIQUE_ID_PREDECESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#UNIQUE_ID_SUCCESSORS">UNIQUE_ID_SUCCESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#UPDATE_NEEDED">UPDATE_NEEDED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#VAC">VAC</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WARNING">WARNING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WBS">WBS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WBS_PREDECESSORS">WBS_PREDECESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WBS_SUCCESSORS">WBS_SUCCESSORS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WORK">WORK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WORK_AREA_CODE">WORK_AREA_CODE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WORK_CONTOUR">WORK_CONTOUR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WORK_VARIANCE">WORK_VARIANCE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#WORKERS_PER_DAY">WORKERS_PER_DAY</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#MAX_VALUE">MAX_VALUE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getDataType--">getDataType</a></span>()</code>
<div class="block">Retrieve the data type of this field.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getFieldTypeClass--">getFieldTypeClass</a></span>()</code>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getInstance-int-">getInstance</a></span>(int&nbsp;type)</code>
<div class="block">This method takes the integer enumeration of a task field
 and returns an appropriate class instance.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getName--">getName</a></span>()</code>
<div class="block">Retrieve the name of this field using the default locale.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getName-java.util.Locale-">getName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</code>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getUnitsType--">getUnitsType</a></span>()</code>
<div class="block">Retrieve the associated units field, if any.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#getValue--">getValue</a></span>()</code>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#toString--">toString</a></span>()</code>
<div class="block">Retrieves the string representation of this instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/TaskField.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.FieldType">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.mpxj.<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></h3>
<code><a href="../../org/mpxj/FieldType.html#name--">name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START</pre>
</li>
</ul>
<a name="DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_DURATION_UNITS</pre>
</li>
</ul>
<a name="ACTUAL_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_DURATION_UNITS</pre>
</li>
</ul>
<a name="LEVELING_DELAY_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVELING_DELAY_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LEVELING_DELAY_UNITS</pre>
</li>
</ul>
<a name="DURATION1_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION1_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION1_UNITS</pre>
</li>
</ul>
<a name="DURATION2_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION2_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION2_UNITS</pre>
</li>
</ul>
<a name="DURATION3_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION3_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION3_UNITS</pre>
</li>
</ul>
<a name="DURATION4_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION4_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION4_UNITS</pre>
</li>
</ul>
<a name="DURATION5_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION5_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION5_UNITS</pre>
</li>
</ul>
<a name="DURATION6_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION6_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION6_UNITS</pre>
</li>
</ul>
<a name="DURATION7_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION7_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION7_UNITS</pre>
</li>
</ul>
<a name="DURATION8_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION8_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION8_UNITS</pre>
</li>
</ul>
<a name="DURATION9_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION9_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION9_UNITS</pre>
</li>
</ul>
<a name="DURATION10_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION10_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION10_UNITS</pre>
</li>
</ul>
<a name="WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WORK</pre>
</li>
</ul>
<a name="BASELINE_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_WORK</pre>
</li>
</ul>
<a name="ACTUAL_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_WORK</pre>
</li>
</ul>
<a name="WORK_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WORK_VARIANCE</pre>
</li>
</ul>
<a name="REMAINING_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_WORK</pre>
</li>
</ul>
<a name="COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST</pre>
</li>
</ul>
<a name="BASELINE_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_COST</pre>
</li>
</ul>
<a name="ACTUAL_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_COST</pre>
</li>
</ul>
<a name="FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FIXED_COST</pre>
</li>
</ul>
<a name="COST_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST_VARIANCE</pre>
</li>
</ul>
<a name="REMAINING_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_COST</pre>
</li>
</ul>
<a name="BCWP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BCWP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BCWP</pre>
</li>
</ul>
<a name="BCWS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BCWS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BCWS</pre>
</li>
</ul>
<a name="SV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SV</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SV</pre>
</li>
</ul>
<a name="NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NAME</pre>
</li>
</ul>
<a name="WBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WBS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WBS</pre>
</li>
</ul>
<a name="CONSTRAINT_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONSTRAINT_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CONSTRAINT_TYPE</pre>
</li>
</ul>
<a name="CONSTRAINT_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONSTRAINT_DATE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CONSTRAINT_DATE</pre>
</li>
</ul>
<a name="SECONDARY_CONSTRAINT_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SECONDARY_CONSTRAINT_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SECONDARY_CONSTRAINT_TYPE</pre>
</li>
</ul>
<a name="SECONDARY_CONSTRAINT_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SECONDARY_CONSTRAINT_DATE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SECONDARY_CONSTRAINT_DATE</pre>
</li>
</ul>
<a name="CRITICAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CRITICAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CRITICAL</pre>
</li>
</ul>
<a name="LEVELING_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVELING_DELAY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LEVELING_DELAY</pre>
</li>
</ul>
<a name="FREE_SLACK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FREE_SLACK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FREE_SLACK</pre>
</li>
</ul>
<a name="TOTAL_SLACK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOTAL_SLACK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TOTAL_SLACK</pre>
</li>
</ul>
<a name="ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ID</pre>
</li>
</ul>
<a name="MILESTONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILESTONE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MILESTONE</pre>
</li>
</ul>
<a name="PRIORITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRIORITY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PRIORITY</pre>
</li>
</ul>
<a name="BASELINE_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_DURATION</pre>
</li>
</ul>
<a name="ACTUAL_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_DURATION</pre>
</li>
</ul>
<a name="DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION</pre>
</li>
</ul>
<a name="DURATION_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION_VARIANCE</pre>
</li>
</ul>
<a name="REMAINING_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_DURATION</pre>
</li>
</ul>
<a name="PERCENT_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PERCENT_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PERCENT_COMPLETE</pre>
</li>
</ul>
<a name="PERCENT_WORK_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PERCENT_WORK_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PERCENT_WORK_COMPLETE</pre>
</li>
</ul>
<a name="EARLY_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EARLY_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EARLY_START</pre>
</li>
</ul>
<a name="EARLY_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EARLY_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EARLY_FINISH</pre>
</li>
</ul>
<a name="REMAINING_EARLY_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_EARLY_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_EARLY_START</pre>
</li>
</ul>
<a name="REMAINING_EARLY_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_EARLY_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_EARLY_FINISH</pre>
</li>
</ul>
<a name="REMAINING_LATE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_LATE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_LATE_START</pre>
</li>
</ul>
<a name="REMAINING_LATE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_LATE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_LATE_FINISH</pre>
</li>
</ul>
<a name="LATE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LATE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LATE_START</pre>
</li>
</ul>
<a name="LATE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LATE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LATE_FINISH</pre>
</li>
</ul>
<a name="ACTUAL_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_START</pre>
</li>
</ul>
<a name="ACTUAL_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_FINISH</pre>
</li>
</ul>
<a name="BASELINE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_START</pre>
</li>
</ul>
<a name="BASELINE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_FINISH</pre>
</li>
</ul>
<a name="START_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START_VARIANCE</pre>
</li>
</ul>
<a name="FINISH_VARIANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH_VARIANCE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH_VARIANCE</pre>
</li>
</ul>
<a name="PREDECESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREDECESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PREDECESSORS</pre>
</li>
</ul>
<a name="SUCCESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUCCESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUCCESSORS</pre>
</li>
</ul>
<a name="RESOURCE_NAMES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_NAMES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_NAMES</pre>
</li>
</ul>
<a name="RESOURCE_INITIALS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_INITIALS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_INITIALS</pre>
</li>
</ul>
<a name="TEXT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT1</pre>
</li>
</ul>
<a name="START1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START1</pre>
</li>
</ul>
<a name="FINISH1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH1</pre>
</li>
</ul>
<a name="TEXT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT2</pre>
</li>
</ul>
<a name="START2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START2</pre>
</li>
</ul>
<a name="FINISH2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH2</pre>
</li>
</ul>
<a name="TEXT3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT3</pre>
</li>
</ul>
<a name="START3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START3</pre>
</li>
</ul>
<a name="FINISH3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH3</pre>
</li>
</ul>
<a name="TEXT4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT4</pre>
</li>
</ul>
<a name="START4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START4</pre>
</li>
</ul>
<a name="FINISH4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH4</pre>
</li>
</ul>
<a name="TEXT5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT5</pre>
</li>
</ul>
<a name="START5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START5</pre>
</li>
</ul>
<a name="FINISH5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH5</pre>
</li>
</ul>
<a name="TEXT6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT6</pre>
</li>
</ul>
<a name="TEXT7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT7</pre>
</li>
</ul>
<a name="TEXT8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT8</pre>
</li>
</ul>
<a name="TEXT9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT9</pre>
</li>
</ul>
<a name="TEXT10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT10</pre>
</li>
</ul>
<a name="MARKED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MARKED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MARKED</pre>
</li>
</ul>
<a name="FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG1</pre>
</li>
</ul>
<a name="FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG2</pre>
</li>
</ul>
<a name="FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG3</pre>
</li>
</ul>
<a name="FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG4</pre>
</li>
</ul>
<a name="FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG5</pre>
</li>
</ul>
<a name="FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG6</pre>
</li>
</ul>
<a name="FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG7</pre>
</li>
</ul>
<a name="FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG8</pre>
</li>
</ul>
<a name="FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG9</pre>
</li>
</ul>
<a name="FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG10</pre>
</li>
</ul>
<a name="ROLLUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROLLUP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ROLLUP</pre>
</li>
</ul>
<a name="CV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CV</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CV</pre>
</li>
</ul>
<a name="PROJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PROJECT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PROJECT</pre>
</li>
</ul>
<a name="OUTLINE_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_LEVEL</pre>
</li>
</ul>
<a name="UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> UNIQUE_ID</pre>
</li>
</ul>
<a name="NUMBER1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER1</pre>
</li>
</ul>
<a name="NUMBER2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER2</pre>
</li>
</ul>
<a name="NUMBER3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER3</pre>
</li>
</ul>
<a name="NUMBER4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER4</pre>
</li>
</ul>
<a name="NUMBER5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER5</pre>
</li>
</ul>
<a name="SUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUMMARY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUMMARY</pre>
</li>
</ul>
<a name="CREATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CREATED</pre>
</li>
</ul>
<a name="NOTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NOTES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NOTES</pre>
</li>
</ul>
<a name="UNIQUE_ID_PREDECESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIQUE_ID_PREDECESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> UNIQUE_ID_PREDECESSORS</pre>
</li>
</ul>
<a name="UNIQUE_ID_SUCCESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIQUE_ID_SUCCESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> UNIQUE_ID_SUCCESSORS</pre>
</li>
</ul>
<a name="OBJECTS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJECTS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OBJECTS</pre>
</li>
</ul>
<a name="LINKED_FIELDS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINKED_FIELDS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LINKED_FIELDS</pre>
</li>
</ul>
<a name="RESUME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESUME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESUME</pre>
</li>
</ul>
<a name="STOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STOP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STOP</pre>
</li>
</ul>
<a name="OUTLINE_NUMBER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_NUMBER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_NUMBER</pre>
</li>
</ul>
<a name="DURATION1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION1</pre>
</li>
</ul>
<a name="DURATION2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION2</pre>
</li>
</ul>
<a name="DURATION3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION3</pre>
</li>
</ul>
<a name="COST1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST1</pre>
</li>
</ul>
<a name="COST2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST2</pre>
</li>
</ul>
<a name="COST3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST3</pre>
</li>
</ul>
<a name="HIDE_BAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIDE_BAR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HIDE_BAR</pre>
</li>
</ul>
<a name="CONFIRMED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONFIRMED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CONFIRMED</pre>
</li>
</ul>
<a name="UPDATE_NEEDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UPDATE_NEEDED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> UPDATE_NEEDED</pre>
</li>
</ul>
<a name="CONTACT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CONTACT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CONTACT</pre>
</li>
</ul>
<a name="RESOURCE_GROUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_GROUP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_GROUP</pre>
</li>
</ul>
<a name="ACWP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACWP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACWP</pre>
</li>
</ul>
<a name="TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TYPE</pre>
</li>
</ul>
<a name="RECURRING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECURRING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RECURRING</pre>
</li>
</ul>
<a name="EFFORT_DRIVEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EFFORT_DRIVEN</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EFFORT_DRIVEN</pre>
</li>
</ul>
<a name="OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OVERTIME_WORK</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_OVERTIME_WORK</pre>
</li>
</ul>
<a name="REMAINING_OVERTIME_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_OVERTIME_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_OVERTIME_WORK</pre>
</li>
</ul>
<a name="REGULAR_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REGULAR_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REGULAR_WORK</pre>
</li>
</ul>
<a name="OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OVERTIME_COST</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_OVERTIME_COST</pre>
</li>
</ul>
<a name="REMAINING_OVERTIME_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_OVERTIME_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_OVERTIME_COST</pre>
</li>
</ul>
<a name="FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="INDICATORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INDICATORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> INDICATORS</pre>
</li>
</ul>
<a name="HYPERLINK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK</pre>
</li>
</ul>
<a name="HYPERLINK_ADDRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_ADDRESS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK_ADDRESS</pre>
</li>
</ul>
<a name="HYPERLINK_SUBADDRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_SUBADDRESS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK_SUBADDRESS</pre>
</li>
</ul>
<a name="HYPERLINK_HREF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_HREF</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK_HREF</pre>
</li>
</ul>
<a name="ASSIGNMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ASSIGNMENT</pre>
</li>
</ul>
<a name="OVERALLOCATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERALLOCATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OVERALLOCATED</pre>
</li>
</ul>
<a name="RESPONSE_PENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESPONSE_PENDING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESPONSE_PENDING</pre>
</li>
</ul>
<a name="TEAMSTATUS_PENDING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEAMSTATUS_PENDING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEAMSTATUS_PENDING</pre>
</li>
</ul>
<a name="LEVELING_CAN_SPLIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVELING_CAN_SPLIT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LEVELING_CAN_SPLIT</pre>
</li>
</ul>
<a name="LEVEL_ASSIGNMENTS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LEVEL_ASSIGNMENTS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LEVEL_ASSIGNMENTS</pre>
</li>
</ul>
<a name="WORK_CONTOUR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_CONTOUR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WORK_CONTOUR</pre>
</li>
</ul>
<a name="COST4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST4</pre>
</li>
</ul>
<a name="COST5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST5</pre>
</li>
</ul>
<a name="COST6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST6</pre>
</li>
</ul>
<a name="COST7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST7</pre>
</li>
</ul>
<a name="COST8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST8</pre>
</li>
</ul>
<a name="COST9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST9</pre>
</li>
</ul>
<a name="COST10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST10</pre>
</li>
</ul>
<a name="DATE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE1</pre>
</li>
</ul>
<a name="DATE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE2</pre>
</li>
</ul>
<a name="DATE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE3</pre>
</li>
</ul>
<a name="DATE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE4</pre>
</li>
</ul>
<a name="DATE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE5</pre>
</li>
</ul>
<a name="DATE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE6</pre>
</li>
</ul>
<a name="DATE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE7</pre>
</li>
</ul>
<a name="DATE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE8</pre>
</li>
</ul>
<a name="DATE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE9</pre>
</li>
</ul>
<a name="DATE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DATE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DATE10</pre>
</li>
</ul>
<a name="DURATION4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION4</pre>
</li>
</ul>
<a name="DURATION5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION5</pre>
</li>
</ul>
<a name="DURATION6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION6</pre>
</li>
</ul>
<a name="DURATION7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION7</pre>
</li>
</ul>
<a name="DURATION8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION8</pre>
</li>
</ul>
<a name="DURATION9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION9</pre>
</li>
</ul>
<a name="DURATION10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION10</pre>
</li>
</ul>
<a name="START6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START6</pre>
</li>
</ul>
<a name="FINISH6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH6</pre>
</li>
</ul>
<a name="START7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START7</pre>
</li>
</ul>
<a name="FINISH7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH7</pre>
</li>
</ul>
<a name="START8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START8</pre>
</li>
</ul>
<a name="FINISH8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH8</pre>
</li>
</ul>
<a name="START9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START9</pre>
</li>
</ul>
<a name="FINISH9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH9</pre>
</li>
</ul>
<a name="START10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START10</pre>
</li>
</ul>
<a name="FINISH10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH10</pre>
</li>
</ul>
<a name="FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG11</pre>
</li>
</ul>
<a name="FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG12</pre>
</li>
</ul>
<a name="FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG13</pre>
</li>
</ul>
<a name="FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG14</pre>
</li>
</ul>
<a name="FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG15</pre>
</li>
</ul>
<a name="FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG16</pre>
</li>
</ul>
<a name="FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG17</pre>
</li>
</ul>
<a name="FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG18</pre>
</li>
</ul>
<a name="FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG19</pre>
</li>
</ul>
<a name="FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLAG20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLAG20</pre>
</li>
</ul>
<a name="NUMBER6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER6</pre>
</li>
</ul>
<a name="NUMBER7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER7</pre>
</li>
</ul>
<a name="NUMBER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER8</pre>
</li>
</ul>
<a name="NUMBER9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER9</pre>
</li>
</ul>
<a name="NUMBER10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER10</pre>
</li>
</ul>
<a name="NUMBER11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER11</pre>
</li>
</ul>
<a name="NUMBER12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER12</pre>
</li>
</ul>
<a name="NUMBER13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER13</pre>
</li>
</ul>
<a name="NUMBER14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER14</pre>
</li>
</ul>
<a name="NUMBER15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER15</pre>
</li>
</ul>
<a name="NUMBER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER16</pre>
</li>
</ul>
<a name="NUMBER17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER17</pre>
</li>
</ul>
<a name="NUMBER18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER18</pre>
</li>
</ul>
<a name="NUMBER19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER19</pre>
</li>
</ul>
<a name="NUMBER20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NUMBER20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NUMBER20</pre>
</li>
</ul>
<a name="TEXT11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT11</pre>
</li>
</ul>
<a name="TEXT12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT12</pre>
</li>
</ul>
<a name="TEXT13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT13</pre>
</li>
</ul>
<a name="TEXT14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT14</pre>
</li>
</ul>
<a name="TEXT15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT15</pre>
</li>
</ul>
<a name="TEXT16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT16</pre>
</li>
</ul>
<a name="TEXT17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT17</pre>
</li>
</ul>
<a name="TEXT18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT18</pre>
</li>
</ul>
<a name="TEXT19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT19</pre>
</li>
</ul>
<a name="TEXT20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT20</pre>
</li>
</ul>
<a name="TEXT21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT21</pre>
</li>
</ul>
<a name="TEXT22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT22</pre>
</li>
</ul>
<a name="TEXT23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT23</pre>
</li>
</ul>
<a name="TEXT24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT24</pre>
</li>
</ul>
<a name="TEXT25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT25</pre>
</li>
</ul>
<a name="TEXT26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT26</pre>
</li>
</ul>
<a name="TEXT27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT27</pre>
</li>
</ul>
<a name="TEXT28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT28</pre>
</li>
</ul>
<a name="TEXT29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT29</pre>
</li>
</ul>
<a name="TEXT30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXT30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TEXT30</pre>
</li>
</ul>
<a name="RESOURCE_PHONETICS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_PHONETICS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_PHONETICS</pre>
</li>
</ul>
<a name="ASSIGNMENT_DELAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_DELAY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ASSIGNMENT_DELAY</pre>
</li>
</ul>
<a name="ASSIGNMENT_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ASSIGNMENT_UNITS</pre>
</li>
</ul>
<a name="COST_RATE_TABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COST_RATE_TABLE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COST_RATE_TABLE</pre>
</li>
</ul>
<a name="PRELEVELED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRELEVELED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PRELEVELED_START</pre>
</li>
</ul>
<a name="PRELEVELED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRELEVELED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PRELEVELED_FINISH</pre>
</li>
</ul>
<a name="ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ESTIMATED</pre>
</li>
</ul>
<a name="IGNORE_RESOURCE_CALENDAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IGNORE_RESOURCE_CALENDAR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> IGNORE_RESOURCE_CALENDAR</pre>
</li>
</ul>
<a name="OUTLINE_CODE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE1</pre>
</li>
</ul>
<a name="OUTLINE_CODE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE2</pre>
</li>
</ul>
<a name="OUTLINE_CODE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE3</pre>
</li>
</ul>
<a name="OUTLINE_CODE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE4</pre>
</li>
</ul>
<a name="OUTLINE_CODE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE5</pre>
</li>
</ul>
<a name="OUTLINE_CODE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE6</pre>
</li>
</ul>
<a name="OUTLINE_CODE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE7</pre>
</li>
</ul>
<a name="OUTLINE_CODE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE8</pre>
</li>
</ul>
<a name="OUTLINE_CODE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE9</pre>
</li>
</ul>
<a name="OUTLINE_CODE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE10</pre>
</li>
</ul>
<a name="DEADLINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEADLINE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DEADLINE</pre>
</li>
</ul>
<a name="START_SLACK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_SLACK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START_SLACK</pre>
</li>
</ul>
<a name="FINISH_SLACK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH_SLACK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH_SLACK</pre>
</li>
</ul>
<a name="VAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VAC</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> VAC</pre>
</li>
</ul>
<a name="GROUP_BY_SUMMARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GROUP_BY_SUMMARY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> GROUP_BY_SUMMARY</pre>
</li>
</ul>
<a name="WBS_PREDECESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WBS_PREDECESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WBS_PREDECESSORS</pre>
</li>
</ul>
<a name="WBS_SUCCESSORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WBS_SUCCESSORS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WBS_SUCCESSORS</pre>
</li>
</ul>
<a name="RESOURCE_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_TYPE</pre>
</li>
</ul>
<a name="BASELINE1_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE2_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE3_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE4_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE5_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE6_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE7_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE8_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE9_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE10_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_DURATION_UNITS</pre>
</li>
</ul>
<a name="BASELINE1_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_START</pre>
</li>
</ul>
<a name="BASELINE1_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_FINISH</pre>
</li>
</ul>
<a name="BASELINE1_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_COST</pre>
</li>
</ul>
<a name="BASELINE1_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_WORK</pre>
</li>
</ul>
<a name="BASELINE1_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_DURATION</pre>
</li>
</ul>
<a name="BASELINE2_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_START</pre>
</li>
</ul>
<a name="BASELINE2_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_FINISH</pre>
</li>
</ul>
<a name="BASELINE2_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_COST</pre>
</li>
</ul>
<a name="BASELINE2_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_WORK</pre>
</li>
</ul>
<a name="BASELINE2_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_DURATION</pre>
</li>
</ul>
<a name="BASELINE3_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_START</pre>
</li>
</ul>
<a name="BASELINE3_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_FINISH</pre>
</li>
</ul>
<a name="BASELINE3_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_COST</pre>
</li>
</ul>
<a name="BASELINE3_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_WORK</pre>
</li>
</ul>
<a name="BASELINE3_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_DURATION</pre>
</li>
</ul>
<a name="BASELINE4_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_START</pre>
</li>
</ul>
<a name="BASELINE4_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_FINISH</pre>
</li>
</ul>
<a name="BASELINE4_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_COST</pre>
</li>
</ul>
<a name="BASELINE4_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_WORK</pre>
</li>
</ul>
<a name="BASELINE4_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_DURATION</pre>
</li>
</ul>
<a name="BASELINE5_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_START</pre>
</li>
</ul>
<a name="BASELINE5_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_FINISH</pre>
</li>
</ul>
<a name="BASELINE5_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_COST</pre>
</li>
</ul>
<a name="BASELINE5_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_WORK</pre>
</li>
</ul>
<a name="BASELINE5_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_DURATION</pre>
</li>
</ul>
<a name="BASELINE6_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_START</pre>
</li>
</ul>
<a name="BASELINE6_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_FINISH</pre>
</li>
</ul>
<a name="BASELINE6_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_COST</pre>
</li>
</ul>
<a name="BASELINE6_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_WORK</pre>
</li>
</ul>
<a name="BASELINE6_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_DURATION</pre>
</li>
</ul>
<a name="BASELINE7_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_START</pre>
</li>
</ul>
<a name="BASELINE7_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_FINISH</pre>
</li>
</ul>
<a name="BASELINE7_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_COST</pre>
</li>
</ul>
<a name="BASELINE7_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_WORK</pre>
</li>
</ul>
<a name="BASELINE7_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_DURATION</pre>
</li>
</ul>
<a name="BASELINE8_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_START</pre>
</li>
</ul>
<a name="BASELINE8_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_FINISH</pre>
</li>
</ul>
<a name="BASELINE8_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_COST</pre>
</li>
</ul>
<a name="BASELINE8_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_WORK</pre>
</li>
</ul>
<a name="BASELINE8_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_DURATION</pre>
</li>
</ul>
<a name="BASELINE9_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_START</pre>
</li>
</ul>
<a name="BASELINE9_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_FINISH</pre>
</li>
</ul>
<a name="BASELINE9_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_COST</pre>
</li>
</ul>
<a name="BASELINE9_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_WORK</pre>
</li>
</ul>
<a name="BASELINE9_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_DURATION</pre>
</li>
</ul>
<a name="BASELINE10_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_START</pre>
</li>
</ul>
<a name="BASELINE10_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_FINISH</pre>
</li>
</ul>
<a name="BASELINE10_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_COST</pre>
</li>
</ul>
<a name="BASELINE10_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_WORK</pre>
</li>
</ul>
<a name="BASELINE10_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_DURATION</pre>
</li>
</ul>
<a name="ENTERPRISE_COST1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST1</pre>
</li>
</ul>
<a name="ENTERPRISE_COST2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST2</pre>
</li>
</ul>
<a name="ENTERPRISE_COST3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST3</pre>
</li>
</ul>
<a name="ENTERPRISE_COST4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST4</pre>
</li>
</ul>
<a name="ENTERPRISE_COST5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST5</pre>
</li>
</ul>
<a name="ENTERPRISE_COST6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST6</pre>
</li>
</ul>
<a name="ENTERPRISE_COST7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST7</pre>
</li>
</ul>
<a name="ENTERPRISE_COST8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST8</pre>
</li>
</ul>
<a name="ENTERPRISE_COST9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST9</pre>
</li>
</ul>
<a name="ENTERPRISE_COST10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_COST10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_COST10</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE1</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE2</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE3</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE4</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE5</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE6</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE7</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE8</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE9</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE10</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE11</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE12</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE13</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE14</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE15</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE16</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE17</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE18</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE19</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE20</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE21</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE22</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE23</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE24</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE25</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE26</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE27</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE28</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE29</pre>
</li>
</ul>
<a name="ENTERPRISE_DATE30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATE30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATE30</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION1_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION1_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION1_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION2_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION2_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION2_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION3_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION3_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION3_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION4_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION4_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION4_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION5_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION5_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION5_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION6_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION6_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION6_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION7_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION7_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION7_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION8_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION8_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION8_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION9_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION9_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION9_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION10_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION10_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION10_UNITS</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION1</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION2</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION3</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION4</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION5</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION6</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION7</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION8</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION9</pre>
</li>
</ul>
<a name="ENTERPRISE_DURATION10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DURATION10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DURATION10</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG1</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG2</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG3</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG4</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG5</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG6</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG7</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG8</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG9</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG10</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG11</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG12</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG13</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG14</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG15</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG16</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG17</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG18</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG19</pre>
</li>
</ul>
<a name="ENTERPRISE_FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_FLAG20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_FLAG20</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER1</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER2</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER3</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER4</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER5</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER6</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER7</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER8</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER9</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER10</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER11</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER12</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER13</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER14</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER15</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER16</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER17</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER18</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER19</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER20</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER21</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER22</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER23</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER24</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER25</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER26</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER27</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER28</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER29</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER30</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER31</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER32</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER33</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER34</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER35</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER36</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER37</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER38</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER39</pre>
</li>
</ul>
<a name="ENTERPRISE_NUMBER40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_NUMBER40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_NUMBER40</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT1</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT2</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT3</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT4</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT5</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT6</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT7</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT8</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT9</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT10</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT11</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT12</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT13</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT14</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT15</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT16</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT17</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT18</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT19</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT20</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT21</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT22</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT23</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT24</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT25</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT26</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT27</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT28</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT29</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT30</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT31</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT32</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT33</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT34</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT35</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT36</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT37</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT38</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT39</pre>
</li>
</ul>
<a name="ENTERPRISE_TEXT40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_TEXT40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_TEXT40</pre>
</li>
</ul>
<a name="COMPLETE_THROUGH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPLETE_THROUGH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> COMPLETE_THROUGH</pre>
</li>
</ul>
<a name="SUMMARY_PROGRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUMMARY_PROGRESS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUMMARY_PROGRESS</pre>
</li>
</ul>
<a name="GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> GUID</pre>
</li>
</ul>
<a name="ACTIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVE</pre>
</li>
</ul>
<a name="TASK_MODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_MODE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TASK_MODE</pre>
</li>
</ul>
<a name="ASSIGNMENT_OWNER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ASSIGNMENT_OWNER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ASSIGNMENT_OWNER</pre>
</li>
</ul>
<a name="BASELINE_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE1_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE1_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE1_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE1_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE1_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE1_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE1_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE1_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE1_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE2_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE2_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE2_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE2_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE2_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE2_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE2_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE2_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE2_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE3_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE3_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE3_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE3_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE3_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE3_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE3_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE3_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE3_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE4_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE4_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE4_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE4_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE4_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE4_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE4_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE4_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE4_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE5_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE5_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE5_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE5_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE5_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE5_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE5_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE5_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE5_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE6_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE6_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE6_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE6_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE6_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE6_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE6_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE6_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE6_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE7_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE7_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE7_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE7_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE7_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE7_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE7_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE7_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE7_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE8_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE8_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE8_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE8_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE8_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE8_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE8_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE8_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE8_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE9_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE9_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE9_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE9_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE9_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE9_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE9_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE9_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE9_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_DELIVERABLE_START</pre>
</li>
</ul>
<a name="BASELINE10_ESTIMATED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_ESTIMATED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_ESTIMATED_START</pre>
</li>
</ul>
<a name="BASELINE10_ESTIMATED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_ESTIMATED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_ESTIMATED_FINISH</pre>
</li>
</ul>
<a name="BASELINE10_ESTIMATED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_ESTIMATED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_ESTIMATED_DURATION</pre>
</li>
</ul>
<a name="BASELINE10_FIXED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_FIXED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_FIXED_COST</pre>
</li>
</ul>
<a name="BASELINE10_FIXED_COST_ACCRUAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_FIXED_COST_ACCRUAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_FIXED_COST_ACCRUAL</pre>
</li>
</ul>
<a name="BASELINE10_BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_BUDGET_COST</pre>
</li>
</ul>
<a name="BASELINE10_BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_BUDGET_WORK</pre>
</li>
</ul>
<a name="BASELINE10_DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="BASELINE10_DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_DELIVERABLE_START</pre>
</li>
</ul>
<a name="CPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CPI</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CPI</pre>
</li>
</ul>
<a name="CVPERCENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CVPERCENT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CVPERCENT</pre>
</li>
</ul>
<a name="DELIVERABLE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DELIVERABLE_FINISH</pre>
</li>
</ul>
<a name="DELIVERABLE_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DELIVERABLE_GUID</pre>
</li>
</ul>
<a name="DELIVERABLE_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DELIVERABLE_NAME</pre>
</li>
</ul>
<a name="DELIVERABLE_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DELIVERABLE_START</pre>
</li>
</ul>
<a name="DELIVERABLE_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELIVERABLE_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DELIVERABLE_TYPE</pre>
</li>
</ul>
<a name="EAC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EAC</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EAC</pre>
</li>
</ul>
<a name="EARNED_VALUE_METHOD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EARNED_VALUE_METHOD</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EARNED_VALUE_METHOD</pre>
</li>
</ul>
<a name="ERROR_MESSAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ERROR_MESSAGE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ERROR_MESSAGE</pre>
</li>
</ul>
<a name="IGNORE_WARNINGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IGNORE_WARNINGS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> IGNORE_WARNINGS</pre>
</li>
</ul>
<a name="PEAK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PEAK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PEAK</pre>
</li>
</ul>
<a name="PHYSICAL_PERCENT_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PHYSICAL_PERCENT_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PHYSICAL_PERCENT_COMPLETE</pre>
</li>
</ul>
<a name="PLACEHOLDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLACEHOLDER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLACEHOLDER</pre>
</li>
</ul>
<a name="PUBLISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PUBLISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PUBLISH</pre>
</li>
</ul>
<a name="REQUEST_DEMAND">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REQUEST_DEMAND</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REQUEST_DEMAND</pre>
</li>
</ul>
<a name="SCHEDULED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCHEDULED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SCHEDULED_DURATION</pre>
</li>
</ul>
<a name="SCHEDULED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCHEDULED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SCHEDULED_FINISH</pre>
</li>
</ul>
<a name="SCHEDULED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SCHEDULED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SCHEDULED_START</pre>
</li>
</ul>
<a name="SPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPI</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPI</pre>
</li>
</ul>
<a name="STATUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STATUS</pre>
</li>
</ul>
<a name="STATUS_INDICATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_INDICATOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STATUS_INDICATOR</pre>
</li>
</ul>
<a name="STATUS_MANAGER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STATUS_MANAGER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STATUS_MANAGER</pre>
</li>
</ul>
<a name="SVPERCENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SVPERCENT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SVPERCENT</pre>
</li>
</ul>
<a name="TASK_CALENDAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_CALENDAR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TASK_CALENDAR</pre>
</li>
</ul>
<a name="TASK_CALENDAR_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_CALENDAR_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TASK_CALENDAR_GUID</pre>
</li>
</ul>
<a name="TCPI">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TCPI</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TCPI</pre>
</li>
</ul>
<a name="WARNING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WARNING</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WARNING</pre>
</li>
</ul>
<a name="UNAVAILABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNAVAILABLE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> UNAVAILABLE</pre>
</li>
</ul>
<a name="SPLITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPLITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPLITS</pre>
</li>
</ul>
<a name="START_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>START_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> START_TEXT</pre>
</li>
</ul>
<a name="FINISH_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH_TEXT</pre>
</li>
</ul>
<a name="DURATION_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION_TEXT</pre>
</li>
</ul>
<a name="MANUAL_DURATION_UNITS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUAL_DURATION_UNITS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MANUAL_DURATION_UNITS</pre>
</li>
</ul>
<a name="MANUAL_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANUAL_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MANUAL_DURATION</pre>
</li>
</ul>
<a name="SUBPROJECT_READ_ONLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_READ_ONLY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_READ_ONLY</pre>
</li>
</ul>
<a name="SUBPROJECT_TASKS_UNIQUEID_OFFSET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_TASKS_UNIQUEID_OFFSET</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_TASKS_UNIQUEID_OFFSET</pre>
</li>
</ul>
<a name="SUBPROJECT_FILE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_FILE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_FILE</pre>
</li>
</ul>
<a name="SUBPROJECT_TASK_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_TASK_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_TASK_UNIQUE_ID</pre>
</li>
</ul>
<a name="SUBPROJECT_TASK_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_TASK_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_TASK_ID</pre>
</li>
</ul>
<a name="SUBPROJECT_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBPROJECT_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUBPROJECT_GUID</pre>
</li>
</ul>
<a name="EXTERNAL_TASK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTERNAL_TASK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXTERNAL_TASK</pre>
</li>
</ul>
<a name="EXTERNAL_PROJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTERNAL_PROJECT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXTERNAL_PROJECT</pre>
</li>
</ul>
<a name="HYPERLINK_DATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_DATA</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK_DATA</pre>
</li>
</ul>
<a name="RECURRING_DATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECURRING_DATA</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RECURRING_DATA</pre>
</li>
</ul>
<a name="OUTLINE_CODE1_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE1_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE1_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE2_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE2_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE2_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE3_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE3_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE3_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE4_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE4_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE4_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE5_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE5_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE5_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE6_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE6_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE6_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE7_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE7_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE7_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE8_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE8_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE8_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE9_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE9_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE9_INDEX</pre>
</li>
</ul>
<a name="OUTLINE_CODE10_INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_CODE10_INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OUTLINE_CODE10_INDEX</pre>
</li>
</ul>
<a name="ENTERPRISE_DATA">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_DATA</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_DATA</pre>
</li>
</ul>
<a name="PARENT_TASK_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PARENT_TASK_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PARENT_TASK_UNIQUE_ID</pre>
</li>
</ul>
<a name="CALENDAR_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CALENDAR_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CALENDAR_UNIQUE_ID</pre>
</li>
</ul>
<a name="SPRINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPRINT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPRINT</pre>
</li>
</ul>
<a name="SPRINT_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPRINT_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPRINT_ID</pre>
</li>
</ul>
<a name="BOARD_STATUS_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BOARD_STATUS_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BOARD_STATUS_ID</pre>
</li>
</ul>
<a name="SPRINT_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPRINT_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPRINT_START</pre>
</li>
</ul>
<a name="SPRINT_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPRINT_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SPRINT_FINISH</pre>
</li>
</ul>
<a name="BOARD_STATUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BOARD_STATUS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BOARD_STATUS</pre>
</li>
</ul>
<a name="TASK_SUMMARY_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TASK_SUMMARY_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> TASK_SUMMARY_NAME</pre>
</li>
</ul>
<a name="SHOW_ON_BOARD">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_ON_BOARD</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SHOW_ON_BOARD</pre>
</li>
</ul>
<a name="FIXED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FIXED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FIXED_DURATION</pre>
</li>
</ul>
<a name="RESUME_NO_EARLIER_THAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESUME_NO_EARLIER_THAN</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESUME_NO_EARLIER_THAN</pre>
</li>
</ul>
<a name="INDEX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INDEX</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> INDEX</pre>
</li>
</ul>
<a name="DURATION1_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION1_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION1_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION2_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION2_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION2_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION3_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION3_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION3_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION4_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION4_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION4_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION5_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION5_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION5_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION6_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION6_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION6_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION7_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION7_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION7_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION8_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION8_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION8_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION9_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION9_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION9_ESTIMATED</pre>
</li>
</ul>
<a name="DURATION10_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DURATION10_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DURATION10_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="HYPERLINK_SCREEN_TIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HYPERLINK_SCREEN_TIP</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HYPERLINK_SCREEN_TIP</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE1</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE2</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE3</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE4</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE5</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE6</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE7</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE8</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE9</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE10</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE11</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE12</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE13</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE14</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE15</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE16</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE17</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE18</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE19</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE20</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE21</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE22</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE23</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE24</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE25</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE26</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE27</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE28</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE29</pre>
</li>
</ul>
<a name="ENTERPRISE_OUTLINE_CODE30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_OUTLINE_CODE30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_OUTLINE_CODE30</pre>
</li>
</ul>
<a name="BASELINE1_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE1_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE1_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE2_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE2_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE2_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE3_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE3_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE3_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE4_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE4_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE4_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE5_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE5_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE5_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE6_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE6_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE6_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE7_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE7_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE7_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE8_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE8_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE8_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE9_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE9_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE9_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="BASELINE10_DURATION_ESTIMATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BASELINE10_DURATION_ESTIMATED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BASELINE10_DURATION_ESTIMATED</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_COST10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_COST10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_COST10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE11</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE12</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE13</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE14</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE15</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE16</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE17</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE18</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE19</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE20</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE21</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE22</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE23</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE24</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE25</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE26</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE27</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE28</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE29</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DATE30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DATE30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DATE30</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_DURATION10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_DURATION10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_DURATION10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE11</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE12</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE13</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE14</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE15</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE16</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE17</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE18</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE19</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE20</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE21</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE22</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE23</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE24</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE25</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE26</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE27</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE28</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE29</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_OUTLINE_CODE30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_OUTLINE_CODE30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_OUTLINE_CODE30</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG11</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG12</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG13</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG14</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG15</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG16</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG17</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG18</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG19</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_FLAG20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_FLAG20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_FLAG20</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER11</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER12</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER13</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER14</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER15</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER16</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER17</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER18</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER19</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER20</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER21</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER22</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER23</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER24</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER25</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER26</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER27</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER28</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER29</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER30</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER31</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER32</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER33</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER34</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER35</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER36</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER37</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER38</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER39</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_NUMBER40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_NUMBER40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_NUMBER40</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT1</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT2</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT3</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT4</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT5</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT6</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT7</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT8</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT9</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT10</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT11</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT12</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT13</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT14</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT15</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT16</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT17</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT18</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT19</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT20</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT21</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT22</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT23</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT24</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT25</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT26</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT27</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT28</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT29</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT30">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT30</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT30</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT31">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT31</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT31</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT32">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT32</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT32</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT33">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT33</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT33</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT34">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT34</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT34</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT35">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT35</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT35</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT36">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT36</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT36</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT37">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT37</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT37</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT38">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT38</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT38</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT39">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT39</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT39</pre>
</li>
</ul>
<a name="ENTERPRISE_PROJECT_TEXT40">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENTERPRISE_PROJECT_TEXT40</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ENTERPRISE_PROJECT_TEXT40</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE1">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE1</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE1</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE2">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE2</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE2</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE3">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE3</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE3</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE4</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE4</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE5">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE5</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE5</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE6">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE6</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE6</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE7">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE7</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE7</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE8">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE8</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE8</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE9">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE9</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE9</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE10">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE10</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE10</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE11">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE11</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE11</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE12">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE12</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE12</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE13">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE13</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE13</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE14">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE14</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE14</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE15">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE15</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE15</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE16">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE16</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE16</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE17">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE17</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE17</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE18">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE18</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE18</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE19">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE19</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE19</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE20</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE21</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE22</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE23</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE24</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE25</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE26</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE27</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE28</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_OUTLINE_CODE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_OUTLINE_CODE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_OUTLINE_CODE29</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_RBS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_RBS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_RBS</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE20">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE20</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE20</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE21">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE21</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE21</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE22">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE22</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE22</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE23">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE23</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE23</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE24">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE24</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE24</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE25">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE25</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE25</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE26">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE26</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE26</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE27">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE27</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE27</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE28">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE28</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE28</pre>
</li>
</ul>
<a name="RESOURCE_ENTERPRISE_MULTI_VALUE_CODE29">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_ENTERPRISE_MULTI_VALUE_CODE29</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESOURCE_ENTERPRISE_MULTI_VALUE_CODE29</pre>
</li>
</ul>
<a name="ACTUAL_WORK_PROTECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK_PROTECTED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_WORK_PROTECTED</pre>
</li>
</ul>
<a name="ACTUAL_OVERTIME_WORK_PROTECTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_OVERTIME_WORK_PROTECTED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_OVERTIME_WORK_PROTECTED</pre>
</li>
</ul>
<a name="BUDGET_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUDGET_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BUDGET_WORK</pre>
</li>
</ul>
<a name="BUDGET_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BUDGET_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BUDGET_COST</pre>
</li>
</ul>
<a name="RECALC_OUTLINE_CODES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECALC_OUTLINE_CODES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RECALC_OUTLINE_CODES</pre>
</li>
</ul>
<a name="IS_START_VALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_START_VALID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> IS_START_VALID</pre>
</li>
</ul>
<a name="IS_FINISH_VALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_FINISH_VALID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> IS_FINISH_VALID</pre>
</li>
</ul>
<a name="IS_DURATION_VALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IS_DURATION_VALID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> IS_DURATION_VALID</pre>
</li>
</ul>
<a name="PATH_DRIVEN_SUCCESSOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATH_DRIVEN_SUCCESSOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PATH_DRIVEN_SUCCESSOR</pre>
</li>
</ul>
<a name="PATH_DRIVING_PREDECESSOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATH_DRIVING_PREDECESSOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PATH_DRIVING_PREDECESSOR</pre>
</li>
</ul>
<a name="PATH_PREDECESSOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATH_PREDECESSOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PATH_PREDECESSOR</pre>
</li>
</ul>
<a name="PATH_SUCCESSOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PATH_SUCCESSOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PATH_SUCCESSOR</pre>
</li>
</ul>
<a name="EXPENSE_ITEMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPENSE_ITEMS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXPENSE_ITEMS</pre>
</li>
</ul>
<a name="STORED_MATERIAL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STORED_MATERIAL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STORED_MATERIAL</pre>
</li>
</ul>
<a name="FEATURE_OF_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FEATURE_OF_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FEATURE_OF_WORK</pre>
</li>
</ul>
<a name="CATEGORY_OF_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CATEGORY_OF_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> CATEGORY_OF_WORK</pre>
</li>
</ul>
<a name="PHASE_OF_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PHASE_OF_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PHASE_OF_WORK</pre>
</li>
</ul>
<a name="BID_ITEM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BID_ITEM</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BID_ITEM</pre>
</li>
</ul>
<a name="MOD_OR_CLAIM_NUMBER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOD_OR_CLAIM_NUMBER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MOD_OR_CLAIM_NUMBER</pre>
</li>
</ul>
<a name="WORK_AREA_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORK_AREA_CODE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WORK_AREA_CODE</pre>
</li>
</ul>
<a name="RESPONSIBILITY_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESPONSIBILITY_CODE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESPONSIBILITY_CODE</pre>
</li>
</ul>
<a name="WORKERS_PER_DAY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WORKERS_PER_DAY</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> WORKERS_PER_DAY</pre>
</li>
</ul>
<a name="HAMMOCK_CODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HAMMOCK_CODE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> HAMMOCK_CODE</pre>
</li>
</ul>
<a name="MAIL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAIL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MAIL</pre>
</li>
</ul>
<a name="SECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SECTION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SECTION</pre>
</li>
</ul>
<a name="MANAGER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANAGER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> MANAGER</pre>
</li>
</ul>
<a name="DEPARTMENT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEPARTMENT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> DEPARTMENT</pre>
</li>
</ul>
<a name="OVERALL_PERCENT_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OVERALL_PERCENT_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> OVERALL_PERCENT_COMPLETE</pre>
</li>
</ul>
<a name="PLANNED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_FINISH</pre>
</li>
</ul>
<a name="PLANNED_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_START</pre>
</li>
</ul>
<a name="PLANNED_DURATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_DURATION</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_DURATION</pre>
</li>
</ul>
<a name="PLANNED_WORK">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_WORK</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_WORK</pre>
</li>
</ul>
<a name="SUSPEND_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUSPEND_DATE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SUSPEND_DATE</pre>
</li>
</ul>
<a name="PRIMARY_RESOURCE_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRIMARY_RESOURCE_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PRIMARY_RESOURCE_UNIQUE_ID</pre>
</li>
</ul>
<a name="ACTIVITY_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVITY_ID</pre>
</li>
</ul>
<a name="PERCENT_COMPLETE_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PERCENT_COMPLETE_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PERCENT_COMPLETE_TYPE</pre>
</li>
</ul>
<a name="ACTIVITY_STATUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_STATUS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVITY_STATUS</pre>
</li>
</ul>
<a name="ACTIVITY_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_TYPE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVITY_TYPE</pre>
</li>
</ul>
<a name="PLANNED_COST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_COST</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_COST</pre>
</li>
</ul>
<a name="EXTERNAL_EARLY_START">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTERNAL_EARLY_START</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXTERNAL_EARLY_START</pre>
</li>
</ul>
<a name="EXTERNAL_LATE_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXTERNAL_LATE_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXTERNAL_LATE_FINISH</pre>
</li>
</ul>
<a name="LONGEST_PATH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LONGEST_PATH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LONGEST_PATH</pre>
</li>
</ul>
<a name="ACTIVITY_CODE_VALUES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_CODE_VALUES</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVITY_CODE_VALUES</pre>
</li>
</ul>
<a name="SEQUENCE_NUMBER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SEQUENCE_NUMBER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SEQUENCE_NUMBER</pre>
</li>
</ul>
<a name="STEPS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STEPS</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> STEPS</pre>
</li>
</ul>
<a name="LOCATION_UNIQUE_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCATION_UNIQUE_ID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> LOCATION_UNIQUE_ID</pre>
</li>
</ul>
<a name="EXPANDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPANDED</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXPANDED</pre>
</li>
</ul>
<a name="RESUME_VALID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESUME_VALID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> RESUME_VALID</pre>
</li>
</ul>
<a name="NULL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NULL</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> NULL</pre>
</li>
</ul>
<a name="FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FINISH</pre>
</li>
</ul>
<a name="BAR_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BAR_NAME</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> BAR_NAME</pre>
</li>
</ul>
<a name="EXPECTED_FINISH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPECTED_FINISH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> EXPECTED_FINISH</pre>
</li>
</ul>
<a name="ACTUAL_WORK_LABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK_LABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_WORK_LABOR</pre>
</li>
</ul>
<a name="ACTUAL_WORK_NONLABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTUAL_WORK_NONLABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTUAL_WORK_NONLABOR</pre>
</li>
</ul>
<a name="PLANNED_WORK_LABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_WORK_LABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_WORK_LABOR</pre>
</li>
</ul>
<a name="PLANNED_WORK_NONLABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLANNED_WORK_NONLABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> PLANNED_WORK_NONLABOR</pre>
</li>
</ul>
<a name="REMAINING_WORK_LABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_WORK_LABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_WORK_LABOR</pre>
</li>
</ul>
<a name="REMAINING_WORK_NONLABOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REMAINING_WORK_NONLABOR</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> REMAINING_WORK_NONLABOR</pre>
</li>
</ul>
<a name="SHOW_START_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_START_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SHOW_START_TEXT</pre>
</li>
</ul>
<a name="SHOW_FINISH_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_FINISH_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SHOW_FINISH_TEXT</pre>
</li>
</ul>
<a name="SHOW_DURATION_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_DURATION_TEXT</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> SHOW_DURATION_TEXT</pre>
</li>
</ul>
<a name="ACTIVITY_PERCENT_COMPLETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVITY_PERCENT_COMPLETE</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> ACTIVITY_PERCENT_COMPLETE</pre>
</li>
</ul>
<a name="METHODOLOGY_GUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>METHODOLOGY_GUID</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> METHODOLOGY_GUID</pre>
</li>
</ul>
<a name="FLOAT_PATH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLOAT_PATH</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLOAT_PATH</pre>
</li>
</ul>
<a name="FLOAT_PATH_ORDER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FLOAT_PATH_ORDER</h4>
<pre>public static final&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a> FLOAT_PATH_ORDER</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MAX_VALUE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MAX_VALUE</h4>
<pre>public static final&nbsp;int MAX_VALUE</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (TaskField c : TaskField.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&nbsp;valueOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getFieldTypeClass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldTypeClass</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a>&nbsp;getFieldTypeClass()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">FieldType</a></code></span></div>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">getFieldTypeClass</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field type class</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName--">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the default locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="getName-java.util.Locale-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>locale</code> - target locale</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html#getValue--">MpxjEnum</a></code></span></div>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/MpxjEnum.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum value</dd>
</dl>
</li>
</ul>
<a name="getDataType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;getDataType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getDataType--">FieldType</a></code></span></div>
<div class="block">Retrieve the data type of this field.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getDataType--">getDataType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>data type</dd>
</dl>
</li>
</ul>
<a name="getUnitsType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitsType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;getUnitsType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getUnitsType--">FieldType</a></code></span></div>
<div class="block">Retrieve the associated units field, if any.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getUnitsType--">getUnitsType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>units field</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Retrieves the string representation of this instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>string representation</dd>
</dl>
</li>
</ul>
<a name="getInstance-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>&nbsp;getInstance(int&nbsp;type)</pre>
<div class="block">This method takes the integer enumeration of a task field
 and returns an appropriate class instance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - integer task field enumeration</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>TaskField instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TaskField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TaskContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TaskMode.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/TaskField.html" target="_top">Frames</a></li>
<li><a href="TaskField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
