<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ObjectFactory (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectFactory (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.conceptdraw.schema</div>
<h2 title="Class ObjectFactory" class="title">Class ObjectFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.conceptdraw.schema.ObjectFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ObjectFactory</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This object contains factory methods for each
 Java content interface and Java element interface
 generated in the org.mpxj.conceptdraw.schema package.
 <p>An ObjectFactory allows you to programatically
 construct new instances of the Java representation
 for XML content. The Java representation of XML
 content can consist of schema derived interfaces
 and classes representing the binding of schema
 type definitions, element declarations and model
 groups.  Factory methods for each of these are
 provided in this class.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#ObjectFactory--">ObjectFactory</a></span>()</code>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.conceptdraw.schema</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/ActiveFilter.html" title="class in org.mpxj.conceptdraw.schema">ActiveFilter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createActiveFilter--">createActiveFilter</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ActiveFilter.html" title="class in org.mpxj.conceptdraw.schema"><code>ActiveFilter</code></a></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Callouts.html" title="class in org.mpxj.conceptdraw.schema">Callouts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createCallouts--">createCallouts</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Callouts.html" title="class in org.mpxj.conceptdraw.schema"><code>Callouts</code></a></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema">Callouts.Callout</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createCalloutsCallout--">createCalloutsCallout</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema"><code>Callouts.Callout</code></a></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.html" title="class in org.mpxj.conceptdraw.schema">Document</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocument--">createDocument</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.html" title="class in org.mpxj.conceptdraw.schema"><code>Document</code></a></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendars--">createDocumentCalendars</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars</code></a></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendar--">createDocumentCalendarsCalendar</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar</code></a></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarExceptedDays--">createDocumentCalendarsCalendarExceptedDays</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays</code></a></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarExceptedDaysExceptedDay--">createDocumentCalendarsCalendarExceptedDaysExceptedDay</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay</code></a></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriods--">createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriods</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</code></a></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriodsTimePeriod--">createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriodsTimePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod</code></a></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarWeekDays--">createDocumentCalendarsCalendarWeekDays</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays</code></a></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarWeekDaysWeekDay--">createDocumentCalendarsCalendarWeekDaysWeekDay</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay</code></a></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriods--">createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriods</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods</code></a></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriodsTimePeriod--">createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriodsTimePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod</code></a></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentDashboards--">createDocumentDashboards</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards</code></a></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.Dashboard.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards.Dashboard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentDashboardsDashboard--">createDocumentDashboardsDashboard</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.Dashboard.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards.Dashboard</code></a></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentLinks--">createDocumentLinks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links</code></a></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.Link.html" title="class in org.mpxj.conceptdraw.schema">Document.Links.Link</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentLinksLink--">createDocumentLinksLink</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.Link.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links.Link</code></a></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentMarkers--">createDocumentMarkers</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers</code></a></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers.Marker</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentMarkersMarker--">createDocumentMarkersMarker</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers.Marker</code></a></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentPrintingProperties--">createDocumentPrintingProperties</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.PrintingProperties</code></a></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjectPortfolioView--">createDocumentProjectPortfolioView</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ProjectPortfolioView</code></a></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjects--">createDocumentProjects</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects</code></a></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjectsProject--">createDocumentProjectsProject</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project</code></a></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjectsProjectTask--">createDocumentProjectsProjectTask</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task</code></a></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjectsProjectTaskResourceAssignments--">createDocumentProjectsProjectTaskResourceAssignments</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task.ResourceAssignments</code></a></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentProjectsProjectTaskResourceAssignmentsResourceAssignment--">createDocumentProjectsProjectTaskResourceAssignmentsResourceAssignment</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment</code></a></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentResources--">createDocumentResources</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources</code></a></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources.Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentResourcesResource--">createDocumentResourcesResource</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources.Resource</code></a></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentResourceUsageDiagram--">createDocumentResourceUsageDiagram</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ResourceUsageDiagram</code></a></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createDocumentWorkspaceProperties--">createDocumentWorkspaceProperties</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.WorkspaceProperties</code></a></div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createHyperlinks--">createHyperlinks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><code>Hyperlinks</code></a></div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>jakarta.xml.bind.JAXBElement&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createOutlineNumber-java.lang.String-">createOutlineNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a><code>&gt;</code></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createPPVItemsType--">createPPVItemsType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType</code></a></div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createPPVItemsTypePPVItem--">createPPVItemsTypePPVItem</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem</code></a></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createPPVItemsTypePPVItemCompleteJournal--">createPPVItemsTypePPVItemCompleteJournal</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem.CompleteJournal</code></a></div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createPPVItemsTypePPVItemCompleteJournalCompleteJournalEntry--">createPPVItemsTypePPVItemCompleteJournalCompleteJournalEntry</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry</code></a></div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>jakarta.xml.bind.JAXBElement&lt;<a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createPriority-org.mpxj.Priority-">createPriority</a></span>(<a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;value)</code>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj"><code>Priority</code></a><code>&gt;</code></div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createStyleProject--">createStyleProject</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject</code></a></div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.GridRowStyle.html" title="class in org.mpxj.conceptdraw.schema">StyleProject.GridRowStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createStyleProjectGridRowStyle--">createStyleProjectGridRowStyle</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.GridRowStyle.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject.GridRowStyle</code></a></div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/TimeScale.html" title="class in org.mpxj.conceptdraw.schema">TimeScale</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createTimeScale--">createTimeScale</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/TimeScale.html" title="class in org.mpxj.conceptdraw.schema"><code>TimeScale</code></a></div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createViewProperties--">createViewProperties</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties</code></a></div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties.GridColumns</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createViewPropertiesGridColumns--">createViewPropertiesGridColumns</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties.GridColumns</code></a></div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.Column.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties.GridColumns.Column</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/ObjectFactory.html#createViewPropertiesGridColumnsColumn--">createViewPropertiesGridColumnsColumn</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.Column.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties.GridColumns.Column</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ObjectFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ObjectFactory</h4>
<pre>public&nbsp;ObjectFactory()</pre>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.conceptdraw.schema</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createDocument--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocument</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.html" title="class in org.mpxj.conceptdraw.schema">Document</a>&nbsp;createDocument()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.html" title="class in org.mpxj.conceptdraw.schema"><code>Document</code></a></div>
</li>
</ul>
<a name="createViewProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createViewProperties</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties</a>&nbsp;createViewProperties()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties</code></a></div>
</li>
</ul>
<a name="createStyleProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createStyleProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a>&nbsp;createStyleProject()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject</code></a></div>
</li>
</ul>
<a name="createCallouts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCallouts</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Callouts.html" title="class in org.mpxj.conceptdraw.schema">Callouts</a>&nbsp;createCallouts()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Callouts.html" title="class in org.mpxj.conceptdraw.schema"><code>Callouts</code></a></div>
</li>
</ul>
<a name="createPPVItemsType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPPVItemsType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType</a>&nbsp;createPPVItemsType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType</code></a></div>
</li>
</ul>
<a name="createPPVItemsTypePPVItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPPVItemsTypePPVItem</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem</a>&nbsp;createPPVItemsTypePPVItem()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem</code></a></div>
</li>
</ul>
<a name="createPPVItemsTypePPVItemCompleteJournal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPPVItemsTypePPVItemCompleteJournal</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal</a>&nbsp;createPPVItemsTypePPVItemCompleteJournal()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem.CompleteJournal</code></a></div>
</li>
</ul>
<a name="createViewPropertiesGridColumns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createViewPropertiesGridColumns</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties.GridColumns</a>&nbsp;createViewPropertiesGridColumns()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties.GridColumns</code></a></div>
</li>
</ul>
<a name="createDocumentDashboards--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentDashboards</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a>&nbsp;createDocumentDashboards()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards</code></a></div>
</li>
</ul>
<a name="createDocumentLinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentLinks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a>&nbsp;createDocumentLinks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links</code></a></div>
</li>
</ul>
<a name="createDocumentProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjects</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a>&nbsp;createDocumentProjects()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects</code></a></div>
</li>
</ul>
<a name="createDocumentProjectsProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjectsProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project</a>&nbsp;createDocumentProjectsProject()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project</code></a></div>
</li>
</ul>
<a name="createDocumentProjectsProjectTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjectsProjectTask</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task</a>&nbsp;createDocumentProjectsProjectTask()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task</code></a></div>
</li>
</ul>
<a name="createDocumentProjectsProjectTaskResourceAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjectsProjectTaskResourceAssignments</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments</a>&nbsp;createDocumentProjectsProjectTaskResourceAssignments()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task.ResourceAssignments</code></a></div>
</li>
</ul>
<a name="createDocumentResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentResources</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a>&nbsp;createDocumentResources()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources</code></a></div>
</li>
</ul>
<a name="createDocumentCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a>&nbsp;createDocumentCalendars()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendar</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar</a>&nbsp;createDocumentCalendarsCalendar()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarExceptedDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarExceptedDays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays</a>&nbsp;createDocumentCalendarsCalendarExceptedDays()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarExceptedDaysExceptedDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarExceptedDaysExceptedDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay</a>&nbsp;createDocumentCalendarsCalendarExceptedDaysExceptedDay()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriods</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a>&nbsp;createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriods()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarWeekDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarWeekDays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays</a>&nbsp;createDocumentCalendarsCalendarWeekDays()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarWeekDaysWeekDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarWeekDaysWeekDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay</a>&nbsp;createDocumentCalendarsCalendarWeekDaysWeekDay()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriods</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods</a>&nbsp;createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriods()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods</code></a></div>
</li>
</ul>
<a name="createDocumentMarkers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentMarkers</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a>&nbsp;createDocumentMarkers()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers</code></a></div>
</li>
</ul>
<a name="createDocumentWorkspaceProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentWorkspaceProperties</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a>&nbsp;createDocumentWorkspaceProperties()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.WorkspaceProperties</code></a></div>
</li>
</ul>
<a name="createDocumentPrintingProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentPrintingProperties</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a>&nbsp;createDocumentPrintingProperties()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.PrintingProperties</code></a></div>
</li>
</ul>
<a name="createDocumentResourceUsageDiagram--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentResourceUsageDiagram</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a>&nbsp;createDocumentResourceUsageDiagram()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ResourceUsageDiagram</code></a></div>
</li>
</ul>
<a name="createDocumentProjectPortfolioView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjectPortfolioView</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a>&nbsp;createDocumentProjectPortfolioView()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ProjectPortfolioView</code></a></div>
</li>
</ul>
<a name="createActiveFilter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createActiveFilter</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/ActiveFilter.html" title="class in org.mpxj.conceptdraw.schema">ActiveFilter</a>&nbsp;createActiveFilter()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ActiveFilter.html" title="class in org.mpxj.conceptdraw.schema"><code>ActiveFilter</code></a></div>
</li>
</ul>
<a name="createHyperlinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHyperlinks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a>&nbsp;createHyperlinks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><code>Hyperlinks</code></a></div>
</li>
</ul>
<a name="createStyleProjectGridRowStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createStyleProjectGridRowStyle</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.GridRowStyle.html" title="class in org.mpxj.conceptdraw.schema">StyleProject.GridRowStyle</a>&nbsp;createStyleProjectGridRowStyle()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.GridRowStyle.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject.GridRowStyle</code></a></div>
</li>
</ul>
<a name="createTimeScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimeScale</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/TimeScale.html" title="class in org.mpxj.conceptdraw.schema">TimeScale</a>&nbsp;createTimeScale()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/TimeScale.html" title="class in org.mpxj.conceptdraw.schema"><code>TimeScale</code></a></div>
</li>
</ul>
<a name="createCalloutsCallout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCalloutsCallout</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema">Callouts.Callout</a>&nbsp;createCalloutsCallout()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema"><code>Callouts.Callout</code></a></div>
</li>
</ul>
<a name="createPPVItemsTypePPVItemCompleteJournalCompleteJournalEntry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPPVItemsTypePPVItemCompleteJournalCompleteJournalEntry</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry.html" title="class in org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry</a>&nbsp;createPPVItemsTypePPVItemCompleteJournalCompleteJournalEntry()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry.html" title="class in org.mpxj.conceptdraw.schema"><code>PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry</code></a></div>
</li>
</ul>
<a name="createViewPropertiesGridColumnsColumn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createViewPropertiesGridColumnsColumn</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.Column.html" title="class in org.mpxj.conceptdraw.schema">ViewProperties.GridColumns.Column</a>&nbsp;createViewPropertiesGridColumnsColumn()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/ViewProperties.GridColumns.Column.html" title="class in org.mpxj.conceptdraw.schema"><code>ViewProperties.GridColumns.Column</code></a></div>
</li>
</ul>
<a name="createDocumentDashboardsDashboard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentDashboardsDashboard</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.Dashboard.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards.Dashboard</a>&nbsp;createDocumentDashboardsDashboard()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.Dashboard.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards.Dashboard</code></a></div>
</li>
</ul>
<a name="createDocumentLinksLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentLinksLink</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.Link.html" title="class in org.mpxj.conceptdraw.schema">Document.Links.Link</a>&nbsp;createDocumentLinksLink()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.Link.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links.Link</code></a></div>
</li>
</ul>
<a name="createDocumentProjectsProjectTaskResourceAssignmentsResourceAssignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentProjectsProjectTaskResourceAssignmentsResourceAssignment</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment</a>&nbsp;createDocumentProjectsProjectTaskResourceAssignmentsResourceAssignment()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment</code></a></div>
</li>
</ul>
<a name="createDocumentResourcesResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentResourcesResource</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources.Resource</a>&nbsp;createDocumentResourcesResource()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources.Resource</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriodsTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriodsTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod</a>&nbsp;createDocumentCalendarsCalendarExceptedDaysExceptedDayTimePeriodsTimePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod</code></a></div>
</li>
</ul>
<a name="createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriodsTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriodsTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod</a>&nbsp;createDocumentCalendarsCalendarWeekDaysWeekDayTimePeriodsTimePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod</code></a></div>
</li>
</ul>
<a name="createDocumentMarkersMarker--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDocumentMarkersMarker</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers.Marker</a>&nbsp;createDocumentMarkersMarker()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers.Marker</code></a></div>
</li>
</ul>
<a name="createOutlineNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createOutlineNumber</h4>
<pre>public&nbsp;jakarta.xml.bind.JAXBElement&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;createOutlineNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a><code>&gt;</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - Java instance representing xml element's value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance of <code>JAXBElement</code><code>&lt;</code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a><code>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="createPriority-org.mpxj.Priority-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createPriority</h4>
<pre>public&nbsp;jakarta.xml.bind.JAXBElement&lt;<a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&gt;&nbsp;createPriority(<a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;value)</pre>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj"><code>Priority</code></a><code>&gt;</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - Java instance representing xml element's value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/Priority.html" title="class in org.mpxj"><code>Priority</code></a><code>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/PPVItemsType.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
