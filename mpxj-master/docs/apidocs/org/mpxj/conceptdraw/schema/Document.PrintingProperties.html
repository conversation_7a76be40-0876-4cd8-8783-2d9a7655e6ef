<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.PrintingProperties (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.PrintingProperties (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.PrintingProperties.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" target="_top">Frames</a></li>
<li><a href="Document.PrintingProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.conceptdraw.schema</div>
<h2 title="Class Document.PrintingProperties" class="title">Class Document.PrintingProperties</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.conceptdraw.schema.Document.PrintingProperties</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/conceptdraw/schema/Document.html" title="class in org.mpxj.conceptdraw.schema">Document</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.PrintingProperties</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;all&gt;
         &lt;element name="PrintOrientation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="MarginLeft" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="MarginRight" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="MarginTop" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="MarginBottom" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="MarginHeader" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="MarginFooter" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
         &lt;element name="FitTo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="PagesH" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="PagesV" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="TimescaleMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="DoPrintGrid" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
         &lt;element name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="LegendMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="FirstPageNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="PrintView" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="HeaderFooterFontName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="HeaderFooterFontSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       &lt;/all&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#doPrintGrid">doPrintGrid</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#finishDate">finishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#firstPageNumber">firstPageNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#fitTo">fitTo</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#footerCenter">footerCenter</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#footerLeft">footerLeft</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#footerRight">footerRight</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#headerCenter">headerCenter</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#headerFooterFontName">headerFooterFontName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#headerFooterFontSize">headerFooterFontSize</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#headerLeft">headerLeft</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#headerRight">headerRight</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#legendMode">legendMode</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginBottom">marginBottom</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginFooter">marginFooter</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginHeader">marginHeader</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginLeft">marginLeft</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginRight">marginRight</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#marginTop">marginTop</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#pagesH">pagesH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#pagesV">pagesV</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#printOrientation">printOrientation</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#printView">printView</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#timescaleMode">timescaleMode</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#PrintingProperties--">PrintingProperties</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFinishDate--">getFinishDate</a></span>()</code>
<div class="block">Gets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFirstPageNumber--">getFirstPageNumber</a></span>()</code>
<div class="block">Gets the value of the firstPageNumber property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFitTo--">getFitTo</a></span>()</code>
<div class="block">Gets the value of the fitTo property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFooterCenter--">getFooterCenter</a></span>()</code>
<div class="block">Gets the value of the footerCenter property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFooterLeft--">getFooterLeft</a></span>()</code>
<div class="block">Gets the value of the footerLeft property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getFooterRight--">getFooterRight</a></span>()</code>
<div class="block">Gets the value of the footerRight property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getHeaderCenter--">getHeaderCenter</a></span>()</code>
<div class="block">Gets the value of the headerCenter property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getHeaderFooterFontName--">getHeaderFooterFontName</a></span>()</code>
<div class="block">Gets the value of the headerFooterFontName property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getHeaderFooterFontSize--">getHeaderFooterFontSize</a></span>()</code>
<div class="block">Gets the value of the headerFooterFontSize property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getHeaderLeft--">getHeaderLeft</a></span>()</code>
<div class="block">Gets the value of the headerLeft property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getHeaderRight--">getHeaderRight</a></span>()</code>
<div class="block">Gets the value of the headerRight property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getLegendMode--">getLegendMode</a></span>()</code>
<div class="block">Gets the value of the legendMode property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginBottom--">getMarginBottom</a></span>()</code>
<div class="block">Gets the value of the marginBottom property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginFooter--">getMarginFooter</a></span>()</code>
<div class="block">Gets the value of the marginFooter property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginHeader--">getMarginHeader</a></span>()</code>
<div class="block">Gets the value of the marginHeader property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginLeft--">getMarginLeft</a></span>()</code>
<div class="block">Gets the value of the marginLeft property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginRight--">getMarginRight</a></span>()</code>
<div class="block">Gets the value of the marginRight property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getMarginTop--">getMarginTop</a></span>()</code>
<div class="block">Gets the value of the marginTop property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getPagesH--">getPagesH</a></span>()</code>
<div class="block">Gets the value of the pagesH property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getPagesV--">getPagesV</a></span>()</code>
<div class="block">Gets the value of the pagesV property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getPrintOrientation--">getPrintOrientation</a></span>()</code>
<div class="block">Gets the value of the printOrientation property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getPrintView--">getPrintView</a></span>()</code>
<div class="block">Gets the value of the printView property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#getTimescaleMode--">getTimescaleMode</a></span>()</code>
<div class="block">Gets the value of the timescaleMode property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#isDoPrintGrid--">isDoPrintGrid</a></span>()</code>
<div class="block">Gets the value of the doPrintGrid property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setDoPrintGrid-boolean-">setDoPrintGrid</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the value of the doPrintGrid property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFinishDate-java.time.LocalDate-">setFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFirstPageNumber-java.lang.Integer-">setFirstPageNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the firstPageNumber property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFitTo-java.lang.Integer-">setFitTo</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the fitTo property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFooterCenter-java.lang.String-">setFooterCenter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the footerCenter property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFooterLeft-java.lang.String-">setFooterLeft</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the footerLeft property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setFooterRight-java.lang.String-">setFooterRight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the footerRight property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setHeaderCenter-java.lang.String-">setHeaderCenter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the headerCenter property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setHeaderFooterFontName-java.lang.String-">setHeaderFooterFontName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the headerFooterFontName property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setHeaderFooterFontSize-java.lang.Integer-">setHeaderFooterFontSize</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the headerFooterFontSize property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setHeaderLeft-java.lang.String-">setHeaderLeft</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the headerLeft property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setHeaderRight-java.lang.String-">setHeaderRight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the headerRight property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setLegendMode-java.lang.Integer-">setLegendMode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the legendMode property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginBottom-java.math.BigDecimal-">setMarginBottom</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginBottom property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginFooter-java.math.BigDecimal-">setMarginFooter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginFooter property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginHeader-java.math.BigDecimal-">setMarginHeader</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginHeader property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginLeft-java.math.BigDecimal-">setMarginLeft</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginLeft property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginRight-java.math.BigDecimal-">setMarginRight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginRight property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setMarginTop-java.math.BigDecimal-">setMarginTop</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</code>
<div class="block">Sets the value of the marginTop property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setPagesH-java.lang.Integer-">setPagesH</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the pagesH property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setPagesV-java.lang.Integer-">setPagesV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the pagesV property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setPrintOrientation-java.lang.Integer-">setPrintOrientation</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the printOrientation property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setPrintView-java.lang.String-">setPrintView</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the printView property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setStartDate-java.time.LocalDate-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html#setTimescaleMode-java.lang.Integer-">setTimescaleMode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the timescaleMode property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="printOrientation">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printOrientation</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> printOrientation</pre>
</li>
</ul>
<a name="marginLeft">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginLeft</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginLeft</pre>
</li>
</ul>
<a name="marginRight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginRight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginRight</pre>
</li>
</ul>
<a name="marginTop">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginTop</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginTop</pre>
</li>
</ul>
<a name="marginBottom">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginBottom</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginBottom</pre>
</li>
</ul>
<a name="marginHeader">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginHeader</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginHeader</pre>
</li>
</ul>
<a name="marginFooter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>marginFooter</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a> marginFooter</pre>
</li>
</ul>
<a name="fitTo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fitTo</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> fitTo</pre>
</li>
</ul>
<a name="pagesH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagesH</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> pagesH</pre>
</li>
</ul>
<a name="pagesV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pagesV</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> pagesV</pre>
</li>
</ul>
<a name="timescaleMode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>timescaleMode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> timescaleMode</pre>
</li>
</ul>
<a name="doPrintGrid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doPrintGrid</h4>
<pre>protected&nbsp;boolean doPrintGrid</pre>
</li>
</ul>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a> startDate</pre>
</li>
</ul>
<a name="finishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a> finishDate</pre>
</li>
</ul>
<a name="headerLeft">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headerLeft</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> headerLeft</pre>
</li>
</ul>
<a name="headerCenter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headerCenter</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> headerCenter</pre>
</li>
</ul>
<a name="headerRight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headerRight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> headerRight</pre>
</li>
</ul>
<a name="footerLeft">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footerLeft</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> footerLeft</pre>
</li>
</ul>
<a name="footerCenter">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footerCenter</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> footerCenter</pre>
</li>
</ul>
<a name="footerRight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footerRight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> footerRight</pre>
</li>
</ul>
<a name="legendMode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>legendMode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> legendMode</pre>
</li>
</ul>
<a name="firstPageNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstPageNumber</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> firstPageNumber</pre>
</li>
</ul>
<a name="printView">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printView</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> printView</pre>
</li>
</ul>
<a name="headerFooterFontName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>headerFooterFontName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> headerFooterFontName</pre>
</li>
</ul>
<a name="headerFooterFontSize">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>headerFooterFontSize</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> headerFooterFontSize</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PrintingProperties--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PrintingProperties</h4>
<pre>public&nbsp;PrintingProperties()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPrintOrientation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintOrientation</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPrintOrientation()</pre>
<div class="block">Gets the value of the printOrientation property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPrintOrientation-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintOrientation</h4>
<pre>public&nbsp;void&nbsp;setPrintOrientation(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the printOrientation property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginLeft</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginLeft()</pre>
<div class="block">Gets the value of the marginLeft property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginLeft-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginLeft</h4>
<pre>public&nbsp;void&nbsp;setMarginLeft(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginLeft property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginRight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginRight()</pre>
<div class="block">Gets the value of the marginRight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginRight-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginRight</h4>
<pre>public&nbsp;void&nbsp;setMarginRight(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginRight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginTop</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginTop()</pre>
<div class="block">Gets the value of the marginTop property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginTop-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginTop</h4>
<pre>public&nbsp;void&nbsp;setMarginTop(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginTop property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginBottom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginBottom</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginBottom()</pre>
<div class="block">Gets the value of the marginBottom property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginBottom-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginBottom</h4>
<pre>public&nbsp;void&nbsp;setMarginBottom(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginBottom property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginHeader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginHeader</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginHeader()</pre>
<div class="block">Gets the value of the marginHeader property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginHeader-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginHeader</h4>
<pre>public&nbsp;void&nbsp;setMarginHeader(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginHeader property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarginFooter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarginFooter</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;getMarginFooter()</pre>
<div class="block">Gets the value of the marginFooter property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarginFooter-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarginFooter</h4>
<pre>public&nbsp;void&nbsp;setMarginFooter(<a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value)</pre>
<div class="block">Sets the value of the marginFooter property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math"><code>BigDecimal</code></a></dd>
</dl>
</li>
</ul>
<a name="getFitTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFitTo</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFitTo()</pre>
<div class="block">Gets the value of the fitTo property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFitTo-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFitTo</h4>
<pre>public&nbsp;void&nbsp;setFitTo(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the fitTo property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPagesH--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPagesH</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPagesH()</pre>
<div class="block">Gets the value of the pagesH property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPagesH-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPagesH</h4>
<pre>public&nbsp;void&nbsp;setPagesH(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pagesH property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPagesV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPagesV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPagesV()</pre>
<div class="block">Gets the value of the pagesV property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPagesV-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPagesV</h4>
<pre>public&nbsp;void&nbsp;setPagesV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pagesV property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getTimescaleMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimescaleMode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getTimescaleMode()</pre>
<div class="block">Gets the value of the timescaleMode property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setTimescaleMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimescaleMode</h4>
<pre>public&nbsp;void&nbsp;setTimescaleMode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the timescaleMode property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDoPrintGrid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoPrintGrid</h4>
<pre>public&nbsp;boolean&nbsp;isDoPrintGrid()</pre>
<div class="block">Gets the value of the doPrintGrid property.</div>
</li>
</ul>
<a name="setDoPrintGrid-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDoPrintGrid</h4>
<pre>public&nbsp;void&nbsp;setDoPrintGrid(boolean&nbsp;value)</pre>
<div class="block">Sets the value of the doPrintGrid property.</div>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;getFinishDate()</pre>
<div class="block">Gets the value of the finishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinishDate-java.time.LocalDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishDate</h4>
<pre>public&nbsp;void&nbsp;setFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the finishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaderLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderLeft</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHeaderLeft()</pre>
<div class="block">Gets the value of the headerLeft property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaderLeft-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeaderLeft</h4>
<pre>public&nbsp;void&nbsp;setHeaderLeft(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headerLeft property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaderCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderCenter</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHeaderCenter()</pre>
<div class="block">Gets the value of the headerCenter property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaderCenter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeaderCenter</h4>
<pre>public&nbsp;void&nbsp;setHeaderCenter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headerCenter property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaderRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderRight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHeaderRight()</pre>
<div class="block">Gets the value of the headerRight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaderRight-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeaderRight</h4>
<pre>public&nbsp;void&nbsp;setHeaderRight(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headerRight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFooterLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooterLeft</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFooterLeft()</pre>
<div class="block">Gets the value of the footerLeft property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFooterLeft-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooterLeft</h4>
<pre>public&nbsp;void&nbsp;setFooterLeft(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the footerLeft property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFooterCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooterCenter</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFooterCenter()</pre>
<div class="block">Gets the value of the footerCenter property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFooterCenter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooterCenter</h4>
<pre>public&nbsp;void&nbsp;setFooterCenter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the footerCenter property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFooterRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFooterRight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFooterRight()</pre>
<div class="block">Gets the value of the footerRight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFooterRight-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFooterRight</h4>
<pre>public&nbsp;void&nbsp;setFooterRight(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the footerRight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLegendMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLegendMode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLegendMode()</pre>
<div class="block">Gets the value of the legendMode property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLegendMode-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLegendMode</h4>
<pre>public&nbsp;void&nbsp;setLegendMode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the legendMode property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFirstPageNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstPageNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFirstPageNumber()</pre>
<div class="block">Gets the value of the firstPageNumber property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFirstPageNumber-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstPageNumber</h4>
<pre>public&nbsp;void&nbsp;setFirstPageNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the firstPageNumber property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPrintView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintView</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPrintView()</pre>
<div class="block">Gets the value of the printView property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPrintView-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintView</h4>
<pre>public&nbsp;void&nbsp;setPrintView(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the printView property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaderFooterFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderFooterFontName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHeaderFooterFontName()</pre>
<div class="block">Gets the value of the headerFooterFontName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaderFooterFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeaderFooterFontName</h4>
<pre>public&nbsp;void&nbsp;setHeaderFooterFontName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headerFooterFontName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeaderFooterFontSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderFooterFontSize</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getHeaderFooterFontSize()</pre>
<div class="block">Gets the value of the headerFooterFontSize property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHeaderFooterFontSize-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setHeaderFooterFontSize</h4>
<pre>public&nbsp;void&nbsp;setHeaderFooterFontSize(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the headerFooterFontSize property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.PrintingProperties.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.Marker.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" target="_top">Frames</a></li>
<li><a href="Document.PrintingProperties.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
