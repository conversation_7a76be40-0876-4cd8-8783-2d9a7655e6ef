<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.html" target="_top">Frames</a></li>
<li><a href="Document.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.conceptdraw.schema</div>
<h2 title="Class Document" class="title">Class Document</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.conceptdraw.schema.Document</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Document</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="WorkspaceProperties"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="CurrencySymbol" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="CurrencyPosition" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="CurrencyDigits" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="HoursPerDay" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="HoursPerWeek" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="DaysPerMonth" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="CalcCPForSubprojects" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                   &lt;element name="MaximumSlack" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="PrintingProperties"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;all&gt;
                   &lt;element name="PrintOrientation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="MarginLeft" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="MarginRight" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="MarginTop" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="MarginBottom" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="MarginHeader" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="MarginFooter" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                   &lt;element name="FitTo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="PagesH" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="PagesV" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="TimescaleMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="DoPrintGrid" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
                   &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
                   &lt;element name="HeaderLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="HeaderCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="HeaderRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="FooterLeft" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="FooterCenter" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="FooterRight" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="LegendMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="FirstPageNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="PrintView" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="HeaderFooterFontName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                   &lt;element name="HeaderFooterFontSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                 &lt;/all&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ThemeID" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="ShowAssignedResources" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="Markers"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Marker" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="DisplayStyle" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceUsageDiagram"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ActiveFilter" minOccurs="0"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Calendars"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Calendar" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="BaseCalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="WeekDays"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="WeekDay" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="Day"&gt;
                                                   &lt;simpleType&gt;
                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                                       &lt;enumeration value="0"/&gt;
                                                       &lt;enumeration value="1"/&gt;
                                                       &lt;enumeration value="2"/&gt;
                                                       &lt;enumeration value="3"/&gt;
                                                       &lt;enumeration value="4"/&gt;
                                                       &lt;enumeration value="5"/&gt;
                                                       &lt;enumeration value="6"/&gt;
                                                     &lt;/restriction&gt;
                                                   &lt;/simpleType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="TimePeriods"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;sequence&gt;
                                                                     &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                     &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                   &lt;/sequence&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="ExceptedDays"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="ExceptedDay" maxOccurs="unbounded" minOccurs="0"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
                                                 &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="TimePeriods"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;sequence&gt;
                                                                     &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                     &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                   &lt;/sequence&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Resources"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
                   &lt;element name="Resource" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
                             &lt;element name="CalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="Type"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="SubType"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                   &lt;enumeration value="work"/&gt;
                                   &lt;enumeration value="material"/&gt;
                                   &lt;enumeration value="cost"/&gt;
                                   &lt;enumeration value="equipment"/&gt;
                                   &lt;enumeration value="company"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="EMail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                             &lt;element name="CostTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
                             &lt;element name="Group" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="ShowAssignedTasks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
                             &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Projects"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
                   &lt;element name="Project" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
                             &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ProjectProps"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ViewProperties"/&gt;
                             &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}TimeScale"/&gt;
                             &lt;element name="Task" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
                                       &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                       &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                       &lt;element name="BaseStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="BaseFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="BaseDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
                                       &lt;element name="BaseDurationTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
                                       &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="ActualDuration" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}DurationType"/&gt;
                                       &lt;element name="TemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="DeadlineTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="BaselineStartTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="BaselineFinishTemplateOffset" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
                                       &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                                       &lt;element name="Cost1" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                                       &lt;element name="ValidatedByProject" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                       &lt;element name="RecalcBase1"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="0"/&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="RecalcBase2"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="0"/&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="IsMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                       &lt;group ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}BaselineGroup" minOccurs="0"/&gt;
                                       &lt;element name="Complete" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
                                       &lt;element name="IsHaveDeadline" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                       &lt;element name="SchedulingType"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                                             &lt;enumeration value="fixedDuration"/&gt;
                                             &lt;enumeration value="fixedUnits"/&gt;
                                             &lt;enumeration value="fixedWork"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="IsEffortDriven" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Priority"/&gt;
                                       &lt;element name="MarkedByUser" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                       &lt;element name="ShowSubtasks" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
                                       &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
                                       &lt;element name="ResourceAssignments"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="ResourceAssignment" maxOccurs="unbounded" minOccurs="0"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                                           &lt;element name="ResourceID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                                           &lt;element name="Use" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                                                           &lt;element name="ManHour" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                       &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Callouts"/&gt;
                                       &lt;element name="DeadlineDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ProjectPortfolioView"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="CompleteJournalTrackingPeriod" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}LongTimeUnitType"/&gt;
                   &lt;element name="PPVItems" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}PPVItemsType"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Links"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Link" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;choice&gt;
                               &lt;sequence&gt;
                                 &lt;element name="SourceTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                 &lt;element name="DestinationTaskID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                               &lt;/sequence&gt;
                               &lt;sequence&gt;
                                 &lt;element name="SourceProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                 &lt;element name="DestinationProjectID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                               &lt;/sequence&gt;
                             &lt;/choice&gt;
                             &lt;element name="Type"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="0"/&gt;
                                   &lt;enumeration value="1"/&gt;
                                   &lt;enumeration value="2"/&gt;
                                   &lt;enumeration value="3"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                             &lt;element name="Lag" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
                             &lt;element name="LagUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Dashboards"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Dashboard" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
       &lt;attribute name="Application" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#application">application</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#calendars">calendars</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#dashboards">dashboards</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#links">links</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#markers">markers</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#printingProperties">printingProperties</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#projectPortfolioView">projectPortfolioView</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#projects">projects</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#resources">resources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#resourceUsageDiagram">resourceUsageDiagram</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#showAssignedResources">showAssignedResources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#themeID">themeID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#version">version</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#workspaceProperties">workspaceProperties</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#Document--">Document</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getApplication--">getApplication</a></span>()</code>
<div class="block">Gets the value of the application property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getCalendars--">getCalendars</a></span>()</code>
<div class="block">Gets the value of the calendars property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getDashboards--">getDashboards</a></span>()</code>
<div class="block">Gets the value of the dashboards property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getLinks--">getLinks</a></span>()</code>
<div class="block">Gets the value of the links property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getMarkers--">getMarkers</a></span>()</code>
<div class="block">Gets the value of the markers property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getPrintingProperties--">getPrintingProperties</a></span>()</code>
<div class="block">Gets the value of the printingProperties property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getProjectPortfolioView--">getProjectPortfolioView</a></span>()</code>
<div class="block">Gets the value of the projectPortfolioView property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getProjects--">getProjects</a></span>()</code>
<div class="block">Gets the value of the projects property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getResources--">getResources</a></span>()</code>
<div class="block">Gets the value of the resources property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getResourceUsageDiagram--">getResourceUsageDiagram</a></span>()</code>
<div class="block">Gets the value of the resourceUsageDiagram property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getThemeID--">getThemeID</a></span>()</code>
<div class="block">Gets the value of the themeID property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Gets the value of the version property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#getWorkspaceProperties--">getWorkspaceProperties</a></span>()</code>
<div class="block">Gets the value of the workspaceProperties property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#isShowAssignedResources--">isShowAssignedResources</a></span>()</code>
<div class="block">Gets the value of the showAssignedResources property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setApplication-java.lang.String-">setApplication</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the application property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setCalendars-org.mpxj.conceptdraw.schema.Document.Calendars-">setCalendars</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendars property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setDashboards-org.mpxj.conceptdraw.schema.Document.Dashboards-">setDashboards</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a>&nbsp;value)</code>
<div class="block">Sets the value of the dashboards property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setLinks-org.mpxj.conceptdraw.schema.Document.Links-">setLinks</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a>&nbsp;value)</code>
<div class="block">Sets the value of the links property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setMarkers-org.mpxj.conceptdraw.schema.Document.Markers-">setMarkers</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a>&nbsp;value)</code>
<div class="block">Sets the value of the markers property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setPrintingProperties-org.mpxj.conceptdraw.schema.Document.PrintingProperties-">setPrintingProperties</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a>&nbsp;value)</code>
<div class="block">Sets the value of the printingProperties property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setProjectPortfolioView-org.mpxj.conceptdraw.schema.Document.ProjectPortfolioView-">setProjectPortfolioView</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectPortfolioView property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setProjects-org.mpxj.conceptdraw.schema.Document.Projects-">setProjects</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a>&nbsp;value)</code>
<div class="block">Sets the value of the projects property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setResources-org.mpxj.conceptdraw.schema.Document.Resources-">setResources</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a>&nbsp;value)</code>
<div class="block">Sets the value of the resources property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setResourceUsageDiagram-org.mpxj.conceptdraw.schema.Document.ResourceUsageDiagram-">setResourceUsageDiagram</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceUsageDiagram property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setShowAssignedResources-boolean-">setShowAssignedResources</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the value of the showAssignedResources property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setThemeID-java.lang.String-">setThemeID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the themeID property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setVersion-java.lang.Integer-">setVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the version property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.html#setWorkspaceProperties-org.mpxj.conceptdraw.schema.Document.WorkspaceProperties-">setWorkspaceProperties</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a>&nbsp;value)</code>
<div class="block">Sets the value of the workspaceProperties property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="workspaceProperties">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>workspaceProperties</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a> workspaceProperties</pre>
</li>
</ul>
<a name="printingProperties">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printingProperties</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a> printingProperties</pre>
</li>
</ul>
<a name="themeID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>themeID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> themeID</pre>
</li>
</ul>
<a name="showAssignedResources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAssignedResources</h4>
<pre>protected&nbsp;boolean showAssignedResources</pre>
</li>
</ul>
<a name="markers">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>markers</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a> markers</pre>
</li>
</ul>
<a name="resourceUsageDiagram">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceUsageDiagram</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a> resourceUsageDiagram</pre>
</li>
</ul>
<a name="calendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendars</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a> calendars</pre>
</li>
</ul>
<a name="resources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a> resources</pre>
</li>
</ul>
<a name="projects">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projects</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a> projects</pre>
</li>
</ul>
<a name="projectPortfolioView">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectPortfolioView</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a> projectPortfolioView</pre>
</li>
</ul>
<a name="links">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>links</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a> links</pre>
</li>
</ul>
<a name="dashboards">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dashboards</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a> dashboards</pre>
</li>
</ul>
<a name="application">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>application</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> application</pre>
</li>
</ul>
<a name="version">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>version</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> version</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Document--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Document</h4>
<pre>public&nbsp;Document()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWorkspaceProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkspaceProperties</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a>&nbsp;getWorkspaceProperties()</pre>
<div class="block">Gets the value of the workspaceProperties property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.WorkspaceProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="setWorkspaceProperties-org.mpxj.conceptdraw.schema.Document.WorkspaceProperties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkspaceProperties</h4>
<pre>public&nbsp;void&nbsp;setWorkspaceProperties(<a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a>&nbsp;value)</pre>
<div class="block">Sets the value of the workspaceProperties property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.WorkspaceProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.WorkspaceProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="getPrintingProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintingProperties</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a>&nbsp;getPrintingProperties()</pre>
<div class="block">Gets the value of the printingProperties property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.PrintingProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="setPrintingProperties-org.mpxj.conceptdraw.schema.Document.PrintingProperties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrintingProperties</h4>
<pre>public&nbsp;void&nbsp;setPrintingProperties(<a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema">Document.PrintingProperties</a>&nbsp;value)</pre>
<div class="block">Sets the value of the printingProperties property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.PrintingProperties.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.PrintingProperties</code></a></dd>
</dl>
</li>
</ul>
<a name="getThemeID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThemeID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getThemeID()</pre>
<div class="block">Gets the value of the themeID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setThemeID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThemeID</h4>
<pre>public&nbsp;void&nbsp;setThemeID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the themeID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isShowAssignedResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowAssignedResources</h4>
<pre>public&nbsp;boolean&nbsp;isShowAssignedResources()</pre>
<div class="block">Gets the value of the showAssignedResources property.</div>
</li>
</ul>
<a name="setShowAssignedResources-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowAssignedResources</h4>
<pre>public&nbsp;void&nbsp;setShowAssignedResources(boolean&nbsp;value)</pre>
<div class="block">Sets the value of the showAssignedResources property.</div>
</li>
</ul>
<a name="getMarkers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarkers</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a>&nbsp;getMarkers()</pre>
<div class="block">Gets the value of the markers property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarkers-org.mpxj.conceptdraw.schema.Document.Markers-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarkers</h4>
<pre>public&nbsp;void&nbsp;setMarkers(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema">Document.Markers</a>&nbsp;value)</pre>
<div class="block">Sets the value of the markers property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Markers.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Markers</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceUsageDiagram--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceUsageDiagram</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a>&nbsp;getResourceUsageDiagram()</pre>
<div class="block">Gets the value of the resourceUsageDiagram property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ResourceUsageDiagram</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceUsageDiagram-org.mpxj.conceptdraw.schema.Document.ResourceUsageDiagram-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceUsageDiagram</h4>
<pre>public&nbsp;void&nbsp;setResourceUsageDiagram(<a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceUsageDiagram property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ResourceUsageDiagram</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a>&nbsp;getCalendars()</pre>
<div class="block">Gets the value of the calendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendars-org.mpxj.conceptdraw.schema.Document.Calendars-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendars</h4>
<pre>public&nbsp;void&nbsp;setCalendars(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="getResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResources</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a>&nbsp;getResources()</pre>
<div class="block">Gets the value of the resources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="setResources-org.mpxj.conceptdraw.schema.Document.Resources-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResources</h4>
<pre>public&nbsp;void&nbsp;setResources(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjects</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a>&nbsp;getProjects()</pre>
<div class="block">Gets the value of the projects property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjects-org.mpxj.conceptdraw.schema.Document.Projects-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjects</h4>
<pre>public&nbsp;void&nbsp;setProjects(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema">Document.Projects</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projects property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Projects.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Projects</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectPortfolioView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectPortfolioView</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a>&nbsp;getProjectPortfolioView()</pre>
<div class="block">Gets the value of the projectPortfolioView property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ProjectPortfolioView</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectPortfolioView-org.mpxj.conceptdraw.schema.Document.ProjectPortfolioView-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectPortfolioView</h4>
<pre>public&nbsp;void&nbsp;setProjectPortfolioView(<a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectPortfolioView property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.ProjectPortfolioView.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.ProjectPortfolioView</code></a></dd>
</dl>
</li>
</ul>
<a name="getLinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a>&nbsp;getLinks()</pre>
<div class="block">Gets the value of the links property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links</code></a></dd>
</dl>
</li>
</ul>
<a name="setLinks-org.mpxj.conceptdraw.schema.Document.Links-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinks</h4>
<pre>public&nbsp;void&nbsp;setLinks(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema">Document.Links</a>&nbsp;value)</pre>
<div class="block">Sets the value of the links property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Links.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Links</code></a></dd>
</dl>
</li>
</ul>
<a name="getDashboards--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDashboards</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a>&nbsp;getDashboards()</pre>
<div class="block">Gets the value of the dashboards property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards</code></a></dd>
</dl>
</li>
</ul>
<a name="setDashboards-org.mpxj.conceptdraw.schema.Document.Dashboards-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDashboards</h4>
<pre>public&nbsp;void&nbsp;setDashboards(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema">Document.Dashboards</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dashboards property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Dashboards.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Dashboards</code></a></dd>
</dl>
</li>
</ul>
<a name="getApplication--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplication</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getApplication()</pre>
<div class="block">Gets the value of the application property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setApplication-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setApplication</h4>
<pre>public&nbsp;void&nbsp;setApplication(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the application property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getVersion()</pre>
<div class="block">Gets the value of the version property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setVersion-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVersion</h4>
<pre>public&nbsp;void&nbsp;setVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the version property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Callouts.Callout.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.html" target="_top">Frames</a></li>
<li><a href="Document.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
