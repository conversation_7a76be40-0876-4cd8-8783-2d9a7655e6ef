<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.Resources.Resource (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.Resources.Resource (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Resources.Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" target="_top">Frames</a></li>
<li><a href="Document.Resources.Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.conceptdraw.schema</div>
<h2 title="Class Document.Resources.Resource" class="title">Class Document.Resources.Resource</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.conceptdraw.schema.Document.Resources.Resource</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema">Document.Resources</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.Resources.Resource</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}OutlineNumber"/&gt;
         &lt;element name="CalendarID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="Type"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
               &lt;enumeration value="0"/&gt;
               &lt;enumeration value="1"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="SubType"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="work"/&gt;
               &lt;enumeration value="material"/&gt;
               &lt;enumeration value="cost"/&gt;
               &lt;enumeration value="equipment"/&gt;
               &lt;enumeration value="company"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="EMail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="Note" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}double"/&gt;
         &lt;element name="CostTimeUnit" type="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}ShortTimeUnitType"/&gt;
         &lt;element name="Group" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="ShowAssignedTasks" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}StyleProject"/&gt;
         &lt;element name="MarkerID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element ref="{http://www.schemas.conceptdraw.com/cdprj/document.xsd}Hyperlinks"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#calendarID">calendarID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#cost">cost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#costTimeUnit">costTimeUnit</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#eMail">eMail</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#group">group</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#hyperlinks">hyperlinks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#id">id</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#markerID">markerID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#note">note</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#outlineNumber">outlineNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#showAssignedTasks">showAssignedTasks</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#styleProject">styleProject</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#subType">subType</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#type">type</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#Resource--">Resource</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getCalendarID--">getCalendarID</a></span>()</code>
<div class="block">Gets the value of the calendarID property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getCost--">getCost</a></span>()</code>
<div class="block">Gets the value of the cost property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getCostTimeUnit--">getCostTimeUnit</a></span>()</code>
<div class="block">Gets the value of the costTimeUnit property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getEMail--">getEMail</a></span>()</code>
<div class="block">Gets the value of the eMail property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getGroup--">getGroup</a></span>()</code>
<div class="block">Gets the value of the group property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getHyperlinks--">getHyperlinks</a></span>()</code>
<div class="block">Hyperlinks associated with the resource.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getID--">getID</a></span>()</code>
<div class="block">Gets the value of the id property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getMarkerID--">getMarkerID</a></span>()</code>
<div class="block">Gets the value of the markerID property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getNote--">getNote</a></span>()</code>
<div class="block">Gets the value of the note property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getOutlineNumber--">getOutlineNumber</a></span>()</code>
<div class="block">The outline number of the resource.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getStyleProject--">getStyleProject</a></span>()</code>
<div class="block">Gets the value of the styleProject property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getSubType--">getSubType</a></span>()</code>
<div class="block">Gets the value of the subType property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#getType--">getType</a></span>()</code>
<div class="block">Gets the value of the type property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#isShowAssignedTasks--">isShowAssignedTasks</a></span>()</code>
<div class="block">Gets the value of the showAssignedTasks property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setCalendarID-java.lang.Integer-">setCalendarID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarID property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setCost-java.lang.Double-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cost property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setCostTimeUnit-org.mpxj.TimeUnit-">setCostTimeUnit</a></span>(<a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;value)</code>
<div class="block">Sets the value of the costTimeUnit property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setEMail-java.lang.String-">setEMail</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the eMail property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setGroup-java.lang.String-">setGroup</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the group property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setHyperlinks-org.mpxj.conceptdraw.schema.Hyperlinks-">setHyperlinks</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a>&nbsp;value)</code>
<div class="block">Sets the value of the hyperlinks property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setID-java.lang.Integer-">setID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the id property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setMarkerID-java.lang.Integer-">setMarkerID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the markerID property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setNote-java.lang.String-">setNote</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the note property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setOutlineNumber-java.lang.String-">setOutlineNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the outlineNumber property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setShowAssignedTasks-boolean-">setShowAssignedTasks</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the value of the showAssignedTasks property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setStyleProject-org.mpxj.conceptdraw.schema.StyleProject-">setStyleProject</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a>&nbsp;value)</code>
<div class="block">Sets the value of the styleProject property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setSubType-org.mpxj.ResourceType-">setSubType</a></span>(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</code>
<div class="block">Sets the value of the subType property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.Resource.html#setType-org.mpxj.ResourceType-">setType</a></span>(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</code>
<div class="block">Sets the value of the type property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="id">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> id</pre>
</li>
</ul>
<a name="outlineNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>outlineNumber</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> outlineNumber</pre>
</li>
</ul>
<a name="calendarID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> calendarID</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="type">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a> type</pre>
</li>
</ul>
<a name="subType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subType</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a> subType</pre>
</li>
</ul>
<a name="eMail">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>eMail</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> eMail</pre>
</li>
</ul>
<a name="note">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>note</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> note</pre>
</li>
</ul>
<a name="cost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cost</pre>
</li>
</ul>
<a name="costTimeUnit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costTimeUnit</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a> costTimeUnit</pre>
</li>
</ul>
<a name="group">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>group</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> group</pre>
</li>
</ul>
<a name="showAssignedTasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAssignedTasks</h4>
<pre>protected&nbsp;boolean showAssignedTasks</pre>
</li>
</ul>
<a name="styleProject">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>styleProject</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a> styleProject</pre>
</li>
</ul>
<a name="markerID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>markerID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> markerID</pre>
</li>
</ul>
<a name="hyperlinks">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hyperlinks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a> hyperlinks</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Resource--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Resource</h4>
<pre>public&nbsp;Resource()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getID()</pre>
<div class="block">Gets the value of the id property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>public&nbsp;void&nbsp;setID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the id property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getOutlineNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOutlineNumber()</pre>
<div class="block">The outline number of the resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOutlineNumber-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineNumber</h4>
<pre>public&nbsp;void&nbsp;setOutlineNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the outlineNumber property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCalendarID()</pre>
<div class="block">Gets the value of the calendarID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarID</h4>
<pre>public&nbsp;void&nbsp;setCalendarID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;getType()</pre>
<div class="block">Gets the value of the type property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setType-org.mpxj.ResourceType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the type property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getSubType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;getSubType()</pre>
<div class="block">Gets the value of the subType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSubType-org.mpxj.ResourceType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubType</h4>
<pre>public&nbsp;void&nbsp;setSubType(<a href="../../../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the subType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEMail--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEMail</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEMail()</pre>
<div class="block">Gets the value of the eMail property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEMail-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEMail</h4>
<pre>public&nbsp;void&nbsp;setEMail(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the eMail property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getNote--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNote</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNote()</pre>
<div class="block">Gets the value of the note property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNote-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNote</h4>
<pre>public&nbsp;void&nbsp;setNote(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the note property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCost()</pre>
<div class="block">Gets the value of the cost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostTimeUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostTimeUnit</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;getCostTimeUnit()</pre>
<div class="block">Gets the value of the costTimeUnit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostTimeUnit-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostTimeUnit</h4>
<pre>public&nbsp;void&nbsp;setCostTimeUnit(<a href="../../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costTimeUnit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroup</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getGroup()</pre>
<div class="block">Gets the value of the group property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroup</h4>
<pre>public&nbsp;void&nbsp;setGroup(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the group property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isShowAssignedTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isShowAssignedTasks</h4>
<pre>public&nbsp;boolean&nbsp;isShowAssignedTasks()</pre>
<div class="block">Gets the value of the showAssignedTasks property.</div>
</li>
</ul>
<a name="setShowAssignedTasks-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowAssignedTasks</h4>
<pre>public&nbsp;void&nbsp;setShowAssignedTasks(boolean&nbsp;value)</pre>
<div class="block">Sets the value of the showAssignedTasks property.</div>
</li>
</ul>
<a name="getStyleProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStyleProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a>&nbsp;getStyleProject()</pre>
<div class="block">Gets the value of the styleProject property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject</code></a></dd>
</dl>
</li>
</ul>
<a name="setStyleProject-org.mpxj.conceptdraw.schema.StyleProject-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStyleProject</h4>
<pre>public&nbsp;void&nbsp;setStyleProject(<a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema">StyleProject</a>&nbsp;value)</pre>
<div class="block">Sets the value of the styleProject property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/StyleProject.html" title="class in org.mpxj.conceptdraw.schema"><code>StyleProject</code></a></dd>
</dl>
</li>
</ul>
<a name="getMarkerID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMarkerID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getMarkerID()</pre>
<div class="block">Gets the value of the markerID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMarkerID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMarkerID</h4>
<pre>public&nbsp;void&nbsp;setMarkerID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the markerID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHyperlinks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a>&nbsp;getHyperlinks()</pre>
<div class="block">Hyperlinks associated with the resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><code>Hyperlinks</code></a></dd>
</dl>
</li>
</ul>
<a name="setHyperlinks-org.mpxj.conceptdraw.schema.Hyperlinks-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setHyperlinks</h4>
<pre>public&nbsp;void&nbsp;setHyperlinks(<a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema">Hyperlinks</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hyperlinks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Hyperlinks.html" title="class in org.mpxj.conceptdraw.schema"><code>Hyperlinks</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Resources.Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Resources.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.ResourceUsageDiagram.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.Resources.Resource.html" target="_top">Frames</a></li>
<li><a href="Document.Resources.Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
