<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Calendar (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Calendar (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Calendar.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/Calendar.html" target="_top">Frames</a></li>
<li><a href="Calendar.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.planner.schema</div>
<h2 title="Class Calendar" class="title">Class Calendar</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.planner.schema.Calendar</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Calendar</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element ref="{}default-week"/&gt;
         &lt;element ref="{}overridden-day-types" minOccurs="0"/&gt;
         &lt;element ref="{}days" minOccurs="0"/&gt;
         &lt;element ref="{}calendar" maxOccurs="unbounded" minOccurs="0"/&gt;
       &lt;/sequence&gt;
       &lt;attribute name="name" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="id" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema">Calendar</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#calendar">calendar</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#days">days</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#defaultWeek">defaultWeek</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#id">id</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#overriddenDayTypes">overriddenDayTypes</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#Calendar--">Calendar</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema">Calendar</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getCalendar--">getCalendar</a></span>()</code>
<div class="block">Gets the value of the calendar property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getDays--">getDays</a></span>()</code>
<div class="block">Gets the value of the days property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getDefaultWeek--">getDefaultWeek</a></span>()</code>
<div class="block">Gets the value of the defaultWeek property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getId--">getId</a></span>()</code>
<div class="block">Gets the value of the id property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#getOverriddenDayTypes--">getOverriddenDayTypes</a></span>()</code>
<div class="block">Gets the value of the overriddenDayTypes property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#setDays-org.mpxj.planner.schema.Days-">setDays</a></span>(<a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a>&nbsp;value)</code>
<div class="block">Sets the value of the days property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#setDefaultWeek-org.mpxj.planner.schema.DefaultWeek-">setDefaultWeek</a></span>(<a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a>&nbsp;value)</code>
<div class="block">Sets the value of the defaultWeek property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#setId-java.lang.String-">setId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the id property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Calendar.html#setOverriddenDayTypes-org.mpxj.planner.schema.OverriddenDayTypes-">setOverriddenDayTypes</a></span>(<a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a>&nbsp;value)</code>
<div class="block">Sets the value of the overriddenDayTypes property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="defaultWeek">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>defaultWeek</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a> defaultWeek</pre>
</li>
</ul>
<a name="overriddenDayTypes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overriddenDayTypes</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a> overriddenDayTypes</pre>
</li>
</ul>
<a name="days">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>days</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a> days</pre>
</li>
</ul>
<a name="calendar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendar</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema">Calendar</a>&gt; calendar</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="id">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>id</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> id</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Calendar--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Calendar</h4>
<pre>public&nbsp;Calendar()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDefaultWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultWeek</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a>&nbsp;getDefaultWeek()</pre>
<div class="block">Gets the value of the defaultWeek property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema"><code>DefaultWeek</code></a></dd>
</dl>
</li>
</ul>
<a name="setDefaultWeek-org.mpxj.planner.schema.DefaultWeek-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultWeek</h4>
<pre>public&nbsp;void&nbsp;setDefaultWeek(<a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema">DefaultWeek</a>&nbsp;value)</pre>
<div class="block">Sets the value of the defaultWeek property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema"><code>DefaultWeek</code></a></dd>
</dl>
</li>
</ul>
<a name="getOverriddenDayTypes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverriddenDayTypes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a>&nbsp;getOverriddenDayTypes()</pre>
<div class="block">Gets the value of the overriddenDayTypes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema"><code>OverriddenDayTypes</code></a></dd>
</dl>
</li>
</ul>
<a name="setOverriddenDayTypes-org.mpxj.planner.schema.OverriddenDayTypes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverriddenDayTypes</h4>
<pre>public&nbsp;void&nbsp;setOverriddenDayTypes(<a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema">OverriddenDayTypes</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overriddenDayTypes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema"><code>OverriddenDayTypes</code></a></dd>
</dl>
</li>
</ul>
<a name="getDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a>&nbsp;getDays()</pre>
<div class="block">Gets the value of the days property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema"><code>Days</code></a></dd>
</dl>
</li>
</ul>
<a name="setDays-org.mpxj.planner.schema.Days-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDays</h4>
<pre>public&nbsp;void&nbsp;setDays(<a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema">Days</a>&nbsp;value)</pre>
<div class="block">Sets the value of the days property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema"><code>Days</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendar</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema">Calendar</a>&gt;&nbsp;getCalendar()</pre>
<div class="block">Gets the value of the calendar property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the calendar property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getCalendar().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema"><code>Calendar</code></a></div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getId()</pre>
<div class="block">Gets the value of the id property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setId-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setId</h4>
<pre>public&nbsp;void&nbsp;setId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the id property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Calendar.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/Calendar.html" target="_top">Frames</a></li>
<li><a href="Calendar.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
