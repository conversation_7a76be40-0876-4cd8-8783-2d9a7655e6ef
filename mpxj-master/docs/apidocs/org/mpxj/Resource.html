<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Resource (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Resource (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/RelationType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Resource.html" target="_top">Frames</a></li>
<li><a href="Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class Resource" class="title">Class Resource</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">org.mpxj.AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;</li>
<li>
<ul class="inheritance">
<li>org.mpxj.Resource</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;, <a href="../../org/mpxj/ChildResourceContainer.html" title="interface in org.mpxj">ChildResourceContainer</a>, <a href="../../org/mpxj/FieldContainer.html" title="interface in org.mpxj">FieldContainer</a>, <a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a>, <a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a>, <a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Resource</span>
extends <a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;, <a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a>, <a href="../../org/mpxj/ChildResourceContainer.html" title="interface in org.mpxj">ChildResourceContainer</a></pre>
<div class="block">This class represents a resource used in a project.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addCalendar--">addCalendar</a></span>()</code>
<div class="block">This method allows a calendar to be added to a resource.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addChildResource-org.mpxj.Resource-">addChildResource</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;child)</code>
<div class="block">Add an existing resource as a child of the current resource.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addResource--">addResource</a></span>()</code>
<div class="block">Creates and adds a resource to the list of resources held by this object.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addResourceAssignment-org.mpxj.ResourceAssignment-">addResourceAssignment</a></span>(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</code>
<div class="block">This method is used internally within MPXJ to track tasks which are
 assigned to a particular resource.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addResourceCodeValue-org.mpxj.ResourceCodeValue-">addResourceCodeValue</a></span>(<a href="../../org/mpxj/ResourceCodeValue.html" title="class in org.mpxj">ResourceCodeValue</a>&nbsp;value)</code>
<div class="block">Assign a resource code value to this resource.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addRoleAssignment-org.mpxj.Resource-org.mpxj.SkillLevel-">addRoleAssignment</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role,
                 <a href="../../org/mpxj/SkillLevel.html" title="enum in org.mpxj">SkillLevel</a>&nbsp;skillLevel)</code>
<div class="block">Add a role assignment, and a skill level for the role, to this resource.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#addRoleCodeValue-org.mpxj.RoleCodeValue-">addRoleCodeValue</a></span>(<a href="../../org/mpxj/RoleCodeValue.html" title="class in org.mpxj">RoleCodeValue</a>&nbsp;value)</code>
<div class="block">Assign a role code value to this resource.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#compareTo-org.mpxj.Resource-">compareTo</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;o)</code>
<div class="block">This method implements the only method in the Comparable interface.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#equals-java.lang.Object-">equals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getAccrueAt--">getAccrueAt</a></span>()</code>
<div class="block">Gets the Accrue at type.The Accrue At field provides choices for how and
 when resource standard and overtime costs are to be charged, or accrued,
 to the cost of a task.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActive--">getActive</a></span>()</code>
<div class="block">Retrieves the active flag.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActiveDirectoryGUID--">getActiveDirectoryGUID</a></span>()</code>
<div class="block">Retrieves the active directory GUID for this resource.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Retrieves the actual cost for the work already performed by this resource.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Retrieve actual overtime cost.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualOvertimeWork--">getActualOvertimeWork</a></span>()</code>
<div class="block">Retrieve the value of the actual overtime work field.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualOvertimeWorkProtected--">getActualOvertimeWorkProtected</a></span>()</code>
<div class="block">Retrieves the actual overtime work protected duration.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualWork--">getActualWork</a></span>()</code>
<div class="block">Retrieves the Actual Work field contains the amount of work that has
 already been done for all assignments assigned to a resource.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getActualWorkProtected--">getActualWorkProtected</a></span>()</code>
<div class="block">Retrieves the actual work protected duration.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getACWP--">getACWP</a></span>()</code>
<div class="block">Set the actual cost of work performed.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getAlwaysCalculatedField-org.mpxj.FieldType-">getAlwaysCalculatedField</a></span>(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Determine if the supplied field is always calculated.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/AvailabilityTable.html" title="class in org.mpxj">AvailabilityTable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getAvailability--">getAvailability</a></span>()</code>
<div class="block">Retrieve the availability table for this resource.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getAvailableFrom--">getAvailableFrom</a></span>()</code>
<div class="block">Retrieves the "available from" date.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getAvailableTo--">getAvailableTo</a></span>()</code>
<div class="block">Retrieves the "available to" date.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaseCalendar--">getBaseCalendar</a></span>()</code>
<div class="block">Retrieves Base Calendar name associated with this resource.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineBudgetCost--">getBaselineBudgetCost</a></span>()</code>
<div class="block">Retrieve the baseline budget cost.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineBudgetCost-int-">getBaselineBudgetCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline budget cost.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineBudgetWork--">getBaselineBudgetWork</a></span>()</code>
<div class="block">Retrieve the baseline budget work.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineBudgetWork-int-">getBaselineBudgetWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline budget work.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">Retrieves the Baseline Cost value.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineCost-int-">getBaselineCost</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineWork--">getBaselineWork</a></span>()</code>
<div class="block">Retrieves the Baseline Work value.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBaselineWork-int-">getBaselineWork</a></span>(int&nbsp;baselineNumber)</code>
<div class="block">Retrieve a baseline value.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBCWP--">getBCWP</a></span>()</code>
<div class="block">Retrieves the budgeted cost of work performed.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBCWS--">getBCWS</a></span>()</code>
<div class="block">Retrieves the budgeted cost of work scheduled.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBookingType--">getBookingType</a></span>()</code>
<div class="block">Retrieves the booking type.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBudget--">getBudget</a></span>()</code>
<div class="block">Retrieve the budget flag.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBudgetCost--">getBudgetCost</a></span>()</code>
<div class="block">Retrieve the budget cost.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getBudgetWork--">getBudgetWork</a></span>()</code>
<div class="block">Retrieve the budget work.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCalculateCostsFromUnits--">getCalculateCostsFromUnits</a></span>()</code>
<div class="block">Retrieve the calculate costs from units flag.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/function/Function.html?is-external=true" title="class or interface in java.util.function">Function</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCalculationMethod-org.mpxj.FieldType-">getCalculationMethod</a></span>(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</code>
<div class="block">Retrieve the method used to calculate the value of the supplied field.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCalendar--">getCalendar</a></span>()</code>
<div class="block">This method retrieves the calendar associated with this resource.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCalendarUniqueID--">getCalendarUniqueID</a></span>()</code>
<div class="block">Retrieve the calendar unique ID.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCanLevel--">getCanLevel</a></span>()</code>
<div class="block">Retrieves the flag indicating if the resource levelling can be applied to
 this resource.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getChildResources--">getChildResources</a></span>()</code>
<div class="block">Retrieve a list of child resources held by this object.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCode--">getCode</a></span>()</code>
<div class="block">Gets code field value.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCost--">getCost</a></span>()</code>
<div class="block">Retrieves the cost field value.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCost-int-">getCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a cost value.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCostCenter--">getCostCenter</a></span>()</code>
<div class="block">Retrieve the cost center.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCostPerUse--">getCostPerUse</a></span>()</code>
<div class="block">Retrieve the cost per use.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCostRateTable-int-">getCostRateTable</a></span>(int&nbsp;index)</code>
<div class="block">Retrieves a cost rate table associated with a resource.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCostVariance--">getCostVariance</a></span>()</code>
<div class="block">Retrieves the cost variance.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Retrieves the creation date.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Currency.html" title="class in org.mpxj">Currency</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Retrieve the currency associated with this resource.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCurrencyUniqueID--">getCurrencyUniqueID</a></span>()</code>
<div class="block">Retrieves the unique ID of the currency associated with this resource.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Availability.html" title="class in org.mpxj">Availability</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCurrentAvailabilityTableEntry--">getCurrentAvailabilityTableEntry</a></span>()</code>
<div class="block">Retrieve the availability table entry effective for the current date.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/CostRateTableEntry.html" title="class in org.mpxj">CostRateTableEntry</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCurrentCostRateTableEntry-int-">getCurrentCostRateTableEntry</a></span>(int&nbsp;costRateTable)</code>
<div class="block">Retrieve the cost rate table entry effective for the current date.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getCV--">getCV</a></span>()</code>
<div class="block">Retrieve the cost variance.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getDate-int-">getDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a date value.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getDefaultUnits--">getDefaultUnits</a></span>()</code>
<div class="block">Retrieves the default availability of a resource.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Retrieve the description field.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getDuration-int-">getDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a duration value.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEmailAddress--">getEmailAddress</a></span>()</code>
<div class="block">Retrieves the resource's email address.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterprise--">getEnterprise</a></span>()</code>
<div class="block">Retrieves a flag indicating that a resource is an enterprise resource.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseCost-int-">getEnterpriseCost</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseDate-int-">getEnterpriseDate</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseDuration-int-">getEnterpriseDuration</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseFlag-int-">getEnterpriseFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseNumber-int-">getEnterpriseNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getEnterpriseText-int-">getEnterpriseText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an enterprise field value.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getExpensesOnly--">getExpensesOnly</a></span>()</code>
<div class="block">Retrieve the expenses only field.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getFieldByAlias-java.lang.String-">getFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</code>
<div class="block">Retrieve the value of a field using its alias.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getFinish--">getFinish</a></span>()</code>
<div class="block">Retrieves the latest finish date for all assigned tasks.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getFinish-int-">getFinish</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a finish value.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getFlag-int-">getFlag</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a flag value.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getGeneric--">getGeneric</a></span>()</code>
<div class="block">Retrieves the generic flag.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getGroup--">getGroup</a></span>()</code>
<div class="block">Gets Group field value.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Retrieves the resource GUID.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getHyperlink--">getHyperlink</a></span>()</code>
<div class="block">Retrieves the hyperlink text.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getHyperlinkAddress--">getHyperlinkAddress</a></span>()</code>
<div class="block">Retrieves the hyperlink address.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getHyperlinkScreenTip--">getHyperlinkScreenTip</a></span>()</code>
<div class="block">Retrieves the hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getHyperlinkSubAddress--">getHyperlinkSubAddress</a></span>()</code>
<div class="block">Retrieves the hyperlink sub-address.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getID--">getID</a></span>()</code>
<div class="block">Gets ID field value.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getInitials--">getInitials</a></span>()</code>
<div class="block">Gets Initials of name field value.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getLinkedFields--">getLinkedFields</a></span>()</code>
<div class="block">Gets Linked Fields field value.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getLocation--">getLocation</a></span>()</code>
<div class="block">Retrieves the location.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getLocationUniqueID--">getLocationUniqueID</a></span>()</code>
<div class="block">Retrieves the location unique ID.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getMaterialLabel--">getMaterialLabel</a></span>()</code>
<div class="block">Retrieves the units label for a material resource.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getMaxUnits--">getMaxUnits</a></span>()</code>
<div class="block">Retrieves the maximum availability of a resource on the current date.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getModifyOnIntegrate--">getModifyOnIntegrate</a></span>()</code>
<div class="block">Retrieve the modify on integrate value.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getName--">getName</a></span>()</code>
<div class="block">Gets Resource Name field value.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Retrieve the plain text representation of the resource notes.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getNotesObject--">getNotesObject</a></span>()</code>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getNtAccount--">getNtAccount</a></span>()</code>
<div class="block">Retrieves the Windows account name for a resource.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getNull--">getNull</a></span>()</code>
<div class="block">Retrieve a flag indicating if this is a null resource.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getNumber-int-">getNumber</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a number value.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getObjects--">getObjects</a></span>()</code>
<div class="block">Gets objects field value.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getOutlineCode-int-">getOutlineCode</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve an outline code value.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getOverAllocated--">getOverAllocated</a></span>()</code>
<div class="block">Retrieves the overallocated flag.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getOvertimeCost--">getOvertimeCost</a></span>()</code>
<div class="block">Retrieve the value of the overtime cost field.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getOvertimeRate--">getOvertimeRate</a></span>()</code>
<div class="block">Retrieves the overtime rate for this resource.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getOvertimeWork--">getOvertimeWork</a></span>()</code>
<div class="block">Retrieves the amount of overtime work.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getParentFile--">getParentFile</a></span>()</code>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getParentResource--">getParentResource</a></span>()</code>
<div class="block">Retrieve the parent resource.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getParentResourceUniqueID--">getParentResourceUniqueID</a></span>()</code>
<div class="block">Retrieve the parent resource's Unique ID.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPeakUnits--">getPeakUnits</a></span>()</code>
<div class="block">Retrieves the peak resource utilisation.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPercentWorkComplete--">getPercentWorkComplete</a></span>()</code>
<div class="block">Retrieves the value of the percent work complete field.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPerDay--">getPerDay</a></span>()</code>
<div class="block">Retrieve the per day field.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPeriodDur--">getPeriodDur</a></span>()</code>
<div class="block">Retrieve the period dur field.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPhone--">getPhone</a></span>()</code>
<div class="block">Retrieve the phone field.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPhonetics--">getPhonetics</a></span>()</code>
<div class="block">Retrieves phonetic information for the Japanese version of MS Project.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPool--">getPool</a></span>()</code>
<div class="block">Retrieve the pool field.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPrimaryRole--">getPrimaryRole</a></span>()</code>
<div class="block">Retrieves the primary role.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPrimaryRoleUniqueID--">getPrimaryRoleUniqueID</a></span>()</code>
<div class="block">Retrieves the primary role unique ID.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getPriority--">getPriority</a></span>()</code>
<div class="block">Retrieve the priority field.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRate--">getRate</a></span>()</code>
<div class="block">Retrieve the rate field.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRegularWork--">getRegularWork</a></span>()</code>
<div class="block">Retrieve the value of the regular work field.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Retrieves the remaining cost for this resource.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRemainingOvertimeCost--">getRemainingOvertimeCost</a></span>()</code>
<div class="block">Retrieve the remaining overtime cost.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRemainingOvertimeWork--">getRemainingOvertimeWork</a></span>()</code>
<div class="block">Retrieve the value of the remaining overtime work field.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRemainingWork--">getRemainingWork</a></span>()</code>
<div class="block">Gets Remaining Work field value.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ResourceCode.html" title="class in org.mpxj">ResourceCode</a>,<a href="../../org/mpxj/ResourceCodeValue.html" title="class in org.mpxj">ResourceCodeValue</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getResourceCodeValues--">getResourceCodeValues</a></span>()</code>
<div class="block">Retrieve the resource code values associated with this resource.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getResourceID--">getResourceID</a></span>()</code>
<div class="block">Retrieve the resource ID field.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRole--">getRole</a></span>()</code>
<div class="block">Retrieve the role field.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>,<a href="../../org/mpxj/SkillLevel.html" title="enum in org.mpxj">SkillLevel</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRoleAssignments--">getRoleAssignments</a></span>()</code>
<div class="block">Retrieve a map of the roles assigned to this resource.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/RoleCode.html" title="class in org.mpxj">RoleCode</a>,<a href="../../org/mpxj/RoleCodeValue.html" title="class in org.mpxj">RoleCodeValue</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getRoleCodeValues--">getRoleCodeValues</a></span>()</code>
<div class="block">Retrieve the role code values associated with this resource.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getSequenceNumber--">getSequenceNumber</a></span>()</code>
<div class="block">Retrieve this resource's sequence number.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getShift--">getShift</a></span>()</code>
<div class="block">Retrieves the shift.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getShiftUniqueID--">getShiftUniqueID</a></span>()</code>
<div class="block">Retrieves the shift unique ID.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getStandardRate--">getStandardRate</a></span>()</code>
<div class="block">Gets Standard Rate field value.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getStart--">getStart</a></span>()</code>
<div class="block">Retrieves the earliest start date for all assigned tasks.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getStart-int-">getStart</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a start value.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getSubprojectResourceUniqueID--">getSubprojectResourceUniqueID</a></span>()</code>
<div class="block">Where a resource in an MPP file represents a resource from a subproject,
 this value will be non-zero.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getSupplyReference--">getSupplyReference</a></span>()</code>
<div class="block">Retrieve the supply reference field.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getSV--">getSV</a></span>()</code>
<div class="block">Retrieve the schedule variance.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getTaskAssignments--">getTaskAssignments</a></span>()</code>
<div class="block">Retrieve a list of tasks assigned to this resource.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getText-int-">getText</a></span>(int&nbsp;index)</code>
<div class="block">Retrieve a text value.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getType--">getType</a></span>()</code>
<div class="block">Retrieves the resource type.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getUniqueID--">getUniqueID</a></span>()</code>
<div class="block">Gets Unique ID field value.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getUnit--">getUnit</a></span>()</code>
<div class="block">Retrieve the unit field.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/UnitOfMeasure.html" title="class in org.mpxj">UnitOfMeasure</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getUnitOfMeasure--">getUnitOfMeasure</a></span>()</code>
<div class="block">Retrieves the unit of measure for this resource.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getUnitOfMeasureUniqueID--">getUnitOfMeasureUniqueID</a></span>()</code>
<div class="block">Retrieve the unit of measure unique ID.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getWork--">getWork</a></span>()</code>
<div class="block">Gets Work field value.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getWorkGroup--">getWorkGroup</a></span>()</code>
<div class="block">Retrieve the messaging method used to communicate with a project team.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#getWorkVariance--">getWorkVariance</a></span>()</code>
<div class="block">Retrieves the work variance.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">handleFieldChange</a></span>(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</code>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#remove--">remove</a></span>()</code>
<div class="block">Removes this resource from the project.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#removeChildResource-org.mpxj.Resource-">removeChildResource</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;child)</code>
<div class="block">Removes a child resource.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#removeRoleAssignment-org.mpxj.Resource-">removeRoleAssignment</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</code>
<div class="block">Remove a role assignment from this resource.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setAccrueAt-org.mpxj.AccrueType-">setAccrueAt</a></span>(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</code>
<div class="block">Sets the Accrue at type.The Accrue At field provides choices for how and
 when resource standard and overtime costs are to be charged, or accrued,
 to the cost of a task.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActive-boolean-">setActive</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the active flag.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActiveDirectoryGUID-java.lang.String-">setActiveDirectoryGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;guid)</code>
<div class="block">Sets the active directory GUID for this resource.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualCost-java.lang.Number-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</code>
<div class="block">Set the actual cost for the work already performed by this resource.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualOvertimeCost-java.lang.Number-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualOvertimeCost)</code>
<div class="block">Sets the actual overtime cost.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualOvertimeWork-org.mpxj.Duration-">setActualOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Sets the value of the actual overtime work field.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualOvertimeWorkProtected-org.mpxj.Duration-">setActualOvertimeWorkProtected</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Sets the actual overtime work protected duration.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualWork-org.mpxj.Duration-">setActualWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">Sets the Actual Work field contains the amount of work that has already
 been done for all assignments assigned to a resource.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setActualWorkProtected-org.mpxj.Duration-">setActualWorkProtected</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Sets the actual work protected duration.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setACWP-java.lang.Number-">setACWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</code>
<div class="block">Set the actual cost of work performed.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaseCalendar-java.lang.String-">setBaseCalendar</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Sets the Base Calendar field indicates which calendar is the base calendar
 for a resource calendar.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineBudgetCost-int-java.lang.Number-">setBaselineBudgetCost</a></span>(int&nbsp;baselineNumber,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline budget cost.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineBudgetCost-java.lang.Number-">setBaselineBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the baseline budget cost.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineBudgetWork-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the baseline budget work.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineBudgetWork-int-org.mpxj.Duration-">setBaselineBudgetWork</a></span>(int&nbsp;baselineNumber,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline budget work.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineCost-int-java.lang.Number-">setBaselineCost</a></span>(int&nbsp;baselineNumber,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineCost-java.lang.Number-">setBaselineCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</code>
<div class="block">Sets the baseline cost.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineWork-org.mpxj.Duration-">setBaselineWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">Sets the baseline work duration.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBaselineWork-int-org.mpxj.Duration-">setBaselineWork</a></span>(int&nbsp;baselineNumber,
               <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a baseline value.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBCWP-java.lang.Number-">setBCWP</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;bcwp)</code>
<div class="block">Sets the budgeted cost of work performed.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBCWS-java.lang.Number-">setBCWS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;bcws)</code>
<div class="block">Sets the budgeted cost of work scheduled.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBookingType-org.mpxj.BookingType-">setBookingType</a></span>(<a href="../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;bookingType)</code>
<div class="block">Sets the booking type.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBudget-boolean-">setBudget</a></span>(boolean&nbsp;budget)</code>
<div class="block">Set the budget flag.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBudgetCost-java.lang.Number-">setBudgetCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the budget cost.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setBudgetWork-org.mpxj.Duration-">setBudgetWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set the budget work.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCalculateCostsFromUnits-boolean-">setCalculateCostsFromUnits</a></span>(boolean&nbsp;calculateCostsFromUnits)</code>
<div class="block">Set the calculate costs from units flag.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCalendar-org.mpxj.ProjectCalendar-">setCalendar</a></span>(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">This method allows a pre-existing resource calendar to be attached to a
 resource.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCalendarUniqueID-java.lang.Integer-">setCalendarUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Set the calendar unique ID.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCanLevel-boolean-">setCanLevel</a></span>(boolean&nbsp;canLevel)</code>
<div class="block">Sets the flag indicating if the resource levelling can be applied to this
 resource.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCode-java.lang.String-">setCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Sets code field value.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCost-int-java.lang.Number-">setCost</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a cost value.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCost-java.lang.Number-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</code>
<div class="block">Sets the cost field value.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCostCenter-java.lang.String-">setCostCenter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the cost center.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCostRateTable-int-org.mpxj.CostRateTable-">setCostRateTable</a></span>(int&nbsp;index,
                <a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a>&nbsp;crt)</code>
<div class="block">Associates a complete cost rate table with the
 current resource.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCostVariance-java.lang.Number-">setCostVariance</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;costVariance)</code>
<div class="block">Sets the cost variance.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCreationDate-java.time.LocalDateTime-">setCreationDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</code>
<div class="block">Sets the creation date.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCurrency-org.mpxj.Currency-">setCurrency</a></span>(<a href="../../org/mpxj/Currency.html" title="class in org.mpxj">Currency</a>&nbsp;currency)</code>
<div class="block">Sets the currency associated with this resource.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCurrencyUniqueID-java.lang.Integer-">setCurrencyUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the unique ID of the currency associated with this resource.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setCV-java.lang.Number-">setCV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cv)</code>
<div class="block">Set the cost variance.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setDate-int-java.time.LocalDateTime-">setDate</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a date value.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setDefaultUnits-java.lang.Number-">setDefaultUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;defaultUnits)</code>
<div class="block">Sets the default availability of a resource.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setDescription-java.lang.String-">setDescription</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the description field.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setDuration-int-org.mpxj.Duration-">setDuration</a></span>(int&nbsp;index,
           <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set a duration value.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEmailAddress-java.lang.String-">setEmailAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;emailAddress)</code>
<div class="block">Set the resource's email address.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterprise-boolean-">setEnterprise</a></span>(boolean&nbsp;enterprise)</code>
<div class="block">Sets a flag indicating that a resource is an enterprise resource.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseCost-int-java.lang.Number-">setEnterpriseCost</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseDate-int-java.time.LocalDateTime-">setEnterpriseDate</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseDuration-int-org.mpxj.Duration-">setEnterpriseDuration</a></span>(int&nbsp;index,
                     <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseFlag-int-boolean-">setEnterpriseFlag</a></span>(int&nbsp;index,
                 boolean&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseNumber-int-java.lang.Number-">setEnterpriseNumber</a></span>(int&nbsp;index,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setEnterpriseText-int-java.lang.String-">setEnterpriseText</a></span>(int&nbsp;index,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set an enterprise field value.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setExpensesOnly-boolean-">setExpensesOnly</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the expenses only field.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setFieldByAlias-java.lang.String-java.lang.Object-">setFieldByAlias</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Set the value of a field using its alias.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setFinish-int-java.time.LocalDateTime-">setFinish</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a finish value.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setFlag-int-boolean-">setFlag</a></span>(int&nbsp;index,
       boolean&nbsp;value)</code>
<div class="block">Set a flag value.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setGeneric-boolean-">setGeneric</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the generic flag.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setGroup-java.lang.String-">setGroup</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Sets Group field value.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setGUID-java.util.UUID-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</code>
<div class="block">Sets the resource GUID.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setHyperlink-java.lang.String-">setHyperlink</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlink)</code>
<div class="block">Sets the hyperlink text.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setHyperlinkAddress-java.lang.String-">setHyperlinkAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkAddress)</code>
<div class="block">Sets the hyperlink address.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setHyperlinkScreenTip-java.lang.String-">setHyperlinkScreenTip</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</code>
<div class="block">Sets the hyperlink screen tip attribute.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setHyperlinkSubAddress-java.lang.String-">setHyperlinkSubAddress</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkSubAddress)</code>
<div class="block">Sets the hyperlink sub-address.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setID-java.lang.Integer-">setID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">Sets ID field value.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setInitials-java.lang.String-">setInitials</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Sets Initials field value.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setIsNull-boolean-">setIsNull</a></span>(boolean&nbsp;isNull)</code>
<div class="block">Set the flag indicating that this is a null resource.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setLinkedFields-boolean-">setLinkedFields</a></span>(boolean&nbsp;val)</code>
<div class="block">This field is ignored on import into MS Project.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setLocation-org.mpxj.Location-">setLocation</a></span>(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</code>
<div class="block">Sets the location.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setLocationUniqueID-java.lang.Integer-">setLocationUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the location unique ID.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setModifyOnIntegrate-boolean-">setModifyOnIntegrate</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the modify on integrate field.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</code>
<div class="block">Sets Name field value.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</code>
<div class="block">Sets the notes text for this resource.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setNotesObject-org.mpxj.Notes-">setNotesObject</a></span>(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</code>
<div class="block">Set the Notes instance representing the resource notes.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setNtAccount-java.lang.String-">setNtAccount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ntAccount)</code>
<div class="block">Sets the Windows account name for a resource.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setNumber-int-java.lang.Number-">setNumber</a></span>(int&nbsp;index,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set a number value.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setObjects-java.lang.Integer-">setObjects</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">Set objects.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setOutlineCode-int-java.lang.String-">setOutlineCode</a></span>(int&nbsp;index,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set an outline code value.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setOverAllocated-boolean-">setOverAllocated</a></span>(boolean&nbsp;overallocated)</code>
<div class="block">Set the overallocated flag.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setOvertimeCost-java.lang.Number-">setOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;currency)</code>
<div class="block">Set the value of the overtime cost field.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setOvertimeWork-org.mpxj.Duration-">setOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;overtimeWork)</code>
<div class="block">Sets the amount of overtime work.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setParentResource-org.mpxj.Resource-">setParentResource</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">Set the parent resource.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setParentResourceUniqueID-java.lang.Integer-">setParentResourceUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</code>
<div class="block">Sets the parent resource's Unique ID.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPeakUnits-java.lang.Number-">setPeakUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;peakUnits)</code>
<div class="block">Sets peak resource utilisation.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPercentWorkComplete-java.lang.Number-">setPercentWorkComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;percentWorkComplete)</code>
<div class="block">Sets the value of the percent work complete field.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPerDay-java.lang.Number-">setPerDay</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the per day field.</div>
</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPeriodDur-java.lang.Number-">setPeriodDur</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the period dur field.</div>
</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPhone-java.lang.String-">setPhone</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the phone field.</div>
</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPhonetics-java.lang.String-">setPhonetics</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;phonetics)</code>
<div class="block">Sets phonetic information for the Japanese version of MS Project.</div>
</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPool-java.lang.Number-">setPool</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the pool field.</div>
</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPrimaryRole-org.mpxj.Resource-">setPrimaryRole</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</code>
<div class="block">Sets the primary role.</div>
</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPrimaryRoleUniqueID-java.lang.Integer-">setPrimaryRoleUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the primary role unique ID.</div>
</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setPriority-java.lang.Number-">setPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the priority field.</div>
</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRate-java.lang.Number-">setRate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</code>
<div class="block">Set the rate field.</div>
</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRegularWork-org.mpxj.Duration-">setRegularWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Set the value of the regular work field.</div>
</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRemainingCost-java.lang.Number-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;remainingCost)</code>
<div class="block">Sets the remaining cost for this resource.</div>
</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRemainingOvertimeCost-java.lang.Number-">setRemainingOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;remainingOvertimeCost)</code>
<div class="block">Set the remaining overtime cost.</div>
</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRemainingOvertimeWork-org.mpxj.Duration-">setRemainingOvertimeWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</code>
<div class="block">Sets the value of the remaining overtime work field.</div>
</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRemainingWork-org.mpxj.Duration-">setRemainingWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">This field is ignored on import into MS Project.</div>
</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setResourceID-java.lang.String-">setResourceID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the resource ID field.</div>
</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setRole-boolean-">setRole</a></span>(boolean&nbsp;value)</code>
<div class="block">Set the role field.</div>
</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setSequenceNumber-java.lang.Integer-">setSequenceNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;sequenceNumber)</code>
<div class="block">Set this resource's sequence number.</div>
</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setShift-org.mpxj.Shift-">setShift</a></span>(<a href="../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a>&nbsp;shift)</code>
<div class="block">Sets the shift.</div>
</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setShiftUniqueID-java.lang.Integer-">setShiftUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the shift unique ID.</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setStart-int-java.time.LocalDateTime-">setStart</a></span>(int&nbsp;index,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Set a start value.</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setSubprojectResourceUniqueID-java.lang.Integer-">setSubprojectResourceUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectUniqueResourceID)</code>
<div class="block">Sets the sub project unique resource ID.</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setSupplyReference-java.lang.String-">setSupplyReference</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the supply reference field.</div>
</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setSV-java.lang.Number-">setSV</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;sv)</code>
<div class="block">Set the schedule variance.</div>
</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setText-int-java.lang.String-">setText</a></span>(int&nbsp;index,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set a text value.</div>
</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setType-org.mpxj.ResourceType-">setType</a></span>(<a href="../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;type)</code>
<div class="block">Set the resource type.</div>
</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setUniqueID-java.lang.Integer-">setUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</code>
<div class="block">Sets Unique ID of this resource.</div>
</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setUnit-java.lang.String-">setUnit</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Set the unit field.</div>
</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setUnitOfMeasure-org.mpxj.UnitOfMeasure-">setUnitOfMeasure</a></span>(<a href="../../org/mpxj/UnitOfMeasure.html" title="class in org.mpxj">UnitOfMeasure</a>&nbsp;unitOfMeasure)</code>
<div class="block">Sets the unit of measure instance for this resource.</div>
</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setUnitOfMeasureUniqueID-java.lang.Integer-">setUnitOfMeasureUniqueID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</code>
<div class="block">Sets the unit of measure unique ID.</div>
</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setWork-org.mpxj.Duration-">setWork</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</code>
<div class="block">This field is ignored on import into MS Project.</div>
</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setWorkGroup-org.mpxj.WorkGroup-">setWorkGroup</a></span>(<a href="../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;workGroup)</code>
<div class="block">Set the messaging method used to communicate with a project team.</div>
</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#setWorkVariance-org.mpxj.Duration-">setWorkVariance</a></span>(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;workVariance)</code>
<div class="block">Sets the work variance.</div>
</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Resource.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.AbstractFieldContainer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj">AbstractFieldContainer</a></h3>
<code><a href="../../org/mpxj/AbstractFieldContainer.html#addFieldListener-org.mpxj.listener.FieldListener-">addFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#disableEvents--">disableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#enableEvents--">enableEvents</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#get-org.mpxj.FieldType-">get</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#getCachedValue-org.mpxj.FieldType-">getCachedValue</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#removeFieldListener-org.mpxj.listener.FieldListener-">removeFieldListener</a>, <a href="../../org/mpxj/AbstractFieldContainer.html#set-org.mpxj.FieldType-java.lang.Object-">set</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResource</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;addResource()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/ChildResourceContainer.html#addResource--">ChildResourceContainer</a></code></span></div>
<div class="block">Creates and adds a resource to the list of resources held by this object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ChildResourceContainer.html#addResource--">addResource</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ChildResourceContainer.html" title="interface in org.mpxj">ChildResourceContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>newly created resource</dd>
</dl>
</li>
</ul>
<a name="addChildResource-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addChildResource</h4>
<pre>public&nbsp;void&nbsp;addChildResource(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;child)</pre>
<div class="block">Add an existing resource as a child of the current resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - child resource</dd>
</dl>
</li>
</ul>
<a name="getChildResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChildResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;&nbsp;getChildResources()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/ChildResourceContainer.html#getChildResources--">ChildResourceContainer</a></code></span></div>
<div class="block">Retrieve a list of child resources held by this object.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ChildResourceContainer.html#getChildResources--">getChildResources</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ChildResourceContainer.html" title="interface in org.mpxj">ChildResourceContainer</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of child resources</dd>
</dl>
</li>
</ul>
<a name="removeChildResource-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeChildResource</h4>
<pre>public&nbsp;void&nbsp;removeChildResource(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;child)</pre>
<div class="block">Removes a child resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>child</code> - child resource instance</dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Sets Name field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets Resource Name field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="setType-org.mpxj.ResourceType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;type)</pre>
<div class="block">Set the resource type. Can be TYPE_MATERIAL, or TYPE_WORK.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - resource type</dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ResourceType.html" title="enum in org.mpxj">ResourceType</a>&nbsp;getType()</pre>
<div class="block">Retrieves the resource type. Can return TYPE_MATERIAL, or TYPE_WORK.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource type</dd>
</dl>
</li>
</ul>
<a name="setIsNull-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsNull</h4>
<pre>public&nbsp;void&nbsp;setIsNull(boolean&nbsp;isNull)</pre>
<div class="block">Set the flag indicating that this is a null resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>isNull</code> - null resource flag</dd>
</dl>
</li>
</ul>
<a name="getNull--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNull</h4>
<pre>public&nbsp;boolean&nbsp;getNull()</pre>
<div class="block">Retrieve a flag indicating if this is a null resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setInitials-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInitials</h4>
<pre>public&nbsp;void&nbsp;setInitials(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Sets Initials field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value</dd>
</dl>
</li>
</ul>
<a name="getInitials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInitials</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getInitials()</pre>
<div class="block">Gets Initials of name field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="setPhonetics-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhonetics</h4>
<pre>public&nbsp;void&nbsp;setPhonetics(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;phonetics)</pre>
<div class="block">Sets phonetic information for the Japanese version of MS Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>phonetics</code> - Japanese phonetic information</dd>
</dl>
</li>
</ul>
<a name="getPhonetics--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhonetics</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPhonetics()</pre>
<div class="block">Retrieves phonetic information for the Japanese version of MS Project.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Japanese phonetic information</dd>
</dl>
</li>
</ul>
<a name="setNtAccount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNtAccount</h4>
<pre>public&nbsp;void&nbsp;setNtAccount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ntAccount)</pre>
<div class="block">Sets the Windows account name for a resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ntAccount</code> - windows account name</dd>
</dl>
</li>
</ul>
<a name="getNtAccount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNtAccount</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNtAccount()</pre>
<div class="block">Retrieves the Windows account name for a resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>windows account name</dd>
</dl>
</li>
</ul>
<a name="getMaterialLabel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterialLabel</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMaterialLabel()</pre>
<div class="block">Retrieves the units label for a material resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>material resource units label</dd>
</dl>
</li>
</ul>
<a name="setCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCode</h4>
<pre>public&nbsp;void&nbsp;setCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Sets code field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value</dd>
</dl>
</li>
</ul>
<a name="getCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCode()</pre>
<div class="block">Gets code field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="setGroup-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroup</h4>
<pre>public&nbsp;void&nbsp;setGroup(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Sets Group field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value</dd>
</dl>
</li>
</ul>
<a name="getGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroup</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getGroup()</pre>
<div class="block">Gets Group field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="setWorkGroup-org.mpxj.WorkGroup-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkGroup</h4>
<pre>public&nbsp;void&nbsp;setWorkGroup(<a href="../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;workGroup)</pre>
<div class="block">Set the messaging method used to communicate with a project team.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>workGroup</code> - messaging method</dd>
</dl>
</li>
</ul>
<a name="getWorkGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkGroup</h4>
<pre>public&nbsp;<a href="../../org/mpxj/WorkGroup.html" title="enum in org.mpxj">WorkGroup</a>&nbsp;getWorkGroup()</pre>
<div class="block">Retrieve the messaging method used to communicate with a project team.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>messaging method</dd>
</dl>
</li>
</ul>
<a name="setEmailAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEmailAddress</h4>
<pre>public&nbsp;void&nbsp;setEmailAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;emailAddress)</pre>
<div class="block">Set the resource's email address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>emailAddress</code> - email address</dd>
</dl>
</li>
</ul>
<a name="getEmailAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEmailAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEmailAddress()</pre>
<div class="block">Retrieves the resource's email address.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>email address</dd>
</dl>
</li>
</ul>
<a name="setHyperlink-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlink</h4>
<pre>public&nbsp;void&nbsp;setHyperlink(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlink)</pre>
<div class="block">Sets the hyperlink text.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hyperlink</code> - hyperlink text</dd>
</dl>
</li>
</ul>
<a name="getHyperlink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlink()</pre>
<div class="block">Retrieves the hyperlink text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink text</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkAddress)</pre>
<div class="block">Sets the hyperlink address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hyperlinkAddress</code> - hyperlink address</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkAddress()</pre>
<div class="block">Retrieves the hyperlink address.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink address</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkSubAddress-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkSubAddress</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkSubAddress(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hyperlinkSubAddress)</pre>
<div class="block">Sets the hyperlink sub-address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>hyperlinkSubAddress</code> - hyperlink sub-address</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkSubAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkSubAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkSubAddress()</pre>
<div class="block">Retrieves the hyperlink sub-address.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink sub-address</dd>
</dl>
</li>
</ul>
<a name="setHyperlinkScreenTip-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHyperlinkScreenTip</h4>
<pre>public&nbsp;void&nbsp;setHyperlinkScreenTip(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;text)</pre>
<div class="block">Sets the hyperlink screen tip attribute.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="getHyperlinkScreenTip--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHyperlinkScreenTip</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHyperlinkScreenTip()</pre>
<div class="block">Retrieves the hyperlink screen tip attribute.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>hyperlink screen tip attribute</dd>
</dl>
</li>
</ul>
<a name="setDefaultUnits-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultUnits</h4>
<pre>public&nbsp;void&nbsp;setDefaultUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;defaultUnits)</pre>
<div class="block">Sets the default availability of a resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaultUnits</code> - default availability</dd>
</dl>
</li>
</ul>
<a name="getDefaultUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getDefaultUnits()</pre>
<div class="block">Retrieves the default availability of a resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>maximum availability</dd>
</dl>
</li>
</ul>
<a name="getMaxUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getMaxUnits()</pre>
<div class="block">Retrieves the maximum availability of a resource on the current date.
 Refer to the availability table to retrieve this value for other dates.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>maximum availability</dd>
</dl>
</li>
</ul>
<a name="setPeakUnits-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeakUnits</h4>
<pre>public&nbsp;void&nbsp;setPeakUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;peakUnits)</pre>
<div class="block">Sets peak resource utilisation.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>peakUnits</code> - peak resource utilisation</dd>
</dl>
</li>
</ul>
<a name="getPeakUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeakUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPeakUnits()</pre>
<div class="block">Retrieves the peak resource utilisation.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>peak resource utilisation</dd>
</dl>
</li>
</ul>
<a name="setOverAllocated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOverAllocated</h4>
<pre>public&nbsp;void&nbsp;setOverAllocated(boolean&nbsp;overallocated)</pre>
<div class="block">Set the overallocated flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overallocated</code> - overallocated flag</dd>
</dl>
</li>
</ul>
<a name="getOverAllocated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOverAllocated</h4>
<pre>public&nbsp;boolean&nbsp;getOverAllocated()</pre>
<div class="block">Retrieves the overallocated flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overallocated flag</dd>
</dl>
</li>
</ul>
<a name="getAvailableFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableFrom</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getAvailableFrom()</pre>
<div class="block">Retrieves the "available from" date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>available from date</dd>
</dl>
</li>
</ul>
<a name="getAvailableTo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailableTo</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getAvailableTo()</pre>
<div class="block">Retrieves the "available to" date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>available from date</dd>
</dl>
</li>
</ul>
<a name="getStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart()</pre>
<div class="block">Retrieves the earliest start date for all assigned tasks.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start date</dd>
</dl>
</li>
</ul>
<a name="getFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish()</pre>
<div class="block">Retrieves the latest finish date for all assigned tasks.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish date</dd>
</dl>
</li>
</ul>
<a name="setCanLevel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCanLevel</h4>
<pre>public&nbsp;void&nbsp;setCanLevel(boolean&nbsp;canLevel)</pre>
<div class="block">Sets the flag indicating if the resource levelling can be applied to this
 resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>canLevel</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getCanLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCanLevel</h4>
<pre>public&nbsp;boolean&nbsp;getCanLevel()</pre>
<div class="block">Retrieves the flag indicating if the resource levelling can be applied to
 this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="setAccrueAt-org.mpxj.AccrueType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAccrueAt</h4>
<pre>public&nbsp;void&nbsp;setAccrueAt(<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;type)</pre>
<div class="block">Sets the Accrue at type.The Accrue At field provides choices for how and
 when resource standard and overtime costs are to be charged, or accrued,
 to the cost of a task. The options are: Start, End and Prorated (Default)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - accrue type</dd>
</dl>
</li>
</ul>
<a name="getAccrueAt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccrueAt</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AccrueType.html" title="enum in org.mpxj">AccrueType</a>&nbsp;getAccrueAt()</pre>
<div class="block">Gets the Accrue at type.The Accrue At field provides choices for how and
 when resource standard and overtime costs are to be charged, or accrued,
 to the cost of a task. The options are: Start, End and Prorated (Default)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>accrue type</dd>
</dl>
</li>
</ul>
<a name="setWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">This field is ignored on import into MS Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWork()</pre>
<div class="block">Gets Work field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getRegularWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRegularWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRegularWork()</pre>
<div class="block">Retrieve the value of the regular work field. Note that this value is an
 extension to the MPX specification.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Regular work value</dd>
</dl>
</li>
</ul>
<a name="setRegularWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRegularWork</h4>
<pre>public&nbsp;void&nbsp;setRegularWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Set the value of the regular work field. Note that this value is an
 extension to the MPX specification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - Regular work value</dd>
</dl>
</li>
</ul>
<a name="setActualWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWork</h4>
<pre>public&nbsp;void&nbsp;setActualWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">Sets the Actual Work field contains the amount of work that has already
 been done for all assignments assigned to a resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getActualWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWork()</pre>
<div class="block">Retrieves the Actual Work field contains the amount of work that has
 already been done for all assignments assigned to a resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Actual work value</dd>
</dl>
</li>
</ul>
<a name="setOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;overtimeWork)</pre>
<div class="block">Sets the amount of overtime work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overtimeWork</code> - overtime work</dd>
</dl>
</li>
</ul>
<a name="getOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getOvertimeWork()</pre>
<div class="block">Retrieves the amount of overtime work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overtime work</dd>
</dl>
</li>
</ul>
<a name="setRemainingWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">This field is ignored on import into MS Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="getRemainingWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingWork()</pre>
<div class="block">Gets Remaining Work field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualOvertimeWork()</pre>
<div class="block">Retrieve the value of the actual overtime work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime work value</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Sets the value of the actual overtime work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - actual overtime work value</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getRemainingOvertimeWork()</pre>
<div class="block">Retrieve the value of the remaining overtime work field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining overtime work value</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeWork</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Sets the value of the remaining overtime work field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - remaining overtime work value</dd>
</dl>
</li>
</ul>
<a name="setPercentWorkComplete-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentWorkComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentWorkComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;percentWorkComplete)</pre>
<div class="block">Sets the value of the percent work complete field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>percentWorkComplete</code> - percent work complete</dd>
</dl>
</li>
</ul>
<a name="getPercentWorkComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentWorkComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPercentWorkComplete()</pre>
<div class="block">Retrieves the value of the percent work complete field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>percent work complete</dd>
</dl>
</li>
</ul>
<a name="getStandardRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStandardRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getStandardRate()</pre>
<div class="block">Gets Standard Rate field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Rate</dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cost)</pre>
<div class="block">Sets the cost field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cost</code> - cost field value</dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost()</pre>
<div class="block">Retrieves the cost field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost field value</dd>
</dl>
</li>
</ul>
<a name="getOvertimeRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeRate</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;getOvertimeRate()</pre>
<div class="block">Retrieves the overtime rate for this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>overtime rate</dd>
</dl>
</li>
</ul>
<a name="getOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getOvertimeCost()</pre>
<div class="block">Retrieve the value of the overtime cost field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Overtime cost value</dd>
</dl>
</li>
</ul>
<a name="setOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;currency)</pre>
<div class="block">Set the value of the overtime cost field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>currency</code> - Overtime cost</dd>
</dl>
</li>
</ul>
<a name="getCostPerUse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostPerUse</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCostPerUse()</pre>
<div class="block">Retrieve the cost per use.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost per use</dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualCost)</pre>
<div class="block">Set the actual cost for the work already performed by this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualCost</code> - actual cost</dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualCost()</pre>
<div class="block">Retrieves the actual cost for the work already performed by this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual cost</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Retrieve actual overtime cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime cost</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;actualOvertimeCost)</pre>
<div class="block">Sets the actual overtime cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>actualOvertimeCost</code> - actual overtime cost</dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;remainingCost)</pre>
<div class="block">Sets the remaining cost for this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>remainingCost</code> - remaining cost</dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingCost()</pre>
<div class="block">Retrieves the remaining cost for this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining cost</dd>
</dl>
</li>
</ul>
<a name="getRemainingOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRemainingOvertimeCost()</pre>
<div class="block">Retrieve the remaining overtime cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>remaining overtime cost</dd>
</dl>
</li>
</ul>
<a name="setRemainingOvertimeCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;remainingOvertimeCost)</pre>
<div class="block">Set the remaining overtime cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>remainingOvertimeCost</code> - remaining overtime cost</dd>
</dl>
</li>
</ul>
<a name="setWorkVariance-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWorkVariance</h4>
<pre>public&nbsp;void&nbsp;setWorkVariance(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;workVariance)</pre>
<div class="block">Sets the work variance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>workVariance</code> - work variance</dd>
</dl>
</li>
</ul>
<a name="getWorkVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkVariance</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getWorkVariance()</pre>
<div class="block">Retrieves the work variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>work variance</dd>
</dl>
</li>
</ul>
<a name="setCostVariance-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostVariance</h4>
<pre>public&nbsp;void&nbsp;setCostVariance(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;costVariance)</pre>
<div class="block">Sets the cost variance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>costVariance</code> - cost variance</dd>
</dl>
</li>
</ul>
<a name="getCostVariance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostVariance</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCostVariance()</pre>
<div class="block">Retrieves the cost variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost variance</dd>
</dl>
</li>
</ul>
<a name="setSV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSV</h4>
<pre>public&nbsp;void&nbsp;setSV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;sv)</pre>
<div class="block">Set the schedule variance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sv</code> - schedule variance</dd>
</dl>
</li>
</ul>
<a name="getSV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getSV()</pre>
<div class="block">Retrieve the schedule variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>schedule variance</dd>
</dl>
</li>
</ul>
<a name="setCV-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCV</h4>
<pre>public&nbsp;void&nbsp;setCV(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;cv)</pre>
<div class="block">Set the cost variance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cv</code> - cost variance</dd>
</dl>
</li>
</ul>
<a name="getCV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCV</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCV()</pre>
<div class="block">Retrieve the cost variance.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost variance</dd>
</dl>
</li>
</ul>
<a name="setACWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setACWP</h4>
<pre>public&nbsp;void&nbsp;setACWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;acwp)</pre>
<div class="block">Set the actual cost of work performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>acwp</code> - actual cost of work performed</dd>
</dl>
</li>
</ul>
<a name="getACWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getACWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getACWP()</pre>
<div class="block">Set the actual cost of work performed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual cost of work performed</dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;notes)</pre>
<div class="block">Sets the notes text for this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - notes to be added</dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Retrieve the plain text representation of the resource notes.
 Use the getNotesObject method to retrieve an object which
 contains both the plain text notes and, if relevant,
 the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>notes</dd>
</dl>
</li>
</ul>
<a name="setNotesObject-org.mpxj.Notes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotesObject</h4>
<pre>public&nbsp;void&nbsp;setNotesObject(<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;notes)</pre>
<div class="block">Set the Notes instance representing the resource notes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>notes</code> - Notes instance</dd>
</dl>
</li>
</ul>
<a name="getNotesObject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotesObject</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Notes.html" title="class in org.mpxj">Notes</a>&nbsp;getNotesObject()</pre>
<div class="block">Retrieve an object which contains both the plain text notes
 and, if relevant, the original formatted version of the notes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Notes instance</dd>
</dl>
</li>
</ul>
<a name="setBCWS-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWS</h4>
<pre>public&nbsp;void&nbsp;setBCWS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;bcws)</pre>
<div class="block">Sets the budgeted cost of work scheduled.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bcws</code> - budgeted cost of work scheduled</dd>
</dl>
</li>
</ul>
<a name="getBCWS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWS()</pre>
<div class="block">Retrieves the budgeted cost of work scheduled.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budgeted cost of work scheduled</dd>
</dl>
</li>
</ul>
<a name="setBCWP-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBCWP</h4>
<pre>public&nbsp;void&nbsp;setBCWP(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;bcwp)</pre>
<div class="block">Sets the budgeted cost of work performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bcwp</code> - budgeted cost of work performed</dd>
</dl>
</li>
</ul>
<a name="getBCWP--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBCWP</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBCWP()</pre>
<div class="block">Retrieves the budgeted cost of work performed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budgeted cost of work performed</dd>
</dl>
</li>
</ul>
<a name="setGeneric-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGeneric</h4>
<pre>public&nbsp;void&nbsp;setGeneric(boolean&nbsp;value)</pre>
<div class="block">Sets the generic flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - generic flag</dd>
</dl>
</li>
</ul>
<a name="getGeneric--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGeneric</h4>
<pre>public&nbsp;boolean&nbsp;getGeneric()</pre>
<div class="block">Retrieves the generic flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>generic flag</dd>
</dl>
</li>
</ul>
<a name="setActive-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActive</h4>
<pre>public&nbsp;void&nbsp;setActive(boolean&nbsp;value)</pre>
<div class="block">Sets the active flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - generic flag</dd>
</dl>
</li>
</ul>
<a name="getActive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActive</h4>
<pre>public&nbsp;boolean&nbsp;getActive()</pre>
<div class="block">Retrieves the active flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>generic flag</dd>
</dl>
</li>
</ul>
<a name="setActiveDirectoryGUID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActiveDirectoryGUID</h4>
<pre>public&nbsp;void&nbsp;setActiveDirectoryGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;guid)</pre>
<div class="block">Sets the active directory GUID for this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>guid</code> - active directory GUID</dd>
</dl>
</li>
</ul>
<a name="getActiveDirectoryGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActiveDirectoryGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActiveDirectoryGUID()</pre>
<div class="block">Retrieves the active directory GUID for this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>active directory GUID</dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeWorkProtected-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeWorkProtected(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Sets the actual overtime work protected duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - actual overtime work protected</dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeWorkProtected</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualOvertimeWorkProtected()</pre>
<div class="block">Retrieves the actual overtime work protected duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual overtime work protected</dd>
</dl>
</li>
</ul>
<a name="setActualWorkProtected-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualWorkProtected</h4>
<pre>public&nbsp;void&nbsp;setActualWorkProtected(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;duration)</pre>
<div class="block">Sets the actual work protected duration.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duration</code> - actual work protected</dd>
</dl>
</li>
</ul>
<a name="getActualWorkProtected--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualWorkProtected</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getActualWorkProtected()</pre>
<div class="block">Retrieves the actual work protected duration.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>actual work protected</dd>
</dl>
</li>
</ul>
<a name="setBookingType-org.mpxj.BookingType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBookingType</h4>
<pre>public&nbsp;void&nbsp;setBookingType(<a href="../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;bookingType)</pre>
<div class="block">Sets the booking type.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bookingType</code> - booking type</dd>
</dl>
</li>
</ul>
<a name="getBookingType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBookingType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/BookingType.html" title="enum in org.mpxj">BookingType</a>&nbsp;getBookingType()</pre>
<div class="block">Retrieves the booking type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>booking type</dd>
</dl>
</li>
</ul>
<a name="setCreationDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;creationDate)</pre>
<div class="block">Sets the creation date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>creationDate</code> - creation date</dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreationDate()</pre>
<div class="block">Retrieves the creation date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>creation date</dd>
</dl>
</li>
</ul>
<a name="setEnterprise-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterprise</h4>
<pre>public&nbsp;void&nbsp;setEnterprise(boolean&nbsp;enterprise)</pre>
<div class="block">Sets a flag indicating that a resource is an enterprise resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>enterprise</code> - boolean flag</dd>
</dl>
</li>
</ul>
<a name="getEnterprise--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterprise</h4>
<pre>public&nbsp;boolean&nbsp;getEnterprise()</pre>
<div class="block">Retrieves a flag indicating that a resource is an enterprise resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean flag</dd>
</dl>
</li>
</ul>
<a name="getCalendarUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCalendarUniqueID()</pre>
<div class="block">Retrieve the calendar unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="setCalendarUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarUniqueID</h4>
<pre>public&nbsp;void&nbsp;setCalendarUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Set the calendar unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - calendar unique ID</dd>
</dl>
</li>
</ul>
<a name="getCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;getCalendar()</pre>
<div class="block">This method retrieves the calendar associated with this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectCalendar instance</dd>
</dl>
</li>
</ul>
<a name="setCalendar-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendar</h4>
<pre>public&nbsp;void&nbsp;setCalendar(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block">This method allows a pre-existing resource calendar to be attached to a
 resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - resource calendar</dd>
</dl>
</li>
</ul>
<a name="addCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCalendar</h4>
<pre>public&nbsp;<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;addCalendar()
                            throws <a href="../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">This method allows a calendar to be added to a resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ResourceCalendar</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code> - if more than one calendar is added</dd>
</dl>
</li>
</ul>
<a name="setBaseCalendar-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseCalendar</h4>
<pre>public&nbsp;void&nbsp;setBaseCalendar(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;val)</pre>
<div class="block">Sets the Base Calendar field indicates which calendar is the base calendar
 for a resource calendar. The list includes the three built-in calendars,
 as well as any new base calendars you have created in the Change Working
 Time dialog box.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - calendar name</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;val)</pre>
<div class="block">Sets the baseline cost. This field is ignored on import into MS Project</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;val)</pre>
<div class="block">Sets the baseline work duration. This field is ignored on import into MS
 Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="setID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>public&nbsp;void&nbsp;setID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">Sets ID field value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithID.html#setID-java.lang.Integer-">setID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - value</dd>
</dl>
</li>
</ul>
<a name="setLinkedFields-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLinkedFields</h4>
<pre>public&nbsp;void&nbsp;setLinkedFields(boolean&nbsp;val)</pre>
<div class="block">This field is ignored on import into MS Project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="setObjects-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObjects</h4>
<pre>public&nbsp;void&nbsp;setObjects(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">Set objects.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - - value to be set</dd>
</dl>
</li>
</ul>
<a name="setText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dd><code>value</code> - text value</dd>
</dl>
</li>
</ul>
<a name="getText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getText(int&nbsp;index)</pre>
<div class="block">Retrieve a text value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - text index (1-30)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>text value</dd>
</dl>
</li>
</ul>
<a name="setUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUniqueID</h4>
<pre>public&nbsp;void&nbsp;setUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;val)</pre>
<div class="block">Sets Unique ID of this resource.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html#setUniqueID-java.lang.Integer-">setUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>val</code> - Unique ID</dd>
</dl>
</li>
</ul>
<a name="getBaseCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseCalendar</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBaseCalendar()</pre>
<div class="block">Retrieves Base Calendar name associated with this resource. This field
 indicates which calendar is the base calendar for a resource calendar.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Base calendar name</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost()</pre>
<div class="block">Retrieves the Baseline Cost value. This value is the total planned cost
 for a resource for all assigned tasks. Baseline cost is also referred to
 as budget at completion (BAC).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Baseline cost value</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork()</pre>
<div class="block">Retrieves the Baseline Work value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Baseline work value</dd>
</dl>
</li>
</ul>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getID()</pre>
<div class="block">Gets ID field value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithID.html#getID--">getID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithID.html" title="interface in org.mpxj">ProjectEntityWithID</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getLinkedFields--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLinkedFields</h4>
<pre>public&nbsp;boolean&nbsp;getLinkedFields()</pre>
<div class="block">Gets Linked Fields field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getObjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getObjects()</pre>
<div class="block">Gets objects field value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUniqueID()</pre>
<div class="block">Gets Unique ID field value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html#getUniqueID--">getUniqueID</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>value</dd>
</dl>
</li>
</ul>
<a name="getParentResourceUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentResourceUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getParentResourceUniqueID()</pre>
<div class="block">Retrieve the parent resource's Unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>parent resource Unique ID</dd>
</dl>
</li>
</ul>
<a name="setParentResourceUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParentResourceUniqueID</h4>
<pre>public&nbsp;void&nbsp;setParentResourceUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;id)</pre>
<div class="block">Sets the parent resource's Unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - parent resource unique ID</dd>
</dl>
</li>
</ul>
<a name="getParentResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentResource</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;getParentResource()</pre>
<div class="block">Retrieve the parent resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>parent resource</dd>
</dl>
</li>
</ul>
<a name="setParentResource-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParentResource</h4>
<pre>public&nbsp;void&nbsp;setParentResource(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">Set the parent resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - parent resource</dd>
</dl>
</li>
</ul>
<a name="setStart-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStart</h4>
<pre>public&nbsp;void&nbsp;setStart(int&nbsp;index,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dd><code>value</code> - start value</dd>
</dl>
</li>
</ul>
<a name="getStart-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStart(int&nbsp;index)</pre>
<div class="block">Retrieve a start value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - start index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>start value</dd>
</dl>
</li>
</ul>
<a name="setFinish-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinish</h4>
<pre>public&nbsp;void&nbsp;setFinish(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dd><code>value</code> - finish value</dd>
</dl>
</li>
</ul>
<a name="getFinish-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinish(int&nbsp;index)</pre>
<div class="block">Retrieve a finish value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - finish index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>finish value</dd>
</dl>
</li>
</ul>
<a name="setNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumber</h4>
<pre>public&nbsp;void&nbsp;setNumber(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dd><code>value</code> - number value</dd>
</dl>
</li>
</ul>
<a name="getNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getNumber(int&nbsp;index)</pre>
<div class="block">Retrieve a number value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - number index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>number value</dd>
</dl>
</li>
</ul>
<a name="setDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuration</h4>
<pre>public&nbsp;void&nbsp;setDuration(int&nbsp;index,
                        <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dd><code>value</code> - duration value</dd>
</dl>
</li>
</ul>
<a name="getDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getDuration(int&nbsp;index)</pre>
<div class="block">Retrieve a duration value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - duration index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>duration value</dd>
</dl>
</li>
</ul>
<a name="setDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dd><code>value</code> - date value</dd>
</dl>
</li>
</ul>
<a name="getDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDate(int&nbsp;index)</pre>
<div class="block">Retrieve a date value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - date index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>date value</dd>
</dl>
</li>
</ul>
<a name="setCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(int&nbsp;index,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dd><code>value</code> - cost value</dd>
</dl>
</li>
</ul>
<a name="getCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getCost(int&nbsp;index)</pre>
<div class="block">Retrieve a cost value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - cost index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost value</dd>
</dl>
</li>
</ul>
<a name="setFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFlag</h4>
<pre>public&nbsp;void&nbsp;setFlag(int&nbsp;index,
                    boolean&nbsp;value)</pre>
<div class="block">Set a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dd><code>value</code> - flag value</dd>
</dl>
</li>
</ul>
<a name="getFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFlag</h4>
<pre>public&nbsp;boolean&nbsp;getFlag(int&nbsp;index)</pre>
<div class="block">Retrieve a flag value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - flag index (1-20)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>flag value</dd>
</dl>
</li>
</ul>
<a name="setOutlineCode-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineCode</h4>
<pre>public&nbsp;void&nbsp;setOutlineCode(int&nbsp;index,
                           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set an outline code value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - outline code index (1-10)</dd>
<dd><code>value</code> - outline code value</dd>
</dl>
</li>
</ul>
<a name="getOutlineCode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOutlineCode(int&nbsp;index)</pre>
<div class="block">Retrieve an outline code value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - outline code index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>outline code value</dd>
</dl>
</li>
</ul>
<a name="remove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;void&nbsp;remove()</pre>
<div class="block">Removes this resource from the project.</div>
</li>
</ul>
<a name="getFieldByAlias-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldByAlias</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias)</pre>
<div class="block">Retrieve the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setFieldByAlias-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldByAlias</h4>
<pre>public&nbsp;void&nbsp;setFieldByAlias(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;alias,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Set the value of a field using its alias.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alias</code> - field alias</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="addResourceAssignment-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceAssignment</h4>
<pre>public&nbsp;void&nbsp;addResourceAssignment(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</pre>
<div class="block">This method is used internally within MPXJ to track tasks which are
 assigned to a particular resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>assignment</code> - resource assignment instance</dd>
</dl>
</li>
</ul>
<a name="getTaskAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskAssignments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&gt;&nbsp;getTaskAssignments()</pre>
<div class="block">Retrieve a list of tasks assigned to this resource. Note that if this
 project data has been read from an MPX file which declared some or all of
 the resources assignments before the tasks and resources to which the
 assignments relate, then these assignments may not appear in this list.
 Caveat emptor!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of tasks assigned to this resource</dd>
</dl>
</li>
</ul>
<a name="addRoleAssignment-org.mpxj.Resource-org.mpxj.SkillLevel-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoleAssignment</h4>
<pre>public&nbsp;void&nbsp;addRoleAssignment(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role,
                              <a href="../../org/mpxj/SkillLevel.html" title="enum in org.mpxj">SkillLevel</a>&nbsp;skillLevel)</pre>
<div class="block">Add a role assignment, and a skill level for the role, to this resource. Replaces any existing
 assignment for this role.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>role</code> - role to assign to the resource</dd>
<dd><code>skillLevel</code> - skill level</dd>
</dl>
</li>
</ul>
<a name="removeRoleAssignment-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeRoleAssignment</h4>
<pre>public&nbsp;void&nbsp;removeRoleAssignment(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</pre>
<div class="block">Remove a role assignment from this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>role</code> - role to remove</dd>
</dl>
</li>
</ul>
<a name="getRoleAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleAssignments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>,<a href="../../org/mpxj/SkillLevel.html" title="enum in org.mpxj">SkillLevel</a>&gt;&nbsp;getRoleAssignments()</pre>
<div class="block">Retrieve a map of the roles assigned to this resource.
 The roles are represented as the keys in this map
 with the skill level represented as the value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>role assignment map</dd>
</dl>
</li>
</ul>
<a name="getSubprojectResourceUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubprojectResourceUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSubprojectResourceUniqueID()</pre>
<div class="block">Where a resource in an MPP file represents a resource from a subproject,
 this value will be non-zero. The value itself is the unique ID value shown
 in the parent project. To retrieve the value of the resource unique ID in
 the child project, remove the top two bytes:
 <p>
 resourceID = (subprojectUniqueID &amp; 0xFFFF)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sub project unique resource ID</dd>
</dl>
</li>
</ul>
<a name="setSubprojectResourceUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectResourceUniqueID</h4>
<pre>public&nbsp;void&nbsp;setSubprojectResourceUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;subprojectUniqueResourceID)</pre>
<div class="block">Sets the sub project unique resource ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>subprojectUniqueResourceID</code> - subproject unique resource ID</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseCost(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseCost</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseCost(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEnterpriseDate(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDate-int-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDate</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDate(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseDuration-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseDuration</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getEnterpriseDuration(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseDuration-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseDuration</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseDuration(int&nbsp;index,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseFlag-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseFlag</h4>
<pre>public&nbsp;boolean&nbsp;getEnterpriseFlag(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseFlag-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseFlag</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseFlag(int&nbsp;index,
                              boolean&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseNumber-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getEnterpriseNumber(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseNumber-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseNumber</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseNumber(int&nbsp;index,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="getEnterpriseText-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnterpriseText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEnterpriseText(int&nbsp;index)</pre>
<div class="block">Retrieve an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field value</dd>
</dl>
</li>
</ul>
<a name="setEnterpriseText-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnterpriseText</h4>
<pre>public&nbsp;void&nbsp;setEnterpriseText(int&nbsp;index,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set an enterprise field value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - field index</dd>
<dd><code>value</code> - field value</dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(int&nbsp;baselineNumber,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="setBaselineWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineWork(int&nbsp;baselineNumber,
                            <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dd><code>value</code> - baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBaselineWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline index (1-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline value</dd>
</dl>
</li>
</ul>
<a name="getBudget--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudget</h4>
<pre>public&nbsp;boolean&nbsp;getBudget()</pre>
<div class="block">Retrieve the budget flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget flag</dd>
</dl>
</li>
</ul>
<a name="setBudget-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudget</h4>
<pre>public&nbsp;void&nbsp;setBudget(boolean&nbsp;budget)</pre>
<div class="block">Set the budget flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>budget</code> - budget flag</dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;getGUID()</pre>
<div class="block">Retrieves the resource GUID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource GUID.</dd>
</dl>
</li>
</ul>
<a name="setUnit-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnit</h4>
<pre>public&nbsp;void&nbsp;setUnit(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the unit field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - unit value</dd>
</dl>
</li>
</ul>
<a name="getUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnit</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getUnit()</pre>
<div class="block">Retrieve the unit field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unit value</dd>
</dl>
</li>
</ul>
<a name="setSupplyReference-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSupplyReference</h4>
<pre>public&nbsp;void&nbsp;setSupplyReference(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the supply reference field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - supply reference value</dd>
</dl>
</li>
</ul>
<a name="getSupplyReference--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSupplyReference</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSupplyReference()</pre>
<div class="block">Retrieve the supply reference field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>supply reference value</dd>
</dl>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>public&nbsp;void&nbsp;setDescription(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the description field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - description field</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDescription()</pre>
<div class="block">Retrieve the description field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>description value</dd>
</dl>
</li>
</ul>
<a name="setResourceID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceID</h4>
<pre>public&nbsp;void&nbsp;setResourceID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the resource ID field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - resource ID value</dd>
</dl>
</li>
</ul>
<a name="getResourceID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceID()</pre>
<div class="block">Retrieve the resource ID field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>resource ID value</dd>
</dl>
</li>
</ul>
<a name="setModifyOnIntegrate-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModifyOnIntegrate</h4>
<pre>public&nbsp;void&nbsp;setModifyOnIntegrate(boolean&nbsp;value)</pre>
<div class="block">Set the modify on integrate field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - modify on integrate value</dd>
</dl>
</li>
</ul>
<a name="getModifyOnIntegrate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModifyOnIntegrate</h4>
<pre>public&nbsp;boolean&nbsp;getModifyOnIntegrate()</pre>
<div class="block">Retrieve the modify on integrate value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>modify on integrate value</dd>
</dl>
</li>
</ul>
<a name="setExpensesOnly-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setExpensesOnly</h4>
<pre>public&nbsp;void&nbsp;setExpensesOnly(boolean&nbsp;value)</pre>
<div class="block">Set the expenses only field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - expenses only value</dd>
</dl>
</li>
</ul>
<a name="getExpensesOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpensesOnly</h4>
<pre>public&nbsp;boolean&nbsp;getExpensesOnly()</pre>
<div class="block">Retrieve the expenses only field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>expenses only value</dd>
</dl>
</li>
</ul>
<a name="setPeriodDur-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodDur</h4>
<pre>public&nbsp;void&nbsp;setPeriodDur(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the period dur field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - period dur value</dd>
</dl>
</li>
</ul>
<a name="getPeriodDur--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodDur</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPeriodDur()</pre>
<div class="block">Retrieve the period dur field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>period dur value</dd>
</dl>
</li>
</ul>
<a name="setPriority-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>public&nbsp;void&nbsp;setPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the priority field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - priority value</dd>
</dl>
</li>
</ul>
<a name="getPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriority</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPriority()</pre>
<div class="block">Retrieve the priority field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>priority value</dd>
</dl>
</li>
</ul>
<a name="setRate-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRate</h4>
<pre>public&nbsp;void&nbsp;setRate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the rate field.
 Note that this is a TurboProject-specific field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - rate value</dd>
</dl>
</li>
</ul>
<a name="getRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getRate()</pre>
<div class="block">Retrieve the rate field.
 Note that this is a TurboProject-specific field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>rate value</dd>
</dl>
</li>
</ul>
<a name="setPool-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPool</h4>
<pre>public&nbsp;void&nbsp;setPool(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the pool field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - pool value</dd>
</dl>
</li>
</ul>
<a name="getPool--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPool</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPool()</pre>
<div class="block">Retrieve the pool field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>pool value</dd>
</dl>
</li>
</ul>
<a name="setPerDay-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPerDay</h4>
<pre>public&nbsp;void&nbsp;setPerDay(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the per day field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - per day value</dd>
</dl>
</li>
</ul>
<a name="getPerDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPerDay</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getPerDay()</pre>
<div class="block">Retrieve the per day field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>per day value</dd>
</dl>
</li>
</ul>
<a name="setPhone-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhone</h4>
<pre>public&nbsp;void&nbsp;setPhone(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the phone field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - phone value</dd>
</dl>
</li>
</ul>
<a name="getPhone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhone</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPhone()</pre>
<div class="block">Retrieve the phone field.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>phone value</dd>
</dl>
</li>
</ul>
<a name="setRole-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRole</h4>
<pre>public&nbsp;void&nbsp;setRole(boolean&nbsp;value)</pre>
<div class="block">Set the role field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - role value</dd>
</dl>
</li>
</ul>
<a name="getRole--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRole</h4>
<pre>public&nbsp;boolean&nbsp;getRole()</pre>
<div class="block">Retrieve the role field.
 Returns true if this object represents a role rather than an individual resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>role value</dd>
</dl>
</li>
</ul>
<a name="setGUID-java.util.UUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;value)</pre>
<div class="block">Sets the resource GUID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - resource GUID</dd>
</dl>
</li>
</ul>
<a name="setCostRateTable-int-org.mpxj.CostRateTable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostRateTable</h4>
<pre>public&nbsp;void&nbsp;setCostRateTable(int&nbsp;index,
                             <a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a>&nbsp;crt)</pre>
<div class="block">Associates a complete cost rate table with the
 current resource. Note that the index corresponds with the
 letter label used by MS Project to identify each table.
 For example 0=Table A, 1=Table B, 2=Table C, and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - table index</dd>
<dd><code>crt</code> - table instance</dd>
</dl>
</li>
</ul>
<a name="getCostRateTable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostRateTable</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CostRateTable.html" title="class in org.mpxj">CostRateTable</a>&nbsp;getCostRateTable(int&nbsp;index)</pre>
<div class="block">Retrieves a cost rate table associated with a resource.
 Note that the index corresponds with the
 letter label used by MS Project to identify each table.
 For example 0=Table A, 1=Table B, 2=Table C, and so on.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - table index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>table instance</dd>
</dl>
</li>
</ul>
<a name="getCurrentCostRateTableEntry-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentCostRateTableEntry</h4>
<pre>public&nbsp;<a href="../../org/mpxj/CostRateTableEntry.html" title="class in org.mpxj">CostRateTableEntry</a>&nbsp;getCurrentCostRateTableEntry(int&nbsp;costRateTable)</pre>
<div class="block">Retrieve the cost rate table entry effective for the current date.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>costRateTable</code> - cost rate table index</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost rate table entry</dd>
</dl>
</li>
</ul>
<a name="getAvailability--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAvailability</h4>
<pre>public&nbsp;<a href="../../org/mpxj/AvailabilityTable.html" title="class in org.mpxj">AvailabilityTable</a>&nbsp;getAvailability()</pre>
<div class="block">Retrieve the availability table for this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>availability table</dd>
</dl>
</li>
</ul>
<a name="getCurrentAvailabilityTableEntry--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentAvailabilityTableEntry</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Availability.html" title="class in org.mpxj">Availability</a>&nbsp;getCurrentAvailabilityTableEntry()</pre>
<div class="block">Retrieve the availability table entry effective for the current date.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>availability table entry</dd>
</dl>
</li>
</ul>
<a name="getBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBudgetCost()</pre>
<div class="block">Retrieve the budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget cost value</dd>
</dl>
</li>
</ul>
<a name="setBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBudgetWork()</pre>
<div class="block">Retrieve the budget work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>budget work value</dd>
</dl>
</li>
</ul>
<a name="setBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - budget work value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost()</pre>
<div class="block">Retrieve the baseline budget cost.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set the baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork()</pre>
<div class="block">Retrieve the baseline budget work.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set the baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetCost-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;getBaselineBudgetCost(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget cost</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetCost-int-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetCost(int&nbsp;baselineNumber,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;value)</pre>
<div class="block">Set a baseline budget cost.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline budget cost value</dd>
</dl>
</li>
</ul>
<a name="getBaselineBudgetWork-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineBudgetWork</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;getBaselineBudgetWork(int&nbsp;baselineNumber)</pre>
<div class="block">Retrieve a baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="setBaselineBudgetWork-int-org.mpxj.Duration-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineBudgetWork</h4>
<pre>public&nbsp;void&nbsp;setBaselineBudgetWork(int&nbsp;baselineNumber,
                                  <a href="../../org/mpxj/Duration.html" title="class in org.mpxj">Duration</a>&nbsp;value)</pre>
<div class="block">Set a baseline budget work.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>baselineNumber</code> - baseline number</dd>
<dd><code>value</code> - baseline budget work value</dd>
</dl>
</li>
</ul>
<a name="getCostCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostCenter</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCostCenter()</pre>
<div class="block">Retrieve the cost center.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>cost center value</dd>
</dl>
</li>
</ul>
<a name="setCostCenter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostCenter</h4>
<pre>public&nbsp;void&nbsp;setCostCenter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Set the cost center.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - cost center value</dd>
</dl>
</li>
</ul>
<a name="getCalculateCostsFromUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculateCostsFromUnits</h4>
<pre>public&nbsp;boolean&nbsp;getCalculateCostsFromUnits()</pre>
<div class="block">Retrieve the calculate costs from units flag.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculate costs from units flag</dd>
</dl>
</li>
</ul>
<a name="setCalculateCostsFromUnits-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalculateCostsFromUnits</h4>
<pre>public&nbsp;void&nbsp;setCalculateCostsFromUnits(boolean&nbsp;calculateCostsFromUnits)</pre>
<div class="block">Set the calculate costs from units flag.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calculateCostsFromUnits</code> - calculate costs from units flag</dd>
</dl>
</li>
</ul>
<a name="getSequenceNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSequenceNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getSequenceNumber()</pre>
<div class="block">Retrieve this resource's sequence number.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>sequence number</dd>
</dl>
</li>
</ul>
<a name="setSequenceNumber-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSequenceNumber</h4>
<pre>public&nbsp;void&nbsp;setSequenceNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;sequenceNumber)</pre>
<div class="block">Set this resource's sequence number.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sequenceNumber</code> - sequence number</dd>
</dl>
</li>
</ul>
<a name="getLocationUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocationUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLocationUniqueID()</pre>
<div class="block">Retrieves the location unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location unique ID</dd>
</dl>
</li>
</ul>
<a name="setLocationUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocationUniqueID</h4>
<pre>public&nbsp;void&nbsp;setLocationUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the location unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - location unique ID</dd>
</dl>
</li>
</ul>
<a name="getLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocation</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;getLocation()</pre>
<div class="block">Retrieves the location.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>location.</dd>
</dl>
</li>
</ul>
<a name="setLocation-org.mpxj.Location-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLocation</h4>
<pre>public&nbsp;void&nbsp;setLocation(<a href="../../org/mpxj/Location.html" title="class in org.mpxj">Location</a>&nbsp;location)</pre>
<div class="block">Sets the location.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - location</dd>
</dl>
</li>
</ul>
<a name="getShiftUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShiftUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getShiftUniqueID()</pre>
<div class="block">Retrieves the shift unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>shift unique ID</dd>
</dl>
</li>
</ul>
<a name="setShiftUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShiftUniqueID</h4>
<pre>public&nbsp;void&nbsp;setShiftUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the shift unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - shift unique ID</dd>
</dl>
</li>
</ul>
<a name="getShift--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShift</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a>&nbsp;getShift()</pre>
<div class="block">Retrieves the shift.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>shift.</dd>
</dl>
</li>
</ul>
<a name="setShift-org.mpxj.Shift-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShift</h4>
<pre>public&nbsp;void&nbsp;setShift(<a href="../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a>&nbsp;shift)</pre>
<div class="block">Sets the shift.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>shift</code> - shift</dd>
</dl>
</li>
</ul>
<a name="getUnitOfMeasureUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitOfMeasureUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUnitOfMeasureUniqueID()</pre>
<div class="block">Retrieve the unit of measure unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unit of measure unique ID</dd>
</dl>
</li>
</ul>
<a name="setUnitOfMeasureUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnitOfMeasureUniqueID</h4>
<pre>public&nbsp;void&nbsp;setUnitOfMeasureUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the unit of measure unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - unit of measure unique ID</dd>
</dl>
</li>
</ul>
<a name="getUnitOfMeasure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitOfMeasure</h4>
<pre>public&nbsp;<a href="../../org/mpxj/UnitOfMeasure.html" title="class in org.mpxj">UnitOfMeasure</a>&nbsp;getUnitOfMeasure()</pre>
<div class="block">Retrieves the unit of measure for this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unit of measure instance</dd>
</dl>
</li>
</ul>
<a name="setUnitOfMeasure-org.mpxj.UnitOfMeasure-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnitOfMeasure</h4>
<pre>public&nbsp;void&nbsp;setUnitOfMeasure(<a href="../../org/mpxj/UnitOfMeasure.html" title="class in org.mpxj">UnitOfMeasure</a>&nbsp;unitOfMeasure)</pre>
<div class="block">Sets the unit of measure instance for this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>unitOfMeasure</code> - unit of measure instance</dd>
</dl>
</li>
</ul>
<a name="getPrimaryRoleUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrimaryRoleUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPrimaryRoleUniqueID()</pre>
<div class="block">Retrieves the primary role unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>primary role unique ID</dd>
</dl>
</li>
</ul>
<a name="setPrimaryRoleUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrimaryRoleUniqueID</h4>
<pre>public&nbsp;void&nbsp;setPrimaryRoleUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the primary role unique ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - primary role unique ID</dd>
</dl>
</li>
</ul>
<a name="getPrimaryRole--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrimaryRole</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;getPrimaryRole()</pre>
<div class="block">Retrieves the primary role.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>primary role</dd>
</dl>
</li>
</ul>
<a name="setPrimaryRole-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrimaryRole</h4>
<pre>public&nbsp;void&nbsp;setPrimaryRole(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;role)</pre>
<div class="block">Sets the primary role.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>role</code> - primary role</dd>
</dl>
</li>
</ul>
<a name="getResourceCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceCodeValues</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/ResourceCode.html" title="class in org.mpxj">ResourceCode</a>,<a href="../../org/mpxj/ResourceCodeValue.html" title="class in org.mpxj">ResourceCodeValue</a>&gt;&nbsp;getResourceCodeValues()</pre>
<div class="block">Retrieve the resource code values associated with this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>map of resource code values</dd>
</dl>
</li>
</ul>
<a name="addResourceCodeValue-org.mpxj.ResourceCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResourceCodeValue</h4>
<pre>public&nbsp;void&nbsp;addResourceCodeValue(<a href="../../org/mpxj/ResourceCodeValue.html" title="class in org.mpxj">ResourceCodeValue</a>&nbsp;value)</pre>
<div class="block">Assign a resource code value to this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - resource code value</dd>
</dl>
</li>
</ul>
<a name="getRoleCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleCodeValues</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../org/mpxj/RoleCode.html" title="class in org.mpxj">RoleCode</a>,<a href="../../org/mpxj/RoleCodeValue.html" title="class in org.mpxj">RoleCodeValue</a>&gt;&nbsp;getRoleCodeValues()</pre>
<div class="block">Retrieve the role code values associated with this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>map of role code values</dd>
</dl>
</li>
</ul>
<a name="addRoleCodeValue-org.mpxj.RoleCodeValue-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoleCodeValue</h4>
<pre>public&nbsp;void&nbsp;addRoleCodeValue(<a href="../../org/mpxj/RoleCodeValue.html" title="class in org.mpxj">RoleCodeValue</a>&nbsp;value)</pre>
<div class="block">Assign a role code value to this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - resoroleurce code value</dd>
</dl>
</li>
</ul>
<a name="getCurrencyUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrencyUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCurrencyUniqueID()</pre>
<div class="block">Retrieves the unique ID of the currency associated with this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>currency unique ID</dd>
</dl>
</li>
</ul>
<a name="setCurrencyUniqueID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrencyUniqueID</h4>
<pre>public&nbsp;void&nbsp;setCurrencyUniqueID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;uniqueID)</pre>
<div class="block">Sets the unique ID of the currency associated with this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>uniqueID</code> - currency unique ID</dd>
</dl>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;<a href="../../org/mpxj/Currency.html" title="class in org.mpxj">Currency</a>&nbsp;getCurrency()</pre>
<div class="block">Retrieve the currency associated with this resource.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Currency instance</dd>
</dl>
</li>
</ul>
<a name="setCurrency-org.mpxj.Currency-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrency</h4>
<pre>public&nbsp;void&nbsp;setCurrency(<a href="../../org/mpxj/Currency.html" title="class in org.mpxj">Currency</a>&nbsp;currency)</pre>
<div class="block">Sets the currency associated with this resource.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>currency</code> - Currency instance</dd>
</dl>
</li>
</ul>
<a name="handleFieldChange-org.mpxj.FieldType-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>handleFieldChange</h4>
<pre>protected&nbsp;void&nbsp;handleFieldChange(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;oldValue,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;newValue)</pre>
<div class="block">Clear any cached calculated values which will be affected by this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>field</code> - modified field</dd>
<dd><code>newValue</code> - new value</dd>
<dd><code>oldValue</code> - old value of the updated field</dd>
</dl>
</li>
</ul>
<a name="getAlwaysCalculatedField-org.mpxj.FieldType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlwaysCalculatedField</h4>
<pre>protected&nbsp;boolean&nbsp;getAlwaysCalculatedField(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../org/mpxj/AbstractFieldContainer.html#getAlwaysCalculatedField-org.mpxj.FieldType-">AbstractFieldContainer</a></code></span></div>
<div class="block">Determine if the supplied field is always calculated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>field</code> - field to check</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this field is always calculated</dd>
</dl>
</li>
</ul>
<a name="getCalculationMethod-org.mpxj.FieldType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalculationMethod</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/function/Function.html?is-external=true" title="class or interface in java.util.function">Function</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getCalculationMethod(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;field)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../org/mpxj/AbstractFieldContainer.html#getCalculationMethod-org.mpxj.FieldType-">AbstractFieldContainer</a></code></span></div>
<div class="block">Retrieve the method used to calculate the value of the supplied field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>field</code> - target field</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>calculation function, or null if the field is not calculated</dd>
</dl>
</li>
</ul>
<a name="compareTo-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareTo</h4>
<pre>public&nbsp;int&nbsp;compareTo(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;o)</pre>
<div class="block">This method implements the only method in the Comparable interface. This
 allows Resources to be compared and sorted based on their ID value. Note
 that if the MPX/MPP file has been generated by MSP, the ID value will
 always be in the correct sequence. The Unique ID value will not
 necessarily be in the correct sequence as task insertions and deletions
 will change the order.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - object to compare this instance with</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>result of comparison</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="getParentFile--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getParentFile</h4>
<pre>public final&nbsp;<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;getParentFile()</pre>
<div class="block">Accessor method allowing retrieval of ProjectFile reference.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>reference to this the parent ProjectFile instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Resource.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/RelationType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Resource.html" target="_top">Frames</a></li>
<li><a href="Resource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
