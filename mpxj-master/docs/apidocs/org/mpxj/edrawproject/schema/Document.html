<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Adapter5.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.html" target="_top">Frames</a></li>
<li><a href="Document.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.edrawproject.schema</div>
<h2 title="Class Document" class="title">Class Document</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.edrawproject.schema.Document</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Document</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="CreatedVersion"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="CreationDate"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Creator"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="LastSaved"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Modifier"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="DPi"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ScreenWidth"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ScreenHeight"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="CalendarUID"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="MinutesPerDay"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="MinutesPerWeek"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="DaysPerMonth"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="DateFormat"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="GanttViewSplitterRate"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="m_isShowSpecificTime"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="GanttOption"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="MajorUnit"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="MinorUnit"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="ProjectUnit"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="BaselineCost"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="StartDate"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="FinishDate"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="Auto"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="ThemeIndex"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="RowColumn"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="ColumnList"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Column" maxOccurs="unbounded"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Text"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
                                                 &lt;element name="TextBlock"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="Character"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                   &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="Paragraph"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="WrapMode"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="FillColor"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="Color"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                         &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                         &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                         &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                               &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                               &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                     &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="Key" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="IsHide" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                                     &lt;attribute name="FilterType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="ColSelectALL" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="SortStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="TaskList"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Task" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Format"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                     &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                     &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="ResourceList"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Resource" maxOccurs="unbounded"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                                               &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                                               &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                                               &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                                               &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                                               &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="PredecessorLink" maxOccurs="unbounded"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                       &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                       &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
                                       &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                       &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Texts"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="TextCell" maxOccurs="unbounded"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
                                                 &lt;element name="TextBlock"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="Character"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                   &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="Paragraph"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                   &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="WrapMode"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="FillColor"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                           &lt;element name="Color"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                         &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                         &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                                         &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                               &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                               &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="BarChart" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
                           &lt;/sequence&gt;
                           &lt;attribute name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="BackGroundColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="ProgressStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="BaseLinePointxEnd" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="m_Bestyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="RowHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="CriticalPath" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="DateBaseStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="Resources" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="FontColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DurationUnits" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DateManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="m_bebartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="m_Istyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="BaseLinePointxBegin" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="HideByColumns" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Level" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="IsToggler" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="BaselineCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="DurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="ManualDurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="DateLateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="LastSaveDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
                           &lt;attribute name="m_IbartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="Work" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="SplitOffsetDuration" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="RowID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="FirstWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DateLateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="CountWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Priority" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DateManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="DateBaseFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="ShowByChild" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="ActualMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="ParentID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="LateSlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="DateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
                           &lt;attribute name="Wbs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="BaseLineNumber" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="HideID" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
                           &lt;attribute name="SplitPointList" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="EarlySlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Childs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceInfo"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Column" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Unit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Email" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="Group" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Calendars"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Calendar" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="NameU" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
                             &lt;element name="IsBaseCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                             &lt;element name="BaseCalendarUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                             &lt;element name="IsTemplateCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                             &lt;element name="MachineInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                             &lt;element name="WeekDays"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="WeekDay" maxOccurs="unbounded"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="WorkingTimes"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;sequence&gt;
                                                                     &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                     &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                   &lt;/sequence&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                             &lt;element name="Exceptions"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Exception" maxOccurs="unbounded"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;sequence&gt;
                                                 &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="TimePeriod"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                                           &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                                 &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                                 &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
                                                 &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                                                 &lt;element name="WorkingTimes"&gt;
                                                   &lt;complexType&gt;
                                                     &lt;complexContent&gt;
                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                         &lt;sequence&gt;
                                                           &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
                                                             &lt;complexType&gt;
                                                               &lt;complexContent&gt;
                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                                                   &lt;sequence&gt;
                                                                     &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                     &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                                                                   &lt;/sequence&gt;
                                                                 &lt;/restriction&gt;
                                                               &lt;/complexContent&gt;
                                                             &lt;/complexType&gt;
                                                           &lt;/element&gt;
                                                         &lt;/sequence&gt;
                                                       &lt;/restriction&gt;
                                                     &lt;/complexContent&gt;
                                                   &lt;/complexType&gt;
                                                 &lt;/element&gt;
                                               &lt;/sequence&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="WaterMark"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="lineStyleInformation"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="line_roundsize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="line_width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="line_Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="line_index" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
       &lt;attribute name="DocGuid" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="OS" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#calendars">calendars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#calendarUID">calendarUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#createdVersion">createdVersion</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#creationDate">creationDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#creator">creator</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#dateFormat">dateFormat</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#daysPerMonth">daysPerMonth</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#docGuid">docGuid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#dPi">dPi</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#ganttOption">ganttOption</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#ganttViewSplitterRate">ganttViewSplitterRate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#lastSaved">lastSaved</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#lineStyleInformation">lineStyleInformation</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#minutesPerDay">minutesPerDay</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#minutesPerWeek">minutesPerWeek</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#mIsShowSpecificTime">mIsShowSpecificTime</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#modifier">modifier</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#os">os</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#resourceInfo">resourceInfo</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#rowColumn">rowColumn</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#screenHeight">screenHeight</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#screenWidth">screenWidth</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#taskList">taskList</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#version">version</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#waterMark">waterMark</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#Document--">Document</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getCalendars--">getCalendars</a></span>()</code>
<div class="block">Gets the value of the calendars property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getCalendarUID--">getCalendarUID</a></span>()</code>
<div class="block">Gets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getCreatedVersion--">getCreatedVersion</a></span>()</code>
<div class="block">Gets the value of the createdVersion property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Gets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Gets the value of the creator property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getDateFormat--">getDateFormat</a></span>()</code>
<div class="block">Gets the value of the dateFormat property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getDaysPerMonth--">getDaysPerMonth</a></span>()</code>
<div class="block">Gets the value of the daysPerMonth property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getDocGuid--">getDocGuid</a></span>()</code>
<div class="block">Gets the value of the docGuid property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getDPi--">getDPi</a></span>()</code>
<div class="block">Gets the value of the dPi property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getGanttOption--">getGanttOption</a></span>()</code>
<div class="block">Gets the value of the ganttOption property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getGanttViewSplitterRate--">getGanttViewSplitterRate</a></span>()</code>
<div class="block">Gets the value of the ganttViewSplitterRate property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getLastSaved--">getLastSaved</a></span>()</code>
<div class="block">Gets the value of the lastSaved property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getLineStyleInformation--">getLineStyleInformation</a></span>()</code>
<div class="block">Gets the value of the lineStyleInformation property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getMinutesPerDay--">getMinutesPerDay</a></span>()</code>
<div class="block">Gets the value of the minutesPerDay property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getMinutesPerWeek--">getMinutesPerWeek</a></span>()</code>
<div class="block">Gets the value of the minutesPerWeek property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getMIsShowSpecificTime--">getMIsShowSpecificTime</a></span>()</code>
<div class="block">Gets the value of the mIsShowSpecificTime property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getModifier--">getModifier</a></span>()</code>
<div class="block">Gets the value of the modifier property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getOS--">getOS</a></span>()</code>
<div class="block">Gets the value of the os property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getResourceInfo--">getResourceInfo</a></span>()</code>
<div class="block">Gets the value of the resourceInfo property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getRowColumn--">getRowColumn</a></span>()</code>
<div class="block">Gets the value of the rowColumn property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getScreenHeight--">getScreenHeight</a></span>()</code>
<div class="block">Gets the value of the screenHeight property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getScreenWidth--">getScreenWidth</a></span>()</code>
<div class="block">Gets the value of the screenWidth property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getTaskList--">getTaskList</a></span>()</code>
<div class="block">Gets the value of the taskList property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Gets the value of the version property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#getWaterMark--">getWaterMark</a></span>()</code>
<div class="block">Gets the value of the waterMark property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setCalendars-org.mpxj.edrawproject.schema.Document.Calendars-">setCalendars</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendars property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setCalendarUID-org.mpxj.edrawproject.schema.Document.CalendarUID-">setCalendarUID</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarUID property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setCreatedVersion-org.mpxj.edrawproject.schema.Document.CreatedVersion-">setCreatedVersion</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a>&nbsp;value)</code>
<div class="block">Sets the value of the createdVersion property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setCreationDate-org.mpxj.edrawproject.schema.Document.CreationDate-">setCreationDate</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the creationDate property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setCreator-org.mpxj.edrawproject.schema.Document.Creator-">setCreator</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a>&nbsp;value)</code>
<div class="block">Sets the value of the creator property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setDateFormat-org.mpxj.edrawproject.schema.Document.DateFormat-">setDateFormat</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateFormat property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setDaysPerMonth-org.mpxj.edrawproject.schema.Document.DaysPerMonth-">setDaysPerMonth</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a>&nbsp;value)</code>
<div class="block">Sets the value of the daysPerMonth property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setDocGuid-java.lang.String-">setDocGuid</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the docGuid property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setDPi-org.mpxj.edrawproject.schema.Document.DPi-">setDPi</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a>&nbsp;value)</code>
<div class="block">Sets the value of the dPi property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setGanttOption-org.mpxj.edrawproject.schema.Document.GanttOption-">setGanttOption</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a>&nbsp;value)</code>
<div class="block">Sets the value of the ganttOption property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setGanttViewSplitterRate-org.mpxj.edrawproject.schema.Document.GanttViewSplitterRate-">setGanttViewSplitterRate</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a>&nbsp;value)</code>
<div class="block">Sets the value of the ganttViewSplitterRate property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setLastSaved-org.mpxj.edrawproject.schema.Document.LastSaved-">setLastSaved</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastSaved property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setLineStyleInformation-org.mpxj.edrawproject.schema.Document.LineStyleInformation-">setLineStyleInformation</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a>&nbsp;value)</code>
<div class="block">Sets the value of the lineStyleInformation property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setMinutesPerDay-org.mpxj.edrawproject.schema.Document.MinutesPerDay-">setMinutesPerDay</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a>&nbsp;value)</code>
<div class="block">Sets the value of the minutesPerDay property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setMinutesPerWeek-org.mpxj.edrawproject.schema.Document.MinutesPerWeek-">setMinutesPerWeek</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a>&nbsp;value)</code>
<div class="block">Sets the value of the minutesPerWeek property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setMIsShowSpecificTime-org.mpxj.edrawproject.schema.Document.MIsShowSpecificTime-">setMIsShowSpecificTime</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the mIsShowSpecificTime property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setModifier-org.mpxj.edrawproject.schema.Document.Modifier-">setModifier</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a>&nbsp;value)</code>
<div class="block">Sets the value of the modifier property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setOS-java.lang.String-">setOS</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the os property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setResourceInfo-org.mpxj.edrawproject.schema.Document.ResourceInfo-">setResourceInfo</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceInfo property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setRowColumn-org.mpxj.edrawproject.schema.Document.RowColumn-">setRowColumn</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a>&nbsp;value)</code>
<div class="block">Sets the value of the rowColumn property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setScreenHeight-org.mpxj.edrawproject.schema.Document.ScreenHeight-">setScreenHeight</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a>&nbsp;value)</code>
<div class="block">Sets the value of the screenHeight property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setScreenWidth-org.mpxj.edrawproject.schema.Document.ScreenWidth-">setScreenWidth</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a>&nbsp;value)</code>
<div class="block">Sets the value of the screenWidth property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setTaskList-org.mpxj.edrawproject.schema.Document.TaskList-">setTaskList</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a>&nbsp;value)</code>
<div class="block">Sets the value of the taskList property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setVersion-java.lang.String-">setVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the version property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.html#setWaterMark-org.mpxj.edrawproject.schema.Document.WaterMark-">setWaterMark</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a>&nbsp;value)</code>
<div class="block">Sets the value of the waterMark property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="createdVersion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createdVersion</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a> createdVersion</pre>
</li>
</ul>
<a name="creationDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creationDate</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a> creationDate</pre>
</li>
</ul>
<a name="creator">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>creator</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a> creator</pre>
</li>
</ul>
<a name="lastSaved">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastSaved</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a> lastSaved</pre>
</li>
</ul>
<a name="modifier">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifier</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a> modifier</pre>
</li>
</ul>
<a name="dPi">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dPi</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a> dPi</pre>
</li>
</ul>
<a name="screenWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>screenWidth</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a> screenWidth</pre>
</li>
</ul>
<a name="screenHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>screenHeight</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a> screenHeight</pre>
</li>
</ul>
<a name="calendarUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarUID</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a> calendarUID</pre>
</li>
</ul>
<a name="minutesPerDay">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minutesPerDay</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a> minutesPerDay</pre>
</li>
</ul>
<a name="minutesPerWeek">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minutesPerWeek</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a> minutesPerWeek</pre>
</li>
</ul>
<a name="daysPerMonth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>daysPerMonth</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a> daysPerMonth</pre>
</li>
</ul>
<a name="dateFormat">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateFormat</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a> dateFormat</pre>
</li>
</ul>
<a name="ganttViewSplitterRate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ganttViewSplitterRate</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a> ganttViewSplitterRate</pre>
</li>
</ul>
<a name="mIsShowSpecificTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mIsShowSpecificTime</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a> mIsShowSpecificTime</pre>
</li>
</ul>
<a name="ganttOption">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ganttOption</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a> ganttOption</pre>
</li>
</ul>
<a name="rowColumn">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rowColumn</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a> rowColumn</pre>
</li>
</ul>
<a name="taskList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskList</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a> taskList</pre>
</li>
</ul>
<a name="resourceInfo">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceInfo</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a> resourceInfo</pre>
</li>
</ul>
<a name="calendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendars</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a> calendars</pre>
</li>
</ul>
<a name="waterMark">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>waterMark</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a> waterMark</pre>
</li>
</ul>
<a name="lineStyleInformation">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lineStyleInformation</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a> lineStyleInformation</pre>
</li>
</ul>
<a name="docGuid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>docGuid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> docGuid</pre>
</li>
</ul>
<a name="version">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>version</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> version</pre>
</li>
</ul>
<a name="os">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>os</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> os</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Document--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Document</h4>
<pre>public&nbsp;Document()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCreatedVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreatedVersion</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a>&nbsp;getCreatedVersion()</pre>
<div class="block">Gets the value of the createdVersion property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CreatedVersion</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreatedVersion-org.mpxj.edrawproject.schema.Document.CreatedVersion-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreatedVersion</h4>
<pre>public&nbsp;void&nbsp;setCreatedVersion(<a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema">Document.CreatedVersion</a>&nbsp;value)</pre>
<div class="block">Sets the value of the createdVersion property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CreatedVersion.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CreatedVersion</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a>&nbsp;getCreationDate()</pre>
<div class="block">Gets the value of the creationDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CreationDate</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreationDate-org.mpxj.edrawproject.schema.Document.CreationDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreationDate</h4>
<pre>public&nbsp;void&nbsp;setCreationDate(<a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema">Document.CreationDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the creationDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CreationDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CreationDate</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a>&nbsp;getCreator()</pre>
<div class="block">Gets the value of the creator property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Creator</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreator-org.mpxj.edrawproject.schema.Document.Creator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreator</h4>
<pre>public&nbsp;void&nbsp;setCreator(<a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema">Document.Creator</a>&nbsp;value)</pre>
<div class="block">Sets the value of the creator property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Creator.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Creator</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastSaved--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSaved</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a>&nbsp;getLastSaved()</pre>
<div class="block">Gets the value of the lastSaved property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema"><code>Document.LastSaved</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastSaved-org.mpxj.edrawproject.schema.Document.LastSaved-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastSaved</h4>
<pre>public&nbsp;void&nbsp;setLastSaved(<a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema">Document.LastSaved</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastSaved property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.LastSaved.html" title="class in org.mpxj.edrawproject.schema"><code>Document.LastSaved</code></a></dd>
</dl>
</li>
</ul>
<a name="getModifier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModifier</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a>&nbsp;getModifier()</pre>
<div class="block">Gets the value of the modifier property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Modifier</code></a></dd>
</dl>
</li>
</ul>
<a name="setModifier-org.mpxj.edrawproject.schema.Document.Modifier-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModifier</h4>
<pre>public&nbsp;void&nbsp;setModifier(<a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema">Document.Modifier</a>&nbsp;value)</pre>
<div class="block">Sets the value of the modifier property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Modifier.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Modifier</code></a></dd>
</dl>
</li>
</ul>
<a name="getDPi--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDPi</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a>&nbsp;getDPi()</pre>
<div class="block">Gets the value of the dPi property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DPi</code></a></dd>
</dl>
</li>
</ul>
<a name="setDPi-org.mpxj.edrawproject.schema.Document.DPi-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDPi</h4>
<pre>public&nbsp;void&nbsp;setDPi(<a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema">Document.DPi</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dPi property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DPi</code></a></dd>
</dl>
</li>
</ul>
<a name="getScreenWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScreenWidth</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a>&nbsp;getScreenWidth()</pre>
<div class="block">Gets the value of the screenWidth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ScreenWidth</code></a></dd>
</dl>
</li>
</ul>
<a name="setScreenWidth-org.mpxj.edrawproject.schema.Document.ScreenWidth-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScreenWidth</h4>
<pre>public&nbsp;void&nbsp;setScreenWidth(<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenWidth</a>&nbsp;value)</pre>
<div class="block">Sets the value of the screenWidth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenWidth.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ScreenWidth</code></a></dd>
</dl>
</li>
</ul>
<a name="getScreenHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScreenHeight</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a>&nbsp;getScreenHeight()</pre>
<div class="block">Gets the value of the screenHeight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ScreenHeight</code></a></dd>
</dl>
</li>
</ul>
<a name="setScreenHeight-org.mpxj.edrawproject.schema.Document.ScreenHeight-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScreenHeight</h4>
<pre>public&nbsp;void&nbsp;setScreenHeight(<a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema">Document.ScreenHeight</a>&nbsp;value)</pre>
<div class="block">Sets the value of the screenHeight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ScreenHeight.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ScreenHeight</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarUID</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a>&nbsp;getCalendarUID()</pre>
<div class="block">Gets the value of the calendarUID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CalendarUID</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarUID-org.mpxj.edrawproject.schema.Document.CalendarUID-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarUID</h4>
<pre>public&nbsp;void&nbsp;setCalendarUID(<a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema">Document.CalendarUID</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarUID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.CalendarUID.html" title="class in org.mpxj.edrawproject.schema"><code>Document.CalendarUID</code></a></dd>
</dl>
</li>
</ul>
<a name="getMinutesPerDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a>&nbsp;getMinutesPerDay()</pre>
<div class="block">Gets the value of the minutesPerDay property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MinutesPerDay</code></a></dd>
</dl>
</li>
</ul>
<a name="setMinutesPerDay-org.mpxj.edrawproject.schema.Document.MinutesPerDay-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerDay</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerDay(<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerDay</a>&nbsp;value)</pre>
<div class="block">Sets the value of the minutesPerDay property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerDay.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MinutesPerDay</code></a></dd>
</dl>
</li>
</ul>
<a name="getMinutesPerWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinutesPerWeek</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a>&nbsp;getMinutesPerWeek()</pre>
<div class="block">Gets the value of the minutesPerWeek property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MinutesPerWeek</code></a></dd>
</dl>
</li>
</ul>
<a name="setMinutesPerWeek-org.mpxj.edrawproject.schema.Document.MinutesPerWeek-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinutesPerWeek</h4>
<pre>public&nbsp;void&nbsp;setMinutesPerWeek(<a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema">Document.MinutesPerWeek</a>&nbsp;value)</pre>
<div class="block">Sets the value of the minutesPerWeek property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MinutesPerWeek.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MinutesPerWeek</code></a></dd>
</dl>
</li>
</ul>
<a name="getDaysPerMonth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDaysPerMonth</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a>&nbsp;getDaysPerMonth()</pre>
<div class="block">Gets the value of the daysPerMonth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DaysPerMonth</code></a></dd>
</dl>
</li>
</ul>
<a name="setDaysPerMonth-org.mpxj.edrawproject.schema.Document.DaysPerMonth-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDaysPerMonth</h4>
<pre>public&nbsp;void&nbsp;setDaysPerMonth(<a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema">Document.DaysPerMonth</a>&nbsp;value)</pre>
<div class="block">Sets the value of the daysPerMonth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DaysPerMonth.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DaysPerMonth</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateFormat</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a>&nbsp;getDateFormat()</pre>
<div class="block">Gets the value of the dateFormat property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DateFormat</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateFormat-org.mpxj.edrawproject.schema.Document.DateFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateFormat</h4>
<pre>public&nbsp;void&nbsp;setDateFormat(<a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema">Document.DateFormat</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateFormat property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.DateFormat.html" title="class in org.mpxj.edrawproject.schema"><code>Document.DateFormat</code></a></dd>
</dl>
</li>
</ul>
<a name="getGanttViewSplitterRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttViewSplitterRate</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a>&nbsp;getGanttViewSplitterRate()</pre>
<div class="block">Gets the value of the ganttViewSplitterRate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttViewSplitterRate</code></a></dd>
</dl>
</li>
</ul>
<a name="setGanttViewSplitterRate-org.mpxj.edrawproject.schema.Document.GanttViewSplitterRate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGanttViewSplitterRate</h4>
<pre>public&nbsp;void&nbsp;setGanttViewSplitterRate(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttViewSplitterRate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the ganttViewSplitterRate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttViewSplitterRate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttViewSplitterRate</code></a></dd>
</dl>
</li>
</ul>
<a name="getMIsShowSpecificTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMIsShowSpecificTime</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a>&nbsp;getMIsShowSpecificTime()</pre>
<div class="block">Gets the value of the mIsShowSpecificTime property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MIsShowSpecificTime</code></a></dd>
</dl>
</li>
</ul>
<a name="setMIsShowSpecificTime-org.mpxj.edrawproject.schema.Document.MIsShowSpecificTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMIsShowSpecificTime</h4>
<pre>public&nbsp;void&nbsp;setMIsShowSpecificTime(<a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema">Document.MIsShowSpecificTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mIsShowSpecificTime property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.MIsShowSpecificTime.html" title="class in org.mpxj.edrawproject.schema"><code>Document.MIsShowSpecificTime</code></a></dd>
</dl>
</li>
</ul>
<a name="getGanttOption--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGanttOption</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a>&nbsp;getGanttOption()</pre>
<div class="block">Gets the value of the ganttOption property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption</code></a></dd>
</dl>
</li>
</ul>
<a name="setGanttOption-org.mpxj.edrawproject.schema.Document.GanttOption-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGanttOption</h4>
<pre>public&nbsp;void&nbsp;setGanttOption(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption</a>&nbsp;value)</pre>
<div class="block">Sets the value of the ganttOption property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption</code></a></dd>
</dl>
</li>
</ul>
<a name="getRowColumn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRowColumn</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a>&nbsp;getRowColumn()</pre>
<div class="block">Gets the value of the rowColumn property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema"><code>Document.RowColumn</code></a></dd>
</dl>
</li>
</ul>
<a name="setRowColumn-org.mpxj.edrawproject.schema.Document.RowColumn-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRowColumn</h4>
<pre>public&nbsp;void&nbsp;setRowColumn(<a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema">Document.RowColumn</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rowColumn property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.RowColumn.html" title="class in org.mpxj.edrawproject.schema"><code>Document.RowColumn</code></a></dd>
</dl>
</li>
</ul>
<a name="getTaskList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTaskList</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a>&nbsp;getTaskList()</pre>
<div class="block">Gets the value of the taskList property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList</code></a></dd>
</dl>
</li>
</ul>
<a name="setTaskList-org.mpxj.edrawproject.schema.Document.TaskList-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTaskList</h4>
<pre>public&nbsp;void&nbsp;setTaskList(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a>&nbsp;value)</pre>
<div class="block">Sets the value of the taskList property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceInfo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceInfo</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a>&nbsp;getResourceInfo()</pre>
<div class="block">Gets the value of the resourceInfo property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ResourceInfo</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceInfo-org.mpxj.edrawproject.schema.Document.ResourceInfo-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceInfo</h4>
<pre>public&nbsp;void&nbsp;setResourceInfo(<a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema">Document.ResourceInfo</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceInfo property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.ResourceInfo.html" title="class in org.mpxj.edrawproject.schema"><code>Document.ResourceInfo</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a>&nbsp;getCalendars()</pre>
<div class="block">Gets the value of the calendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendars-org.mpxj.edrawproject.schema.Document.Calendars-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendars</h4>
<pre>public&nbsp;void&nbsp;setCalendars(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="getWaterMark--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWaterMark</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a>&nbsp;getWaterMark()</pre>
<div class="block">Gets the value of the waterMark property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema"><code>Document.WaterMark</code></a></dd>
</dl>
</li>
</ul>
<a name="setWaterMark-org.mpxj.edrawproject.schema.Document.WaterMark-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWaterMark</h4>
<pre>public&nbsp;void&nbsp;setWaterMark(<a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema">Document.WaterMark</a>&nbsp;value)</pre>
<div class="block">Sets the value of the waterMark property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.WaterMark.html" title="class in org.mpxj.edrawproject.schema"><code>Document.WaterMark</code></a></dd>
</dl>
</li>
</ul>
<a name="getLineStyleInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLineStyleInformation</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a>&nbsp;getLineStyleInformation()</pre>
<div class="block">Gets the value of the lineStyleInformation property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema"><code>Document.LineStyleInformation</code></a></dd>
</dl>
</li>
</ul>
<a name="setLineStyleInformation-org.mpxj.edrawproject.schema.Document.LineStyleInformation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLineStyleInformation</h4>
<pre>public&nbsp;void&nbsp;setLineStyleInformation(<a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema">Document.LineStyleInformation</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lineStyleInformation property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.LineStyleInformation.html" title="class in org.mpxj.edrawproject.schema"><code>Document.LineStyleInformation</code></a></dd>
</dl>
</li>
</ul>
<a name="getDocGuid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDocGuid</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDocGuid()</pre>
<div class="block">Gets the value of the docGuid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDocGuid-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDocGuid</h4>
<pre>public&nbsp;void&nbsp;setDocGuid(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the docGuid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getVersion()</pre>
<div class="block">Gets the value of the version property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVersion</h4>
<pre>public&nbsp;void&nbsp;setVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the version property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getOS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOS</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOS()</pre>
<div class="block">Gets the value of the os property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOS-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setOS</h4>
<pre>public&nbsp;void&nbsp;setOS(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the os property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Adapter5.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.html" target="_top">Frames</a></li>
<li><a href="Document.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
