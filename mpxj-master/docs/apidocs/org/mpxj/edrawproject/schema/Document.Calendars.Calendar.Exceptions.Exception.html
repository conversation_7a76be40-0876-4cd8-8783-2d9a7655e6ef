<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.Calendars.Calendar.Exceptions.Exception (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.Calendars.Calendar.Exceptions.Exception (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Calendars.Calendar.Exceptions.Exception.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html" target="_top">Frames</a></li>
<li><a href="Document.Calendars.Calendar.Exceptions.Exception.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.edrawproject.schema</div>
<h2 title="Class Document.Calendars.Calendar.Exceptions.Exception" class="title">Class Document.Calendars.Calendar.Exceptions.Exception</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.edrawproject.schema.Document.Calendars.Calendar.Exceptions.Exception</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.Calendars.Calendar.Exceptions.Exception</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="TimePeriod"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                   &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
         &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="WorkingTimes"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                             &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#dayWorking">dayWorking</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#enteredByOccurrences">enteredByOccurrences</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#holiday">holiday</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#occurrences">occurrences</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#timePeriod">timePeriod</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#type">type</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#workingTimes">workingTimes</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#Exception--">Exception</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#getOccurrences--">getOccurrences</a></span>()</code>
<div class="block">Gets the value of the occurrences property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#getTimePeriod--">getTimePeriod</a></span>()</code>
<div class="block">Gets the value of the timePeriod property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#getType--">getType</a></span>()</code>
<div class="block">Gets the value of the type property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#getWorkingTimes--">getWorkingTimes</a></span>()</code>
<div class="block">Gets the value of the workingTimes property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#isDayWorking--">isDayWorking</a></span>()</code>
<div class="block">Gets the value of the dayWorking property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#isEnteredByOccurrences--">isEnteredByOccurrences</a></span>()</code>
<div class="block">Gets the value of the enteredByOccurrences property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#isHoliday--">isHoliday</a></span>()</code>
<div class="block">Gets the value of the holiday property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setDayWorking-java.lang.Boolean-">setDayWorking</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the dayWorking property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setEnteredByOccurrences-java.lang.Boolean-">setEnteredByOccurrences</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the enteredByOccurrences property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setHoliday-java.lang.Boolean-">setHoliday</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the holiday property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setOccurrences-java.lang.Integer-">setOccurrences</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the occurrences property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setTimePeriod-org.mpxj.edrawproject.schema.Document.Calendars.Calendar.Exceptions.Exception.TimePeriod-">setTimePeriod</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a>&nbsp;value)</code>
<div class="block">Sets the value of the timePeriod property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setType-java.lang.Integer-">setType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the type property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html#setWorkingTimes-org.mpxj.edrawproject.schema.Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes-">setWorkingTimes</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a>&nbsp;value)</code>
<div class="block">Sets the value of the workingTimes property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="enteredByOccurrences">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enteredByOccurrences</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> enteredByOccurrences</pre>
</li>
</ul>
<a name="holiday">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>holiday</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> holiday</pre>
</li>
</ul>
<a name="timePeriod">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>timePeriod</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a> timePeriod</pre>
</li>
</ul>
<a name="occurrences">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>occurrences</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> occurrences</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="type">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>type</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> type</pre>
</li>
</ul>
<a name="dayWorking">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dayWorking</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> dayWorking</pre>
</li>
</ul>
<a name="workingTimes">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>workingTimes</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a> workingTimes</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Exception--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Exception</h4>
<pre>public&nbsp;Exception()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isEnteredByOccurrences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnteredByOccurrences</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isEnteredByOccurrences()</pre>
<div class="block">Gets the value of the enteredByOccurrences property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEnteredByOccurrences-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnteredByOccurrences</h4>
<pre>public&nbsp;void&nbsp;setEnteredByOccurrences(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the enteredByOccurrences property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isHoliday--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHoliday</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isHoliday()</pre>
<div class="block">Gets the value of the holiday property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHoliday-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHoliday</h4>
<pre>public&nbsp;void&nbsp;setHoliday(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the holiday property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a>&nbsp;getTimePeriod()</pre>
<div class="block">Gets the value of the timePeriod property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</code></a></dd>
</dl>
</li>
</ul>
<a name="setTimePeriod-org.mpxj.edrawproject.schema.Document.Calendars.Calendar.Exceptions.Exception.TimePeriod-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTimePeriod</h4>
<pre>public&nbsp;void&nbsp;setTimePeriod(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</a>&nbsp;value)</pre>
<div class="block">Sets the value of the timePeriod property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars.Calendar.Exceptions.Exception.TimePeriod</code></a></dd>
</dl>
</li>
</ul>
<a name="getOccurrences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOccurrences</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getOccurrences()</pre>
<div class="block">Gets the value of the occurrences property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOccurrences-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOccurrences</h4>
<pre>public&nbsp;void&nbsp;setOccurrences(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the occurrences property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getType()</pre>
<div class="block">Gets the value of the type property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setType-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setType</h4>
<pre>public&nbsp;void&nbsp;setType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the type property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDayWorking--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDayWorking</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDayWorking()</pre>
<div class="block">Gets the value of the dayWorking property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDayWorking-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDayWorking</h4>
<pre>public&nbsp;void&nbsp;setDayWorking(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dayWorking property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWorkingTimes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWorkingTimes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a>&nbsp;getWorkingTimes()</pre>
<div class="block">Gets the value of the workingTimes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</code></a></dd>
</dl>
</li>
</ul>
<a name="setWorkingTimes-org.mpxj.edrawproject.schema.Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setWorkingTimes</h4>
<pre>public&nbsp;void&nbsp;setWorkingTimes(<a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema">Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a>&nbsp;value)</pre>
<div class="block">Sets the value of the workingTimes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.edrawproject.schema"><code>Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Calendars.Calendar.Exceptions.Exception.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.Calendars.Calendar.Exceptions.Exception.html" target="_top">Frames</a></li>
<li><a href="Document.Calendars.Calendar.Exceptions.Exception.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
