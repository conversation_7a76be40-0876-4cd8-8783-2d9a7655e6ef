<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.GanttOption (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.GanttOption (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.GanttOption.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.GanttOption.html" target="_top">Frames</a></li>
<li><a href="Document.GanttOption.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.edrawproject.schema</div>
<h2 title="Class Document.GanttOption" class="title">Class Document.GanttOption</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.edrawproject.schema.Document.GanttOption</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/edrawproject/schema/Document.html" title="class in org.mpxj.edrawproject.schema">Document</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.GanttOption</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="MajorUnit"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="MinorUnit"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ProjectUnit"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="BaselineCost"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="StartDate"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="FinishDate"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Auto"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ThemeIndex"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#auto">auto</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#baselineCost">baselineCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#finishDate">finishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#majorUnit">majorUnit</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#minorUnit">minorUnit</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#projectUnit">projectUnit</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#themeIndex">themeIndex</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#GanttOption--">GanttOption</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getAuto--">getAuto</a></span>()</code>
<div class="block">Gets the value of the auto property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">Gets the value of the baselineCost property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getFinishDate--">getFinishDate</a></span>()</code>
<div class="block">Gets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getMajorUnit--">getMajorUnit</a></span>()</code>
<div class="block">Gets the value of the majorUnit property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getMinorUnit--">getMinorUnit</a></span>()</code>
<div class="block">Gets the value of the minorUnit property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getProjectUnit--">getProjectUnit</a></span>()</code>
<div class="block">Gets the value of the projectUnit property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#getThemeIndex--">getThemeIndex</a></span>()</code>
<div class="block">Gets the value of the themeIndex property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setAuto-org.mpxj.edrawproject.schema.Document.GanttOption.Auto-">setAuto</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a>&nbsp;value)</code>
<div class="block">Sets the value of the auto property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setBaselineCost-org.mpxj.edrawproject.schema.Document.GanttOption.BaselineCost-">setBaselineCost</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineCost property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setFinishDate-org.mpxj.edrawproject.schema.Document.GanttOption.FinishDate-">setFinishDate</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setMajorUnit-org.mpxj.edrawproject.schema.Document.GanttOption.MajorUnit-">setMajorUnit</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a>&nbsp;value)</code>
<div class="block">Sets the value of the majorUnit property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setMinorUnit-org.mpxj.edrawproject.schema.Document.GanttOption.MinorUnit-">setMinorUnit</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a>&nbsp;value)</code>
<div class="block">Sets the value of the minorUnit property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setProjectUnit-org.mpxj.edrawproject.schema.Document.GanttOption.ProjectUnit-">setProjectUnit</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectUnit property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setStartDate-org.mpxj.edrawproject.schema.Document.GanttOption.StartDate-">setStartDate</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.html#setThemeIndex-org.mpxj.edrawproject.schema.Document.GanttOption.ThemeIndex-">setThemeIndex</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a>&nbsp;value)</code>
<div class="block">Sets the value of the themeIndex property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="majorUnit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>majorUnit</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a> majorUnit</pre>
</li>
</ul>
<a name="minorUnit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minorUnit</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a> minorUnit</pre>
</li>
</ul>
<a name="projectUnit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectUnit</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a> projectUnit</pre>
</li>
</ul>
<a name="baselineCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineCost</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a> baselineCost</pre>
</li>
</ul>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a> startDate</pre>
</li>
</ul>
<a name="finishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishDate</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a> finishDate</pre>
</li>
</ul>
<a name="auto">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>auto</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a> auto</pre>
</li>
</ul>
<a name="themeIndex">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>themeIndex</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a> themeIndex</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="GanttOption--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>GanttOption</h4>
<pre>public&nbsp;GanttOption()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getMajorUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMajorUnit</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a>&nbsp;getMajorUnit()</pre>
<div class="block">Gets the value of the majorUnit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.MajorUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="setMajorUnit-org.mpxj.edrawproject.schema.Document.GanttOption.MajorUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMajorUnit</h4>
<pre>public&nbsp;void&nbsp;setMajorUnit(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MajorUnit</a>&nbsp;value)</pre>
<div class="block">Sets the value of the majorUnit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MajorUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.MajorUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="getMinorUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinorUnit</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a>&nbsp;getMinorUnit()</pre>
<div class="block">Gets the value of the minorUnit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.MinorUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="setMinorUnit-org.mpxj.edrawproject.schema.Document.GanttOption.MinorUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinorUnit</h4>
<pre>public&nbsp;void&nbsp;setMinorUnit(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.MinorUnit</a>&nbsp;value)</pre>
<div class="block">Sets the value of the minorUnit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.MinorUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.MinorUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectUnit</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a>&nbsp;getProjectUnit()</pre>
<div class="block">Gets the value of the projectUnit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.ProjectUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectUnit-org.mpxj.edrawproject.schema.Document.GanttOption.ProjectUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectUnit</h4>
<pre>public&nbsp;void&nbsp;setProjectUnit(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ProjectUnit</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectUnit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ProjectUnit.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.ProjectUnit</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a>&nbsp;getBaselineCost()</pre>
<div class="block">Gets the value of the baselineCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.BaselineCost</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-org.mpxj.edrawproject.schema.Document.GanttOption.BaselineCost-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.BaselineCost</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.BaselineCost.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.BaselineCost</code></a></dd>
</dl>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.StartDate</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-org.mpxj.edrawproject.schema.Document.GanttOption.StartDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.StartDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.StartDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.StartDate</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishDate</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a>&nbsp;getFinishDate()</pre>
<div class="block">Gets the value of the finishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.FinishDate</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinishDate-org.mpxj.edrawproject.schema.Document.GanttOption.FinishDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishDate</h4>
<pre>public&nbsp;void&nbsp;setFinishDate(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.FinishDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the finishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.FinishDate.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.FinishDate</code></a></dd>
</dl>
</li>
</ul>
<a name="getAuto--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAuto</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a>&nbsp;getAuto()</pre>
<div class="block">Gets the value of the auto property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.Auto</code></a></dd>
</dl>
</li>
</ul>
<a name="setAuto-org.mpxj.edrawproject.schema.Document.GanttOption.Auto-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAuto</h4>
<pre>public&nbsp;void&nbsp;setAuto(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.Auto</a>&nbsp;value)</pre>
<div class="block">Sets the value of the auto property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.Auto</code></a></dd>
</dl>
</li>
</ul>
<a name="getThemeIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThemeIndex</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a>&nbsp;getThemeIndex()</pre>
<div class="block">Gets the value of the themeIndex property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.ThemeIndex</code></a></dd>
</dl>
</li>
</ul>
<a name="setThemeIndex-org.mpxj.edrawproject.schema.Document.GanttOption.ThemeIndex-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setThemeIndex</h4>
<pre>public&nbsp;void&nbsp;setThemeIndex(<a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema">Document.GanttOption.ThemeIndex</a>&nbsp;value)</pre>
<div class="block">Sets the value of the themeIndex property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.ThemeIndex.html" title="class in org.mpxj.edrawproject.schema"><code>Document.GanttOption.ThemeIndex</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.GanttOption.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.DPi.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.GanttOption.Auto.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.GanttOption.html" target="_top">Frames</a></li>
<li><a href="Document.GanttOption.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
