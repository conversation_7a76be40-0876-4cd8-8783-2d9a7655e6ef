<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.TaskList.Task (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.TaskList.Task (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.TaskList.Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.TaskList.Task.html" target="_top">Frames</a></li>
<li><a href="Document.TaskList.Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.edrawproject.schema</div>
<h2 title="Class Document.TaskList.Task" class="title">Class Document.TaskList.Task</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.edrawproject.schema.Document.TaskList.Task</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.TaskList.Task</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="Format"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                 &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                 &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceList"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="Resource" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
                           &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="PredecessorLink" maxOccurs="unbounded"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
                   &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
                   &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="Texts"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="TextCell" maxOccurs="unbounded"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
                             &lt;element name="TextBlock"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="Character"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                               &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="Paragraph"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                               &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="WrapMode"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="FillColor"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="Color"&gt;
                                         &lt;complexType&gt;
                                           &lt;complexContent&gt;
                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                               &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                             &lt;/restriction&gt;
                                           &lt;/complexContent&gt;
                                         &lt;/complexType&gt;
                                       &lt;/element&gt;
                                     &lt;/sequence&gt;
                                     &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                     &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                                     &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                           &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                           &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                           &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
         &lt;element name="BarChart" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
       &lt;/sequence&gt;
       &lt;attribute name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="BackGroundColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="ProgressStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="BaseLinePointxEnd" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="m_Bestyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="RowHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="CriticalPath" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       &lt;attribute name="DateBaseStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="Resources" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="FontColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="DurationUnits" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="DateManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="m_bebartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="m_Istyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="BaseLinePointxBegin" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="HideByColumns" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Level" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="IsToggler" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="BaselineCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       &lt;attribute name="DurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       &lt;attribute name="ManualDurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       &lt;attribute name="DateLateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="LastSaveDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       &lt;attribute name="m_IbartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       &lt;attribute name="Work" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       &lt;attribute name="SplitOffsetDuration" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="DateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="RowID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       &lt;attribute name="FirstWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="DateLateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="CountWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="Priority" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="DateManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="DateBaseFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="ShowByChild" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="ActualMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="ParentID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="LateSlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="DateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       &lt;attribute name="Wbs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="BaseLineNumber" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       &lt;attribute name="HideID" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       &lt;attribute name="SplitPointList" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       &lt;attribute name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       &lt;attribute name="EarlySlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="Childs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.PredecessorLink</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#actualDuration">actualDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#actualFinish">actualFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#actualMilestone">actualMilestone</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#actualStart">actualStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#backGroundColor">backGroundColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#barChart">barChart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#baselineCost">baselineCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#baseLineNumber">baseLineNumber</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#baseLinePointxBegin">baseLinePointxBegin</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#baseLinePointxEnd">baseLinePointxEnd</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#childs">childs</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#cost">cost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#countWidth">countWidth</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#criticalPath">criticalPath</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateBaseFinish">dateBaseFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateBaseStart">dateBaseStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateFinish">dateFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateLateFinish">dateLateFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateLateStart">dateLateStart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateManualFinish">dateManualFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateManualStart">dateManualStart</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#dateStart">dateStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#durationSecs">durationSecs</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#durationUnits">durationUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#earlySlack">earlySlack</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#firstWidth">firstWidth</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#fontColor">fontColor</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#format">format</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#hideByColumns">hideByColumns</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#hideID">hideID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#id">id</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isToggler">isToggler</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#lastSaveDate">lastSaveDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#lateSlack">lateSlack</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#level">level</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#manual">manual</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#manualDurationSecs">manualDurationSecs</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#mBebartextIndex">mBebartextIndex</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#mBestyletext">mBestyletext</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#mIbartextIndex">mIbartextIndex</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#milestone">milestone</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#mIstyletext">mIstyletext</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#notes">notes</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#parentID">parentID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#percent">percent</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.PredecessorLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#predecessorLink">predecessorLink</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#priority">priority</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#progressStatus">progressStatus</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#remainingCost">remainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#resourceList">resourceList</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#resources">resources</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#rowHeight">rowHeight</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#rowID">rowID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#showByChild">showByChild</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#splitOffsetDuration">splitOffsetDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#splitPointList">splitPointList</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#startText">startText</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#texts">texts</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#wbs">wbs</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#work">work</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#Task--">Task</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getActualDuration--">getActualDuration</a></span>()</code>
<div class="block">Gets the value of the actualDuration property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getActualFinish--">getActualFinish</a></span>()</code>
<div class="block">Gets the value of the actualFinish property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getActualStart--">getActualStart</a></span>()</code>
<div class="block">Gets the value of the actualStart property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBackGroundColor--">getBackGroundColor</a></span>()</code>
<div class="block">Gets the value of the backGroundColor property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBarChart--">getBarChart</a></span>()</code>
<div class="block">Gets the value of the barChart property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBaselineCost--">getBaselineCost</a></span>()</code>
<div class="block">Gets the value of the baselineCost property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBaseLineNumber--">getBaseLineNumber</a></span>()</code>
<div class="block">Gets the value of the baseLineNumber property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBaseLinePointxBegin--">getBaseLinePointxBegin</a></span>()</code>
<div class="block">Gets the value of the baseLinePointxBegin property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getBaseLinePointxEnd--">getBaseLinePointxEnd</a></span>()</code>
<div class="block">Gets the value of the baseLinePointxEnd property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getChilds--">getChilds</a></span>()</code>
<div class="block">Gets the value of the childs property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getCost--">getCost</a></span>()</code>
<div class="block">Gets the value of the cost property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getCountWidth--">getCountWidth</a></span>()</code>
<div class="block">Gets the value of the countWidth property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateBaseFinish--">getDateBaseFinish</a></span>()</code>
<div class="block">Gets the value of the dateBaseFinish property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateBaseStart--">getDateBaseStart</a></span>()</code>
<div class="block">Gets the value of the dateBaseStart property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateFinish--">getDateFinish</a></span>()</code>
<div class="block">Gets the value of the dateFinish property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateLateFinish--">getDateLateFinish</a></span>()</code>
<div class="block">Gets the value of the dateLateFinish property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateLateStart--">getDateLateStart</a></span>()</code>
<div class="block">Gets the value of the dateLateStart property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateManualFinish--">getDateManualFinish</a></span>()</code>
<div class="block">Gets the value of the dateManualFinish property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateManualStart--">getDateManualStart</a></span>()</code>
<div class="block">Gets the value of the dateManualStart property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDateStart--">getDateStart</a></span>()</code>
<div class="block">Gets the value of the dateStart property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDurationSecs--">getDurationSecs</a></span>()</code>
<div class="block">Gets the value of the durationSecs property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getDurationUnits--">getDurationUnits</a></span>()</code>
<div class="block">Gets the value of the durationUnits property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getEarlySlack--">getEarlySlack</a></span>()</code>
<div class="block">Gets the value of the earlySlack property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getFirstWidth--">getFirstWidth</a></span>()</code>
<div class="block">Gets the value of the firstWidth property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getFontColor--">getFontColor</a></span>()</code>
<div class="block">Gets the value of the fontColor property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getFormat--">getFormat</a></span>()</code>
<div class="block">Gets the value of the format property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getHideByColumns--">getHideByColumns</a></span>()</code>
<div class="block">Gets the value of the hideByColumns property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getID--">getID</a></span>()</code>
<div class="block">Gets the value of the id property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getLastSaveDate--">getLastSaveDate</a></span>()</code>
<div class="block">Gets the value of the lastSaveDate property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getLateSlack--">getLateSlack</a></span>()</code>
<div class="block">Gets the value of the lateSlack property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Gets the value of the level property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getManualDurationSecs--">getManualDurationSecs</a></span>()</code>
<div class="block">Gets the value of the manualDurationSecs property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getMBebartextIndex--">getMBebartextIndex</a></span>()</code>
<div class="block">Gets the value of the mBebartextIndex property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getMBestyletext--">getMBestyletext</a></span>()</code>
<div class="block">Gets the value of the mBestyletext property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getMIbartextIndex--">getMIbartextIndex</a></span>()</code>
<div class="block">Gets the value of the mIbartextIndex property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getMIstyletext--">getMIstyletext</a></span>()</code>
<div class="block">Gets the value of the mIstyletext property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getNotes--">getNotes</a></span>()</code>
<div class="block">Gets the value of the notes property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getParentID--">getParentID</a></span>()</code>
<div class="block">Gets the value of the parentID property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getPercent--">getPercent</a></span>()</code>
<div class="block">Gets the value of the percent property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.PredecessorLink</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getPredecessorLink--">getPredecessorLink</a></span>()</code>
<div class="block">Gets the value of the predecessorLink property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getPriority--">getPriority</a></span>()</code>
<div class="block">Gets the value of the priority property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getProgressStatus--">getProgressStatus</a></span>()</code>
<div class="block">Gets the value of the progressStatus property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Gets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getResourceList--">getResourceList</a></span>()</code>
<div class="block">Gets the value of the resourceList property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getResources--">getResources</a></span>()</code>
<div class="block">Gets the value of the resources property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getRowHeight--">getRowHeight</a></span>()</code>
<div class="block">Gets the value of the rowHeight property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getRowID--">getRowID</a></span>()</code>
<div class="block">Gets the value of the rowID property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getShowByChild--">getShowByChild</a></span>()</code>
<div class="block">Gets the value of the showByChild property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getSplitOffsetDuration--">getSplitOffsetDuration</a></span>()</code>
<div class="block">Gets the value of the splitOffsetDuration property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getSplitPointList--">getSplitPointList</a></span>()</code>
<div class="block">Gets the value of the splitPointList property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getStartText--">getStartText</a></span>()</code>
<div class="block">Gets the value of the startText property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getTexts--">getTexts</a></span>()</code>
<div class="block">Gets the value of the texts property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getWbs--">getWbs</a></span>()</code>
<div class="block">Gets the value of the wbs property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#getWork--">getWork</a></span>()</code>
<div class="block">Gets the value of the work property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isActualMilestone--">isActualMilestone</a></span>()</code>
<div class="block">Gets the value of the actualMilestone property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isCriticalPath--">isCriticalPath</a></span>()</code>
<div class="block">Gets the value of the criticalPath property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isHideID--">isHideID</a></span>()</code>
<div class="block">Gets the value of the hideID property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isIsToggler--">isIsToggler</a></span>()</code>
<div class="block">Gets the value of the isToggler property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isManual--">isManual</a></span>()</code>
<div class="block">Gets the value of the manual property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#isMilestone--">isMilestone</a></span>()</code>
<div class="block">Gets the value of the milestone property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setActualDuration-java.lang.Long-">setActualDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualDuration property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setActualFinish-java.lang.Long-">setActualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualFinish property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setActualMilestone-java.lang.Boolean-">setActualMilestone</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualMilestone property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setActualStart-java.lang.Long-">setActualStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualStart property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBackGroundColor-java.lang.String-">setBackGroundColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the backGroundColor property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBarChart-java.lang.Object-">setBarChart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Sets the value of the barChart property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBaselineCost-java.lang.Double-">setBaselineCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselineCost property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBaseLineNumber-java.lang.Integer-">setBaseLineNumber</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseLineNumber property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBaseLinePointxBegin-java.lang.Integer-">setBaseLinePointxBegin</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseLinePointxBegin property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setBaseLinePointxEnd-java.lang.Integer-">setBaseLinePointxEnd</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the baseLinePointxEnd property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setChilds-java.lang.String-">setChilds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the childs property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setCost-java.lang.Double-">setCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cost property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setCountWidth-java.lang.Integer-">setCountWidth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the countWidth property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setCriticalPath-java.lang.Boolean-">setCriticalPath</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the criticalPath property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateBaseFinish-java.time.LocalDateTime-">setDateBaseFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateBaseFinish property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateBaseStart-java.time.LocalDateTime-">setDateBaseStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateBaseStart property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateFinish-java.time.LocalDateTime-">setDateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateFinish property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateLateFinish-java.time.LocalDateTime-">setDateLateFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateLateFinish property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateLateStart-java.time.LocalDateTime-">setDateLateStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateLateStart property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateManualFinish-java.time.LocalDateTime-">setDateManualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateManualFinish property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateManualStart-java.time.LocalDateTime-">setDateManualStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateManualStart property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDateStart-java.time.LocalDateTime-">setDateStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the dateStart property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDurationSecs-java.lang.Long-">setDurationSecs</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</code>
<div class="block">Sets the value of the durationSecs property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setDurationUnits-java.lang.Integer-">setDurationUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the durationUnits property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setEarlySlack-java.lang.String-">setEarlySlack</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the earlySlack property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setFirstWidth-java.lang.Integer-">setFirstWidth</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the firstWidth property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setFontColor-java.lang.String-">setFontColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the fontColor property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setFormat-org.mpxj.edrawproject.schema.Document.TaskList.Task.Format-">setFormat</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a>&nbsp;value)</code>
<div class="block">Sets the value of the format property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setHideByColumns-java.lang.String-">setHideByColumns</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the hideByColumns property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setHideID-java.lang.Boolean-">setHideID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the hideID property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setID-java.lang.Integer-">setID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the id property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setIsToggler-java.lang.Boolean-">setIsToggler</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isToggler property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setLastSaveDate-javax.xml.datatype.XMLGregorianCalendar-">setLastSaveDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastSaveDate property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setLateSlack-java.lang.String-">setLateSlack</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the lateSlack property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setLevel-java.lang.Integer-">setLevel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the level property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setManual-java.lang.Boolean-">setManual</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the manual property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setManualDurationSecs-java.lang.Long-">setManualDurationSecs</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</code>
<div class="block">Sets the value of the manualDurationSecs property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setMBebartextIndex-java.lang.String-">setMBebartextIndex</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the mBebartextIndex property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setMBestyletext-java.lang.String-">setMBestyletext</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the mBestyletext property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setMIbartextIndex-java.lang.String-">setMIbartextIndex</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the mIbartextIndex property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setMilestone-java.lang.Boolean-">setMilestone</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the milestone property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setMIstyletext-java.lang.String-">setMIstyletext</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the mIstyletext property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setNotes-java.lang.String-">setNotes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the notes property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setParentID-java.lang.Integer-">setParentID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the parentID property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setPercent-java.lang.Double-">setPercent</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the percent property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setPriority-java.lang.Integer-">setPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the priority property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setProgressStatus-java.lang.Integer-">setProgressStatus</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the progressStatus property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setRemainingCost-java.lang.Double-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setResourceList-org.mpxj.edrawproject.schema.Document.TaskList.Task.ResourceList-">setResourceList</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceList property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setResources-java.lang.String-">setResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resources property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setRowHeight-java.lang.Integer-">setRowHeight</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the rowHeight property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setRowID-java.lang.Integer-">setRowID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the rowID property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setShowByChild-java.lang.Integer-">setShowByChild</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the showByChild property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setSplitOffsetDuration-java.lang.String-">setSplitOffsetDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the splitOffsetDuration property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setSplitPointList-java.lang.String-">setSplitPointList</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the splitPointList property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setStartText-java.lang.String-">setStartText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the startText property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setTexts-org.mpxj.edrawproject.schema.Document.TaskList.Task.Texts-">setTexts</a></span>(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a>&nbsp;value)</code>
<div class="block">Sets the value of the texts property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setWbs-java.lang.String-">setWbs</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the wbs property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.html#setWork-java.lang.Double-">setWork</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the work property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="format">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>format</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a> format</pre>
</li>
</ul>
<a name="resourceList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceList</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a> resourceList</pre>
</li>
</ul>
<a name="predecessorLink">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>predecessorLink</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.PredecessorLink</a>&gt; predecessorLink</pre>
</li>
</ul>
<a name="texts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>texts</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a> texts</pre>
</li>
</ul>
<a name="barChart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>barChart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> barChart</pre>
</li>
</ul>
<a name="milestone">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>milestone</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> milestone</pre>
</li>
</ul>
<a name="backGroundColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>backGroundColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> backGroundColor</pre>
</li>
</ul>
<a name="progressStatus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>progressStatus</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> progressStatus</pre>
</li>
</ul>
<a name="baseLinePointxEnd">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseLinePointxEnd</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> baseLinePointxEnd</pre>
</li>
</ul>
<a name="mBestyletext">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mBestyletext</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mBestyletext</pre>
</li>
</ul>
<a name="rowHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rowHeight</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> rowHeight</pre>
</li>
</ul>
<a name="criticalPath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>criticalPath</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> criticalPath</pre>
</li>
</ul>
<a name="id">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>id</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> id</pre>
</li>
</ul>
<a name="actualDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a> actualDuration</pre>
</li>
</ul>
<a name="dateBaseStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateBaseStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateBaseStart</pre>
</li>
</ul>
<a name="resources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resources</pre>
</li>
</ul>
<a name="fontColor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fontColor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> fontColor</pre>
</li>
</ul>
<a name="durationUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>durationUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> durationUnits</pre>
</li>
</ul>
<a name="dateManualFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateManualFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateManualFinish</pre>
</li>
</ul>
<a name="mBebartextIndex">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mBebartextIndex</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mBebartextIndex</pre>
</li>
</ul>
<a name="mIstyletext">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mIstyletext</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mIstyletext</pre>
</li>
</ul>
<a name="baseLinePointxBegin">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseLinePointxBegin</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> baseLinePointxBegin</pre>
</li>
</ul>
<a name="manual">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manual</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> manual</pre>
</li>
</ul>
<a name="hideByColumns">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hideByColumns</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> hideByColumns</pre>
</li>
</ul>
<a name="level">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>level</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> level</pre>
</li>
</ul>
<a name="isToggler">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isToggler</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isToggler</pre>
</li>
</ul>
<a name="baselineCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselineCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselineCost</pre>
</li>
</ul>
<a name="durationSecs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>durationSecs</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a> durationSecs</pre>
</li>
</ul>
<a name="manualDurationSecs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manualDurationSecs</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a> manualDurationSecs</pre>
</li>
</ul>
<a name="dateLateStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateLateStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateLateStart</pre>
</li>
</ul>
<a name="lastSaveDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastSaveDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a> lastSaveDate</pre>
</li>
</ul>
<a name="mIbartextIndex">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mIbartextIndex</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mIbartextIndex</pre>
</li>
</ul>
<a name="actualStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a> actualStart</pre>
</li>
</ul>
<a name="work">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>work</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> work</pre>
</li>
</ul>
<a name="cost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cost</pre>
</li>
</ul>
<a name="splitOffsetDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>splitOffsetDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> splitOffsetDuration</pre>
</li>
</ul>
<a name="dateStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateStart</pre>
</li>
</ul>
<a name="rowID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rowID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> rowID</pre>
</li>
</ul>
<a name="startText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startText</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> startText</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="actualFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a> actualFinish</pre>
</li>
</ul>
<a name="firstWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firstWidth</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> firstWidth</pre>
</li>
</ul>
<a name="dateLateFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateLateFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateLateFinish</pre>
</li>
</ul>
<a name="countWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>countWidth</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> countWidth</pre>
</li>
</ul>
<a name="priority">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priority</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> priority</pre>
</li>
</ul>
<a name="dateManualStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateManualStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateManualStart</pre>
</li>
</ul>
<a name="dateBaseFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateBaseFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateBaseFinish</pre>
</li>
</ul>
<a name="showByChild">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showByChild</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> showByChild</pre>
</li>
</ul>
<a name="actualMilestone">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualMilestone</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> actualMilestone</pre>
</li>
</ul>
<a name="parentID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parentID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> parentID</pre>
</li>
</ul>
<a name="lateSlack">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lateSlack</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> lateSlack</pre>
</li>
</ul>
<a name="dateFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dateFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> dateFinish</pre>
</li>
</ul>
<a name="wbs">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wbs</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> wbs</pre>
</li>
</ul>
<a name="notes">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>notes</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> notes</pre>
</li>
</ul>
<a name="baseLineNumber">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baseLineNumber</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> baseLineNumber</pre>
</li>
</ul>
<a name="hideID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hideID</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> hideID</pre>
</li>
</ul>
<a name="splitPointList">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>splitPointList</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> splitPointList</pre>
</li>
</ul>
<a name="percent">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>percent</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> percent</pre>
</li>
</ul>
<a name="remainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingCost</pre>
</li>
</ul>
<a name="earlySlack">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earlySlack</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> earlySlack</pre>
</li>
</ul>
<a name="childs">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>childs</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> childs</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Task--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Task</h4>
<pre>public&nbsp;Task()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormat</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a>&nbsp;getFormat()</pre>
<div class="block">Gets the value of the format property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.Format</code></a></dd>
</dl>
</li>
</ul>
<a name="setFormat-org.mpxj.edrawproject.schema.Document.TaskList.Task.Format-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFormat</h4>
<pre>public&nbsp;void&nbsp;setFormat(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Format</a>&nbsp;value)</pre>
<div class="block">Sets the value of the format property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.Format</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceList</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a>&nbsp;getResourceList()</pre>
<div class="block">Gets the value of the resourceList property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.ResourceList</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceList-org.mpxj.edrawproject.schema.Document.TaskList.Task.ResourceList-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceList</h4>
<pre>public&nbsp;void&nbsp;setResourceList(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.ResourceList</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceList property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.ResourceList.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.ResourceList</code></a></dd>
</dl>
</li>
</ul>
<a name="getPredecessorLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPredecessorLink</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.PredecessorLink</a>&gt;&nbsp;getPredecessorLink()</pre>
<div class="block">Gets the value of the predecessorLink property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the predecessorLink property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getPredecessorLink().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.PredecessorLink.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.PredecessorLink</code></a></div>
</li>
</ul>
<a name="getTexts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTexts</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a>&nbsp;getTexts()</pre>
<div class="block">Gets the value of the texts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.Texts</code></a></dd>
</dl>
</li>
</ul>
<a name="setTexts-org.mpxj.edrawproject.schema.Document.TaskList.Task.Texts-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTexts</h4>
<pre>public&nbsp;void&nbsp;setTexts(<a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema">Document.TaskList.Task.Texts</a>&nbsp;value)</pre>
<div class="block">Sets the value of the texts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Texts.html" title="class in org.mpxj.edrawproject.schema"><code>Document.TaskList.Task.Texts</code></a></dd>
</dl>
</li>
</ul>
<a name="getBarChart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBarChart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getBarChart()</pre>
<div class="block">Gets the value of the barChart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><code>Object</code></a></dd>
</dl>
</li>
</ul>
<a name="setBarChart-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBarChart</h4>
<pre>public&nbsp;void&nbsp;setBarChart(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Sets the value of the barChart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><code>Object</code></a></dd>
</dl>
</li>
</ul>
<a name="isMilestone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMilestone</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isMilestone()</pre>
<div class="block">Gets the value of the milestone property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMilestone-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMilestone</h4>
<pre>public&nbsp;void&nbsp;setMilestone(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the milestone property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBackGroundColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackGroundColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getBackGroundColor()</pre>
<div class="block">Gets the value of the backGroundColor property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBackGroundColor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackGroundColor</h4>
<pre>public&nbsp;void&nbsp;setBackGroundColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the backGroundColor property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProgressStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProgressStatus</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getProgressStatus()</pre>
<div class="block">Gets the value of the progressStatus property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProgressStatus-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProgressStatus</h4>
<pre>public&nbsp;void&nbsp;setProgressStatus(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the progressStatus property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseLinePointxEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseLinePointxEnd</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaseLinePointxEnd()</pre>
<div class="block">Gets the value of the baseLinePointxEnd property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseLinePointxEnd-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseLinePointxEnd</h4>
<pre>public&nbsp;void&nbsp;setBaseLinePointxEnd(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseLinePointxEnd property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMBestyletext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMBestyletext</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMBestyletext()</pre>
<div class="block">Gets the value of the mBestyletext property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMBestyletext-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMBestyletext</h4>
<pre>public&nbsp;void&nbsp;setMBestyletext(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mBestyletext property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRowHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRowHeight</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRowHeight()</pre>
<div class="block">Gets the value of the rowHeight property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRowHeight-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRowHeight</h4>
<pre>public&nbsp;void&nbsp;setRowHeight(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rowHeight property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isCriticalPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCriticalPath</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isCriticalPath()</pre>
<div class="block">Gets the value of the criticalPath property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCriticalPath-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCriticalPath</h4>
<pre>public&nbsp;void&nbsp;setCriticalPath(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the criticalPath property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getID()</pre>
<div class="block">Gets the value of the id property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setID</h4>
<pre>public&nbsp;void&nbsp;setID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the id property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getActualDuration()</pre>
<div class="block">Gets the value of the actualDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualDuration-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualDuration</h4>
<pre>public&nbsp;void&nbsp;setActualDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateBaseStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateBaseStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateBaseStart()</pre>
<div class="block">Gets the value of the dateBaseStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateBaseStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateBaseStart</h4>
<pre>public&nbsp;void&nbsp;setDateBaseStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateBaseStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResources</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResources()</pre>
<div class="block">Gets the value of the resources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResources-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResources</h4>
<pre>public&nbsp;void&nbsp;setResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFontColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontColor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFontColor()</pre>
<div class="block">Gets the value of the fontColor property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFontColor-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFontColor</h4>
<pre>public&nbsp;void&nbsp;setFontColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the fontColor property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDurationUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getDurationUnits()</pre>
<div class="block">Gets the value of the durationUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDurationUnits-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationUnits</h4>
<pre>public&nbsp;void&nbsp;setDurationUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the durationUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateManualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateManualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateManualFinish()</pre>
<div class="block">Gets the value of the dateManualFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateManualFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateManualFinish</h4>
<pre>public&nbsp;void&nbsp;setDateManualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateManualFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMBebartextIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMBebartextIndex</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMBebartextIndex()</pre>
<div class="block">Gets the value of the mBebartextIndex property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMBebartextIndex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMBebartextIndex</h4>
<pre>public&nbsp;void&nbsp;setMBebartextIndex(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mBebartextIndex property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getMIstyletext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMIstyletext</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMIstyletext()</pre>
<div class="block">Gets the value of the mIstyletext property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMIstyletext-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMIstyletext</h4>
<pre>public&nbsp;void&nbsp;setMIstyletext(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mIstyletext property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseLinePointxBegin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseLinePointxBegin</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaseLinePointxBegin()</pre>
<div class="block">Gets the value of the baseLinePointxBegin property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseLinePointxBegin-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseLinePointxBegin</h4>
<pre>public&nbsp;void&nbsp;setBaseLinePointxBegin(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseLinePointxBegin property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isManual--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isManual</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isManual()</pre>
<div class="block">Gets the value of the manual property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setManual-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManual</h4>
<pre>public&nbsp;void&nbsp;setManual(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the manual property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getHideByColumns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideByColumns</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHideByColumns()</pre>
<div class="block">Gets the value of the hideByColumns property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHideByColumns-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHideByColumns</h4>
<pre>public&nbsp;void&nbsp;setHideByColumns(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hideByColumns property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getLevel()</pre>
<div class="block">Gets the value of the level property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLevel-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the level property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsToggler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsToggler</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsToggler()</pre>
<div class="block">Gets the value of the isToggler property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsToggler-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsToggler</h4>
<pre>public&nbsp;void&nbsp;setIsToggler(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isToggler property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselineCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselineCost()</pre>
<div class="block">Gets the value of the baselineCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselineCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineCost</h4>
<pre>public&nbsp;void&nbsp;setBaselineCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselineCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDurationSecs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationSecs</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getDurationSecs()</pre>
<div class="block">Gets the value of the durationSecs property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="setDurationSecs-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationSecs</h4>
<pre>public&nbsp;void&nbsp;setDurationSecs(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</pre>
<div class="block">Sets the value of the durationSecs property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="getManualDurationSecs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManualDurationSecs</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getManualDurationSecs()</pre>
<div class="block">Gets the value of the manualDurationSecs property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="setManualDurationSecs-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManualDurationSecs</h4>
<pre>public&nbsp;void&nbsp;setManualDurationSecs(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</pre>
<div class="block">Sets the value of the manualDurationSecs property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateLateStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateLateStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateLateStart()</pre>
<div class="block">Gets the value of the dateLateStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateLateStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateLateStart</h4>
<pre>public&nbsp;void&nbsp;setDateLateStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateLateStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastSaveDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSaveDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a>&nbsp;getLastSaveDate()</pre>
<div class="block">Gets the value of the lastSaveDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype"><code>XMLGregorianCalendar</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastSaveDate-javax.xml.datatype.XMLGregorianCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastSaveDate</h4>
<pre>public&nbsp;void&nbsp;setLastSaveDate(<a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype">XMLGregorianCalendar</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastSaveDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/javax/xml/datatype/XMLGregorianCalendar.html?is-external=true" title="class or interface in javax.xml.datatype"><code>XMLGregorianCalendar</code></a></dd>
</dl>
</li>
</ul>
<a name="getMIbartextIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMIbartextIndex</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMIbartextIndex()</pre>
<div class="block">Gets the value of the mIbartextIndex property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMIbartextIndex-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMIbartextIndex</h4>
<pre>public&nbsp;void&nbsp;setMIbartextIndex(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mIbartextIndex property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getActualStart()</pre>
<div class="block">Gets the value of the actualStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualStart-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualStart</h4>
<pre>public&nbsp;void&nbsp;setActualStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="getWork--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWork</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getWork()</pre>
<div class="block">Gets the value of the work property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWork-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWork</h4>
<pre>public&nbsp;void&nbsp;setWork(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the work property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCost()</pre>
<div class="block">Gets the value of the cost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCost</h4>
<pre>public&nbsp;void&nbsp;setCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getSplitOffsetDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSplitOffsetDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSplitOffsetDuration()</pre>
<div class="block">Gets the value of the splitOffsetDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSplitOffsetDuration-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplitOffsetDuration</h4>
<pre>public&nbsp;void&nbsp;setSplitOffsetDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the splitOffsetDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateStart()</pre>
<div class="block">Gets the value of the dateStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateStart</h4>
<pre>public&nbsp;void&nbsp;setDateStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRowID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRowID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRowID()</pre>
<div class="block">Gets the value of the rowID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRowID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRowID</h4>
<pre>public&nbsp;void&nbsp;setRowID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rowID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStartText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartText</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getStartText()</pre>
<div class="block">Gets the value of the startText property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartText</h4>
<pre>public&nbsp;void&nbsp;setStartText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startText property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getActualFinish()</pre>
<div class="block">Gets the value of the actualFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualFinish-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualFinish</h4>
<pre>public&nbsp;void&nbsp;setActualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang"><code>Long</code></a></dd>
</dl>
</li>
</ul>
<a name="getFirstWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirstWidth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFirstWidth()</pre>
<div class="block">Gets the value of the firstWidth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFirstWidth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFirstWidth</h4>
<pre>public&nbsp;void&nbsp;setFirstWidth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the firstWidth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateLateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateLateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateLateFinish()</pre>
<div class="block">Gets the value of the dateLateFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateLateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateLateFinish</h4>
<pre>public&nbsp;void&nbsp;setDateLateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateLateFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCountWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCountWidth</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCountWidth()</pre>
<div class="block">Gets the value of the countWidth property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCountWidth-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCountWidth</h4>
<pre>public&nbsp;void&nbsp;setCountWidth(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the countWidth property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPriority--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriority</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getPriority()</pre>
<div class="block">Gets the value of the priority property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPriority-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriority</h4>
<pre>public&nbsp;void&nbsp;setPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the priority property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateManualStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateManualStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateManualStart()</pre>
<div class="block">Gets the value of the dateManualStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateManualStart-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateManualStart</h4>
<pre>public&nbsp;void&nbsp;setDateManualStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateManualStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateBaseFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateBaseFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateBaseFinish()</pre>
<div class="block">Gets the value of the dateBaseFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateBaseFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateBaseFinish</h4>
<pre>public&nbsp;void&nbsp;setDateBaseFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateBaseFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getShowByChild--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowByChild</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getShowByChild()</pre>
<div class="block">Gets the value of the showByChild property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setShowByChild-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowByChild</h4>
<pre>public&nbsp;void&nbsp;setShowByChild(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the showByChild property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isActualMilestone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActualMilestone</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isActualMilestone()</pre>
<div class="block">Gets the value of the actualMilestone property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualMilestone-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualMilestone</h4>
<pre>public&nbsp;void&nbsp;setActualMilestone(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualMilestone property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getParentID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getParentID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getParentID()</pre>
<div class="block">Gets the value of the parentID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setParentID-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParentID</h4>
<pre>public&nbsp;void&nbsp;setParentID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the parentID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLateSlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLateSlack</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLateSlack()</pre>
<div class="block">Gets the value of the lateSlack property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLateSlack-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLateSlack</h4>
<pre>public&nbsp;void&nbsp;setLateSlack(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lateSlack property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDateFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDateFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getDateFinish()</pre>
<div class="block">Gets the value of the dateFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDateFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDateFinish</h4>
<pre>public&nbsp;void&nbsp;setDateFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the dateFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWbs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWbs</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWbs()</pre>
<div class="block">Gets the value of the wbs property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWbs-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWbs</h4>
<pre>public&nbsp;void&nbsp;setWbs(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the wbs property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getNotes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNotes</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getNotes()</pre>
<div class="block">Gets the value of the notes property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setNotes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNotes</h4>
<pre>public&nbsp;void&nbsp;setNotes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the notes property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaseLineNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaseLineNumber</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getBaseLineNumber()</pre>
<div class="block">Gets the value of the baseLineNumber property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaseLineNumber-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaseLineNumber</h4>
<pre>public&nbsp;void&nbsp;setBaseLineNumber(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baseLineNumber property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isHideID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHideID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isHideID()</pre>
<div class="block">Gets the value of the hideID property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHideID-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHideID</h4>
<pre>public&nbsp;void&nbsp;setHideID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hideID property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getSplitPointList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSplitPointList</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSplitPointList()</pre>
<div class="block">Gets the value of the splitPointList property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setSplitPointList-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplitPointList</h4>
<pre>public&nbsp;void&nbsp;setSplitPointList(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the splitPointList property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPercent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercent</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPercent()</pre>
<div class="block">Gets the value of the percent property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPercent-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercent</h4>
<pre>public&nbsp;void&nbsp;setPercent(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the percent property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingCost()</pre>
<div class="block">Gets the value of the remainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarlySlack--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarlySlack</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEarlySlack()</pre>
<div class="block">Gets the value of the earlySlack property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarlySlack-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarlySlack</h4>
<pre>public&nbsp;void&nbsp;setEarlySlack(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earlySlack property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getChilds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChilds</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getChilds()</pre>
<div class="block">Gets the value of the childs property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setChilds-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setChilds</h4>
<pre>public&nbsp;void&nbsp;setChilds(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the childs property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.TaskList.Task.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/edrawproject/schema/Document.TaskList.Task.Format.html" title="class in org.mpxj.edrawproject.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/edrawproject/schema/Document.TaskList.Task.html" target="_top">Frames</a></li>
<li><a href="Document.TaskList.Task.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
