<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Priority (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Priority (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Priority.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Priority.html" target="_top">Frames</a></li>
<li><a href="Priority.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class Priority" class="title">Class Priority</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.Priority</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Priority</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class is used to represent a priority. It provides a mapping
 between the textual description of a priority found in an MPX
 file, and an enumerated representation that can be more easily manipulated
 programmatically.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#DO_NOT_LEVEL">DO_NOT_LEVEL</a></span></code>
<div class="block">Constant for do not level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#HIGH">HIGH</a></span></code>
<div class="block">Constant for high priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#HIGHER">HIGHER</a></span></code>
<div class="block">Constant for higher priority.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#HIGHEST">HIGHEST</a></span></code>
<div class="block">Constant for highest priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#LOW">LOW</a></span></code>
<div class="block">Constant for low priority.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#LOWER">LOWER</a></span></code>
<div class="block">Constant for lower priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#LOWEST">LOWEST</a></span></code>
<div class="block">Constant for lowest priority.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#MEDIUM">MEDIUM</a></span></code>
<div class="block">Constant for medium priority.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#VERY_HIGH">VERY_HIGH</a></span></code>
<div class="block">Constant for very high priority.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#VERY_LOW">VERY_LOW</a></span></code>
<div class="block">Constant for low priority.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#getInstance-int-">getInstance</a></span>(int&nbsp;priority)</code>
<div class="block">This method takes an integer enumeration of a priority
 and returns an appropriate instance of this class.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#getValue--">getValue</a></span>()</code>
<div class="block">Accessor method used to retrieve the numeric representation of the
 priority.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Priority.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="LOWEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOWEST</h4>
<pre>public static final&nbsp;int LOWEST</pre>
<div class="block">Constant for lowest priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.LOWEST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERY_LOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERY_LOW</h4>
<pre>public static final&nbsp;int VERY_LOW</pre>
<div class="block">Constant for low priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.VERY_LOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOWER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOWER</h4>
<pre>public static final&nbsp;int LOWER</pre>
<div class="block">Constant for lower priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.LOWER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOW</h4>
<pre>public static final&nbsp;int LOW</pre>
<div class="block">Constant for low priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.LOW">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MEDIUM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MEDIUM</h4>
<pre>public static final&nbsp;int MEDIUM</pre>
<div class="block">Constant for medium priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.MEDIUM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HIGH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIGH</h4>
<pre>public static final&nbsp;int HIGH</pre>
<div class="block">Constant for high priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.HIGH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HIGHER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIGHER</h4>
<pre>public static final&nbsp;int HIGHER</pre>
<div class="block">Constant for higher priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.HIGHER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VERY_HIGH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VERY_HIGH</h4>
<pre>public static final&nbsp;int VERY_HIGH</pre>
<div class="block">Constant for very high priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.VERY_HIGH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HIGHEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIGHEST</h4>
<pre>public static final&nbsp;int HIGHEST</pre>
<div class="block">Constant for highest priority.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.HIGHEST">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DO_NOT_LEVEL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DO_NOT_LEVEL</h4>
<pre>public static final&nbsp;int DO_NOT_LEVEL</pre>
<div class="block">Constant for do not level.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Priority.DO_NOT_LEVEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../org/mpxj/Priority.html" title="class in org.mpxj">Priority</a>&nbsp;getInstance(int&nbsp;priority)</pre>
<div class="block">This method takes an integer enumeration of a priority
 and returns an appropriate instance of this class. Note that unrecognised
 values are treated as medium priority.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>priority</code> - int version of the priority</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Priority class instance</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block">Accessor method used to retrieve the numeric representation of the
 priority.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>int representation of the priority</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Priority.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/PercentCompleteType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Priority.html" target="_top">Frames</a></li>
<li><a href="Priority.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
