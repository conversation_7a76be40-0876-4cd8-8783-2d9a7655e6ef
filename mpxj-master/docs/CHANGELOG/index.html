
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
      
      
        <link rel="canonical" href="http://www.mpxj.org/CHANGELOG/">
      
      
        <link rel="prev" href="..">
      
      
        <link rel="next" href="../support/">
      
      
      <link rel="icon" href="../images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.0, mkdocs-material-9.5.20">
    
    
      
        <title>Changes - MPXJ</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.66ac8b77.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce((e,_)=>(e<<5)-e+_.charCodeAt(0),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      
  


  
  

<script id="__analytics">function __md_analytics(){function n(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],n("js",new Date),n("config","G-9R48LPVHKE"),document.addEventListener("DOMContentLoaded",function(){document.forms.search&&document.forms.search.query.addEventListener("blur",function(){this.value&&n("event","search",{search_term:this.value})}),document$.subscribe(function(){var a=document.forms.feedback;if(void 0!==a)for(var e of a.querySelectorAll("[type=submit]"))e.addEventListener("click",function(e){e.preventDefault();var t=document.location.pathname,e=this.getAttribute("data-md-value");n("event","feedback",{page:t,data:e}),a.firstElementChild.disabled=!0;e=a.querySelector(".md-feedback__note [data-md-value='"+e+"']");e&&(e.hidden=!1)}),a.hidden=!1}),location$.subscribe(function(e){n("config","G-9R48LPVHKE",{page_path:e.pathname})})});var e=document.createElement("script");e.async=!0,e.src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE",document.getElementById("__analytics").insertAdjacentElement("afterEnd",e)}</script>
  
    <script>"undefined"!=typeof __md_analytics&&__md_analytics()</script>
  

    
    
    
  </head>
  
  
    <body dir="ltr">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#changelog" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="MPXJ" class="md-header__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MPXJ
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Changes
            
          </span>
        </div>
      </div>
    </div>
    
    
      <script>var media,input,key,value,palette=__md_get("__palette");if(palette&&palette.color){"(prefers-color-scheme)"===palette.color.media&&(media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']"),palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent"));for([key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12Z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="MPXJ" class="md-nav__button md-logo" aria-label="MPXJ" data-md-component="logo">
      
  <img src="../images/mpxj-white.svg" alt="logo">

    </a>
    MPXJ
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/joniles/mpxj" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    mpxj
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Introduction
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    Changes
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    Changes
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#note" class="md-nav__link">
    <span class="md-ellipsis">
      NOTE
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1420-unreleased" class="md-nav__link">
    <span class="md-ellipsis">
      14.2.0 (unreleased)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1410-2025-06-05" class="md-nav__link">
    <span class="md-ellipsis">
      14.1.0 (2025-06-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1400-2025-05-07" class="md-nav__link">
    <span class="md-ellipsis">
      14.0.0 (2025-05-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13120-2025-04-09" class="md-nav__link">
    <span class="md-ellipsis">
      13.12.0 (2025-04-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13110-2025-03-10" class="md-nav__link">
    <span class="md-ellipsis">
      13.11.0 (2025-03-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13100-2025-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      13.10.0 (2025-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1390-2025-01-09" class="md-nav__link">
    <span class="md-ellipsis">
      13.9.0 (2025-01-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1380-2024-12-17" class="md-nav__link">
    <span class="md-ellipsis">
      13.8.0 (2024-12-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1370-2024-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      13.7.0 (2024-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1360-2024-11-06" class="md-nav__link">
    <span class="md-ellipsis">
      13.6.0 (2024-11-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1351-2024-10-28" class="md-nav__link">
    <span class="md-ellipsis">
      13.5.1 (2024-10-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1350-2024-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      13.5.0 (2024-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1342-2024-10-08" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.2 (2024-10-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1341-2024-10-07" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.1 (2024-10-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1340-2024-09-18" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.0 (2024-09-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1331-2024-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      13.3.1 (2024-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1330-2024-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      13.3.0 (2024-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1322-2024-08-14" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.2 (2024-08-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1321-2024-08-13" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.1 (2024-08-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1320-2024-08-12" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.0 (2024-08-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1310-2024-07-26" class="md-nav__link">
    <span class="md-ellipsis">
      13.1.0 (2024-07-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1302-2024-07-08" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.2 (2024-07-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1301-2024-07-04" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.1 (2024-07-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1300-2024-06-20" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.0 (2024-06-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12103-2024-06-14" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.3 (2024-06-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12102-2024-06-03" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.2 (2024-06-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12101-2024-05-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.1 (2024-05-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12100-2024-05-13" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.0 (2024-05-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1293-2024-04-24" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.3 (2024-04-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1292-2024-04-19" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.2 (2024-04-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1291-2024-04-17" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.1 (2024-04-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1290-2024-04-11" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.0 (2024-04-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1281-2024-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      12.8.1 (2024-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1280-2024-03-04" class="md-nav__link">
    <span class="md-ellipsis">
      12.8.0 (2024-03-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1270-2024-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      12.7.0 (2024-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1260-2024-01-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.6.0 (2024-01-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1250-2023-12-18" class="md-nav__link">
    <span class="md-ellipsis">
      12.5.0 (2023-12-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1240-2023-11-23" class="md-nav__link">
    <span class="md-ellipsis">
      12.4.0 (2023-11-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1230-2023-11-07" class="md-nav__link">
    <span class="md-ellipsis">
      12.3.0 (2023-11-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1220-2023-10-12" class="md-nav__link">
    <span class="md-ellipsis">
      12.2.0 (2023-10-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1213-2023-09-25" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.3 (2023-09-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1212-2023-09-21" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.2 (2023-09-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1211-2023-08-23" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.1 (2023-08-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1210-2023-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.0 (2023-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1202-2023-07-25" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.2 (2023-07-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1201-2023-07-21" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.1 (2023-07-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1200-2023-06-29" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.0 (2023-06-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1154-2023-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.4 (2023-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1153-2023-06-19" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.3 (2023-06-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1152-2023-06-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.2 (2023-06-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1151-2023-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.1 (2023-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1150-2023-05-19" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.0 (2023-05-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1140-2023-05-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.4.0 (2023-05-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1132-2023-04-29" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.2 (2023-04-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1131-2023-04-21" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.1 (2023-04-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1130-2023-04-12" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.0 (2023-04-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1120-2023-03-13" class="md-nav__link">
    <span class="md-ellipsis">
      11.2.0 (2023-03-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1110-2023-02-15" class="md-nav__link">
    <span class="md-ellipsis">
      11.1.0 (2023-02-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1100-2023-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.0.0 (2023-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10162-2023-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.2 (2023-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10161-2023-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.1 (2023-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10160-2023-01-24" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.0 (2023-01-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10150-2023-01-11" class="md-nav__link">
    <span class="md-ellipsis">
      10.15.0 (2023-01-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10141-2022-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      10.14.1 (2022-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10140-2022-11-21" class="md-nav__link">
    <span class="md-ellipsis">
      10.14.0 (2022-11-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10130-2022-11-16" class="md-nav__link">
    <span class="md-ellipsis">
      10.13.0 (2022-11-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10120-2022-11-01" class="md-nav__link">
    <span class="md-ellipsis">
      10.12.0 (2022-11-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10110-2022-09-27" class="md-nav__link">
    <span class="md-ellipsis">
      10.11.0 (2022-09-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10100-2022-09-13" class="md-nav__link">
    <span class="md-ellipsis">
      10.10.0 (2022-09-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1091-2022-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      10.9.1 (2022-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1090-2022-08-23" class="md-nav__link">
    <span class="md-ellipsis">
      10.9.0 (2022-08-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1080-2022-08-17" class="md-nav__link">
    <span class="md-ellipsis">
      10.8.0 (2022-08-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1070-2022-08-09" class="md-nav__link">
    <span class="md-ellipsis">
      10.7.0 (2022-08-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1062-2022-06-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.2 (2022-06-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1061-2022-06-14" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.1 (2022-06-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1060-2022-06-08" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.0 (2022-06-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1050-2022-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      10.5.0 (2022-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1040-2022-05-05" class="md-nav__link">
    <span class="md-ellipsis">
      10.4.0 (2022-05-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1030-2022-04-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.3.0 (2022-04-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1020-2022-03-06" class="md-nav__link">
    <span class="md-ellipsis">
      10.2.0 (2022-03-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1010-2022-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.1.0 (2022-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1005-2022-01-11" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.5 (2022-01-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1004-2022-01-07" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.4 (2022-01-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1003-2021-12-22" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.3 (2021-12-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1002-2021-12-16" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.2 (2021-12-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1001-2021-12-10" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.1 (2021-12-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1000-2021-12-01" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.0 (2021-12-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#983-2021-11-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.3 (2021-11-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#982-2021-11-01" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.2 (2021-11-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#981-2021-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.1 (2021-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#980-2021-09-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.0 (2021-09-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#970-2021-09-28" class="md-nav__link">
    <span class="md-ellipsis">
      9.7.0 (2021-09-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#960-2021-09-13" class="md-nav__link">
    <span class="md-ellipsis">
      9.6.0 (2021-09-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#952-2021-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.2 (2021-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#951-2021-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.1 (2021-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#950-2021-06-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.0 (2021-06-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#940-2021-06-11" class="md-nav__link">
    <span class="md-ellipsis">
      9.4.0 (2021-06-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#931-2021-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      9.3.1 (2021-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#930-2021-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      9.3.0 (2021-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#926-2021-04-26" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.6 (2021-04-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#925-2021-04-20" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.5 (2021-04-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#924-2021-04-09" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.4 (2021-04-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#923-2021-04-08" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.3 (2021-04-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#922-2021-04-07" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.2 (2021-04-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#921-2021-04-04" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.1 (2021-04-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#920-2021-03-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.0 (2021-03-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#910-2021-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      9.1.0 (2021-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#900-2020-02-18" class="md-nav__link">
    <span class="md-ellipsis">
      9.0.0 (2020-02-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#851-2021-01-07" class="md-nav__link">
    <span class="md-ellipsis">
      8.5.1 (2021-01-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#850-2021-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      8.5.0 (2021-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#840-2020-12-29" class="md-nav__link">
    <span class="md-ellipsis">
      8.4.0 (2020-12-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#835-2020-12-15" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.5 (2020-12-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#834-2020-12-10" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.4 (2020-12-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#833-2020-11-24" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.3 (2020-11-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#832-2020-10-22" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.2 (2020-10-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#831-2020-10-14" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.1 (2020-10-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#830-2020-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.0 (2020-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#820-2020-09-09" class="md-nav__link">
    <span class="md-ellipsis">
      8.2.0 (2020-09-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#814-2020-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.4 (2020-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#813-2020-06-25" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.3 (2020-06-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#812-2020-06-18" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.2 (2020-06-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#811-2020-06-17" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.1 (2020-06-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#810-2020-06-11" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.0 (2020-06-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#808-2020-04-20" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.8 (2020-04-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#807-2020-04-17" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.7 (2020-04-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#806-2020-03-05" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.6 (2020-03-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#805-2020-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.5 (2020-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#804-2020-02-06" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.4 (2020-02-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#803-2020-01-27" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.3 (2020-01-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#802-2020-01-16" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.2 (2020-01-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#801-2020-01-05" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.1 (2020-01-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#800-2020-01-02" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.0 (2020-01-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#798-2019-12-27" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.8 (2019-12-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#797-2019-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.7 (2019-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#796-2019-11-22" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.6 (2019-11-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#795-2019-11-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.5 (2019-11-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#794-2019-11-08" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.4 (2019-11-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#793-2019-09-10" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.3 (2019-09-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#792-2019-08-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.2 (2019-08-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#791-2019-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.1 (2019-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#790-2019-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.0 (2019-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#784-2019-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.4 (2019-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#783-2019-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.3 (2019-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#782-2019-05-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.2 (2019-05-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#781-2019-02-13" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.1 (2019-02-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#780-2019-01-18" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.0 (2019-01-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#771-2018-10-23" class="md-nav__link">
    <span class="md-ellipsis">
      7.7.1 (2018-10-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#770-2018-10-12" class="md-nav__link">
    <span class="md-ellipsis">
      7.7.0 (2018-10-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#763-2018-10-04" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.3 (2018-10-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#762-2018-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.2 (2018-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#761-2018-08-29" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.1 (2018-08-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#760-2018-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.0 (2018-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#750-2018-06-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.5.0 (2018-06-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#744-2018-06-06" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.4 (2018-06-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#743-2018-05-25" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.3 (2018-05-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#742-2018-04-30" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.2 (2018-04-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#741-2018-04-16" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.1 (2018-04-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#740-2018-03-23" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.0 (2018-03-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#730-2018-03-12" class="md-nav__link">
    <span class="md-ellipsis">
      7.3.0 (2018-03-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#721-2018-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      7.2.1 (2018-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#720-2018-01-18" class="md-nav__link">
    <span class="md-ellipsis">
      7.2.0 (2018-01-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#710-2018-01-03" class="md-nav__link">
    <span class="md-ellipsis">
      7.1.0 (2018-01-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#703-2017-12-21" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.3 (2017-12-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#702-2017-11-20" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.2 (2017-11-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#701-2017-11-20" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.1 (2017-11-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#700-2017-11-08" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.0 (2017-11-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#621-2017-10-11" class="md-nav__link">
    <span class="md-ellipsis">
      6.2.1 (2017-10-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#620-2017-10-06" class="md-nav__link">
    <span class="md-ellipsis">
      6.2.0 (2017-10-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#612-2017-09-12" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.2 (2017-09-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#611-2017-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.1 (2017-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#610-2017-07-28" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.0 (2017-07-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#600-2017-07-22" class="md-nav__link">
    <span class="md-ellipsis">
      6.0.0 (2017-07-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5140-2017-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.14.0 (2017-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5130-2017-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.13.0 (2017-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5120-2017-06-26" class="md-nav__link">
    <span class="md-ellipsis">
      5.12.0 (2017-06-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5110-2017-06-20" class="md-nav__link">
    <span class="md-ellipsis">
      5.11.0 (2017-06-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5100-2017-05-23" class="md-nav__link">
    <span class="md-ellipsis">
      5.10.0 (2017-05-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#590-2017-04-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.9.0 (2017-04-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#580-2017-04-21" class="md-nav__link">
    <span class="md-ellipsis">
      5.8.0 (2017-04-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#571-2017-03-22" class="md-nav__link">
    <span class="md-ellipsis">
      5.7.1 (2017-03-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#570-2017-03-20" class="md-nav__link">
    <span class="md-ellipsis">
      5.7.0 (2017-03-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#565-2017-03-07" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.5 (2017-03-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#564-2017-02-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.4 (2017-02-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#563-2017-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.3 (2017-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#562-2017-02-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.2 (2017-02-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#561-2017-02-03" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.1 (2017-02-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#560-2017-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.0 (2017-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#559-2017-01-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.9 (2017-01-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#558-2017-01-23" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.8 (2017-01-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#557-2017-01-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.7 (2017-01-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#556-2017-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.6 (2017-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#555-2017-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.5 (2017-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#554-2016-12-01" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.4 (2016-12-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#553-2016-11-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.3 (2016-11-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#552-2016-11-02" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.2 (2016-11-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#551-2016-10-14" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.1 (2016-10-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#550-2016-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.0 (2016-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#540-2016-10-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.4.0 (2016-10-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#533-2016-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.3 (2016-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#532-2016-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.2 (2016-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#531-2016-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.1 (2016-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#530-2016-06-10" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.0 (2016-06-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#522-2016-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.2 (2016-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#521-2016-02-11" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.1 (2016-02-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#520-2016-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.0 (2016-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5118-2016-01-25" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.18 (2016-01-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5117-2015-12-30" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.17 (2015-12-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5116-2015-12-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.16 (2015-12-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5115-2015-12-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.15 (2015-12-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5114-2015-12-09" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.14 (2015-12-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5113-2015-11-26" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.13 (2015-11-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5112-2015-11-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.12 (2015-11-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5111-2015-11-12" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.11 (2015-11-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5110-2015-09-09" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.10 (2015-09-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#519-2015-08-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.9 (2015-08-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#518-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.8 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#517-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.7 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#516-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.6 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#515-2015-06-05" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.5 (2015-06-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#514-2015-06-03" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.4 (2015-06-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#513-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.3 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#512-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.2 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#511-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.1 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#510-2015-05-17" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.0 (2015-05-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#500-2015-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.0.0 (2015-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#476-2015-03-18" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.6 (2015-03-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#475-2015-02-27" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.5 (2015-02-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#474-2015-02-25" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.4 (2015-02-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#473-2014-12-23" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.3 (2014-12-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#472-2014-12-15" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.2 (2014-12-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#471-2014-12-08" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.1 (2014-12-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#470-2014-12-04" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.0 (2014-12-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#462-2014-11-11" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.2 (2014-11-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#461-2014-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.1 (2014-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#460-2014-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.0 (2014-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#450-2014-03-01" class="md-nav__link">
    <span class="md-ellipsis">
      4.5.0 (2014-03-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#440-2013-03-14" class="md-nav__link">
    <span class="md-ellipsis">
      4.4.0 (2013-03-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#430-2012-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      4.3.0 (2012-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#420-2011-06-23" class="md-nav__link">
    <span class="md-ellipsis">
      4.2.0 (2011-06-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#410-2011-05-30" class="md-nav__link">
    <span class="md-ellipsis">
      4.1.0 (2011-05-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#400-2010-05-25" class="md-nav__link">
    <span class="md-ellipsis">
      4.0.0 (2010-05-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#320-2010-01-20" class="md-nav__link">
    <span class="md-ellipsis">
      3.2.0 (2010-01-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#310-2009-05-20" class="md-nav__link">
    <span class="md-ellipsis">
      3.1.0 (2009-05-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#300-2009-01-25" class="md-nav__link">
    <span class="md-ellipsis">
      3.0.0 (2009-01-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#210-2008-03-23" class="md-nav__link">
    <span class="md-ellipsis">
      2.1.0 (2008-03-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#200-2007-10-07" class="md-nav__link">
    <span class="md-ellipsis">
      2.0.0 (2007-10-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#100-2007-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      1.0.0 (2007-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#092-2006-03-07" class="md-nav__link">
    <span class="md-ellipsis">
      0.9.2 (2006-03-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#091-2006-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      0.9.1 (2006-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0025-2005-08-11" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.25 (2005-08-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0024-2005-01-10" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.24 (2005-01-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0023-2004-11-17" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.23 (2004-11-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0022-2004-07-27" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.22 (2004-07-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0021-2004-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.21 (2004-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0020-2004-03-15" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.20 (2004-03-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0019-2003-12-02" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.19 (2003-12-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0018-2003-11-13" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.18 (2003-11-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0017-2003-08-05" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.17 (2003-08-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0016-2003-07-04" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.16 (2003-07-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0015-2003-06-17" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.15 (2003-06-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0014-2003-05-28" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.14 (2003-05-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0013-2003-05-22" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.13 (2003-05-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0012-2003-05-08" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.12 (2003-05-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0011-2003-04-15" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.11 (2003-04-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0010-2003-04-08" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.10 (2003-04-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#009-2003-04-03" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.9 (2003-04-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#008-2003-03-27" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.8 (2003-03-27)
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../support/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Support
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../supported-formats/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    File Formats
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="https://mpxj.teemill.com/collection/all-products/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Store
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Getting Started
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-java/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Java
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-dotnet/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with .Net
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-python/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Python
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start-ruby/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Getting Started with Ruby
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-start/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPXJ Basics
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-build/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Building MPXJ
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-convert/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Converting Files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Read...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            How to Read...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-asta/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Asta files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-conceptdraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ConceptDraw PROJECT files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-openplan/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Deltek Open Plan BK3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-edraw/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Edraw Project EDPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-fasttrack/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FastTrack files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttdesigner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Gantt Designer files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-ganttproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    GanttProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-merlin/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Merlin files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpd-database/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPD databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpp/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX Files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-p3/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P3 files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-primavera/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    P6 Databases
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-phoenix/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Phoenix files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-plf/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PLF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectcommander/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Project Commander files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-projectlibre/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    ProjectLibre files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-schedule-grid/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Schedule Grid files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-suretrak/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SureTrak files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-synchro/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Synchro Scheduler files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-turboproject/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    TurboProject files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-read-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Write...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            How to Write...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mpx/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPX files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-mspdi/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MSPDI files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-planner/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Planner files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-pmxml/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    PMXML files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-sdef/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    SDEF files
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-write-xer/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    XER files
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    How to Use...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            How to Use...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-baselines/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Baselines
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-calendars/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Calendars
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-cpm/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    CPM Schedulers
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-external-projects/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    External Projects
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-fields/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Fields
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../howto-use-universal/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Universal Project Reader
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_10" >
        
          
          <label class="md-nav__link" for="__nav_10" id="__nav_10_label" tabindex="0">
            
  
  <span class="md-ellipsis">
    Field Guides...
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_10_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_10">
            <span class="md-nav__icon md-icon"></span>
            Field Guides...
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Field Guide
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../mpp-field-guide/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    MPP Field Guide
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../apidocs/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    JavaDoc
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../faq/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    FAQ
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../users/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Users
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../summary.html" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Maven Reports
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#note" class="md-nav__link">
    <span class="md-ellipsis">
      NOTE
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1420-unreleased" class="md-nav__link">
    <span class="md-ellipsis">
      14.2.0 (unreleased)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1410-2025-06-05" class="md-nav__link">
    <span class="md-ellipsis">
      14.1.0 (2025-06-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1400-2025-05-07" class="md-nav__link">
    <span class="md-ellipsis">
      14.0.0 (2025-05-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13120-2025-04-09" class="md-nav__link">
    <span class="md-ellipsis">
      13.12.0 (2025-04-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13110-2025-03-10" class="md-nav__link">
    <span class="md-ellipsis">
      13.11.0 (2025-03-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#13100-2025-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      13.10.0 (2025-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1390-2025-01-09" class="md-nav__link">
    <span class="md-ellipsis">
      13.9.0 (2025-01-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1380-2024-12-17" class="md-nav__link">
    <span class="md-ellipsis">
      13.8.0 (2024-12-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1370-2024-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      13.7.0 (2024-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1360-2024-11-06" class="md-nav__link">
    <span class="md-ellipsis">
      13.6.0 (2024-11-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1351-2024-10-28" class="md-nav__link">
    <span class="md-ellipsis">
      13.5.1 (2024-10-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1350-2024-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      13.5.0 (2024-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1342-2024-10-08" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.2 (2024-10-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1341-2024-10-07" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.1 (2024-10-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1340-2024-09-18" class="md-nav__link">
    <span class="md-ellipsis">
      13.4.0 (2024-09-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1331-2024-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      13.3.1 (2024-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1330-2024-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      13.3.0 (2024-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1322-2024-08-14" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.2 (2024-08-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1321-2024-08-13" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.1 (2024-08-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1320-2024-08-12" class="md-nav__link">
    <span class="md-ellipsis">
      13.2.0 (2024-08-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1310-2024-07-26" class="md-nav__link">
    <span class="md-ellipsis">
      13.1.0 (2024-07-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1302-2024-07-08" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.2 (2024-07-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1301-2024-07-04" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.1 (2024-07-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1300-2024-06-20" class="md-nav__link">
    <span class="md-ellipsis">
      13.0.0 (2024-06-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12103-2024-06-14" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.3 (2024-06-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12102-2024-06-03" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.2 (2024-06-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12101-2024-05-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.1 (2024-05-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#12100-2024-05-13" class="md-nav__link">
    <span class="md-ellipsis">
      12.10.0 (2024-05-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1293-2024-04-24" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.3 (2024-04-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1292-2024-04-19" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.2 (2024-04-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1291-2024-04-17" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.1 (2024-04-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1290-2024-04-11" class="md-nav__link">
    <span class="md-ellipsis">
      12.9.0 (2024-04-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1281-2024-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      12.8.1 (2024-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1280-2024-03-04" class="md-nav__link">
    <span class="md-ellipsis">
      12.8.0 (2024-03-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1270-2024-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      12.7.0 (2024-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1260-2024-01-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.6.0 (2024-01-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1250-2023-12-18" class="md-nav__link">
    <span class="md-ellipsis">
      12.5.0 (2023-12-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1240-2023-11-23" class="md-nav__link">
    <span class="md-ellipsis">
      12.4.0 (2023-11-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1230-2023-11-07" class="md-nav__link">
    <span class="md-ellipsis">
      12.3.0 (2023-11-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1220-2023-10-12" class="md-nav__link">
    <span class="md-ellipsis">
      12.2.0 (2023-10-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1213-2023-09-25" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.3 (2023-09-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1212-2023-09-21" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.2 (2023-09-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1211-2023-08-23" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.1 (2023-08-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1210-2023-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      12.1.0 (2023-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1202-2023-07-25" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.2 (2023-07-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1201-2023-07-21" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.1 (2023-07-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1200-2023-06-29" class="md-nav__link">
    <span class="md-ellipsis">
      12.0.0 (2023-06-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1154-2023-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.4 (2023-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1153-2023-06-19" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.3 (2023-06-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1152-2023-06-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.2 (2023-06-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1151-2023-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.1 (2023-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1150-2023-05-19" class="md-nav__link">
    <span class="md-ellipsis">
      11.5.0 (2023-05-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1140-2023-05-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.4.0 (2023-05-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1132-2023-04-29" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.2 (2023-04-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1131-2023-04-21" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.1 (2023-04-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1130-2023-04-12" class="md-nav__link">
    <span class="md-ellipsis">
      11.3.0 (2023-04-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1120-2023-03-13" class="md-nav__link">
    <span class="md-ellipsis">
      11.2.0 (2023-03-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1110-2023-02-15" class="md-nav__link">
    <span class="md-ellipsis">
      11.1.0 (2023-02-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1100-2023-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      11.0.0 (2023-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10162-2023-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.2 (2023-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10161-2023-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.1 (2023-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10160-2023-01-24" class="md-nav__link">
    <span class="md-ellipsis">
      10.16.0 (2023-01-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10150-2023-01-11" class="md-nav__link">
    <span class="md-ellipsis">
      10.15.0 (2023-01-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10141-2022-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      10.14.1 (2022-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10140-2022-11-21" class="md-nav__link">
    <span class="md-ellipsis">
      10.14.0 (2022-11-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10130-2022-11-16" class="md-nav__link">
    <span class="md-ellipsis">
      10.13.0 (2022-11-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10120-2022-11-01" class="md-nav__link">
    <span class="md-ellipsis">
      10.12.0 (2022-11-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10110-2022-09-27" class="md-nav__link">
    <span class="md-ellipsis">
      10.11.0 (2022-09-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#10100-2022-09-13" class="md-nav__link">
    <span class="md-ellipsis">
      10.10.0 (2022-09-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1091-2022-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      10.9.1 (2022-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1090-2022-08-23" class="md-nav__link">
    <span class="md-ellipsis">
      10.9.0 (2022-08-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1080-2022-08-17" class="md-nav__link">
    <span class="md-ellipsis">
      10.8.0 (2022-08-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1070-2022-08-09" class="md-nav__link">
    <span class="md-ellipsis">
      10.7.0 (2022-08-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1062-2022-06-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.2 (2022-06-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1061-2022-06-14" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.1 (2022-06-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1060-2022-06-08" class="md-nav__link">
    <span class="md-ellipsis">
      10.6.0 (2022-06-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1050-2022-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      10.5.0 (2022-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1040-2022-05-05" class="md-nav__link">
    <span class="md-ellipsis">
      10.4.0 (2022-05-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1030-2022-04-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.3.0 (2022-04-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1020-2022-03-06" class="md-nav__link">
    <span class="md-ellipsis">
      10.2.0 (2022-03-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1010-2022-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      10.1.0 (2022-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1005-2022-01-11" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.5 (2022-01-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1004-2022-01-07" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.4 (2022-01-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1003-2021-12-22" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.3 (2021-12-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1002-2021-12-16" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.2 (2021-12-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1001-2021-12-10" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.1 (2021-12-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#1000-2021-12-01" class="md-nav__link">
    <span class="md-ellipsis">
      10.0.0 (2021-12-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#983-2021-11-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.3 (2021-11-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#982-2021-11-01" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.2 (2021-11-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#981-2021-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.1 (2021-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#980-2021-09-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.8.0 (2021-09-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#970-2021-09-28" class="md-nav__link">
    <span class="md-ellipsis">
      9.7.0 (2021-09-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#960-2021-09-13" class="md-nav__link">
    <span class="md-ellipsis">
      9.6.0 (2021-09-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#952-2021-08-22" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.2 (2021-08-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#951-2021-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.1 (2021-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#950-2021-06-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.5.0 (2021-06-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#940-2021-06-11" class="md-nav__link">
    <span class="md-ellipsis">
      9.4.0 (2021-06-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#931-2021-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      9.3.1 (2021-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#930-2021-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      9.3.0 (2021-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#926-2021-04-26" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.6 (2021-04-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#925-2021-04-20" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.5 (2021-04-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#924-2021-04-09" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.4 (2021-04-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#923-2021-04-08" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.3 (2021-04-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#922-2021-04-07" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.2 (2021-04-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#921-2021-04-04" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.1 (2021-04-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#920-2021-03-30" class="md-nav__link">
    <span class="md-ellipsis">
      9.2.0 (2021-03-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#910-2021-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      9.1.0 (2021-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#900-2020-02-18" class="md-nav__link">
    <span class="md-ellipsis">
      9.0.0 (2020-02-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#851-2021-01-07" class="md-nav__link">
    <span class="md-ellipsis">
      8.5.1 (2021-01-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#850-2021-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      8.5.0 (2021-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#840-2020-12-29" class="md-nav__link">
    <span class="md-ellipsis">
      8.4.0 (2020-12-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#835-2020-12-15" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.5 (2020-12-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#834-2020-12-10" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.4 (2020-12-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#833-2020-11-24" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.3 (2020-11-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#832-2020-10-22" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.2 (2020-10-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#831-2020-10-14" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.1 (2020-10-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#830-2020-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      8.3.0 (2020-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#820-2020-09-09" class="md-nav__link">
    <span class="md-ellipsis">
      8.2.0 (2020-09-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#814-2020-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.4 (2020-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#813-2020-06-25" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.3 (2020-06-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#812-2020-06-18" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.2 (2020-06-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#811-2020-06-17" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.1 (2020-06-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#810-2020-06-11" class="md-nav__link">
    <span class="md-ellipsis">
      8.1.0 (2020-06-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#808-2020-04-20" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.8 (2020-04-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#807-2020-04-17" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.7 (2020-04-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#806-2020-03-05" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.6 (2020-03-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#805-2020-02-07" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.5 (2020-02-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#804-2020-02-06" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.4 (2020-02-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#803-2020-01-27" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.3 (2020-01-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#802-2020-01-16" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.2 (2020-01-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#801-2020-01-05" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.1 (2020-01-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#800-2020-01-02" class="md-nav__link">
    <span class="md-ellipsis">
      8.0.0 (2020-01-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#798-2019-12-27" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.8 (2019-12-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#797-2019-11-25" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.7 (2019-11-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#796-2019-11-22" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.6 (2019-11-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#795-2019-11-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.5 (2019-11-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#794-2019-11-08" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.4 (2019-11-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#793-2019-09-10" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.3 (2019-09-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#792-2019-08-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.2 (2019-08-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#791-2019-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.1 (2019-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#790-2019-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      7.9.0 (2019-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#784-2019-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.4 (2019-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#783-2019-05-24" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.3 (2019-05-24)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#782-2019-05-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.2 (2019-05-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#781-2019-02-13" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.1 (2019-02-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#780-2019-01-18" class="md-nav__link">
    <span class="md-ellipsis">
      7.8.0 (2019-01-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#771-2018-10-23" class="md-nav__link">
    <span class="md-ellipsis">
      7.7.1 (2018-10-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#770-2018-10-12" class="md-nav__link">
    <span class="md-ellipsis">
      7.7.0 (2018-10-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#763-2018-10-04" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.3 (2018-10-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#762-2018-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.2 (2018-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#761-2018-08-29" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.1 (2018-08-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#760-2018-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      7.6.0 (2018-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#750-2018-06-19" class="md-nav__link">
    <span class="md-ellipsis">
      7.5.0 (2018-06-19)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#744-2018-06-06" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.4 (2018-06-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#743-2018-05-25" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.3 (2018-05-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#742-2018-04-30" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.2 (2018-04-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#741-2018-04-16" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.1 (2018-04-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#740-2018-03-23" class="md-nav__link">
    <span class="md-ellipsis">
      7.4.0 (2018-03-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#730-2018-03-12" class="md-nav__link">
    <span class="md-ellipsis">
      7.3.0 (2018-03-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#721-2018-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      7.2.1 (2018-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#720-2018-01-18" class="md-nav__link">
    <span class="md-ellipsis">
      7.2.0 (2018-01-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#710-2018-01-03" class="md-nav__link">
    <span class="md-ellipsis">
      7.1.0 (2018-01-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#703-2017-12-21" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.3 (2017-12-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#702-2017-11-20" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.2 (2017-11-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#701-2017-11-20" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.1 (2017-11-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#700-2017-11-08" class="md-nav__link">
    <span class="md-ellipsis">
      7.0.0 (2017-11-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#621-2017-10-11" class="md-nav__link">
    <span class="md-ellipsis">
      6.2.1 (2017-10-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#620-2017-10-06" class="md-nav__link">
    <span class="md-ellipsis">
      6.2.0 (2017-10-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#612-2017-09-12" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.2 (2017-09-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#611-2017-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.1 (2017-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#610-2017-07-28" class="md-nav__link">
    <span class="md-ellipsis">
      6.1.0 (2017-07-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#600-2017-07-22" class="md-nav__link">
    <span class="md-ellipsis">
      6.0.0 (2017-07-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5140-2017-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.14.0 (2017-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5130-2017-06-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.13.0 (2017-06-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5120-2017-06-26" class="md-nav__link">
    <span class="md-ellipsis">
      5.12.0 (2017-06-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5110-2017-06-20" class="md-nav__link">
    <span class="md-ellipsis">
      5.11.0 (2017-06-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5100-2017-05-23" class="md-nav__link">
    <span class="md-ellipsis">
      5.10.0 (2017-05-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#590-2017-04-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.9.0 (2017-04-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#580-2017-04-21" class="md-nav__link">
    <span class="md-ellipsis">
      5.8.0 (2017-04-21)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#571-2017-03-22" class="md-nav__link">
    <span class="md-ellipsis">
      5.7.1 (2017-03-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#570-2017-03-20" class="md-nav__link">
    <span class="md-ellipsis">
      5.7.0 (2017-03-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#565-2017-03-07" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.5 (2017-03-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#564-2017-02-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.4 (2017-02-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#563-2017-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.3 (2017-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#562-2017-02-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.2 (2017-02-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#561-2017-02-03" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.1 (2017-02-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#560-2017-01-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.6.0 (2017-01-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#559-2017-01-27" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.9 (2017-01-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#558-2017-01-23" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.8 (2017-01-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#557-2017-01-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.7 (2017-01-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#556-2017-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.6 (2017-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#555-2017-01-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.5 (2017-01-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#554-2016-12-01" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.4 (2016-12-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#553-2016-11-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.3 (2016-11-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#552-2016-11-02" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.2 (2016-11-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#551-2016-10-14" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.1 (2016-10-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#550-2016-10-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.5.0 (2016-10-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#540-2016-10-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.4.0 (2016-10-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#533-2016-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.3 (2016-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#532-2016-08-31" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.2 (2016-08-31)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#531-2016-07-01" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.1 (2016-07-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#530-2016-06-10" class="md-nav__link">
    <span class="md-ellipsis">
      5.3.0 (2016-06-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#522-2016-03-11" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.2 (2016-03-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#521-2016-02-11" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.1 (2016-02-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#520-2016-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      5.2.0 (2016-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5118-2016-01-25" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.18 (2016-01-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5117-2015-12-30" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.17 (2015-12-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5116-2015-12-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.16 (2015-12-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5115-2015-12-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.15 (2015-12-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5114-2015-12-09" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.14 (2015-12-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5113-2015-11-26" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.13 (2015-11-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5112-2015-11-16" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.12 (2015-11-16)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5111-2015-11-12" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.11 (2015-11-12)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#5110-2015-09-09" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.10 (2015-09-09)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#519-2015-08-29" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.9 (2015-08-29)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#518-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.8 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#517-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.7 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#516-2015-07-13" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.6 (2015-07-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#515-2015-06-05" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.5 (2015-06-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#514-2015-06-03" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.4 (2015-06-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#513-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.3 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#512-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.2 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#511-2015-05-18" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.1 (2015-05-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#510-2015-05-17" class="md-nav__link">
    <span class="md-ellipsis">
      5.1.0 (2015-05-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#500-2015-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      5.0.0 (2015-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#476-2015-03-18" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.6 (2015-03-18)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#475-2015-02-27" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.5 (2015-02-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#474-2015-02-25" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.4 (2015-02-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#473-2014-12-23" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.3 (2014-12-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#472-2014-12-15" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.2 (2014-12-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#471-2014-12-08" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.1 (2014-12-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#470-2014-12-04" class="md-nav__link">
    <span class="md-ellipsis">
      4.7.0 (2014-12-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#462-2014-11-11" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.2 (2014-11-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#461-2014-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.1 (2014-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#460-2014-10-17" class="md-nav__link">
    <span class="md-ellipsis">
      4.6.0 (2014-10-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#450-2014-03-01" class="md-nav__link">
    <span class="md-ellipsis">
      4.5.0 (2014-03-01)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#440-2013-03-14" class="md-nav__link">
    <span class="md-ellipsis">
      4.4.0 (2013-03-14)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#430-2012-02-08" class="md-nav__link">
    <span class="md-ellipsis">
      4.3.0 (2012-02-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#420-2011-06-23" class="md-nav__link">
    <span class="md-ellipsis">
      4.2.0 (2011-06-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#410-2011-05-30" class="md-nav__link">
    <span class="md-ellipsis">
      4.1.0 (2011-05-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#400-2010-05-25" class="md-nav__link">
    <span class="md-ellipsis">
      4.0.0 (2010-05-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#320-2010-01-20" class="md-nav__link">
    <span class="md-ellipsis">
      3.2.0 (2010-01-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#310-2009-05-20" class="md-nav__link">
    <span class="md-ellipsis">
      3.1.0 (2009-05-20)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#300-2009-01-25" class="md-nav__link">
    <span class="md-ellipsis">
      3.0.0 (2009-01-25)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#210-2008-03-23" class="md-nav__link">
    <span class="md-ellipsis">
      2.1.0 (2008-03-23)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#200-2007-10-07" class="md-nav__link">
    <span class="md-ellipsis">
      2.0.0 (2007-10-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#100-2007-08-30" class="md-nav__link">
    <span class="md-ellipsis">
      1.0.0 (2007-08-30)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#092-2006-03-07" class="md-nav__link">
    <span class="md-ellipsis">
      0.9.2 (2006-03-07)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#091-2006-01-26" class="md-nav__link">
    <span class="md-ellipsis">
      0.9.1 (2006-01-26)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0025-2005-08-11" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.25 (2005-08-11)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0024-2005-01-10" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.24 (2005-01-10)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0023-2004-11-17" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.23 (2004-11-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0022-2004-07-27" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.22 (2004-07-27)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0021-2004-05-06" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.21 (2004-05-06)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0020-2004-03-15" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.20 (2004-03-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0019-2003-12-02" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.19 (2003-12-02)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0018-2003-11-13" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.18 (2003-11-13)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0017-2003-08-05" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.17 (2003-08-05)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0016-2003-07-04" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.16 (2003-07-04)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0015-2003-06-17" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.15 (2003-06-17)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0014-2003-05-28" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.14 (2003-05-28)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0013-2003-05-22" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.13 (2003-05-22)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0012-2003-05-08" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.12 (2003-05-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0011-2003-04-15" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.11 (2003-04-15)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#0010-2003-04-08" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.10 (2003-04-08)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#009-2003-04-03" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.9 (2003-04-03)
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#008-2003-03-27" class="md-nav__link">
    <span class="md-ellipsis">
      0.0.8 (2003-03-27)
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


<h1 id="changelog">Changelog</h1>
<h2 id="note">NOTE</h2>
<p>From version 14.0.0 onwards the <code>net.sf.mpxj</code>, <code>net.sf.mpxj-for-csharp</code> and <code>net.sf.mpxj-for-vb</code> packages are
no longer distributed. Please use the <code>MPXJ.Net</code> package instead.</p>
<h2 id="1420-unreleased">14.2.0 (unreleased)</h2>
<h2 id="1410-2025-06-05">14.1.0 (2025-06-05)</h2>
<ul>
<li>Updated to POI 5.4.1</li>
<li>Improve actual duration calculation for activities with suspend and resume dates when reading XER files and Primavera P6 databases.</li>
<li>Added Enable Summarization and Enable Publication flags to <code>ProjectProperties</code>.</li>
<li>Added support for Enable Summarization and Enable Publication flags when reading and writing PMXML files.</li>
<li>Ensure the Actual Start and Actual Finish attributes in <code>ProjectProperties</code> are populated.</li>
<li>Improve retrieval of Enterprise Custom Field names when reading MSPDI files.</li>
<li>Updated the JSON writer to use Jackson.</li>
</ul>
<h2 id="1400-2025-05-07">14.0.0 (2025-05-07)</h2>
<ul>
<li><strong>NEW FEATURES</strong></li>
<li>MPXJ can now schedule projects using CPM (Critical Path Method)</li>
<li>Two new classes (<code>MicrosoftScheduler</code> and <code>PrimaveraScheduler</code>) allow MPXJ to schedule a project in a way which follows the approach of either Microsoft Project or Primavera P6.</li>
<li>Added support for reading Edraw Project EDPX files</li>
<li><strong>CHANGES</strong></li>
<li>Improvements to accuracy of reading text UDF values from Powerproject PP files.</li>
<li>Corrected conversion of elapsed durations when writing JSON files.</li>
<li>Added the <code>Relation#lag_units</code> method to the ruby gem.</li>
<li><strong>BREAKING CHANGES - .Net</strong></li>
<li>The <code>net.sf.mpxj</code>, <code>net.sf.mpxj-for-csharp</code>, and <code>net.sf.mpxj-for-vb</code> NuGet packages are no longer being distributed. You must migrate your code to use the <code>MPXJ.Net</code> NuGet package instead.</li>
<li><strong>BREAKING CHANGES - Java, Python</strong></li>
<li>The name of the package containing MPXJ's Java classes has changed from <code>net.sf.mpxj</code> to <code>org.mpxj</code>. You will need to update your code by searching for <code>net.sf.mpxj</code> and replace this with <code>org.mpxj</code>. NOTE: for Java applications using Maven, the Maven Group ID <strong>has not changed</strong>, you will still retrieve MPXJ using the Group ID <code>net.sf.mpxj</code>.</li>
<li>The constant <code>TaskField.PRIMARY_RESOURCE_ID</code> has been renamed to <code>TaskField.PRIMARY_RESOURCE_UNIQUE_ID</code>.</li>
<li>The <code>RelationContainer#getRawSuccessors</code> method has been removed. Use the <code>RelationContainer#getSuccessors</code> method instead. This method now returns the same data <code>getRawSuccessors</code> returned previously.</li>
<li>The deprecated <code>UserDefinedField</code> constructors have been removed, use <code>UserDefinedField.Builder</code> instead.</li>
<li>The deprecated <code>UserDefinedField#setDataType</code> method has been removed, use the <code>UserDefinedField.Builder#dataType</code> method instead.</li>
<li>The deprecated <code>StructuredNotes</code> constructor has been removed, use the <code>StructuredNotes</code> constructor taking a <code>ProjectFile</code> instance instead.</li>
<li>The deprecated <code>Relation#getSourceTask</code> and <code>Relation#getTargetTask</code> methods have been removed, use <code>Relation#getPredecessorTask</code> and <code>Relation#getSuccessorTask</code> methods instead.</li>
<li>The deprecated <code>Relation.Builder#sourceTask</code> and <code>Relation.Builder#targetTask</code> methods have been removed, use <code>Relation.Builder#predecessorTask</code> and <code>Relation.Builder#successorTask</code> methods instead.</li>
<li>The deprecated <code>ActivityCodeValue#getType</code> method has been removed. Use the <code>ActivityCodeValue#getParentCode</code> method instead.</li>
<li>The deprecated <code>ActivityCodeValue#getActivityCode</code> method has been removed. Use the <code>ActivityCodeValue#getParentCode</code> method instead.</li>
<li>The deprecated <code>ActivityCodeValue#getParent</code> method has been removed. Use the <code>ActivityCodeValue#getParentValue</code> method instead.</li>
<li>The deprecated <code>ActivityCodeValue#getParentUniqueID</code> method has been removed. Use the <code>ActivityCodeValue#getParentValueUniqueID</code> method instead.</li>
<li>The deprecated <code>ActivityCodeValue.Builder#type</code> method has been removed. Use the <code>ActivityCodeValue.Builder#activityCode</code> method instead.</li>
<li>The deprecated <code>ActivityCodeValue.Builder#parent</code> method has been removed. Use the <code>ActivityCodeValue.Builder#parentValue</code> method instead.</li>
<li>The deprecated <code>Task#addActivityCode</code> method has been removed. Use the <code>Task#addActivityCodeValue</code> method instead.</li>
<li>The deprecated <code>GanttBarStyleException#getBarStyleIndex</code> method has been removed. Use <code>GanttBarStyleException#getGanttBarStyleID</code> to retrieve the bar style ID, and <code>GanttChartView#getGanttBarStyleByID</code> to retrieve the style</li>
<li>The deprecated constant <code>TaskField.ACTIVITY_CODE_LIST</code> has been removed. Use <code>TaskField.ACTIVITY_CODE_VALUES</code> instead.</li>
<li>The deprecated <code>Task#getActivityCodes</code> method has been removed. Use the <code>Task#getActivityCodeValues</code> method instead.</li>
<li>The deprecated <code>Task#setPrimaryResourceID</code> method has been removed. Use the <code>Task#setPrimaryResourceUniqueID</code> method instead.</li>
<li>The deprecated <code>Task#getPrimaryResourceID</code> method has been removed. Use the <code>Task#getPrimaryResourceUniqueID</code> method instead.</li>
<li>The deprecated <code>Task#isSucessor</code> method has been removed. Use the <code>Task#isSuccessor</code> method instead.</li>
<li>The common <code>MPPUtility</code> static methods <code>getShort</code>, <code>getInt</code>and <code>getLong</code> have been moved to the <code>ByteArrayHelper</code> class.</li>
<li><strong>BREAKING CHANGES - Ruby</strong></li>
<li>The deprecated Ruby attribute <code>Relation#task_unique_id</code> has been removed, use <code>Relation#predecessor_task_unique_id</code> and <code>Relation#successor_task_unique_id</code> instead.</li>
</ul>
<h2 id="13120-2025-04-09">13.12.0 (2025-04-09)</h2>
<ul>
<li>Added support for reading Float Path and Float Path Order from XER files and P6 databases.</li>
<li>Added support for writing Float Path and Float Path Order to XER files.</li>
<li>Added support for reading baselines from Phoenix schedules.</li>
<li>Improve date arithmetic when using the <code>ProjectCalendar#getDate()</code> method with elapsed durations.</li>
<li>Include units percent complete when writing resource assignments to PMXML files.</li>
<li>Improve accuracy of resource assignment remaining units when writing XER and PMXML files.</li>
<li>When writing MSPDI files, calculate resource assignment remaining work if not present.</li>
</ul>
<h2 id="13110-2025-03-10">13.11.0 (2025-03-10)</h2>
<ul>
<li>Add support for reading the WBS and Activity Methodology GUID attribute from XER files and P6 databases, and for writing this to XER files.</li>
<li>Improve accuracy of resource assignment start and finsh dates when reading XER files and P6 databases.</li>
<li>Fixed an issue reading resource code value hierarchy from XER files.</li>
<li>Improve retrieval of Gantt Bar Styles from certain MPP files.</li>
<li>Added the <code>GanttBarStyleException</code> methods <code>getGanttBarStyleID()</code> and <code>setGanttBarStyleID()</code>.</li>
<li>Added the <code>GanttChartView</code> method <code>getGanttBarStyleByID()</code>.</li>
<li>Marked the <code>GanttBarStyleException#getBarStyleIndex()</code> method as deprecated. Use the <code>GanttBarStyleException#getGanttBarStyleID()</code> method to retrieve the Gantt Bar Style ID, then use the view's <code>getGanttBarStyleByID()</code> method to retrieve a list of matching styles.</li>
<li>Added the <code>Duration#negate()</code> method to simplify negating a duration.</li>
<li>Improve provision of default values for Project Planned Start date and Activity Planned Duration when writing XER files.</li>
</ul>
<h2 id="13100-2025-02-07">13.10.0 (2025-02-07)</h2>
<ul>
<li>Add support for reading the P6 EPS using the <code>listEps()</code> method provided by the <code>PrimaveraDatabaseReader</code> and <code>PrimaveraDatabaseFileReader</code> classes.</li>
<li>Improve handling of Activity Type attribute when reading PMXML files written by Primavera P6 6.x.</li>
<li>Ensure that the External Early Start and External Late Finish attributes are written to XER files.</li>
<li>Fix a NPE when calling <code>PrimaveraXERFileReader.listProjects()</code>.</li>
<li>Avoid unnecessary data storage and type conversion to improve efficiency when calling <code>PrimaveraXERFileReader.listProjects()</code>.</li>
<li>Provide additional <code>ResourceAssignment</code> methods to allow <code>List&lt;TimephasedWork&gt;</code> to be used to add timephased work, rather than requiring a <code>TimephasedWorkContainer</code>.</li>
<li>Improve identification of tasks when reading certain Asta Powerproject PP files.</li>
</ul>
<h2 id="1390-2025-01-09">13.9.0 (2025-01-09)</h2>
<ul>
<li>Updated to POI 5.4.0</li>
<li>Updated PMXML schema to version 24.12.</li>
<li>Added support for reading and writing currencies for Primavera P6 schedules.</li>
<li>Improve recognition of dates displayed as NA in Microsoft Project when reading certain MPP file.</li>
<li>Ignore invalid cost rate table entries when reading certain MPP files.</li>
</ul>
<h2 id="1380-2024-12-17">13.8.0 (2024-12-17)</h2>
<ul>
<li>Added support for reading and writing Project Codes, Resource Codes, Role Codes and Resource Assignment Codes for Primavera P6 schedules.</li>
<li>When writing PMXML files, improve handling of P6 schedules where activity code sequence numbers are missing.</li>
<li>Added an <em>experimental</em> feature to <code>MSPDIWriter</code> to allow the writer to generate timephased data when none is present. Disabled by default, call the <code>setGenerateMissingTimephasedData</code> and pass <code>true</code> to enable.</li>
<li>To improve consistency, the methods <code>Task.getPrimaryResourceID()</code> and <code>Task.setPrimaryResourceID()</code> have been marked as deprecated. Use the new <code>Task.getPrimaryResourceUniqueID()</code> and <code>Task.setPrimaryResourceUniqueID()</code> methods instead.</li>
<li>Added the methods <code>Task.getPrimaryResource()</code> and <code>Task.setPrimaryResource()</code>.</li>
<li>Improved accuracy of retrieving the resource assignment GUID attribute when reading MPP files (Contributed by Fabian Schmidt).</li>
<li>Improve population of Task Start and Finish attributes when reading Primavera P6 schedules.</li>
<li>Marked the <code>ActivityCodeValue.getParent()</code> method as deprecated. Use <code>ActivityCodeValue.getParentValue()</code> instead.</li>
<li>Marked the <code>ActivityCodeValue.getParentUniqueID()</code> method as deprecated. Use <code>ActivityCodeValue.getParentValueUniqueID()</code> instead.</li>
<li>Marked the <code>ActivityCodeValue.Builder.parent()</code> method as deprecated. Use <code>ActivityCodeValue.Builder.parentValue()</code> instead.</li>
<li>Marked the <code>ActivityCodeValue.getActivityCode()</code> method as deprecated. Use <code>ActivityCodeValue.getParentCode()</code> instead.</li>
</ul>
<h2 id="1370-2024-11-25">13.7.0 (2024-11-25)</h2>
<ul>
<li>Update the MPXJ ruby gem to allow access to calendar data.</li>
<li>Mark the <code>ActivityCodeValue.getType()</code> method as deprecated. For clarity this method has been replaced by the new <code>ActivityCodeValue.getActivityCode()</code> method.</li>
<li>Mark the <code>ActivityCodeValue.Builder.type()</code> method as deprecated. For clarity this method has been replaced by the new <code>ActivityCodeValue.Builder.activityCode()</code> method.</li>
<li>Added the <code>Task.getActivityCodeValues()</code> method, which returns a <code>Map</code> of <code>ActivityCodeValue</code> instances, keyed by <code>ActivityCode</code>.</li>
<li>Marked the <code>Task.getActivityCodes()</code> method as deprecated. Replaced with the <code>Task.getActivityCodeValues()</code> method which is more clearly named, and presents the activity code values in a more flexible form.</li>
<li>Added the <code>Task.addActivityCodeValue()</code> method.</li>
<li>Marked the <code>Task.addActivityCode()</code> method as deprecated. Replaced with the <code>Task.addActivityCodeValue()</code> method which is more clearly named.</li>
<li>Further improvements to retrieval of custom field values read from MPP files.</li>
<li>Ensure that missing resource assignment and task start and finish dates are handled gracefully when working with calendars for manually scheduled tasks.</li>
</ul>
<h2 id="1360-2024-11-06">13.6.0 (2024-11-06)</h2>
<ul>
<li>Added the <code>Task.getBaselineTask()</code> methods. For applications where a separate baseline schedule is present or a baseline has been manually added to the <code>ProjectFile</code> instance, these methods will allow you to access the underlying baseline task instance from the current task instance.</li>
<li>Added the Activity Percent Complete attribute to the <code>Task</code> class. The value of this attribute will be the Duration, Physical or Units percent complete value, based on the Percent Complete Type setting. This attribute is provided as a convenience to match the Activity Percent Complete type value shown in P6.</li>
<li>Improve retrieval of custom field values for certain MPP files.</li>
<li>Improve handling of PMXML files with more than 11 baselines.</li>
<li>Improve handling of unexpected data types when writing JSON files.</li>
<li>Added the <code>Relation.getPredecessorTask()</code> and <code>Relation.getSuccessorTask()</code> methods.</li>
<li>Marked the <code>Relation.getSourceTask()</code> and <code>Relation.getTargetTask()</code> methods as deprecated, use the <code>Relation.getPredecessorTask()</code> and <code>Relation.getSuccessorTask()</code> instead.</li>
<li>Ensure that with "Link Cross Project Relations" enabled when reading XER or PMXML files, the predecessor and successor lists for both tasks related acrosss projects are correctly populated.</li>
</ul>
<h2 id="1351-2024-10-28">13.5.1 (2024-10-28)</h2>
<ul>
<li>Fix CVE-2024-49771: Potential Path Traversal Vulnerability (Contributed by yyjLF and sprinkle).</li>
</ul>
<h2 id="1350-2024-10-17">13.5.0 (2024-10-17)</h2>
<ul>
<li>Added support for reading and writing Resource Role Assignments for Primavera schedules. The <code>Resource.getRoleAssignments()</code> method retrieves a map representing the roles a resource is assigned to, along with the skill level for each assignment. The <code>Resource.addRoleAssignment()</code> and <code>Resource.removeRoleAssignment()</code> methods allow role assignments to be added and removed.</li>
<li>Added support for the Resource Primary Role attribute, which is read from and written to Primavera schedules.</li>
<li>Improve handling Boolean attributes with default values when reading XER files.</li>
<li>Added the <code>getShowStartText</code>, <code>getShowFinishText</code> and <code>getShowDurationText</code> methods to the <code>Task</code> class. When working with manually scheduled tasks in Microsoft Project, users can potentially supply arbitrary text for the Start, Finish and Duration attributes. Microsoft Project still stores appropriate values for these attributes, which can be accessed in MPXJ as Start, Finish and Duration, but where the user has supplied text, these attributes are available as Start Text, Finish Text, and Duration Text. The methods added by this change allow the caller to determine which version of each attribute should be shown to the user in order to replicate what they see in Microsoft Project.</li>
</ul>
<h2 id="1342-2024-10-08">13.4.2 (2024-10-08)</h2>
<ul>
<li>Added the <code>ProjectCalendarDays.getCalendarHours()</code> method to allow direct access to the <code>ProjectCalendarHours</code> instances for each day of the week.</li>
</ul>
<h2 id="1341-2024-10-07">13.4.1 (2024-10-07)</h2>
<ul>
<li>Added the <code>ProjectCalendarDays.getCalendarDayTypes()</code> method to allow direct access to the <code>DayType</code> instances for each day of the week.</li>
</ul>
<h2 id="1340-2024-09-18">13.4.0 (2024-09-18)</h2>
<ul>
<li>Added support for reading and writing resource shifts for P6 schedules.</li>
<li>Ensure the Scheduling Progressed Activities project property is populated when reading Phoenix schedules.</li>
<li>When reading milestones from an Asta schedule, ensure that the Activity Type attribute is populated to allow start milestones and finish milestones to be differentiated.</li>
<li>Fix an issue which occurred when writing MSPDI files with manually scheduled tasks starting on non-working days where their timephased data is split as days.</li>
</ul>
<h2 id="1331-2024-08-30">13.3.1 (2024-08-30)</h2>
<ul>
<li>Handle duplicate custom field value unique IDs when reading MSPDI files.</li>
<li>Handle missing remaining early start date when reading timephased data from a P6 schedule.</li>
</ul>
<h2 id="1330-2024-08-22">13.3.0 (2024-08-22)</h2>
<ul>
<li>When reading multiple Primavera schedules from the same source, MPXJ now ensures that instances of activity code definitions, user defined field definitions, locations, units of measure, expense categories, cost accounts, work contours, and notes topics are shared across projects. This will allow you to, for example, filter tasks from multiple projects using a <code>Location</code> instance. Previously each project had its own independent instances for each of these types, which could not be used across multiple projects.</li>
<li>When reading Powerproject schedules, ensure that the Activity ID attribute for WBS entries is populated using Powerproject's Unique Task ID attribute.</li>
<li>Add support for reading timephased planned work from MPP files for manually scheduled tasks (Contributed by Fabian Schmidt).</li>
</ul>
<h2 id="1322-2024-08-14">13.2.2 (2024-08-14)</h2>
<ul>
<li>Add missing constructors to <code>TimephasedCost</code> and <code>TimephasedWork</code> in MPXJ.Net.</li>
</ul>
<h2 id="1321-2024-08-13">13.2.1 (2024-08-13)</h2>
<ul>
<li>Make the MPXJ.Net assembly strong named.</li>
</ul>
<h2 id="1320-2024-08-12">13.2.0 (2024-08-12)</h2>
<ul>
<li>Implemented the <code>UserDefinedField.Builder</code> class.</li>
<li>Marked the <code>UserDefinedField</code> constructor as deprecated. Use the builder class instead.</li>
<li>Marked the <code>UserDefinedField.setDataType()</code> method as deprecated. Use the builder class instead.</li>
<li>Updated to address an issue when writing XER files where a project does not have an explicit Unique ID value, and there are project UDF values.</li>
<li>Added the convenience method <code>ActivityCode.addValue</code> to make it easier to add a value to an activity code.</li>
</ul>
<h2 id="1310-2024-07-26">13.1.0 (2024-07-26)</h2>
<ul>
<li>Updated to POI 5.3.0</li>
<li>Add support for reading and writing timephased data for activities in P6 schedules which have a "manual" curve. (Note: MPXJ does not currently support translating timephased data between different applications, so timephased data read from an MPP file won't be written to a P6 schedule and vice versa).</li>
<li>Add an attribute to the <code>ResourceAssignment</code> class to represent timephased planned work. This is read from/written to P6 as Budgeted Work.</li>
<li>Update Phoenix schemas to ensure that cost types are represented as doubles.</li>
<li>Updated to avoid reading apparently invalid resources from Project Commander files.</li>
<li>Correct the <code>Finish</code> attribute for resource assignments when reading PMXML files.</li>
<li>Improve accuracy of the <code>RemainingDuration</code> attribute for resource assignments when writing PMXML files.</li>
<li>Improve recognition of non-working days when reading calendars certain PMXML files.</li>
<li>Add support for the Resource Assignment field Remaining Units. (Note: this field defaults to the same value as Units if it is not explicitly populated).</li>
<li>Ensure the Resource Assignment field Remaining Units is read from and written to P6 schedules.</li>
<li>Improve handling of invalid calendar exception data when reading P6 schedules from XER files or a P6 database.</li>
<li>Improve the implementation of the Unique ID sequence generator used by MPXJ to avoid issues when multiple classloaders are used.</li>
<li>Deprecated the original <code>StructuredNotes</code> constructor. A new version of the constructor takes an additional <code>ProjectFile</code> argument.</li>
<li>Deprecated the original <code>UserDefinedField</code> constructor. A new version of the constructor takes an additional <code>ProjectFile</code> argument.</li>
<li>Add support for reading and writing the Project Website URL attribute for P6 schedules.</li>
<li>Add support for the Notes attribute as part of the <code>ProjectProperties</code> class.</li>
<li>Ensure that project notes are read from and written to PMXML files.</li>
<li>Usability improvements to the Notes class hierarchy to make it easier to update notes.</li>
<li>Improvements to notes handling when writing PMXML files to make it easier to construct structured notes using plain text.</li>
</ul>
<h2 id="1302-2024-07-08">13.0.2 (2024-07-08)</h2>
<ul>
<li>When writing XER files, provide a default value for the Resource ID if it is not populated.</li>
</ul>
<h2 id="1301-2024-07-04">13.0.1 (2024-07-04)</h2>
<ul>
<li>For XER files, ignore the "null" resource when writing resource rates.</li>
<li>When reading MPP files, ensure that Enterprise Custom Field Unique IDs are unique across entities.</li>
</ul>
<h2 id="1300-2024-06-20">13.0.0 (2024-06-20)</h2>
<ul>
<li>NOTE: this is a major release containing breaking changes. When updating from a 12.x release it is recommended that you first update to the most recent 12.x release and deal with any deprecation warnings before moving to this release.</li>
<li>NOTE: the <a href="https://www.nuget.org/packages?q=net.sf.mpxj">original <code>net.sf.mpxj</code> NuGet packages</a> are now deprecated and will be replaced by the <a href="https://www.nuget.org/packages/MPXJ.Net">MPXJ.Net NuGet Package</a> in the next major MPXJ release. The <code>net.sf.mpxj</code> packages will continue to be maintained until then, at which point they will no longer be distributed. Please migrate your code to use MPXJ.Net at the earliest opportunity, and open an issue in the GitHub issue tracker if you encounter problems.</li>
<li>Updated to use JAXB3. Among other things this change ensures compatibility with Spring Boot 3. Note that this may be a breaking change for you if you own application uses JAXB2.</li>
<li>When reading P6 schedules, the custom properties (as retrieved using <code>ProjectProperties.getCustomProperties</code>) will no longer contain scheduling options. These are now all available as attributes of the <code>ProjectProperties</code> class.</li>
<li>Removed redundant <code>setUniqueID</code> methods from immutable objects. These previously threw <code>UnsupportedOperationException</code> when called.</li>
<li>The <code>ProjectEntityWithUniqueID</code> interface no longer contains the <code>setUniqueID</code> method. Entities with a mutable Unique ID attribute now implement the <code>ProjectEntityWithMutableUniqueID</code> interface, which inherits from the <code>ProjectEntityWithUniqueID</code> interface.</li>
<li>The <code>MSPDIReader</code> and <code>PrimaveraXERFileReader</code> classes no longer provide getter and setter methods for <code>Encoding</code>, use the <code>Charset</code> getter and setter methods instead.</li>
<li>Removed the <code>XerFieldType</code> class and replaced usages of it with the <code>DataType</code> class.</li>
<li>The deprecated <code>ActivityCode()</code> constructor and <code>addValue</code> method have been removed.</li>
<li>The deprecated <code>ActivityCodeValue()</code> constructor and <code>setParent</code> method have been removed.</li>
<li>The deprecated <code>CostAccount()</code> constructor and <code>getDescription</code> method have been removed.</li>
<li>The deprecated <code>CustomFieldValueItem</code> methods <code>getParent</code> and <code>setParent</code> have been removed.</li>
<li>The deprecated <code>ExpenseCategory()</code> constructor has been removed.</li>
<li>The deprecated <code>ExpenseItem(Task)</code> constructor and all setter methods have been removed.</li>
<li>The deprecated <code>JsonWriter</code> methods <code>setEncoding</code> and <code>getEncoding</code> have been removed.</li>
<li>The deprecated <code>Location.Builder()</code> constructor has been removed.</li>
<li>The deprecated <code>NotesTopic()</code> constructor has been removed.</li>
<li>The deprecated <code>ObjectSequence</code> method <code>reset</code> has been removed.</li>
<li>The deprecated <code>PlannerWriter</code> methods <code>setEncoding</code> and <code>getEncoding</code> have been removed.</li>
<li>The deprecated <code>PrimaveraXERFileWriter</code> method <code>setEncoding</code> has been removed.</li>
<li>The deprecated <code>ProjectCalendar</code> method <code>getDate</code> has been removed.</li>
<li>The deprecated <code>ProjectCalendarHelper</code> method <code>getExpandedExceptionsWithWorkWeeks</code> has been removed.</li>
<li>The deprecated <code>ProjectEntityContainer</code> methods <code>getNextUniqueID</code>, <code>renumberUniqueIDs</code> and <code>updateUniqueIdCounter</code> have been removed.</li>
<li>The deprecated <code>ProjectFile</code> methods <code>expandSubprojects</code> and <code>updateUniqueIdCounters</code> have been removed.</li>
<li>The deprecated <code>ProjectReader</code> method <code>setProperties</code> and <code>setCharset</code> have been removed.</li>
<li>The deprecated <code>ProjectWriterUtility</code> class has been removed.</li>
<li>The deprecated <code>RateHelper</code> methods accepting a <code>ProjectFile</code> argument have veen removed.</li>
<li>The deprecated <code>Relation(Task,Task,RelationType,Duration)</code> constructor has been removed.</li>
<li>The deprecated <code>RelationContainer.addPredecessor(Task,Task,RelationType,Duration)</code> method has been removed</li>
<li>The deprecated <code>Resource</code> methods <code>setAvailableFrom</code>, <code>setAvailableTo</code>, <code>setMaterialLabel</code> and <code>setMaxUnits</code> have been removed.</li>
<li>The deprecated <code>ResourceAssignment</code> method <code>getCalendar</code> has been removed.</li>
<li>The deprecated <code>Step(Task)</code> constructor and all setter methods have been removed.</li>
<li>The deprecated <code>Task</code> method <code>addPredecessor(Task,RelationType,Duration)</code> has been removed</li>
<li>The deprecated <code>TimephasedUtility</code> methods <code>segmentBaselineWork(ProjectFile, ...)</code> and <code>segmentBaselineCost(ProjectFile, ...)</code> methods have been removed.</li>
<li>The deprecated <code>UnitOfMeasure.Builder()</code> constructor has been removed.</li>
</ul>
<h2 id="12103-2024-06-14">12.10.3 (2024-06-14)</h2>
<ul>
<li>Add new project property <code>IsProjectBaseline</code>. When using the <code>readAll</code> method to retrieve a set of schedules, if the data source contains both schedules and baselines this property will be true for the <code>ProjectFile</code> instances which represent a baseline.</li>
</ul>
<h2 id="12102-2024-06-03">12.10.2 (2024-06-03)</h2>
<ul>
<li>Added a missing unique ID mapping when writing resource assignment resource unique IDs to MSPDI files (Contributed by Alex Matatov)</li>
<li>Handle null field type when reading outline code values from an MPP9 file.</li>
</ul>
<h2 id="12101-2024-05-22">12.10.1 (2024-05-22)</h2>
<ul>
<li>Ignore missing <code>PropertySet</code>s when reading MPP files (Contributed by Fabian Schmidt).</li>
<li>Corrected handling of the "24 Hour Calendar" Relationship Lag Calendar setting when reading and writing XER files (Based on a contribution by Alex Matatov)</li>
</ul>
<h2 id="12100-2024-05-13">12.10.0 (2024-05-13)</h2>
<ul>
<li>When a baseline is added using one of the <code>ProjectFile.setBaseline</code> methods, ensure that the relevant baseline date is set in <code>ProjectProperties</code>.</li>
<li>Marked the <code>JsonWriter</code> methods <code>setEncoding</code> and <code>getEncoding</code> as deprecated, use <code>setCharset</code> and <code>getCharset</code> instead.</li>
<li>Marked the <code>PlannerWriter</code> methods <code>setEncoding</code> and <code>getEncoding</code> as deprecated, use <code>setCharset</code> and <code>getCharset</code> instead.</li>
<li>Marked the <code>PrimaveraXERFileWriter</code> method <code>setEncoding</code> as deprecated, use <code>setCharset</code> instead.</li>
<li>Marked the <code>ProjectCalendarHelper</code> method <code>getExpandedExceptionsWithWorkWeeks</code> as deprecated, use <code>ProjectCalendar.getExpandedCalendarExceptionsWithWorkWeeks</code> instead.</li>
<li>Marked the <code>ProjectReader</code> method <code>setCharset</code> as deprecated. Readers which support setting the Charset now implement the <code>HasCharset</code> interface, which includes Charset getter and setter methods.</li>
<li>Implemented the <code>UniversalProjectWriter</code> class. This complements the <code>UniversalProjectReader</code> class by providing a simple way for MPXJ users to write project files without having to be concerned with details of the individual <code>ProjectWriter</code> classes. This is intended to replace the <code>ProjectWriterUtility</code> class. Note that the <code>ProjectWriterUtility</code> has a somewhat brittle mechanism to determine the output file format from the supplied output file name. This is not replicated by <code>UniversalProjectWriter</code>, users are expected to provide their own code to determine the appropriate file format.</li>
<li>Marked the <code>ProjectWriterUtility</code> class as deprecated.</li>
</ul>
<h2 id="1293-2024-04-24">12.9.3 (2024-04-24)</h2>
<ul>
<li>Improve handling of non-standard timestamp formats in XER files.</li>
</ul>
<h2 id="1292-2024-04-19">12.9.2 (2024-04-19)</h2>
<ul>
<li>Ensure calendars in Asta schedules have the correct name.</li>
<li>Improve assignment of calendars to summary tasks when reading Asta schedules.</li>
<li>Preserve calendar hierarchy when reading Asta schedules.</li>
</ul>
<h2 id="1291-2024-04-17">12.9.1 (2024-04-17)</h2>
<ul>
<li>Fix an issue where <code>UniversalProjectReader</code> would raise an exception when handling an unknown file type.</li>
<li>Ensure that resource type is included as part of the resource assignment data when writing PMXML files.</li>
</ul>
<h2 id="1290-2024-04-11">12.9.0 (2024-04-11)</h2>
<ul>
<li>Updated <code>UniversalProjectReader</code> to add <code>getProjectReaderProxy</code> methods to allow access to the instance of the reader class which will be used to read a schedule, prior to the schedule being read. This will allow the reader to be configured, or schedule to be ignored without reading its content.</li>
<li>Deprecated the <code>ProjectReader.setProperties</code> method. This method was originally implemented to allow settings to be passed to reader classes when using <code>UniversalProjectReader</code>. You can now use <code>UniversalProjectReader.getProjectReaderProxy</code> to achieve this.</li>
<li>Add <code>from</code> method to all <code>Builder</code> classes to allow initialisation from existing objects.</li>
<li>The <code>CostAccount.Builder</code> class now provides two <code>notes</code> methods to allow formatted or unformatted notes to be added to cost accounts.</li>
<li>The <code>CostAccount</code> method <code>getDescription()</code> has been marked as deprecated. Use the <code>getNotes()</code> or <code>getNotesObject()</code> method instead.</li>
<li>The <code>CustomFieldValueItem</code> methods <code>getParent</code> and <code>setParent</code> have been marked as deprecated. Use the <code>getParentUniqueID</code> and <code>setParentUniqueID</code> methods instead.</li>
<li>JSON output from MPXJ now includes more detail for custom field definitions read from MPP files.</li>
<li>When reading a PMXML file, populate the Early/Late Start/Finish date attributes from the Remaining Early/Late Start/Finish date attributes.</li>
<li>Fix an issue reading WBS ID for P3 and SureTrak schedules.</li>
</ul>
<h2 id="1281-2024-03-11">12.8.1 (2024-03-11)</h2>
<ul>
<li>Improve reading resource assignments from certain FastTrack FTS files.</li>
</ul>
<h2 id="1280-2024-03-04">12.8.0 (2024-03-04)</h2>
<ul>
<li>Add experimental support for reading Deltek Open Plan BK3 files.</li>
<li>Implemented the <code>Relation.Builder</code> class.</li>
<li>Marked the <code>Relation(Task,Task,RelationType,Duration)</code> constructor as deprecated, use the <code>Relation.Builder</code> class instead.</li>
<li>Marked the <code>RelationContainer.addPredecessor(Task,Task,RelationType,Duration)</code> method as deprecated, use the <code>RelationContainer.addPredecessor(Relation.Builder)</code> method instead.</li>
<li>Marked the <code>Task.addPredecessor(Task,RelationType,Duration)</code> method as deprecated, use the <code>Task.addPredecessor(Relation.Builder)</code> method instead.</li>
<li>Add a notes attribute to the <code>Relation</code> class and ensure that it is read from and written to P6 schedules.</li>
<li>Read the Relationship Lag Calendar setting from Phoenix 5 files. (Contributed by Rohit Sinha)</li>
<li>Don't write a material label to an MSPDI file for a resource which isn't a material.</li>
<li>Update representation of Work Variance when writing MSPDI files to more closely match output from Microsoft Project.</li>
<li>Updated to ensure that when schedules are read from XER files or P6 databases, labor and nonlabor work amounts are combined for the Actual, Remaining and Planned work attributes. This is now consistent with the existing behavior when reading PMXML files.</li>
<li>Added support for new Task attributes Actual Work Labor, Actual Work Nonlabor, Remaining Work Labor, Remaining Work Nonlabor, Planned Work Labor, Planned Work Nonlabor, when reading and writing P6 schedules.</li>
<li>Update default <code>readAll</code> method on reader classes to ensure that if the reader is unable to read any schedule data, an empty list is returned rather than a list containing <code>null</code>.</li>
<li>Ensure that Task Start and Finish dates are both the same when reading milestones from PMXML files, and that the correct date is used depending on whether we have a Start Milestone or a Finish Milestone.</li>
</ul>
<h2 id="1270-2024-02-07">12.7.0 (2024-02-07)</h2>
<ul>
<li>Added support for reading and writing the project property Baseline Calendar Name to and from MPP and MSPDI files.</li>
<li>Ensure Start Variance and Finish Variance are read from and written to MSPDI files in the correct format.</li>
<li>Improve accuracy of large Work Variance values read from MSPDI files.</li>
<li>Add support for the Calendar GUID attribute, which is read from MPP and MSPDI files, and written to MSPDI files.</li>
<li>Ensure Activity Codes are available when reading Phoenix PPX files even if they are also being used to construct the task hierarchy.</li>
<li>Ensure Activity Codes Values are populated when reading Phoenix PPX files. (Contributed by Rohit Sinha)</li>
<li>When writing an MSPDI file, derive the TimephasedData Unit attribute from the duration of the timephased data item.</li>
<li>Fixed an issue with the <code>ProjectCalendar.getPreviousWorkFinish</code> method when called with a time which was already at the end of a period of work.</li>
<li>Ensure that the <code>proj_node_flag</code> is set for the root WBS node when writing XER files.</li>
</ul>
<h2 id="1260-2024-01-22">12.6.0 (2024-01-22)</h2>
<ul>
<li>Updated PMXML schema to version 23.12.</li>
<li>Ensure that baselines in PMXML files written by Oracle Primavera Cloud are read.</li>
<li>Fix an issue reading certain XER files and P6 databases where activities lost the relationship with their parent WBS entry.</li>
<li>Added <code>ResourceAssignment.getEffectiveCalendar</code> method.</li>
<li>Deprecated <code>ResourceAssignment.getCalendar</code> method, use <code>getEffectiveCalendar</code> method instead.</li>
<li>Improved reading timephased baseline work from MPP files.</li>
<li>Added new versions of the <code>TimephasedUtility.segmentBaselineWork</code> and <code>segmentBaselineCost</code> methods which take a <code>ProjectCalendar</code> instance as the first argument rather than a <code>ProjectFile</code> instance.</li>
<li>Deprecated the <code>TimephasedUtility.segmentBaselineWork</code> and <code>segmentBaselineCost</code> methods which take a <code>ProjectFile</code> instance as the first argument.</li>
<li>Added a new version of the <code>ProjectCalendar.getDate()</code> method which just takes a date and a duration as its arguments. This method handles both positive and negative durations.</li>
<li>Marked the original version of the <code>ProjectCalendar.getDate()</code> method as deprecated. Use the new version instead.</li>
<li>Improve recognition of task splits when reading MPP and MSPDI files.</li>
</ul>
<h2 id="1250-2023-12-18">12.5.0 (2023-12-18)</h2>
<ul>
<li>Add support for the following Resource Assignment attributes: Remaining Early Start, Remaining Early Finish, Remaining Late Start, and Remaining Late Finish.</li>
<li>Ensure that the Resource Assignment attributes Remaining Early Start and Remaining Early Finish are read from and written to PMXML files.</li>
<li>Ensure that the Resource Assignment attributes Remaining Early Start, Remaining Early Finish, Remaining Late Start, and Remaining Late Finish are read from and written to XER files.</li>
<li>Improve accuracy of reading and writing the <code>ProjectProperties</code> Relationship Lag Calendar attribute for PMXML files.</li>
<li>All P6 scheduling and leveling options which were previously made available via the <code>ProjectProperties</code> custom properties map are now deprecated. These properties now have individual getter and setter methods available on the <code>ProjectProperties</code> class. Note: this may be a breaking change if you were creating schedules from scratch, populating the custom properties map, then writing PMXML or XER files. In this case you will need to update your code, for all other use cases your code will continue to work unchanged until the next major version of MPXJ.</li>
<li>Added support for reading and writing the <code>ProjectProperties</code> attributes Baseline Type Name, Baseline Type Unique ID, and Last Baseline Update Date for baseline projects in PMXML files.</li>
<li>When reading projects from PMXML files, if the creation date attribute is not present in the file fall back to populating the <code>ProjectProperties</code> creation date attribute with the PMXML date added attribute.</li>
<li>When writing PMXML files, ensure the date added attribute for projects is populated with the creation date.</li>
<li>Add the <code>CustomFieldContainer.remove</code> method to allow field configurations to be removed.</li>
<li>Updated the <code>UserDefinedFieldContainer.remove</code> method to ensure that any associated field configuration is removed from the <code>CustomFieldContainer</code>.</li>
<li>Ensure that Microsoft Project's "unknown" resource (with Unique ID zero) is not exported to XER files.</li>
<li>Ensure that resource assignments which are not associated with an Activity or a Resource are not written to XER files.</li>
<li>Durations are written to PMXML files in hours. We now round to 2 decimal places to allow minutes to be represented, and avoid unnecessary precision.</li>
<li>Currency amounts written to PMXML files are now rounded to 8 decimal places to more closely match the behavior of P6, and avoid unnecessary precision.</li>
<li>Decimal amounts other than currency and duration are written to PMXML files with 15 decimal places to more closely match the behavior of P6.</li>
<li>Fix an issue reading ConceptDraw calendars.</li>
<li>Fixed a misspelled field name in the JSON output (Contributed by Daniel Taylor).</li>
<li>Improved handling of the Resource Assignment Planned and Remaining Units and Units per Time attributes read from and written to P6 schedules.</li>
<li>Added support for the following project properties: Activity ID Prefix, Activity ID Suffix, Activity ID Increment and Activity ID Based On Selected Activity, and ensure these are read from and written to P6 schedules.</li>
</ul>
<h2 id="1240-2023-11-23">12.4.0 (2023-11-23)</h2>
<ul>
<li>Added support for the WBS Code Separator attribute to <code>ProjectProperties</code>.</li>
<li>Avoid creating duplicate <code>ActivityCodeValue</code> instances when reading Asta PP files.</li>
<li>Added a new version of the <code>ProjectFile.expandSubprojects</code> method which takes a <code>boolean</code> argument indicating if external tasks should be removed. Passing <code>true</code> to this method will recreate predecessor and successor relationships using the original tasks rather than the placeholder external tasks, and will remove the external tasks.</li>
<li>Marked the <code>ProjectFile.expandSubprojects()</code> method as deprecated, use the new version which takes a <code>boolean</code> argument instead.</li>
<li>Ensure the <code>ProjectProperties</code> name attribute is set correctly when reading XER files and P6 databases.</li>
<li>The <code>ProjectEntityContainer</code> method <code>renumberUniqueIDs</code> has been marked as deprecated.</li>
<li>The <code>ProjectEntityContainer</code> method <code>getNextUniqueID</code> has been marked as deprecated. Use <code>ProjectFile.getUniqueIdObjectSequence(class).getNext()</code> instead.</li>
<li>The <code>ProjectEntityContainer</code> method <code>updateUniqueIdCounter</code> has been marked as deprecated as it is no longer required.</li>
<li>The <code>ProjectFile</code> method <code>updateUniqueIdCounters</code> has been marked as deprecated as it is no longer required.</li>
<li>The <code>ObjectSequence</code> method <code>reset</code> has been marked as deprecated as it is no longer required.</li>
<li>When creating a <code>Location</code> instance using the <code>Builder</code> class, a Unique ID will be generated if one is not supplied.</li>
<li>The no-arg <code>Location.Builder</code> constructor has been marked a deprecated. Use the constructor which requires a <code>ProjectFile</code> instance instead.</li>
<li>Implemented the <code>ExpenseItem.Builder</code> class.</li>
<li>Marked the <code>ExpenseItem(task)</code> constructor as deprecated, use the <code>ExpenseItem.Builder</code> class instead.</li>
<li>Marked all <code>ExpenseItem</code> setter methods a deprecated. The <code>ExpenseItem</code> class will be immutable in the next major release.</li>
<li>Marked no-arg <code>UnitOfMeasure.Builder()</code> constructor as deprecated, use the <code>UnitOfMeasure.Builder(ProjectFile)</code> constructor instead.</li>
<li>Implemented the <code>Step.Builder</code> class.</li>
<li>Marked the <code>Step(task)</code> constructor as deprecated, use the <code>Step.Builder</code> class instead.</li>
<li>Marked all <code>Step</code> setter methods a deprecated. The <code>Step</code> class will be immutable in the next major release.</li>
<li>Marked the <code>NotesTopic</code> constructor as deprecated, use the <code>NotesTopic.Builder(ProjectFile)</code> constructor instead.</li>
<li>Implemented the <code>ExpenseCategory.Builder</code> class.</li>
<li>Marked the <code>ExpenseCategory</code> constructor as deprecated, use the <code>ExpenseCategory.Builder</code> class instead.</li>
<li>Implemented the <code>CostAccount.Builder</code> class.</li>
<li>Marked the <code>CostAccount</code> constructor as deprecated, use the <code>CostAccount.Builder</code> class instead.</li>
<li>Implemented the <code>ActivityCodeValue.Builder</code> class.</li>
<li>Marked the <code>ActivityCodeValue</code> constructor as deprecated, use the <code>ActivityCodeValue.Builder</code> class instead.</li>
<li>Marked the <code>ActivityCodeValue.setParent</code> method as deprecated, use the <code>ActivityCodeValue.Builder</code> class instead.</li>
<li>Marked the <code>ActivityCode.addValue</code> method as deprecated, use the <code>ActivityCodeValue.Builder</code> class instead to create an <code>ActivityCodeValue</code> instance and add it directly to the list held by the parent <code>ActivityCode</code>.</li>
<li>Implemented the <code>ActivityCode.Builder</code> class.</li>
<li>Marked the <code>ActivityCode</code> constructor as deprecated, use the <code>ActivityCode.Builder</code> class instead.</li>
<li>Only predecessor <code>Relation</code> instances are now stored in <code>RelationContainer</code>, successors are generated dynamically. You will only notice a difference if you are iterating over the <code>RelationContainer</code> collection directly, in which case you will only see predecessors.</li>
</ul>
<h2 id="1230-2023-11-07">12.3.0 (2023-11-07)</h2>
<ul>
<li>Retrieve role availability data when reading a schedule from a P6 database.</li>
<li>Populate the project's Name and Title attributes when exporting an MSPDI file.</li>
<li>Ensure the Project ID attribute is populated when writing an XER file.</li>
<li>Don't include null tasks (blank tasks) when writing an XER file.</li>
<li>Strip control characters from entity names written to MSPDI files and XER files.</li>
<li>Ensure resource material labels written to MSPDI files meet Microsoft Project's naming requirements.</li>
<li>Ensure the activity code value Name attribute is populated when read from an Asta PP file.</li>
<li>Don't allow multiple values for an activity code when writing XER and PMXML files.</li>
<li>The MSPDI and MPX writers now dynamically renumber Unique ID values which are too large for Microsoft Project. The original schedule is no longer modified to achieve this.</li>
</ul>
<h2 id="1220-2023-10-12">12.2.0 (2023-10-12)</h2>
<ul>
<li>Add the <code>UnitOfMeasure</code> class to represent the unit of measure for a material resource. The unit of measure corresponds to the current "material label" attribute of a resource. The <code>Resource.getMaterialLabel()</code> method will now retrieve the label from the <code>UnitOfMeasure</code> instance associated with the resource. The <code>Resource.setMaterialLabel()</code> method is now deprecated, the <code>Resource.setUnitOfMeasure()</code> or <code>Resource.setUnitOfMeasureUniqueID()</code> methods should be used instead.</li>
<li>Unit of measure for material resources are now read from and written to Primavera schedules.</li>
<li>Improve task duration and percent completion calculation for Asta PP files.</li>
<li>Improve date parsing when reading XER files written by older versions of P6.</li>
<li>Added the <code>setIgnoreErrors</code> method to the Primavera database reader class, and MSPDI, Schedule Grid, and SDEF file reader classes. The current default behavior of ignoring data type parse errors is unchanged. Calling <code>setIgnoreErrors(false)</code> on one of these reader classes will ensure that an exception is raised when a data type parse error is encountered.</li>
<li>Added the <code>ProjectFile.getIgnoredErrors()</code> method. The default behavior for MPXJ reader classes is to ignore data type parse errors. If any errors have been ignored when reading a schedule, details of these errors can be retrieved by calling the <code>ProjectFile.getIgnoredErrors()</code> method.</li>
<li>Handle duplicate relation unique IDs when reading schedules.</li>
<li>Include resource availability table in JSON output.</li>
<li>Add the Resource field Default Units, and ensure this field is read and written for P6 Schedules.</li>
<li>Updated the Resource attribute Max Units to ensure that this is calculated from the resource's availability table. Note that the <code>Resource.getMaxUnits()</code> method will return the resource's Max Units attribute for the current date. To retrieve the Max Units for a different date, use the <code>AvailabilityTable.getEntryByDate()</code> method.</li>
<li>Marked the <code>Resource.setMaxUnits()</code> method as deprecated. The Max Units attribute is derived from the resource's availability table. Changes to Max Units should now be made by modifying the availability table.</li>
<li>Updated the Resource attribute Available From to ensure that this is calculated from the resource's availability table. Note that the <code>Resource.getAvailableFrom()</code> method will return the resource's Available From attribute for the current date. To retrieve the Available From attribute for a different date, use the <code>AvailabilityTable.availableFrom()</code> method.</li>
<li>Marked the <code>Resource.setAvailableFrom()</code> method as deprecated. The Available From attribute is derived from the resource's availability table. Changes to the Available From attribute  should now be made by modifying the availability table.</li>
<li>Updated the Resource attribute Available To to ensure that this is calculated from the resource's availability table. Note that the <code>Resource.getAvailableTo()</code> method will return the resource's Available To attribute for the current date. To retrieve the Available To attribute for a different date, use the <code>AvailabilityTable.availableTo()</code> method.</li>
<li>Marked the <code>Resource.setAvailableTo()</code> method as deprecated. The Available To attribute is derived from the resource's availability table. Changes to the Available To attribute  should now be made by modifying the availability table.</li>
</ul>
<h2 id="1213-2023-09-25">12.1.3 (2023-09-25)</h2>
<ul>
<li>Added the Project Properties attribute Relationship Lag Calendar and implemented read and write support for this for P6 schedules. (Contributed by Rohit Sinha).</li>
<li>Improve compatibility of PMXML files with P6 EPPM by moving the Schedule Options tag.</li>
<li>Ensure Baseline Projects in PMXML files include Schedule Options and Location Object ID.</li>
</ul>
<h2 id="1212-2023-09-21">12.1.2 (2023-09-21)</h2>
<ul>
<li>Updates to improve compatibility with versions of Java after Java 8.</li>
<li>Ensure timestamps with fractional sections are read correctly from Phoenix PPX files (Based on a contribution by Rohit Sinha).</li>
<li>Improve handling of double quotes when reading and writing XER files.</li>
<li>To allow XER files written by MPXJ to be imported correctly by P6, ensure that they have a single top level WBS entry (Based on a contribution by Alex Matatov)</li>
<li>Ensure that <code>ProjectProperties.getCustomProperties()</code> returns an empty Map rather than returning <code>null</code> if no custom properties have been configured.</li>
<li>Ensure project calendars and project activity codes are nested within the project tag of PMXML files.</li>
</ul>
<h2 id="1211-2023-08-23">12.1.1 (2023-08-23)</h2>
<ul>
<li>Fix an issue preventing native SQLite library from loading when using the .Net version of MPXJ on macOS.</li>
</ul>
<h2 id="1210-2023-08-22">12.1.0 (2023-08-22)</h2>
<ul>
<li>Write schedule options to PMXML and XER files.</li>
<li>Fix an arithmetic error in RateHelper when converting a rate from minutes to hours.</li>
<li>Introduced new methods to RateHelper accepting a <code>TimeUnitDefaultsContainer</code> argument rather than a <code>ProjectFile</code> for greater flexibility. Marked methods taking a <code>ProjectFile</code> argument as deprecated.</li>
<li>Ensure Early Finish and Late Finish are populated for Asta milestones and tasks.</li>
<li>Don't attempt to calculate total slack if start slack or finish slack are missing.</li>
<li>Ensure completed tasks are not marked as critical.</li>
<li>Improve handling of non-standard Boolean values in MPX files.</li>
<li>Improve Total Slack calculation for P6 projects.</li>
<li>Handle finish milestones with <code>null</code> actual start date for actual duration calculation when reading PMXML files (Contributed by Andrew Marks).</li>
</ul>
<h2 id="1202-2023-07-25">12.0.2 (2023-07-25)</h2>
<ul>
<li>Ensure that the Fixed Cost attribute is rolled up from activities to WBS entries when reading P6 schedules.</li>
</ul>
<h2 id="1201-2023-07-21">12.0.1 (2023-07-21)</h2>
<ul>
<li>Improve resource hierarchy handling.</li>
<li>Improve handling of external tasks read from MSPDI files.</li>
<li>Improve handling of resource assignments read from Asta PP files containing multiple baselines.</li>
<li>Improve filtering to ignore hammock tasks in Asta PP files and ensure that non-hammock items are not incorrectly ignored.</li>
<li>Improve handling of bars without additional linked data read from Asta PP files.</li>
<li>Ensure that invalid duplicate Unique ID values encountered when reading schedule data are renumbered to maintain uniqueness.</li>
<li>Improve reading certain FastTrack FTS files.</li>
<li>Roll up the expense item at completion values read from P6 schedules to the task Fixed Cost attribute.</li>
</ul>
<h2 id="1200-2023-06-29">12.0.0 (2023-06-29)</h2>
<ul>
<li>NOTE: this is a major version release, breaking changes have been made to the MPXJ API as documented below.</li>
<li>Timestamps, dates, and times are now represented by <code>java.time.LocalDateTime</code>, <code>java.time.LocalDate</code> and <code>java.time.LocalTime</code> respectively, rather than <code>java.util.Date</code> as they were originally.</li>
<li>For .Net users, new <code>ToDateTime</code> and <code>ToNullableDateTime</code> extension methods have been provided to convert <code>java.time.LocalDateTime</code>, <code>java.time.LocalDate</code>, <code>java.time.LocalTime</code> to <code>DateTime</code> instances.</li>
<li>For .Net users, new <code>ToJavaLocalDateTime</code>, <code>ToJavaLocalDate</code> and <code>ToJavaLocalTime</code> extension methods have been provided to convert <code>DateTime</code> instances to <code>java.time.LocalDateTime</code>, <code>java.time.LocalDate</code>, and <code>java.time.LocalTime</code>.</li>
<li>The class <code>net.sf.mpxj.Day</code> has been replaced by <code>java.time.DayOfWeek</code>.</li>
<li>All code previously marked as deprecated has been removed.</li>
<li>Added support for reading and writing the Activity attribute "Expected Finish" for P6 schedules.</li>
</ul>
<h2 id="1154-2023-06-27">11.5.4 (2023-06-27)</h2>
<ul>
<li>Improve accuracy of dates read from Synchro, Suretrak and Turboproject files.</li>
<li>By default ignore errors in individual records read from XER files. This matches P6's behavior when importing XER files. Use the <code>PrimaveraXERFileReader.setIgnoreErrors</code> method to change the behavior.</li>
</ul>
<h2 id="1153-2023-06-19">11.5.3 (2023-06-19)</h2>
<ul>
<li>When writing an XER file, provide the necessary default values to allow non-P6 schedules to be successfully imported into P6.</li>
<li>Ensure multi-day exceptions are written to XER files correctly.</li>
<li>Ensure GanttProject exception dates are read correctly.</li>
<li>More closely match the Planner predecessor lag calculation.</li>
<li>Ensure that <code>java.sql.Date</code> values are correctly formatted when writing XER files.</li>
<li>When reading from a P6 database, check to ensure the location table is present before attemting to read locations.</li>
</ul>
<h2 id="1152-2023-06-08">11.5.2 (2023-06-08)</h2>
<ul>
<li>Improve accuracy of calendar data read from certain Powerproject schedules.</li>
<li>Improve handling of unusual XER files with calendar time ranges expressed in 12-hour format.</li>
<li>Correctly parse midnight represented as 24:00:00 from MSPDI files written by certain non-Microsoft Project applications.</li>
<li>For MSPDI files produced by applications other than Microsoft Project which have an incorrectly nested calendar hierarchy, avoid pruning derived calendars which are referenced elsewhere in the hierarchy.</li>
</ul>
<h2 id="1151-2023-05-24">11.5.1 (2023-05-24)</h2>
<ul>
<li>Improve read performance when working with large schedules.</li>
<li>Improve read and write performance of code handling resource calendars.</li>
<li>Updated to use sqlite-jdbc 3.42.0.0</li>
</ul>
<h2 id="1150-2023-05-19">11.5.0 (2023-05-19)</h2>
<ul>
<li>Added the ability to read Subproject data embedded in MSPDI files.</li>
<li>Added the ability to read timephased baseline work and cost from MSPDI files.</li>
<li>Added the ability to write timephased baseline work and cost to MSPDI files.</li>
<li>Improve accuracy of timephased baseline work read from MPP files.</li>
<li>Ensure that non-recurring calendar exceptions take precedence over recurring calendar exceptions.</li>
<li>Avoid creating duplicate calendar exceptions when reading Asta PP files.</li>
<li>Added the Bar Name attribute to Task, which is accessed using the <code>getBarName</code> and <code>setBarName</code> methods. This is populated with the name of the bar to which a task belongs when reading an Asta Powerproject schedule.</li>
<li>When reading schedules from XER files and P6 databases, ensure durations without a value are returned as <code>null</code> rather than as a zero duration.</li>
</ul>
<h2 id="1140-2023-05-08">11.4.0 (2023-05-08)</h2>
<ul>
<li>Added the "Resource Pool File" attribute to ProjectProperties, which represents the full path of the resource pool used by an MPP file. This attribute is accessible via the <code>getResourcePoolFile</code> and <code>setResourcePoolFile</code> methods.</li>
<li>Added the <code>getResourcePoolObject</code> method to allow the resource pool file to be located and read</li>
<li>Added support for reading the task attribute Subproject GUID from MPP files. This attribute can be accessed via the <code>getSubprojectGUID</code> and <code>setSubprojectGUID</code> methods.</li>
<li>Added support for the task attribute "External Project". When this attribute is true it indicates that the task represents a subproject. The attribute is accessed via the <code>getExternalProject</code> and <code>setExternalProject</code> methods.</li>
<li>When reading an MSPDI file with external task predecessors, MPXJ now attempts to recreate the placeholder external tasks which would be present if the equivalent MPP file was read.</li>
<li>External task predecessors are now represented when writing an MSPDI file.</li>
<li>Added the Task method <code>getSubprojectObject</code> which allows the caller to retrieve a ProjectFile instance representing the external project linked to a task.</li>
<li>Added the Task method <code>expandSubproject</code>. For task which represent an external project, this method automatically loads the external project and attaches the tasks it contains as children of the current task. This is analogous to the behavior in Microsoft Project where a subproject is expanded to reveal the tasks it contains.</li>
<li>Added the ProjectFile method <code>expandSubprojects</code> which identifies any tasks in the project which represent an external project and expands them, linking the tasks from the external project as children of the task in the parent project. Note that the method works recursively so multiple levels of external tasks will be expanded.</li>
<li>Updated to ensure that the <code>internal_name</code> attribute of a <code>UserdefinedField</code> is generated if not present.</li>
<li>Updated to avoid an exception when reading notebook topics from PMXML files.</li>
<li>Marked the Task method <code>setSubprojectName</code> as deprecated. Use the <code>setSubProjectFile</code> method instead.</li>
<li>Marked the Task method <code>getSubprojectName</code> as deprecated. Use <code>getSubprojectFile</code> instead.</li>
<li>Marked the Task method <code>setExternalTaskProject</code> as deprecated. Use the <code>setSubprojectFile</code> method instead.</li>
<li>Marked the Task method <code>getExternalTaskProject</code> as deprecated. Use the <code>getSubprojectFile</code> method instead.</li>
<li>Marked the ProjectFile method <code>getSubProjects</code> as deprecated. Use the subproject attributes on individual tasks instead.</li>
<li>Marked the Task methods <code>getSubProject</code> and <code>setSubProject</code> as deprecated. Use the subproject attributes instead.</li>
</ul>
<h2 id="1132-2023-04-29">11.3.2 (2023-04-29)</h2>
<ul>
<li>Improve default values provided for P6 calendars with missing data.</li>
<li>Implement both "planned dates" and "current dates" strategies for populating P6 baselines.</li>
<li>Ensure the Project GUID is read from MPP files.</li>
</ul>
<h2 id="1131-2023-04-21">11.3.1 (2023-04-21)</h2>
<ul>
<li>Improve accuracy of resource assignment Actual Start and Actual Finish dates when reading MPP files.</li>
<li>Avoid generating timephased data for zero duration tasks.</li>
<li>Improve preservation of custom timephased data start and end times.</li>
</ul>
<h2 id="1130-2023-04-12">11.3.0 (2023-04-12)</h2>
<ul>
<li>Implemented <code>PrimaveraXERFileWriter</code> to allow MPXJ to write XER files.</li>
<li>Updated the <code>ActivityCode</code> class to ensure that both the scope Project ID and EPS ID can be represented when reading a P6 schedule. (Potentially breaking change if you were using this class).</li>
<li>Ensure secondary constraint date and type are written to PMXML files.</li>
<li>Ensure leveling priority is written to PMXML files.</li>
<li>Ensure WBS UDF values are written to PMXML files.</li>
<li>Ensure integer UDF values are read correctly from XER files and P6 databases.</li>
<li>Add methods to allow the project's default calendar unique ID to be set and retrieved.</li>
<li>Add method to allow a calendar's parent calendar unique ID to be retrieved.</li>
<li>Add method to allow a task's parent task unique ID to be retrieved.</li>
<li>Add methods to allow a resource assignment's role unique ID to be set and retrieved.</li>
<li>Add methods to allow a resource assignment's cost account unique ID to be set and retrieved.</li>
<li>Add method to allow a cost account's parent unique ID to be retrieved.</li>
<li>Add method to allow an expense item's cost account unique ID to be retrieved.</li>
<li>Add method to allow an expense item's category unique ID to be retrieved.</li>
<li>Added <code>WorkContour.isDefault()</code> method to allow "built in" resource curves/work contours to be distinguished from user defined curves.</li>
<li>Updated to retrieve the project's start date from Phoenix PPX files (Contributed by Rohit Sinha).</li>
<li>Provide access to notebook topics from P6 schedules via the <code>ProjectFile.getNotesTopics()</code> method.</li>
<li>Capture unique ID of Activity and WBS notes from P6 schedules.</li>
<li>Improve the calculation used to determine At Completion Duration of activities when reading XER files and P6 databases.</li>
<li>Improve representation of certain duration values written to MSPDI files.</li>
<li>Improve accuracy of certain work calculations where the specified time period does not start with a working day.</li>
<li>Fix an issue which caused negative timephased work values to be generated when reading certain MPP files.</li>
<li>Fix an issue reading XER files where the <code>critical_drtn_hr_cnt</code> field is expressed a decimal rather than an integer.</li>
<li>Fix an issue populating the WBS attribute for activities read from certain XER files.</li>
</ul>
<h2 id="1120-2023-03-13">11.2.0 (2023-03-13)</h2>
<ul>
<li>The project property Critical Slack Limit is now represented as a <code>Duration</code> rather than as an <code>Integer</code>. (Potentially breaking change if you were using this property directly).</li>
<li><code>TaskType</code> is now a simple enum with all Microsoft Project specific functionality moved into <code>TaskTypeHelper</code>. (Potentially breaking change if you were using the <code>TaskType</code> methods <code>getInstance</code> or <code>getValue</code> in your code)</li>
<li>When reading the task type from P6 schedule the mapping to the MPXJ <code>TaskType</code> enum has been updated to more closely match P6. The main changes are that the P6 type "Fixed Units" now maps to <code>TaskType.FIXED_WORK</code> and the "Fixed Duration &amp; Units" type now maps to a new enumeration value <code>TaskType.FIXED_DURATION_AND_UNITS</code>.</li>
<li>Added support for reading project calendar exceptions from Phoenix schedules (based on a contribution by Rohit Sinha).</li>
<li>The Resource attribute Active now defaults to true if the schedule being read doesn't support or contain a value for this attribute.</li>
<li>Add support for reading and writing the Resource's Active flag for P6 schedules.</li>
<li>Add support for reading and writing the Resource's Default Units/Time value for P6 schedules.</li>
<li>Add support for reading and writing the Project's Critical Slack Limit value for P6 schedules.</li>
<li>Fixed an issue reading certain types of Enterprise Custom Fields containing date values.</li>
<li>Ensure activity code value parent can be set to null.</li>
<li>Improved existing .Net extension methods and added support for more types.</li>
<li>Added NuGet package icon</li>
<li>Simplified  NuGet packaging</li>
</ul>
<h2 id="1110-2023-02-15">11.1.0 (2023-02-15)</h2>
<ul>
<li>Write activity code definitions and activity code assignments to PMXML files.</li>
<li>Added support for "secure" and "max length" attributes to the <code>ActivityCode</code> class.</li>
<li>Added <code>getChildCodes</code> method to <code>ActivityCode</code> and <code>ActivityCodeValue</code> to make it easier to traverse activity code values hierarchically.</li>
<li>Added <code>setDescription</code> method to <code>Step</code> class to make it simpler to add a plan text description.</li>
</ul>
<h2 id="1100-2023-02-08">11.0.0 (2023-02-08)</h2>
<ul>
<li>User defined fields read from P6, Asta and GanttProject schedules are now represented by instances of <code>UserDefinedField</code>. They will no longer be mapped to custom field instances.</li>
<li>Enterprise Custom Fields read from MPP and MSPDI files are now represented by instances of <code>UserDefinedField</code>.</li>
<li>When writing MSPDI files, UserDefinedField instances which were originally read from enterprise custom fields will be written to the MSPDI file as enterprise custom fields.</li>
<li>When writing MSPDI files, UserDefinedField instances which were from applications other than Microsoft Project will automatically be mapped to available custom fields.</li>
<li>When writing MPX files, UserDefinedField instances will automatically be mapped to available custom fields.</li>
<li>The <code>UserDefinedField</code> type implements the <code>FieldType</code> interface and so can be used with the <code>FieldContainer</code> <code>get</code> and <code>set</code> methods to work with the contents of the user defined fields.</li>
<li>The <code>ProjectFile.getUserDefinedFields()</code> method has been added to provide access to all user defined fields defined in the project.</li>
<li>The <code>CustomFieldContainer</code> returned by <code>ProjectFile.getCustomFields()</code> will contain entries for all <code>UserDefinedField</code> instances.</li>
<li>The various <code>getFieldTypeByAlias</code> and <code>getFieldByAlias</code> methods will retrieve user defined fields by name.</li>
<li>Added the convenience method <code>ProjectFile.getPopulatedFields()</code> to retrieve details of all populated fields across the project. This avoids the caller having to individually retrieve the populated fields from the tasks container, resource container and so on.</li>
<li>Updated the <code>getPopulatedFields</code> methods to return a <code>Set</code> of <code>FieldType</code> rather than a <code>Set</code> of <code>TaskField</code>, <code>ResourceField</code> etc.</li>
<li>The various <code>getPopulatedFields</code> methods will include instances of <code>UserDefinedField</code> in the returned collection if relevant.</li>
<li>All <code>ENTERPRISE_CUSTOM_FIELDn</code> values have been removed from the <code>TaskField</code>, <code>ResourceField</code>, <code>AssignmentField</code> and <code>ProjectField</code> enumerations.</li>
<li>The <code>getEnterpriseCustomField</code> and <code>setEnterpriseCustomField</code> methods have been removed from <code>ProjectProperties</code>, Task<code>,</code>Resource<code>and</code>ResourceAssignment`.</li>
<li>Project UDFs are now read from P6 schedules.</li>
<li>Project UDFs are now written to PMXML files.</li>
<li>All code previously marked as deprecated has been removed.</li>
</ul>
<h2 id="10162-2023-01-29">10.16.2 (2023-01-29)</h2>
<ul>
<li>Updated to improve reading resource attributes from certain MPP14 files.</li>
</ul>
<h2 id="10161-2023-01-26">10.16.1 (2023-01-26)</h2>
<ul>
<li>Updated to make resource curve definitions (work contours) available in the <code>WorkContourContainer</code>. This container is accessed using the <code>ProjectFile.getWorkContours()</code> method.</li>
</ul>
<h2 id="10160-2023-01-24">10.16.0 (2023-01-24)</h2>
<ul>
<li>Improve accuracy when normalising timephased data.</li>
<li>Add support for reading activity steps from XER files, PMXML files and Primavera databases.</li>
<li>Add support for writing activity steps to PMXML files.</li>
<li>Updated PMXML schema to version 22.12.</li>
<li>Updated methods in the <code>GanttBarCommonStyle</code> and <code>GanttBarStyle</code> classes to use a <code>FieldType</code> instance rather than a <code>TaskField</code> instance to allow more flexibility. (Note: this may be a breaking change if you are currently using these classes.)</li>
<li>Optionally include some Microsoft Project layout data in JSON output.</li>
</ul>
<h2 id="10150-2023-01-11">10.15.0 (2023-01-11)</h2>
<ul>
<li>Avoid writing invalid characters to PMXML, MSPDI and Planner XML files.</li>
<li>Improve handling of slack values for schedules which only contain a value for total slack.</li>
<li>Add support for reading constraint type and constraint date from Phoenix schedules (based on a contribution by Rohit Sinha).</li>
<li>Improve timephased data calculation when assignment has zero units.</li>
<li>Improve handling of very large duration values when reading and writing MSPDI files.</li>
<li>Ensure the Task attributes Active, Constraint Type, Task Mode, and Type always have a value.</li>
<li>Ensure the Resource attributes Type, Calculate Costs from Units, and Role always have a value.</li>
<li>Ensure the Resource Assignment attributes Calculate Costs from Units, Rate Index, and Rate Source always have a value.</li>
<li>Add version number constant to the Java source, accessible as <code>MPXJ.VERSION</code>.</li>
<li>Ensure that UDF values are read for WBS entries in PMXML files.</li>
<li>Avoid writing duplicate resource assignments to MPX files.</li>
</ul>
<h2 id="10141-2022-11-25">10.14.1 (2022-11-25)</h2>
<ul>
<li>Fix CVE-2022-41954: Temporary File Information Disclosure Vulnerability (Contributed by Jonathan Leitschuh)</li>
</ul>
<h2 id="10140-2022-11-21">10.14.0 (2022-11-21)</h2>
<ul>
<li>Handle missing default calendar when reading a PMXML file.</li>
<li>When reading an MPP file using a file name or <code>File</code> instance, ensure a more memory-efficient approach is used.</li>
<li>Improve reading certain FastTrack FTS files.</li>
<li>Improve generation of timephased data where working time ends at midnight.</li>
<li>Improve generation of timephased data for tasks with a calendar assigned.</li>
</ul>
<h2 id="10130-2022-11-16">10.13.0 (2022-11-16)</h2>
<ul>
<li>Add support for reading a resource assignment's cost account from P6 schedules.</li>
<li>Add support for writing a resource assignment's cost account to PMXML files.</li>
<li>Read resource assignment custom field definitions present in MPP14 files.</li>
<li>Improve identification of deleted resources when reading MPP9 files.</li>
<li>Ensure tasks with task calendars in MPP files are handled correctly when generating timephased data.</li>
<li>Improve generation of timephased data for material resource assignments.</li>
<li>Improve accuracy of timephased data when reading certain MPP files.</li>
</ul>
<h2 id="10120-2022-11-01">10.12.0 (2022-11-01)</h2>
<ul>
<li>Added the Resource Assignment attribute Calculate Costs From Units, and added read and write support for Primavera schedules.</li>
<li>Added the Resource attribute Calculate Costs From Units, and added read and write support for Primavera schedules.</li>
<li>Added the Resource and Role attribute Sequence Number, and added read and write support for Primavera schedules.</li>
<li>Added the WBS attribute Sequence Number, and added read and write support for Primavera schedules.</li>
<li>Ensure activity type is read from Phoenix schedules. (Contributed by Christopher John)</li>
<li>Deprecate the <code>CostAccount</code> method <code>getSequence</code> and replace with <code>getSequenceNumber</code> to improve naming consistency.</li>
<li>Deprecate the <code>ExpenseCategory</code> method <code>getSequence</code> and replace with <code>getSequenceNumber</code> to improve naming consistency.</li>
<li>Avoid possible ArrayIndexOutOfBoundsException when reading GUID values from MPP files (Contributed by Rohit Sinha).</li>
</ul>
<h2 id="10110-2022-09-27">10.11.0 (2022-09-27)</h2>
<ul>
<li>Deprecated the <code>Resource</code> methods <code>getParentID</code> and <code>setParentID</code>. Replaced with <code>getParentResourceUniqueID</code> and <code>setParentResourceUniqueID</code> for clarity and consistency.</li>
<li>Added the <code>Resource</code> methods <code>setParent</code> and <code>getParent</code>.</li>
<li>Added the <code>ChildResourceContainer</code> interface and <code>ResourceContainer.updateStructure</code> method to ensure that resources can be accessed hierarchically when reading a schedule.</li>
<li>Added the <code>ResourceAssignment</code> methods <code>getFieldByAlias</code> and <code>setFieldByAlias</code> to simplify working with custom fields, and mkae the API consistent with existing methods on <code>Task</code> and <code>Resource</code>.</li>
<li>Added the <code>TaskContainer</code> methods <code>getCustomFields</code> and <code>getFieldTypeByAlias</code> to simplify access to task custom fields.</li>
<li>Added the <code>ResourceContainer</code> methods <code>getCustomFields</code> and <code>getFieldTypeByAlias</code> to simplify access to resource  custom fields.</li>
<li>Added the <code>ResourceAssignmentContainer</code> methods <code>getCustomFields</code> and <code>getFieldTypeByAlias</code> to simplify access to resource assignment custom fields.</li>
<li>Added the <code>getCustomFieldsByFieldTypeClass</code> method to <code>CustomFieldContainer</code> to allow retrieval of custom field details by parent class.</li>
<li>Deprecated the <code>CustomFieldContainer</code> method <code>getFieldByAlias</code> to be replaced by <code>getFieldTypeByAlias</code> to provide a more consistent method name.</li>
<li>Don't attempt to write unknown extended attributes to MSPDI files.</li>
<li>Don't populate graphical indicator data if the graphical indicator is not enabled.</li>
<li>Don't set custom field aliases to empty strings.</li>
<li>Added the <code>CustomFieldContainer</code> method <code>add</code>.</li>
<li>Deprecated the <code>CustomFieldContainer</code> method <code>getCustomField</code>, which is replaced by the <code>get</code> method (which returns <code>null</code> if the field type is not configured) and the <code>getOrCreate</code> method (which will return an existing configuration or create a new one if the requested field does not yet have a configuration).</li>
</ul>
<h2 id="10100-2022-09-13">10.10.0 (2022-09-13)</h2>
<ul>
<li>Add an option to import Phoenix schedules as a flat set of tasks with separate activity codes, rather than creating a hierarchy of tasks from the activity codes. Note the default is to disable this behavior so existing functionality is unchanged. (Contributed by Christopher John)</li>
<li>Add a <code>setProperties</code> method to reader classes to allow configuration to be supplied via a <code>Properties</code> instance rather than having to call setter methods. Properties passed to the <code>UniversalProjectReader</code> version of this method will be forwarded to the reader class <code>UniversalProjectReader</code> chooses to reader the supplied file. Properties for multiple reader classes can be included in the <code>Properties</code> instance, each reader class will ignore irrelevant properties.</li>
<li>Added the <code>get</code> method to <code>Task</code>, <code>Resource</code>, <code>ResourceAssignment</code> and <code>ProjectProperties</code> as a replacement for the <code>getCurrentValue</code> method. The new <code>get</code> method is paired with the existing <code>set</code> method to provide read and write access to attributes of these classes. This change is intended to improve the interfaces to these classes by making them more consistent, and thus easier to understand.</li>
<li>Deprecated the <code>getCurrentValue</code> method on the <code>Task</code>, <code>Resource</code>, <code>ResourceAssignment</code> and <code>ProjectProperties</code> classes. Use the new <code>get</code> method instead.</li>
<li>Add getter and setter methods for the Resource attributes Cost Center, Budget Cost, Budget Work, Baseline Budget Cost, Baseline Budget Work, Baseline Budget Cost 1-10, and Baseline Budget Work 1-10.</li>
<li>Add getter and setter methods for the Task attributes Response Pending, Scheduled Start,  Scheduled Finish, Scheduled Duration, Budget Cost, Budget Work, Baseline Budget Cost, Baseline Budget Work, Baseline Budget Cost 1-10, and Baseline Budget Work 1-10.</li>
<li>Added support for the Resource Cost Centre attribute for MSPDI files.</li>
<li>Move MPP file-specific functionality for determining baseline values from the Task class into the MPP reader class.</li>
<li>Improve handling of the TaskMode attribute.</li>
<li>Don't set a Task's Critical attribute unless we have valid slack values.</li>
<li>Ensure <code>ResourceAssignment</code> calculated fields are returned correctly when using the <code>getCurrentValue</code> method.</li>
<li>Ensure <code>ProjectProperties</code> calculated fields are returned correctly when using the <code>getCurrentValue</code> method.</li>
<li>Updated to use jsoup 1.15.3</li>
</ul>
<h2 id="1091-2022-08-31">10.9.1 (2022-08-31)</h2>
<ul>
<li>Ensure monthly and yearly recurrences are calculated correctly when the supplied start date is the same as the first recurrence date (Contributed by Rohit Sinha).</li>
<li>Add support for reading task calendars from Phoenix files (Contributed by Rohit Sinha).</li>
<li>Improve reliability of ProjectCleanUtility when using the replacement strategy.</li>
</ul>
<h2 id="1090-2022-08-23">10.9.0 (2022-08-23)</h2>
<ul>
<li>Added the <code>ResourceAssignment.getEffectiveRate</code> method to allow the cost rate effective on a given date for a resource assignment to be calculated. For P6 schedules this will take account of the cost rate configuration included as part of the resource assignment.</li>
<li>For P6 schedules, the <code>ResourceAssignment.getCostRateTable</code> method now takes in account any cost rate configuration details from the resource assignment when determining which table to return.</li>
<li>A resource's Standard Rate, Overtime Rate and Cost per Use are now all derived from the resource's cost rate table, and not stored as attributes of the resource itself.</li>
<li>The resource methods <code>setStandardRate</code>, <code>setOvertimeRate</code>, and <code>setCostPerUse</code> have been deprecated. These attributes can now only be set or updated by modifying the resource's cost rate table.</li>
<li>When writing MPX files, only include attributes which have a non-empty, non-default value in at least one task or resource.</li>
<li>When writing MPX files, ensure attributes which have calculated values are used.</li>
<li>Add support for reading a resource assignment's rate type from P6 schedules. The rate type is accessed via the <code>ResourceAssignment.getRateIndex</code> method. The value returned by this method can be used to select the required rate using the <code>CostRateTableEntry,getRate</code> method.</li>
<li>Add support for writing a resource assignment's rate type to PMXML files.</li>
<li>Add support for reading a resource assignment's role from P6 schedules. The role is accessed via the <code>ResourceAssignment.getRole</code> and <code>ResourceAssignment.setRole</code> methods.</li>
<li>Add support for writing a resource assignment's role to PMXML files.</li>
<li>Add support for reading a resource assignment's override rate (Price / Unit) from P6 schedules. The rate is accessed via the <code>ResourceAssignment.getOverrideRate</code> and <code>ResourceAssignment.setOverrideRate</code> methods.</li>
<li>Add support for writing a resource assignment's override rate (Price / Unit) to PMXML files.</li>
<li>Add support for reading a resource assignment's rate source from P6 schedules. The rate source is accessed via the <code>ResourceAssignment.getRateSource</code> and <code>ResourceAssignment.setRateSource</code> methods.</li>
<li>Add support for writing a resource assignment's rate source to PMXML files.</li>
</ul>
<h2 id="1080-2022-08-17">10.8.0 (2022-08-17)</h2>
<ul>
<li>When reading P6 schedules, all five cost rates for a resource are now available via the <code>CostRateTableEntry.getRate</code> method.</li>
<li>All five rates from each cost rate table entry can now be written to PMXML files.</li>
<li>When reading files written by Microsoft Project, resource rate values now use the same units as seen by the end user rather than defaulting to hours as was the case previously. (For example, if the user sees $8/day in the source application, you will receive a Rate instance of $8/day rather than $1/hr).</li>
<li>The values for a resource's standard rate, overtime rate, and cost per use attributes are now derived from the cost rate table. The values stored on the resource itself are only used if a cost rate table for the resource is not present.</li>
<li>The Resource methods <code>getStandardRateUnits</code> and <code>getOvertimeRateUnits</code> are deprecated. Use the <code>getStandardRate</code> and <code>getOvertimeRate</code> methods to retrieve a <code>Rate</code> instance which will include the units for these rates.</li>
<li>The Resource methods <code>setStandardRateUnits</code> and <code>setOvertimeRateUnits</code> are deprecated. Supply <code>Rate</code> instances to the <code>setStandardRate</code> and <code>setOvertimeRate</code> methods with the required units instead.</li>
<li>The CostRateTableEntry methods <code>getStandardRateUnits</code> and <code>getOvertimeRateUnits</code> are deprecated. Use the <code>getStandardRate</code> and <code>getOvertimeRate</code> methods to retrieve a <code>Rate</code> instance which will include the units for these rates.</li>
<li>Ensure rates are formatted "per hour" when writing MSPDI and PMXML files.</li>
<li>Include cost rate tables in JSON output.</li>
</ul>
<h2 id="1070-2022-08-09">10.7.0 (2022-08-09)</h2>
<ul>
<li>Use Jackcess to read Asta MDB and Microsoft Project MPD files. This allows these file to be read on platforms other than Windows.</li>
<li>Improve support for reading correctly typed values for enterprise custom fields from MPP files.</li>
<li>Improve array index validation when reading GUID values from MPP files.</li>
</ul>
<h2 id="1062-2022-06-29">10.6.2 (2022-06-29)</h2>
<ul>
<li>Ensure <code>ProjectCleanUtility</code> can load dictionary words from distribution jar.</li>
<li>Improve handling of calendars without days read from PMXML files.</li>
</ul>
<h2 id="1061-2022-06-14">10.6.1 (2022-06-14)</h2>
<ul>
<li>Updated to use POI 5.2.2</li>
<li>Updated to use sqlite-jdbc 3.36.0.3</li>
<li>Updated to use jsoup 1.15.1</li>
</ul>
<h2 id="1060-2022-06-08">10.6.0 (2022-06-08)</h2>
<ul>
<li>Added support for reading and writing the unique ID of P6 user defined fields via new <code>getUniqueID</code> and <code>setUniqueID</code> methods on `CustomField (based on a suggestion by Wes Lund).</li>
<li>Added support for reading and writing scope, scope ID, and sequence number attributes for activity codes (based on a suggestion by Wes Lund).</li>
<li>Added support for reading and writing sequence number and color attributes for activity code values (based on a suggestion by Wes Lund).</li>
<li>Added <code>isWorking</code> method to <code>ProjectCalendarException</code> to make it clearer how to determine if the exception changes the dates it is applied to into working or non-working days.</li>
<li>Improve reading task start from certain Planner files.</li>
<li>Improve reading predecessor lag values from Planner files.</li>
<li>Ensure calendar hierarchy is written correctly to Planner files.</li>
<li>Don't write null tasks to Planner files as Planner will not read files which contain them.</li>
<li>When writing Planner file, ignore constraint types which Planner can't represent.</li>
<li>Don't write emply predecessor lists to Planner files.</li>
<li>Improve handling of lag duration when writing Planner files.</li>
<li>Improve ProjectCalendar start date calculation when we have long runs of non-working days.</li>
<li>Performance enhancement for timephased data normalisation.</li>
</ul>
<h2 id="1050-2022-05-24">10.5.0 (2022-05-24)</h2>
<ul>
<li>The <code>ProjectCalendarWeek</code> methods <code>addCalendarHours()</code>, <code>attachHoursToDay</code>, <code>removeHoursFromDay</code> have been removed. Use <code>addCalendarHours(day)</code>, <code>removeCalendarHours(day)</code> instead. (Note: this will be a breaking change if you were using the original methods to create or modify a schedule)</li>
<li>The <code>ProjectCalendar</code> methods <code>attachHoursToDay</code> and <code>removeHoursFromDay</code> have been removed. Use the <code>addCalendarHours</code> and <code>removeCalendarHours</code> methods instead. (Note: this will be a breaking change if you were using the original methods to create or modify a schedule)</li>
<li>The class hierarchy for <code>ProjectCalendarHours</code> and <code>ProjectCalendarException</code> has been simplified, but there should be no impact for uses of these classes.</li>
<li>The <code>ProjectCalendarHours</code> class now implements the <code>List</code> interface. Methods in this class not part ofthe <code>List</code> interface have been deprecated in favour of the equivalent <code>List</code> methods.</li>
<li>Updated <code>MPXWriter</code> to ensure: calendar names are quoted if necessary, all calendars have names, all calendar names are unique.</li>
<li>Updated <code>MPXReader</code> to recognise <code>wk</code> as a valid time unit.</li>
<li>Updated <code>MPXWriter</code>, <code>PrimaveraPMFileWriter</code>, <code>SDEFWriter</code> and <code>PlannerWriter</code> to ensure any working weeks defined by a calendar are represented by exceptons.</li>
<li>Updated <code>MSPDIWriter</code> to ensure any working weeks defined by a calendar are represented in the "legacy" exception definition used by Microsoft Project prior to 2007.</li>
<li>Updated <code>SDEFWriter</code> to ensure: only relevant calendars are written, and derived calendars are flattened.</li>
<li>When reading Planner schedules MPXJ will no longer create an "artificial" resource calendar for each resource. Resources will be linked directly to the calendar used in the original schedule.</li>
<li>Add support for reading the P6 calendar type and personal calendar flag from P6 schedules.</li>
<li>Add support for writing the calendar type and personal calendar flag to PMXML files.</li>
<li>Updated the calendar class hierarchy: <code>ProjectCalendar</code> and <code>ProjectCalendarWeek</code> both now inherit from a new class <code>ProjectCalendarDays</code>. Note that <code>ProjectCalendar</code> is no longer a subclass of <code>ProjectCalendarWeek</code>.</li>
<li>The <code>getHours</code> and <code>isWorkingDay</code> methods have been moved up to <code>ProjectCalendar</code> from the <code>ProjectCalendarWeek</code> class.</li>
<li>The <code>ProjectCalendar</code> method <code>copy</code> has been deprecated, without replacement.</li>
<li>Added a <code>getWork</code> method to <code>ProjectCalendar</code> which calculates the amount of work given a <code>Day</code> instance.</li>
<li>Added <code>removeWorkWeek</code> and <code>removeCalendarException</code> methods to <code>ProjectCalendar</code>.</li>
<li>Recurring exceptions are now added to a <code>ProjectCalendar</code> using the <code>addCalendarException</code> method which takes a <code>recurringData</code> instance its argument.</li>
<li>The <code>ProjectCalendarException</code> method <code>setRecurringData</code> has been removed, recurring exceptions should be added using the <code>addCalendarExcepton</code> method described above. (Note: this will be a breaking change if you were creating recurring exceptions)</li>
</ul>
<h2 id="1040-2022-05-05">10.4.0 (2022-05-05)</h2>
<ul>
<li>Remove <code>getParent</code>, <code>setParent</code>, and <code>isDerived</code> from <code>ProjectCalendarWeek</code>. (Note: this will be a breaking change if you were working with <code>ProjectCalendarWeek</code> directly).</li>
<li>The <code>ProjectProperties</code> methods <code>getDefaultCalendarName()</code> and <code>setDefaultCalendarName()</code> have been deprecated. Use <code>getDefaultCalendar()</code> and <code>setDefaultCalendar()</code> instead.</li>
<li>Ensure that percent complete values can be read from MSPDI files even if the values are decimals.</li>
<li>Improve handling of the default calendar when reading certain MSPDI files.</li>
<li>Improve reading certain Phoenix PPX files.</li>
<li>Improve reading certain FastTrack FTS files.</li>
<li>Improve formatting of time project properties when written to JSON.</li>
<li>Improve reading MPP files generated by Microsoft Project 16.0.15128.20158 and later versions.</li>
</ul>
<h2 id="1030-2022-04-29">10.3.0 (2022-04-29)</h2>
<ul>
<li>General improvements to make calendar data read from different file formats more consistent.</li>
<li>When reading P6 and Powerproject schedules MPXJ will no longer create an "artificial" resource calendar for each resource. Resources will be linked directly to the calendars they use in the original schedule.</li>
<li>Update <code>MPXWriter</code> and <code>MSPDIWriter</code> to ensure that, when written, calendars are correctly structured in the form required by Microsoft Project.</li>
<li><code>JsonWriter</code> now includes calendar data as part of its output.</li>
<li>The <code>ProjectCalendar</code> methods <code>setMinutesPerDay</code>, <code>setMinutesPerWeek</code>, <code>setMinutesPerMonth</code> and <code>setMinutesPerYear</code> have been deprecated, use <code>setCalendarMinutesPerDay</code>, <code>setCalendarMinutesPerWeek</code>, <code>setCalendarMinutesPerMonth</code> and <code>setCalendarMinutesPerYear</code> instead.</li>
<li>The ProjectCalendar method <code>setResource</code> has been deprecated and will not be replaced. Use the Resource method <code>setCalendar</code> or <code>setCalendarUniqueID</code> to link a calendar with a resource.</li>
<li>The ProjectCalendar method <code>getResource</code> has been deprecated. Use the <code>getResources</code> method instead to retrieve all resources linked with a calendar.</li>
<li>The <code>Resource</code> methods <code>addResourceCalendar</code>, <code>setResourceCalendar</code>, <code>getResourceCalendar</code>, <code>setResourceCalendarUniqueID</code> and <code>getResourceCalendarUniqueID</code> have been deprecated and replaced by <code>addCalendar</code>, <code>setCalendar</code>, <code>getCalendar</code>, <code>setCalendarUniqueID</code> and <code>getCalendarUniqueID</code> respectively.</li>
</ul>
<h2 id="1020-2022-03-06">10.2.0 (2022-03-06)</h2>
<ul>
<li>Improvements to writing currency, rate and units amounts to MSPDI files.</li>
<li>When reading MPP and MSPDI files, calendar exceptions representing a single range of days, but defined as a recurring exception are converted to a range of days, removing the unnecessary recurring definition.</li>
<li>Added <code>StructuredTextParser</code> to replace original code handling calendar data, project properties and curve data read from XER files and Primavera databases. Can also be used to extract data from Primavera Layout Files (PLF).</li>
<li>Improve recognition of contoured resource assignments read from MPP files.</li>
<li>Improve retrieval of resource assignment confirmed, response pending, linked fields, and team status pending flags from certain MPP files.</li>
</ul>
<h2 id="1010-2022-01-29">10.1.0 (2022-01-29)</h2>
<ul>
<li>Improve PMXML file compatibility with P6.</li>
<li>Strip any trailing invalid characters from text read from FTS files.</li>
<li>Ensure all tasks read from Powerproject and Project Commander have unique IDs.</li>
<li>Correct expansion of exceptions from a weekly recurring calendar exception.</li>
<li>Ensure that expanded calendar exceptions are written to file formats which do not support recurring exceptions.</li>
<li>Ensure that start and finish dates are set when reading milestones from GanttProject files.</li>
</ul>
<h2 id="1005-2022-01-11">10.0.5 (2022-01-11)</h2>
<ul>
<li>Ensure <code>Task.getActivityCodes()</code> returns an empty list rather than <code>null</code> when no activity code values have been assigned.</li>
<li>Default to using ASCII when reading and writing SDEF files, as per the SDEF specification.</li>
<li>Provide methods to set and get the charset used when reading and writing SDEF files.</li>
</ul>
<h2 id="1004-2022-01-07">10.0.4 (2022-01-07)</h2>
<ul>
<li>Added support for reading Code Library values (as Activity Codes) from Powerproject files.</li>
<li>Updated <code>ProjectCleanUtility</code> to provide a "replace" strategy alongside the original "redact" strategy.</li>
</ul>
<h2 id="1003-2021-12-22">10.0.3 (2021-12-22)</h2>
<ul>
<li>Fix issue with null tasks from certain MPP files introduced in 10.0.2.</li>
</ul>
<h2 id="1002-2021-12-16">10.0.2 (2021-12-16)</h2>
<ul>
<li>Improve identification of null tasks for certain MPP files.</li>
</ul>
<h2 id="1001-2021-12-10">10.0.1 (2021-12-10)</h2>
<ul>
<li>Avoid false positives when detecting password protected MPP files.</li>
</ul>
<h2 id="1000-2021-12-01">10.0.0 (2021-12-01)</h2>
<ul>
<li>Added support for .NET Core 3.1</li>
<li>Nuget packages now explicitly target .NET Framework 4.5 (<code>net45</code>) and .NET Core 3.1 (<code>netcoreapp3.1</code>)</li>
</ul>
<h2 id="983-2021-11-30">9.8.3 (2021-11-30)</h2>
<ul>
<li>Improve reliability when reading certain Phoenix files.</li>
<li>Ensure multiple trailing nul characters are stripped from text when reading schedules from a Primavera database.</li>
</ul>
<h2 id="982-2021-11-01">9.8.2 (2021-11-01)</h2>
<ul>
<li>Improve accuracy of identifying null tasks in certain MPP files.</li>
<li>Improve accuracy of identifying valid tasks in certain MPP files.</li>
<li>Ensure hierarchical outline code values are read correctly from MSPDI files.</li>
<li>Improve support for files produced by recent versions of FastTrack.</li>
</ul>
<h2 id="981-2021-10-13">9.8.1 (2021-10-13)</h2>
<ul>
<li>Added support for Phoenix 5 schedules.</li>
<li>Improve handling of null tasks read from MPP files.</li>
</ul>
<h2 id="980-2021-09-30">9.8.0 (2021-09-30)</h2>
<ul>
<li>Introduced the BaselineStrategy interface and implementing classes. (Note: this includes a breaking change if you were using the ProjectFile.setBaseline method and supplying a lambda. You will now need to implement a BaselineStrategy and set this in ProjectConfig before setting a baseline).</li>
<li>Improved accuracy of baseline attributes for Primavera schedules.</li>
</ul>
<h2 id="970-2021-09-28">9.7.0 (2021-09-28)</h2>
<ul>
<li>Add Sprint ID and Board Status ID attributes to task.</li>
<li>Introduce the TimeUnitDefaultsContainer to allow constants for time unit conversions to be obtained from both project properties and calendars.</li>
<li>Duration attributes are no longer returned as Duration instances by the ruby gem, they are now returned as floating point numbers. By default, durations are expressed in seconds. A new optional argument to MPXJ::Reader.read allows you to change the units used to express durations. (Note: this is a breaking change for users of the ruby gem)</li>
<li>Update JsonWriter to use a relevant calendar when converting durations.</li>
<li>Ensure default calendar is set correctly when reading XER and PMXML files, and P6 databases.</li>
<li>Use default hours per day/week/month/year when reading P6 XER files or databases if these values are not present.</li>
<li>Ensure that the minutes per day/week/month/year attributes are copied when a calendar is copied.</li>
<li>When reading P6 schedules, roll up calendar for WBS entries when child activities all share the same calendar.</li>
<li>Generate missing minutes per day/week/month/year for calendars read from P6 schedules.</li>
<li>Inherit minutes per day/week/month/year from base calendars (Note: minor method signature changes on ProjectProperties and ProjectCalendar).</li>
<li>Allow explicit values to be set for project minutes per week and minutes per year.</li>
<li>Fall back on defaults for project minutes per day/week/month/year attributes.</li>
</ul>
<h2 id="960-2021-09-13">9.6.0 (2021-09-13)</h2>
<ul>
<li>Add Planned Start and Scheduled Finish to project properties.</li>
<li>Add attribute_types method to Ruby classes.</li>
<li>Updated to use POI 5.0.0.</li>
<li>Corrected source of Must Finish By project property when reading XER files or P6 databases.</li>
<li>When reading PMXML files, ensure that the activity calendar is set before calculating slack.</li>
<li>Remove unused field TaskField.PARENT_TASK.</li>
<li>Ensure task Unique ID and task Parent Unique ID attributes are treated as mandatory when written to JSON.</li>
<li>Fix an issue with Ruby gem where a task's parent was not being retrieved correctly in some circumstances.</li>
</ul>
<h2 id="952-2021-08-22">9.5.2 (2021-08-22)</h2>
<ul>
<li>Add Must Finish By date to project properties.</li>
<li>Add support for the task attributes Longest Path, External Early Start and External Early Finish, and ensure they can be read from P6 schedules.</li>
<li>Rename ProjectFile.getStartDate() and ProjectFile.getFinishDate() methods for clarity. Original method names are marked as deprecated</li>
<li>Ensure that all activities in a PMXML file have a CalendarID attribute to ensure compatibility with older versions of P6.</li>
<li>Ensure that the user's selected progress period is used to set the project's status date attribute when reading Asta PP files.</li>
<li>Ensure that a task's Complete Through attribute is not advanced to the start of the next working day (the behaviour of Microsoft Project prior to 2007 was to report Complete Through as the start of the next working day. This change ensures MPXJ matches versions of Microsoft Project from 2007 onwards. Previous behaviour can be restored using the ProjectConfig.setCompleteThroughIsNextWorkStart() method).</li>
<li>Deprecate task getSplitCompleteDuration() and setSplitCompleteDuration() in favour of getCompleteThrough() and setCompleteThrough().</li>
<li>Improved the implementation of the TaskContainer.synchronizeTaskIDToHierarchy method.</li>
<li>Update jsoup to 1.14.2.</li>
</ul>
<h2 id="951-2021-07-01">9.5.1 (2021-07-01)</h2>
<ul>
<li>When applying a baseline using ProjectFile.setBaseline, gracefully handle duplicate task key values.</li>
<li>Handle missing values populating cost rate table from an MPP file.</li>
</ul>
<h2 id="950-2021-06-30">9.5.0 (2021-06-30)</h2>
<ul>
<li>Added support for reading baseline data from embedded baselines in PP files.</li>
<li>Correct resource assignment percent complete values read from PP files.</li>
<li>JsonWriter no longer writes attribute type information by default. (The original behaviour can be restored by calling setWriteAttributeTypes(true) on your JsonWriter instance).</li>
<li>The MPXJ Ruby Gem now generates explicit methods to access attributes rather than relying on "method_missing" to intercept and act on attribute access.</li>
<li>Don't write Assignment Task GUID, Assignment Resource GUID or Resource Calendar GUID to JSON.</li>
<li>Don't write a value for Assignment Work Contour to JSON if the contour is the default value (i.e. flat).</li>
<li>Don't write a value for Assignment Resource Request Type to JSON if the type is the default value (i.e. none).</li>
<li>Don't write a value for Task Earned Value Method to JSON if the method matches the project default.</li>
<li>Don't write a value for Task Type to JSON if the type matches the project default.</li>
<li>Stop writing a default value (-1) for Parent Task ID to JSON if the task does not have a parent.</li>
<li>Stop writing a default value (-1) for Task Calendar ID to JSON if the task does not have a calendar.</li>
<li>When reading resource assignments from an MPP file, don't record Project's internal representation of a null resource ID (-65535), record the resource ID explicitly as null.</li>
<li>For MPX and Planner files, don't write resource assignments for the "null" resource.</li>
<li>Handle missing status date when reading P6 schedules from XER files or database.</li>
<li>When reading MPP files, treat UUIDs which are all zeros as null.</li>
<li>Deprecate the 10 Resource Outline Code get and set methods and replace with get and set methods which take an index argument.</li>
<li>Provide a helper method (PrimaveraHelper.baselineKey) to encapsulate key generation for setting Primavera baselines.</li>
</ul>
<h2 id="940-2021-06-11">9.4.0 (2021-06-11)</h2>
<ul>
<li>Read custom value lists for resource custom fields from MPP files (based on a suggestion by Markus Höger).</li>
<li>Added support for reading custom fields from Asta Powerproject files.</li>
<li>Ensure short data type values are written to JSON files as numeric values.</li>
<li>Ensure delay data type values are written to JSON files as duration values.</li>
<li>Don't write zero rates to JSON files.</li>
<li>Introduced a separator into rate values when written to a JSON file to make it clear that the value is a rate not a duration (for example: 5.00h is now 5.00/h).</li>
<li>When writing an enum value of a JSON file, ensure we write the original enum name rather than the value return by toString. This provides more meaningful output (Potentially breaking change if you use the Ruby gem or consume the JSON output directly. Affected attributes are project properties: currency symbol position, time format, date format, bar text date format, schedule from, mpx file version; resource attribute: type).</li>
<li>Ensure invalid cost rate table data is handled gracefully when reading from MSPDI files.</li>
<li>Handle missing data when reading MSPDI files (based on a contribution by Lord Helmchen).</li>
<li>Improve population of summary task names when reading from Powerproject PP files.</li>
<li>Correctly read hierarchical resource outline codes from MPP files (based on a suggestion by Markus Höger).</li>
</ul>
<h2 id="931-2021-05-18">9.3.1 (2021-05-18)</h2>
<ul>
<li>Preserve multiple assignments between an activity and a resource when reading P6 schedules.</li>
<li>Renamed WorkContour.isFlat to isContourFlat and WorkContour.isContoured to isContourManual.</li>
<li>Include an entry for 0% in the WorkContour curve definition.</li>
<li>Fix an issue where non-working days were not being treated correctly in date calculations if they happen to still have time ranges attached.</li>
</ul>
<h2 id="930-2021-05-06">9.3.0 (2021-05-06)</h2>
<ul>
<li>Add support for reading roles from P6 databases, XER and PMXML files, and for writing roles to PMXML files. Roles are represented as resources. The new resource Boolean attribute "Role" is used to distinguish between Resource instances which represent resources and those which represent roles.</li>
<li>Add support for reading resource curves from P6 databases, XER and PMXML files, and for writing resource curves to PMXML files. The WorkContour enum is now a class, and instance of this class are used to represent resource curves. The curves are available via the work contour attribute of resource assignments.</li>
<li>Corrected the data type of the task physical percent complete attribute.</li>
<li>Improve handling of non-standard relationship type representations encountered in XER files and P6 databases.</li>
</ul>
<h2 id="926-2021-04-26">9.2.6 (2021-04-26)</h2>
<ul>
<li>Handle invalid baseline numbers when reading MSPDI files.</li>
<li>Improve custom field handling when reading GanttProject files.</li>
</ul>
<h2 id="925-2021-04-20">9.2.5 (2021-04-20)</h2>
<ul>
<li>Add launcher batch file and shell script.</li>
<li>Improve handling of calculated task attributes when writing a project to a different format.</li>
<li>Ensure that dates are rolled up to summary tasks when reading FastTrack files.</li>
<li>Improve support for Synchro 6.3 SP files.</li>
</ul>
<h2 id="924-2021-04-09">9.2.4 (2021-04-09)</h2>
<ul>
<li>Fix an issue reading resource rate information GanttProject files.</li>
</ul>
<h2 id="923-2021-04-08">9.2.3 (2021-04-08)</h2>
<ul>
<li>Fix an issue reading Planned Duration from P6 databases and XER files.</li>
<li>Ensure Duration and Actual Duration are populated for WBS entries when reading P6 schedules.</li>
</ul>
<h2 id="922-2021-04-07">9.2.2 (2021-04-07)</h2>
<ul>
<li>Fix issue with WBS ordering when writing PMXML files.</li>
</ul>
<h2 id="921-2021-04-04">9.2.1 (2021-04-04)</h2>
<ul>
<li>Improve Task critical flag calculation when reading PMXML files.</li>
<li>Improve support for Synchro 6.3 SP files.</li>
</ul>
<h2 id="920-2021-03-30">9.2.0 (2021-03-30)</h2>
<ul>
<li>Improve accuracy when reading subprojects from MPP files.</li>
<li>Add Remaining Late Start and Remaining Late Finish attributes to Task.</li>
<li>Add Critical Activity Type attribute to Project Properties</li>
<li>Read Remaining Early Start, Remaining Late Start, Remaining Early Finish and Remaining Late finish from and write to PMXML files.</li>
<li>Read Remaining Late Start and Remaining Late finish from P6 database and XER files.</li>
<li>Ensure that WBS entries without child activities are not marked as critical.</li>
<li>Don't attempt to set the critical flag when reading XER and PMXML files where the schedule is using "longest path" to determine critical activities. (MPXJ currently doesn't have enough information to be able to determine the correct value for the critical flag in this situation).</li>
<li>Ensure cost, duration, date and work attributes are rolled up to WBS entries for P6 schedules read from PMXML files, XER files and P6 databases.</li>
<li>Populate baseline cost, duration, finish, start and work when reading from XER files, PMXML files and P6 databases where the "Project Baseline" has been set to "Current Project".</li>
</ul>
<h2 id="910-2021-03-11">9.1.0 (2021-03-11)</h2>
<ul>
<li>Add methods to the ProjectFile class to attach a ProjectFile instance as a baseline. The baselines attached to the ProjectFile will be used to populate the relevant baseline attributes in the current schedule.</li>
<li>Added experimental support for writing baseline projects to PMXML files. </li>
<li>Added the Project GUID attribute.</li>
<li>When reading PMXML files, the list of projects returned by the readAll method will include any baseline projects present in the file.</li>
<li>When reading PMXML files which include the current baseline project, use this to populate the relevant baseline attributes in the main schedule.</li>
<li>The Project Unique ID property is now an integer rather than a string.</li>
<li>When reading Primavera schedules, populate the project properties Project ID and Baseline Project Unique ID.</li>
<li>Handle Primavera resource rates which don't have a start or finish date.</li>
<li>Handle MSPDI files with resource availability tables which don't have a start or finish date.</li>
<li>Ensure that the Activity ID field is populated consistently for WBS entries in PMXML files compared to the same schedule read from an XER file or P6 database.</li>
<li>Ensure duration of manually scheduled tasks in MPP files is represented correctly.</li>
</ul>
<h2 id="900-2020-02-18">9.0.0 (2020-02-18)</h2>
<ul>
<li>NOTE: this release introduces breaking changes!</li>
<li>All fields which are non-user defined, but were previously being returned by MPXJ as custom fields are now represented as explicit field types. Custom fields now only contain values for user-defined custom fields.</li>
<li>All code previously marked as deprecated has been removed.</li>
<li>When reading an XER file or a P6 database, some custom project property names have been updated. LagCalendar is now CalendarForSchedulingRelationshipLag, RetainedLogic is now WhenSchedulingProgressedActivitiesUseRetainedLogic, ProgressOverride is now WhenSchedulingProgressedActivitiesUseProgressOverride, IgnoreOtherProjectRelationships is now WhenSchedulingProgressedActivitiesUseProgressOverride, and StartToStartLagCalculationType is now ComputeStartToStartLagFromEarlyStart.</li>
<li>Updated PMXML schema to version 20.12.</li>
<li>Fix an issue where GUID values were not being read correctly from XER files and P6 databases.</li>
<li>Percent complete type is now available as a task attribute for P6 schedules from any source.</li>
<li>Ensure that percent complete values are stored in the appropriate attributes when reading P6 schedules. (NOTE: Previously the "reported" percent complete value was stored as the tasks "percent complete" attribute. Now this holds the schedule percent complete value, and the percent work complete and physical percent complete attributes are also populated. To determine which value should be reported for a task, see the "percent complete type" extended field attribute.)</li>
<li>Correctly handle default calendar when reading and writing PMXML files.</li>
<li>Update the sort order of WBS entries and activities in PMXML files to match the order exported by P6.</li>
<li>Match the way P6 exports the WBS code attribute for PMXML files.</li>
<li>Update the representation of Boolean values when writing PMXML files to match the form exported by P6.</li>
<li>Set the task type attribute when reading PMXML files.</li>
<li>Improve duration and actual duration calculations when reading XER files and P6 databases.</li>
<li>Fix an issue where resource assignment costs were not being read correctly from PMXML files.</li>
<li>Read and write the suspend date and resume date attributes for PMXML files.</li>
<li>General improvements to the SDEF writer.</li>
<li>Updated to rtfparserkit 1.16.0.</li>
</ul>
<h2 id="851-2021-01-07">8.5.1 (2021-01-07)</h2>
<ul>
<li>Don't write unused enterprise custom field definitions to MSPDI files. This ensures that MS Project will open these files correctly.</li>
</ul>
<h2 id="850-2021-01-06">8.5.0 (2021-01-06)</h2>
<ul>
<li>Notes in their original format (HTML from P6, RTF from MS Project) can now be retrieved via the getNotesObject method on Task, Resource, and ResourceAssignment. Plain text notes can still be retrieved via the getNotes method. If you were previously using the "preserve note formatting" flag to retrieve the original formated version of a note, you will now need to use the getNotesObject method.</li>
<li>Write WBS and Activity notes to PMXML files.</li>
<li>PMXML compatibility improvements to ensure files can be successfully imported into P6.</li>
</ul>
<h2 id="840-2020-12-29">8.4.0 (2020-12-29)</h2>
<ul>
<li>Previously when reading PMXML files, XER files, and P6 databases, a set of baseline attributes on tasks and assignments (including Start, Finish, Duration, Cost and Work) were being populated with planned values rather than baseline values. These baseline attributes are no longer being set. The values they previously contained are now available as custom fields.</li>
<li>Read activity notepad entries for XER, PMXML files and P6 databases.</li>
<li>Read schedule and leveling options from PMXML files and P6 databases.</li>
<li>Improve support for reading activity cost and work from PMXML files.</li>
</ul>
<h2 id="835-2020-12-15">8.3.5 (2020-12-15)</h2>
<ul>
<li>Fix CVE-2020-35460: zip slip vulnerability (with thanks to Sangeetha Rajesh S, ZOHO Corporation)</li>
</ul>
<h2 id="834-2020-12-10">8.3.4 (2020-12-10)</h2>
<ul>
<li>Updated PMXML schema to version 19.12.</li>
<li>Ensure that we always set the activity planned start and planned finish dates when writing a PMXML file.</li>
<li>Updated the getPopulatedFields methods to ignore fields with default values.</li>
<li>Made the Resource ID attribute available as a resource's TEXT1 custom field, with the alias "Resource ID" when reading PMXML and XER files, or from a P6 database. (Note that presently for XER files and P6 databases, the Resource ID value is also read into the initials attribute. This behaviour is deprecated and will be removed in the next major MPXJ release).</li>
<li>Populate the Resource ID with the value read from a P6 schedule when writing a PMXML file.</li>
<li>Ensure that the hours per day, week, month and year attributes are read from and written to PMXML files.</li>
<li>Fix an issue causing the hours per day calendar attribute to be read inaccurately from XER files and P6 databases.</li>
<li>Read assignment actual overtime cost and work attributes from PMXML files.</li>
<li>Update calculation of assignment work, cost and units attributes for PMXML files.</li>
</ul>
<h2 id="833-2020-11-24">8.3.3 (2020-11-24)</h2>
<ul>
<li>Added cost rate table support when reading from and writing to PMXML files.</li>
<li>Added a getPopulatedFields method to the TaskContainer, ResourceContainer and ResourceAssignmentContainer classes. This will retrieve the set of fields which are populated with a non-null value across the whole project for Tasks, Resources, and ResourceAssignments respectively. </li>
<li>Add START_ON, FINISH_ON constraint types. § MANDATORY_START, MANDATORY_FINISH constraint types. MANDATORY_START/FINISH are now represented as MUST_START/FINISH_ON. This change allows users to distinguish between START/FINISH_ON and the MANDATORY_* constraints when reading P6 schedules.</li>
<li>Improve handling of cost rate tables and availability tables when writing to an MSPDI file.</li>
<li>Handle P6 databases and XER files with user defined fields of type FT_FLOAT.</li>
<li>Align invalid XER record behaviour with P6.</li>
<li>Handle Planner files which don't contain an allocations tag.</li>
<li>Gracefully handle MPP files with missing view or table data.</li>
</ul>
<h2 id="832-2020-10-22">8.3.2 (2020-10-22)</h2>
<ul>
<li>Added support for "new tasks are manual" project property (Contributed by Rohit Sinha)</li>
<li>Improved support for reading and writing outline codes and extended attributes for MSPDI files (Based on a contribution by Dave McKay)</li>
<li>Improved handling of enterprise custom fields when reading MPP files</li>
<li>Update Primavera database and XER readers to avoid potential type conversion errors when the caller provides their own field mappings.</li>
<li>Improve handling of some MPP12 MPP file variants.</li>
<li>Avoid error when reading timephased data from certain MPP files.</li>
<li>Gracefully handle MPP files with missing view data.</li>
<li>Update junit to 4.13.1.</li>
</ul>
<h2 id="831-2020-10-14">8.3.1 (2020-10-14)</h2>
<ul>
<li>Minor updates to PlannerReader.</li>
</ul>
<h2 id="830-2020-10-13">8.3.0 (2020-10-13)</h2>
<ul>
<li>Add the "userDefined" attribute to the CustomField class to allow caller to determine if the field has been created by a user or MPXJ.</li>
<li>Add support for reading expense items, expense categories and cost accounts from XER files, PMXML files and Primavera databases.</li>
<li>Add support for writing expense items, expense categories and cost accounts to PMXML files.</li>
<li>Updated the XER file reader to ignore invalid records rather than reporting an error, matching the behaviour of P6</li>
<li>Updated the XER file reader to ensure that activity suspend and resume dates are read correctly.</li>
<li>Updated the XER file reader to ensure that if the reader returns the project selected by the caller when the caller supplies a value for project ID.</li>
<li>Updated PMXML reader to avoid user defined field collisions.</li>
<li>Updated PMXML reader to add setProjectID and listProjects methods.</li>
<li>Update the .net extension method ToIEnumerable to work with java.lang.Iterable rather than java.util.Collection</li>
</ul>
<h2 id="820-2020-09-09">8.2.0 (2020-09-09)</h2>
<ul>
<li>All readers, including the UniversalProjectReader, now support a readAll method. If a file or database contains more than one project the readAll method can be used to retrieve them all in one operation. If the file format doesn't support multiple schedules, readAll will just return a single schedule.</li>
<li>Add PrimaveraDatabaseFileReader to encapsulate access to SQLite Primavera databases.</li>
<li>Ensure that the summary flag is true for WBS items in Primavera schedules, even if they have no child activities.</li>
<li>Ensure that the critical flag is rolled up appropriately to WBS items when reading Primavera schedules.</li>
<li>Set export flag property when reading projects from a PMXML file.</li>
<li>Corrected data type of resource assignment Work Contour field.</li>
<li>Corrected data type of resource fields: BCWS, BCWP, ACWP, SV, CV, and Work Contour.</li>
<li>Corrected data type of task fields: CV, ACWP, VAC, CPI, EAC, SPI, TCPI, and Work Contour.</li>
</ul>
<h2 id="814-2020-08-31">8.1.4 (2020-08-31)</h2>
<ul>
<li>Fix CVE-2020-25020: XXE vulnerability (with thanks to Sangeetha Rajesh S, ZOHO Corporation)</li>
<li>Import milestone constraints from Asta schedules (Contributed by Dave McKay)</li>
<li>Handle elapsed durations in Asta schedules (Based on a contribution by Dave McKay)</li>
<li>Correctly determine the constraint type for tasks with ALAP placement with or without predecessors when reading from Asta schedules (Contributed by Dave McKay)</li>
<li>Gracefully handle a missing table name when reading an XER file.</li>
<li>Gracefully handle an unexpected calendar data when reading an XER file.</li>
<li>Correctly handle XER files with multibyte character encoding.</li>
<li>Import all schedule and leveling options from XER files.</li>
<li>Ensure project calendars are read from PMXML files.</li>
<li>Added readAll methods to PrimaveraPMFileReader to allow all projects contained in a PMXML file to be read in a single pass.</li>
</ul>
<h2 id="813-2020-06-25">8.1.3 (2020-06-25)</h2>
<ul>
<li>Improve reliability when reading custom field values from certain MPP12 files.</li>
<li>Improve accuracy of activity percent complete when reading from certain XER files or P6 databases.</li>
<li>Improve accuracy of WBS percent complete when reading from certain XER files or P6 databases.</li>
<li>Improve accuracy of task durations when reading Asta schedules.</li>
<li>Fix an issue handling the end date of calendar exceptions when reading Asta schedules.</li>
<li>Fix an issue with correctly identifying the calendar applied to summary tasks when reading Asta schedules.</li>
<li>Populate percent complete, duration, actual start, actual finish, early start, late start, early finish and late finish attributes for summary tasks when reading Asta schedules.</li>
<li>The percent complete value reported for tasks when reading Asta schedules is now Duration Percent Complete. The Overall Percent Complete value originally being returned is available in a custom field. </li>
</ul>
<h2 id="812-2020-06-18">8.1.2 (2020-06-18)</h2>
<ul>
<li>Improve detection of unusual MSPDI file variants.</li>
<li>Updated to read task notes from FastTrack FTS files.</li>
</ul>
<h2 id="811-2020-06-17">8.1.1 (2020-06-17)</h2>
<ul>
<li>Improve support for Synchro 6.2 SP files.</li>
</ul>
<h2 id="810-2020-06-11">8.1.0 (2020-06-11)</h2>
<ul>
<li>Experimental support for reading Project Commander schedules.</li>
<li>Update to use JAXB 2.3.2.</li>
<li>Avoid failures caused by unreadable OLE compound documents when the UniversalProjectReader is trying to determine the file type.</li>
<li>Strip trailing ASCII NUL characters from text fields when reading from a Primavera database.</li>
<li>Improve accuracy of task order when reading Phoenix files.</li>
<li>Improve accuracy of task data when reading some MPP file variants.</li>
<li>Improve reliability when reading certain SureTrak files.</li>
</ul>
<h2 id="808-2020-04-20">8.0.8 (2020-04-20)</h2>
<ul>
<li>Improve handling of numeric character references invalid for XML 1.0 in PMXML files.</li>
<li>Improve handling of resource calendars read from Planner files.</li>
<li>Improve handling of resource calendars read from MPX files.</li>
<li>Ignore the milestone flag when reading MPX files if the task has a non-zero duration.</li>
<li>Ensure JSON files can be written when Unique ID predecessor/successor attributes have been read from an MPX file.</li>
</ul>
<h2 id="807-2020-04-17">8.0.7 (2020-04-17)</h2>
<ul>
<li>Updated to rtfparserkit 1.15.0.</li>
<li>Improve handling of PMXML files with empty calendar exception time ranges.</li>
</ul>
<h2 id="806-2020-03-05">8.0.6 (2020-03-05)</h2>
<ul>
<li>Updated to use POI 4.1.2.</li>
<li>Improve handling of some XER file variants.</li>
</ul>
<h2 id="805-2020-02-07">8.0.5 (2020-02-07)</h2>
<ul>
<li>Allow users to determine WBS attribute content with "wbs is full path" flag for Primavera readers.</li>
<li>Ensure summary task start and finish dates are populated when reading PMXML files.</li>
<li>Use baseline start and finish dates as planned start and finish dates when writing PMXML files.</li>
<li>Late start and late finish dates are now written to PMXML files.</li>
</ul>
<h2 id="804-2020-02-06">8.0.4 (2020-02-06)</h2>
<ul>
<li>Update sqlite-jdbc dependency to 3.30.1</li>
<li>Improve handling of characters invalid for XML 1.0 in PMXML files generated by P6.</li>
</ul>
<h2 id="803-2020-01-27">8.0.3 (2020-01-27)</h2>
<ul>
<li>Improve handling of zero value durations, costs and units from certain MPP files.</li>
<li>Improve percent complete calculation for certain XER file and P6 Database schedules.</li>
<li>Improve percent complete calculation for certain P3 schedules.</li>
<li>Improve handling of incorrectly encoded characters in PMXML files generated by P6.</li>
<li>Ensure that negative durations can be written to and read from MSPDI files in the format understood by MS Project.</li>
</ul>
<h2 id="802-2020-01-16">8.0.2 (2020-01-16)</h2>
<ul>
<li>Improve handling of zero duration tasks read from Phoenix files.</li>
</ul>
<h2 id="801-2020-01-05">8.0.1 (2020-01-05)</h2>
<ul>
<li>Add missing nuget dependency</li>
</ul>
<h2 id="800-2020-01-02">8.0.0 (2020-01-02)</h2>
<ul>
<li>MPXJ now requires Java 8 or later.</li>
<li>Removed deprecated methods.</li>
<li>Updated to use POI 4.1.1.</li>
<li>Updated to use IKVM 8.1.5717.0.</li>
</ul>
<h2 id="798-2019-12-27">7.9.8 (2019-12-27)</h2>
<ul>
<li>Added support for reading and writing outline code/custom field lookup tables for MSPDI files.</li>
<li>Added sample code to demonstrate creation of timephased work.</li>
<li>Populate project status date attribute when reading Asta schedules.</li>
<li>Populate parent attribute when reading activity code values from Primavera schedules.</li>
<li>Improve configurability of PrimaveraDatabaseReader and PrimaveraXERFileReader.</li>
<li>Made JAXB JARs an explicit dependency to avoid issues with recent Java versions which do not include them.</li>
</ul>
<h2 id="797-2019-11-25">7.9.7 (2019-11-25)</h2>
<ul>
<li>Round percent complete values read from Asta files to two decimal places to avoid values like 99.9999999%.</li>
</ul>
<h2 id="796-2019-11-22">7.9.6 (2019-11-22)</h2>
<ul>
<li>Improve support for FastTrack files.</li>
</ul>
<h2 id="795-2019-11-19">7.9.5 (2019-11-19)</h2>
<ul>
<li>Added flag to manage compliance with password protection. (Contributed by ztravis)</li>
<li>Improve support for Synchro 6.1 SP files.</li>
<li>Fix an issue where the task hierarchy was not correctly represented when reading a PMXML file.</li>
</ul>
<h2 id="794-2019-11-08">7.9.4 (2019-11-08)</h2>
<ul>
<li>Add support for reading Sage 100 Contractor schedule grid files.</li>
<li>Ensure attribute names are valid when exporting JSON.</li>
<li>Improve handling of custom field lookup values (Based on a contribution by Nick Darlington).</li>
<li>Fix an issue when copying a calendar which has exceptions defined.</li>
</ul>
<h2 id="793-2019-09-10">7.9.3 (2019-09-10)</h2>
<ul>
<li>Add support for reading task early finish and late finish attributes from Asta PP files.</li>
<li>Ensure XER files containing secondary constraints can be read correctly.</li>
<li>Preserve calendar IDs when reading from XER files and P6 database (Based on a contribution by forenpm).</li>
<li>Ensure base calendars are read correctly for P6 schedules.</li>
<li>Ensure MPP files with unexpected auto filter definition data are handled gracefully.</li>
<li>Preserve leveling delay format when reading tasks from MSPDI files.</li>
<li>Ensure unexpected structure of timephased data is handled gracefully when reading MPP files.</li>
</ul>
<h2 id="792-2019-08-19">7.9.2 (2019-08-19)</h2>
<ul>
<li>Add support for reading and writing secondary constraints from P6 schedules (Based on a contribution by Sruthi-Ganesh)</li>
<li>Improve support for Synchro SP files containing blank tasks.</li>
<li>Make constraint type mapping consistent when reading and writing PMXML files.</li>
<li>Improve handling of leveling delay units and actual duration units (Based in a contribution by Daniel Schmidt).</li>
<li>Improve handling of certain types of malformed MPP files.</li>
<li>Improve handling of certain types of malformed SDEF files.</li>
<li>Map P6 Equipment resource type to cost rather than work (Contributed by forenpm)</li>
<li>Improve handling of certain MPP files containing large numbers of blank tasks.</li>
<li>Improve handling of certain MPX files containing trailing delimiters.</li>
</ul>
<h2 id="791-2019-07-01">7.9.1 (2019-07-01)</h2>
<ul>
<li>Set task start, finish and percent complete when reading SDEF files.</li>
</ul>
<h2 id="790-2019-07-01">7.9.0 (2019-07-01)</h2>
<ul>
<li>Add support for reading SDEF files.</li>
</ul>
<h2 id="784-2019-06-27">7.8.4 (2019-06-27)</h2>
<ul>
<li>Add support for reading data links (linked fields) configuration from MPP files.</li>
<li>Updated to avoid an infinite loop when processing certain corrupt files (Contributed by ninthwaveltd).</li>
<li>Update MSPDI generation to ensure MS Project correctly recognises complete tasks without resource assignments.</li>
<li>Ensure that activity codes are read for P6 schedules.</li>
<li>Improve support for reading custom field values derived from custom field lookup tables in MPP files.</li>
<li>Improve support for MPP files written with the June 2019 update of Microsoft Project.</li>
</ul>
<h2 id="783-2019-05-24">7.8.3 (2019-05-24)</h2>
<ul>
<li>Improve handling of task baseline start, start, baseline finish, finish and slack fields read from FTS files.</li>
</ul>
<h2 id="782-2019-05-19">7.8.2 (2019-05-19)</h2>
<ul>
<li>Improve handling of MPP files with missing Props.</li>
<li>Improve handling of custom field lookup tables for MPP12 files.</li>
<li>Correctly write activity duration type to a PMXML file (Contributed by Sebastian Stock)</li>
<li>Improve handling of Activity Type and Activity ID when writing PMXML files (Based on a contribution by Sebastian Stock)</li>
<li>Update PMXML file reader for greater consistency with XER and P6 database readers (Activity ID, Activity Type, Status, and Primary Resource ID)</li>
<li>Improve handling of certain FTS files.</li>
<li>Improve handling of task notes from MPP8 files.</li>
<li>More accurately read predecessors and successors from Asta PP files (Based on a contribution by Dave McKay)</li>
<li>When a schedule is read from P6, P3, or SureTrak, Task.getSummary will return true only if a task is part of the WBS</li>
<li>Improve support for reading the Synchro Scheduler 2018 SP files.</li>
<li>Added Task.hasChildTasks() method.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/330">Issue 330</a>: Splits data coming in as null for all tasks</li>
</ul>
<h2 id="781-2019-02-13">7.8.1 (2019-02-13)</h2>
<ul>
<li>Improve support for reading the Synchro Scheduler 2018 SP files.</li>
<li>Add support for reading Gantt Designer GNT files.</li>
<li>Improve handling of non-standard MSPDI files.</li>
<li>Improve handling of non-standard GanttProject files.</li>
<li>Update MSPDI generation to ensure MS Project correctly recognises complete milestones without resource assignments.</li>
<li>Improve support for reading user defined fields from PMXML files.</li>
<li>Ignore hammock tasks when reading PP files.</li>
</ul>
<h2 id="780-2019-01-18">7.8.0 (2019-01-18)</h2>
<ul>
<li>Added support for reading and writing GUIDs for Tasks, Resources, and Assignments in MSPDI files.</li>
<li>Updated Java build to use Maven</li>
<li>Updated to provide a general performance improvement (Based on a contribution by Tiago de Mello)</li>
<li>Updated to fix an issue when the Microsoft JDBC driver is used to access a P6 database in SQL Server 2005</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/332">Issue 332</a>: Asta lag sign incorrect (Based on a contribution by Dave McKay)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/333">Issue 333</a>: Asta constraints lost (Contributed by Dave McKay)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/335">Issue 335</a>: MSDPI into Asta doesn't import Calendar exceptions (Contributed by Dave McKay)</li>
</ul>
<h2 id="771-2018-10-23">7.7.1 (2018-10-23)</h2>
<ul>
<li>Read additional schedule options from XER files. (Contributed by forenpm)</li>
<li>Improve handling of some types of MPP file with missing resource assignment data.</li>
<li>Ensure that resource assignment flag fields are read correctly for all MPP file types (Based on a contribution by Vadim Gerya).</li>
<li>Ensure that timephased actual work is handled correctly for material resources (Contributed by Vadim Gerya).</li>
<li>Improve accuracy when reading resource type from MPP files.</li>
<li>Improve compatibility of generated MSPDI files with Asta Powerproject (Contributed by Dave McKay).</li>
</ul>
<h2 id="770-2018-10-12">7.7.0 (2018-10-12)</h2>
<ul>
<li>Add support for reading the Synchro Scheduler SP files.</li>
<li>Add support for reading the activity code (ID) from Asta files.</li>
<li>When reading a Phoenix file, set the project's status date to the data date from the storepoint.</li>
<li>Handle MSPDI files with timephased assignments that don't specify a start and end date.</li>
</ul>
<h2 id="763-2018-10-04">7.6.3 (2018-10-04)</h2>
<ul>
<li>Add support for reading Remaining Early Start and Remaining Early Finish task attributes from P6. (Contributed by forenpm)</li>
<li>Add support for reading Retained Logic and Progressive Override project attributes from P6. (Contributed by forenpm)</li>
<li>Fix incorrect sign when calculating start and finish slack (Contributed by Brian Leach).</li>
<li>Correctly read predecessors and successors from Phoenix files.</li>
</ul>
<h2 id="762-2018-08-30">7.6.2 (2018-08-30)</h2>
<ul>
<li>Add support for nvarchar columns when reading from a P6 database.</li>
<li>Updated to correctly read percent lag durations from MSPDI files (based on a contribution by Lord Helmchen).</li>
<li>Updated the data type for the ValueGUID tag in an MSPDI file (based on a contribution by Lord Helmchen).</li>
</ul>
<h2 id="761-2018-08-29">7.6.1 (2018-08-29)</h2>
<ul>
<li>Improve handling of MPP files where MPXJ is unable to read the filter definitions.</li>
<li>Improve handling of SureTrak projects without a WBS.</li>
<li>Improve handling of SureTrak and P3 WBS extraction.</li>
<li>Handle unsupported ProjectLibre POD files more gracefully.</li>
<li>Improve detection of non MS Project compound OLE documents.</li>
<li>Gracefully handle XER files which contain no projects.</li>
</ul>
<h2 id="760-2018-07-13">7.6.0 (2018-07-13)</h2>
<ul>
<li>Added support for reading ConceptDraw PROJECT CDPX, CPDZ and CPDTZ files.</li>
<li>Add support for reading the export_flag attribute from XER files. (Contributed by forenpm)</li>
<li>Use correct licence details in Maven pom.xml (contributed by Mark Atwood).</li>
<li>Improve UniversalProjectReader's handling of XER files containing multiple projects.</li>
</ul>
<h2 id="750-2018-06-19">7.5.0 (2018-06-19)</h2>
<ul>
<li>Added support for reading activity codes from P6 databases, XER files, and PMXML files.</li>
<li>Added support for reading user defined values from a P6 database.</li>
<li>Added support for PRX files which contain a SureTrak database.</li>
<li>Added support for reading the resource "enterprise" attribute from MPP12 and MPP14 files.</li>
<li>Improve performance when reading user defined values from XER files.</li>
<li>Improved support for older Primavera PMXML files.</li>
<li>Updated to rtfparserkit 1.11.0 for improved RTF parsing.</li>
</ul>
<h2 id="744-2018-06-06">7.4.4 (2018-06-06)</h2>
<ul>
<li>Improve handling of calendar exceptions in MPX files.</li>
<li>Improve handling of MPP files with large numbers of null tasks.</li>
<li>Improve robustness when reading timephased data.</li>
<li>Correctly sort Primavera schedules containing WBS entries with no child activities.</li>
</ul>
<h2 id="743-2018-05-25">7.4.3 (2018-05-25)</h2>
<ul>
<li>Add support for reading the resource "generic" attribute from MPP files.</li>
<li>Add a Unique ID attribute to the Relation class and populate for schedule types which support this concept.</li>
<li>Store the Primavera Project ID as Unique ID in the project properties.</li>
<li>Update MerlinReader to ensure support for Merlin Project Pro 5.</li>
</ul>
<h2 id="742-2018-04-30">7.4.2 (2018-04-30)</h2>
<ul>
<li>Gracefully handle malformed duration values in MSPDI files.</li>
<li>Gracefully handle unexpected calendar exception data structure in certain MPP files.</li>
<li>Improve handling of certain unusual MPP12 files.</li>
<li>More work to gracefully handle POI issue 61677, allowing affected MPP files to be read successfully.</li>
</ul>
<h2 id="741-2018-04-16">7.4.1 (2018-04-16)</h2>
<ul>
<li>Add methods to list projects available in P3 and SureTrak database directories.</li>
<li>Avoid NPE when a work pattern can't be located in an Asta Powerproject PP file.</li>
<li>Avoid array bounds exception when reading certain PRX files.</li>
<li>Read outline code value lists from MPP9 files.</li>
<li>Handle SureTrak projects without a WBS.</li>
</ul>
<h2 id="740-2018-03-23">7.4.0 (2018-03-23)</h2>
<ul>
<li>Added support for reading Primavera SureTrak databases from directories, zip files, and STX files.</li>
<li>Added support for PP files generated by Asta Powerproject from version ********</li>
</ul>
<h2 id="730-2018-03-12">7.3.0 (2018-03-12)</h2>
<ul>
<li>Added support for reading Primavera P3 databases from directories, zip files, and PRX files.</li>
<li>Improve robustness when reading MPP files containing apparently invalid custom field data.</li>
<li>Improve UniversalProjectReader byte order mark handling.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/324">Issue 324</a>: Fields with lookup unreadable when a field has custom name.</li>
</ul>
<h2 id="721-2018-01-26">7.2.1 (2018-01-26)</h2>
<ul>
<li>More work to gracefully handle POI issue 61677, allowing affected MPP files to be read successfully.</li>
<li>Avoid divide by zero when calculating percent complete from certain Primavera PMXML files.</li>
<li>UniversalProjectReader updated to recognise MPX files with non-default separator characters.</li>
<li>Update FastTrack reader to handle invalid percentage values on resource assignments.</li>
<li>Update FastTrack reader to handle variations in UUID format.</li>
<li>Read the full project name from XER files and the Primavera database and store it in the project title attribute.</li>
</ul>
<h2 id="720-2018-01-18">7.2.0 (2018-01-18)</h2>
<ul>
<li>Added support for reading TurboProject PEP files.</li>
<li>Handle numeric values with leading spaces in XER files.</li>
<li>Fix array bounds error when reading constraints from certain MPP files.</li>
</ul>
<h2 id="710-2018-01-03">7.1.0 (2018-01-03)</h2>
<ul>
<li>Added support for reading GanttProject GAN files.</li>
<li>Ensure that calendar exception dates are read correctly from XER files and P6 databases regardless of the user's timezone.</li>
<li>Read working day calendar exceptions from XER files and P6 database.</li>
<li>Mark some ProjectFile methods as deprecated.</li>
</ul>
<h2 id="703-2017-12-21">7.0.3 (2017-12-21)</h2>
<ul>
<li>Use the Windows-1252 character set as the default when reading XER files.</li>
<li>Gracefully handle POI issue 61677 to allow MPP affected MPP files to be read successfully.</li>
<li>Handle recurring calendar exceptions read from MSPDI files without an occurrence count.</li>
<li>Improve robustness of FastTrack schedule reader.</li>
<li>Avoid reading empty calendar exceptions from MPX files.</li>
</ul>
<h2 id="702-2017-11-20">7.0.2 (2017-11-20)</h2>
<ul>
<li>Further improvements to task pruning for Asta PP files.</li>
</ul>
<h2 id="701-2017-11-20">7.0.1 (2017-11-20)</h2>
<ul>
<li>Improve robustness when reading MPP files when using certain 64-bit Java runtimes.</li>
<li>Populate the project's comments property when reading an MSPDI file.</li>
<li>Ensure that tasks are not discarded when reading PP files from older Asta versions.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/319">Issue 319</a>: Wrong date ranges for split tasks</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/222">Issue 222</a>: getDefaultTaskType() not returning correct default task type</li>
</ul>
<h2 id="700-2017-11-08">7.0.0 (2017-11-08)</h2>
<ul>
<li>Added support for reading recurring exceptions from MPP and MSPDI files.</li>
<li>Updated RecurringTask class interface (Note: this is a breaking API change)</li>
<li>MSPDI writer now uses save version 14 by default (Note: this may affect applications which consume MSPDI files you generate)</li>
<li>Correctly handle MSPDI files with Byte Order Marks.</li>
<li>Handle MSPDI files with varying namespaces.</li>
<li>Improve robustness Merlin file reader.</li>
<li>Improve extraction of task start and finish dates from PMXML files only containing partial data.</li>
<li>Prevent POI from closing the input stream when using UniversalProjectReader</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/321">Issue 321</a>: Cannot read mpp file using getProjectReader.</li>
</ul>
<h2 id="621-2017-10-11">6.2.1 (2017-10-11)</h2>
<ul>
<li>Gracefully handle corrupt MPP files.</li>
<li>Improve reading and writing slack values for MSPDI files.</li>
<li>Improve activity hierarchy extraction from Phoenix files.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/243">Issue 243</a>: MSPDI Slack values not correctly set while loading.</li>
</ul>
<h2 id="620-2017-10-06">6.2.0 (2017-10-06)</h2>
<ul>
<li>Added support for reading Work Weeks from MPP files.</li>
<li>Add support for calendar exception names for MPP and MSPDI files.</li>
<li>Updated to use POI 3.17.</li>
<li>Improve accuracy of calendar exception dates read from XER files and P6 database.</li>
<li>Only write non-default user-defined field values to a PMXML file.</li>
<li>Use Primavera P6 17.7 XML schema.</li>
<li>Gracefully handle corrupt document summary information in MPP files.</li>
<li>Don't duplicate exceptions when reading from an MSPDI file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/231">Issue 231</a>: MPP DataType: Non-unique enumeration value.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/258">Issue 258</a>: Calendar Work Week missing from MPP data extraction.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/318">Issue 318</a>: TimephasedWork Negative TotalAmount.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/320">Issue 320</a>: Date conversion fails in PrimaveraReader.</li>
</ul>
<h2 id="612-2017-09-12">6.1.2 (2017-09-12)</h2>
<ul>
<li>Gracefully handle incomplete records in XER files.</li>
</ul>
<h2 id="611-2017-08-30">6.1.1 (2017-08-30)</h2>
<ul>
<li>Ensure all classes in the gem are required</li>
</ul>
<h2 id="610-2017-07-28">6.1.0 (2017-07-28)</h2>
<ul>
<li>Provide Task.getEffectiveCalendar() method</li>
<li>Populate missing finish dates in MSPDI files</li>
</ul>
<h2 id="600-2017-07-22">6.0.0 (2017-07-22)</h2>
<ul>
<li>Gracefully handle invalid calendar data in XER files.</li>
<li>Handle XER files containing blank lines.</li>
<li>Add support for reading resource rates and availability tables from P6 (Contributed by Brandon Herzog).</li>
<li>Include overtime in work and cost fields when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Read default project calendar from P6 (Contributed by Brandon Herzog).</li>
<li>Read resource rate and assignment units from P6 (Contributed by Brandon Herzog).</li>
<li>Set ignore resource calendar flag for tasks from P6 (Contributed by Brandon Herzog).</li>
<li>Change P6 costs to be calculated from resource assignment to support XER files without the cost table (Contributed by Brandon Herzog).</li>
<li>Map anticipated end date to deadline for P6 (Contributed by Brandon Herzog).</li>
<li>Update task work to include actual and remaining work when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Calculate summary task work fields by summing up children when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Set task project name when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Fix "00:00" calendar finish times to parse as end of day when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Add default working hours if a calendar does not specify any hours when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Read fiscal year start month from P6 (Contributed by Brandon Herzog).</li>
<li>Fix bug in rollup of child task dates containing null values that could set incorrect end date when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Fix date offset in parse of P6 calendar exceptions (Contributed by Brandon Herzog).</li>
<li>Fix count of P6 UDFs that map to same data type (Contributed by Brandon Herzog).</li>
<li>Add support for reading Resource and Assignment UDFs from P6 (Contributed by Brandon Herzog).</li>
<li>Update P6 UDFs to fill into multiple field types to expand storage capacity, for example into TEXT and ENTERPRISE_TEXT (Contributed by Brandon Herzog).</li>
<li>Use only the WBS as activity code for WBS tasks instead of also appending name for P6 tasks (Contributed by Brandon Herzog).</li>
<li>Add the ability to link task Relations that cross project boundaries in XER files (Contributed by Brandon Herzog).</li>
<li>Add function to clear all exceptions from ProjectCalendar instances (Contributed by Brandon Herzog).</li>
<li>Reading the lag calendar scheduling option as the "LagCalendar" custom project property when reading from P6 (Contributed by Brandon Herzog).</li>
<li>Updated UDF parsing to handle values as booleans if the user chooses to map them to Flag fields (Contributed by Brandon Herzog).</li>
</ul>
<h2 id="5140-2017-07-13">5.14.0 (2017-07-13)</h2>
<ul>
<li>Improve handling of activity codes read from Phoenix files</li>
<li>Calculate percent complete for tasks read from Phoenix files</li>
<li>Populate task duration with Original Duration attribute when reading from XER files or P6 databases.</li>
<li>Ensure that task finish dates are read correctly from Phoenix files.</li>
<li>Improve UniversalProjectReader's handling of non-MPP OLE compound documents.</li>
<li>Improve task hierarchy and ordering when reading some MPP files.</li>
</ul>
<h2 id="5130-2017-06-27">5.13.0 (2017-06-27)</h2>
<ul>
<li>Further improve handling of WBS, bar, and task structure from Asta files.</li>
</ul>
<h2 id="5120-2017-06-26">5.12.0 (2017-06-26)</h2>
<ul>
<li>Improve handling of WBS, bar, and task structure from Asta files.</li>
</ul>
<h2 id="5110-2017-06-20">5.11.0 (2017-06-20)</h2>
<ul>
<li>Improve handling of malformed durations in MSPDI files.</li>
<li>Improve performance when reading MPP files with certain kinds of timephased data.</li>
<li>Raise a specific "password protected" exception type from the Ruby gem.</li>
<li>Fix an issue with the storage of the "earned value method" task attribute.</li>
</ul>
<h2 id="5100-2017-05-23">5.10.0 (2017-05-23)</h2>
<ul>
<li>Improve handling of deleted tasks in MPP files.</li>
<li>Improve handling of invalid predecessor tasks in MPX files.</li>
<li>Improve handling of invalid saved view state in MPP files.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/313">Issue 313</a>: Empty baseline dates populated with garbage date instead of null.</li>
</ul>
<h2 id="590-2017-04-27">5.9.0 (2017-04-27)</h2>
<ul>
<li>Add support for reading ProjectLibre POD files (from ProjectLibre version 1.5.5 onwards).</li>
<li>Correct getter method name for "file application" project property.</li>
</ul>
<h2 id="580-2017-04-21">5.8.0 (2017-04-21)</h2>
<ul>
<li>Updated to use POI 3.16 (note new dependency on Apache Commons Collections required by POI).</li>
<li>Improve support for estimated durations in Merlin files.</li>
<li>Read task notes from Asta files.</li>
<li>Improve support for reading resource rates from Phoenix files.</li>
<li>Add "file application" and "file type" to project properties to determine source of schedule data.</li>
</ul>
<h2 id="571-2017-03-22">5.7.1 (2017-03-22)</h2>
<ul>
<li>Improve support for Phoenix Project Manager XML files.</li>
</ul>
<h2 id="570-2017-03-20">5.7.0 (2017-03-20)</h2>
<ul>
<li>Add support for FastTrack Schedule files.</li>
<li>Ensure that timephased data calculations correctly handle entry to and exit from DST.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/306">Issue 306</a>: Microsoft Project 2016:  Issue with assignment 'Work Contour' attribute.</li>
</ul>
<h2 id="565-2017-03-07">5.6.5 (2017-03-07)</h2>
<ul>
<li>Improve handling of invalid calendar data in MSPDI files</li>
<li>Improve handling of XER files containing multi-line records</li>
<li>Improve handling of malformed MPX files</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/308">Issue 308</a>: Add support for elapsed percent to MSPDI writer</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/310">Issue 310</a>: MPX percent lag incorrect</li>
</ul>
<h2 id="564-2017-02-16">5.6.4 (2017-02-16)</h2>
<ul>
<li>UniversalProjectReader now recognises and handles byte order marks</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/307">Issue 307</a>: TimeUnit.ELAPSED_PERCENT read incorrectly from MPP files</li>
</ul>
<h2 id="563-2017-02-08">5.6.3 (2017-02-08)</h2>
<ul>
<li>Added a parameter to the Ruby gem to allow the maximum JVM memory size to be set.</li>
<li>Updated to rtfparserkit 1.10.0 for improved RTF parsing.</li>
</ul>
<h2 id="562-2017-02-06">5.6.2 (2017-02-06)</h2>
<ul>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/305">Issue 305</a>: Failed to Parse error with Primavera 15.2 or 16.1 XML files</li>
</ul>
<h2 id="561-2017-02-03">5.6.1 (2017-02-03)</h2>
<ul>
<li>Correct resource assignment handling for Phoenix Project Manager schedules.</li>
</ul>
<h2 id="560-2017-01-29">5.6.0 (2017-01-29)</h2>
<ul>
<li>Add support for Phoenix Project Manager schedules.</li>
</ul>
<h2 id="559-2017-01-27">5.5.9 (2017-01-27)</h2>
<ul>
<li>Improve robustness of date parsing for MPX files.</li>
</ul>
<h2 id="558-2017-01-23">5.5.8 (2017-01-23)</h2>
<ul>
<li>Fix NPE when reading graphical indicators with unknown field type.</li>
</ul>
<h2 id="557-2017-01-13">5.5.7 (2017-01-13)</h2>
<ul>
<li>Fix percent complete NaN value for some Primavera schedules.</li>
</ul>
<h2 id="556-2017-01-06">5.5.6 (2017-01-06)</h2>
<ul>
<li>Fix incorrectly set critical flag for primavera schedules.</li>
</ul>
<h2 id="555-2017-01-06">5.5.5 (2017-01-06)</h2>
<ul>
<li>Updated to rtfparserkit 1.9.0 for improved RTF parsing</li>
<li>Improve calendar exception parsing for Primavera XER and database readers.</li>
<li>Ensure the task summary flag is set correctly for Primavera schedules.</li>
<li>Rollup baseline, early and late start and finish dates to WBS for Primavera schedules.</li>
<li>Rollup baseline duration, remaining duration and percent complete to WBS for Primavera schedules.</li>
<li>Use the project's critical slack limit value when setting the critical flag on a task.</li>
<li>Experimental support for reading Merlin Project schedules.</li>
</ul>
<h2 id="554-2016-12-01">5.5.4 (2016-12-01)</h2>
<ul>
<li>Default to UTF-8 encoding when generating JSON files</li>
</ul>
<h2 id="553-2016-11-29">5.5.3 (2016-11-29)</h2>
<ul>
<li>Correctly read text from MPP files when default charset is not UTF-8.</li>
<li>Improve accuracy when reading MPP9 files.</li>
</ul>
<h2 id="552-2016-11-02">5.5.2 (2016-11-02)</h2>
<ul>
<li>Add Primavera Parent Resource ID as a specific resource attribute (Based on a contribution by Dave McKay).</li>
<li>PMXML writer generates currency record (Based on a contribution by Dave McKay).</li>
<li>PMXML writer defaults Activity PercentCompleteType to Duration (Based on a contribution by Dave McKay).</li>
<li>PMXML writer records currency and parent attributes for Resource (Based on a contribution by Dave McKay).</li>
<li>PMXML writer resource assignments include RateSource and ActualOvertimeUnits attributes(Based on a contribution by Dave McKay).</li>
<li>MSPDI reader: gracefully handle invalid calendar exceptions..</li>
<li>PMXML writer: gracefully handle missing data.</li>
<li>Planner writer: gracefully handle missing data.</li>
</ul>
<h2 id="551-2016-10-14">5.5.1 (2016-10-14)</h2>
<ul>
<li>Update universal project reader to support zip files.</li>
<li>Update ruby to align error handling with universal project reader.</li>
</ul>
<h2 id="550-2016-10-13">5.5.0 (2016-10-13)</h2>
<ul>
<li>Universal project reader.</li>
<li>Avoid NPE when reading PMXML files.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/297">Issue 297</a>: Missing extended attributes</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/300">Issue 300</a>: CrossProject field omission causes issues when importing to P6</li>
</ul>
<h2 id="540-2016-10-06">5.4.0 (2016-10-06)</h2>
<ul>
<li>Updated to use POI 3.15.</li>
</ul>
<h2 id="533-2016-08-31">5.3.3 (2016-08-31)</h2>
<ul>
<li>Avoid NPE when field type is unknown.</li>
<li>Improve Ruby error reporting.</li>
<li>Improve support for non-standard time formats in MPX files</li>
<li>Improve support for MPP14 files with very large numbers of blank tasks</li>
</ul>
<h2 id="532-2016-08-31">5.3.2 (2016-08-31)</h2>
<ul>
<li>When reading an XER file, treat FT_STATICTPYE user defined fields as text.</li>
</ul>
<h2 id="531-2016-07-01">5.3.1 (2016-07-01)</h2>
<ul>
<li>Add data date attribute to PMXML output.</li>
<li>Update PMXML writer to avoid NPE.</li>
<li>Update PMXML writer to allow task field used for Activity ID to be chosen.</li>
<li>Updated to avoid NPE when reading an XER file where project not under EPS.</li>
<li>Generate Task IDs if missing from MSPDI file</li>
</ul>
<h2 id="530-2016-06-10">5.3.0 (2016-06-10)</h2>
<ul>
<li>Add support for PP files generated by Asta Powerproject from version 13.0.0.3 onwards</li>
<li>Minor improvements to SDEF support.</li>
<li>Updated to rtfparserkit 1.8.0</li>
<li>Improve finish time handling in PMXML files (contributed by lobmeleon)</li>
</ul>
<h2 id="522-2016-03-11">5.2.2 (2016-03-11)</h2>
<ul>
<li>Add support for resource assignment Stop and Resume attributes for MPP and MSPDI files</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/291">Issue 291</a>: PrimaveraPMFileWriter.write fails with java.lang.IllegalArgumentException</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/292">Issue 292</a>: Microsoft Project 2016 : Need to set 'Stop' and 'Resume' properties for org.mpxj.ResourceAssignment</li>
</ul>
<h2 id="521-2016-02-11">5.2.1 (2016-02-11)</h2>
<ul>
<li>Add support for PP files generated by Asta Powerproject up to version 13.0.0.3</li>
</ul>
<h2 id="520-2016-02-08">5.2.0 (2016-02-08)</h2>
<ul>
<li>Add support for PP files generated by Asta Powerproject 11, Powerproject 12, Easyplan 2, Easyplan 3, Easyplan 4, Easyplan 5 and Easyplan 6</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/285">Issue 285</a>: Unsupported encoding command ansicpg949</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/288">Issue 288</a>: AvailabilityTable getEntryByDate does not work properly</li>
</ul>
<h2 id="5118-2016-01-25">5.1.18 (2016-01-25)</h2>
<ul>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/285">Issue 285</a>: Unsupported encoding command ansicpg1254</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/286">Issue 286</a>: NullPointerException in CriteriaReader.getConstantValue</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/287">Issue 287</a>: Allow a character encoding to be specified when reading an XER file</li>
<li>Write Primavera Primary Resource Unique ID to Task field Number1</li>
</ul>
<h2 id="5117-2015-12-30">5.1.17 (2015-12-30)</h2>
<ul>
<li>Improve support for reading MPP files generated by Project 2016</li>
<li>Handle missing time component of a time stamp field when reading an MPX file.</li>
</ul>
<h2 id="5116-2015-12-18">5.1.16 (2015-12-18)</h2>
<ul>
<li>Improve support for reading MPX files generated by SureTrak</li>
</ul>
<h2 id="5115-2015-12-16">5.1.15 (2015-12-16)</h2>
<ul>
<li>Fix WBS and Activity ordering for tasks from Primavera.</li>
</ul>
<h2 id="5114-2015-12-09">5.1.14 (2015-12-09)</h2>
<ul>
<li>Strip unescaped control characters from JSON output.</li>
</ul>
<h2 id="5113-2015-11-26">5.1.13 (2015-11-26)</h2>
<ul>
<li>For schedules imported from Primavera ensure tasks representing activities are ordered by Activity ID within the WBS to match Primavera.</li>
</ul>
<h2 id="5112-2015-11-16">5.1.12 (2015-11-16)</h2>
<ul>
<li>Avoid NPE when writing MSPDI files with timephased data  (contributed by Bruno Gasnier)</li>
<li>Improve resource assignment constructor (based on a contribution by Bruno Gasnier)</li>
<li>Improve MPX French translations (contributed by Bruno Gasnier)</li>
<li>Add calendar specific minutes per day, week, month, and year (based on a contribution by Bruno Gasnier)</li>
<li>Add support for reading and writing GUID attribute for PMXML, XER files and Primavera database.</li>
</ul>
<h2 id="5111-2015-11-12">5.1.11 (2015-11-12)</h2>
<ul>
<li>Avoid NPE when reading MPP14 custom properties.</li>
<li>Ensure calculated task attributes are present in JSON output.</li>
<li>Handle MSPDI files written by German versions of Microsoft Project (based on a contribution by Lord Helmchen)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/277">Issue 277</a>: synchronizeTaskIDToHierarchy clears list of tasks</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/273">Issue 273</a>: PrimaveraPMFileWriter throws Exception at write(..)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/281">Issue 281</a>: Parent task is always null when reading a Primavera XER file</li>
<li>Ensure that Task.getSuccesors() and Task.getPredecessors() return an empty list rather than null.</li>
</ul>
<h2 id="5110-2015-09-09">5.1.10 (2015-09-09)</h2>
<ul>
<li>Improve FixedMeta2 block size heuristic to improve reliability when reading MPP14 files.</li>
</ul>
<h2 id="519-2015-08-29">5.1.9 (2015-08-29)</h2>
<ul>
<li>Ensure Resource BookingType is read correctly from MPP files</li>
<li>Added basic custom field attributes to JSON output</li>
<li>Added Ruby methods to work with custom field aliases</li>
<li>Fix to infinite loop condition when writing calendar (contributed by lobmeleon)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/274">Issue 274</a>: MPXJ getNotes() API returns garbled value for multibyte characters</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/268">Issue 268</a>: Unsupported encoding error when reading resource notes</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/256">Issue 256</a>: Incorrect resource types are read (contributed by Colin Rodriguez)</li>
<li>Symmetry between Primavera PM reader/writer (contributed by lobmeleon)</li>
<li>Added UDF support to PMXML file reader and writer(contributed by lobmeleon)</li>
<li>Updated to rtfparserkit 1.4.0</li>
</ul>
<h2 id="518-2015-07-13">5.1.8 (2015-07-13)</h2>
<ul>
<li>Another attempt at getting tzinfo-data dependency working</li>
</ul>
<h2 id="517-2015-07-13">5.1.7 (2015-07-13)</h2>
<ul>
<li>Updated ruby gem to make tzinfo-data dependency conditional on platform</li>
</ul>
<h2 id="516-2015-07-13">5.1.6 (2015-07-13)</h2>
<ul>
<li>Updated ruby gem to allow timezone to be provided</li>
</ul>
<h2 id="515-2015-06-05">5.1.5 (2015-06-05)</h2>
<ul>
<li>Updated to use IKVM 8.0.5449.1</li>
</ul>
<h2 id="514-2015-06-03">5.1.4 (2015-06-03)</h2>
<ul>
<li>Updated to generate Activity ID for Primavera WBS.</li>
<li>Updated to correct Primavera duration percent complete calculation.</li>
</ul>
<h2 id="513-2015-05-18">5.1.3 (2015-05-18)</h2>
<ul>
<li>Updated to ensure Ruby reads Boolean attributes correctly.</li>
</ul>
<h2 id="512-2015-05-18">5.1.2 (2015-05-18)</h2>
<ul>
<li>Updated to ensure Ruby recognises short type as an integer.</li>
</ul>
<h2 id="511-2015-05-18">5.1.1 (2015-05-18)</h2>
<ul>
<li>Updated to use ruby-duration gem to avoid conflict with ActiveSupport::Duration.</li>
</ul>
<h2 id="510-2015-05-17">5.1.0 (2015-05-17)</h2>
<ul>
<li>Updated to ensure that PrimaveraDatabaseReader.setSchema accepts null or empty string</li>
<li>Ensure conversion to/from .Net DateTime takes account of timezone and daylight savings (based on a contribution by Timour Koupeev)</li>
<li>Updated to use POI 3.12.</li>
<li>Removed ProjectFile.getTaskFieldAliases, replaced by ProjectFile.getCustomField().getFieldByAlias</li>
<li>Removed ProjectFile.getResourceFieldAliases, replaced by ProjectFile.getCustomField().getFieldByAlias</li>
</ul>
<h2 id="500-2015-05-06">5.0.0 (2015-05-06)</h2>
<ul>
<li>Added project properties to the JSON output</li>
<li>Added support for project properties to the Ruby wrapper</li>
<li>Added support for reading data from a standalone Primavera P6 SQLite database</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/267">Issue 267</a>: XXE security vulnerability</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/266">Issue 266</a>: Task Number fields not saved to file if the value would floor to zero</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/255">Issue 255</a>: Not all project calendars are read in for Project 2013 files (based on a contribution by Colin Rodriguez)</li>
<li>Renamed TaskContainer class to ChildTaskContainer</li>
<li>Renamed ProjectHeader class to ProjectProperties</li>
<li>Introduced ProjectConfig class</li>
<li>Introduced TaskContainer class</li>
<li>Introduced ResourceContainer class</li>
<li>Introduced ResourceAssignmentContainer class</li>
<li>Introduced ProjectCalendarContainer class</li>
<li>Renamed ProjectFile.getProjectHeader to getProjectProperties</li>
<li>Renamed ProjectFile.getCalendar to getDefaultCalendar</li>
<li>Renamed ProjectFile.setCalendar to setDefaultCalendar</li>
<li>Renamed MppReader.getReadHeaderOnly to getReadPropertiesOnly</li>
<li>Renamed MppReader.setReadHeaderOnly to setReadPropertiesOnly</li>
<li>Renamed ProjectFile.getCalendarUniqueID to ProjectConfig.getNextCalendarUniqueID</li>
<li>Renamed ProjectFile.getResourceUniqueID to ProjectConfig.getNextResourceUniqueID</li>
<li>Renamed ProjectFile.getTaskUniqueID to ProjectConfig.getNextTaskUniqueID</li>
<li>Renamed ProjectFile.getAssignmentUniqueID to ProjectConfig.getNextAssignmentUniqueID</li>
<li>Renamed ProjectFile.getResourceID to ProjectConfig.getNextResourceID</li>
<li>Renamed ProjectFile.getTaskID to ProjectConfig.getNextTaskID</li>
<li>Renamed ProjectHeader.getApplicationName to getShortApplicationName</li>
<li>Renamed ProjectHeader.setApplicationName to setShortApplicationName</li>
<li>Renamed ProjectHeader.setCalendarName to setDefaultCalendarName</li>
<li>Renamed ProjectHeader.getCalendarName to getDefaultCalendarName</li>
<li>Moved ProjectFile.getProjectFilePath to ProjectHeader.getProjectFilePath</li>
<li>Moved ProjectFile.setProjectFilePath to ProjectHeader.setProjectFilePath</li>
<li>Moved ProjectFile.getApplicationName to ProjectHeader.getFullApplicationName</li>
<li>Moved ProjectFile.setApplicationName to ProjectHeader.setFullApplicationName</li>
<li>Moved FileCreationRecord.setDelimiter to ProjectHeader.setMpxDelimiter</li>
<li>Moved FileCreationRecord.getDelimiter to ProjectHeader.getMpxDelimiter</li>
<li>Moved FileCreationRecord.setProgramName to ProjectHeader.setMpxProgramName</li>
<li>Moved FileCreationRecord.getProgramName to ProjectHeader.getMpxProgramName</li>
<li>Moved FileCreationRecord.setFileVersion to ProjectHeader.setMpxFileVersion</li>
<li>Moved FileCreationRecord.getFileVersion to ProjectHeader.getMpxFileVersion</li>
<li>Moved FileCreationRecord.setCodePage to ProjectHeader.setMpxCodePage</li>
<li>Moved FileCreationRecord.getCodePage to ProjectHeader.getMpxCodePage</li>
<li>Moved ProjectFile.getMppFileType to ProjectHeader.getMppFileType</li>
<li>Moved ProjectFile.setMppFileType to ProjectHeader.setMppFileType</li>
<li>Moved ProjectFile.getApplicationVersion to ProjectHeader.getApplicationVersion</li>
<li>Moved ProjectFile.setApplicationVersion to ProjectHeader.setApplicationVersion</li>
<li>Moved ProjectFile.setAutoFilter to ProjectHeader.setAutoFilter</li>
<li>Moved ProjectFile.getAutoFilter to ProjectHeader.getAutoFilter</li>
<li>Removed ProjectFile.getAliasTaskField, replaced by ProjectFile.getTaskFieldAliases().getField()</li>
<li>Removed ProjectFile.getAliasResourceField, replaced by ProjectFile.getResourceFieldAliases().getField()</li>
<li>Removed ProjectFile.getTaskFieldAlias, replaced by ProjectFile.getTaskFieldAliases().getAlias()</li>
<li>Removed ProjectFile.setTaskFieldAlias, replaced by ProjectFile.getTaskFieldAliases().setAlias()</li>
<li>Removed ProjectFile.getResourceFieldAlias, replaced by ProjectFile.getResourceFieldAliases().getAlias()</li>
<li>Removed ProjectFile.setResourceFieldAlias, replaced by ProjectFile.getResourceFieldAliases().setAlias()</li>
<li>Removed ProjectFile.getTaskFieldAliasMap, replaced by ProjectFile.getTaskFieldAliases</li>
<li>Removed ProjectFile.getResourceFieldAliasMap, replaced by ProjectFile.getResourceFieldAliases</li>
<li>Removed ProjectFile.addTable, replaced by ProjectFile.getTables().add()</li>
<li>Removed ProjectFile.getTaskTableByName, replaced by ProjectFile.getTables().getTaskTableByName()</li>
<li>Removed ProjectFile.getResourceTableByName, replaced by ProjectFile.getTables().getResourceTableByName()</li>
<li>Removed ProjectFile.addFilter, replaced by ProjectFile.getFilters().addFilter()</li>
<li>Removed ProjectFile.removeFilter, replaced by ProjectFile.getFilters().rmoveFilter()</li>
<li>Removed ProjectFile.getAllResourceFilters, replaced by ProjectFile.getFilters().getResourceFilters()</li>
<li>Removed ProjectFile.getAllTaskFilters, replaced by ProjectFile.getFilters().getTaskFilters()</li>
<li>Removed ProjectFile.getFilterByName, replaced by ProjectFile.getFilters().getFilterByName()</li>
<li>Removed ProjectFile.getFilterByID, replaced by ProjectFile.getFilters().getFilterByID()</li>
<li>Removed ProjectFile.getAllGroups, replaced by ProjectFile.getGroups()</li>
<li>Removed ProjectFile.getGroupByName, replaced by ProjectFile.getGroups().getByName()</li>
<li>Removed ProjectFile.addGroups, replaced by ProjectFile.getGroups().add()</li>
<li>Removed ProjectFile.addView, replaced by ProjectFile.getViews().add()</li>
<li>Removed ProjectFile.setViewState, replaced by ProjectFile.getViews().setViewState()</li>
<li>Removed ProjectFile.getViewState, replaced by ProjectFile.getViews().getViewState()</li>
<li>Removed ProjectFile.getResourceSubProject, replaced by ProjectFile.getSubProjects().getResourceSubProject()</li>
<li>Removed ProjectFile.setResourceSubProject, replaced by ProjectFile.getSubProjects().setResourceSubProject()</li>
<li>Removed ProjectFile.addSubProject, replaced by ProjectFile.getSubProjects().add()</li>
<li>Removed ProjectFile.getAllSubProjects, replaced by ProjectFile.getSubProjects</li>
<li>Removed ProjectFile.fireTaskReadEvent, replaced by ProjectFile.getEventManager().fireTaskReadEvent()</li>
<li>Removed ProjectFile.fireTaskWrittenEvent, replaced by ProjectFile.getEventManager().fireTaskWrittenEvent()</li>
<li>Removed ProjectFile.fireResourceReadEvent, replaced by ProjectFile.getEventManager().fireResourceReadEvent()</li>
<li>Removed ProjectFile.fireResourceWrittenEvent, replaced by ProjectFile.getEventManager().fireResourceWrittenEvent()</li>
<li>Removed ProjectFile.fireCalendarReadEvent, replaced by ProjectFile.getEventManager().fireCalendarReadEvent()</li>
<li>Removed ProjectFile.fireAssignmentReadEvent, replaced by ProjectFile.getEventManager().fireAssignmentReadEvent()</li>
<li>Removed ProjectFile.fireAssignmentWrittenEvent, replaced by ProjectFile.getEventManager().fireAssignmentWrittenEvent()</li>
<li>Removed ProjectFile.fireRelationReadEvent, replaced by ProjectFile.getEventManager().fireRelationReadEvent()</li>
<li>Removed ProjectFile.fireRelationWrittenEvent, replaced by ProjectFile.getEventManager().fireRelationWrittenEvent()</li>
<li>Removed ProjectFile.fireCalendarWrittenEvent, replaced by ProjectFile.getEventManager().fireCalendarWrittenEvent()</li>
<li>Removed ProjectFile.addProjectListener, replaced by ProjectFile.getEventManager().addProjectListener()</li>
<li>Removed ProjectFile.addProjectListeners, replaced by ProjectFile.getEventManager().addProjectListeners()</li>
<li>Removed ProjectFile.removeProjectListener, replaced by ProjectFile.getEventManager().removeProjectListener()</li>
<li>Removed ProjectFile.addGraphicalIndicator</li>
<li>Removed ProjectFile.getGraphicalIndicator, replaced by ProjectFile.getCustomFields().getCustomField().getGraphicalIndicator()</li>
</ul>
<h2 id="476-2015-03-18">4.7.6 (2015-03-18)</h2>
<ul>
<li>Added a Ruby wrapper for MPXJ</li>
<li>Added the ability to export project data as JSON, to make it easier to work with in languages other than Java</li>
<li>Added support for the Assignment attribute Resource Request Type</li>
<li>Primavera database and XER readers updated to match WBS visible in Primavera for each task. Previous behaviour of generating a unique WBS for each task can be restored using a flag set on the readers.</li>
<li>Avoid NPE when calculating Task Completed Through</li>
<li>Read Task Earned Value Method correctly from MPP files</li>
<li>Fix issue where some floating point attributes were returning NaN</li>
</ul>
<h2 id="475-2015-02-27">4.7.5 (2015-02-27)</h2>
<ul>
<li>Handle invalid Primavera calendar data gracefully</li>
</ul>
<h2 id="474-2015-02-25">4.7.4 (2015-02-25)</h2>
<ul>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/257">Issue 257</a>: Failed to read project containing CodePage 1250 text.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/259">Issue 259</a>: MS Project 2010: tasks with null baseline dates</li>
<li>Incorrect task end date read from Primavera XER and database</li>
<li>Incorrect percent complete read from Primavera XER, database, and PMXML files</li>
<li>Failed to read fields held at the end of a fixed data block</li>
<li>Added support for Task Baseline Estimated Duration, Baseline Estimated Start, Baseline Estimated Finish, Baseline Fixed Cost, and Baseline Fixed Cost Accrual</li>
<li>Added the ability to customise the fields read from a Primavera database or XER file.</li>
<li>Added Task Activity Type and Task Status as additional fields read from Primavera database and XER and files</li>
<li>Changed Task physical percent complete methods for consistency to use Number rather than Integer</li>
</ul>
<h2 id="473-2014-12-23">4.7.3 (2014-12-23)</h2>
<ul>
<li>Updated to use POI 3.11.</li>
<li>Updated to use rtfparserkit 1.1.0 for Java 6 compatibility.</li>
</ul>
<h2 id="472-2014-12-15">4.7.2 (2014-12-15)</h2>
<ul>
<li>Updated to fix Maven dependency issue.</li>
</ul>
<h2 id="471-2014-12-08">4.7.1 (2014-12-08)</h2>
<ul>
<li>Added a flag to MPPReader to indicate that only the project header should be read.</li>
</ul>
<h2 id="470-2014-12-04">4.7.0 (2014-12-04)</h2>
<ul>
<li>Implemented new RTF parser for stripping RTF to improve performance and accuracy</li>
<li>Removed non-API code from the top level package</li>
<li>Improved support for reading built-in and custom project properties from MPP files.</li>
<li>Improved resilience of MPP file reading to unknown data structures</li>
<li>Fixed issue which could cause an infinite loop when ordering tasks in a file containing multiple consecutive blank tasks</li>
<li>Fixed issue where free text versions of task start, finish, and duration fields were not being read correctly from MPP14 files</li>
</ul>
<h2 id="462-2014-11-11">4.6.2 (2014-11-11)</h2>
<ul>
<li>Fixed issue with custom duration field units not read correctly from MSPDI files</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/223">Issue 223</a>: Problems with the lag calculated in the relation</li>
<li>Outline code not read correctly from MPP file written by Project 2013</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/239">Issue 239</a>: Defensive changes to avoid exceptions when reading MPP files</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/250">Issue 250</a>: Deleted tasks being read from mpp file</li>
<li>Added DotNetInputStream and DotNetOutputStream classes for ease of use under .Net.</li>
<li>Updated to automatically generate and package MpxjUtilities.dll</li>
</ul>
<h2 id="461-2014-10-17">4.6.1 (2014-10-17)</h2>
<ul>
<li>Fixed NuGet metadata</li>
</ul>
<h2 id="460-2014-10-17">4.6.0 (2014-10-17)</h2>
<ul>
<li>Added support for NuGet.</li>
<li>Fixed an issue where the ID and Unique ID resource attributes were being read incorrectly from MPP14 files.</li>
<li>Fixed an issue where the project's default duration format was not being used</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/248">Issue 248</a>: Reading .MPP file using MPXJ 4.2 reads extra unintentional ResourceAssignment with the task which is not seen in Task Sheet in Microsoft Project</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/235">Issue 235</a>: All resources have "Material" property</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to capture the Project ID to align with data read from XER file/database (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to ensure task percent complete supports Physical Percent, Duration Percent and Units Percent (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to ensure task baseline values match values read from XER file/database (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to ensure task actual duration to matches value read from XER file/database (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to read the task duration (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to read task LateStart, LateFinish, EarlyStart, EarlyFinish attributes correctly (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to read task Start and End correctly (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to identify milestones (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to set the task Critical attribute (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera PM XML file reader to include costs (contributed by Nathaniel Marrin)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/247">Issue 247</a>: Updated Primavera XER/Database readers to read task Start and End correctly (contributed by Nathaniel Marrin)</li>
<li>Migrated tests to JUnit 4</li>
</ul>
<h2 id="450-2014-03-01">4.5.0 (2014-03-01)</h2>
<ul>
<li>Added the ability to call the .Net version of MPXJ from COM.</li>
<li>Added support Primavera decimal database columns.</li>
<li>Added support for user defined task fields (contributed by Mario Fuentes).</li>
<li>Added POM for current Maven versions (contributed by Nick Burch)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/213">Issue 213</a>: Unable to load mpp from project-2013</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/226">Issue 226</a>: Primavera currency files without currency information</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/227">Issue 227</a>: PrimaveraReader cannot handle files with more than 30 user defined fields</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/224">Issue 224</a>: setMilestone() issue</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/210">Issue 210</a>: MPXJ 4.4 and 2013 files - invalid load of task data</li>
<li>Updated to fix an issue with Asta Powerproject PP file tokenization</li>
<li>Updated to fix an issue where valid WBS values containing .0 are corrupted</li>
<li>Updated to allow Primavera hours per day to be a decimal value</li>
<li>Updated to support Primavera PM XML files generated by Primavera versions up to P6v8.3 (contributed by Mario Fuentes)</li>
<li>Updated to set the StatusDate attribute in the project header from a Primavera database, XER file or PM XML file.</li>
<li>Updated to use (a patched version of) POI 3.10.</li>
</ul>
<h2 id="440-2013-03-14">4.4.0 (2013-03-14)</h2>
<ul>
<li>Added support for writing Primavera PM XML files.</li>
<li>Added support for reading Asta Powerproject PP and MDB files.</li>
<li>Added support for writing SDEF files (Contributed by William Iverson).</li>
<li>Added support for reading Enterprise Custom Fields 1-50 for Task, Resources, and Resource Assignments.</li>
<li>Added MpxjExtensionMethods assembly to simplify working with Java types in .Net (Contributed by Kyle Patmore)</li>
<li>Provided two new .Net DLL versions in addition to the original version. These allow properties to be accessed in a ".Net style", and for languages apart from VB, provide .Net style method names.</li>
<li>Updated to remove the distinction between base calendar and resource calendars in the ProjectFile class.</li>
<li>Updated to improve support for custom outline codes (Contributed by Gary McKenney)</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/189">Issue 189</a>: getTimephasedOvertimeWork can return TimephasedWork with NaN</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/190">Issue 190</a>: Support for timephased cost for cost type resources</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/195">Issue 195</a>: Rolled Up tasks don't use default duration units</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/199">Issue 199</a>: Extract Primavera Task ID</li>
<li>Updated to fix an issue where the resource assignment delay attribute was not being read from or written to MSPDI files correctly</li>
<li>Updated to fix an issue where derived calendars were not being read correctly from MPP files</li>
<li>Updated to use IKVM 7.2.</li>
</ul>
<h2 id="430-2012-02-08">4.3.0 (2012-02-08)</h2>
<ul>
<li>Added support for reading Primavera PM XML files.</li>
<li>Added support for reading timephased cost, and timephased baseline cost and baseline work from MPP files.</li>
<li>Added support for Work Weeks in MSPDI files (SourceForge feature request 23).</li>
<li>Updated to use IKVM 7.0.</li>
<li>Updated to fix SourceForge bug 3290224: Incorrect order of tasks when writing an MSPDI file (contributed by Jonathan Besanceney).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/161">Issue 161</a>: ResourceAssignment.getTaskUniqueID() returns null.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/169">Issue 169</a>: Wrong project name in MPX file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/170">Issue 170</a>: Wrong title in XML file when importing from XER file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/168">Issue 168</a>: Wrong record number for resource calendar in MPX file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/171">Issue 171</a>: In the XML file the element field SaveVersion is missing.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/167">Issue 167</a>: Loop when import task with 0% on units of works in resources.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/163">Issue 163</a>: French locale NA incorrect.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/175">Issue 175</a>: Invalid dependency between child and parent.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/174">Issue 174</a>: Missing tasks from MS Project 2010 mpp file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/179">Issue 179</a>: Wrong WBS code and WBS when converting a Primavera XER file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/177">Issue 177</a>: Error reading XER file with German localisation for numbers.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/166">Issue 166</a>: TimephasedResourceAssignments with negative TotalWork.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/181">Issue 181</a>: Wrong currency symbol in the exported file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/104">Issue 104</a>: TimephasedResourceAssignment end date not correct.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/116">Issue 116</a>: Calendar hours are incorrect.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/188">Issue 188</a>: NullReferenceException with getTimephasedBaselineWork.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/191">Issue 191</a>: Outline number is null when opening Project 2003 MPP file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/192">Issue 192</a>: Unable to parse note (unknown locale).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/193">Issue 193</a>: MPP9Reader marks all tasks after a null task as null.</li>
<li>Updated to fix an issue where the Task critical attribute was incorrectly calculated for some manually scheduled tasks.</li>
<li>Updated to fix an issue where the Task summary attribute was not set correctly when using certain methods to add or remove child tasks.</li>
<li>Updated to fix an issue where subprojects were not read correctly (Contributed by Gary McKenney).</li>
</ul>
<h2 id="420-2011-06-23">4.2.0 (2011-06-23)</h2>
<ul>
<li>Added support for resource assignment fields Baseline Cost 1-n, Baseline Work 1-n, Baseline Start 1-n, Baseline Finish 1-n, Start 1-n, Finish 1-n, Date 1-n, Duration 1-n, Cost 1-n, Text 1-n, Number 1-n, Flag 1-n, for MPP, MPD, and MSPDI files.</li>
<li>Added support for task suspend date, task resume date, and task code read from Primavera, and represented in MS Project custom fields Date1, Date2, and Text1 respectively.</li>
<li>Added support for retrieving the table associated with any view.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/158">Issue 158</a>: Error converting Mpp to planner.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/157">Issue 157</a>: MSPDI Linklag for TimeUnit.Percent.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/156">Issue 156</a>: Error reading calendars for 2010 files.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/159">Issue 159</a>: Duplication of calendar id.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/153">Issue 153</a>: Wrong task start.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/156">Issue 156</a>: Wrong start and finish dates for 2010 files.</li>
</ul>
<h2 id="410-2011-05-30">4.1.0 (2011-05-30)</h2>
<ul>
<li>Updated ProjectFile class to change default value for "auto" flags to simplify programmatic creation of project files.</li>
<li>Added support for Manual, Start Text, Finish Text, and Duration Text attributes in MSPDI files.</li>
<li>Added support cost resource type for MPP12, MPP14 and MSPDI files.</li>
<li>Added Task.removePredecessor method (contributed by Leslie Damon).</li>
<li>Added "read presentation data" flag to MPPReader - allows clients to save time and memory when MPP presentation data not required.</li>
<li>Added support for reading Primavera calendars (contributed by Bruno Gasnier).</li>
<li>Added support for resource assignment leveling delay for MPP, MPD, and MSPDI files.</li>
<li>Added support for "unassigned" resource assignments.</li>
<li>Added support for task manual duration attribute for manually scheduled tasks in MPP14 and MSPDI files.</li>
<li>Added support for resource NT account attribute for MPP9, MPP12, and MPP14 files.</li>
<li>Added support for physical % complete for MPP9, MPP12, and MPP14 files.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/120">Issue 120</a>: MPXJ API returns the incorrect start date of a manual task.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/123">Issue 123</a>: Task id incorrect after importing from MPP14 file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/124">Issue 124</a>: MPXJ 4.0 fails to work with Project 2010 format.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/128">Issue 128</a>: Index was outside the bounds of the array.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/131">Issue 131</a>: header.getHonorConstraints() is not working in case of MPP.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/139">Issue 139</a>: Empty notes appear for all tasks when saving in XML format.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/122">Issue 122</a>: All Extended Attributes always added when using MSPDIWriter.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/144">Issue 144</a>: Baseline/Actual Work in 2010 MPP missing.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/114">Issue 114</a>: ResourceAssignment getCalendar not using IgnoreResourceCalendar flag</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/146">Issue 146</a>: ExternalTaskProject value missing.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/137">Issue 137</a>: Deleted Primavera tasks handling problem.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/143">Issue 143</a>: Latest CVS version gives wrong values for inactive field.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/125">Issue 125</a>: Task ID order when creating a project file is not correct.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/106">Issue 106</a>: Invalid tasks that should not be there.</li>
<li>Updated to fix task calendars read incorrectly from MPP14 files.</li>
<li>Updated to fix incorrect month duration assumption (contributed by Frank Illenberger).</li>
<li>Updated to fix incorrect number format in MSPDI file in non-English locales (contributed by Frank Illenberger).</li>
<li>Updated to fix incorrect resource assignment actual work attribute for MPP14 files.</li>
<li>Updated to fix incorrect task leveling delay attribute for MPP9, MPP12, and MPP14 files.</li>
<li>Updated to fix leveling delay and link lag when writing an MSPDI file (contributed by Frank Illenberger).</li>
<li>Updated to fix incorrect assignment actual start date when writing an MSPDI file.</li>
<li>Updated to improve support for material resources in MSPDI files.</li>
<li>Updated to reduce overall size of MSPDI files by not writing default values.</li>
<li>Updated to use IKVM 0.46.0.1.</li>
<li>Updated to use POI 3.7.</li>
<li>Updated to make task, resource, and assignment fields read from MPP files data-driven, rather than hard coded.</li>
</ul>
<h2 id="400-2010-05-25">4.0.0 (2010-05-25)</h2>
<ul>
<li>Added support for reading Microsoft Project 2010 MPP files.</li>
<li>Added support for reading Primavera P6 XER files.</li>
<li>Added support for reading Primavera P6 databases.</li>
<li>Updated to target Java 1.6.</li>
<li>Added Russian locale (Contributed by Roman Bilous).</li>
<li>Relation.getDuration() is always giving result in 'HOUR' fmt.</li>
</ul>
<h2 id="320-2010-01-20">3.2.0 (2010-01-20)</h2>
<ul>
<li>Added support for Resource cost rate tables (Based on code by Andrei Missine).</li>
<li>Added support for Resource availability (Based on code by Andrei Missine).</li>
<li>Added support for successors (Based on an idea by John D. Lewis).</li>
<li>Added support for task and resource GUIDs.</li>
<li>Added a flag to allow raw timephased data to be retrieved from MPP files.</li>
<li>Updated to fix logical operator read issue in MPP auto filters (Contributed by Andrei Missine).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/94">Issue 94</a>: MPXJ Issue: Related to Project Calendar.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/90">Issue 90</a>: POI License in legal folder of download wrong.</li>
<li>Updated to fix Steelray bug 15468: Null Pointer Exception reading task constraints.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/102">Issue 102</a>: Planner writer causes Null Pointer exception.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/100">Issue 100</a>: getRecurring() task is not working</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/98">Issue 98</a>: getStandardRateFormat() is returning 'null'</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/97">Issue 97</a>: getWeekStartDay() is not working.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/96">Issue 96</a>: getDaysPerMonth() is not working.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/101">Issue 101</a>: Resource.getNotes() not working for MPP12 file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/105">Issue 105</a>: MPP: getEditableActualCosts() is not behaving correctly.</li>
<li>Updated to use POI 3.6.</li>
<li>Updated to use IKVM 0.42.0.3.</li>
<li>Updated to make MPX duration parsing more lenient (Contributed by Jari Niskala).</li>
<li>Updated to make MPP Var2Data extraction more robust (Contributed by Jari Niskala).</li>
<li>Updated to implement MSPDI context caching to improve performance (Contributed by Jari Niskala).</li>
<li>Updated to improve MPP file task structure validation. (Contributed by Jari Niskala).</li>
<li>Updated to improve MPX file parsing. (Contributed by Jari Niskala).</li>
<li>Updated to automatically populate missing WBS attributes. (Contributed by Jari Niskala).</li>
<li>Updated to refactor the Relation class (note minor method name changes).</li>
<li>Updated to add default calendar to Planner output.</li>
</ul>
<h2 id="310-2009-05-20">3.1.0 (2009-05-20)</h2>
<ul>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/73">Issue 73</a>: Plan file fails to load.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/72">Issue 72</a>: Resource Assignment Normaliser rounding problem.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/78">Issue 78</a>: Column alignment values are incorrect.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/76">Issue 76</a>: NullPointerException in parseExtendedAttribute() (Contributed by Paul Pogonyshev).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/74">Issue 74</a>: .0 at the end of WBS code and outline number (Contributed by Paul Pogonyshev).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/79">Issue 79</a>: Too strict net.sf.mpxj.mpd.ResultSetRow.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/80">Issue 80</a>: Generated planner file can't be opened.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/82">Issue 82</a>: Support for loading global.mpt.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/81">Issue 81</a>: Lowercase table name won't work with db on linux machines.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/71">Issue 71</a>: Standard Calendar localization import problem.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/83">Issue 83</a>: Strange duration conversion from database</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/86">Issue 86</a>: FilterCriteria not being read in properly (Contributed by James Styles)</li>
<li>Updated to fix Steelray bug 12335: Infinite loop when reading an MPP9 file.</li>
<li>Updated to fix Steelray bug 8469: Subproject flag not set correctly.</li>
<li>Updated to fix potential NPEs (Suggested by Steve Jonik).</li>
<li>Updated EncryptedDocumentInputStream to wrap rather than extend the POI DocumentInputStream to allow use with POI 3.5. (Contributed by Josh Micich)</li>
<li>Updated to provide strong names for .Net DLLs.</li>
</ul>
<h2 id="300-2009-01-25">3.0.0 (2009-01-25)</h2>
<ul>
<li>Updated to the Project 2007 MSPDI schema.</li>
<li>Updated to POI 3.2.</li>
<li>Updated to use the SAX parser with JAXB rather than DOM to reduce memory consumption.</li>
<li>Updated MPX output to prevent Project 2007 complaining.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/68">Issue 68</a>: Task getNumber*() methods return inaccurate large values.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/56">Issue 56</a>: Duplicate task in file.getChildTasks() when opening MPX.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/57">Issue 57</a>: Relation.getTask returns null.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/58">Issue 58</a>: Task.getSplits() not consistent.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/60">Issue 60</a>: WBS Field not imported Mpp12.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/63">Issue 63</a>: There are some conflict in TaskField.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/66">Issue 66</a>: MSPDIReader is not setting calendarName in projectHeader.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/67">Issue 67</a>: Write resource calendar with exceptions only.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/69">Issue 69</a>: File loses predecessors.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/70">Issue 70</a>: Resources not bring read.</li>
<li>Updated to fix incorrect duration calculations where minutes per week were not being used (Contributed by Jonas Tampier).</li>
<li>Updated split task implementation to represent splits as DateRange instances rather than as hours.</li>
<li>Added .Net DLLs using IKVM.</li>
<li>Added support for reading timephased resource assignment data from MPP files.</li>
<li>Added support CurrencyCode, CreationDate, LastSaved and HyperlinkBase project header fields.</li>
<li>Added support for reading recurring task data from MPP files.</li>
<li>Added methods to MPXReader and MPXWriter to allow the caller to determine the supported locales.</li>
<li>Added Spanish locale (Contributed by Agustin Barto).</li>
<li>Added support for durations with percentage time lag (Contributed by Jonas Tampier).</li>
<li>Added support MSPDI file split tasks.</li>
</ul>
<h2 id="210-2008-03-23">2.1.0 (2008-03-23)</h2>
<ul>
<li>Updated to POI 3.0.2</li>
<li>Updated to address an out of memory exception raised when processing certain MPP12 files.</li>
<li>Updated to fix a problem caused by duplicate ID values in MPP12 files.</li>
<li>Updated to fix a problem with the subproject unique ID calculation (Contributed by Jari Niskala).</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/48">Issue 48</a>: Import from Project 2007 ignores some tasks.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/52">Issue 52</a>: Crash on priority not set in MSPDI-file.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/51">Issue 51</a>: Resource start/finish dates with MSP 2007.</li>
<li>Fixed <a href="https://sourceforge.net/p/mpxj/bugs/51">Issue 51</a>: MS Project 2007: Calendar exceptions dates are wrong.</li>
<li>Added support for Enterprise task and resource fields.</li>
<li>Added support for Baseline task and resource fields.</li>
<li>Added support for extracting non-English (i.e. character set encoded) text from note fields.</li>
<li>Added support for Chinese MPX files (contributed by Felix Tian).</li>
<li>Added support for reading project start and end dates from all MPP file types (Bug #1827633).</li>
<li>Added support for password protected MPP9 files (Contributed by Jari Niskala)</li>
<li>Added support for calendar exceptions for MPP12 files (Contributed by Jari Niskala)</li>
<li>Added support for value lists and descriptions for custom fields (Contributed by Jari Niskala)</li>
<li>Added support for timescale formats (Contributed by Jari Niskala)</li>
<li>Added support for the project file path attribute (Contributed by Jari Niskala)</li>
<li>Added support for the ignore resource calendar attribute (Contributed by Jari Niskala)</li>
<li>Added support for the resource actual overtime work attribute (Contributed by Jari Niskala)</li>
<li>Added support for the resource material label attribute (Contributed by Jari Niskala)</li>
<li>Added support for the resource NT account attribute (Contributed by Jari Niskala)</li>
<li>Improved support for hyperlinks (Contributed by Jari Niskala)</li>
<li>Improved support for custom fields in MPP12 files (Contributed by Jari Niskala)</li>
</ul>
<h2 id="200-2007-10-07">2.0.0 (2007-10-07)</h2>
<ul>
<li>Migrated to Java 5</li>
<li>Introduced generics</li>
<li>Introduced enums</li>
<li>Updated to POI 3.0.1</li>
<li>Updated to JAXB 2.1.4</li>
<li>Changed company details from Tapster Rock to Packwood Software</li>
</ul>
<h2 id="100-2007-08-30">1.0.0 (2007-08-30)</h2>
<ul>
<li>Added support for reading MPD files via JDBC</li>
<li>Added support for reading Planner files</li>
<li>Added support for over allocated flag to all MPP file formats.</li>
<li>Added support for calculating duration variance from MPP files.</li>
<li>Added support for calculating start and finish variance from MPP files.</li>
<li>Added support for attribute change listeners for Task and Resource classes.</li>
<li>Added support for start slack, finish slack, free slack and total slack read from MPP files.</li>
<li>Added support for external tasks.</li>
<li>Added unique ID generation for calendars read from MPX files.</li>
<li>Added support for the status date property of the project.</li>
<li>Fixed a timezone related bug when handling dates for calendar exceptions (Contributed by Todd Brannam).</li>
<li>Fixed incorrect calculation of lag times for some MPP files.</li>
<li>Fixed missing predecessor tasks in certain rare MPP9 files.</li>
<li>Fixed incorrect MPX file AM/PM text setting in certain locales.</li>
<li>Fixed an ArrayIndexOutOfBoundsException.</li>
<li>Fixed a ClassCastException.</li>
<li>Fixed a zero length string error.</li>
<li>Fixed a duration rounding error when reading MSPDI files.</li>
<li>Fixed incorrect "as late as possible" constraint handling.</li>
<li>Incorrect late start date read from an MPP9 file.</li>
<li>Incorrect total slack calculation.</li>
<li>Added a default for the task constraint type attribute to prevent a possible NPE when writing an MSPDI file.</li>
<li>Added a default resource calendar name where the resource name is empty.</li>
<li>Updated the Column.getTitle method to take account of user defined column aliases.</li>
<li>Updated to add another condition to the test for deleted tasks in MPP8 files.</li>
<li>Updated to significantly improve the performance of writing MSPDI files.</li>
</ul>
<h2 id="092-2006-03-07">0.9.2 (2006-03-07)</h2>
<ul>
<li>Added support for split views.</li>
<li>Added support for graphical indicators.</li>
<li>Added a workaround for a bug in MS Project which is seen when calendar exceptions are exported to an MSPDI file. If the exception contained seconds and milliseconds, MS Project marked every day as being affected by the exception, not the day or range of days specified.</li>
<li>Updated to make date/time/number formats generic, and thus available to end users. For example, this allows users to format currencies in line with the settings in the project file.</li>
<li>Standardised on minutes per day and minutes per week, rather than hours per day and hours per week.</li>
<li>Provided additional time ranges for calendar exceptions.</li>
<li>Refactored Task and Resource to use TaskField and ResourceField to identify fields.</li>
<li>Updated to automatically generate WBS for tasks read from MPP files when no WBS information is present in the file.</li>
<li>Fixed a bug when reading MPP files where task finish dates appeared before the start date where a "start no later than" constraint was in use.</li>
<li>Fixed a bug which resulted in invalid MPX files being generated when a project either had no tasks, or it had no resources.</li>
<li>Fixed a long-standing bug where the calendar records were being written into MPX files after they were referred to in the project summary record.</li>
<li>Fixed a bug where WBS and Outline Levels were not being auto generated correctly when an MPP file contained a project summary task.</li>
<li>Fixed a bug where split tasks were not being reported correctly.</li>
</ul>
<h2 id="091-2006-01-26">0.9.1 (2006-01-26)</h2>
<ul>
<li>Major API rewrite.</li>
<li>Added a flag called "expanded" to the Task class to represent whether a task in an MPP9 file is shown as expanded or collapsed by MS Project.</li>
<li>Fixed a bug in the relation code in MpxjQuery (contributed by Shlomo Swidler).</li>
<li>Modified MPXDateFormat, MPXTimeFormat and MPXCurrencyFormat to derive them from DateFormat and NumberFormat.</li>
<li>Added support for MPT files.</li>
<li>Fixed a bug which could case an NPE when reading certain MPP9 files.</li>
<li>Added support for the "marked" attribute for MPP9 files.</li>
<li>Added support for reading split task data from MPP9 files.</li>
<li>Added support for reading calculate multiple critical paths flag.</li>
<li>Fixed a bug which could case an array out of bounds exception in the Priority (contributed by Frank Illenberger).</li>
<li>Fixed bug #1346735 "Priorities of the tasks are exported incorrectly".</li>
<li>Added code to allow tasks, resources, resource assignments and calendars to be removed from the data structure.</li>
<li>Implemented Italian MPX file format translation (contributed by Elio Zoggia).</li>
<li>Cleaned up calendar usage.</li>
<li>Added support for retrieval of custom document summary fields from the project header (contributed by Wade Golden).</li>
<li>Updated to use checkstyle 4.0 and fixed warnings.</li>
<li>Rationalised duration conversions into a set of methods in the MPXDuration class.</li>
<li>Replaced various file format conversion utilities with the general purpose MpxjConvert utility.</li>
<li>Fixed an issue where tasks with a percent complete value, but no resource assignments, would not write correctly to an MSPDI file.</li>
<li>Added an accessor method for resource calendars.</li>
<li>Unique ID generation was not correct for tasks, resources and calendars if these entities were added to an existing project file.</li>
<li>Fixed a compatibility issue with POI3</li>
<li>Added an event listener to the project file to allow notifications of resources and tasks being read and written to and from a file.</li>
<li>Fixed a compiler warning when build with JDK5.</li>
<li>Fixed a bug where a project start date was not being set correctly in the project header.</li>
<li>Added support for reading the project header "calendar name", "schedule from" and "revision" values from MPP files.</li>
<li>Fixed split task support.</li>
<li>Enhanced TableFontStyle implementation.</li>
</ul>
<h2 id="0025-2005-08-11">0.0.25 (2005-08-11)</h2>
<ul>
<li>Added support for reading all properties from an MPP9 file which define the visual appearance of the Gantt Chart view shown in Microsoft Project (development funding courtesy of Steelray).</li>
<li>Tidied up constructors. Added no-argument constructors to the MPPFile and MSPDIFile classes.</li>
<li>Fixed incorrect value in WorkGroup enumerated type.</li>
<li>Implemented the resource assignment work contour property (contributed by Wade Golden).</li>
<li>Implemented correct handling for MPX files using different character set encodings (suggested by Frank Illenberger).</li>
<li>Fixed task duration calculation when importing an MPP file with a "non-standard" hours-per-day setting (contributed by Wade Golden).</li>
<li>Updated to ensure that the MPX task fixed attribute, and the MPP/MSPDI task type attribute are correctly handled.</li>
<li>Updated to implement the remaining project header attributes supported by the MSPDI file format.</li>
<li>Updated to add support for reading the MPX 3.0 files generated by Primavera (courtesy of CapitalSoft).</li>
<li>Fixed incorrect assumptions about conversion of durations to hours when writing MPX files (contributed by Frank Illenberger).</li>
<li>Updated to calculate remaining work for resource assignments on import, to allow MSPDI export of this data to work correctly (contributed by Frank Illenberger).</li>
<li>Updated to add another condition to the test for deleted tasks in MPP8 files.</li>
<li>Updated to fix a problem with reading assignment data from MPP9 files.</li>
<li>Rationalised the location of the JUnit tests and the sample files.</li>
<li>Fixed a problem where the project start and end dates reported in the project header were incorrect.</li>
<li>Fixed an array out of bounds exception when reading an MPP9 file.</li>
<li>Updated to allow MPXCalendarHours to accept an arbitrary number of time periods.</li>
<li>Introduced the Day class to replace the use of arbitrary integers to represent days of the week.</li>
<li>Added the ability to query the task assignments for a resource using the Resource.getTaskAssignments() method.</li>
<li>Fixed a problem with number formats in MSPDI files.</li>
<li>Updated the MPP View class to extract the view type.</li>
<li>Updated to ensure that duration values read from an MSPDI file are converted into the appropriate duration units, rather than being left as hours as the durations are represented in the MSPDI file.</li>
<li>Implemented French MPX file format translation (contributed by Benoit Baranne).</li>
<li>Fixed a bug reading assignment work contour attribute.</li>
<li>Updated to make failure more graceful when a Microsoft Project 4.0 MPP file is encountered.</li>
<li>Fixed a bug where deleted constraints in an MPP9 file were not being ignored.</li>
<li>Updated to make replace the int relation type in the Relation class with instances of the RelationType class.</li>
<li>Updated to derive RelationList from AbstractList.</li>
<li>Added sample code to MpxjQuery to illustrate retrieval of information from Relation instances.</li>
<li>Updated MpqjQuery to parse MSPDI files as well as MPP and MPX files.</li>
<li>Added support for early start, early finish, late start, late finish to MPP files.</li>
<li>Updated MPP9 file support to handle start as late as possible constraints. </li>
<li>Added support for subproject file information in MPP9 files.</li>
<li>Fixed a bug where occasionally a task in MPP9 files were not being read.</li>
<li>Fixed a NegativeArrayIndexException thrown when reading certain MPP8 files.</li>
<li>Reduced the memory used by MPXJ by anything up to 60%, particularly when reading large MPP files.</li>
<li>Fixed a bug when reading MPX files where the field delimiter was not comma, and task relation lists contained more than one entry.</li>
<li>Updated to fix unreliable retrieval of project start and end dates from certain MPP files.</li>
<li>Fixed schedule from value in MSPDI files (contributed by Frank Illenberger).</li>
<li>Fixed a bug when reading durations in elapsed days from an MPP file.</li>
<li>Tasks can now have arbitrary priority values. These values are mapped to/from the fixed MPP8/MPX priority values where necessary.</li>
</ul>
<h2 id="0024-2005-01-10">0.0.24 (2005-01-10)</h2>
<ul>
<li>Fixed a bug (again!) where deleted resource assignments in MPP9 files were still seen by MPXJ.</li>
<li>Updated to use class instances instead of primitives to represent some enumerated types.</li>
<li>Updated to implement support for reading and writing all the basic Resource attributes found in MSPDI files.</li>
<li>Updated to implement support for reading and writing all the basic Task attributes found in MSPDI files.</li>
<li>Updated to implement support for reading and writing all the basic Project Header attributes from MPP8 and MPP9 files.</li>
<li>Made MSPDI file parsing more robust to allow it by default to cope with non-schema-compliant XML in the same manner as MS Project. Implemented a new compatibility flag to allow this behaviour to be disabled in favour of strict parsing.</li>
<li>Merged DateTimeSettings, CurrencySettings, and DefaultSettings into the ProjectHeader class. This change makes the project header data easier to use as it is in a single place. It also makes the entities used to describe a project consistent with the contents of the MPP and MSPDI file formats.</li>
</ul>
<h2 id="0023-2004-11-17">0.0.23 (2004-11-17)</h2>
<ul>
<li>Fixed a bug where MPXJ was still using the default locale of the user's machine to create localised MPX files when a normal international MPX file was expected.</li>
<li>Fixed a bug where the incorrect record delimiter was being used in by the MPX RelationList class.</li>
<li>Fixed a bug where the method Task.getText21 was not retrieving the correct text value.</li>
<li>Fixed a bug where the task unique ID values were being truncated unnecessarily.</li>
<li>Fixed a bug where calendar exceptions were not testing the range of dates between the start and end date correctly.</li>
<li>Fixed a bug where the priority of a task was being escalated when converting between an MPP9 file and an MSPDI file.</li>
<li>Fixed a bug where a deadline was incorrectly being added to a task when importing data from an MPP9 file.</li>
<li>Fixed a bug where deleted resource assignments in MPP9 files were still seen by MPXJ.</li>
<li>Fixed a bug where MPXFile attributes were not being correctly copied by the copy constructor.</li>
<li>Fixed a rounding error in MPXCalendar.getDaysInRange (contributed by Wade Golden)</li>
<li>Updated to make MPXJ more robust in the face of unexpected offsets in MPP8 file format.</li>
<li>Updated support for password-protected files to allow write-reserved files to be read.</li>
<li>Updated to use the latest version of JAXB, as shipped in Sun's Java Web Services Developer Pack (JWSDP) version  1.4.</li>
<li>Updated the distribution to include the redistributable files from the JWSDP JAXB implementation. Users will no longer need to download JWSDP separately in order to make use of MPXJ's MSPDI functionality.</li>
<li>Updated to prevent empty notes records being added to tasks and resources when reading an MSPDI file.</li>
<li>Updated to improve accuracy when converting an MPP file to an MSPDI file.</li>
<li>Added support for blank task rows in MPP8 files.</li>
<li>Added support for blank resource rows in MPP8 files.</li>
<li>Added support for Portuguese MPX files.</li>
<li>Added support reading and writing extended attributes (apart from outline codes) for MSPDI files.</li>
<li>Added support for the Resource Type attribute.</li>
</ul>
<h2 id="0022-2004-07-27">0.0.22 (2004-07-27)</h2>
<ul>
<li>Fixed a bug where task data was not being read correctly from very large MPP9 files.</li>
<li>Fixed a bug where certain MPP8 files were not read correctly when no constraint data is present.</li>
<li>Fixed a bug where certain MPP9 files were not read correctly.</li>
<li>Fixed a bug where MPP9 files containing invalid resource data were not read correctly.</li>
<li>Fixed a bug where MPXJ was using the default locale of the user's machine to create localised MPX files when a normal international MPX file was expected.</li>
<li>Fixed a bug where MPXJ not correctly handling embedded line breaks when reading and writing MPX files.</li>
<li>Removed arbitrary restrictions on the number of various entities, originally taken from the MPX specification.</li>
<li>Updated MPX documentation for Task.getFixed and Task.setFixed.</li>
<li>Updated MPP9 file code to improve handling invalid offset values.</li>
<li>Updated to remove leading and trailing spaces from MPX task field names before processing.</li>
<li>Updated to detect password protected files and raise a suitable exception.</li>
<li>Implemented an enhancement to improve file loading speed by an order of magnitude for files with a large number of tasks or resources (based on a contribution by Brian Leach).</li>
<li>Implemented support for Maven.</li>
<li>Updated MpxCreate utility to allow it to create both MPX and MSPDI files.</li>
<li>Added new JUnit test for confidential customer data.</li>
<li>Added support for the resource assignment remaining work attribute for MPP8, MPP9 and MSPDI files.</li>
</ul>
<h2 id="0021-2004-05-06">0.0.21 (2004-05-06)</h2>
<ul>
<li>Fixed a bug where the task start date attribute was not always correct for MPP8 files.</li>
<li>Fixed a bug causing valid tasks to be incorrectly identified as being deleted in MPP8 files.</li>
<li>Fixed a bug causing an exception when reading certain MPP9 files.</li>
<li>Updated to allow localised MPX files to be written and read.</li>
<li>Implemented support for German MPX files.</li>
<li>Implemented generic mechanism for dealing with task field aliases.</li>
<li>Implemented task field alias read/write for MSPDI files.</li>
<li>Implemented task field alias read for MPP9 files.</li>
<li>Implemented resource field alias read/write for MSPDI files.</li>
<li>Implemented resource field alias read for MPP9 files.</li>
</ul>
<h2 id="0020-2004-03-15">0.0.20 (2004-03-15)</h2>
<ul>
<li>Fixed a bug where alternative decimal delimiters and thousands separators were not being handled correctly when reading and writing MPX files.</li>
<li>Fixed a bug causing a null pointer exception when writing an MSPDI file.</li>
<li>Fixed a bug in MSPDI files where default values were being written incorrectly for some task attributes.</li>
<li>Fixed a bug with MSPDI file date handling in non GMT time zones.</li>
<li>Fixed a bug in processing calendar data where data block is not a multiple of 12 bytes</li>
<li>Fixed a bug processing tables where column data is null</li>
<li>Fixed checkstyle code warnings.</li>
<li>Fixed Eclipse code warnings.</li>
<li>Updated to include version 2.5 of the POI library.</li>
<li>Added support for task calendars.</li>
</ul>
<h2 id="0019-2003-12-02">0.0.19 (2003-12-02)</h2>
<ul>
<li>Fixed a bug reading table data from certain MPP8 files</li>
<li>Updated MSPDI support to use the latest version of JAXB (from JWSDP-1.3)</li>
<li>Re-implemented base and resource calendars as a single MPXCalendar class</li>
<li>Updated support for base calendars and resource calendars for all file formats</li>
<li>Improved MPXException to print details of any nested exception when a stack trace is printed.</li>
<li>Removed unnecessary use of ByteArray.java</li>
<li>Added support for the following task fields: ActualOvertimeCost, ActualOvertimeWork, FixedCostAccrual, Hyperlink, HyperlinkAddress, HyperlinkSubAddress, LevelAssignments, LevelingCanSplit, LevelingDelay, PreleveledStart, PreleveledFinish, RemainingOvertimeCost, RemainingOvertimeWork.</li>
</ul>
<h2 id="0018-2003-11-13">0.0.18 (2003-11-13)</h2>
<ul>
<li>Fixed a bug with writing MS Project compatible MSPDI XML files in non-GMT timezones.</li>
<li>Fixed a bug with writing MSPDI XML files in non-GMT timezones.</li>
<li>Fixed a bug causing an exception when zero length calendar names were present</li>
<li>Fixed a bug causing MPP8 flags to be read incorrectly. Note that flag 20 is still not read correctly.</li>
<li>Fixed a bug with the "Microsoft Project Compatible Output" flag for MSPDI files.</li>
<li>Fixed a bug reading task text 10.</li>
<li>Added new MPXFile.setIgnoreTextModel() method to allow MPXJ to ignore faulty text version of task or resource model records in MPX files.</li>
<li>Improved invalid MPX data error handling and reporting.</li>
<li>Added BaseCalendar.getDate method to allow end dates to be calculated based on a start date and a duration of working time.</li>
<li>Made MPXDateFormat implement java.io.Serializable to allow MPXDate to serialize correctly.</li>
<li>Updated the ant build file to allow MPXJ to be built without the components that depend on JAXB</li>
<li>Rationalised setDefaultStartTime and setDefaultEndTime methods</li>
<li>Added MppXml utility</li>
<li>Added support for querying view information held in MPP files.</li>
<li>Added support for querying table information held in MPP files. (NB This allows the user to retrieve column information, including user defined column names)</li>
<li>Added support for outlinecode1-10 fields in MPP9 files.</li>
<li>Added support for resource "available from" and "available to" fields.</li>
<li>Verified that MPXJ will read MPP files written by Microsoft Project 2003 (they are still MPP9 files).</li>
</ul>
<h2 id="0017-2003-08-05">0.0.17 (2003-08-05)</h2>
<ul>
<li>Fixed a bug where a decimal point was being appended to the currency format even if no decimal digits were required.</li>
<li>Fixed a bug where special characters appearing in the currency symbol were not being quoted.</li>
<li>Fixed a bug that caused resource assignments to be incorrectly read from some MPP8 files.</li>
<li>Added a new write method to MPXFile allowing the user control over the character encoding used when writing an MPX file.</li>
</ul>
<h2 id="0016-2003-07-04">0.0.16 (2003-07-04)</h2>
<ul>
<li>Fixed bug causing some extended boolean attributes to be read incorrectly.</li>
<li>Fixed bug causing MPP8 file task data to be read incorrectly under certain circumstances.</li>
<li>Updated calendar duration code to account for calendar exceptions.</li>
</ul>
<h2 id="0015-2003-06-17">0.0.15 (2003-06-17)</h2>
<ul>
<li>Fixed a bug causing resource assignments to be duplicated in an MPX file created programmatically.</li>
<li>Fixed a bug causing an incorrect duration value to be read from an MPP9 file.</li>
<li>Fixed a bug causing invalid MPX files to be written in locales which don't use a period as the decimal separator.</li>
<li>Fixed a bug causing embedded quote and comma characters in task and resource notes to be handled incorrectly.</li>
<li>Added simple JUnit test to demonstrate iteration through relationships.</li>
<li>Added an example of programmatically creating a partially complete task to the MPXCreate.java example.</li>
<li>Added default values to the MPX project header.</li>
<li>Added support for reading the RemainingDuration field from an MPP9 file.</li>
<li>Updated predecessor and successor method documentation.</li>
<li>Updated Task.get/set ResourceInitials and Task.get/set ResourceNames method documentation.</li>
<li>Updated to extract the following fields from resource assignment data in MPP files which were previously not imported: ActualCost, ActualWork, Cost, Finish, Start, Units, Work.</li>
</ul>
<h2 id="0014-2003-05-28">0.0.14 (2003-05-28)</h2>
<ul>
<li>Updated to extract the following fields from resource data in an MPP9 file which were previously not imported: Flag1-Flag20.</li>
<li>Added the method MPPFile.getFileType to allow the type of MPP file (MPP8: 98, MPP9: 2000,2002) to be determined.</li>
<li>Updated API to make classes final and constructors package access only where appropriate.</li>
<li>Updated to use of 6 byte long int fields for cost and work values for MPP8.</li>
<li>Fixed error in reading task fixed cost for MPP8.</li>
<li>Updated to extract the following fields from task data in an MPP8 file which were previously not imported: Contact, Cost1-Cost10, Date1-Date10, Duration1-Duration10, EffortDriven, Finish1-Finish10, Flag1-Flag20, HideBar, Milestone, Number1-Number20, Rollup, Start1-Start10, Text1-Text30, Type, WBS.</li>
<li>Updated to extract the following fields from resource data in an MPP8 file which were previously not imported: Code, Cost1-Cost10, Date1-Date10, Duration1-Duration10, EmailAddress, Finish1-Finish10, Number1-Number20, Start1-Start10, Text1-Text30</li>
<li>Added support for task and resource note fields in MPP8 files.</li>
<li>Added support for the OvertimeCost task attribute for all file formats.</li>
<li>Updated to extract calendar data from MPP8 files.</li>
<li>Updated resource notes to fix end of line handling problem.</li>
<li>Added functionality to read default settings and currency settings data from MPP files.</li>
</ul>
<h2 id="0013-2003-05-22">0.0.13 (2003-05-22)</h2>
<ul>
<li>Implemented support for the Microsoft Project 98 file format.</li>
<li>Fixed a bug that prevented task and resource note text from being read.</li>
<li>Updated to remove a Java 1.4 dependency introduced in 0.0.12. Will now work with Java 1.3.</li>
<li>Updated to correct handling of carriage returns embedded in note fields.</li>
</ul>
<h2 id="0012-2003-05-08">0.0.12 (2003-05-08)</h2>
<ul>
<li>Fixed incorrect handling of timezones and daylight saving time.</li>
<li>Fixed incorrect task structure generated from outline levels.</li>
<li>Updated to extract the notes fields from tasks and resources read from an MPP file.</li>
<li>Added the option to remove or preserve the RTF formatting from the note fields from an MPP file.</li>
<li>Updated to extract the following fields from task data in an MPP file which were previously not imported: Text11-Text30, Number6-Number20, Duration4-Duration10, Date1-Date10, Cost4-Cost10, Start6-Start10, Finish6-Finish10</li>
<li>Updated to extract the following fields from resource data in an MPP file which were previously not imported: Text6-Text30, Start1-Start10, Finish1-Finish10, Number1-Number20, Duration1-Duration10, Date1-Date10, Cost1-Cost10</li>
</ul>
<h2 id="0011-2003-04-15">0.0.11 (2003-04-15)</h2>
<ul>
<li>Fixed error in format string used in one of the example files.</li>
<li>Fixed error where double byte characters were being read incorrectly.</li>
<li>Fixed error where deleted constraints were being resurrected when read from an MPP file.</li>
<li>Updated to extract the following fields from task data in an MPP file which were previously not imported: Flag11-Flag20, Rollup, HideBar, EffortDriven.</li>
</ul>
<h2 id="0010-2003-04-08">0.0.10 (2003-04-08)</h2>
<ul>
<li>Corrected Actual Start and Actual End fields from MPP file.</li>
<li>Fixed bug where time values were being broken by daylight saving time in the user's default locale.</li>
<li>Updated to extract the following fields from task data in an MPP file which were previously not imported: Actual Work, Baseline Work, Cost Variance, Deadline, Remaining Work, Work.</li>
<li>Updated to extract the following fields from resource data in an MPP file which were previously not imported: Actual Cost, Actual Overtime Cost, Actual Work, Baseline Work, Cost, Cost Variance, Max Units, Overtime Cost, Overtime Rate, Overtime Work, Peak, Regular work, Remaining Cost, Remaining Overtime Cost, Remaining Work, Standard Rate, Work, Work Variance</li>
</ul>
<h2 id="009-2003-04-03">0.0.9 (2003-04-03)</h2>
<ul>
<li>Fixed bug when handling certain types of modified MPP file where resources have been updated.</li>
<li>Added sample MPP files for bugs to the JUnit tests.</li>
<li>Added support for summary flag import from MPP files.</li>
<li>Added automatic summary flag update when creating an MPX file programmatically.</li>
<li>Added new constructor to the MSPDIFile class to allow MSPDI files to be created from scratch.</li>
</ul>
<h2 id="008-2003-03-27">0.0.8 (2003-03-27)</h2>
<ul>
<li>Added support for estimated durations.</li>
<li>Fixed bug in handling certain types of modified MPP file where tasks have been updated.</li>
<li>Added the facility to auto generate outline numbers.</li>
</ul>









  




                
              </article>
            </div>
          
          
  <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var tab,labels=set.querySelector(".tabbed-labels");for(tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script>

<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["content.tabs.link", "content.code.copy"], "search": "../assets/javascripts/workers/search.b8dbb3d2.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../assets/javascripts/bundle.dd8806f2.min.js"></script>
      
    
  </body>
</html>