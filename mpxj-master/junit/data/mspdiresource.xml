<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>12</SaveVersion>
	<Name>mspdiresource.xml</Name>
	<Company>Steelray Software</Company>
	<Author><PERSON></Author>
	<CreationDate>2006-08-25T15:41:00</CreationDate>
	<LastSaved>2010-03-16T21:58:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2006-08-25T08:00:00</StartDate>
	<FinishDate>2006-09-08T09:08:24</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>$</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>1</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>0</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2010-03-16T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>1</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>1</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<OutlineCodes>
		<OutlineCode>
			<Guid>D8C7E507-A694-44B6-A2D2-64B892118ADC</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>1</ValueID>
					<FieldGUID>47FA4ABF-2BDE-4397-8F4A-D44F2E2B1ACD</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>AAA</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>496BFAEE-0E71-4F7D-AA44-F98244BD7E95</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>2</ValueID>
					<FieldGUID>0FCE5C8F-F47F-4EAA-BABD-4E64E44741E8</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>BBB</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>A8059104-**************-98B827D76031</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>3</ValueID>
					<FieldGUID>930F6976-E9D2-49DC-AD51-3469869A0B99</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>CCC</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>1F67F55E-99EB-46C3-80F6-2CF56F1D3BFC</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>4</ValueID>
					<FieldGUID>70A6AB9B-E141-46E1-8A81-587A79C1E96D</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>DDD</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>9B00AF6E-**************-CA92129ED950</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>5</ValueID>
					<FieldGUID>C1FB5407-A74A-4CEC-A7EF-1D382DC368F7</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>EEE</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>968434A4-D4A7-4CD1-88A9-D5BEE9D5F1CC</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>6</ValueID>
					<FieldGUID>98A29E46-E532-4977-88B2-F9663453A7B4</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>FFF</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>EF92FA6C-5575-4123-B360-CD4A8D841FC6</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>7</ValueID>
					<FieldGUID>9C1F5530-4138-4279-A4CC-1BE2F1D607DF</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>GGG</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>21C3F1B8-9FEC-4DDF-93F0-10848B424486</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>8</ValueID>
					<FieldGUID>D7A9472C-4FEE-475A-B3A4-AACFC1D0EAA5</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>HHH</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>F24EBAA4-A522-457A-9D57-27EE5D78301D</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>9</ValueID>
					<FieldGUID>9EC92A86-72E0-4250-9BBE-3DE146155B83</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>III</Value>
				</Value>
			</Values>
		</OutlineCode>
		<OutlineCode>
			<Guid>035BF04C-3B23-4E91-AF30-C161A9444A77</Guid>
			<Enterprise>0</Enterprise>
			<ResourceSubstitutionEnabled>0</ResourceSubstitutionEnabled>
			<LeafOnly>0</LeafOnly>
			<AllLevelsRequired>0</AllLevelsRequired>
			<OnlyTableValuesAllowed>0</OnlyTableValuesAllowed>
			<Masks>
				<Mask>
					<Level>1</Level>
					<Type>1</Type>
					<Length>0</Length>
					<Separator>.</Separator>
				</Mask>
			</Masks>
			<Values>
				<Value>
					<ValueID>10</ValueID>
					<FieldGUID>F3595C4F-3588-4A4A-B023-0C14CFA10918</FieldGUID>
					<ParentValueID>0</ParentValueID>
					<Type>21</Type>
					<Value>JJJ</Value>
				</Value>
			</Values>
		</OutlineCode>
	</OutlineCodes>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>205521174</FieldID>
			<FieldName>Outline Code1</FieldName>
			<Ltuid>D8C7E507-A694-44B6-A2D2-64B892118ADC</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521176</FieldID>
			<FieldName>Outline Code2</FieldName>
			<Ltuid>496BFAEE-0E71-4F7D-AA44-F98244BD7E95</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521178</FieldID>
			<FieldName>Outline Code3</FieldName>
			<Ltuid>A8059104-**************-98B827D76031</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521180</FieldID>
			<FieldName>Outline Code4</FieldName>
			<Ltuid>1F67F55E-99EB-46C3-80F6-2CF56F1D3BFC</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521182</FieldID>
			<FieldName>Outline Code5</FieldName>
			<Ltuid>9B00AF6E-**************-CA92129ED950</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521184</FieldID>
			<FieldName>Outline Code6</FieldName>
			<Ltuid>968434A4-D4A7-4CD1-88A9-D5BEE9D5F1CC</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521186</FieldID>
			<FieldName>Outline Code7</FieldName>
			<Ltuid>EF92FA6C-5575-4123-B360-CD4A8D841FC6</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521188</FieldID>
			<FieldName>Outline Code8</FieldName>
			<Ltuid>21C3F1B8-9FEC-4DDF-93F0-10848B424486</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521190</FieldID>
			<FieldName>Outline Code9</FieldName>
			<Ltuid>F24EBAA4-A522-457A-9D57-27EE5D78301D</Ltuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521192</FieldID>
			<FieldName>Outline Code10</FieldName>
			<Ltuid>035BF04C-3B23-4E91-AF30-C161A9444A77</Ltuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<Name>Wade Golden</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<Name>Jon Iles</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<Name>Brian Leach</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>6</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<Name>Night Shift</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>23:00:00</FromTime>
							<ToTime>00:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>00:00:00</FromTime>
							<ToTime>03:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>04:00:00</FromTime>
							<ToTime>08:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>23:00:00</FromTime>
							<ToTime>00:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>00:00:00</FromTime>
							<ToTime>03:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>04:00:00</FromTime>
							<ToTime>08:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>23:00:00</FromTime>
							<ToTime>00:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>00:00:00</FromTime>
							<ToTime>03:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>04:00:00</FromTime>
							<ToTime>08:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>23:00:00</FromTime>
							<ToTime>00:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>00:00:00</FromTime>
							<ToTime>03:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>04:00:00</FromTime>
							<ToTime>08:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>23:00:00</FromTime>
							<ToTime>00:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>00:00:00</FromTime>
							<ToTime>03:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>04:00:00</FromTime>
							<ToTime>08:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>7</UID>
			<Name>Concrete</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2006-08-25T15:41:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2006-08-25T08:00:00</Start>
			<Finish>2006-09-08T09:08:24</Finish>
			<Duration>PT81H8M24S</Duration>
			<DurationFormat>53</DurationFormat>
			<Work>PT128H0M0S</Work>
			<Stop>2006-08-25T08:00:00</Stop>
			<Resume>2006-08-25T08:00:00</Resume>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2006-08-25T08:00:00</EarlyStart>
			<EarlyFinish>2006-09-08T09:08:24</EarlyFinish>
			<LateStart>2006-08-25T08:00:00</LateStart>
			<LateFinish>2006-09-08T09:08:24</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>7680000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>13</PercentComplete>
			<PercentWorkComplete>13</PercentWorkComplete>
			<Cost>820000</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualStart>2006-08-25T08:00:00</ActualStart>
			<ActualDuration>PT10H43M0S</ActualDuration>
			<ActualCost>80000</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT16H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT128H0M0S</RegularWork>
			<RemainingDuration>PT70H25M18S</RemainingDuration>
			<RemainingCost>740000</RemainingCost>
			<RemainingWork>PT112H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>80000.00</ACWP>
			<CV>-80000.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>2</UID>
			<ID>1</ID>
			<Name>Task A</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2006-08-25T13:28:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2006-08-25T08:00:00</Start>
			<Finish>2006-08-30T08:00:00</Finish>
			<Duration>PT48H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT72H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2006-08-25T08:00:00</EarlyStart>
			<EarlyFinish>2006-08-30T08:00:00</EarlyFinish>
			<LateStart>2006-08-25T08:00:00</LateStart>
			<LateFinish>2006-08-30T08:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4320000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>540000</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT72H0M0S</RegularWork>
			<RemainingDuration>PT48H0M0S</RemainingDuration>
			<RemainingCost>540000</RemainingCost>
			<RemainingWork>PT72H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>3</UID>
			<ID>2</ID>
			<Name>Contoured Task</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2006-08-25T14:14:00</CreateDate>
			<WBS>2</WBS>
			<OutlineNumber>2</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2006-08-30T08:00:00</Start>
			<Finish>2006-09-08T09:08:24</Finish>
			<Duration>PT57H8M24S</Duration>
			<DurationFormat>9</DurationFormat>
			<Work>PT40H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2006-08-30T08:00:00</EarlyStart>
			<EarlyFinish>2006-09-08T09:08:24</EarlyFinish>
			<LateStart>2006-08-30T08:00:00</LateStart>
			<LateFinish>2006-09-08T09:08:24</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>2400000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>200000</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT40H0M0S</RegularWork>
			<RemainingDuration>PT57H8M24S</RemainingDuration>
			<RemainingCost>200000</RemainingCost>
			<RemainingWork>PT40H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<PredecessorLink>
				<PredecessorUID>2</PredecessorUID>
				<Type>1</Type>
				<CrossProject>0</CrossProject>
				<LinkLag>0</LinkLag>
				<LagFormat>7</LagFormat>
			</PredecessorLink>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>4</UID>
			<ID>3</ID>
			<Name>Completed Task</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2010-03-16T16:00:00</CreateDate>
			<WBS>3</WBS>
			<OutlineNumber>3</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2006-08-26T08:00:00</Start>
			<Finish>2006-08-29T17:00:00</Finish>
			<Duration>PT16H0M0S</Duration>
			<DurationFormat>39</DurationFormat>
			<Work>PT16H0M0S</Work>
			<Stop>2006-08-29T17:00:00</Stop>
			<Resume>2006-08-29T17:00:00</Resume>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>0</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2006-08-26T08:00:00</EarlyStart>
			<EarlyFinish>2006-08-29T17:00:00</EarlyFinish>
			<LateStart>2006-08-26T08:00:00</LateStart>
			<LateFinish>2006-08-29T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>960000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>100</PercentComplete>
			<PercentWorkComplete>100</PercentWorkComplete>
			<Cost>80000</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualStart>2006-08-26T08:00:00</ActualStart>
			<ActualFinish>2006-08-29T17:00:00</ActualFinish>
			<ActualDuration>PT16H0M0S</ActualDuration>
			<ActualCost>80000</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT16H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT16H0M0S</RegularWork>
			<RemainingDuration>PT0H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>80000.00</ACWP>
			<CV>-80000.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<TimephasedData>
				<Type>11</Type>
				<UID>4</UID>
				<Start>2006-08-26T08:00:00</Start>
				<Finish>2006-08-27T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>32768</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>11</Type>
				<UID>4</UID>
				<Start>2006-08-27T08:00:00</Start>
				<Finish>2006-08-28T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>32768</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>11</Type>
				<UID>4</UID>
				<Start>2006-08-28T08:00:00</Start>
				<Finish>2006-08-29T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>50</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>11</Type>
				<UID>4</UID>
				<Start>2006-08-29T08:00:00</Start>
				<Finish>2006-08-29T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>50</Value>
			</TimephasedData>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T21:57:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Wade Golden</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>WG</Initials>
			<Code>10</Code>
			<Group>Steelray</Group>
			<WorkGroup>0</WorkGroup>
			<EmailAddress><EMAIL></EmailAddress>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>2.00</PeakUnits>
			<OverAllocated>1</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>2</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT16H0M0S</ActualWork>
			<RemainingWork>PT64H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>20</PercentWorkComplete>
			<StandardRate>50</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>400000</Cost>
			<OvertimeRate>100</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>80000</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>320000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>400000</CostVariance>
			<SV>0.00</SV>
			<CV>-80000.00</CV>
			<ACWP>80000.00</ACWP>
			<CalendarUID>3</CalendarUID>
			<Notes>Resource Notes 1</Notes>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T21:57:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520904</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520905</FieldID>
				<Value>2</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520926</FieldID>
				<Value>3</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520927</FieldID>
				<Value>4</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520928</FieldID>
				<Value>5</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520993</FieldID>
				<Value>6</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520994</FieldID>
				<Value>7</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520995</FieldID>
				<Value>8</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520996</FieldID>
				<Value>9</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520997</FieldID>
				<Value>10</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521121</FieldID>
				<Value>11</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521122</FieldID>
				<Value>12</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521123</FieldID>
				<Value>13</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521124</FieldID>
				<Value>14</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521125</FieldID>
				<Value>15</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521126</FieldID>
				<Value>16</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521127</FieldID>
				<Value>17</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521128</FieldID>
				<Value>18</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521129</FieldID>
				<Value>19</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521130</FieldID>
				<Value>20</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521131</FieldID>
				<Value>21</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521132</FieldID>
				<Value>22</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521133</FieldID>
				<Value>23</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521134</FieldID>
				<Value>24</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521135</FieldID>
				<Value>25</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521136</FieldID>
				<Value>26</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521137</FieldID>
				<Value>27</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521138</FieldID>
				<Value>28</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521139</FieldID>
				<Value>29</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521140</FieldID>
				<Value>30</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520998</FieldID>
				<Value>2006-02-01T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205520999</FieldID>
				<Value>2006-02-02T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521000</FieldID>
				<Value>2006-02-03T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521001</FieldID>
				<Value>2006-02-04T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521002</FieldID>
				<Value>2006-02-05T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521116</FieldID>
				<Value>2006-02-06T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521117</FieldID>
				<Value>2006-02-07T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521118</FieldID>
				<Value>2006-02-08T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521119</FieldID>
				<Value>2006-02-09T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521120</FieldID>
				<Value>2006-02-10T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521003</FieldID>
				<Value>2006-03-01T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521004</FieldID>
				<Value>2006-03-02T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521005</FieldID>
				<Value>2006-03-03T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521006</FieldID>
				<Value>2006-03-04T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521007</FieldID>
				<Value>2006-03-05T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521086</FieldID>
				<Value>2006-03-06T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521087</FieldID>
				<Value>2006-03-07T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521088</FieldID>
				<Value>2006-03-08T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521089</FieldID>
				<Value>2006-03-09T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521090</FieldID>
				<Value>2006-03-10T17:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521019</FieldID>
				<Value>100</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521020</FieldID>
				<Value>200</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521021</FieldID>
				<Value>300</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521062</FieldID>
				<Value>400</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521063</FieldID>
				<Value>500</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521064</FieldID>
				<Value>600</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521065</FieldID>
				<Value>700</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521066</FieldID>
				<Value>800</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521067</FieldID>
				<Value>900</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521068</FieldID>
				<Value>1000</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521069</FieldID>
				<Value>2006-01-01T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521070</FieldID>
				<Value>2006-01-02T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521071</FieldID>
				<Value>2006-01-03T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521072</FieldID>
				<Value>2006-01-04T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521073</FieldID>
				<Value>2006-01-05T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521074</FieldID>
				<Value>2006-01-06T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521075</FieldID>
				<Value>2006-01-07T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521076</FieldID>
				<Value>2006-01-08T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521077</FieldID>
				<Value>2006-01-09T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521078</FieldID>
				<Value>2006-01-10T08:00:00</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521008</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521009</FieldID>
				<Value>2</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521010</FieldID>
				<Value>3</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521011</FieldID>
				<Value>4</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521012</FieldID>
				<Value>5</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521101</FieldID>
				<Value>6</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521102</FieldID>
				<Value>7</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521103</FieldID>
				<Value>8</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521104</FieldID>
				<Value>9</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521105</FieldID>
				<Value>10</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521106</FieldID>
				<Value>11</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521107</FieldID>
				<Value>12</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521108</FieldID>
				<Value>13</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521109</FieldID>
				<Value>14</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521110</FieldID>
				<Value>15</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521111</FieldID>
				<Value>16</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521112</FieldID>
				<Value>17</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521113</FieldID>
				<Value>18</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521114</FieldID>
				<Value>19</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521115</FieldID>
				<Value>20</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521013</FieldID>
				<Value>PT8H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521014</FieldID>
				<Value>PT16H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521015</FieldID>
				<Value>PT24H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521079</FieldID>
				<Value>PT32H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521080</FieldID>
				<Value>PT40H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521081</FieldID>
				<Value>PT48H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521082</FieldID>
				<Value>PT56H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521083</FieldID>
				<Value>PT64H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521084</FieldID>
				<Value>PT72H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>205521085</FieldID>
				<Value>PT80H0M0S</Value>
				<DurationFormat>7</DurationFormat>
			</ExtendedAttribute>
			<OutlineCode>
				<FieldID>205521174</FieldID>
				<ValueID>1</ValueID>
				<ValueGUID>47FA4ABF-2BDE-4397-8F4A-D44F2E2B1ACD</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521176</FieldID>
				<ValueID>2</ValueID>
				<ValueGUID>0FCE5C8F-F47F-4EAA-BABD-4E64E44741E8</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521178</FieldID>
				<ValueID>3</ValueID>
				<ValueGUID>930F6976-E9D2-49DC-AD51-3469869A0B99</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521180</FieldID>
				<ValueID>4</ValueID>
				<ValueGUID>70A6AB9B-E141-46E1-8A81-587A79C1E96D</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521182</FieldID>
				<ValueID>5</ValueID>
				<ValueGUID>C1FB5407-A74A-4CEC-A7EF-1D382DC368F7</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521184</FieldID>
				<ValueID>6</ValueID>
				<ValueGUID>98A29E46-E532-4977-88B2-F9663453A7B4</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521186</FieldID>
				<ValueID>7</ValueID>
				<ValueGUID>9C1F5530-4138-4279-A4CC-1BE2F1D607DF</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521188</FieldID>
				<ValueID>8</ValueID>
				<ValueGUID>D7A9472C-4FEE-475A-B3A4-AACFC1D0EAA5</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521190</FieldID>
				<ValueID>9</ValueID>
				<ValueGUID>9EC92A86-72E0-4250-9BBE-3DE146155B83</ValueGUID>
			</OutlineCode>
			<OutlineCode>
				<FieldID>205521192</FieldID>
				<ValueID>10</ValueID>
				<ValueGUID>F3595C4F-3588-4A4A-B023-0C14CFA10918</ValueGUID>
			</OutlineCode>
		</Resource>
		<Resource>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Jon Iles</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>JI</Initials>
			<Code>20</Code>
			<Group>Tapsterrock</Group>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT24H0M0S</Work>
			<RegularWork>PT24H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT24H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>75</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>180000</Cost>
			<OvertimeRate>150</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>180000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>1440000.00</WorkVariance>
			<CostVariance>180000</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>4</CalendarUID>
			<Notes>Resource Notes 2</Notes>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T21:57:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Brian Leach</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>BL</Initials>
			<Code>30</Code>
			<Group>Steelray</Group>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT24H0M0S</Work>
			<RegularWork>PT24H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT24H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>100</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>240000</Cost>
			<OvertimeRate>200</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>240000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>1440000.00</WorkVariance>
			<CostVariance>240000</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<Notes>Resource Notes 3</Notes>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T21:57:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Concrete</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<Initials>Con</Initials>
			<MaterialLabel>ton</MaterialLabel>
			<Group>Mat</Group>
			<WorkGroup>1</WorkGroup>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>0</CanLevel>
			<AccrueAt>1</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>8</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<CostPerUse>50000</CostPerUse>
			<ActualCost>0</ActualCost>
			<RemainingCost>0</RemainingCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>7</CalendarUID>
			<Notes>Resource Notes 4</Notes>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T21:57:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>5</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>120000</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>119900</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2006-08-29T17:00:00</Finish>
			<FinishVariance>820800</FinishVariance>
			<WorkVariance>1320000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT24H0M0S</RegularWork>
			<RemainingCost>120000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT24H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2006-08-25T08:00:00</Start>
			<Stop>2006-08-25T08:00:00</Stop>
			<Resume>2006-08-25T08:00:00</Resume>
			<StartVariance>811200</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT24H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2006-09-29T17:06:00</CreationDate>
			<Baseline>
				<Number>0</Number>
				<Start>2006-01-01T08:00:00</Start>
				<Finish>2006-01-02T17:00:00</Finish>
				<Work>PT2H0M0S</Work>
				<Cost>100</Cost>
			</Baseline>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2006-08-25T08:00:00</Start>
				<Finish>2006-08-26T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2006-08-26T08:00:00</Start>
				<Finish>2006-08-27T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2006-08-27T08:00:00</Start>
				<Finish>2006-08-28T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2006-08-28T08:00:00</Start>
				<Finish>2006-08-29T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2006-08-29T08:00:00</Start>
				<Finish>2006-08-29T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>6</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>2</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>180000</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>180000</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2006-08-29T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>1440000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT24H0M0S</RegularWork>
			<RemainingCost>180000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT24H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2006-08-25T08:00:00</Start>
			<Stop>2006-08-25T08:00:00</Stop>
			<Resume>2006-08-25T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT24H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2006-09-29T17:06:00</CreationDate>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2006-08-25T08:00:00</Start>
				<Finish>2006-08-26T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2006-08-26T08:00:00</Start>
				<Finish>2006-08-27T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2006-08-27T08:00:00</Start>
				<Finish>2006-08-28T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2006-08-28T08:00:00</Start>
				<Finish>2006-08-29T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2006-08-29T08:00:00</Start>
				<Finish>2006-08-29T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>7</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>3</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>240000</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>240000</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2006-08-30T08:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>1440000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT24H0M0S</RegularWork>
			<RemainingCost>240000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT24H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2006-08-25T23:00:00</Start>
			<Stop>2006-08-25T23:00:00</Stop>
			<Resume>2006-08-25T23:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT24H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2006-09-29T17:06:00</CreationDate>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-25T23:00:00</Start>
				<Finish>2006-08-26T23:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-26T23:00:00</Start>
				<Finish>2006-08-27T23:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-27T23:00:00</Start>
				<Finish>2006-08-28T23:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-28T23:00:00</Start>
				<Finish>2006-08-29T23:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-29T23:00:00</Start>
				<Finish>2006-08-30T00:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T00:00:00</Start>
				<Finish>2006-08-30T01:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T01:00:00</Start>
				<Finish>2006-08-30T02:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T02:00:00</Start>
				<Finish>2006-08-30T03:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T03:00:00</Start>
				<Finish>2006-08-30T04:00:00</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T04:00:00</Start>
				<Finish>2006-08-30T05:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T05:00:00</Start>
				<Finish>2006-08-30T06:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T06:00:00</Start>
				<Finish>2006-08-30T07:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2006-08-30T07:00:00</Start>
				<Finish>2006-08-30T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>8</UID>
			<TaskUID>3</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>200000</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>200000</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2006-09-08T09:08:24</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>2400000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT40H0M0S</RegularWork>
			<RemainingCost>200000</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT40H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2006-08-30T08:00:00</Start>
			<Stop>2006-08-30T08:00:00</Stop>
			<Resume>2006-08-30T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT40H0M0S</Work>
			<WorkContour>7</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2006-09-29T17:06:00</CreationDate>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T08:00:00</Start>
				<Finish>2006-08-30T09:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T09:00:00</Start>
				<Finish>2006-08-30T10:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T10:00:00</Start>
				<Finish>2006-08-30T11:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T11:00:00</Start>
				<Finish>2006-08-30T12:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T12:00:00</Start>
				<Finish>2006-08-30T13:00:00</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T13:00:00</Start>
				<Finish>2006-08-30T14:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T14:00:00</Start>
				<Finish>2006-08-30T14:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H10M42.11S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T14:42:48</Start>
				<Finish>2006-08-30T15:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T15:42:48</Start>
				<Finish>2006-08-30T16:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T16:42:48</Start>
				<Finish>2006-08-30T17:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H8M35.94S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T17:42:48</Start>
				<Finish>2006-08-30T18:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T18:42:48</Start>
				<Finish>2006-08-30T19:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T19:42:48</Start>
				<Finish>2006-08-30T20:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T20:42:48</Start>
				<Finish>2006-08-30T21:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T21:42:48</Start>
				<Finish>2006-08-30T22:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T22:42:48</Start>
				<Finish>2006-08-30T23:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-30T23:42:48</Start>
				<Finish>2006-08-31T00:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T00:42:48</Start>
				<Finish>2006-08-31T01:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T01:42:48</Start>
				<Finish>2006-08-31T02:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T02:42:48</Start>
				<Finish>2006-08-31T03:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T03:42:48</Start>
				<Finish>2006-08-31T04:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T04:42:48</Start>
				<Finish>2006-08-31T05:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T05:42:48</Start>
				<Finish>2006-08-31T06:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T06:42:48</Start>
				<Finish>2006-08-31T07:42:48</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T07:42:48</Start>
				<Finish>2006-08-31T08:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H21M23.84S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T08:42:48</Start>
				<Finish>2006-08-31T09:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T09:42:48</Start>
				<Finish>2006-08-31T10:42:48</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:42:48</Start>
				<Finish>2006-08-31T10:43:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:43:48</Start>
				<Finish>2006-08-31T10:44:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:44:48</Start>
				<Finish>2006-08-31T10:45:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:45:48</Start>
				<Finish>2006-08-31T10:46:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:46:48</Start>
				<Finish>2006-08-31T10:47:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:47:48</Start>
				<Finish>2006-08-31T10:48:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:48:48</Start>
				<Finish>2006-08-31T10:49:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:49:48</Start>
				<Finish>2006-08-31T10:50:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:50:48</Start>
				<Finish>2006-08-31T10:51:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:51:48</Start>
				<Finish>2006-08-31T10:52:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:52:48</Start>
				<Finish>2006-08-31T10:53:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:53:48</Start>
				<Finish>2006-08-31T10:54:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:54:48</Start>
				<Finish>2006-08-31T10:55:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:55:48</Start>
				<Finish>2006-08-31T10:56:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:56:48</Start>
				<Finish>2006-08-31T10:57:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:57:48</Start>
				<Finish>2006-08-31T10:58:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:58:48</Start>
				<Finish>2006-08-31T10:59:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T10:59:48</Start>
				<Finish>2006-08-31T11:00:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:00:48</Start>
				<Finish>2006-08-31T11:01:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:01:48</Start>
				<Finish>2006-08-31T11:02:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:02:48</Start>
				<Finish>2006-08-31T11:03:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:03:48</Start>
				<Finish>2006-08-31T11:04:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:04:48</Start>
				<Finish>2006-08-31T11:05:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:05:48</Start>
				<Finish>2006-08-31T11:06:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:06:48</Start>
				<Finish>2006-08-31T11:07:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:07:48</Start>
				<Finish>2006-08-31T11:08:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:08:48</Start>
				<Finish>2006-08-31T11:09:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:09:48</Start>
				<Finish>2006-08-31T11:10:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:10:48</Start>
				<Finish>2006-08-31T11:11:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:11:48</Start>
				<Finish>2006-08-31T11:12:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:12:48</Start>
				<Finish>2006-08-31T11:13:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:13:48</Start>
				<Finish>2006-08-31T11:14:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:14:48</Start>
				<Finish>2006-08-31T11:15:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:15:48</Start>
				<Finish>2006-08-31T11:16:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:16:48</Start>
				<Finish>2006-08-31T11:17:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:17:48</Start>
				<Finish>2006-08-31T11:18:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:18:48</Start>
				<Finish>2006-08-31T11:19:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:19:48</Start>
				<Finish>2006-08-31T11:20:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:20:48</Start>
				<Finish>2006-08-31T11:21:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:21:48</Start>
				<Finish>2006-08-31T11:22:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:22:48</Start>
				<Finish>2006-08-31T11:23:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:23:48</Start>
				<Finish>2006-08-31T11:24:48</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T11:25:42</Start>
				<Finish>2006-08-31T12:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H25M43.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T12:25:42</Start>
				<Finish>2006-08-31T13:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H19M16.69S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T13:25:42</Start>
				<Finish>2006-08-31T14:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T14:25:42</Start>
				<Finish>2006-08-31T15:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T15:25:42</Start>
				<Finish>2006-08-31T16:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T16:25:42</Start>
				<Finish>2006-08-31T17:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H25M43.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T17:25:42</Start>
				<Finish>2006-08-31T18:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T18:25:42</Start>
				<Finish>2006-08-31T19:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T19:25:42</Start>
				<Finish>2006-08-31T20:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T20:25:42</Start>
				<Finish>2006-08-31T21:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T21:25:42</Start>
				<Finish>2006-08-31T22:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T22:25:42</Start>
				<Finish>2006-08-31T23:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-08-31T23:25:42</Start>
				<Finish>2006-09-01T00:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T00:25:42</Start>
				<Finish>2006-09-01T01:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T01:25:42</Start>
				<Finish>2006-09-01T02:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T02:25:42</Start>
				<Finish>2006-09-01T03:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T03:25:42</Start>
				<Finish>2006-09-01T04:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T04:25:42</Start>
				<Finish>2006-09-01T05:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T05:25:42</Start>
				<Finish>2006-09-01T06:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T06:25:42</Start>
				<Finish>2006-09-01T07:25:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T07:25:42</Start>
				<Finish>2006-09-01T08:25:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H19M16.69S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:25:42</Start>
				<Finish>2006-09-01T08:26:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:26:42</Start>
				<Finish>2006-09-01T08:27:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:27:42</Start>
				<Finish>2006-09-01T08:28:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:28:42</Start>
				<Finish>2006-09-01T08:29:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:29:42</Start>
				<Finish>2006-09-01T08:30:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:30:42</Start>
				<Finish>2006-09-01T08:31:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:31:42</Start>
				<Finish>2006-09-01T08:32:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:32:42</Start>
				<Finish>2006-09-01T08:33:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:33:42</Start>
				<Finish>2006-09-01T08:34:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:34:42</Start>
				<Finish>2006-09-01T08:35:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:35:42</Start>
				<Finish>2006-09-01T08:36:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:36:42</Start>
				<Finish>2006-09-01T08:37:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:37:42</Start>
				<Finish>2006-09-01T08:38:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:38:42</Start>
				<Finish>2006-09-01T08:39:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:39:42</Start>
				<Finish>2006-09-01T08:40:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:40:42</Start>
				<Finish>2006-09-01T08:41:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:41:42</Start>
				<Finish>2006-09-01T08:42:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:42:42</Start>
				<Finish>2006-09-01T08:43:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:43:42</Start>
				<Finish>2006-09-01T08:44:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:44:42</Start>
				<Finish>2006-09-01T08:45:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:45:42</Start>
				<Finish>2006-09-01T08:46:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:46:42</Start>
				<Finish>2006-09-01T08:47:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:47:42</Start>
				<Finish>2006-09-01T08:48:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:48:42</Start>
				<Finish>2006-09-01T08:49:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:49:42</Start>
				<Finish>2006-09-01T08:50:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:50:42</Start>
				<Finish>2006-09-01T08:51:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:51:42</Start>
				<Finish>2006-09-01T08:52:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:52:42</Start>
				<Finish>2006-09-01T08:53:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:53:42</Start>
				<Finish>2006-09-01T08:54:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:54:42</Start>
				<Finish>2006-09-01T08:55:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:55:42</Start>
				<Finish>2006-09-01T08:56:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:56:42</Start>
				<Finish>2006-09-01T08:57:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:57:42</Start>
				<Finish>2006-09-01T08:58:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:58:42</Start>
				<Finish>2006-09-01T08:59:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T08:59:42</Start>
				<Finish>2006-09-01T09:00:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:00:42</Start>
				<Finish>2006-09-01T09:01:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:01:42</Start>
				<Finish>2006-09-01T09:02:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:02:42</Start>
				<Finish>2006-09-01T09:03:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:03:42</Start>
				<Finish>2006-09-01T09:04:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:04:42</Start>
				<Finish>2006-09-01T09:05:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:05:42</Start>
				<Finish>2006-09-01T09:06:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:06:42</Start>
				<Finish>2006-09-01T09:07:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-01T09:08:30</Start>
				<Finish>2006-09-02T09:08:30</Finish>
				<Unit>2</Unit>
				<Value>PT6H51M30.51S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-02T09:08:30</Start>
				<Finish>2006-09-03T09:08:30</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-03T09:08:30</Start>
				<Finish>2006-09-04T09:08:30</Finish>
				<Unit>2</Unit>
				<Value>PT1H8M30.09S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-04T09:08:30</Start>
				<Finish>2006-09-05T09:08:30</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0.6S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T09:08:30</Start>
				<Finish>2006-09-05T16:59:54</Finish>
				<Unit>2</Unit>
				<Value>PT6H51M24.51S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T16:59:54</Start>
				<Finish>2006-09-05T17:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M4.5S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T17:59:54</Start>
				<Finish>2006-09-05T18:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T18:59:54</Start>
				<Finish>2006-09-05T19:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T19:59:54</Start>
				<Finish>2006-09-05T20:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T20:59:54</Start>
				<Finish>2006-09-05T21:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T21:59:54</Start>
				<Finish>2006-09-05T22:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-05T22:59:54</Start>
				<Finish>2006-09-06T00:00:00</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T00:00:00</Start>
				<Finish>2006-09-06T00:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T00:59:54</Start>
				<Finish>2006-09-06T01:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T01:59:54</Start>
				<Finish>2006-09-06T02:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T02:59:54</Start>
				<Finish>2006-09-06T03:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T03:59:54</Start>
				<Finish>2006-09-06T04:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T04:59:54</Start>
				<Finish>2006-09-06T05:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T05:59:54</Start>
				<Finish>2006-09-06T06:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T06:59:54</Start>
				<Finish>2006-09-06T07:59:54</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T07:59:54</Start>
				<Finish>2006-09-06T08:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H44M55.95S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T08:59:54</Start>
				<Finish>2006-09-06T09:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T09:59:54</Start>
				<Finish>2006-09-06T10:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T10:59:54</Start>
				<Finish>2006-09-06T11:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H45M0.45S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T11:59:54</Start>
				<Finish>2006-09-06T12:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M4.5S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T12:59:54</Start>
				<Finish>2006-09-06T13:59:54</Finish>
				<Unit>1</Unit>
				<Value>PT0H44M55.95S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T13:59:54</Start>
				<Finish>2006-09-06T14:00:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:00:54</Start>
				<Finish>2006-09-06T14:01:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:01:54</Start>
				<Finish>2006-09-06T14:02:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:02:54</Start>
				<Finish>2006-09-06T14:03:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:03:54</Start>
				<Finish>2006-09-06T14:04:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:04:54</Start>
				<Finish>2006-09-06T14:05:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:05:54</Start>
				<Finish>2006-09-06T14:06:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:06:54</Start>
				<Finish>2006-09-06T14:07:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:07:54</Start>
				<Finish>2006-09-06T14:08:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:08:54</Start>
				<Finish>2006-09-06T14:09:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:09:54</Start>
				<Finish>2006-09-06T14:10:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:10:54</Start>
				<Finish>2006-09-06T14:11:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:11:54</Start>
				<Finish>2006-09-06T14:12:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:12:54</Start>
				<Finish>2006-09-06T14:13:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:13:54</Start>
				<Finish>2006-09-06T14:14:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:14:54</Start>
				<Finish>2006-09-06T14:15:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:15:54</Start>
				<Finish>2006-09-06T14:16:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:16:54</Start>
				<Finish>2006-09-06T14:17:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:17:54</Start>
				<Finish>2006-09-06T14:18:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:18:54</Start>
				<Finish>2006-09-06T14:19:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:19:54</Start>
				<Finish>2006-09-06T14:20:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:20:54</Start>
				<Finish>2006-09-06T14:21:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:21:54</Start>
				<Finish>2006-09-06T14:22:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:22:54</Start>
				<Finish>2006-09-06T14:23:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:23:54</Start>
				<Finish>2006-09-06T14:24:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:24:54</Start>
				<Finish>2006-09-06T14:25:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:25:54</Start>
				<Finish>2006-09-06T14:26:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:26:54</Start>
				<Finish>2006-09-06T14:27:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:27:54</Start>
				<Finish>2006-09-06T14:28:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:28:54</Start>
				<Finish>2006-09-06T14:29:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:29:54</Start>
				<Finish>2006-09-06T14:30:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:30:54</Start>
				<Finish>2006-09-06T14:31:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:31:54</Start>
				<Finish>2006-09-06T14:32:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:32:54</Start>
				<Finish>2006-09-06T14:33:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:33:54</Start>
				<Finish>2006-09-06T14:34:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:34:54</Start>
				<Finish>2006-09-06T14:35:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:35:54</Start>
				<Finish>2006-09-06T14:36:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:36:54</Start>
				<Finish>2006-09-06T14:37:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:37:54</Start>
				<Finish>2006-09-06T14:38:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:38:54</Start>
				<Finish>2006-09-06T14:39:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:39:54</Start>
				<Finish>2006-09-06T14:40:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:40:54</Start>
				<Finish>2006-09-06T14:41:54</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M45.01S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T14:42:42</Start>
				<Finish>2006-09-06T15:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T15:42:42</Start>
				<Finish>2006-09-06T16:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T16:42:42</Start>
				<Finish>2006-09-06T17:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H8M38.94S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T17:42:42</Start>
				<Finish>2006-09-06T18:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T18:42:42</Start>
				<Finish>2006-09-06T19:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T19:42:42</Start>
				<Finish>2006-09-06T20:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T20:42:42</Start>
				<Finish>2006-09-06T21:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T21:42:42</Start>
				<Finish>2006-09-06T22:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T22:42:42</Start>
				<Finish>2006-09-06T23:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-06T23:42:42</Start>
				<Finish>2006-09-07T00:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T00:42:42</Start>
				<Finish>2006-09-07T01:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T01:42:42</Start>
				<Finish>2006-09-07T02:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T02:42:42</Start>
				<Finish>2006-09-07T03:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T03:42:42</Start>
				<Finish>2006-09-07T04:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T04:42:42</Start>
				<Finish>2006-09-07T05:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T05:42:42</Start>
				<Finish>2006-09-07T06:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T06:42:42</Start>
				<Finish>2006-09-07T07:42:42</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T07:42:42</Start>
				<Finish>2006-09-07T08:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H21M20.84S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T08:42:42</Start>
				<Finish>2006-09-07T09:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T09:42:42</Start>
				<Finish>2006-09-07T10:42:42</Finish>
				<Unit>1</Unit>
				<Value>PT0H29M59.78S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:42:42</Start>
				<Finish>2006-09-07T10:43:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:43:42</Start>
				<Finish>2006-09-07T10:44:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:44:42</Start>
				<Finish>2006-09-07T10:45:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:45:42</Start>
				<Finish>2006-09-07T10:46:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:46:42</Start>
				<Finish>2006-09-07T10:47:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:47:42</Start>
				<Finish>2006-09-07T10:48:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:48:42</Start>
				<Finish>2006-09-07T10:49:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:49:42</Start>
				<Finish>2006-09-07T10:50:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:50:42</Start>
				<Finish>2006-09-07T10:51:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:51:42</Start>
				<Finish>2006-09-07T10:52:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:52:42</Start>
				<Finish>2006-09-07T10:53:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:53:42</Start>
				<Finish>2006-09-07T10:54:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:54:42</Start>
				<Finish>2006-09-07T10:55:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:55:42</Start>
				<Finish>2006-09-07T10:56:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:56:42</Start>
				<Finish>2006-09-07T10:57:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:57:42</Start>
				<Finish>2006-09-07T10:58:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:58:42</Start>
				<Finish>2006-09-07T10:59:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T10:59:42</Start>
				<Finish>2006-09-07T11:00:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:00:42</Start>
				<Finish>2006-09-07T11:01:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:01:42</Start>
				<Finish>2006-09-07T11:02:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:02:42</Start>
				<Finish>2006-09-07T11:03:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:03:42</Start>
				<Finish>2006-09-07T11:04:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:04:42</Start>
				<Finish>2006-09-07T11:05:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:05:42</Start>
				<Finish>2006-09-07T11:06:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:06:42</Start>
				<Finish>2006-09-07T11:07:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:07:42</Start>
				<Finish>2006-09-07T11:08:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:08:42</Start>
				<Finish>2006-09-07T11:09:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:09:42</Start>
				<Finish>2006-09-07T11:10:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:10:42</Start>
				<Finish>2006-09-07T11:11:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:11:42</Start>
				<Finish>2006-09-07T11:12:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:12:42</Start>
				<Finish>2006-09-07T11:13:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:13:42</Start>
				<Finish>2006-09-07T11:14:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:14:42</Start>
				<Finish>2006-09-07T11:15:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:15:42</Start>
				<Finish>2006-09-07T11:16:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:16:42</Start>
				<Finish>2006-09-07T11:17:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:17:42</Start>
				<Finish>2006-09-07T11:18:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:18:42</Start>
				<Finish>2006-09-07T11:19:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:19:42</Start>
				<Finish>2006-09-07T11:20:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:20:42</Start>
				<Finish>2006-09-07T11:21:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:21:42</Start>
				<Finish>2006-09-07T11:22:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:22:42</Start>
				<Finish>2006-09-07T11:23:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:23:42</Start>
				<Finish>2006-09-07T11:24:42</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M30S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T11:25:36</Start>
				<Finish>2006-09-07T12:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H8M36.09S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T12:25:36</Start>
				<Finish>2006-09-07T13:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H6M24.06S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T13:25:36</Start>
				<Finish>2006-09-07T14:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T14:25:36</Start>
				<Finish>2006-09-07T15:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T15:25:36</Start>
				<Finish>2006-09-07T16:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H15M0.15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T16:25:36</Start>
				<Finish>2006-09-07T17:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H8M36.09S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T17:25:36</Start>
				<Finish>2006-09-07T18:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T18:25:36</Start>
				<Finish>2006-09-07T19:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T19:25:36</Start>
				<Finish>2006-09-07T20:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T20:25:36</Start>
				<Finish>2006-09-07T21:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T21:25:36</Start>
				<Finish>2006-09-07T22:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T22:25:36</Start>
				<Finish>2006-09-07T23:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-07T23:25:36</Start>
				<Finish>2006-09-08T00:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T00:25:36</Start>
				<Finish>2006-09-08T01:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T01:25:36</Start>
				<Finish>2006-09-08T02:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T02:25:36</Start>
				<Finish>2006-09-08T03:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T03:25:36</Start>
				<Finish>2006-09-08T04:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T04:25:36</Start>
				<Finish>2006-09-08T05:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T05:25:36</Start>
				<Finish>2006-09-08T06:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T06:25:36</Start>
				<Finish>2006-09-08T07:25:36</Finish>
				<Unit>1</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T07:25:36</Start>
				<Finish>2006-09-08T08:25:36</Finish>
				<Unit>1</Unit>
				<Value>PT0H6M24.06S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:25:36</Start>
				<Finish>2006-09-08T08:26:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:26:36</Start>
				<Finish>2006-09-08T08:27:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:27:36</Start>
				<Finish>2006-09-08T08:28:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:28:36</Start>
				<Finish>2006-09-08T08:29:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:29:36</Start>
				<Finish>2006-09-08T08:30:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:30:36</Start>
				<Finish>2006-09-08T08:31:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:31:36</Start>
				<Finish>2006-09-08T08:32:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:32:36</Start>
				<Finish>2006-09-08T08:33:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:33:36</Start>
				<Finish>2006-09-08T08:34:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:34:36</Start>
				<Finish>2006-09-08T08:35:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:35:36</Start>
				<Finish>2006-09-08T08:36:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:36:36</Start>
				<Finish>2006-09-08T08:37:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:37:36</Start>
				<Finish>2006-09-08T08:38:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:38:36</Start>
				<Finish>2006-09-08T08:39:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:39:36</Start>
				<Finish>2006-09-08T08:40:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:40:36</Start>
				<Finish>2006-09-08T08:41:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:41:36</Start>
				<Finish>2006-09-08T08:42:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:42:36</Start>
				<Finish>2006-09-08T08:43:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:43:36</Start>
				<Finish>2006-09-08T08:44:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:44:36</Start>
				<Finish>2006-09-08T08:45:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:45:36</Start>
				<Finish>2006-09-08T08:46:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:46:36</Start>
				<Finish>2006-09-08T08:47:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:47:36</Start>
				<Finish>2006-09-08T08:48:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:48:36</Start>
				<Finish>2006-09-08T08:49:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:49:36</Start>
				<Finish>2006-09-08T08:50:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:50:36</Start>
				<Finish>2006-09-08T08:51:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:51:36</Start>
				<Finish>2006-09-08T08:52:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:52:36</Start>
				<Finish>2006-09-08T08:53:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:53:36</Start>
				<Finish>2006-09-08T08:54:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:54:36</Start>
				<Finish>2006-09-08T08:55:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:55:36</Start>
				<Finish>2006-09-08T08:56:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:56:36</Start>
				<Finish>2006-09-08T08:57:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:57:36</Start>
				<Finish>2006-09-08T08:58:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:58:36</Start>
				<Finish>2006-09-08T08:59:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T08:59:36</Start>
				<Finish>2006-09-08T09:00:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:00:36</Start>
				<Finish>2006-09-08T09:01:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:01:36</Start>
				<Finish>2006-09-08T09:02:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:02:36</Start>
				<Finish>2006-09-08T09:03:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:03:36</Start>
				<Finish>2006-09-08T09:04:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:04:36</Start>
				<Finish>2006-09-08T09:05:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:05:36</Start>
				<Finish>2006-09-08T09:06:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2006-09-08T09:06:36</Start>
				<Finish>2006-09-08T09:07:36</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M15S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>9</UID>
			<TaskUID>4</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>100</PercentWorkComplete>
			<ActualCost>80000</ActualCost>
			<ActualFinish>2006-08-29T17:00:00</ActualFinish>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualStart>2006-08-26T08:00:00</ActualStart>
			<ActualWork>PT16H0M0S</ActualWork>
			<ACWP>80000.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>80000</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>80000</CostVariance>
			<CV>-80000.00</CV>
			<Delay>0</Delay>
			<Finish>2006-08-29T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>960000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT16H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2006-08-26T08:00:00</Start>
			<Stop>2006-08-29T17:00:00</Stop>
			<Resume>2006-08-29T17:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT16H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2010-03-16T16:00:00</CreationDate>
			<TimephasedData>
				<Type>2</Type>
				<UID>9</UID>
				<Start>2006-08-26T08:00:00</Start>
				<Finish>2006-08-27T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>9</UID>
				<Start>2006-08-27T08:00:00</Start>
				<Finish>2006-08-28T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>9</UID>
				<Start>2006-08-28T08:00:00</Start>
				<Finish>2006-08-29T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>9</UID>
				<Start>2006-08-29T08:00:00</Start>
				<Finish>2006-08-29T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>