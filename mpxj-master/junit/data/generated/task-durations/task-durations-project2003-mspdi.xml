<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project"><Name>task-durations-project2003-mspdi.xml</Name><Author>Project User</Author><CreationDate>2014-10-17T19:41:00</CreationDate><LastSaved>2014-10-17T19:41:00</LastSaved><ScheduleFromStart>1</ScheduleFromStart><StartDate>2014-10-17T08:00:00</StartDate><FinishDate>2014-10-17T17:00:00</FinishDate><FYStartDate>1</FYStartDate><CriticalSlackLimit>0</CriticalSlackLimit><CurrencyDigits>2</CurrencyDigits><CurrencySymbol>£</CurrencySymbol><CurrencySymbolPosition>0</CurrencySymbolPosition><CalendarUID>1</CalendarUID><DefaultStartTime>08:00:00</DefaultStartTime><DefaultFinishTime>17:00:00</DefaultFinishTime><MinutesPerDay>480</MinutesPerDay><MinutesPerWeek>2400</MinutesPerWeek><DaysPerMonth>20</DaysPerMonth><DefaultTaskType>0</DefaultTaskType><DefaultFixedCostAccrual>3</DefaultFixedCostAccrual><DefaultStandardRate>0</DefaultStandardRate><DefaultOvertimeRate>0</DefaultOvertimeRate><DurationFormat>7</DurationFormat><WorkFormat>2</WorkFormat><EditableActualCosts>0</EditableActualCosts><HonorConstraints>0</HonorConstraints><InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary><MultipleCriticalPaths>0</MultipleCriticalPaths><NewTasksEffortDriven>1</NewTasksEffortDriven><NewTasksEstimated>1</NewTasksEstimated><SplitsInProgressTasks>1</SplitsInProgressTasks><SpreadActualCost>0</SpreadActualCost><SpreadPercentComplete>0</SpreadPercentComplete><TaskUpdatesResource>1</TaskUpdatesResource><FiscalYearStart>0</FiscalYearStart><WeekStartDay>1</WeekStartDay><MoveCompletedEndsBack>0</MoveCompletedEndsBack><MoveRemainingStartsBack>0</MoveRemainingStartsBack><MoveRemainingStartsForward>0</MoveRemainingStartsForward><MoveCompletedEndsForward>0</MoveCompletedEndsForward><BaselineForEarnedValue>0</BaselineForEarnedValue><AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks><CurrentDate>2014-10-17T08:00:00</CurrentDate><MicrosoftProjectServerURL>1</MicrosoftProjectServerURL><Autolink>1</Autolink><NewTaskStartDate>0</NewTaskStartDate><DefaultTaskEVMethod>0</DefaultTaskEVMethod><ProjectExternallyEdited>0</ProjectExternallyEdited><ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate><ActualsInSync>1</ActualsInSync><RemoveFileProperties>0</RemoveFileProperties><AdminProject>0</AdminProject><OutlineCodes/><WBSMasks/><ExtendedAttributes><ExtendedAttribute><FieldID>188743783</FieldID><FieldName>Duration1</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743784</FieldID><FieldName>Duration2</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743785</FieldID><FieldName>Duration3</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743955</FieldID><FieldName>Duration4</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743956</FieldID><FieldName>Duration5</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743957</FieldID><FieldName>Duration6</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743958</FieldID><FieldName>Duration7</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743959</FieldID><FieldName>Duration8</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743960</FieldID><FieldName>Duration9</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743961</FieldID><FieldName>Duration10</FieldName></ExtendedAttribute></ExtendedAttributes><Calendars><Calendar><UID>1</UID><Name>Standard</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar></Calendars><Tasks><Task><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>0</WBS><OutlineNumber>0</OutlineNumber><OutlineLevel>0</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>53</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>0</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>1</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected></Task><Task><UID>1</UID><ID>1</ID><Name>Duration1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>1</WBS><OutlineNumber>1</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>1</UID><FieldID>188743783</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>2</UID><ID>2</ID><Name>Duration2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>2</WBS><OutlineNumber>2</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>2</UID><FieldID>188743784</FieldID><Value>PT16H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>3</UID><ID>3</ID><Name>Duration3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>3</WBS><OutlineNumber>3</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>3</UID><FieldID>188743785</FieldID><Value>PT24H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>4</UID><ID>4</ID><Name>Duration4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>4</WBS><OutlineNumber>4</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>4</UID><FieldID>188743955</FieldID><Value>PT32H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>5</UID><ID>5</ID><Name>Duration5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>5</WBS><OutlineNumber>5</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>5</UID><FieldID>188743956</FieldID><Value>PT40H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>6</UID><ID>6</ID><Name>Duration6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>6</WBS><OutlineNumber>6</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>6</UID><FieldID>188743957</FieldID><Value>PT48H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>7</UID><ID>7</ID><Name>Duration7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>7</WBS><OutlineNumber>7</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>7</UID><FieldID>188743958</FieldID><Value>PT56H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>8</UID><ID>8</ID><Name>Duration8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>8</WBS><OutlineNumber>8</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>8</UID><FieldID>188743959</FieldID><Value>PT64H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>9</UID><ID>9</ID><Name>Duration9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>9</WBS><OutlineNumber>9</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>9</UID><FieldID>188743960</FieldID><Value>PT72H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>10</UID><ID>10</ID><Name>Duration10</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>10</WBS><OutlineNumber>10</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>10</UID><FieldID>188743961</FieldID><Value>PT80H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>11</UID><ID>11</ID><Name>Duration1 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>11</WBS><OutlineNumber>11</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>11</UID><FieldID>188743783</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>12</UID><ID>12</ID><Name>Duration1 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>12</WBS><OutlineNumber>12</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>12</UID><FieldID>188743783</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>13</UID><ID>13</ID><Name>Duration1 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>13</WBS><OutlineNumber>13</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>13</UID><FieldID>188743783</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>14</UID><ID>14</ID><Name>Duration1 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>14</WBS><OutlineNumber>14</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>14</UID><FieldID>188743783</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>15</UID><ID>15</ID><Name>Duration1 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>15</WBS><OutlineNumber>15</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>15</UID><FieldID>188743783</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>16</UID><ID>16</ID><Name>Duration1 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>16</WBS><OutlineNumber>16</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>16</UID><FieldID>188743783</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>17</UID><ID>17</ID><Name>Duration1 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>17</WBS><OutlineNumber>17</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>17</UID><FieldID>188743783</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>18</UID><ID>18</ID><Name>Duration1 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>18</WBS><OutlineNumber>18</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>18</UID><FieldID>188743783</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>19</UID><ID>19</ID><Name>Duration1 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>19</WBS><OutlineNumber>19</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>19</UID><FieldID>188743783</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>20</UID><ID>20</ID><Name>Duration1 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>20</WBS><OutlineNumber>20</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>20</UID><FieldID>188743783</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>21</UID><ID>21</ID><Name>Duration2 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>21</WBS><OutlineNumber>21</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>21</UID><FieldID>188743784</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>22</UID><ID>22</ID><Name>Duration2 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>22</WBS><OutlineNumber>22</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>22</UID><FieldID>188743784</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>23</UID><ID>23</ID><Name>Duration2 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>23</WBS><OutlineNumber>23</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>23</UID><FieldID>188743784</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>24</UID><ID>24</ID><Name>Duration2 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>24</WBS><OutlineNumber>24</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>24</UID><FieldID>188743784</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>25</UID><ID>25</ID><Name>Duration2 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>25</WBS><OutlineNumber>25</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>25</UID><FieldID>188743784</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>26</UID><ID>26</ID><Name>Duration2 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>26</WBS><OutlineNumber>26</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>26</UID><FieldID>188743784</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>27</UID><ID>27</ID><Name>Duration2 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>27</WBS><OutlineNumber>27</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>27</UID><FieldID>188743784</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>28</UID><ID>28</ID><Name>Duration2 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>28</WBS><OutlineNumber>28</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>28</UID><FieldID>188743784</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>29</UID><ID>29</ID><Name>Duration2 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>29</WBS><OutlineNumber>29</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>29</UID><FieldID>188743784</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>30</UID><ID>30</ID><Name>Duration2 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>30</WBS><OutlineNumber>30</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>30</UID><FieldID>188743784</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>31</UID><ID>31</ID><Name>Duration3 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>31</WBS><OutlineNumber>31</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>31</UID><FieldID>188743785</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>32</UID><ID>32</ID><Name>Duration3 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>32</WBS><OutlineNumber>32</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>32</UID><FieldID>188743785</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>33</UID><ID>33</ID><Name>Duration3 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>33</WBS><OutlineNumber>33</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>33</UID><FieldID>188743785</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>34</UID><ID>34</ID><Name>Duration3 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>34</WBS><OutlineNumber>34</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>34</UID><FieldID>188743785</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>35</UID><ID>35</ID><Name>Duration3 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>35</WBS><OutlineNumber>35</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>35</UID><FieldID>188743785</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>36</UID><ID>36</ID><Name>Duration3 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>36</WBS><OutlineNumber>36</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>36</UID><FieldID>188743785</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>37</UID><ID>37</ID><Name>Duration3 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>37</WBS><OutlineNumber>37</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>37</UID><FieldID>188743785</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>38</UID><ID>38</ID><Name>Duration3 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>38</WBS><OutlineNumber>38</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>38</UID><FieldID>188743785</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>39</UID><ID>39</ID><Name>Duration3 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>39</WBS><OutlineNumber>39</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>39</UID><FieldID>188743785</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>40</UID><ID>40</ID><Name>Duration3 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>40</WBS><OutlineNumber>40</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>40</UID><FieldID>188743785</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>41</UID><ID>41</ID><Name>Duration4 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>41</WBS><OutlineNumber>41</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>41</UID><FieldID>188743955</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>42</UID><ID>42</ID><Name>Duration4 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>42</WBS><OutlineNumber>42</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>42</UID><FieldID>188743955</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>43</UID><ID>43</ID><Name>Duration4 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>43</WBS><OutlineNumber>43</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>43</UID><FieldID>188743955</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>44</UID><ID>44</ID><Name>Duration4 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>44</WBS><OutlineNumber>44</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>44</UID><FieldID>188743955</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>45</UID><ID>45</ID><Name>Duration4 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>45</WBS><OutlineNumber>45</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>45</UID><FieldID>188743955</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>46</UID><ID>46</ID><Name>Duration4 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>46</WBS><OutlineNumber>46</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>46</UID><FieldID>188743955</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>47</UID><ID>47</ID><Name>Duration4 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>47</WBS><OutlineNumber>47</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>47</UID><FieldID>188743955</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>48</UID><ID>48</ID><Name>Duration4 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>48</WBS><OutlineNumber>48</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>48</UID><FieldID>188743955</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>49</UID><ID>49</ID><Name>Duration4 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>49</WBS><OutlineNumber>49</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>49</UID><FieldID>188743955</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>50</UID><ID>50</ID><Name>Duration4 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>50</WBS><OutlineNumber>50</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>50</UID><FieldID>188743955</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>51</UID><ID>51</ID><Name>Duration5 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>51</WBS><OutlineNumber>51</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>51</UID><FieldID>188743956</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>52</UID><ID>52</ID><Name>Duration5 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>52</WBS><OutlineNumber>52</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>52</UID><FieldID>188743956</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>53</UID><ID>53</ID><Name>Duration5 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>53</WBS><OutlineNumber>53</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>53</UID><FieldID>188743956</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>54</UID><ID>54</ID><Name>Duration5 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>54</WBS><OutlineNumber>54</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>54</UID><FieldID>188743956</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>55</UID><ID>55</ID><Name>Duration5 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>55</WBS><OutlineNumber>55</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>55</UID><FieldID>188743956</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>56</UID><ID>56</ID><Name>Duration5 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>56</WBS><OutlineNumber>56</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>56</UID><FieldID>188743956</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>57</UID><ID>57</ID><Name>Duration5 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>57</WBS><OutlineNumber>57</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>57</UID><FieldID>188743956</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>58</UID><ID>58</ID><Name>Duration5 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>58</WBS><OutlineNumber>58</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>58</UID><FieldID>188743956</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>59</UID><ID>59</ID><Name>Duration5 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>59</WBS><OutlineNumber>59</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>59</UID><FieldID>188743956</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>60</UID><ID>60</ID><Name>Duration5 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>60</WBS><OutlineNumber>60</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>60</UID><FieldID>188743956</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>61</UID><ID>61</ID><Name>Duration6 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>61</WBS><OutlineNumber>61</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>61</UID><FieldID>188743957</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>62</UID><ID>62</ID><Name>Duration6 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>62</WBS><OutlineNumber>62</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>62</UID><FieldID>188743957</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>63</UID><ID>63</ID><Name>Duration6 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>63</WBS><OutlineNumber>63</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>63</UID><FieldID>188743957</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>64</UID><ID>64</ID><Name>Duration6 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>64</WBS><OutlineNumber>64</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>64</UID><FieldID>188743957</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>65</UID><ID>65</ID><Name>Duration6 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>65</WBS><OutlineNumber>65</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>65</UID><FieldID>188743957</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>66</UID><ID>66</ID><Name>Duration6 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>66</WBS><OutlineNumber>66</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>66</UID><FieldID>188743957</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>67</UID><ID>67</ID><Name>Duration6 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>67</WBS><OutlineNumber>67</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>67</UID><FieldID>188743957</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>68</UID><ID>68</ID><Name>Duration6 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>68</WBS><OutlineNumber>68</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>68</UID><FieldID>188743957</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>69</UID><ID>69</ID><Name>Duration6 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>69</WBS><OutlineNumber>69</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>69</UID><FieldID>188743957</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>70</UID><ID>70</ID><Name>Duration6 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>70</WBS><OutlineNumber>70</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>70</UID><FieldID>188743957</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>71</UID><ID>71</ID><Name>Duration7 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>71</WBS><OutlineNumber>71</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>71</UID><FieldID>188743958</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>72</UID><ID>72</ID><Name>Duration7 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>72</WBS><OutlineNumber>72</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>72</UID><FieldID>188743958</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>73</UID><ID>73</ID><Name>Duration7 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>73</WBS><OutlineNumber>73</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>73</UID><FieldID>188743958</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>74</UID><ID>74</ID><Name>Duration7 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>74</WBS><OutlineNumber>74</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>74</UID><FieldID>188743958</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>75</UID><ID>75</ID><Name>Duration7 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>75</WBS><OutlineNumber>75</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>75</UID><FieldID>188743958</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>76</UID><ID>76</ID><Name>Duration7 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>76</WBS><OutlineNumber>76</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>76</UID><FieldID>188743958</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>77</UID><ID>77</ID><Name>Duration7 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>77</WBS><OutlineNumber>77</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>77</UID><FieldID>188743958</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>78</UID><ID>78</ID><Name>Duration7 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>78</WBS><OutlineNumber>78</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>78</UID><FieldID>188743958</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>79</UID><ID>79</ID><Name>Duration7 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>79</WBS><OutlineNumber>79</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>79</UID><FieldID>188743958</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>80</UID><ID>80</ID><Name>Duration7 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>80</WBS><OutlineNumber>80</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>80</UID><FieldID>188743958</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>81</UID><ID>81</ID><Name>Duration8 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>81</WBS><OutlineNumber>81</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>81</UID><FieldID>188743959</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>82</UID><ID>82</ID><Name>Duration8 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>82</WBS><OutlineNumber>82</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>82</UID><FieldID>188743959</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>83</UID><ID>83</ID><Name>Duration8 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>83</WBS><OutlineNumber>83</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>83</UID><FieldID>188743959</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>84</UID><ID>84</ID><Name>Duration8 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>84</WBS><OutlineNumber>84</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>84</UID><FieldID>188743959</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>85</UID><ID>85</ID><Name>Duration8 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>85</WBS><OutlineNumber>85</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>85</UID><FieldID>188743959</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>86</UID><ID>86</ID><Name>Duration8 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>86</WBS><OutlineNumber>86</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>86</UID><FieldID>188743959</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>87</UID><ID>87</ID><Name>Duration8 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>87</WBS><OutlineNumber>87</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>87</UID><FieldID>188743959</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>88</UID><ID>88</ID><Name>Duration8 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>88</WBS><OutlineNumber>88</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>88</UID><FieldID>188743959</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>89</UID><ID>89</ID><Name>Duration8 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>89</WBS><OutlineNumber>89</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>89</UID><FieldID>188743959</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>90</UID><ID>90</ID><Name>Duration8 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>90</WBS><OutlineNumber>90</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>90</UID><FieldID>188743959</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>91</UID><ID>91</ID><Name>Duration9 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>91</WBS><OutlineNumber>91</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>91</UID><FieldID>188743960</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>92</UID><ID>92</ID><Name>Duration9 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>92</WBS><OutlineNumber>92</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>92</UID><FieldID>188743960</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>93</UID><ID>93</ID><Name>Duration9 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>93</WBS><OutlineNumber>93</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>93</UID><FieldID>188743960</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>94</UID><ID>94</ID><Name>Duration9 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>94</WBS><OutlineNumber>94</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>94</UID><FieldID>188743960</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>95</UID><ID>95</ID><Name>Duration9 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>95</WBS><OutlineNumber>95</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>95</UID><FieldID>188743960</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>96</UID><ID>96</ID><Name>Duration9 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>96</WBS><OutlineNumber>96</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>96</UID><FieldID>188743960</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>97</UID><ID>97</ID><Name>Duration9 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>97</WBS><OutlineNumber>97</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>97</UID><FieldID>188743960</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>98</UID><ID>98</ID><Name>Duration9 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>98</WBS><OutlineNumber>98</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>98</UID><FieldID>188743960</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>99</UID><ID>99</ID><Name>Duration9 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>99</WBS><OutlineNumber>99</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>99</UID><FieldID>188743960</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>100</UID><ID>100</ID><Name>Duration9 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>100</WBS><OutlineNumber>100</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>100</UID><FieldID>188743960</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task><Task><UID>101</UID><ID>101</ID><Name>Duration10 - Task 0</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>101</WBS><OutlineNumber>101</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>101</UID><FieldID>188743961</FieldID><Value>PT0H1M0S</Value><DurationFormat>3</DurationFormat></ExtendedAttribute></Task><Task><UID>102</UID><ID>102</ID><Name>Duration10 - Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>102</WBS><OutlineNumber>102</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>102</UID><FieldID>188743961</FieldID><Value>PT1H0M0S</Value><DurationFormat>5</DurationFormat></ExtendedAttribute></Task><Task><UID>103</UID><ID>103</ID><Name>Duration10 - Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>103</WBS><OutlineNumber>103</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>103</UID><FieldID>188743961</FieldID><Value>PT8H0M0S</Value><DurationFormat>7</DurationFormat></ExtendedAttribute></Task><Task><UID>104</UID><ID>104</ID><Name>Duration10 - Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>104</WBS><OutlineNumber>104</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>104</UID><FieldID>188743961</FieldID><Value>PT40H0M0S</Value><DurationFormat>9</DurationFormat></ExtendedAttribute></Task><Task><UID>105</UID><ID>105</ID><Name>Duration10 - Task 4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>105</WBS><OutlineNumber>105</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>105</UID><FieldID>188743961</FieldID><Value>PT160H0M0S</Value><DurationFormat>11</DurationFormat></ExtendedAttribute></Task><Task><UID>106</UID><ID>106</ID><Name>Duration10 - Task 5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>106</WBS><OutlineNumber>106</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>106</UID><FieldID>188743961</FieldID><Value>PT0H1M0S</Value><DurationFormat>4</DurationFormat></ExtendedAttribute></Task><Task><UID>107</UID><ID>107</ID><Name>Duration10 - Task 6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>107</WBS><OutlineNumber>107</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>107</UID><FieldID>188743961</FieldID><Value>PT1H0M0S</Value><DurationFormat>6</DurationFormat></ExtendedAttribute></Task><Task><UID>108</UID><ID>108</ID><Name>Duration10 - Task 7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>108</WBS><OutlineNumber>108</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>108</UID><FieldID>188743961</FieldID><Value>PT24H0M0S</Value><DurationFormat>8</DurationFormat></ExtendedAttribute></Task><Task><UID>109</UID><ID>109</ID><Name>Duration10 - Task 8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>109</WBS><OutlineNumber>109</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>109</UID><FieldID>188743961</FieldID><Value>PT168H0M0S</Value><DurationFormat>10</DurationFormat></ExtendedAttribute></Task><Task><UID>110</UID><ID>110</ID><Name>Duration10 - Task 9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:41:00</CreateDate><WBS>110</WBS><OutlineNumber>110</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>110</UID><FieldID>188743961</FieldID><Value>PT720H0M0S</Value><DurationFormat>12</DurationFormat></ExtendedAttribute></Task></Tasks><Resources><Resource><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>2</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive><IsEnterprise>0</IsEnterprise><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate></Resource></Resources><Assignments><Assignment><UID>1</UID><TaskUID>1</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>1</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>2</UID><TaskUID>2</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>2</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>3</UID><TaskUID>3</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>3</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>4</UID><TaskUID>4</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>4</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>5</UID><TaskUID>5</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>5</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>6</UID><TaskUID>6</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>6</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>7</UID><TaskUID>7</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>7</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>8</UID><TaskUID>8</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>8</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>9</UID><TaskUID>9</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>9</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>10</UID><TaskUID>10</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>10</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>11</UID><TaskUID>11</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>11</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>12</UID><TaskUID>12</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>12</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>13</UID><TaskUID>13</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>13</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>14</UID><TaskUID>14</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>14</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>15</UID><TaskUID>15</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>15</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>16</UID><TaskUID>16</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>16</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>17</UID><TaskUID>17</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>17</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>18</UID><TaskUID>18</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>18</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>19</UID><TaskUID>19</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>19</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>20</UID><TaskUID>20</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>20</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>21</UID><TaskUID>21</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>21</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>22</UID><TaskUID>22</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>22</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>23</UID><TaskUID>23</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>23</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>24</UID><TaskUID>24</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>24</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>25</UID><TaskUID>25</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>25</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>26</UID><TaskUID>26</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>26</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>27</UID><TaskUID>27</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>27</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>28</UID><TaskUID>28</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>28</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>29</UID><TaskUID>29</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>29</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>30</UID><TaskUID>30</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>30</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>31</UID><TaskUID>31</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>31</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>32</UID><TaskUID>32</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>32</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>33</UID><TaskUID>33</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>33</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>34</UID><TaskUID>34</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>34</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>35</UID><TaskUID>35</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>35</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>36</UID><TaskUID>36</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>36</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>37</UID><TaskUID>37</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>37</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>38</UID><TaskUID>38</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>38</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>39</UID><TaskUID>39</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>39</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>40</UID><TaskUID>40</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>40</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>41</UID><TaskUID>41</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>41</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>42</UID><TaskUID>42</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>42</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>43</UID><TaskUID>43</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>43</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>44</UID><TaskUID>44</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>44</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>45</UID><TaskUID>45</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>45</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>46</UID><TaskUID>46</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>46</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>47</UID><TaskUID>47</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>47</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>48</UID><TaskUID>48</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>48</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>49</UID><TaskUID>49</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>49</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>50</UID><TaskUID>50</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>50</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>51</UID><TaskUID>51</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>51</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>52</UID><TaskUID>52</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>52</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>53</UID><TaskUID>53</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>53</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>54</UID><TaskUID>54</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>54</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>55</UID><TaskUID>55</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>55</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>56</UID><TaskUID>56</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>56</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>57</UID><TaskUID>57</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>57</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>58</UID><TaskUID>58</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>58</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>59</UID><TaskUID>59</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>59</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>60</UID><TaskUID>60</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>60</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>61</UID><TaskUID>61</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>61</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>62</UID><TaskUID>62</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>62</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>63</UID><TaskUID>63</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>63</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>64</UID><TaskUID>64</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>64</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>65</UID><TaskUID>65</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>65</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>66</UID><TaskUID>66</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>66</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>67</UID><TaskUID>67</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>67</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>68</UID><TaskUID>68</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>68</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>69</UID><TaskUID>69</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>69</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>70</UID><TaskUID>70</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>70</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>71</UID><TaskUID>71</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>71</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>72</UID><TaskUID>72</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>72</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>73</UID><TaskUID>73</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>73</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>74</UID><TaskUID>74</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>74</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>75</UID><TaskUID>75</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>75</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>76</UID><TaskUID>76</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>76</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>77</UID><TaskUID>77</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>77</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>78</UID><TaskUID>78</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>78</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>79</UID><TaskUID>79</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>79</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>80</UID><TaskUID>80</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>80</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>81</UID><TaskUID>81</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>81</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>82</UID><TaskUID>82</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>82</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>83</UID><TaskUID>83</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>83</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>84</UID><TaskUID>84</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>84</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>85</UID><TaskUID>85</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>85</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>86</UID><TaskUID>86</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>86</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>87</UID><TaskUID>87</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>87</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>88</UID><TaskUID>88</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>88</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>89</UID><TaskUID>89</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>89</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>90</UID><TaskUID>90</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>90</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>91</UID><TaskUID>91</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>91</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>92</UID><TaskUID>92</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>92</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>93</UID><TaskUID>93</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>93</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>94</UID><TaskUID>94</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>94</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>95</UID><TaskUID>95</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>95</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>96</UID><TaskUID>96</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>96</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>97</UID><TaskUID>97</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>97</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>98</UID><TaskUID>98</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>98</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>99</UID><TaskUID>99</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>99</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>100</UID><TaskUID>100</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>100</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>101</UID><TaskUID>101</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>101</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>102</UID><TaskUID>102</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>102</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>103</UID><TaskUID>103</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>103</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>104</UID><TaskUID>104</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>104</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>105</UID><TaskUID>105</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>105</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>106</UID><TaskUID>106</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>106</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>107</UID><TaskUID>107</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>107</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>108</UID><TaskUID>108</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>108</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>109</UID><TaskUID>109</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>109</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>110</UID><TaskUID>110</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:41:00</CreationDate><TimephasedData><Type>1</Type><UID>110</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment></Assignments></Project>
