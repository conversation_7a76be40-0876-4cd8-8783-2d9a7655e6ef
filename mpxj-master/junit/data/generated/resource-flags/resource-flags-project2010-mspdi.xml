<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<Name>resource-flags-project2010-mspdi.xml</Name>
	<Author>Project User</Author>
	<CreationDate>2017-03-08T08:00:00</CreationDate>
	<LastSaved>2017-03-08T21:19:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2017-03-08T08:00:00</StartDate>
	<FinishDate>2017-03-08T08:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2017-03-08T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>1</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>188743752</FieldID>
			<FieldName>Flag1</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743753</FieldID>
			<FieldName>Flag2</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743754</FieldID>
			<FieldName>Flag3</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743755</FieldID>
			<FieldName>Flag4</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743756</FieldID>
			<FieldName>Flag5</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743757</FieldID>
			<FieldName>Flag6</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743758</FieldID>
			<FieldName>Flag7</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743759</FieldID>
			<FieldName>Flag8</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743760</FieldID>
			<FieldName>Flag9</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743761</FieldID>
			<FieldName>Flag10</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743972</FieldID>
			<FieldName>Flag11</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743973</FieldID>
			<FieldName>Flag12</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743974</FieldID>
			<FieldName>Flag13</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743975</FieldID>
			<FieldName>Flag14</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743976</FieldID>
			<FieldName>Flag15</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743977</FieldID>
			<FieldName>Flag16</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743978</FieldID>
			<FieldName>Flag17</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743979</FieldID>
			<FieldName>Flag18</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743980</FieldID>
			<FieldName>Flag19</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743981</FieldID>
			<FieldName>Flag20</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521022</FieldID>
			<FieldName>Flag10</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852666</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521023</FieldID>
			<FieldName>Flag1</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852667</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521024</FieldID>
			<FieldName>Flag2</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852668</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521025</FieldID>
			<FieldName>Flag3</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852669</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521026</FieldID>
			<FieldName>Flag4</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852670</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521027</FieldID>
			<FieldName>Flag5</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852671</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521028</FieldID>
			<FieldName>Flag6</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852672</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521029</FieldID>
			<FieldName>Flag7</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852673</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521030</FieldID>
			<FieldName>Flag8</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852674</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521031</FieldID>
			<FieldName>Flag9</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852675</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521091</FieldID>
			<FieldName>Flag11</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852732</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521092</FieldID>
			<FieldName>Flag12</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852733</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521093</FieldID>
			<FieldName>Flag13</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852734</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521094</FieldID>
			<FieldName>Flag14</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852735</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521095</FieldID>
			<FieldName>Flag15</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852736</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521096</FieldID>
			<FieldName>Flag16</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852737</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521097</FieldID>
			<FieldName>Flag17</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852738</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521098</FieldID>
			<FieldName>Flag18</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852739</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521099</FieldID>
			<FieldName>Flag19</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852740</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521100</FieldID>
			<FieldName>Flag20</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852741</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<Name>Flag1</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<Name>Flag2</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<Name>Flag3</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<Name>Flag4</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>7</UID>
			<Name>Flag5</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>8</UID>
			<Name>Flag6</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>9</UID>
			<Name>Flag7</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>10</UID>
			<Name>Flag8</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>11</UID>
			<Name>Flag9</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>12</UID>
			<Name>Flag10</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>13</UID>
			<Name>Flag11</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>14</UID>
			<Name>Flag12</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>15</UID>
			<Name>Flag13</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>16</UID>
			<Name>Flag14</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>17</UID>
			<Name>Flag15</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>18</UID>
			<Name>Flag16</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>19</UID>
			<Name>Flag17</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>20</UID>
			<Name>Flag18</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>21</UID>
			<Name>Flag19</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>22</UID>
			<Name>Flag20</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2017-03-08T21:19:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2017-03-08T08:00:00</Start>
			<Finish>2017-03-08T08:00:00</Finish>
			<Duration>PT0H0M0S</Duration>
			<ManualStart>2017-03-08T08:00:00</ManualStart>
			<ManualFinish>2017-03-08T08:00:00</ManualFinish>
			<ManualDuration>PT0H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2017-03-08T08:00:00</EarlyStart>
			<EarlyFinish>2017-03-08T08:00:00</EarlyFinish>
			<LateStart>2017-03-08T08:00:00</LateStart>
			<LateFinish>2017-03-08T08:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT0H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Flag1</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>3</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521023</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Flag2</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>4</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521024</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Flag3</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521025</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Flag4</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521026</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>5</UID>
			<ID>5</ID>
			<Name>Flag5</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>7</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521027</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>6</UID>
			<ID>6</ID>
			<Name>Flag6</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>8</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521028</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>7</UID>
			<ID>7</ID>
			<Name>Flag7</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>9</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521029</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>8</UID>
			<ID>8</ID>
			<Name>Flag8</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>10</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521030</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>9</UID>
			<ID>9</ID>
			<Name>Flag9</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>11</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521031</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>10</UID>
			<ID>10</ID>
			<Name>Flag10</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>12</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521022</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>11</UID>
			<ID>11</ID>
			<Name>Flag11</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>13</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521091</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>12</UID>
			<ID>12</ID>
			<Name>Flag12</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>14</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521092</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>13</UID>
			<ID>13</ID>
			<Name>Flag13</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>15</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521093</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>14</UID>
			<ID>14</ID>
			<Name>Flag14</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>16</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521094</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>15</UID>
			<ID>15</ID>
			<Name>Flag15</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>17</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521095</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>16</UID>
			<ID>16</ID>
			<Name>Flag16</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>18</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521096</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>17</UID>
			<ID>17</ID>
			<Name>Flag17</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>19</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521097</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>18</UID>
			<ID>18</ID>
			<Name>Flag18</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>20</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521098</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>19</UID>
			<ID>19</ID>
			<Name>Flag19</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>21</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521099</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>20</UID>
			<ID>20</ID>
			<Name>Flag20</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>F</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>22</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-03-08T21:19:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521100</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
	</Resources>
	<Assignments/>
</Project>