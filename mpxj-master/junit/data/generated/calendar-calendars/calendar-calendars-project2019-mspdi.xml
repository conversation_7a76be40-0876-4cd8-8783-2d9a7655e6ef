<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<BuildNumber>16.0.10730.20102</BuildNumber>
	<Name>calendar-calendars-project2019-mspdi.xml</Name>
	<GUID>5425A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
	<CreationDate>2018-10-18T08:00:00</CreationDate>
	<LastSaved>2018-10-18T14:33:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2018-10-18T08:00:00</StartDate>
	<FinishDate>2018-10-18T08:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2018-10-18T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>0</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<Views>
		<View>
			<Name>Gantt &amp;with Timeline</Name>
		</View>
		<View>
			<Name>&amp;Gantt Chart</Name>
			<IsCustomized>true</IsCustomized>
		</View>
		<View>
			<Name>Time&amp;line</Name>
			<IsCustomized>true</IsCustomized>
		</View>
	</Views>
	<Filters>
		<Filter>
			<Name>&amp;All Tasks</Name>
		</Filter>
	</Filters>
	<Groups>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
	</Groups>
	<Tables>
		<Table>
			<Name>&amp;Entry</Name>
			<IsCustomized>true</IsCustomized>
		</Table>
	</Tables>
	<Maps/>
	<Reports/>
	<Drawings/>
	<DataLinks/>
	<VBAProjects>
		<VBAProject>
			<Name>ThisProject</Name>
			<IsCustomized>true</IsCustomized>
		</VBAProject>
	</VBAProjects>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes/>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<GUID>16F4FFF7-D8D2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<GUID>6525A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Calendar1</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<GUID>6625A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Calendar2</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<GUID>6725A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource One</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<GUID>6925A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource Two</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<GUID>5625A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:33:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2018-10-18T08:00:00</Start>
			<Finish>2018-10-18T08:00:00</Finish>
			<Duration>PT0H0M0S</Duration>
			<ManualStart>2018-10-18T08:00:00</ManualStart>
			<ManualFinish>2018-10-18T08:00:00</ManualFinish>
			<ManualDuration>PT0H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<FreeformDurationFormat>39</FreeformDurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2018-10-18T08:00:00</EarlyStart>
			<EarlyFinish>2018-10-18T08:00:00</EarlyFinish>
			<LateStart>2018-10-18T08:00:00</LateStart>
			<LateFinish>2018-10-18T08:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT0H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<GUID>A0CB8B7E-2A8C-436D-0000-0000000000FF</GUID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:33:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<GUID>6725A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>1</ID>
			<Name>Resource One</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:33:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>2</UID>
			<GUID>6925A46C-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>2</ID>
			<Name>Resource Two</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:33:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments/>
</Project>