<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<Name>calendar-recurring-exceptions-project2013-mspdi.xml</Name>
	<Title>Project1</Title>
	<Author>Project User</Author>
	<CreationDate>2017-10-20T08:00:00</CreationDate>
	<LastSaved>2017-11-08T10:26:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2017-10-20T08:00:00</StartDate>
	<FinishDate>2017-10-20T08:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2017-11-08T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>0</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes/>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-01-01T00:00:00</FromDate>
						<ToDate>2000-01-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-02-01T00:00:00</FromDate>
						<ToDate>2000-02-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-02-04T00:00:00</FromDate>
						<ToDate>2000-02-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-02-07T00:00:00</FromDate>
						<ToDate>2000-02-07T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-02-10T00:00:00</FromDate>
						<ToDate>2000-02-10T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-03-01T00:00:00</FromDate>
						<ToDate>2000-03-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-03-06T00:00:00</FromDate>
						<ToDate>2000-03-06T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-03-11T00:00:00</FromDate>
						<ToDate>2000-03-11T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-03-16T00:00:00</FromDate>
						<ToDate>2000-03-16T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-03-21T00:00:00</FromDate>
						<ToDate>2000-03-21T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-04-01T00:00:00</FromDate>
						<ToDate>2000-04-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-04-08T00:00:00</FromDate>
						<ToDate>2000-04-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-04-15T00:00:00</FromDate>
						<ToDate>2000-04-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-04-22T00:00:00</FromDate>
						<ToDate>2000-04-22T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-04-29T00:00:00</FromDate>
						<ToDate>2000-04-29T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2000-05-06T00:00:00</FromDate>
						<ToDate>2000-05-06T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-01-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-02T00:00:00</FromDate>
						<ToDate>2001-01-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-03T00:00:00</FromDate>
						<ToDate>2001-01-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-04T00:00:00</FromDate>
						<ToDate>2001-01-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-05T00:00:00</FromDate>
						<ToDate>2001-01-05T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-06T00:00:00</FromDate>
						<ToDate>2001-01-06T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-08T00:00:00</FromDate>
						<ToDate>2001-01-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-15T00:00:00</FromDate>
						<ToDate>2001-01-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-16T00:00:00</FromDate>
						<ToDate>2001-01-16T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-24T00:00:00</FromDate>
						<ToDate>2001-01-24T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-01-30T00:00:00</FromDate>
						<ToDate>2001-01-30T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-01T00:00:00</FromDate>
						<ToDate>2001-02-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-09T00:00:00</FromDate>
						<ToDate>2001-02-09T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-13T00:00:00</FromDate>
						<ToDate>2001-02-13T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-14T00:00:00</FromDate>
						<ToDate>2001-02-14T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-17T00:00:00</FromDate>
						<ToDate>2001-02-17T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-02-18T00:00:00</FromDate>
						<ToDate>2001-02-18T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-01T00:00:00</FromDate>
						<ToDate>2001-03-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-07T00:00:00</FromDate>
						<ToDate>2001-03-07T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-16T00:00:00</FromDate>
						<ToDate>2001-03-16T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-28T00:00:00</FromDate>
						<ToDate>2001-03-28T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-29T00:00:00</FromDate>
						<ToDate>2001-03-29T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-03-31T00:00:00</FromDate>
						<ToDate>2001-03-31T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-04-08T00:00:00</FromDate>
						<ToDate>2001-04-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-04-20T00:00:00</FromDate>
						<ToDate>2001-04-20T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-04-26T00:00:00</FromDate>
						<ToDate>2001-04-26T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-05-12T00:00:00</FromDate>
						<ToDate>2001-05-12T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-05-24T00:00:00</FromDate>
						<ToDate>2001-05-24T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-05-25T00:00:00</FromDate>
						<ToDate>2001-05-25T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-05-27T00:00:00</FromDate>
						<ToDate>2001-05-27T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-06-23T00:00:00</FromDate>
						<ToDate>2001-06-23T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-06-29T00:00:00</FromDate>
						<ToDate>2001-06-29T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-07-15T00:00:00</FromDate>
						<ToDate>2001-07-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-08-03T00:00:00</FromDate>
						<ToDate>2001-08-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-08-04T00:00:00</FromDate>
						<ToDate>2001-08-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-09-02T00:00:00</FromDate>
						<ToDate>2001-09-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-09-15T00:00:00</FromDate>
						<ToDate>2001-09-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-10-21T00:00:00</FromDate>
						<ToDate>2001-10-21T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-10-27T00:00:00</FromDate>
						<ToDate>2001-10-27T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2001-12-09T00:00:00</FromDate>
						<ToDate>2001-12-09T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-05T00:00:00</FromDate>
						<ToDate>2002-01-05T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-07T00:00:00</FromDate>
						<ToDate>2002-01-07T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-08T00:00:00</FromDate>
						<ToDate>2002-01-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-13T00:00:00</FromDate>
						<ToDate>2002-01-13T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-16T00:00:00</FromDate>
						<ToDate>2002-01-16T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-24T00:00:00</FromDate>
						<ToDate>2002-01-24T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-25T00:00:00</FromDate>
						<ToDate>2002-01-25T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-01-27T00:00:00</FromDate>
						<ToDate>2002-01-27T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-03-04T00:00:00</FromDate>
						<ToDate>2002-03-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-03-17T00:00:00</FromDate>
						<ToDate>2002-03-17T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-04-09T00:00:00</FromDate>
						<ToDate>2002-04-09T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-05-06T00:00:00</FromDate>
						<ToDate>2002-05-06T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-05-15T00:00:00</FromDate>
						<ToDate>2002-05-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-06-27T00:00:00</FromDate>
						<ToDate>2002-06-27T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-07-09T00:00:00</FromDate>
						<ToDate>2002-07-09T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-07-26T00:00:00</FromDate>
						<ToDate>2002-07-26T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-08-03T00:00:00</FromDate>
						<ToDate>2002-08-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-09-08T00:00:00</FromDate>
						<ToDate>2002-09-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-09-18T00:00:00</FromDate>
						<ToDate>2002-09-18T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-10-08T00:00:00</FromDate>
						<ToDate>2002-10-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2002-11-28T00:00:00</FromDate>
						<ToDate>2002-11-28T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-01-01T00:00:00</FromDate>
						<ToDate>2003-01-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-01-04T00:00:00</FromDate>
						<ToDate>2003-01-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-01-15T00:00:00</FromDate>
						<ToDate>2003-01-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-01-31T00:00:00</FromDate>
						<ToDate>2003-01-31T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-03-01T00:00:00</FromDate>
						<ToDate>2003-03-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-04-24T00:00:00</FromDate>
						<ToDate>2003-04-24T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-05-01T00:00:00</FromDate>
						<ToDate>2003-05-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-05-11T00:00:00</FromDate>
						<ToDate>2003-05-11T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-05-21T00:00:00</FromDate>
						<ToDate>2003-05-21T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-06-04T00:00:00</FromDate>
						<ToDate>2003-06-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-07-25T00:00:00</FromDate>
						<ToDate>2003-07-25T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-09-25T00:00:00</FromDate>
						<ToDate>2003-09-25T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-10-04T00:00:00</FromDate>
						<ToDate>2003-10-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2003-11-04T00:00:00</FromDate>
						<ToDate>2003-11-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-01-11T00:00:00</FromDate>
						<ToDate>2004-01-11T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-01-30T00:00:00</FromDate>
						<ToDate>2004-01-30T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-02-26T00:00:00</FromDate>
						<ToDate>2004-02-26T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-03-02T00:00:00</FromDate>
						<ToDate>2004-03-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-04-04T00:00:00</FromDate>
						<ToDate>2004-04-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-04-14T00:00:00</FromDate>
						<ToDate>2004-04-14T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-05-01T00:00:00</FromDate>
						<ToDate>2004-05-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-05-20T00:00:00</FromDate>
						<ToDate>2004-05-20T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-07-30T00:00:00</FromDate>
						<ToDate>2004-07-30T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-09-04T00:00:00</FromDate>
						<ToDate>2004-09-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-09-12T00:00:00</FromDate>
						<ToDate>2004-09-12T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2004-12-04T00:00:00</FromDate>
						<ToDate>2004-12-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-01-28T00:00:00</FromDate>
						<ToDate>2005-01-28T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-02-01T00:00:00</FromDate>
						<ToDate>2005-02-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-02-04T00:00:00</FromDate>
						<ToDate>2005-02-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-03-01T00:00:00</FromDate>
						<ToDate>2005-03-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-03-02T00:00:00</FromDate>
						<ToDate>2005-03-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-04-03T00:00:00</FromDate>
						<ToDate>2005-04-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-04-13T00:00:00</FromDate>
						<ToDate>2005-04-13T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-05-08T00:00:00</FromDate>
						<ToDate>2005-05-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-05-19T00:00:00</FromDate>
						<ToDate>2005-05-19T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2005-07-02T00:00:00</FromDate>
						<ToDate>2005-07-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-01-08T00:00:00</FromDate>
						<ToDate>2006-01-08T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-02-01T00:00:00</FromDate>
						<ToDate>2006-02-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-02-04T00:00:00</FromDate>
						<ToDate>2006-02-04T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-03-02T00:00:00</FromDate>
						<ToDate>2006-03-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-03-07T00:00:00</FromDate>
						<ToDate>2006-03-07T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-04-03T00:00:00</FromDate>
						<ToDate>2006-04-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-04-12T00:00:00</FromDate>
						<ToDate>2006-04-12T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-05-18T00:00:00</FromDate>
						<ToDate>2006-05-18T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2006-09-10T00:00:00</FromDate>
						<ToDate>2006-09-10T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-02-01T00:00:00</FromDate>
						<ToDate>2007-02-01T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-03-02T00:00:00</FromDate>
						<ToDate>2007-03-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-03-06T00:00:00</FromDate>
						<ToDate>2007-03-06T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-04-03T00:00:00</FromDate>
						<ToDate>2007-04-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-04-11T00:00:00</FromDate>
						<ToDate>2007-04-11T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-05-13T00:00:00</FromDate>
						<ToDate>2007-05-13T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2007-05-17T00:00:00</FromDate>
						<ToDate>2007-05-17T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2008-03-02T00:00:00</FromDate>
						<ToDate>2008-03-02T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2008-04-03T00:00:00</FromDate>
						<ToDate>2008-04-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2008-04-09T00:00:00</FromDate>
						<ToDate>2008-04-09T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2008-05-15T00:00:00</FromDate>
						<ToDate>2008-05-15T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2009-04-03T00:00:00</FromDate>
						<ToDate>2009-04-03T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>0</DayWorking>
					<TimePeriod>
						<FromDate>2009-05-21T00:00:00</FromDate>
						<ToDate>2009-05-21T23:59:00</ToDate>
					</TimePeriod>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>1</DayWorking>
					<TimePeriod>
						<FromDate>2010-01-02T00:00:00</FromDate>
						<ToDate>2010-01-02T23:59:00</ToDate>
					</TimePeriod>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>09:00:00</FromTime>
							<ToTime>13:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>14:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>1</DayWorking>
					<TimePeriod>
						<FromDate>2010-02-06T00:00:00</FromDate>
						<ToDate>2010-02-06T23:59:00</ToDate>
					</TimePeriod>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>09:00:00</FromTime>
							<ToTime>13:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>14:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>0</DayType>
					<DayWorking>1</DayWorking>
					<TimePeriod>
						<FromDate>2010-03-06T00:00:00</FromDate>
						<ToDate>2010-03-06T23:59:00</ToDate>
					</TimePeriod>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>09:00:00</FromTime>
							<ToTime>13:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>14:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
			</WeekDays>
			<Exceptions>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2000-01-01T00:00:00</FromDate>
						<ToDate>2000-01-03T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Daily 1</Name>
					<Type>1</Type>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2000-02-01T00:00:00</FromDate>
						<ToDate>2000-02-10T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>4</Occurrences>
					<Name>Daily 2</Name>
					<Type>7</Type>
					<Period>3</Period>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2000-03-01T00:00:00</FromDate>
						<ToDate>2000-03-21T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>5</Occurrences>
					<Name>Daily 3</Name>
					<Type>7</Type>
					<Period>5</Period>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2000-04-01T00:00:00</FromDate>
						<ToDate>2000-05-06T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>6</Occurrences>
					<Name>Daily 4</Name>
					<Type>7</Type>
					<Period>7</Period>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-01-15T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Weekly 1 Monday</Name>
					<Type>6</Type>
					<Period>1</Period>
					<DaysOfWeek>2</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-02-13T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>4</Occurrences>
					<Name>Weekly 2 Tuesday</Name>
					<Type>6</Type>
					<Period>2</Period>
					<DaysOfWeek>4</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-03-28T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>5</Occurrences>
					<Name>Weekly 3 Wednesday</Name>
					<Type>6</Type>
					<Period>3</Period>
					<DaysOfWeek>8</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-05-24T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>6</Occurrences>
					<Name>Weekly 4 Thursday</Name>
					<Type>6</Type>
					<Period>4</Period>
					<DaysOfWeek>16</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-08-03T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>7</Occurrences>
					<Name>Weekly 5 Friday</Name>
					<Type>6</Type>
					<Period>5</Period>
					<DaysOfWeek>32</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2001-10-27T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>8</Occurrences>
					<Name>Weekly 6 Saturday</Name>
					<Type>6</Type>
					<Period>6</Period>
					<DaysOfWeek>64</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2001-01-01T00:00:00</FromDate>
						<ToDate>2002-03-17T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>9</Occurrences>
					<Name>Weekly 7 Sunday</Name>
					<Type>6</Type>
					<Period>7</Period>
					<DaysOfWeek>1</DaysOfWeek>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2002-05-06T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Monthly Relative 1</Name>
					<Type>5</Type>
					<Period>2</Period>
					<MonthItem>4</MonthItem>
					<MonthPosition>0</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2002-10-08T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>4</Occurrences>
					<Name>Monthly Relative 2</Name>
					<Type>5</Type>
					<Period>3</Period>
					<MonthItem>5</MonthItem>
					<MonthPosition>1</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2003-05-21T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>5</Occurrences>
					<Name>Monthly Relative 3</Name>
					<Type>5</Type>
					<Period>4</Period>
					<MonthItem>6</MonthItem>
					<MonthPosition>2</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2004-02-26T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>6</Occurrences>
					<Name>Monthly Relative 4</Name>
					<Type>5</Type>
					<Period>5</Period>
					<MonthItem>7</MonthItem>
					<MonthPosition>3</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2005-01-28T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>7</Occurrences>
					<Name>Monthly Relative 5</Name>
					<Type>5</Type>
					<Period>6</Period>
					<MonthItem>8</MonthItem>
					<MonthPosition>4</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2006-02-04T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>8</Occurrences>
					<Name>Monthly Relative 6</Name>
					<Type>5</Type>
					<Period>7</Period>
					<MonthItem>9</MonthItem>
					<MonthPosition>0</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2002-01-01T00:00:00</FromDate>
						<ToDate>2007-05-13T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>9</Occurrences>
					<Name>Monthly Relative 7</Name>
					<Type>5</Type>
					<Period>8</Period>
					<MonthItem>3</MonthItem>
					<MonthPosition>1</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2003-01-01T00:00:00</FromDate>
						<ToDate>2003-05-01T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Monthly Absolute 1</Name>
					<Type>4</Type>
					<Period>2</Period>
					<MonthDay>1</MonthDay>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2003-01-01T00:00:00</FromDate>
						<ToDate>2005-02-04T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>6</Occurrences>
					<Name>Monthly Absolute 2</Name>
					<Type>4</Type>
					<Period>5</Period>
					<MonthDay>4</MonthDay>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2004-01-01T00:00:00</FromDate>
						<ToDate>2007-03-06T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>4</Occurrences>
					<Name>Yearly Relative 1</Name>
					<Type>3</Type>
					<Month>2</Month>
					<MonthItem>5</MonthItem>
					<MonthPosition>0</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2004-01-01T00:00:00</FromDate>
						<ToDate>2008-04-09T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>5</Occurrences>
					<Name>Yearly Relative 2</Name>
					<Type>3</Type>
					<Month>3</Month>
					<MonthItem>6</MonthItem>
					<MonthPosition>1</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2004-01-01T00:00:00</FromDate>
						<ToDate>2009-05-21T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>6</Occurrences>
					<Name>Yearly Relative 3</Name>
					<Type>3</Type>
					<Month>4</Month>
					<MonthItem>7</MonthItem>
					<MonthPosition>2</MonthPosition>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2005-01-01T00:00:00</FromDate>
						<ToDate>2007-02-01T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Yearly Absolute 1</Name>
					<Type>2</Type>
					<Month>1</Month>
					<MonthDay>1</MonthDay>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2005-01-01T00:00:00</FromDate>
						<ToDate>2008-03-02T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>4</Occurrences>
					<Name>Yearly Absolute 2</Name>
					<Type>2</Type>
					<Month>2</Month>
					<MonthDay>2</MonthDay>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2005-01-01T00:00:00</FromDate>
						<ToDate>2009-04-03T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>5</Occurrences>
					<Name>Yearly Absolute 3</Name>
					<Type>2</Type>
					<Month>3</Month>
					<MonthDay>3</MonthDay>
					<DayWorking>0</DayWorking>
				</Exception>
				<Exception>
					<EnteredByOccurrences>1</EnteredByOccurrences>
					<TimePeriod>
						<FromDate>2010-01-01T00:00:00</FromDate>
						<ToDate>2010-03-06T23:59:00</ToDate>
					</TimePeriod>
					<Occurrences>3</Occurrences>
					<Name>Recurring Working</Name>
					<Type>5</Type>
					<Period>1</Period>
					<MonthItem>9</MonthItem>
					<MonthPosition>0</MonthPosition>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>09:00:00</FromTime>
							<ToTime>13:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>14:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</Exception>
			</Exceptions>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Name>Project1</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2017-10-20T10:46:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2017-10-20T08:00:00</Start>
			<Finish>2017-10-20T08:00:00</Finish>
			<Duration>PT0H0M0S</Duration>
			<ManualStart>2017-10-20T08:00:00</ManualStart>
			<ManualFinish>2017-10-20T08:00:00</ManualFinish>
			<ManualDuration>PT0H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>1</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2017-10-20T08:00:00</EarlyStart>
			<EarlyFinish>2017-10-20T08:00:00</EarlyFinish>
			<LateStart>2017-10-20T08:00:00</LateStart>
			<LateFinish>2017-10-20T08:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT0H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2017-10-20T10:46:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments/>
</Project>