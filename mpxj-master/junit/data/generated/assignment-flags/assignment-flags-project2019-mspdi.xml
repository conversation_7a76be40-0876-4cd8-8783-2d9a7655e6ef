<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<BuildNumber>16.0.10730.20102</BuildNumber>
	<Name>assignment-flags-project2019-mspdi.xml</Name>
	<GUID>46BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
	<CreationDate>2018-10-18T08:00:00</CreationDate>
	<LastSaved>2018-10-18T14:34:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2018-10-18T08:00:00</StartDate>
	<FinishDate>2016-01-15T17:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2018-10-18T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>0</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<Views>
		<View>
			<Name>Gantt &amp;with Timeline</Name>
		</View>
		<View>
			<Name>&amp;Gantt Chart</Name>
			<IsCustomized>true</IsCustomized>
		</View>
		<View>
			<Name>Time&amp;line</Name>
			<IsCustomized>true</IsCustomized>
		</View>
		<View>
			<Name>Resource &amp;Usage</Name>
			<IsCustomized>true</IsCustomized>
		</View>
	</Views>
	<Filters>
		<Filter>
			<Name>&amp;All Tasks</Name>
		</Filter>
		<Filter>
			<Name>&amp;All Resources</Name>
		</Filter>
	</Filters>
	<Groups>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
	</Groups>
	<Tables>
		<Table>
			<Name>&amp;Entry</Name>
			<IsCustomized>true</IsCustomized>
		</Table>
		<Table>
			<Name>&amp;Usage</Name>
			<IsCustomized>true</IsCustomized>
		</Table>
	</Tables>
	<Maps/>
	<Reports/>
	<Drawings/>
	<DataLinks/>
	<VBAProjects>
		<VBAProject>
			<Name>ThisProject</Name>
			<IsCustomized>true</IsCustomized>
		</VBAProject>
	</VBAProjects>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>205521022</FieldID>
			<FieldName>Flag10</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852666</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521023</FieldID>
			<FieldName>Flag1</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852667</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521024</FieldID>
			<FieldName>Flag2</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852668</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521025</FieldID>
			<FieldName>Flag3</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852669</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521026</FieldID>
			<FieldName>Flag4</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852670</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521027</FieldID>
			<FieldName>Flag5</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852671</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521028</FieldID>
			<FieldName>Flag6</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852672</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521029</FieldID>
			<FieldName>Flag7</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852673</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521030</FieldID>
			<FieldName>Flag8</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852674</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521031</FieldID>
			<FieldName>Flag9</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852675</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521091</FieldID>
			<FieldName>Flag11</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852732</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521092</FieldID>
			<FieldName>Flag12</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852733</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521093</FieldID>
			<FieldName>Flag13</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852734</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521094</FieldID>
			<FieldName>Flag14</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852735</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521095</FieldID>
			<FieldName>Flag15</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852736</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521096</FieldID>
			<FieldName>Flag16</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852737</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521097</FieldID>
			<FieldName>Flag17</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852738</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521098</FieldID>
			<FieldName>Flag18</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852739</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521099</FieldID>
			<FieldName>Flag19</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852740</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521100</FieldID>
			<FieldName>Flag20</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852741</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<GUID>16F4FFF7-D8D2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<GUID>5CBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 1</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<GUID>61BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 2</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<GUID>66BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 3</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<GUID>6BBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 4</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>7</UID>
			<GUID>70BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 5</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>8</UID>
			<GUID>75BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 6</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>9</UID>
			<GUID>7ABA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 7</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>10</UID>
			<GUID>7FBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 8</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>11</UID>
			<GUID>84BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 9</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>12</UID>
			<GUID>89BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 10</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>13</UID>
			<GUID>8EBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 11</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>14</UID>
			<GUID>93BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 12</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>15</UID>
			<GUID>98BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 13</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>16</UID>
			<GUID>9DBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 14</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>17</UID>
			<GUID>A2BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 15</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>18</UID>
			<GUID>A7BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 16</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>19</UID>
			<GUID>ACBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 17</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>20</UID>
			<GUID>B1BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 18</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>21</UID>
			<GUID>B6BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 19</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>22</UID>
			<GUID>BBBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Resource 20</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<GUID>48BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<FreeformDurationFormat>39</FreeformDurationFormat>
			<Work>PT1600H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>96000000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT1600H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT1600H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>1</UID>
			<GUID>5ABA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>1</ID>
			<Name>Task 1</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>2</UID>
			<GUID>5FBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>2</ID>
			<Name>Task 2</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>2</WBS>
			<OutlineNumber>2</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>3</UID>
			<GUID>64BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>3</ID>
			<Name>Task 3</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>3</WBS>
			<OutlineNumber>3</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>4</UID>
			<GUID>69BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>4</ID>
			<Name>Task 4</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>4</WBS>
			<OutlineNumber>4</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>5</UID>
			<GUID>6EBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>5</ID>
			<Name>Task 5</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>5</WBS>
			<OutlineNumber>5</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>6</UID>
			<GUID>73BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>6</ID>
			<Name>Task 6</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>6</WBS>
			<OutlineNumber>6</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>7</UID>
			<GUID>78BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>7</ID>
			<Name>Task 7</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>7</WBS>
			<OutlineNumber>7</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>8</UID>
			<GUID>7DBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>8</ID>
			<Name>Task 8</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>8</WBS>
			<OutlineNumber>8</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>9</UID>
			<GUID>82BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>9</ID>
			<Name>Task 9</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>9</WBS>
			<OutlineNumber>9</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>10</UID>
			<GUID>87BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>10</ID>
			<Name>Task 10</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>10</WBS>
			<OutlineNumber>10</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>11</UID>
			<GUID>8CBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>11</ID>
			<Name>Task 11</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>11</WBS>
			<OutlineNumber>11</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>12</UID>
			<GUID>91BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>12</ID>
			<Name>Task 12</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>12</WBS>
			<OutlineNumber>12</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>13</UID>
			<GUID>96BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>13</ID>
			<Name>Task 13</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>13</WBS>
			<OutlineNumber>13</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>14</UID>
			<GUID>9BBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>14</ID>
			<Name>Task 14</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>14</WBS>
			<OutlineNumber>14</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>15</UID>
			<GUID>A0BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>15</ID>
			<Name>Task 15</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>15</WBS>
			<OutlineNumber>15</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>16</UID>
			<GUID>A5BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>16</ID>
			<Name>Task 16</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>16</WBS>
			<OutlineNumber>16</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>17</UID>
			<GUID>AABA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>17</ID>
			<Name>Task 17</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>17</WBS>
			<OutlineNumber>17</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>18</UID>
			<GUID>AFBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>18</ID>
			<Name>Task 18</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>18</WBS>
			<OutlineNumber>18</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>19</UID>
			<GUID>B4BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>19</ID>
			<Name>Task 19</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>19</WBS>
			<OutlineNumber>19</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>20</UID>
			<GUID>B9BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>20</ID>
			<Name>Task 20</Name>
			<Active>1</Active>
			<Manual>1</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:34:00</CreateDate>
			<WBS>20</WBS>
			<OutlineNumber>20</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<ManualStart>2016-01-04T08:00:00</ManualStart>
			<ManualFinish>2016-01-15T17:00:00</ManualFinish>
			<ManualDuration>PT80H0M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<FreeformDurationFormat>7</FreeformDurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<GUID>A0CB8B7E-2A8C-436D-0000-0000000000FF</GUID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<GUID>5CBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>1</ID>
			<Name>Resource 1</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>3</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>2</UID>
			<GUID>61BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>2</ID>
			<Name>Resource 2</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>4</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>3</UID>
			<GUID>66BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>3</ID>
			<Name>Resource 3</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>4</UID>
			<GUID>6BBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>4</ID>
			<Name>Resource 4</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>5</UID>
			<GUID>70BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>5</ID>
			<Name>Resource 5</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>7</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>6</UID>
			<GUID>75BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>6</ID>
			<Name>Resource 6</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>8</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>7</UID>
			<GUID>7ABA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>7</ID>
			<Name>Resource 7</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>9</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>8</UID>
			<GUID>7FBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>8</ID>
			<Name>Resource 8</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>10</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>9</UID>
			<GUID>84BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>9</ID>
			<Name>Resource 9</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>11</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>10</UID>
			<GUID>89BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>10</ID>
			<Name>Resource 10</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>12</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>11</UID>
			<GUID>8EBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>11</ID>
			<Name>Resource 11</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>13</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>12</UID>
			<GUID>93BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>12</ID>
			<Name>Resource 12</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>14</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>13</UID>
			<GUID>98BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>13</ID>
			<Name>Resource 13</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>15</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>14</UID>
			<GUID>9DBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>14</ID>
			<Name>Resource 14</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>16</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>15</UID>
			<GUID>A2BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>15</ID>
			<Name>Resource 15</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>17</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>16</UID>
			<GUID>A7BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>16</ID>
			<Name>Resource 16</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>18</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>17</UID>
			<GUID>ACBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>17</ID>
			<Name>Resource 17</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>19</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>18</UID>
			<GUID>B1BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>18</ID>
			<Name>Resource 18</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>20</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>19</UID>
			<GUID>B6BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>19</ID>
			<Name>Resource 19</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>21</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>20</UID>
			<GUID>BBBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>20</ID>
			<Name>Resource 20</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>22</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>2</UID>
			<GUID>5EBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>1</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852667</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<ExtendedAttribute>
				<FieldID>255852985</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>4</UID>
			<GUID>63BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>2</TaskUID>
			<ResourceUID>2</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852668</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>6</UID>
			<GUID>68BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>3</TaskUID>
			<ResourceUID>3</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852669</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>8</UID>
			<GUID>6DBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>4</TaskUID>
			<ResourceUID>4</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852670</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>10</UID>
			<GUID>72BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>5</TaskUID>
			<ResourceUID>5</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852671</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>12</UID>
			<GUID>77BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>6</TaskUID>
			<ResourceUID>6</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852672</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>14</UID>
			<GUID>7CBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>7</TaskUID>
			<ResourceUID>7</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852673</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>16</UID>
			<GUID>81BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>8</TaskUID>
			<ResourceUID>8</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852674</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>18</UID>
			<GUID>86BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>9</TaskUID>
			<ResourceUID>9</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852675</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>20</UID>
			<GUID>8BBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>10</TaskUID>
			<ResourceUID>10</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852666</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>22</UID>
			<GUID>90BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>11</TaskUID>
			<ResourceUID>11</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852732</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>24</UID>
			<GUID>95BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>12</TaskUID>
			<ResourceUID>12</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852733</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>26</UID>
			<GUID>9ABA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>13</TaskUID>
			<ResourceUID>13</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852734</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>28</UID>
			<GUID>9FBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>14</TaskUID>
			<ResourceUID>14</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852735</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>30</UID>
			<GUID>A4BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>15</TaskUID>
			<ResourceUID>15</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852736</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>32</UID>
			<GUID>A9BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>16</TaskUID>
			<ResourceUID>16</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852737</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>34</UID>
			<GUID>AEBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>17</TaskUID>
			<ResourceUID>17</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852738</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>36</UID>
			<GUID>B3BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>18</TaskUID>
			<ResourceUID>18</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852739</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>38</UID>
			<GUID>B8BA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>19</TaskUID>
			<ResourceUID>19</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852740</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>40</UID>
			<GUID>BDBA2381-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<TaskUID>20</TaskUID>
			<ResourceUID>20</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:34:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<ExtendedAttribute>
				<FieldID>255852741</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT0H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>1</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>