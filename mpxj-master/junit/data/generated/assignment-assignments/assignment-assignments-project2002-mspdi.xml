<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project"><Name>assignment-assignments-project2002-mspdi.xml</Name><Title>assignment-assignments-project2002-mpp9</Title><Author>Project User</Author><CreationDate>2016-03-11T09:31:00</CreationDate><LastSaved>2016-03-11T09:31:00</LastSaved><ScheduleFromStart>1</ScheduleFromStart><StartDate>2016-03-11T08:00:00</StartDate><FinishDate>2016-01-15T17:00:00</FinishDate><FYStartDate>1</FYStartDate><CriticalSlackLimit>0</CriticalSlackLimit><CurrencyDigits>2</CurrencyDigits><CurrencySymbol>£</CurrencySymbol><CurrencySymbolPosition>0</CurrencySymbolPosition><CalendarUID>1</CalendarUID><DefaultStartTime>08:00:00</DefaultStartTime><DefaultFinishTime>17:00:00</DefaultFinishTime><MinutesPerDay>480</MinutesPerDay><MinutesPerWeek>2400</MinutesPerWeek><DaysPerMonth>20</DaysPerMonth><DefaultTaskType>0</DefaultTaskType><DefaultFixedCostAccrual>3</DefaultFixedCostAccrual><DefaultStandardRate>0</DefaultStandardRate><DefaultOvertimeRate>0</DefaultOvertimeRate><DurationFormat>7</DurationFormat><WorkFormat>2</WorkFormat><EditableActualCosts>0</EditableActualCosts><HonorConstraints>0</HonorConstraints><InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary><MultipleCriticalPaths>0</MultipleCriticalPaths><NewTasksEffortDriven>1</NewTasksEffortDriven><NewTasksEstimated>1</NewTasksEstimated><SplitsInProgressTasks>1</SplitsInProgressTasks><SpreadActualCost>0</SpreadActualCost><SpreadPercentComplete>0</SpreadPercentComplete><TaskUpdatesResource>1</TaskUpdatesResource><FiscalYearStart>0</FiscalYearStart><WeekStartDay>1</WeekStartDay><MoveCompletedEndsBack>0</MoveCompletedEndsBack><MoveRemainingStartsBack>0</MoveRemainingStartsBack><MoveRemainingStartsForward>0</MoveRemainingStartsForward><MoveCompletedEndsForward>0</MoveCompletedEndsForward><BaselineForEarnedValue>0</BaselineForEarnedValue><AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks><CurrentDate>2016-03-11T08:00:00</CurrentDate><MicrosoftProjectServerURL>1</MicrosoftProjectServerURL><Autolink>1</Autolink><NewTaskStartDate>0</NewTaskStartDate><DefaultTaskEVMethod>0</DefaultTaskEVMethod><ProjectExternallyEdited>0</ProjectExternallyEdited><OutlineCodes/><WBSMasks/><ExtendedAttributes/><Calendars><Calendar><UID>1</UID><Name>Standard</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar><Calendar><UID>3</UID><Name>Resource 1</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>4</UID><Name>Resource 2</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>5</UID><Name>Resource 3</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar></Calendars><Tasks><Task><UID>0</UID><ID>0</ID><Name>assignment-assignments-project2002-mpp9</Name><Type>1</Type><IsNull>0</IsNull><CreateDate>2016-03-11T09:31:00</CreateDate><WBS>0</WBS><OutlineNumber>0</OutlineNumber><OutlineLevel>0</OutlineLevel><Priority>500</Priority><Start>2016-01-04T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Duration>PT80H0M0S</Duration><DurationFormat>21</DurationFormat><Work>PT240H0M0S</Work><Stop>2016-01-04T08:00:00</Stop><Resume>2016-01-04T08:00:00</Resume><ResumeValid>0</ResumeValid><EffortDriven>0</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>1</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2016-01-04T08:00:00</EarlyStart><EarlyFinish>2016-01-15T17:00:00</EarlyFinish><LateStart>2016-01-04T08:00:00</LateStart><LateFinish>2016-01-15T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>14400000</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>25</PercentComplete><PercentWorkComplete>25</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualStart>2016-01-04T08:00:00</ActualStart><ActualDuration>PT20H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT60H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT240H0M0S</RegularWork><RemainingDuration>PT60H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT180H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod></Task><Task><UID>1</UID><ID>1</ID><Name>Task 1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2016-03-11T09:31:00</CreateDate><WBS>1</WBS><OutlineNumber>1</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2016-01-04T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Duration>PT80H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT80H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2016-01-04T08:00:00</EarlyStart><EarlyFinish>2016-01-15T17:00:00</EarlyFinish><LateStart>2016-01-04T08:00:00</LateStart><LateFinish>2016-01-15T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingDuration>PT80H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT80H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>4</ConstraintType><CalendarUID>-1</CalendarUID><ConstraintDate>2016-01-04T08:00:00</ConstraintDate><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod></Task><Task><UID>2</UID><ID>2</ID><Name>Task 2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2016-03-11T09:31:00</CreateDate><WBS>2</WBS><OutlineNumber>2</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2016-01-04T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Duration>PT80H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT80H0M0S</Work><Stop>2016-01-06T12:00:00</Stop><Resume>2016-01-06T13:00:00</Resume><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2016-01-04T08:00:00</EarlyStart><EarlyFinish>2016-01-15T17:00:00</EarlyFinish><LateStart>2016-01-04T08:00:00</LateStart><LateFinish>2016-01-15T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>25</PercentComplete><PercentWorkComplete>25</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualStart>2016-01-04T08:00:00</ActualStart><ActualDuration>PT20H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT20H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingDuration>PT60H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT60H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>4</ConstraintType><CalendarUID>-1</CalendarUID><ConstraintDate>2016-01-04T08:00:00</ConstraintDate><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><TimephasedData><Type>11</Type><UID>2</UID><Start>2016-01-04T08:00:00</Start><Finish>2016-01-05T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>2</UID><Start>2016-01-05T08:00:00</Start><Finish>2016-01-06T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>2</UID><Start>2016-01-06T08:00:00</Start><Finish>2016-01-06T12:00:00</Finish><Unit>2</Unit><Value>5</Value></TimephasedData></Task><Task><UID>3</UID><ID>3</ID><Name>Task 3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2016-03-11T09:31:00</CreateDate><WBS>3</WBS><OutlineNumber>3</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2016-01-04T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Duration>PT80H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT80H0M0S</Work><Stop>2016-01-08T17:00:00</Stop><Resume>2016-01-11T08:00:00</Resume><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2016-01-04T08:00:00</EarlyStart><EarlyFinish>2016-01-15T17:00:00</EarlyFinish><LateStart>2016-01-04T08:00:00</LateStart><LateFinish>2016-01-15T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>50</PercentComplete><PercentWorkComplete>50</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualStart>2016-01-04T08:00:00</ActualStart><ActualDuration>PT40H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT40H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingDuration>PT40H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT40H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>4</ConstraintType><CalendarUID>-1</CalendarUID><ConstraintDate>2016-01-04T08:00:00</ConstraintDate><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><TimephasedData><Type>11</Type><UID>3</UID><Start>2016-01-04T08:00:00</Start><Finish>2016-01-05T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>3</UID><Start>2016-01-05T08:00:00</Start><Finish>2016-01-06T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>3</UID><Start>2016-01-06T08:00:00</Start><Finish>2016-01-07T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>3</UID><Start>2016-01-07T08:00:00</Start><Finish>2016-01-08T08:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData><TimephasedData><Type>11</Type><UID>3</UID><Start>2016-01-08T08:00:00</Start><Finish>2016-01-08T17:00:00</Finish><Unit>2</Unit><Value>10</Value></TimephasedData></Task></Tasks><Resources><Resource><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>2</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>1</UID><ID>1</ID><Name>Resource 1</Name><Type>1</Type><IsNull>0</IsNull><Initials>R</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>1</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT80H0M0S</Work><RegularWork>PT80H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT80H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>4800000</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>3</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>2</UID><ID>2</ID><Name>Resource 2</Name><Type>1</Type><IsNull>0</IsNull><Initials>R</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>1</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT80H0M0S</Work><RegularWork>PT80H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT20H0M0S</ActualWork><RemainingWork>PT60H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>25</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>4800000</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>4</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>3</UID><ID>3</ID><Name>Resource 3</Name><Type>1</Type><IsNull>0</IsNull><Initials>R</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>1</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT80H0M0S</Work><RegularWork>PT80H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT40H0M0S</ActualWork><RemainingWork>PT40H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>50</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>4800000</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>5</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource></Resources><Assignments><Assignment><UID>4</UID><TaskUID>1</TaskUID><ResourceUID>1</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2016-01-15T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT80H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2016-01-04T08:00:00</Start><Stop>2016-01-04T08:00:00</Stop><Resume>2016-01-04T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT80H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-04T08:00:00</Start><Finish>2016-01-05T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-05T08:00:00</Start><Finish>2016-01-06T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-06T08:00:00</Start><Finish>2016-01-07T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-07T08:00:00</Start><Finish>2016-01-08T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-08T08:00:00</Start><Finish>2016-01-09T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-09T08:00:00</Start><Finish>2016-01-10T08:00:00</Finish><Unit>2</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-10T08:00:00</Start><Finish>2016-01-11T08:00:00</Finish><Unit>2</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-11T08:00:00</Start><Finish>2016-01-12T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-12T08:00:00</Start><Finish>2016-01-13T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-13T08:00:00</Start><Finish>2016-01-14T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-14T08:00:00</Start><Finish>2016-01-15T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>4</UID><Start>2016-01-15T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>5</UID><TaskUID>2</TaskUID><ResourceUID>2</ResourceUID><PercentWorkComplete>25</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualStart>2016-01-04T08:00:00</ActualStart><ActualWork>PT20H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2016-01-15T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT60H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2016-01-04T08:00:00</Start><Stop>2016-01-06T12:00:00</Stop><Resume>2016-01-06T13:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT80H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-06T13:00:00</Start><Finish>2016-01-07T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-07T13:00:00</Start><Finish>2016-01-08T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-08T13:00:00</Start><Finish>2016-01-09T13:00:00</Finish><Unit>2</Unit><Value>PT4H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-09T13:00:00</Start><Finish>2016-01-10T13:00:00</Finish><Unit>2</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-10T13:00:00</Start><Finish>2016-01-11T13:00:00</Finish><Unit>2</Unit><Value>PT4H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-11T13:00:00</Start><Finish>2016-01-12T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-12T13:00:00</Start><Finish>2016-01-13T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-13T13:00:00</Start><Finish>2016-01-14T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-14T13:00:00</Start><Finish>2016-01-15T13:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>5</UID><Start>2016-01-15T13:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Unit>2</Unit><Value>PT4H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>5</UID><Start>2016-01-04T08:00:00</Start><Finish>2016-01-05T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>5</UID><Start>2016-01-05T08:00:00</Start><Finish>2016-01-06T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>5</UID><Start>2016-01-06T08:00:00</Start><Finish>2016-01-06T12:00:00</Finish><Unit>2</Unit><Value>PT4H0M0S</Value></TimephasedData></Assignment><Assignment><UID>6</UID><TaskUID>3</TaskUID><ResourceUID>3</ResourceUID><PercentWorkComplete>50</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualStart>2016-01-04T08:00:00</ActualStart><ActualWork>PT40H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2016-01-15T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>4800000</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT80H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT40H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2016-01-04T08:00:00</Start><Stop>2016-01-08T17:00:00</Stop><Resume>2016-01-11T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT80H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><TimephasedData><Type>1</Type><UID>6</UID><Start>2016-01-11T08:00:00</Start><Finish>2016-01-12T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>6</UID><Start>2016-01-12T08:00:00</Start><Finish>2016-01-13T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>6</UID><Start>2016-01-13T08:00:00</Start><Finish>2016-01-14T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>6</UID><Start>2016-01-14T08:00:00</Start><Finish>2016-01-15T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>6</UID><Start>2016-01-15T08:00:00</Start><Finish>2016-01-15T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>6</UID><Start>2016-01-04T08:00:00</Start><Finish>2016-01-05T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>6</UID><Start>2016-01-05T08:00:00</Start><Finish>2016-01-06T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>6</UID><Start>2016-01-06T08:00:00</Start><Finish>2016-01-07T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>6</UID><Start>2016-01-07T08:00:00</Start><Finish>2016-01-08T08:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData><TimephasedData><Type>2</Type><UID>6</UID><Start>2016-01-08T08:00:00</Start><Finish>2016-01-08T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment></Assignments></Project>
