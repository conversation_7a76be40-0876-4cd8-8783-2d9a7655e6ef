<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<Name>task-costs-project2013-mspdi.xml</Name>
	<Author>Project User</Author>
	<CreationDate>2014-11-11T08:00:00</CreationDate>
	<LastSaved>2014-11-11T07:51:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2014-11-11T08:00:00</StartDate>
	<FinishDate>2014-11-11T17:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2014-11-11T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>0</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>0</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>188743786</FieldID>
			<FieldName>Cost1</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40006A</Guid>
			<SecondaryPID>255868958</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40401E</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743787</FieldID>
			<FieldName>Cost2</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40006B</Guid>
			<SecondaryPID>255868959</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40401F</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743788</FieldID>
			<FieldName>Cost3</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40006C</Guid>
			<SecondaryPID>255868960</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404020</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743938</FieldID>
			<FieldName>Cost4</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400102</Guid>
			<SecondaryPID>255868961</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404021</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743939</FieldID>
			<FieldName>Cost5</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400103</Guid>
			<SecondaryPID>255868962</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404022</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743940</FieldID>
			<FieldName>Cost6</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400104</Guid>
			<SecondaryPID>255868963</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404023</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743941</FieldID>
			<FieldName>Cost7</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400105</Guid>
			<SecondaryPID>255868964</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404024</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743942</FieldID>
			<FieldName>Cost8</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400106</Guid>
			<SecondaryPID>255868965</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404025</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743943</FieldID>
			<FieldName>Cost9</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400107</Guid>
			<SecondaryPID>255868966</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404026</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743944</FieldID>
			<FieldName>Cost10</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400108</Guid>
			<SecondaryPID>255868967</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404027</SecondaryGuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Cost1</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743786</FieldID>
				<Value>100</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Cost2</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>2</WBS>
			<OutlineNumber>2</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743787</FieldID>
				<Value>200</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Cost3</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>3</WBS>
			<OutlineNumber>3</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743788</FieldID>
				<Value>300</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Cost4</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>4</WBS>
			<OutlineNumber>4</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743938</FieldID>
				<Value>400</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>5</UID>
			<ID>5</ID>
			<Name>Cost5</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>5</WBS>
			<OutlineNumber>5</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743939</FieldID>
				<Value>500</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>6</UID>
			<ID>6</ID>
			<Name>Cost6</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>6</WBS>
			<OutlineNumber>6</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743940</FieldID>
				<Value>600</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>7</UID>
			<ID>7</ID>
			<Name>Cost7</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>7</WBS>
			<OutlineNumber>7</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743941</FieldID>
				<Value>700</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>8</UID>
			<ID>8</ID>
			<Name>Cost8</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>8</WBS>
			<OutlineNumber>8</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743942</FieldID>
				<Value>800</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>9</UID>
			<ID>9</ID>
			<Name>Cost9</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>9</WBS>
			<OutlineNumber>9</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743943</FieldID>
				<Value>900</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>10</UID>
			<ID>10</ID>
			<Name>Cost10</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-11-11T07:51:00</CreateDate>
			<WBS>10</WBS>
			<OutlineNumber>10</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-11-11T08:00:00</Start>
			<Finish>2014-11-11T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-11-11T08:00:00</ManualStart>
			<ManualFinish>2014-11-11T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-11-11T08:00:00</EarlyStart>
			<EarlyFinish>2014-11-11T17:00:00</EarlyFinish>
			<LateStart>2014-11-11T08:00:00</LateStart>
			<LateFinish>2014-11-11T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743944</FieldID>
				<Value>1000</Value>
			</ExtendedAttribute>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>1</UID>
			<TaskUID>1</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>1</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>2</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>3</UID>
			<TaskUID>3</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>3</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>4</UID>
			<TaskUID>4</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>5</UID>
			<TaskUID>5</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>6</UID>
			<TaskUID>6</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>7</UID>
			<TaskUID>7</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>8</UID>
			<TaskUID>8</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>9</UID>
			<TaskUID>9</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>9</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>10</UID>
			<TaskUID>10</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-11-11T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-11-11T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-11-11T07:51:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2014-11-11T08:00:00</Start>
				<Finish>2014-11-11T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>